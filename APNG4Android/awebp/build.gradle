plugins {
    id 'com.android.library'
}
android {
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 35
        compileSdk 35

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    publishing {
        singleVariant('release') {
            withSourcesJar()
        }
    }
    namespace = "com.github.penfeizhou.animation.awebp"
}

dependencies {
    implementation 'androidx.annotation:annotation:1.9.1'
    implementation 'androidx.vectordrawable:vectordrawable-animated:1.2.0'
    api(project(":APNG4Android:frameanimation"))

    testImplementation 'junit:junit:4.13'
    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'
}
