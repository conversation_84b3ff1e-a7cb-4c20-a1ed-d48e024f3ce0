package com.overseas.common.ext

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import android.view.View
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.Fragment

context(Activity)
fun <T> Class<T>.startActivity(block: Intent.() -> Unit = {}) {
    <EMAIL>(Intent(this@Activity, this).apply(block))
}

context(Fragment)
fun <T> Class<T>.startActivity(block: Intent.() -> Unit = {}) {
    <EMAIL>(Intent(<EMAIL>(), this).apply(block))
}

context(View)
inline fun <reified A : Activity> startActivity(block: Intent.() -> Unit = {}) {
    val context: Context = <EMAIL>
    context.startActivity(Intent(context, A::class.java).also { block.invoke(it) })
}

context(Activity)
inline fun <reified A : Activity> startActivity(block: Intent.() -> Unit = {}) {
    val context: Context = this@Activity
    context.startActivity(Intent(context, A::class.java).also { block.invoke(it) })
}

context(Activity)
fun Intent.startActivity() {
    startActivity(this)
}

context(Activity)
inline fun <reified A : Activity> startPage() {
    startActivity(Intent(this@Activity, A::class.java))
}

context(Fragment)
fun Intent.startActivity() {
    startActivity(this)
}


// 刘海屏高度
val Activity.notchHeight: Int
    get() = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
        val ret = window.decorView.rootWindowInsets?.displayCutout?.safeInsetTop ?: 0
        if (ret <= 0) {
            statusHeight
        } else {
            ret
        }
    } else {
        statusHeight
    }


// 状态栏高度
val Activity.statusHeight: Int
    get() = ViewCompat.getRootWindowInsets(window.decorView)?.getInsets(WindowInsetsCompat.Type.systemBars())?.top ?: 0

// 状态栏高度
val Activity.navigationHeight: Int
    get() = ViewCompat.getRootWindowInsets(window.decorView)?.getInsets(WindowInsetsCompat.Type.systemBars())?.bottom ?: 0