package com.overseas.common.ext

import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.StateListDrawable
import androidx.annotation.ColorInt

fun StateListDrawable.addSelectedDrawable(drawable: Drawable) {
    addState(intArrayOf(android.R.attr.state_selected), drawable)
}

fun StateListDrawable.addUnSelectedDrawable(drawable: Drawable) {
    addState(intArrayOf(-android.R.attr.state_selected), drawable)
}

fun buildColorStateList(colorSelect: Int, colorUnSelect: Int) =
    ColorStateList(
        arrayOf(intArrayOf(android.R.attr.state_selected), intArrayOf(-android.R.attr.state_selected)),
        intArrayOf(colorSelect, colorUnSelect)
    )

fun gradientDrawable(
    @ColorInt color: Int = Color.TRANSPARENT,
    strokeWidth: Int = -1,
    @ColorInt strokeColor: Int = Color.WHITE,
    cornerRadius: Float = -1f,
    block: GradientDrawable.() -> Unit = {},
): Drawable =
    GradientDrawable().apply {
        this.color = ColorStateList.valueOf(color)
        if (cornerRadius > 0) {
            this.cornerRadius = cornerRadius
        }
        if (strokeWidth > 0) {
            this.setStroke(strokeWidth, strokeColor)
        }
        block()
    }