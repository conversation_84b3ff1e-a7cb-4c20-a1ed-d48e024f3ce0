package com.overseas.common.ext

import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.overseas.common.R
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext

private const val TAG_FRAGMENT_VIEW_LIFECYCLE = "dylan.fragment.view.lifecycle.tag"

/**
 * 在任意时刻安全的获取viewLifecycleOwner [Fragment.mViewLifecycleOwner]
 * 你应知悉[Fragment.mViewLifecycleOwner] 和 [LifecycleOwner] 的区别
 * 不熟悉可以看下这篇文章[viewLifecycleOwner](https://juejin.cn/post/6915222252506054663)
 */
val Fragment.safeViewLifecycleOwner: LifecycleOwner
    get() {
        return if (viewLifecycleOwnerLiveData.value == null) {
            val tempBagOfTags = mBagOfTags
            tempBagOfTags?.getAnyTag<LifecycleOwner>(TAG_FRAGMENT_VIEW_LIFECYCLE)
                ?: SafeViewLifecycleOwner(this).also {
                    tempBagOfTags?.setTag(TAG_FRAGMENT_VIEW_LIFECYCLE, it)
                }
        } else {
            viewLifecycleOwner
        }
    }
val Fragment.safeLifecycle: Lifecycle
    get() = safeViewLifecycleOwner.lifecycle

val Fragment.safeViewLifecycleScope: LifecycleCoroutineScope
    get() = safeViewLifecycleOwner.lifecycleScope

fun Fragment.withViewLifecycleOwner(block: LifecycleOwner.() -> Unit) {
    viewLifecycleOwnerLiveData.observeLifecycleForeverOnce(this) {
        it?.block()
    }
}

/**
 * 获取[Fragment.mViewLifecycleOwner]对应的[LifecycleCoroutineScope]
 */
fun Fragment.withViewCoroutineScope(block: LifecycleCoroutineScope.() -> Unit) {
    withViewLifecycleOwner {
        block(this.lifecycleScope)
    }
}

val Fragment.isStarted: Boolean
    get() = isResumed || lifecycle.isAtLeastState(Lifecycle.State.STARTED)

/**
 * [Fragment] 扩展[viewLaunch]方法
 * 注意和[launch]的区别，这里使用的[CoroutineContext]对应的是[Fragment.mViewLifecycleOwner]的[LifecycleCoroutineScope]
 */
fun Fragment.viewLaunch(
    context: CoroutineContext = EmptyCoroutineContext,
    start: CoroutineStart = CoroutineStart.DEFAULT,
    block: suspend CoroutineScope.() -> Unit,
): DelegateJob {
    val delegateJob = DelegateJob()
    withViewCoroutineScope {
        if (delegateJob.isActive) {
            delegateJob.attachJob(launch(context, start, block))
        }
    }
    return delegateJob
}

/**
 * [Fragment] 扩展[viewLaunchWhenCreated]方法
 * 注意和[launch]的区别，这里使用的[CoroutineContext]对应的是[Fragment.mViewLifecycleOwner]的[LifecycleCoroutineScope]
 */
fun Fragment.viewLaunchWhenCreated(block: suspend CoroutineScope.() -> Unit): DelegateJob {
    val delegateJob = DelegateJob()
    withViewCoroutineScope {
        if (delegateJob.isActive) {
            delegateJob.attachJob(launchWhenCreated(block))
        }
    }
    return delegateJob
}

/**
 * [Fragment] 扩展[viewLaunchWhenStarted]方法
 * 注意和[launch]的区别，这里使用的[CoroutineContext]对应的是[Fragment.mViewLifecycleOwner]的[LifecycleCoroutineScope]
 */
fun Fragment.viewLaunchWhenStarted(block: suspend CoroutineScope.() -> Unit): DelegateJob {
    val delegateJob = DelegateJob()
    withViewCoroutineScope {
        if (delegateJob.isActive) {
            delegateJob.attachJob(launchWhenStarted(block))
        }
    }
    return delegateJob
}

/**
 * [Fragment] 扩展[viewLaunchWhenResumed]方法
 * 注意和[launch]的区别，这里使用的[CoroutineContext]对应的是[Fragment.mViewLifecycleOwner]的[LifecycleCoroutineScope]
 */
fun Fragment.viewLaunchWhenResumed(block: suspend CoroutineScope.() -> Unit): DelegateJob {
    val delegateJob = DelegateJob()
    withViewCoroutineScope {
        if (delegateJob.isActive) {
            delegateJob.attachJob(launchWhenResumed(block))
        }
    }
    return delegateJob
}

class SafeViewLifecycleOwner internal constructor(fragment: Fragment) : LifecycleOwner {

    private val mLifecycleRegistry = LifecycleRegistry(this)

    private var first = true

    init {
        fragment.viewLifecycleOwnerLiveData.observeLifecycleForeverOnce(fragment) {
            if (it == null) {
                if (first) {
                    first = false
                    mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_CREATE)
                }
                mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_DESTROY)
            } else {
                it.lifecycle.addObserver(object : LifecycleEventObserver {
                    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                        if (event == Lifecycle.Event.ON_DESTROY) {
                            it.lifecycle.removeObserver(this)
                            if (first) {
                                first = false
                                mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_CREATE)
                            }
                        }
                        mLifecycleRegistry.handleLifecycleEvent(event)
                    }
                })
            }
        }
    }

    override val lifecycle: Lifecycle = mLifecycleRegistry

}

class DelegateJob(private var _job: Job? = null) {

    private var _isEnable = true

    val isActive: Boolean
        get() = _isEnable

    val job: Job?
        get() = _job

    fun attachJob(job: Job) {
        if (_job == null) {
            _job = job
        }
    }

    fun cancel(cause: CancellationException? = null) {
        _isEnable = false
        _job?.cancel(cause)
    }
}

inline fun FragmentManager.safeCommit(
    allowStateLoss: Boolean = false, body: FragmentTransaction.() -> Unit,
) {
    val transaction = beginTransaction()
    transaction.body()
    if (allowStateLoss || isStateSaved) {
        transaction.commitAllowingStateLoss()
    } else {
        try {
            transaction.commit()
        } catch (e: Exception) {
            transaction.commitAllowingStateLoss()
        }
    }
}

inline fun FragmentManager.safeCommitNow(
    allowStateLoss: Boolean = false, body: FragmentTransaction.() -> Unit,
) {
    val transaction = beginTransaction()
    transaction.body()
    if (allowStateLoss || isStateSaved) {
        transaction.commitNowAllowingStateLoss()
    } else {
        try {
            transaction.commitNow()
        } catch (e: Exception) {
            transaction.commitNowAllowingStateLoss()
        }
    }
}

fun DialogFragment.safeDismiss(allowStateLoss: Boolean = false) {
    if (allowStateLoss || fragmentManager?.isStateSaved == true) {
        dismissAllowingStateLoss()
    } else {
        try {
            dismiss()
        } catch (e: Exception) {
            dismissAllowingStateLoss()
        }
    }
}

fun DialogFragment.safeDismissNow() {
    if (fragmentManager?.isStateSaved == true) {
        dismissAllowingStateLoss()
    } else {
        try {
            dismissNow()
        } catch (e: Exception) {
            dismissAllowingStateLoss()
        }
    }
}

typealias FragmentCreator = () -> Fragment

fun Fragment.createViewPager2Adapter(fragmentCreators: List<FragmentCreator>) =
    object : FragmentStateAdapter(this@Fragment) {
        override fun getItemCount() = fragmentCreators.size

        override fun createFragment(position: Int) = fragmentCreators[position].invoke()
    }

fun FragmentActivity.createViewPager2Adapter(fragmentCreators: List<FragmentCreator>) =
    object : FragmentStateAdapter(this) {
        override fun getItemCount() = fragmentCreators.size

        override fun createFragment(position: Int) = fragmentCreators[position].invoke()
    }

fun createViewPager2Adapter(
    fragmentManager: FragmentManager, lifecycle: Lifecycle, fragmentCreators: List<FragmentCreator>,
) = object : FragmentStateAdapter(fragmentManager, lifecycle) {
    override fun getItemCount() = fragmentCreators.size

    override fun createFragment(position: Int) = fragmentCreators[position].invoke()
}

fun FragmentActivity.showChildFragment(
    fragmentContainerId: Int = android.R.id.content,
    fragment: Fragment,
    replace: Boolean,
    tag: String? = null,
    verticalSlide: Boolean = true,
    addToBackStack: Boolean = true,
) {
    val run = {
        supportFragmentManager.safeCommit {
            if (verticalSlide) {
                setCustomAnimations(
                    R.anim.anim_slide_in_bottom,
                    R.anim.anim_slide_out_bottom,
                    R.anim.anim_slide_in_bottom,
                    R.anim.anim_slide_out_bottom
                )
            } else {
                setCustomAnimations(
                    R.anim.anim_slide_in_right,
                    R.anim.anim_slide_out_right,
                    R.anim.anim_slide_in_right,
                    R.anim.anim_slide_out_right
                )
            }
            if (replace) {
                replace(fragmentContainerId, fragment, tag)
            } else {
                add(fragmentContainerId, fragment, tag)
            }
            if (addToBackStack) {
                addToBackStack(null)
            }
        }
    }
    val backStackCount = supportFragmentManager.backStackEntryCount
    if (replace && backStackCount > 0) {
        try {
            if (supportFragmentManager.isStateSaved) {
                supportFragmentManager.popBackStack(null, FragmentManager.POP_BACK_STACK_INCLUSIVE)
            } else {
                supportFragmentManager.popBackStackImmediate(null, FragmentManager.POP_BACK_STACK_INCLUSIVE)
            }
        } catch (_: Exception) {
        }
        lifecycleScope.launchWhenStarted {
            delay(150)
            run()
        }
    } else {
        run()
    }
}


fun Fragment.showChildFragment(
    fragmentContainerId: Int,
    fragment: Fragment,
    replace: Boolean,
    tag: String? = null,
    verticalSlide: Boolean = true,
    addToBackStack: Boolean = true,
) {
    val run = {
        childFragmentManager.safeCommit {
            if (verticalSlide) {
                setCustomAnimations(
                    R.anim.anim_slide_in_bottom,
                    R.anim.anim_slide_out_bottom,
                    R.anim.anim_slide_in_bottom,
                    R.anim.anim_slide_out_bottom
                )
            } else {
                setCustomAnimations(
                    R.anim.anim_slide_in_right,
                    R.anim.anim_slide_out_left,
                    R.anim.anim_slide_in_left,
                    R.anim.anim_slide_out_right
                )
            }
            if (replace) {
                replace(fragmentContainerId, fragment, tag)
            } else {
                add(fragmentContainerId, fragment, tag)
            }
            if (addToBackStack) {
                addToBackStack(null)
            }
        }
    }
    val backStackCount = childFragmentManager.backStackEntryCount
    if (replace && backStackCount > 0) {
        if (childFragmentManager.isStateSaved) {
            childFragmentManager.popBackStack(null, FragmentManager.POP_BACK_STACK_INCLUSIVE)
        } else {
            childFragmentManager.popBackStackImmediate(null, FragmentManager.POP_BACK_STACK_INCLUSIVE)
        }
        lifecycleScope.launchWhenStarted {
            delay(150)
            run()
        }
    } else {
        run()
    }
}

inline fun <reified T> Fragment.findParentOwner(predicate: (T) -> Boolean = { true }): T? {
    var target: Any? = activity
    if (target is T && predicate(target)) {
        return target
    }
    target = parentFragment
    while (target != null) {
        if (target is T && predicate(target)) {
            return target
        }
        target = (target as Fragment).parentFragment
    }
    activity?.supportFragmentManager?.fragments?.forEach {
        if (it is T && predicate(it)) {
            return it
        }
    }
    return null
}

fun Fragment.launchAndRepeat(
    state: Lifecycle.State = Lifecycle.State.RESUMED,
    block: suspend CoroutineScope.() -> Unit,
) = safeViewLifecycleScope.launch {
    repeatOnLifecycle(state, block)
}