package com.overseas.common.ext

import android.app.Activity
import android.app.Application
import android.os.Bundle
import android.util.SparseArray
import androidx.core.util.containsKey
import androidx.core.util.valueIterator
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.lifecycle.lifecycleScope
import com.overseas.common.utils.appContext
import com.overseas.common.utils.postToMainThread
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.launch
import java.io.Closeable
import java.io.IOException
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.collections.set
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext

/**
 * [LifecycleOwner] 扩展[launch]方法
 * 在[Activity]和[Fragment]中可以直接使用
 */
fun LifecycleOwner.launch(
    context: CoroutineContext = EmptyCoroutineContext,
    start: CoroutineStart = CoroutineStart.DEFAULT,
    block: suspend CoroutineScope.() -> Unit,
): Job = lifecycleScope.launch(context, start, block)

/**
 * [LifecycleOwner] 扩展[launchWhenCreated]方法
 * 在[Activity]和[Fragment]中可以直接使用
 */
fun LifecycleOwner.launchWhenCreated(block: suspend CoroutineScope.() -> Unit): Job = lifecycleScope.launchWhenCreated(block)

/**
 * [LifecycleOwner] 扩展[launchWhenStarted]方法
 * 在[Activity]和[Fragment]中可以直接使用
 */
fun LifecycleOwner.launchWhenStarted(block: suspend CoroutineScope.() -> Unit): Job = lifecycleScope.launchWhenStarted(block)

/**
 * [LifecycleOwner] 扩展[launchWhenResumed]方法
 * 在[Activity]和[Fragment]中可以直接使用
 */
fun LifecycleOwner.launchWhenResumed(block: suspend CoroutineScope.() -> Unit): Job = lifecycleScope.launchWhenResumed(block)

val Lifecycle.isDestroyed
    get() = currentState <= Lifecycle.State.DESTROYED

val Lifecycle.isResumed
    get() = isAtLeastState(Lifecycle.State.RESUMED)

val LifecycleOwner.isDestroyed
    get() = lifecycle.isDestroyed

fun Lifecycle.isAtLeastState(state: Lifecycle.State): Boolean {
    return currentState.isAtLeast(state)
}

fun LifecycleOwner.isAtLeastState(state: Lifecycle.State): Boolean {
    return lifecycle.isAtLeastState(state)
}

class DisposableImpl(var callback: () -> Unit = {}) : Disposable {

    private val disposable = AtomicBoolean(false)

    override fun dispose() {
        if (disposable.compareAndSet(false, true)) {
            callback()
        }
    }

    override fun isDisposed(): Boolean = disposable.get()

}

fun Lifecycle.lifecycleFlow(): Flow<Lifecycle.Event> {
    return callbackFlow {
        val disposable = doOnLifecycleEvent {
            trySend(it)
            if (it == Lifecycle.Event.ON_DESTROY) {
                close()
            }
        }
        awaitClose {
            disposable.dispose()
        }
    }
}

fun Lifecycle.doOnLifecycleEvent(block: (Lifecycle.Event) -> Unit): Disposable {
    val disposable = DisposableImpl()
    val callback = object : LifecycleEventObserver {
        override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
            if (event == Lifecycle.Event.ON_DESTROY) {
                removeObserver(this)
            }
            block(event)
        }
    }
    disposable.callback = {
        removeObserver(callback)
    }
    postToMainThread {
        if (disposable.isDisposed()) {
            return@postToMainThread
        }
        addObserver(callback)
    }
    return disposable
}

fun Lifecycle.doAtLeastState(invokeOnce: Boolean = false, state: Lifecycle.State = Lifecycle.State.CREATED, block: () -> Unit) {
    if (currentState.isAtLeast(state)) {
        block.invoke()
    } else {
        val invokeEvent = when (state) {
            Lifecycle.State.DESTROYED -> Lifecycle.Event.ON_DESTROY
            Lifecycle.State.INITIALIZED, Lifecycle.State.CREATED -> Lifecycle.Event.ON_CREATE
            Lifecycle.State.STARTED -> Lifecycle.Event.ON_START
            Lifecycle.State.RESUMED -> Lifecycle.Event.ON_RESUME
        }
        val ob = object : LifecycleEventObserver {
            override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                if (event == invokeEvent) {
                    block.invoke()
                    if (invokeOnce) {
                        source.lifecycle.removeObserver(this)
                    }
                }
                if (invokeOnce.not() && event == Lifecycle.Event.ON_DESTROY) {
                    source.lifecycle.removeObserver(this)
                }
            }
        }
        addObserver(ob)
    }
}


fun Lifecycle.doOnDestroy(block: () -> Unit): Disposable {
    return doOnLifecycleEvent { event ->
        if (event == Lifecycle.Event.ON_DESTROY) {
            block()
        }
    }
}

fun Lifecycle.doOnCreate(block: () -> Unit): Disposable {
    return doOnLifecycleEvent { event ->
        if (event == Lifecycle.Event.ON_CREATE) {
            block()
        }
    }
}

fun LifecycleOwner.doOnLifecycleEvent(block: (Lifecycle.Event) -> Unit): Disposable {
    return lifecycle.doOnLifecycleEvent(block)
}

fun LifecycleOwner.doOnDestroy(block: () -> Unit): Disposable {
    return lifecycle.doOnDestroy(block)
}

class ReferenceBag<K> {

    private val mIntBagOfTags by lazy {
        initState = 1
        SparseArray<Any?>()
    }

    private val mBagOfTags by lazy {
        initState = 2
        HashMap<K, Any?>()
    }

    @Volatile
    private var initState = 0

    @Volatile
    private var mCleared = false

    fun <T> getAnyTag(key: K): T? {
        return try {
            if (key is Int) {
                synchronized(mIntBagOfTags) {
                    mIntBagOfTags[key] as? T
                }
            } else {
                synchronized(mBagOfTags) {
                    mBagOfTags[key] as? T
                }
            }
        } catch (e: Exception) {
            null
        }
    }

    fun <T> setTagIfAbsent(key: K, providerValue: () -> T?): T? {
        var previous: T? = null
        var newValue: T? = null
        if (key is Int) {
            synchronized(mIntBagOfTags) {
                if (mIntBagOfTags.containsKey(key)) {
                    previous = mIntBagOfTags[key] as? T?
                } else {
                    newValue = providerValue()
                    mIntBagOfTags[key] = newValue
                }
            }
        } else {
            synchronized(mBagOfTags) {
                if (mBagOfTags.containsKey(key)) {
                    previous = mBagOfTags[key] as? T?
                } else {
                    newValue = providerValue()
                    mBagOfTags[key] = newValue
                }
            }
        }

        return previous ?: newValue
    }

    fun <T> setTagIfNull(key: K, providerValue: () -> T): T {
        var previous: T?
        if (key is Int) {
            synchronized(mIntBagOfTags) {
                previous = mIntBagOfTags[key] as? T?
                if (previous == null) {
                    val newValue = providerValue()
                    mIntBagOfTags[key] = newValue
                    return newValue
                }
            }
        } else {
            synchronized(mBagOfTags) {
                previous = mBagOfTags[key] as? T?
                if (previous == null) {
                    val newValue = providerValue()
                    mBagOfTags[key] = newValue
                    return newValue
                }
            }
        }
        return previous!!
    }

    fun setTag(key: K, value: Any?) {
        if (key is Int) {
            synchronized(mIntBagOfTags) {
                mIntBagOfTags[key] = value
            }
        } else {
            synchronized(mBagOfTags) {
                mBagOfTags[key] = value
            }
        }
    }

    fun remove(key: K) {
        if (key is Int) {
            synchronized(mIntBagOfTags) {
                mIntBagOfTags.remove(key)
            }
        } else {
            synchronized(mBagOfTags) {
                mBagOfTags.remove(key)
            }
        }
    }

    fun containsKey(key: K): Boolean {
        return if (key is Int) {
            synchronized(mIntBagOfTags) {
                mIntBagOfTags.containsKey(key)
            }
        } else {
            synchronized(mBagOfTags) {
                mBagOfTags.containsKey(key)
            }
        }
    }

    fun clear() {
        mCleared = true
        // Since clear() is final, this method is still called on mock objects
        // and in those cases, mBagOfTags is null. It'll always be empty though
        // because setTagIfAbsent and getTag are not final so we can skip
        // clearing it
        if (initState == 1) {
            synchronized(mIntBagOfTags) {
                for (value in mIntBagOfTags.valueIterator()) {
                    // see comment for the similar call in setTagIfAbsent
                    closeWithRuntimeException(value)
                }
            }
        } else if (initState == 2) {
            synchronized(mBagOfTags) {
                for (value in mBagOfTags.values) {
                    // see comment for the similar call in setTagIfAbsent
                    closeWithRuntimeException(value)
                }
            }
        }
    }

    private fun closeWithRuntimeException(obj: Any?) {
        if (obj is Closeable) {
            try {
                obj.close()
            } catch (e: IOException) {
                throw RuntimeException(e)
            }
        }
    }
}

private val appAnyBags = ReferenceBag<Int>()

val Activity.activityBagOfTags: ReferenceBag<String>?
    get() {
        if (isFinishing || isDestroyed) {
            return null
        }
        val key = System.identityHashCode(this)
        return appAnyBags.setTagIfNull(key) {
            ReferenceBag()
        }
    }

/**
 * [LifecycleOwner] 存储数据，可以作为临时缓存，当[Lifecycle.State.DESTROYED]后数据销毁
 */
val LifecycleOwner.mBagOfTags: ReferenceBag<String>?
    get() {
        if (this is Activity) {
            return (this as Activity).activityBagOfTags
        }
        val key = System.identityHashCode(this)
        return appAnyBags.setTagIfNull(key) {
            doOnDestroy {
                appAnyBags.remove(key)
            }
            ReferenceBag()
        }
    }

val Activity.asLifecycleOwner: LifecycleOwner
    get() = if (this is LifecycleOwner) {
        this
    } else {
        obtainLifecycleCompat(this)
    }

private fun obtainLifecycleCompat(activity: Activity): ActivityLifecycleCompat {
    val bagOfTags = activity.activityBagOfTags ?: return ActivityLifecycleCompat().also {
        it.handleLifecycleEvent(Lifecycle.Event.ON_CREATE)
        it.handleLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    }
    return bagOfTags.setTagIfNull("ActivityLifecycleCompat") {
        ActivityLifecycleCompat()
    }
}

class ActivityLifecycleCompat : LifecycleOwner {

    private val lifecycleRegistry = LifecycleRegistry(this)

    override val lifecycle: Lifecycle = lifecycleRegistry

    fun handleLifecycleEvent(event: Lifecycle.Event) {
        lifecycleRegistry.handleLifecycleEvent(event)
    }
}

fun init() {
    appContext.registerActivityLifecycleCallbacks(object : Application.ActivityLifecycleCallbacks {
        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
            if (activity !is LifecycleOwner) {
                obtainLifecycleCompat(activity).handleLifecycleEvent(Lifecycle.Event.ON_CREATE)
            }
        }

        override fun onActivityStarted(activity: Activity) {
            if (activity !is LifecycleOwner) {
                activity.activityBagOfTags
                    ?.getAnyTag<ActivityLifecycleCompat>("ActivityLifecycleCompat")
                    ?.handleLifecycleEvent(Lifecycle.Event.ON_START)
            }
        }

        override fun onActivityResumed(activity: Activity) {
            if (activity !is LifecycleOwner) {
                activity.activityBagOfTags
                    ?.getAnyTag<ActivityLifecycleCompat>("ActivityLifecycleCompat")
                    ?.handleLifecycleEvent(Lifecycle.Event.ON_RESUME)
            }
        }

        override fun onActivityPaused(activity: Activity) {
            if (activity !is LifecycleOwner) {
                activity.activityBagOfTags
                    ?.getAnyTag<ActivityLifecycleCompat>("ActivityLifecycleCompat")
                    ?.handleLifecycleEvent(Lifecycle.Event.ON_PAUSE)
            }
        }

        override fun onActivityStopped(activity: Activity) {
            if (activity !is LifecycleOwner) {
                activity.activityBagOfTags
                    ?.getAnyTag<ActivityLifecycleCompat>("ActivityLifecycleCompat")
                    ?.handleLifecycleEvent(Lifecycle.Event.ON_STOP)
            }
        }

        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
            // do nothing
        }

        override fun onActivityDestroyed(activity: Activity) {
            if (activity !is LifecycleOwner) {
                activity.activityBagOfTags
                    ?.getAnyTag<ActivityLifecycleCompat>("ActivityLifecycleCompat")
                    ?.handleLifecycleEvent(Lifecycle.Event.ON_DESTROY)
                activity.activityBagOfTags?.remove("ActivityLifecycleCompat")
            }
            appAnyBags.remove(System.identityHashCode(activity))
        }
    })
}