package com.overseas.common.ext

import androidx.annotation.MainThread
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import com.overseas.common.utils.getAccessibleField
import com.overseas.common.utils.postToMainThread
import com.overseas.common.utils.removeCallbacks
import java.lang.reflect.Field
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 可以把liveData看成一个事件分发器，订阅时不会立刻收到初始值
 */
class ShareLiveData<T> : MutableLiveData<T> {

    private val source: LiveData<T>?

    private var mVersionField1: Field? = null

    private var mVersionField2: Field? = null

    constructor() : super() {
        this.source = null
    }

    constructor(value: T) : super(value) {
        this.source = null
    }

    constructor(source: LiveData<T>?) : super() {
        this.source = source
    }

    override fun observe(owner: LifecycleOwner, observer: Observer<in T>) {
        source?.apply { observe(owner, ObserverWrapper(this, observer)) } ?: super.observe(owner, ObserverWrapper(this, observer))
    }

    override fun observeForever(observer: Observer<in T>) {
        source?.apply { observeForever(ObserverWrapper(this, observer)) } ?: super.observeForever(ObserverWrapper(this, observer))
    }

    override fun removeObserver(observer: Observer<in T>) {
        source?.removeObserver(observer) ?: super.removeObserver(observer)
    }

    override fun removeObservers(owner: LifecycleOwner) {
        source?.removeObservers(owner) ?: super.removeObservers(owner)
    }

    override fun getValue(): T? {
        return if (source != null) {
            source.value
        } else {
            super.getValue()
        }
    }

    override fun hasObservers(): Boolean {
        return source?.hasObservers() ?: super.hasObservers()
    }

    override fun hasActiveObservers(): Boolean {
        return source?.hasActiveObservers() ?: super.hasActiveObservers()
    }

    override fun setValue(value: T) {
        if (source != null) {
            (source as? MutableLiveData)?.setValue(value)
        } else {
            super.setValue(value)
        }
    }

    override fun postValue(value: T) {
        if (source != null) {
            (source as? MutableLiveData)?.postValue(value)
        } else {
            super.postValue(value)
        }
    }

    private companion object {
        private const val MediatorLiveData_Source = "androidx.lifecycle.MediatorLiveData${'$'}Source"
    }

    inner class ObserverWrapper<T>(liveData: LiveData<T>, private val observer: Observer<in T>) : Observer<T> {

        var mFirstTime = liveData.hasValue()

        override fun onChanged(value: T) {
            if (!mFirstTime) {
                if (source != null && observer.javaClass.name == MediatorLiveData_Source) {
                    // 在MediatorLiveData需要比较version
                    try {
                        val field1 = mVersionField1 ?: source.javaClass.getAccessibleField("mVersion").also {
                            mVersionField1 = it
                            it.isAccessible = true
                        }
                        val field2 = mVersionField2 ?: <EMAIL>("mVersion").also {
                            mVersionField2 = it
                            it.isAccessible = true
                        }
                        field2.set(this@ShareLiveData, field1.get(source))
                    } catch (e: Throwable) {
                    }
                }
                observer.onChanged(value)
            } else {
                mFirstTime = false
            }
        }

        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (other?.javaClass == observer.javaClass) {
                other as Observer<*>
                if (observer == other) return true
            }
            if (javaClass != other?.javaClass) return false
            other as ObserverWrapper<*>
            if (observer != other.observer) return false
            return true
        }

        override fun hashCode(): Int {
            return observer.hashCode()
        }
    }
}

class DuObserverScope<T> internal constructor(
    private val liveData: LiveData<T>,
    private val observer: IDuObserverScope.(T) -> Unit,
    owner: LifecycleOwner?,
    forever: Boolean,
) : IDuObserverScope, Observer<T> {

    init {
        postToMainThread {
            if (owner == null) {
                liveData.observeForever(this)
            } else {
                if (forever) {
                    liveData.observeLifecycleForever(owner, this)
                } else {
                    liveData.observe(owner, this)
                }
            }
        }
    }

    override fun cancel() {
        postToMainThread {
            liveData.removeObserver(this)
        }
    }

    override fun onChanged(t: T) {
        observer.invoke(this, t)
    }
}

interface IDuObserverScope {
    fun cancel()
}

fun <T> LiveData<T>.toShare(): LiveData<T> {
    return ShareLiveData(this)
}

/**
 * 判断LiveData是否发射过数据，发射null也算发射过
 * @return true表示有发射过，反正则没发射过
 */
fun <T> LiveData<T>.hasValue(): Boolean {
    if (value != null) {
        // value不为null就说明发射过值
        return true
    }
    // realValue为null，说明发射过null
    return realValue() == null
}

/**
 * 获取LiveData真实mData，可能会是默认值[LiveData.NOT_SET]
 */
fun <T> LiveData<T>.realValue(): Any? {
    return value ?: try {
        javaClass.getAccessibleField("mData").get(this)
    } catch (e: Throwable) {
        null
    }
}

@MainThread
fun <T> LiveData<T>.observeLifecycleForever(owner: LifecycleOwner, observer: Observer<in T>) {
    val lifecycleObserver = object : LifecycleEventObserver {
        override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
            if (event == Lifecycle.Event.ON_DESTROY) {
                owner.lifecycle.removeObserver(this)
                removeObserver(observer)
            }
        }
    }
    owner.lifecycle.addObserver(lifecycleObserver)
    observeForever(observer)
}

@MainThread
fun <T> LiveData<T>.observeOnce(owner: LifecycleOwner, observer: Observer<in T>) {
    val o = object : Observer<T> {
        override fun onChanged(t: T) {
            removeObserver(this)
            observer.onChanged(t)
        }
    }
    observe(owner, o)
}

@MainThread
fun <T> LiveData<T>.observeForeverOnce(observer: Observer<in T>) {
    val o = object : Observer<T> {
        override fun onChanged(t: T) {
            removeObserver(this)
            observer.onChanged(t)
        }
    }
    observeForever(o)
}

@MainThread
fun <T> LiveData<T>.observeLifecycleForeverOnce(owner: LifecycleOwner, observer: Observer<in T>) {
    val o = object : Observer<T> {
        override fun onChanged(t: T) {
            removeObserver(this)
            observer.onChanged(t)
        }
    }
    observeLifecycleForever(owner, o)
}

fun <T> LiveData<T>.observeScope(owner: LifecycleOwner? = null, observer: IDuObserverScope.(T) -> Unit): IDuObserverScope {
    return DuObserverScope(this, observer, owner, false)
}

fun <T> LiveData<T>.observeForeverScope(owner: LifecycleOwner? = null, observer: IDuObserverScope.(T) -> Unit): IDuObserverScope {
    return DuObserverScope(this, observer, owner, true)
}

interface Disposable {
    fun dispose()

    fun isDisposed(): Boolean
}

class TimeoutLiveData<T>(timeout: Long, unit: TimeUnit = TimeUnit.MILLISECONDS) : LiveData<T?>(), Disposable {

    private val disposable = AtomicBoolean(false)

    private val timeoutAction = Runnable {
        if (!isDisposed()) {
            disposable.lazySet(true)
            value = null
        }
    }

    init {
        postToMainThread(unit.toMillis(timeout), timeoutAction)
    }

    override fun dispose() {
        if (disposable.compareAndSet(false, true)) {
            removeCallbacks(timeoutAction)
        }
    }

    override fun isDisposed(): Boolean = disposable.get()

}

fun <T> MediatorLiveData<T>.addTimeoutSource(timeout: Long, unit: TimeUnit = TimeUnit.MILLISECONDS): Disposable {
    return TimeoutLiveData<T>(timeout, unit).also {
        addSource(it) { _ ->
            value = null
        }
    }
}

/**
 * 给指定的liveData添加一个超时等待，超过设定的时间会收到一个null
 */
fun <T> LiveData<T>.withTimeout(timeout: Long, unit: TimeUnit = TimeUnit.MILLISECONDS, waitNewValue: Boolean = false): LiveData<T?> {
    return MediatorLiveData<T?>().apply {
        val disposable = addTimeoutSource(timeout, unit)
        val oldValue = realValue()
        addSource(this@withTimeout) {
            if (waitNewValue && oldValue === it) {
                return@addSource
            }
            if (!disposable.isDisposed()) {
                value = it
            } else {
                disposable.dispose()
            }
        }
    }
}
