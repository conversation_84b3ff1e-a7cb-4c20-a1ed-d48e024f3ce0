package com.overseas.common.ext

import android.R.attr.maxLength
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Outline
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.os.Build
import android.os.SystemClock
import android.text.InputFilter
import android.text.InputFilter.LengthFilter
import android.view.*
import android.view.animation.Interpolator
import android.view.inputmethod.InputMethodManager
import android.widget.*
import androidx.annotation.*
import androidx.constraintlayout.helper.widget.Flow
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.content.res.use
import androidx.core.view.*
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.google.android.material.tabs.TabLayoutMediator.TabConfigurationStrategy
import com.overseas.common.utils.suppressLayoutCompat
import kotlinx.coroutines.*
import java.lang.Runnable
import java.lang.reflect.InvocationTargetException
import java.lang.reflect.Method
import java.nio.charset.Charset
import kotlin.coroutines.resume


class BreatheInterpolator : Interpolator {
    override fun getInterpolation(input: Float): Float {
        val x = 6 * input
        val k = 1.0f / 3
        val t = 6
        val n = 1 //控制函数周期，这里取此函数的第一个周期
        val PI = Math.PI
        var output = 0f
        if (x >= (n - 1) * t && x < (n - (1 - k)) * t) {
            output =
                (0.5 * Math.sin((PI / (k * t) * (x - k * t / 2 - (n - 1) * t)).toDouble()) + 0.5).toFloat()
        } else if (x >= (n - (1 - k)) * t && x < n * t) {
            output = Math.pow(
                0.5 * Math.sin((PI / ((1 - k) * t) * (x - (3 - k) * t / 2 - (n - 1) * t)).toDouble()) + 0.5,
                2.0
            ).toFloat()
        }
        return output
    }
}

inline fun View.doOnApplyWindowInsets(crossinline callback: (View, InitialPadding, WindowInsetsCompat) -> WindowInsetsCompat) {
    val initialPadding = recordInitialPaddingForView(this)
    doOnAttach {
        ViewCompat.setOnApplyWindowInsetsListener(it) { v, insets ->
            callback(v, initialPadding, insets)
        }
        requestApplyInsets()
    }
}

data class InitialPadding(
    val left: Int, val top: Int,
    val right: Int, val bottom: Int,
)

fun recordInitialPaddingForView(view: View) = InitialPadding(
    view.paddingLeft, view.paddingTop, view.paddingRight, view.paddingBottom
)


private open class ViewUtilsBase {

    open fun setLeftTopRightBottom(v: View, left: Int, top: Int, right: Int, bottom: Int): Boolean {
        fetchSetFrame()
        try {
            if (sSetFrameMethod != null) {
                sSetFrameMethod?.invoke(v, left, top, right, bottom)
                return true
            }
        } catch (e: IllegalAccessException) {
            // Do nothing
        } catch (e: InvocationTargetException) {
            throw RuntimeException(e.cause)
        }
        return false
    }

    /**
     * Note, this is only called on API 18 and older.
     */
    @SuppressLint("PrivateApi", "SoonBlockedPrivateApi")
    private fun fetchSetFrame() {
        if (!sSetFrameFetched) {
            try {
                sSetFrameMethod = View::class.java.getDeclaredMethod(
                    "setFrame",
                    Int::class.javaPrimitiveType,
                    Int::class.javaPrimitiveType,
                    Int::class.javaPrimitiveType,
                    Int::class.javaPrimitiveType
                )
                sSetFrameMethod?.isAccessible = true
            } catch (e: NoSuchMethodException) {
                // Do nothing
            }
            sSetFrameFetched = true
        }
    }

    companion object {
        private var sSetFrameMethod: Method? = null
        private var sSetFrameFetched = false
    }

}

@RequiresApi(22)
private open class ViewUtilsApi22 : ViewUtilsBase() {

    @SuppressLint("NewApi") // Lint doesn't know about the hidden method.
    override fun setLeftTopRightBottom(
        v: View,
        left: Int,
        top: Int,
        right: Int,
        bottom: Int,
    ): Boolean {
        return if (sTryHiddenSetLeftTopRightBottom) {
            // Since this was an @hide method made public, we can link directly against it with
            // a try/catch for its absence instead of doing the same through reflection.
            try {
                v.setLeftTopRightBottom(left, top, right, bottom)
                true
            } catch (e: NoSuchMethodError) {
                sTryHiddenSetLeftTopRightBottom = false
                super.setLeftTopRightBottom(v, left, top, right, bottom)
            }
        } else {
            super.setLeftTopRightBottom(v, left, top, right, bottom)
        }
    }

    companion object {
        /**
         * False when linking of the hidden setLeftTopRightBottom method has previously failed.
         */
        private var sTryHiddenSetLeftTopRightBottom = true
    }
}

@RequiresApi(29)
private open class ViewUtilsApi29 : ViewUtilsApi22() {

    override fun setLeftTopRightBottom(
        v: View,
        left: Int,
        top: Int,
        right: Int,
        bottom: Int,
    ): Boolean {
        v.setLeftTopRightBottom(left, top, right, bottom)
        return true
    }
}

private val IMPL = if (Build.VERSION.SDK_INT >= 29) {
    ViewUtilsApi29()
} else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
    ViewUtilsApi22()
} else {
    ViewUtilsBase()
}

fun View.setLeftTopRightBottomCompat(left: Int, top: Int, right: Int, bottom: Int): Boolean {
    return IMPL.setLeftTopRightBottom(this, left, top, right, bottom)
}

fun linearGradient(
    orientation: GradientDrawable.Orientation,
    startColor: Int,
    endColor: Int,
    block: ((GradientDrawable) -> Unit)? = null,
): Drawable {
    return GradientDrawable().also {
        it.gradientType = GradientDrawable.LINEAR_GRADIENT
        it.colors = intArrayOf(startColor, endColor)
        it.orientation = orientation
        block?.invoke(it)
    }
}

fun linearGradient(
    orientation: GradientDrawable.Orientation,
    startColor: Int,
    centerColor: Int,
    endColor: Int,
    block: ((GradientDrawable) -> Unit)? = null,
): Drawable {
    return GradientDrawable().also {
        it.gradientType = GradientDrawable.LINEAR_GRADIENT
        it.colors = intArrayOf(startColor, centerColor, endColor)
        it.orientation = orientation
        block?.invoke(it)
    }
}

fun radiusDrawable(@ColorInt color: Int, vararg radii: Float): Drawable {
    return GradientDrawable().also {
        it.setColor(color)
        it.cornerRadii = radii
    }
}

fun newOvalBorderDrawable(@ColorInt color: Int, strokeWidth: Float) = GradientDrawable().apply {
    shape = GradientDrawable.OVAL
    setStroke(strokeWidth.toInt(), color)
}
//ShapeDrawable(OvalShape()).also {
//    it.paint.color = color
//    it.paint.style = Paint.Style.STROKE
//    it.paint.strokeWidth = strokeWidth
//}

fun allRadiusDrawable(@ColorInt color: Int, radius: Float): Drawable {
    return radiusDrawable(color, radius, radius, radius, radius, radius, radius, radius, radius)
}

fun topRadiusDrawable(@ColorInt color: Int, radius: Float): Drawable {
    return radiusDrawable(color, radius, radius, radius, radius, 0F, 0F, 0F, 0F)
}

fun bottomRadiusDrawable(@ColorInt color: Int, radius: Float): Drawable {
    return radiusDrawable(color, 0F, 0F, 0F, 0F, radius, radius, radius, radius)
}

fun leftRadiusDrawable(@ColorInt color: Int, radius: Float): Drawable {
    return radiusDrawable(color, radius, radius, 0F, 0F, 0F, 0F, radius, radius)
}

fun rightRadiusDrawable(@ColorInt color: Int, radius: Float): Drawable {
    return radiusDrawable(color, 0F, 0F, radius, radius, radius, radius, 0F, 0F)
}

fun Context.getAndroidDrawable(attr: Int = android.R.attr.selectableItemBackground): Drawable? {
    //  android.R.attr.selectableItemBackgroundBorderless
    return obtainStyledAttributes(intArrayOf(attr)).use {
        it.getDrawable(0)
    }
}

/**
 * 应用点击波纹特效
 * @param isBackground 是否应用于背景色（默认），前景色要求高版本api
 */
fun View.applyClickRippleEffect(
    attr: Int = android.R.attr.selectableItemBackground,
    isBackground: Boolean = true,
) {
    val drawable = context.getAndroidDrawable(attr) ?: return
    if (isBackground) {
        background = drawable
    } else {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            foreground = drawable
        } else if (this is FrameLayout) {
            this.foreground = drawable
        }
    }
}


/**
 * 避免在 [time] 内触发多次点击时间
 * @param time
 * @param runnable
 */
inline fun <T : View> T.click(
    time: Long = 250,
    crossinline block: (view: T) -> Unit,
) {
    setOnClickListener(object : View.OnClickListener {
        var lastClickTime: Long = 0
        override fun onClick(v: View?) {
            if (SystemClock.elapsedRealtime() - lastClickTime < time) {
                return
            }
            lastClickTime = SystemClock.elapsedRealtime()
            (v as? T)?.let(block)
        }
    })
}

inline fun <reified A : Activity> View.clickGoto(crossinline block: Intent.() -> Unit = {}) {
    click {
        startActivity<A>(block = block)
    }
}


/**
 * 避免在 [time] 内触发多次点击时间
 * @param time
 * @param block
 */
inline fun View.clickWithEffect(
    time: Long = 250,
    attr: Int = android.R.attr.selectableItemBackground,
    isBackground: Boolean = true,
    crossinline block: (view: View) -> Unit,
) {
    applyClickRippleEffect(attr, isBackground)
    click(time, block)
}


fun TextView.textColor(@ColorRes res: Int) = setTextColor(ContextCompat.getColor(context, res))

/**
 * 在view attached之前保持挂起，注意要把view添加到视图树，不然会一直挂起
 */
suspend fun View.suspendOnAttached() {
    return suspendCancellableCoroutine { cont: CancellableContinuation<Unit> ->
        doOnAttach {
            if (cont.isActive) {
                cont.resume(Unit)
            }
        }
    }
}

/**
 * 在view layout之前保持挂起，注意要把view添加到视图树，不然会一直挂起
 * 可以获取到view的尺寸
 */
suspend fun View.suspendOnLayout() {
    return suspendCancellableCoroutine { cont: CancellableContinuation<Unit> ->
        doOnLayout {
            if (cont.isActive) {
                cont.resume(Unit)
            }
        }
    }
}

/**
 * 在view post之前保持挂起，注意要把view添加到视图树，不然会一直挂起
 * 可以获取到view的尺寸
 * 注意区别于[delay]，不会导致被切线程
 */
suspend fun View.suspendOnPostDelay(delayMillis: Long = 0L) {
    return suspendCancellableCoroutine { cont: CancellableContinuation<Unit> ->
        val action = Runnable {
            cont.resume(Unit)
        }
        postDelayed(action, delayMillis)
        cont.invokeOnCancellation {
            removeCallbacks(action)
        }
    }
}

/**
 * 当[PopupWindow]依附的[Activity]销毁时，或者[View.getWindowToken]为null时，[PopupWindow.showAtLocation]或[PopupWindow.showAsDropDown]会抛出[WindowManager.BadTokenException]异常
 * 在[PopupWindow] 属于子[Window]，Type值范围[1000,1999]，不能单独显示，需要依附其他[Window]
 * 在一般的场景下[PopupWindow]依附的[Window]是[Activity]的[PhoneWindow]
 * 安全校验处理[PopupWindow]的弹出事件
 * @param block 弹出代码
 */
fun View.safePopupWindow(block: (anchorView: View) -> Unit) {
    val mContext = context
    if (mContext is Activity && (mContext.isFinishing || mContext.isDestroyed)) {
        return
    }
    if (this.windowToken != null) {
        try {
            block(this)
        } catch (e: Throwable) {
        }
    } else {
        doOnAttach {
            if (it.windowToken != null) {
                try {
                    block(it)
                } catch (e: Throwable) {
                }
            }
        }
    }
}

fun View.increaseTouchArea(increaseRect: Rect) {
    val mParent = parent as View
    mParent.post {
        val rect = Rect()
        this.getHitRect(rect)
        rect.left -= increaseRect.left
        rect.top -= increaseRect.top
        rect.right += increaseRect.right
        rect.bottom += increaseRect.bottom
        rect.left = rect.left.coerceAtLeast(0)
        rect.top = rect.top.coerceAtLeast(0)
        rect.right = rect.right.coerceAtLeast(0)
        rect.bottom = rect.bottom.coerceAtLeast(0)
        mParent.touchDelegate = TouchDelegate(rect, this)
    }
}

class SingleSelectGroup(
    private var mSelectedIndex: Int = -1,
    private val views: Array<out View>,
    clickWithEffect: Boolean? = null,
    isClickable: Boolean = true,
    private val selector: ((Int, View) -> Unit)? = null,
) {

    val selectedIndex: Int
        get() = mSelectedIndex

    init {
        views.forEachIndexed { index, view ->
            if (mSelectedIndex == index) {
                selector?.invoke(index, view)
                view.isSelected = true
            } else {
                view.isSelected = false
            }
            if (isClickable) {
                val click: (view: View) -> Unit = {
                    selected(index)
                }
                if (clickWithEffect != null && clickWithEffect) {
                    view.clickWithEffect(isBackground = clickWithEffect, block = click)
                } else {
                    view.click(block = click)
                }
            }
        }
    }

    fun selected(index: Int) {
        val view = views[index]
        if (mSelectedIndex != -1) {
            views.getOrNull(mSelectedIndex)?.isSelected = false
        }
        mSelectedIndex = index
        view.isSelected = true
        selector?.invoke(index, view)
    }

    fun unselectCurrent() {
        if (mSelectedIndex > -1) {
            views.getOrNull(mSelectedIndex)?.isSelected = false
            mSelectedIndex = -1
        }
    }
}

fun View.setCircleRect() {
    outlineProvider = object : ViewOutlineProvider() {
        override fun getOutline(view: View, outline: Outline) {

            outline.setRoundRect(
                0,
                0,
                view.width,
                view.height,
                view.width.coerceAtLeast(view.height).toFloat()
            )
        }
    }
    clipToOutline = true
}

fun View.applyRoundRect(radius: Float, overHalfHeight: Boolean = false) {
    outlineProvider = object : ViewOutlineProvider() {
        override fun getOutline(view: View, outline: Outline) {
            val r = if (overHalfHeight) radius else Math.min(view.height / 2f, radius)
            outline.setRoundRect(0, 0, view.width, view.height, r)
        }
    }
    clipToOutline = true
}

fun View.applyCircle() {
    outlineProvider = object : ViewOutlineProvider() {
        override fun getOutline(view: View, outline: Outline) {
            outline.setRoundRect(
                0,
                0,
                view.width,
                view.height,
                minOf(view.width, view.height).div(2f)
            )
        }
    }
    clipToOutline = true
}

fun Context.inflate(
    @LayoutRes resource: Int,
    parent: ViewGroup? = null,
    attachToRoot: Boolean = parent != null,
): View {
    return LayoutInflater.from(this).inflate(resource, parent, attachToRoot)
}

fun ViewGroup.inflate(
    @LayoutRes resource: Int,
    parent: ViewGroup? = this,
    attachToRoot: Boolean = parent != null,
): View {
    return this.context.inflate(resource, parent, attachToRoot)
}


/**
 * 添加item
 */
inline fun Flow.addItem(provider: () -> View) {
    val parent = parent as ConstraintLayout
    val flow = this
    val child = provider()
    require(child.parent == null)
    require(child.id != View.NO_ID)
    parent.addView(child)
    flow.addView(child)
}

/**
 * 删除item
 */
fun Flow.removeItem(removeView: View) {
    val parent = parent as ConstraintLayout
    val flow = this
    require(removeView.parent == parent)
    parent.removeView(removeView)
    flow.removeView(removeView)
}

/**
 * 设置items, 会完全覆盖旧数据
 */
inline fun Flow.setItems(count: Int, provider: (Int) -> View) {
    val parent = parent as ConstraintLayout
    val flow = this
    val ids = flow.referencedIds.toSet()
    val removeIds = IntArray(ids.size)
    var i = 0
    parent.children.forEachIndexed { index, child ->
        if (ids.contains(child.id)) {
            removeIds[i++] = index
        }
    }
    parent.suppressLayoutCompat(true)
    repeat(i) {
        parent.removeViewAt(removeIds[removeIds.size - 1 - it])
    }
    val refIds = IntArray(count)
    repeat(count) {
        val child = provider(it)
        require(child.parent == null)
        if (child.id == View.NO_ID) {
            child.id = View.generateViewId()
        }
        parent.addView(child)
        refIds[it] = child.id
    }
    flow.referencedIds = refIds
    flow.requestLayout()
    parent.suppressLayoutCompat(false)
}

/**
 * 更新items, 会复用已存在的view
 * 有使用限制，需要items都是同一种子view，否则请使用[setItems]
 */
inline fun <T : View> Flow.updateItems(count: Int, provider: () -> T, onBind: (Int, T) -> Unit) {
    val parent = parent as ConstraintLayout
    val flow = this
    val ids = flow.referencedIds
    val size = ids.size
    val refIds = IntArray(count)
    parent.suppressLayoutCompat(true)
    repeat(count) { index ->
        val child = if (index < size) {
            parent.findViewById(ids[index])
        } else {
            provider().also { child ->
                require(child.parent == null)
                if (child.id == View.NO_ID) {
                    child.id = View.generateViewId()
                }
                parent.addView(child)
            }
        }
        refIds[index] = child.id
        onBind(index, child)
    }
    if (size > count) {
        repeat(size - count) { index ->
            parent.removeView(parent.findViewById(ids[count + index]))
        }
    }
    flow.referencedIds = refIds
    flow.requestLayout()
    parent.suppressLayoutCompat(false)
}

inline fun <reified T : View> Flow.updateItemsWithSelect(
    count: Int,
    provider: () -> T,
    onBind: (Int, T) -> Unit,
    noinline selector: ((Int, View) -> Unit)? = null,
): SingleSelectGroup {
    val parent = parent as ConstraintLayout
    val flow = this
    val ids = flow.referencedIds
    val size = ids.size
    val refIds = IntArray(count)
    parent.suppressLayoutCompat(true)
    val selectViews = Array(count) { index ->
        val child = if (index < size) {
            parent.findViewById(ids[index])
        } else {
            provider().also { child ->
                require(child.parent == null)
                if (child.id == View.NO_ID) {
                    child.id = View.generateViewId()
                }
                parent.addView(child)
            }
        }
        refIds[index] = child.id
        onBind(index, child)
        child
    }
    if (size > count) {
        repeat(size - count) { index ->
            parent.removeView(parent.findViewById(ids[count + index]))
        }
    }
    flow.referencedIds = refIds
    flow.requestLayout()
    parent.suppressLayoutCompat(false)
    return SingleSelectGroup(0, selectViews, selector = selector)
}

private val defaultCharset = Charset.forName("GB2312")

/**
 * Desc 输入框过滤器
 */
class AppInputFilter(val charset: Charset = defaultCharset) {

    /**
     * 保留布局内部的过滤条件
     */
    private fun addFilter(editText: EditText, filter: InputFilter) {
        val mFilters = arrayOfNulls<InputFilter>(editText.filters.size + 1)
        editText.filters.forEachIndexed<InputFilter?> { i: Int, inputFilter: InputFilter? ->
            if (inputFilter != null) {
                mFilters[i] = inputFilter
            }
        }
        mFilters[editText.filters.size] = filter
        editText.filters = mFilters
    }

    /**
     * 长度过滤器
     */
    fun lengthFilter(editText: EditText, maxLen: Int) {
        val lengthFilter = InputFilter { source, _, _, _, _, _ ->
            val oldLen = editText.text.toString().toByteArray(charset)
            val left = maxLen - oldLen.size
            if (left <= 0) {
                return@InputFilter ""
            }
            val len: ByteArray = source.toString().toByteArray(charset)
            if (len.size <= left) {
                return@InputFilter null
            }
            return@InputFilter source.find(left)
        }
        addFilter(editText, lengthFilter)
    }

    private fun CharSequence.find(left: Int): String {
        var i = 1
        while (true) {
            val text = substring(0, i)
            if (text.toByteArray(charset).size > left) {
                return substring(0, i - 1)
            }
            i++
        }
    }


}

fun String.calculateLength(charset: Charset = defaultCharset): Int {
    return toByteArray(charset).size
}

fun EditText.setMaxLength(len: Int) {
    val filters = arrayOfNulls<InputFilter>(1)
    filters[0] = LengthFilter(len)
    setFilters(filters)
}

fun Job.asAutoDisposable(view: View, awaitAttach: Boolean = false) = AutoDisposableJob(view, this, awaitAttach)

class AutoDisposableJob(private val view: View, private val wrapped: Job, awaitAttach: Boolean) : Job by wrapped,
    View.OnAttachStateChangeListener {
    //我们实现了 Job 这个接口，但没有直接实现它的方法，而是用 wrapped 这个成员去代理这个接口
    override fun onViewAttachedToWindow(v: View) {
        attached = true
    }

    override fun onViewDetachedFromWindow(v: View) {
        //当 View 被移除的时候，取消协程
        if (attached) {
            cancel()
            view.removeOnAttachStateChangeListener(this)
        }
    }

    private fun isViewAttached() = ViewCompat.isAttachedToWindow(view)

    private var attached = false

    init {
        if (awaitAttach) {
            attached = isViewAttached()
            view.addOnAttachStateChangeListener(this)
        } else {
            if (isViewAttached()) {
                attached = true
                view.addOnAttachStateChangeListener(this)
            } else {
                cancel()
            }
        }

        //协程执行完毕时要及时移除 listener 免得造成泄露
        invokeOnCompletion {
            view.removeOnAttachStateChangeListener(this)
        }
    }
}


fun TabLayout.setupWidthViewPager2(
    viewPager2: ViewPager2,
    autoRefresh: Boolean = true,
    smoothScroll: Boolean = true,
    tabConfigurationStrategy: TabConfigurationStrategy,
) =
    TabLayoutMediator(
        this@TabLayout,
        viewPager2,
        autoRefresh,
        smoothScroll,
        tabConfigurationStrategy
    ).also { it.attach() }

fun com.overseas.common.tablayout.TabLayout.setupWidthViewPager2(
    viewPager2: ViewPager2,
    autoRefresh: Boolean = true,
    tabConfigurationStrategy: com.overseas.common.tablayout.TabLayoutMediator.TabConfigurationStrategy,
) =
    com.overseas.common.tablayout.TabLayoutMediator(
        this@TabLayout,
        viewPager2,
        autoRefresh,
        tabConfigurationStrategy
    ).also { it.attach() }

context(View)
val layoutInflater: LayoutInflater
    get() = LayoutInflater.from(<EMAIL>)

fun TextView.setLeftCompoundDrawable(drawable: Drawable?) {
    setCompoundDrawables(drawable, null, null, null)
}

fun TextView.setTopCompoundDrawable(drawable: Drawable?) {
    setCompoundDrawables(null, drawable, null, null)
}

fun TextView.setRightCompoundDrawable(drawable: Drawable?) {
    setCompoundDrawables(null, null, drawable, null)
}

fun TextView.setBottomCompoundDrawable(drawable: Drawable?) {
    setCompoundDrawables(null, null, null, drawable)
}

val View.asText: TextView?
    get() = this as? TextView

val View.asImage: ImageView?
    get() = this as? ImageView

val View.asGroup: ViewGroup?
    get() = this as? ViewGroup
val View.inflater: LayoutInflater
    get() = LayoutInflater.from(context)

fun ViewGroup.addChildWithMatchParentParams(child: View) {
    addView(child, ViewGroup.LayoutParams(-1, -1))
}

fun ViewGroup.addChildWithWrapContentParams(child: View) {
    addView(child, ViewGroup.LayoutParams(-2, -2))
}

fun View.escapeFromParent() {
    (parent as? ViewGroup)?.removeView(this)
}

fun EditText.hideSoftKeyboard() {
    ContextCompat.getSystemService(context, InputMethodManager::class.java)?.hideSoftInputFromWindow(windowToken, 0)
}

fun TextView.setColorRes(@ColorRes colorRes: Int) {
    setTextColor(ContextCompat.getColor(context, colorRes))
}

fun View.setMatchParentLayoutParams() {
    layoutParams = ViewGroup.LayoutParams(-1, -1)
}

val View.parentViewGroup: ViewGroup?
    get() = parent as? ViewGroup

val RecyclerView.lastVisibleItem: Int
    get() {
        val manager = layoutManager as? LinearLayoutManager
        return manager?.findLastVisibleItemPosition() ?: -1
    }

/**
 * 底部可见的item的index
 * @return 如果没有可见的item，则返回RecyclerView.NO_POSITION
 * 计算规则是从最底部往上数
 */
val RecyclerView.bottomVisibleItemIndex: Int
    get() {
        val manager = layoutManager as? LinearLayoutManager ?: return RecyclerView.NO_POSITION
        if (manager.itemCount == 0) return RecyclerView.NO_POSITION
        return if (manager.reverseLayout) {
            manager.findFirstVisibleItemPosition()
        } else {
            val position = manager.findLastVisibleItemPosition()
            if (position == RecyclerView.NO_POSITION) {
                RecyclerView.NO_POSITION
            } else {
                manager.itemCount - 1 - position
            }
        }
    }

val ViewHolder.clickPosition: Int?
    get() = bindingAdapterPosition.takeIf {
        it != RecyclerView.NO_POSITION
    }

context(ViewHolder)
inline fun View.clickOfVH(
    time: Long = 250,
    crossinline block: (position: Int) -> Unit,
) {
    click(time) {
        try {
            clickPosition?.also(block)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}

context(ViewHolder)
inline fun View.clickWithEffectOfVH(
    time: Long = 250,
    attr: Int = android.R.attr.selectableItemBackground,
    isBackground: Boolean = true,
    crossinline block: (position: Int) -> Unit,
) {
    clickWithEffect(time, attr, isBackground) {
        try {
            clickPosition?.also(block)
        } catch (_: Exception) {
        }
    }
}
