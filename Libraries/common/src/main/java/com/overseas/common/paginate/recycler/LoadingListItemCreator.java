package com.overseas.common.paginate.recycler;

import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

/**
 * RecyclerView creator that will be called to create and bind loading list item.
 */
public interface LoadingListItemCreator {

    /**
     * Create new loading list item {@link RecyclerView.ViewHolder}.
     *
     * @param parent   parent ViewGroup.
     * @param viewType type of the loading list item.
     * @return ViewHolder that will be used as loading list item.
     */
    RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType);

    /**
     * Bind the loading list item.
     *
     * @param holder   loading list item ViewHolder.
     * @param position loading list item position.
     */
    void onBindViewHolder(RecyclerView.ViewHolder holder, int position);

}