package com.overseas.common.tablayout;

import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.util.Consumer;

import com.overseas.common.utils.UtilsKt;

public interface ITabEventListener {

    // 匹配模式
    boolean matchMode(@TabLayout.Mode int mode);

    // tabView的宽高确认
    default void onTabViewLayout(@NonNull TabLayout.TabView tabView) {

    }

    default void onReMeasureChildren(@NonNull LinearLayout slidingTabIndicator, Consumer<Integer> action) {
    }

    default void onRelayoutChildren(@NonNull LinearLayout slidingTabIndicator) {

    }

    default void onUpdateProgress(@NonNull LinearLayout slidingTabIndicator, @NonNull TabLayout.TabView currentTab, @Nullable TabLayout.TabView nextTab, float progress) {

    }

    default int getScaleTabContentWidth(@NonNull TabLayout.TabView tabView, int originSize) {
        return originSize;
    }

    default int getScaleTabContentHeight(@NonNull TabLayout.TabView tabView, int originSize) {
        return originSize;
    }

    default int getScaleTabWidth(@NonNull TabLayout.TabView tabView, int originSize) {
        return originSize;
    }

    default int getScaleTabHeight(@NonNull TabLayout.TabView tabView, int originSize) {
        return originSize;
    }

    default int getScaleTabLeft(@NonNull TabLayout.TabView tabView, int left) {
        if (UtilsKt.isLayoutRtl(tabView)) {
            return tabView.getScaleRight() - tabView.getScaleTabWidth();
        } else {
            return left;
        }
    }

    default int getScaleTabTop(@NonNull TabLayout.TabView tabView, int top) {
        return top;
    }

    default int getScaleTabRight(@NonNull TabLayout.TabView tabView, int right) {
        if (UtilsKt.isLayoutRtl(tabView)) {
            return right;
        } else {
            return tabView.getScaleLeft() + tabView.getScaleTabWidth();
        }
    }

    default int getScaleTabBottom(@NonNull TabLayout.TabView tabView, int bottom) {
        bottom = tabView.getScaleTop() + tabView.getScaleTabHeight();
        return bottom;
    }

    default int transformScrollX(int x) {
        return x;
    }

    default int transformScrollY(int y) {
        return y;
    }

}
