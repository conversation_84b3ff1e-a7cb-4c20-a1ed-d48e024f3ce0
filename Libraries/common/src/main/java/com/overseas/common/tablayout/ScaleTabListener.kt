package com.overseas.common.tablayout

import android.view.View
import android.widget.LinearLayout
import androidx.core.util.Consumer
import androidx.core.view.children
import androidx.core.view.forEach
import com.overseas.common.utils.isLayoutRtl
import kotlin.math.roundToInt

class ScaleTabListener(scale: Float = 1.6f) : ITabEventListener {

    private val selectedTabScale = scale

    private var startView: TabLayout.TabView? = null
    private var endView: TabLayout.TabView? = null

    private var maxScrollX = 0f

    private var extraWidth = 0

    override fun matchMode(mode: Int): Boolean {
        return TabLayout.MODE_SCROLLABLE == mode
    }

    override fun onReMeasureChildren(slidingTabIndicator: LinearLayout, action: Consumer<Int>) {
        slidingTabIndicator.clipChildren = false
        slidingTabIndicator.clipToPadding = false
        slidingTabIndicator.apply {
            if (selectedTabScale == 1f) {
                return
            }
            val largestTabWidth = children.fold(0) { acc, child ->
                val tabView = child as TabLayout.TabView
                if (child.visibility == View.VISIBLE) {
                    acc.coerceAtLeast(tabView.textView.measuredWidth)
                } else {
                    acc
                }
            }
            if (largestTabWidth <= 0) {
                // If we don't have a largest child yet, skip until the next measure pass
                return@apply
            }

            extraWidth = ((selectedTabScale - 1f) * largestTabWidth).roundToInt()
            action.accept(measuredWidth + extraWidth)
        }
    }

    override fun onRelayoutChildren(slidingTabIndicator: LinearLayout) {
        layoutChildren(slidingTabIndicator, true)
    }

    override fun onTabViewLayout(tabView: TabLayout.TabView) {
        tabView.clipChildren = false
        tabView.clipToPadding = false
        tabView.textView.apply {
            pivotX = if (tabView.isLayoutRtl()) {
                width.toFloat()
            } else {
                0f
            }
            pivotY = height * 0.8f
        }
    }

    override fun onUpdateProgress(slidingTabIndicator: LinearLayout, currentTab: TabLayout.TabView, nextTab: TabLayout.TabView?, progress: Float) {
        if (startView != null && startView != currentTab && startView != nextTab) {
            startView?.textView?.apply {
                scaleX = 1f
                scaleY = 1f
            }
        }

        if (endView != null && endView != currentTab && endView != nextTab) {
            endView?.textView?.apply {
                scaleX = 1f
                scaleY = 1f
            }
        }

        currentTab.textView.apply {
            val scale = selectedTabScale.plus(1.minus(selectedTabScale).times(progress))
            scaleX = scale
            scaleY = scale
            startView = currentTab
        }

        nextTab?.textView?.apply {
            val scale = 1.plus(selectedTabScale.minus(1f).times(progress))
            scaleX = scale
            scaleY = scale
            endView = nextTab
        }
        layoutChildren(slidingTabIndicator, false)
    }

    private fun layoutChildren(slidingTabIndicator: LinearLayout, fromOnLayout: Boolean) {
        var translationX = 0f
        slidingTabIndicator.forEach { child ->
            val tabView = child as TabLayout.TabView
            if (child.visibility == View.VISIBLE) {
                val rightGap = (tabView.textView.scaleX - 1).takeIf {
                    it != 0f
                }?.let { (it * tabView.textView.measuredWidth) } ?: 0f
                child.translationX = if (tabView.isLayoutRtl()) {
                    -translationX
                } else {
                    translationX
                }
                translationX += rightGap
            }
        }
        maxScrollX = slidingTabIndicator.right - (slidingTabIndicator.parent as View).width - extraWidth + translationX
    }

    override fun getScaleTabContentWidth(tabView: TabLayout.TabView, originSize: Int): Int {
        return tabView.textView.scaleX.times(originSize).toInt()
    }

    override fun getScaleTabContentHeight(tabView: TabLayout.TabView, originSize: Int): Int {
        return tabView.textView.scaleY.times(originSize).toInt()
    }

    override fun getScaleTabWidth(tabView: TabLayout.TabView, originSize: Int): Int {
        return originSize.plus(tabView.textView.scaleX.minus(1).times(tabView.textView.width).toInt())
    }

    override fun getScaleTabHeight(tabView: TabLayout.TabView, originSize: Int): Int {
        return originSize.plus(tabView.textView.scaleY.minus(1).times(tabView.textView.height).toInt())
    }

    override fun transformScrollX(x: Int): Int {
        return maxScrollX.toInt().coerceAtMost(x)
    }
}