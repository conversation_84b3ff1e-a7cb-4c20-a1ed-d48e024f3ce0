package com.overseas.common.utils

import android.animation.Animator
import androidx.core.animation.doOnEnd
import kotlinx.coroutines.suspendCancellableCoroutine

/**
 * @param cancelAnim 协程取消动画是否取消
 */
suspend fun Animator.suspendOnEnd(cancelAnim: Boolean = false) {
    suspendCancellableCoroutine<Unit> { continuation ->
        if (!isRunning) {
            start()
        }
        doOnEnd {
            continuation.resumeIfActive(Unit)
        }
        continuation.invokeOnCancellation {
            if (cancelAnim) {
                cancel()
            }
        }
    }
}