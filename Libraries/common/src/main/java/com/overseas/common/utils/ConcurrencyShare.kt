package com.overseas.common.utils

import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.yield
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicInteger
import kotlin.coroutines.coroutineContext

class ConcurrencyShare constructor(
    private val successResultKeepTime: Long = 0,
    private val timeoutByCancellation: Long = 300,
    private val scope: CoroutineScope = CoroutineScope(
        Dispatchers.IO + SupervisorJob() + CoroutineExceptionHandler { _, _ ->
        }
    )
) {
    companion object {
        val globalInstance by lazy {
            ConcurrencyShare()
        }
    }

    private val caches = ConcurrentHashMap<String, Item<*>>()

    @Suppress("UNCHECKED_CAST")
    suspend fun <T> joinPreviousOrRun(key: String, keepTime: Long = successResultKeepTime, block: suspend CoroutineScope.() -> T): T {
        while (true) {
            val activeTask = caches[key] ?: break
            if (activeTask.task.isCancelled) {
                yield()
                continue
            }

            if (activeTask.task.isCompleted) {
                return activeTask.task.getCompleted() as T
            }

            val counter = activeTask.counter.get()
            if (counter < 0) {
                yield()
                continue
            }
            if (activeTask.counter.compareAndSet(counter, counter + 1)) {
                return awaitItem(activeTask) as T
            } else {
                yield()
            }
        }

        val contextInfo = coroutineContext.minusKey(Job)
        val newTask = scope.async(start = CoroutineStart.LAZY) {
            withContext(contextInfo) {
                block()
            }
        }
        val item = Item(newTask)
        invokeWhenCompletion(key, item, keepTime)

        while (true) {
            val otherTask = caches.putIfAbsent(key, item)
            if (otherTask != null) {
                if (otherTask.task.isCancelled) {
                    yield()
                    continue
                }

                if (otherTask.task.isCompleted) {
                    return otherTask.task.getCompleted() as T
                }

                val counter = otherTask.counter.get()
                if (counter < 0) {
                    yield()
                } else if (otherTask.counter.compareAndSet(counter, counter + 1)) {
                    newTask.cancel()
                    return awaitItem(otherTask) as T
                } else {
                    yield()
                }
            } else {
                return awaitItem(item)
            }
        }
    }

    suspend fun <T> cancelPreviousThenRun(key: String, keepTime: Long = successResultKeepTime, block: suspend CoroutineScope.() -> T): T {
        val contextInfo = coroutineContext.minusKey(Job)
        val newTask = scope.async(start = CoroutineStart.LAZY) {
            withContext(contextInfo) {
                block()
            }
        }
        val item = Item(newTask)
        invokeWhenCompletion(key, item, keepTime)
        val oldTask = caches.put(key, item)
        oldTask?.task?.cancelAndJoin()
        return awaitItem(item)
    }

    private suspend fun <T> awaitItem(item: Item<T>): T {
        try {
            return item.task.await()
        } finally {
            val count = item.counter.decrementAndGet()
            if (count == 0 && item.task.isActive) {
                scope.launch {
                    delay(timeoutByCancellation)
                    if (item.counter.compareAndSet(0, -1) && item.task.isActive) {
                        item.task.cancel()
                    }
                }
            }
        }
    }

    private fun invokeWhenCompletion(key: String, item: Item<*>, keepTime: Long) {
        item.task.invokeOnCompletion {
            if (it != null) {
                caches.remove(key, item)
            } else {
                scope.launch {
                    delay(keepTime)
                    caches.remove(key, item)
                }
            }
        }
    }

    private class Item<T>(
        val task: Deferred<T>,
        val counter: AtomicInteger = AtomicInteger(1)
    )
}