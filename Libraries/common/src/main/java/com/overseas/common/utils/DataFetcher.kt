package com.overseas.common.utils

import com.overseas.common.utils.TaskFetchMode.Async
import com.overseas.common.utils.TaskFetchMode.Latest
import com.overseas.common.utils.TaskFetchMode.Sync
import com.overseas.common.utils.TaskRunMode.Auto
import com.overseas.common.utils.TaskRunMode.Default
import com.overseas.common.utils.TaskRunMode.Fast
import com.overseas.common.utils.TaskRunMode.Local
import com.overseas.common.utils.TaskRunMode.Remote
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.async
import kotlinx.coroutines.cancel
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.supervisorScope
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.io.Serializable
import java.util.concurrent.atomic.AtomicInteger

/**
 * [Default]: 同时请求本地数据源和远程数据源，哪个数据源先返回先发射哪个，但如果远程数据源先返回会取消本地数据源发射数据
 * [Auto]: 同时请求本地数据源和远程数据源，哪个数据源先返回先发射哪个，不会取消其他数据源。
 * [Fast]: 同时请求本地数据源和远程数据源，哪个数据源先返回先发射哪个，然后取消另一个数据源发射数据。
 * [Local]: 仅请求本地数据源然后发射。
 * [Remote]: 仅请求远程数据源然后发射。
 */
sealed class TaskRunMode {
    object Default : TaskRunMode()
    object Auto : TaskRunMode()
    object Fast : TaskRunMode()
    object Local : TaskRunMode()
    object Remote : TaskRunMode()
}

/**
 * [Sync] 同步的方式执行不同任务
 * [Async] 异步的方式执行不同任务
 * [Latest] 只处理最新的任务，在新任务到来时，取消上一个未完成的任务
 *
 */
sealed class TaskFetchMode {
    object Sync : TaskFetchMode()
    object Async : TaskFetchMode()
    object Latest : TaskFetchMode()
}

data class FetchResult<out INPUT, out DATA>(
    val input: INPUT,
    val fromLocal: Boolean,
    val value: Any?,
) {

    val isSuccess: Boolean get() = value !is Failure

    val isFailure: Boolean get() = value is Failure

    fun getOrNull(): DATA? =
        when {
            isFailure -> null
            else -> value as DATA?
        }

    fun exceptionOrNull(): Throwable? =
        when (value) {
            is Failure -> value.exception
            else -> null
        }

    internal class Failure(
        @JvmField
        val exception: Throwable
    ) : Serializable {
        override fun equals(other: Any?): Boolean = other is Failure && exception == other.exception
        override fun hashCode(): Int = exception.hashCode()
        override fun toString(): String = "Failure($exception)"
    }
}

private class TagInput<INPUT : IInput>(
    val tag: Int,
    val input: INPUT,
) : IInput by input

/**
 * 数据获取器，内部使用[flow]实现，适合在获取网络和缓存数据的场景使用，每个任务之间是串行执行
 * @param coroutineScope 任务执行默认上下文
 * @param taskFetchMode 任务调度模式，目前有三种模式，默认为[TaskFetchMode.Latest]，详情见[TaskFetchMode]
 * @param taskRunMode 数据获取模式，目前有五种模式，默认为[Default]，详情见[TaskRunMode]
 * @param local 本地数据源
 * @param cache 持久化远程数据
 * @param remote 远程数据源
 */
class DataFetcher<INPUT : IInput, DATA>(
    private val coroutineScope: CoroutineScope,
    private val taskFetchMode: TaskFetchMode = Latest,
    private val taskRunMode: TaskRunMode = Default,
    private val local: (suspend (INPUT) -> DATA?)? = null,
    private val cache: (suspend (INPUT, DATA) -> Unit)? = null,
    private val remote: (suspend (INPUT) -> DATA?)? = null
) {

    private val requestEvent = MutableSharedFlow<Pair<TagInput<INPUT>, TaskRunMode>>(extraBufferCapacity = 10)

    private val counter = AtomicInteger(0)

    private val _flow = MutableSharedFlow<FetchResult<TagInput<INPUT>, DATA?>>(extraBufferCapacity = 10)

    private val remoteConcurrencyShare = ConcurrencyShare()

    val flow: Flow<FetchResult<INPUT, DATA?>>
        get() = _flow.map {
            FetchResult(it.input.input, it.fromLocal, it.value)
        }

    init {
        coroutineScope.launch {
            if (taskFetchMode == Latest) {
                requestEvent.collectLatest {
                    handeEventRequest(it)
                }
            } else {
                requestEvent.collect {
                    if (taskFetchMode == Async) {
                        coroutineScope.launch {
                            handeEventRequest(it)
                        }
                    } else {
                        handeEventRequest(it)
                    }
                }
            }
        }
    }

    suspend fun fetchResultByInput(input: INPUT, mode: TaskRunMode = taskRunMode): FetchResult<INPUT, DATA?> {
        val tag = counter.incrementAndGet()
        return suspendCancellableCoroutine { continuation ->
            coroutineScope.launch {
                _flow.filter {
                    it.input.tag == tag
                }.collect {
                    cancel()
                    continuation.resumeIfActive(FetchResult(it.input.input, it.fromLocal, it.value))
                }
            }
            coroutineScope.launch {
                requestEvent.emit(TagInput(tag, input) to mode)
            }
        }
    }

    private suspend fun handeEventRequest(params: Pair<TagInput<INPUT>, TaskRunMode>) {
        val (tagInput, mode) = params
        val input = tagInput.input
        supervisorScope {
            var remoteDeferred: Deferred<DATA?>? = null

            val localDeferred = if (local != null && mode != Remote) {
                async {
                    local.invoke(input)?.also { data ->
                        if (mode == Fast) {
                            remoteDeferred?.cancel()
                        }
                        _flow.emit(FetchResult(tagInput, true, data))
                    }
                }
            } else {
                null
            }

            remoteDeferred = if (remote != null && mode != Local) {
                async {
                    var fromShare = true
                    val result = remoteConcurrencyShare.joinPreviousOrRun(input.toString()) {
                        remote.invoke(input).also {
                            fromShare = false
                        }
                    }

                    result?.also { data ->
                        if (mode != Auto) {
                            localDeferred?.cancel()
                        }
                        if (!fromShare) {
                            runCoroutineCatching {
                                withContext(NonCancellable) {
                                    cache?.invoke(input, data)
                                }
                            }
                        }
                        _flow.emit(FetchResult(tagInput, false, data))
                    }
                }
            } else {
                null
            }
            ensureActive()
            val localResult = runCatching {
                localDeferred?.await()
            }
            val remoteResult = runCatching {
                remoteDeferred?.await()
            }
            ensureActive()
            if (localResult.isFailure) {
                _flow.emit(FetchResult(tagInput, true, FetchResult.Failure(localResult.exceptionOrNull()!!)))
            } else if (localResult.getOrNull() == null) {
                _flow.emit(FetchResult(tagInput, true, null))
            }
            if (remoteResult.isFailure) {
                _flow.emit(FetchResult(tagInput, false, FetchResult.Failure(remoteResult.exceptionOrNull()!!)))
            } else if (remoteResult.getOrNull() == null) {
                _flow.emit(FetchResult(tagInput, false, null))
            }
        }
    }

    /**
     * 获取新数据请求
     * @param input 入参
     * @param mode 请求模式
     */
    fun fetch(input: INPUT, mode: TaskRunMode? = taskRunMode) {
        coroutineScope.launch {
            requestEvent.emit(TagInput(counter.incrementAndGet(), input) to (mode ?: taskRunMode))
        }
    }

    fun update(input: INPUT, data: DATA) {
        coroutineScope.launch {
            runCoroutineCatching {
                withContext(NonCancellable) {
                    cache?.invoke(input, data)
                }
            }
            _flow.emit(FetchResult(TagInput(counter.incrementAndGet(), input), false, data))
        }
    }
}

interface IInput {
    val key: String
}