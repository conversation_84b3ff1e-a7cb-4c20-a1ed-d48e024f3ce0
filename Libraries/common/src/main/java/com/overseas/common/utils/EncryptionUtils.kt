package com.overseas.common.utils

import java.nio.charset.Charset
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.security.SecureRandom
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.Random
import kotlin.math.sqrt

object EncryptionUtils {

    private val HEX_DIGITS = charArrayOf(
        '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'
    )

    @Throws(NoSuchAlgorithmException::class)
    fun md5(msg: ByteArray): ByteArray {
        return MessageDigest.getInstance("MD5").digest(msg)
    }

    @Throws(NoSuchAlgorithmException::class)
    fun md5(value: String): String {
        return bytes2Hex(md5(value.toByteArray(Charset.forName("UTF-8"))))
    }

    fun sha1(value: String): String {
        val digest = MessageDigest.getInstance("SHA-1")
        val bytes = value.toByteArray(Charset.forName("UTF-8"))
        digest.update(bytes, 0, bytes.size)
        val sha1hash = digest.digest()
        return bytes2Hex(sha1hash)
    }

    fun generateRandomString(length: Int = 15): String {
        val random = SecureRandom()
        val nonce = ByteArray(length)
        random.nextBytes(nonce)
        return bytes2Hex(nonce)
    }

    fun bytes2Hex(bytes: ByteArray, toUpperCase: Boolean = true): String {
        if (bytes.isEmpty()) {
            return ""
        }
        val len = bytes.size
        val buf = CharArray(len shl 1)
        for (i in 0 until len) {
            val b = bytes[i].toInt()
            val index = i shl 1
            buf[index] = HEX_DIGITS[b shr 4 and 0xF]
            buf[index + 1] = HEX_DIGITS[b and 0xF]
        }
        return if (toUpperCase) {
            String(buf).uppercase(Locale.getDefault())
        } else {
            String(buf).lowercase(Locale.getDefault())
        }
    }

    fun formatTime(t: Long): String {
        val date = Date(t)
        val sd = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
        return sd.format(date)
    }

    fun getPageSize(): Int {
        try {
            val unsafeClass = Class.forName("sun.misc.Unsafe")
            val theUnsafe = unsafeClass.getDeclaredField("theUnsafe")
            theUnsafe.isAccessible = true
            val method = unsafeClass.getDeclaredMethod("pageSize")
            method.isAccessible = true
            return method.invoke(theUnsafe[null]) as Int
        } catch (e: Throwable) {
            e.printStackTrace()
        }
        return 0
    }


    /**
     * Get array with normal distribution.
     */
    fun getDistributedArray(n: Int, times: Int, r: Random): IntArray {
        val avg = n / 2
        val v: Int = if (n <= 50) {
            n
        } else if (n <= 100) {
            (n * 1.5).toInt()
        } else if (n <= 200) {
            n * 2
        } else {
            n * 3
        }
        val count = n * times
        val a = IntArray(count)
        var i = 0
        while (i < count) {
            val x = (sqrt(v.toDouble()) * r.nextGaussian() + avg).toInt()
            if (x in 0 until n) {
                a[i++] = x
            }
        }
        return a
    }

}