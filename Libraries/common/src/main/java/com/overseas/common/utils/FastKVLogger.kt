package com.overseas.common.utils

import com.qyqy.ucoo.utils.logE
import com.qyqy.ucoo.utils.logI
import com.qyqy.ucoo.utils.logW
import io.fastkv.FastKV

object FastKVLogger : FastKV.Logger {

    override fun i(name: String, message: String) {
        if (message != "gc finish") {
            logI(name, message)
        }
    }

    override fun w(name: String, e: Exception) {
        logW(name, e)
    }

    override fun e(name: String, e: Exception) {
        logE(name, e)
    }
}


object EmptyFastKVLogger : FastKV.Logger {

    override fun i(name: String, message: String) = Unit

    override fun w(name: String, e: Exception) = Unit

    override fun e(name: String, e: Exception) = Unit
}