package com.overseas.common.utils

import android.content.DialogInterface

interface IDialogEventObserver {

    fun addOnDismissListener(listener: DialogInterface.OnDismissListener)

    fun removeOnDismissListener(listener: DialogInterface.OnDismissListener)

    fun addOnCancelListener(listener: DialogInterface.OnCancelListener)

    fun removeOnCancelListener(listener: DialogInterface.OnCancelListener)
}