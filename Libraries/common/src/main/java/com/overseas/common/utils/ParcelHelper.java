package com.overseas.common.utils;

import android.os.Parcel;
import android.os.Parcelable;

public class ParcelHelper {

    public static <T extends Parcelable> T copy(Parcelable input) {
        Parcel parcel = Parcel.obtain();
        try {
            parcel.writeParcelable(input, 0);
            parcel.setDataPosition(0);
            return parcel.readParcelable(input.getClass().getClassLoader());
        } finally {
            parcel.recycle();
        }
    }

}
