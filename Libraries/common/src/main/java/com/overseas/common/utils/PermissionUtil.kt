package com.overseas.common.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Process
import android.os.SystemClock
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.ActivityResultCaller
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.ActivityResultRegistry
import androidx.activity.result.ActivityResultRegistryOwner
import androidx.activity.result.contract.ActivityResultContract
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.MainThread
import androidx.core.app.ActivityCompat
import androidx.core.app.ActivityOptionsCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.lifecycle.lifecycleScope
import com.overseas.common.ext.mBagOfTags
import com.overseas.common.ext.safeViewLifecycleScope
import io.fastkv.BuildConfig
import kotlinx.coroutines.CancellableContinuation
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.suspendCancellableCoroutine
import java.util.UUID
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicReference
import kotlin.coroutines.resume

object PermissionUtil {

    fun hasPermissions(vararg permissions: String): Boolean {
        val context = appContext
        val pid = Process.myPid()
        val uid = Process.myUid()
        for (permission in permissions) {
            val permission = if (permission == android.Manifest.permission.WRITE_EXTERNAL_STORAGE) {
                android.Manifest.permission.READ_EXTERNAL_STORAGE
            } else {
                permission
            }
            if (context.checkPermission(permission, pid, uid) != PackageManager.PERMISSION_GRANTED) {
                return false
            }
        }
        return true
    }

    fun allGran(grantResults: IntArray): Boolean {
        for (result in grantResults) {
            if (result != PackageManager.PERMISSION_GRANTED) {
                return false
            }
        }
        return true
    }
}


interface ActivityResultDeferred<O> {
    suspend fun await(): O?
}

/**
 * 基于ActivityResult封装，用来替代[Activity.startActivityForResult]或者[Fragment.startActivityForResult]
 *
 * 以回调的形式获取result，也支持协程同步写法获取
 *
 * 值得注意的是，原[Activity.startActivityForResult]或者[Fragment.startActivityForResult]需要在[Activity]或者[Fragment]的[Lifecycle.State.STARTED]生命周期之前创建[ActivityResultLauncher]，
 * 否则会抛异常，具体见[ActivityResultRegistry.register]
 *
 * 该扩展工具没有上述限制，可以在非[Lifecycle.State.DESTROYED]以外的任何生命周期中使用，对于一次性使用的调用建议使用[onlyOnceLaunch]或[onlyOnceAsync]
 *
 * @param caller    一般是activity 或者 fragment
 * @param contract  协议，描述了如何传递数据和处理返回数据，可以自定义，也可以使用sdk内置的[ActivityResultContract]
 * @param registry  一般情况下为null，也可以自定义
 *
 * 以下是sdk内置的[ActivityResultContract]说明，其中最常用的就是[StartActivityForResult]以及[RequestPermission]
 *
 * [StartActivityForResult]: 通用的Contract,不做任何转换，Intent作为输入，ActivityResult作为输出，这也是最常用的一个协定。
 * [RequestMultiplePermissions]： 用于请求一组权限
 * [RequestPermission]: 用于请求单个权限
 * [TakePicturePreview]: 调用MediaStore.ACTION_IMAGE_CAPTURE拍照，返回值为Bitmap图片
 * [TakePicture]: 调用MediaStore.ACTION_IMAGE_CAPTURE拍照，并将图片保存到给定的Uri地址，返回true表示保存成功。
 * [TakeVideo]: 调用MediaStore.ACTION_VIDEO_CAPTURE 拍摄视频，保存到给定的Uri地址，返回一张缩略图。
 * [PickContact]: 从通讯录APP获取联系人
 * [GetContent]: 提示用选择一条内容，返回一个通过ContentResolver#openInputStream(Uri)访问原生数据的Uri地址（content://形式） 。默认情况下，它增加了Intent#CATEGORY_OPENABLE, 返回可以表示流的内容。
 * [CreateDocument]: 提示用户选择一个文档，返回一个(file:/http:/content:)开头的Uri。
 * [OpenMultipleDocuments]: 提示用户选择文档（可以选择多个），分别返回它们的Uri，以List的形式。
 * [OpenDocumentTree]: 提示用户选择一个目录，并返回用户选择的作为一个Uri返回，应用程序可以完全管理返回目录中的文档。
 */
@Deprecated("只建议在申请权限时使用，代替RxPermission")
open class AppActivityResultLauncher<I, O>(
    caller: ActivityResultCaller,
    contract: ActivityResultContract<I, O>,
    registry: ActivityResultRegistry? = null
) {

    init {
        if (caller is LifecycleOwner && caller.lifecycle.currentState == Lifecycle.State.DESTROYED) {
            if (BuildConfig.DEBUG) {
                throw IllegalStateException("在destroyed以后创建无意义，后续还得兜底launch异常")
            }
        }
    }

    private var onlyOnce = false

    private var callback: ActivityResultCallback<O>? = null

    private val launcher: ActivityResultLauncher<I> = ActivityResultCallback<O> {
        callback?.apply {
            onActivityResult(it)
        }
        if (onlyOnce) {
            unregister()
        }
    }.run {
        when (caller) {
            is ComponentActivity -> {
                if (caller.lifecycle.currentState.isAtLeast(Lifecycle.State.STARTED)) {
                    val owner = ActivityResultLifecycleOwner(caller)
                    owner.trySafeState()
                    (registry ?: caller.activityResultRegistry).register(generateActivityResultKey(caller), owner, contract, this)
                        .also {
                            owner.resetState()
                        }
                } else {
                    registry?.let {
                        caller.registerForActivityResult(contract, it, this)
                    } ?: caller.registerForActivityResult(contract, this)
                }
            }

            is Fragment -> {
                prepareCallInternal(caller, contract, registry, this)
            }

            else -> {
                registry?.let {
                    caller.registerForActivityResult(contract, it, this)
                } ?: kotlin.run {
                    if (caller is ActivityResultRegistryOwner) {
                        caller.registerForActivityResult(contract, caller.activityResultRegistry, this)
                    } else {
                        caller.registerForActivityResult(contract, this)
                    }
                }
            }
        }
    }

    /**
     * 普通发起，以callback的形式接收结果
     */
    @JvmOverloads
    open fun launch(input: I, options: ActivityOptionsCompat? = null, callback: ActivityResultCallback<O>) {
        this.callback = callback
        try {
            launcher.launch(input, options)
        } catch (e: Exception) {
            if (BuildConfig.DEBUG) {
                throw e
            }
        }
    }

    /**
     * 一次性发起，以callback的形式接收结果，收到结果后内部会立刻[unregister]，之后不能在发起launch，否则会抛异常
     */
    open fun onlyOnceLaunch(input: I, options: ActivityOptionsCompat? = null, callback: ActivityResultCallback<O>) {
        onlyOnce = true
        launch(input, options, callback)
    }

    /**
     * 协程式发起，挂起式等待返回结果
     */
    @JvmOverloads
    open fun async(input: I, options: ActivityOptionsCompat? = null): ActivityResultDeferred<O> {
        return object : ActivityResultDeferred<O> {

            @Volatile
            var result: Lazy<O?>? = null

            @Volatile
            var continuation: CancellableContinuation<O>? = null

            init {
                launch(input, options) { value ->
                    continuation?.resume(value) ?: run {
                        result = lazyOf(value)
                    }
                    continuation = null
                }
            }

            override suspend fun await(): O? {
                return suspendCancellableCoroutine {
                    if (result != null) {
                        it.resume(result?.value)
                    } else {
                        continuation = it
                    }
                    result = null
                }
            }
        }
    }

    /**
     * 协程式发起，挂起式等待返回结果，收到结果后内部会立刻[unregister]，之后不能在发起launch，否则会抛异常
     */
    open fun onlyOnceAsync(input: I, options: ActivityOptionsCompat? = null): ActivityResultDeferred<O> {
        onlyOnce = true
        return async(input, options)
    }

    /**
     * 取消监听结果，注意调用该方法，不能在发起launch，否则会抛异常
     */
    fun unregister() {
        launcher.unregister()
    }

    private val Fragment.activityResultRegistry: ActivityResultRegistry?
        get() {
            val mHost = this.host
            if (mHost is ActivityResultRegistryOwner) {
                return mHost.activityResultRegistry
            }
            val activity = this.activity
            if (activity is FragmentActivity) {
                return activity.activityResultRegistry
            }
            return null
        }

    private fun <I, O> prepareCallInternal(
        fragment: Fragment,
        contract: ActivityResultContract<I, O>,
        registry: ActivityResultRegistry?,
        callback: ActivityResultCallback<O>,
    ): ActivityResultLauncher<I> {

        if (!fragment.lifecycle.currentState.isAtLeast(Lifecycle.State.STARTED)) {
            return registry?.let {
                fragment.registerForActivityResult(contract, it, callback)
            } ?: fragment.registerForActivityResult(contract, callback)
        }

        (registry ?: fragment.activityResultRegistry)?.also {
            val owner = ActivityResultLifecycleOwner(fragment)
            owner.trySafeState()
            return it.register(generateActivityResultKey(fragment), owner, contract, callback).also {
                owner.resetState()
            }
        }

        val ref = AtomicReference<ActivityResultLauncher<I>>()
        fragment.fragmentManager?.run {
            addFragmentOnAttachListener { _, f ->
                if (f === fragment) {
                    ref.set(
                        requireNotNull(f.activityResultRegistry).register(
                            generateActivityResultKey(f),
                            fragment,
                            contract,
                            callback
                        )
                    )
                }
            }
        } ?: fragment.lifecycle.run {
            addObserver(object : LifecycleEventObserver {
                override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                    if (event == Lifecycle.Event.ON_DESTROY) {
                        removeObserver(this)
                    } else if (ref.get() == null) {
                        removeObserver(this)
                        val mRegistry = requireNotNull(fragment.activityResultRegistry)
                        val owner = ActivityResultLifecycleOwner(fragment)
                        owner.trySafeState()
                        ref.set(mRegistry.register(generateActivityResultKey(fragment), owner, contract, callback))
                        owner.resetState()
                    }
                }
            })
        }

        return object : ActivityResultLauncher<I>() {
            override fun launch(input: I, options: ActivityOptionsCompat?) {
                val delegate =
                    ref.get() ?: throw IllegalStateException("Operation cannot be started before fragment " + "is in created state")
                delegate.launch(input, options)
            }

            override val contract: ActivityResultContract<I, *>
                get() = contract

            override fun unregister() {
                val delegate = ref.getAndSet(null)
                delegate?.unregister()
            }

        }
    }

    private fun generateActivityResultKey(owner: LifecycleOwner): String {
        val key = System.identityHashCode(owner).toString()
        val counter = owner.mBagOfTags?.run {
            setTagIfNull(key) {
                AtomicInteger()
            }
        } ?: AtomicInteger()
        return when (owner) {
            is ComponentActivity -> {
                "activity_rq#${counter.getAndIncrement()}"
            }

            is Fragment -> {
                "fragment_${UUID.randomUUID()}_rq#${counter.getAndIncrement()}"
            }

            else -> {
                "other_${UUID.randomUUID()}_rq#${counter.getAndIncrement()}"
            }
        }
    }
}

private class ActivityResultLifecycleOwner(provider: LifecycleOwner) : LifecycleOwner {

    private val mLifecycleRegistry = LifecycleRegistry(this)

    private var originState = Lifecycle.State.INITIALIZED

    init {
        provider.lifecycle.addObserver(object : LifecycleEventObserver {
            override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                originState = event.targetState
                mLifecycleRegistry.handleLifecycleEvent(event)
                if (event == Lifecycle.Event.ON_DESTROY) {
                    provider.lifecycle.removeObserver(this)
                }
            }
        })
    }

    override val lifecycle: Lifecycle = mLifecycleRegistry

    @MainThread
    fun getCurrentState(): Lifecycle.State = mLifecycleRegistry.currentState

    fun trySafeState() {
        if (mLifecycleRegistry.currentState.isAtLeast(Lifecycle.State.STARTED)) {
            mLifecycleRegistry.currentState = Lifecycle.State.CREATED
        }
    }

    fun resetState() {
        mLifecycleRegistry.currentState = originState
    }
}

private const val PERMISSION_DIALOG_THRESHOLD = 200L

class PermissionRequest constructor(
    val permissions: Array<String>,
    val descTitle: String = "",
    val descContent: String = "",
    val goSettingIfDenied: Boolean = false,
) {
    constructor(
        permission: String,
        descTitle: String = "",
        descContent: String = "",
        goSettingIfDenied: Boolean = false,
    ) : this(arrayOf(permission), descTitle, descContent, goSettingIfDenied)
}

/**
 * @param caller   一般是activity 或者 fragment
 * @param onEvent  0：系统权限弹窗曝光，1：系统权限弹-点击拒绝，2：系统权限弹-点击允许，3：自定义跳转到设置弹窗曝光，4：自定义跳转到设置弹窗-点击取消，5：自定义跳转到设置弹窗-点击去设置
 * 6: 权限自动拒绝，需要引导到设置手动开启
 */
class AppPermissionResultLauncher(
    private val caller: ActivityResultCaller,
    private val onEvent: ((Int) -> Unit)? = null,
) : AppActivityResultLauncher<PermissionRequest, Boolean>(caller, object : ActivityResultContract<PermissionRequest, Boolean>() {

    val delegate = ActivityResultContracts.RequestMultiplePermissions()

    override fun createIntent(context: Context, input: PermissionRequest): Intent {
        return delegate.createIntent(context, input.permissions)
    }

    override fun parseResult(resultCode: Int, intent: Intent?): Boolean {
        return delegate.parseResult(resultCode, intent).values.all {
            it
        }
    }

    override fun getSynchronousResult(context: Context, input: PermissionRequest): SynchronousResult<Boolean>? {
        return delegate.getSynchronousResult(context, input.permissions)?.run {
            SynchronousResult(value.values.all {
                it
            })
        }
    }

}) {

    private var multiplePermissionOnce = false

    override fun launch(input: PermissionRequest, options: ActivityOptionsCompat?, callback: ActivityResultCallback<Boolean>) {
        launch(arrayOf(input), callback)
    }

    fun launch(input: Array<PermissionRequest>, callback: ActivityResultCallback<Boolean>) {
        enqueuePermission(input, 0, callback)
    }

    fun onlyOnceLaunch(input: Array<PermissionRequest>, callback: ActivityResultCallback<Boolean>) {
        multiplePermissionOnce = true
        launch(input, callback)
    }

    fun async(input: Array<PermissionRequest>): ActivityResultDeferred<Boolean> {
        return object : ActivityResultDeferred<Boolean> {

            @Volatile
            var result: Lazy<Boolean?>? = null

            @Volatile
            var continuation: CancellableContinuation<Boolean>? = null

            init {
                launch(input) { value ->
                    continuation?.resume(value) ?: run {
                        result = lazyOf(value)
                    }
                    continuation = null
                }
            }

            override suspend fun await(): Boolean? {
                return suspendCancellableCoroutine {
                    if (result != null) {
                        it.resume(result?.value)
                    } else {
                        continuation = it
                    }
                    result = null
                }
            }
        }
    }

    fun onlyOnceAsync(input: Array<PermissionRequest>): ActivityResultDeferred<Boolean> {
        multiplePermissionOnce = true
        return async(input)
    }

    private fun enqueuePermission(inputArray: Array<PermissionRequest>, index: Int, callback: ActivityResultCallback<Boolean>) {
        val request = inputArray[index]
        requestPermission(request) {
            if (it == true) {
                val nextIndex = index.plus(1)
                if (nextIndex > inputArray.lastIndex) {
                    callback.onActivityResult(true)
                } else {
                    enqueuePermission(inputArray, nextIndex, callback)
                    return@requestPermission
                }
            } else {
                callback.onActivityResult(false)
            }
            if (multiplePermissionOnce) {
                unregister()
            }
        }
    }

    private fun requestPermission(input: PermissionRequest, callback: ActivityResultCallback<Boolean>) {
        val scopeCaller = when (caller) {
            is FragmentActivity -> {
                ActivityPermissionScope(caller)
            }

            is Fragment -> {
                FragmentPermissionScope(caller)
            }

            else -> null
        }
        var dialogJob: Job? = null
        val requestPermissionTimestamp = SystemClock.elapsedRealtime()
        scopeCaller?.apply {
            dialogJob = lifecycleScope.launchWhenStarted {
                delay(PERMISSION_DIALOG_THRESHOLD)
                onEvent?.invoke(0)
            }
        }

        super.launch(input, null) {
            dialogJob?.cancel()
            if (it != true) {
                // 通过这个时间差判断，是不是自动拒绝，可能有误差，一般人应该按不了这么快
                if (SystemClock.elapsedRealtime().minus(requestPermissionTimestamp) < PERMISSION_DIALOG_THRESHOLD
                    && scopeCaller?.shouldShowRequestPermissionRationale(input.permissions.first()) == false
                ) {
                    onEvent?.invoke(6)
                    if (input.goSettingIfDenied) {
                        onEvent?.invoke(3)
                        val activity = scopeCaller.activity
//                        CommonDialog.Builder(activity)
//                            .setTitle("获取${input.descTitle}失败")
//                            .setContent("${input.descContent}\n去设置打开权限？")
//                            .setMaxContentLine(10)
//                            .setContentGravity(Gravity.START)
//                            .setPositiveButton("去设置") { dialog ->
//                                onEvent?.invoke(5)
//                                dialog.dismiss()
//                                NotifyUtils.goToApplicationDetail(activity)
//                            }.setNegativeButton("取消") { dialog ->
//                                onEvent?.invoke(4)
//                                dialog.dismiss()
//                            }.show()
                    }
                } else {
                    onEvent?.invoke(1)
                }
            } else {
                onEvent?.invoke(2)
            }
            callback.onActivityResult(it)
        }
    }

    private interface IPermissionScope {

        val activity: Activity

        val lifecycleScope: LifecycleCoroutineScope

        fun shouldShowRequestPermissionRationale(permission: String): Boolean
    }

    private class ActivityPermissionScope(scope: FragmentActivity) : IPermissionScope {

        override val activity: Activity = scope

        override val lifecycleScope: LifecycleCoroutineScope = scope.lifecycleScope

        override fun shouldShowRequestPermissionRationale(permission: String): Boolean {
            return ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)
        }

    }

    private class FragmentPermissionScope(private val scope: Fragment) : IPermissionScope {

        override val activity: Activity
            get() = scope.requireActivity()

        override val lifecycleScope: LifecycleCoroutineScope
            get() = scope.safeViewLifecycleScope

        override fun shouldShowRequestPermissionRationale(permission: String): Boolean {
            return scope.shouldShowRequestPermissionRationale(permission)
        }
    }
}
