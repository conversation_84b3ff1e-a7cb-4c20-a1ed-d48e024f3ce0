package com.overseas.common.utils

import android.content.res.Configuration
import android.content.res.Resources
import android.os.Build
import java.util.Locale


@SuppressWarnings("NewApi")
fun Resources.currentLocale(): Locale = if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.N) {
    @Suppress("DEPRECATION")
    configuration.locale
} else {
    configuration.locales[0]
}

val Configuration.currentLocale: Locale
    get() = if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.N) {
        @Suppress("DEPRECATION")
        locale
    } else {
        locales.get(0) ?: Locale.getDefault()
    }

val Resources.currentLocale: Locale
    get() = configuration.currentLocale
