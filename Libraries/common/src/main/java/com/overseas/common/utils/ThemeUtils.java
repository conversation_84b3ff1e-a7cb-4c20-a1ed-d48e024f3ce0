package com.overseas.common.utils;

import android.content.res.ColorStateList;

import androidx.annotation.NonNull;

public class ThemeUtils {


    static final int[] DISABLED_STATE_SET = new int[]{-android.R.attr.state_enabled};
    static final int[] EMPTY_STATE_SET = new int[0];

    /**
     * Creates a color state list from the provided colors.
     *
     * @param textColor Regular text color.
     * @param disabledTextColor Disabled text color.
     * @return Color state list.
     */
    @NonNull
    public static ColorStateList createDisabledStateList(int textColor, int disabledTextColor) {
        // Now create a new ColorStateList with the default color, and the new disabled
        // color
        final int[][] states = new int[2][];
        final int[] colors = new int[2];
        int i = 0;

        // Disabled state
        states[i] = DISABLED_STATE_SET;
        colors[i] = disabledTextColor;
        i++;

        // Default state
        states[i] = EMPTY_STATE_SET;
        colors[i] = textColor;
        i++;

        return new ColorStateList(states, colors);
    }

    private ThemeUtils() {
    }
}
