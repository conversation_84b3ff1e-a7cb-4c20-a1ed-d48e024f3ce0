package com.overseas.common.utils

import android.annotation.SuppressLint
import android.app.Activity
import android.app.ActivityOptions
import android.app.Application
import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.*
import android.util.DisplayMetrics
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.ColorRes
import androidx.annotation.StringRes
import androidx.core.text.TextUtilsCompat
import androidx.core.view.ViewCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.lifecycle.repeatOnLifecycle
import com.elvishew.xlog.XLog
import com.overseas.common.ext.init
import com.overseas.common.sntp.SNTPManager
import com.qyqy.ucoo.utils.LogUtils
import io.fastkv.FastKV
import kotlinx.coroutines.CancellableContinuation
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.delay
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull
import java.io.IOException
import java.lang.reflect.*
import java.util.*
import kotlin.concurrent.thread
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.math.roundToInt
import kotlin.reflect.KClass
import kotlin.reflect.full.declaredMemberProperties
import kotlin.reflect.full.primaryConstructor
import kotlin.reflect.typeOf


internal lateinit var _appContext: Application

internal val appContext: Application
    get() = if (isPreviewOnCompose && ::_appContext.isInitialized.not()) {
        _appContext = object : Application() {
        }
        _appContext
    } else {
        _appContext
    }

internal val appResources: Resources
    get() = if (!::_appContext.isInitialized) {
        Resources.getSystem()
    } else {
        _appContext.resources
    }


private val NTP_BR_HOSTS = arrayOf(
    "ntp4.aliyun.com",
    "ntp5.aliyun.com",
    "ntp1.aliyun.com",
    "ntp3.aliyun.com",
    "ntp2.aliyun.com",
    "ntp6.aliyun.com",
    "ntp7.aliyun.com",
    "*************",
    "**************",
    "***************",
    "*************",
    "***********",
    "************",
    "*************",
    "*************",
)

var isPreviewOnCompose = true

internal val sAppKV: FastKV by lazy {
    FastKV.Builder("${appContext.filesDir.absolutePath}/fastkv", "Global").build()
}

val currentLocale: Locale
    get() = appResources.configuration?.let {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return it.getLocales().get(0);
        } else {
            return it.locale
        }
    } ?: Locale.getDefault()

val isLayoutRtl: Boolean
    get() = TextUtilsCompat.getLayoutDirectionFromLocale(currentLocale) == ViewCompat.LAYOUT_DIRECTION_RTL

fun View.isLayoutRtl(): Boolean {
    return ViewCompat.getLayoutDirection(this) == ViewCompat.LAYOUT_DIRECTION_RTL
}

fun initUtils(application: Application) {
    _appContext = application
    init()
//    syncSntpTimeFromServer()
}

private fun syncSntpTimeFromServer() {
    thread {
        var position = 0
        while (!SNTPManager.isInitialized()) {
            try {
                SNTPManager.build()
                    .withNtpHost(NTP_BR_HOSTS[position])
                    .withConnectionTimeout(3000)
                    .initialize()
            } catch (e: IOException) {
                e.printStackTrace()
            }
            position++
            if (position >= NTP_BR_HOSTS.size) {
                break
            }
        }
    }
}


/**
 * Provides access to the hidden ViewGroup#suppressLayout method.
 */
fun ViewGroup.suppressLayoutCompat(suppress: Boolean) {
    if (Build.VERSION.SDK_INT >= 29) {
        suppressLayout(suppress)
    } else {
        hiddenSuppressLayout(this, suppress)
    }
}

/**
 * False when linking of the hidden suppressLayout method has previously failed.
 */
private var tryHiddenSuppressLayout = true

@SuppressLint("NewApi") // Lint doesn't know about the hidden method.
private fun hiddenSuppressLayout(group: ViewGroup, suppress: Boolean) {
    if (tryHiddenSuppressLayout) {
        // Since this was an @hide method made public, we can link directly against it with
        // a try/catch for its absence instead of doing the same through reflection.
        try {
            group.suppressLayout(suppress)
        } catch (e: NoSuchMethodError) {
            tryHiddenSuppressLayout = false
        }
    }
}

@Throws(IllegalAccessException::class)
fun Any.getAccessibleField(name: String): Field {
    val field: Field = accessible(getAnyField(name))
    if (field.modifiers and Modifier.FINAL == Modifier.FINAL) {
        try {
            val modifiersField = Field::class.java.getDeclaredField("modifiers")
            modifiersField.isAccessible = true
            modifiersField.setInt(field, field.modifiers and Modifier.FINAL.inv())
        } catch (ignore: NoSuchFieldException) {
            // runs in android will happen
            field.isAccessible = true
        }
    }
    return field
}

fun Any.setFiledValue(name: String, value: Any?) {
    try {
        getAccessibleField(name).set(
            if (this is Class<*>) {
                null
            } else {
                this.javaClass
            }, value
        )
    } catch (e: Throwable) {
        e.printStackTrace()
    }
}


@Throws(IllegalAccessException::class)
private fun Any.getAnyField(name: String): Field {
    return with(
        if (this is Class<*>) {
            this
        } else {
            this.javaClass
        }
    ) {
        try {
            getField(name)
        } catch (e: NoSuchFieldException) {
            var type: Class<*>? = this
            do {
                try {
                    return type!!.getDeclaredField(name)
                } catch (ignore: NoSuchFieldException) {
                }
                type = type!!.superclass
            } while (type != null)
            throw e
        }
    }
}

private fun <T : AccessibleObject> accessible(accessible: T): T {
    if (accessible is Member) {
        val member = accessible as Member
        if (Modifier.isPublic(member.modifiers)
            && Modifier.isPublic(member.declaringClass.modifiers)
        ) {
            return accessible
        }
    }
    if (!accessible.isAccessible) accessible.isAccessible = true
    return accessible
}

private val mMainHandler by lazy {
    val looper = Looper.getMainLooper()
    var h: Handler? = null
    if (Build.VERSION.SDK_INT >= 28) {
        h = Handler.createAsync(looper)
    } else {
        if (Build.VERSION.SDK_INT >= 16) {
            try {
                h = Handler::class.java.getDeclaredConstructor(
                    Looper::class.java, Handler.Callback::class.java,
                    Boolean::class.javaPrimitiveType
                ).newInstance(looper, null, true)
            } catch (ignored: IllegalAccessException) {
            } catch (ignored: InstantiationException) {
            } catch (ignored: NoSuchMethodException) {
            } catch (e: InvocationTargetException) {
                h = Handler(looper)
            }
        }
    }
    h ?: Handler(looper)
}

fun isMainThread(): Boolean {
    return Looper.getMainLooper().thread === Thread.currentThread()
}

fun postToMainThread(delay: Long = 0, runnable: Runnable) {
    if (isMainThread() && delay <= 0) {
        runnable.run()
    } else {
        mMainHandler.postDelayed(runnable, delay)
    }
}

fun removeCallbacks(runnable: Runnable) {
    mMainHandler.removeCallbacks(runnable)
}

fun Activity.convertActivityToTranslucent(): Boolean {
    return try {
        val classes = Activity::class.java.declaredClasses
        var translucentConversionListenerClazz: Class<*>? = null
        for (clazz in classes) {
            if (clazz.simpleName.contains("TranslucentConversionListener")) {
                translucentConversionListenerClazz = clazz
            }
        }
        if ((Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP)) {
            val convertToTranslucent: Method = Activity::class.java.getDeclaredMethod(
                "convertToTranslucent",
                translucentConversionListenerClazz,
                ActivityOptions::class.java
            )
            convertToTranslucent.isAccessible = true
            convertToTranslucent.invoke(this, null, null)
        } else {
            val convertToTranslucent: Method = Activity::class.java.getDeclaredMethod(
                "convertToTranslucent",
                translucentConversionListenerClazz
            )
            convertToTranslucent.isAccessible = true
            convertToTranslucent.invoke(this, null)
        }
        window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        true
    } catch (t: Throwable) {
        false
    }
}

fun Map<String, String>.toBundle(): Bundle {
    return Bundle().also {
        for ((key, value) in this) {
            it.putString(key, value)
        }
    }
}

fun Bundle.toMap(): Map<String, String> {
    return mutableMapOf<String, String>().also {
        keySet().forEach { key ->
            it[key] = getString(key).orEmpty()
        }
    }
}

inline fun <reified T : Any> noOpDelegate(): T {
    val javaClass = T::class.java
    return Proxy.newProxyInstance(
        javaClass.classLoader, arrayOf(javaClass), NO_OP_HANDLER
    ) as T
}

val NO_OP_HANDLER = InvocationHandler { _, _, _ ->
    // no op
}

@JvmInline
value class TryResult<T>(val value: Any?)

inline fun <R> tryEval(block: () -> R): TryResult<R> {
    return try {
        TryResult(block())
    } catch (e: Throwable) {
        TryResult(e)
    }
}

inline fun tryRun(block: () -> Unit): TryResult<Unit> {
    return tryEval(block)
}

infix fun <R> TryResult<R>.onCancel(block: (e: CancellationException) -> Unit): TryResult<R> {
    if (value is CancellationException) {
        block(value)
    }
    return this
}

inline infix fun <reified T : Throwable, R> TryResult<R>.catch(block: (t: T) -> R): R {
    if (value is CancellationException) throw value
    return if (value is Throwable) {
        if (value is T) {
            block(value)
        } else {
            throw value
        }
    } else {
        value as R
    }
}

inline infix fun <reified R> TryResult<R>.catchAll(block: (t: Throwable) -> R): R {
    if (value is CancellationException) throw value
    return if (value is Throwable) {
        block(value)
    } else {
        value as R
    }
}

inline infix fun <reified T : Throwable, R> TryResult<R>.catching(block: (t: T) -> R): TryResult<R> {
    return if (value is T) {
        TryResult(block(value))
    } else {
        this
    }
}

inline infix fun <reified R> TryResult<R>.finally(block: () -> Unit): R {
    block()
    if (value is Throwable) throw value
    return value as R
}

interface DeepCopyable

fun <T : DeepCopyable> T.deepCopy(): T {
    if (!this::class.isData) return this

    val thisClass = (this::class as KClass<T>)
    return thisClass.primaryConstructor!!.let { primaryConstructor ->
        primaryConstructor.parameters.associateWith { parameter ->
            thisClass.declaredMemberProperties
                .first { it.name == parameter.name }
                .get(this)
                ?.let {
                    (it as? DeepCopyable)?.deepCopy() ?: it
                }
        }.let(primaryConstructor::callBy)
    }
}

val Number.px2dpF: Float
    get() {
        val metrics = appResources.displayMetrics
        return this.toFloat() / (metrics.densityDpi.toFloat() / DisplayMetrics.DENSITY_DEFAULT)
    }

val Number.px2dp: Int
    get() = px2dpF.toInt()

val Number.dpF: Float
    get() = TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_DIP, toFloat(), appResources.displayMetrics
    )

val Number.dp: Int
    get() = dpF.roundToInt()

context(View)
val Number.dpF: Float
    get() = toFloat() * resources.displayMetrics.density

context(View)
val Number.dp: Int
    get() = dpF.roundToInt()

val Number.spF: Float
    get() = TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_SP, toFloat(), appResources.displayMetrics
    )

val Number.sp: Int
    get() = spF.roundToInt()

context(View)
val Number.spF: Float
    get() = toFloat() * resources.displayMetrics.scaledDensity

context(View)
val Number.sp: Int
    get() = spF.roundToInt()

/**
 * 一个语法糖，比如定义一个方法test如下，它接收一个[Int]类型参数，返回一个[Int]类型参数
 * ```
 * fun test(b: Int): Int {
 *    return b
 * }
 * ```
 * 现在看下面这一段代码，看上去比较丑陋
 * ```
 * fun main(b: Int?) {
 *   val c = b?.let{
 *      test(b)
 *   }
 *   println(c)
 * }
 * ```
 * 现在我们使用下面这个语法糖调用，看上去美丽多了
 * ```
 * fun main(b: Int?) {
 *   val c = b.call(::test)
 *   println(c)
 * }
 * ```
 *
 * 要注意的是，某些情况下显示的泛型类型声明，结果可能不一样
 * ```
 * fun test(b: Int?): Int {
 *    return b
 * }
 *
 * fun main(b: Int?) {
 *   val c = b.call<Int, Int>(::test)
 *   println(c)
 * }
 *
 * fun main(b: Int?) {
 *   val c = b.call<Int?, Int>(::test)
 *   println(c)
 * }
 * ```
 */

inline fun <reified T, R> T?.call(lambda: (T) -> R): R? {
    val kType = typeOf<T>()
    if (!kType.isMarkedNullable && this == null) { // 判断泛型T是否是可空的
        return null
    }
    return lambda(this as T)
}

/**
 * 使用invoke约定，进一步简化[call]语法糖
 * ```
 * fun test(b: Int): Int {
 *    return b
 * }
 *
 * fun main(b: Int?) {
 *   val c = b(::test)
 *   println(c)
 * }
 * ```
 */
//inline operator fun <reified T, R> T?.invoke(lambda: (T) -> R): R? {
//    return call(lambda)
//}

fun <T> CancellableContinuation<T>.resumeIfActive(value: T) {
    if (isActive) {
        try {
            resume(value)
        } catch (e: IllegalStateException) {
            //fixed https://console.firebase.google.com/u/1/project/ucoo-386017/crashlytics/app/android:com.qyqy.ucoo/issues/62fef20289e5320bc42dd37d4e3b5c7f?time=last-seven-days&types=crash&sessionEventKey=66E6E70101D700010866C526C50947C0_1993334523905692072
            //可能是线程导致的问题, 简单点直接捕获完了
            LogUtils.w("CancellableContinuation", e)
        }
    }
}

fun <T> CancellableContinuation<T>.throwIfActive(exception: Throwable) {
    if (isActive) {
        resumeWithException(exception)
    }
}

suspend fun <T> runCoroutineCatching(block: suspend () -> T): Result<T> {
    return try {
        Result.success(block())
    } catch (e: java.util.concurrent.CancellationException) {
        throw e
    } catch (e: Throwable) {
        LogUtils.e("runFixCatching",e)
        Result.failure(e)
    }
}

fun id2Color(@ColorRes resId: Int): Int = appContext.getColor(resId)

fun id2String(@StringRes resId: Int, vararg formatArgs: Any): String =
    appContext.getString(resId, formatArgs)


open class ResultContinuation<T> : (CancellableContinuation<T>) -> Unit {

    @Volatile
    private var continuation: CancellableContinuation<T>? = null

    val isActive: Boolean
        get() = continuation != null && continuation?.isActive == true

    override fun invoke(continuation: CancellableContinuation<T>) {
        this.continuation = continuation
        continuation.invokeOnCancellation {
            this.continuation = null
        }
    }

    fun resume(value: T) {
        if (continuation?.isActive == true) {
            continuation?.resumeIfActive(value)
            continuation = null
        }
    }

    fun cancel() {
        if (continuation?.isActive == true) {
            continuation?.resumeWithException(CancellationException())
            continuation = null
        }
    }

    suspend fun suspendUntil(): T {
        return suspendCancellableCoroutine(this)
    }

    suspend fun suspendUntilWithTimeout(timeMillis: Long): T? {
        return withTimeoutOrNull(timeMillis) {
            suspendUntil()
        }
    }

}

class AwaitContinuation : ResultContinuation<Unit>() {

    var shouldSuspend = false
        set(value) {
            field = value
            if (value.not()) {
                resume()
            }
        }


    fun resume() {
        resume(Unit)
    }
}

fun awaitContinuation() = AwaitContinuation()

class ActiveDelay : LifecycleOwner {

    private val mLifecycleRegistry = LifecycleRegistry(this)

    private var leftTimeMillis = 0L

    suspend fun aDelay(timeMillis: Long) {
        if (timeMillis <= 0) {
            return
        }
        if (mLifecycleRegistry.currentState == Lifecycle.State.INITIALIZED) {
            mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_RESUME)
        }
        leftTimeMillis = timeMillis
        repeatOnLifecycle(Lifecycle.State.RESUMED) {
            if (leftTimeMillis > 0) {
                val temp = SystemClock.elapsedRealtime()
                try {
                    delay(leftTimeMillis)
                } catch (e: CancellationException) {
                    leftTimeMillis = leftTimeMillis.minus(SystemClock.elapsedRealtime().minus(temp))
                    throw e
                }
            }
            mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_DESTROY)
        }
    }

    fun pause() {
        mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_PAUSE)
    }

    fun resume() {
        mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_RESUME)
    }

    fun abort() {
        mLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    }

    override val lifecycle: Lifecycle = mLifecycleRegistry

}

val Context.screenWidth: Int
    get() = resources.displayMetrics.widthPixels

val Context.screenHeight: Int
    get() = resources.displayMetrics.heightPixels

val Context.layoutInflater: LayoutInflater
    get() = LayoutInflater.from(this)

fun <E> MutableIterable<E>.removeIfCompat(filter: (E) -> Boolean): Boolean {
    var removed = false
    try {
        val each: MutableIterator<E> = iterator()
        while (each.hasNext()) {
            if (filter.invoke(each.next())) {
                each.remove()
                removed = true
            }
        }
    } catch (e: Exception) {
    }
    return removed
}

fun <E> MutableIterable<E>.removeFirstIfCompat(filter: (E) -> Boolean): E? {
    val each: MutableIterator<E> = iterator()
    while (each.hasNext()) {
        val item = each.next()
        if (filter.invoke(item)) {
            each.remove()
            return item
        }
    }
    return null
}

inline fun <R> runFinally(finally: () -> Unit, block: () -> R): R {
    return try {
        block()
    } finally {
        finally()
    }
}

inline fun <R> (() -> R).finally(block: () -> Unit): R {
    return runFinally(block, this)
}

fun Int.formatTimeOfSeconds(): String {
    val minute = div(60)
    val second = rem(60)
    return "${if (minute > 9) minute else "0$minute"}:${if (second > 9) second else "0$second"}"
}