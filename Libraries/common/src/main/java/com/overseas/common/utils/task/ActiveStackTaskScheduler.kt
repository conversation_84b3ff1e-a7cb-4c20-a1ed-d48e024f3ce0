package com.overseas.common.utils.task

import kotlinx.coroutines.CoroutineScope

open class ActiveStackTaskInfo constructor(
    id: String, // 任务id
    priority: Int = 0, // 任务优先级
    extra: Any? = null,
    internal val filter: ((ActiveStack) -> Boolean)? = null,
) : TaskInfo(id, priority, extra) {

    constructor(attr: TaskInfo) : this(attr.id, attr.priority, attr.extra, null) {
        isReady = attr.isReady
        identityTag = attr.identityTag
        taskJob = attr.taskJob
    }
}

open class ActiveStackTaskScheduler(
    spaceName: String,
    taskScope: CoroutineScope,
    taskQueue: IPriorityQueue<ITask> = SimplePriorityQueue(),
) : SerialTaskScheduler(spaceName, taskScope, taskQueue) {

    @Volatile
    var activeStack: ActiveStack = DEFAULT_ACTIVE_STACK
        private set

    override fun getCurrentRunningTask(checkState: Boolean): ITask? {
        return findTask(true) {
            val attribute = it.info
            attribute.identityTag == activeStack.identityCode && attribute.taskJob != null
        }
    }

    override fun findLatestTask(): ITask? {
        log("findLatestTask ${taskQueue.isEmpty()}")
        val currentIdentityTag = activeStack.identityCode
        return findTask(false) {
            val attribute = it.info
            log("findLatestTask info $attribute")
            if (attribute.taskJob != null || !attribute.isReady) { // 正在执行或没准备好
                return@findTask false
            }
            if (attribute is ActiveStackTaskInfo) {
                attribute.filter?.invoke(activeStack) != false
            } else {
                true
            }
        }?.also {
            it.info.identityTag = currentIdentityTag
        }
    }

    open fun updateActiveStack(newStack: ActiveStack) {
        if (activeStack == newStack) {
            return
        }
        synchronized(this) {
            if (activeStack != newStack) {
                activeStack = newStack
                tryScheduleLatestTask()
            }
        }
    }

    protected fun clearTaskWithDestroyStack(identityCode: String) {
        removeTaskIf {
            val attribute = it.info
            val removed = attribute.identityTag == identityCode
            if (removed && attribute.taskJob != null) {  // 取消关联此任务栈的正在运行的任务
                attribute.cancelTaskJob()
            }
            removed
        }
    }
}