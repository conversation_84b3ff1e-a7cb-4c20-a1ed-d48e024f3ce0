package com.overseas.common.utils.task

import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Job
import java.lang.ref.WeakReference

sealed class State {
    object IDLE : State()
    object RUNNING : State()
    object PAUSE : State()
    object DONE : State()
}

/**
 * 活跃栈（组）
 */
data class ActiveStack(
    val name: String,
    val identityCode: String,
    val weakStackObj: WeakReference<Any>? = null,
) {

    constructor(
        name: String,
        identityCode: Int,
        weakStackObj: WeakReference<Any>? = null,
    ) : this(name, identityCode.toString(), weakStackObj)

    val stackObj: Any?
        get() = weakStackObj?.get()
}

val DEFAULT_ACTIVE_STACK = ActiveStack("", "")

/**
 * 任务信息
 */
open class TaskInfo constructor(
    val id: String, // 任务id
    override val priority: Int = 0, // 任务优先级
    val extra: Any? = null,
    var isReady: Boolean = true, // 是否准备好开始执行任务
) : IPriority {

    internal var identityTag: String? = null // 任务标签

    internal var taskJob: Job? = null // 当前任务是否已完成

    var taskIsScheduled = false
        internal set

    override fun toString(): String {
        return "TaskInfo(id='$id', priority=$priority, isReady=$isReady, identityTag=$identityTag, taskIsScheduled=$taskIsScheduled, taskJob=$taskJob)"
    }

}

class AbortTaskException : CancellationException()