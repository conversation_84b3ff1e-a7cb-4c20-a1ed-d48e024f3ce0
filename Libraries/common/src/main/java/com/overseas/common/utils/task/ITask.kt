package com.overseas.common.utils.task

interface ITask : IPriority {

    val info: TaskInfo

    /**
     * 开始任务直到结束
     * @return 任务执行结果
     */
    suspend fun runTask(): Any?

    /**
     * 暂停任务
     */
    fun pause() {

    }

    /**
     * 继续任务
     */
    fun resume() {

    }

    /**
     * 是否正在运行
     */
    fun checkIsRunning(): <PERSON><PERSON><PERSON> {
        return true
    }

    /*
     *
     */
    fun onFinish(success: Boolean) {

    }


    /**
     * 任务取消或结束，释放资源，挂起函数可异步释放
     */
    suspend fun release() {

    }

    override val priority: Int
        get() = info.priority
}