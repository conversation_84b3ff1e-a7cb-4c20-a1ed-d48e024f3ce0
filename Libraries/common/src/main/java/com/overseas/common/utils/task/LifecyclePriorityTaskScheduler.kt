package com.overseas.common.utils.task

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.coroutineScope
import androidx.lifecycle.whenStateAtLeast
import com.overseas.common.ext.doOnLifecycleEvent
import com.overseas.common.ext.isAtLeastState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

class LifecyclePriorityTaskScheduler(
    spaceName: String,
    private val lifecycle: Lifecycle,
    private val minState: Lifecycle.State = Lifecycle.State.STARTED,
    taskQueue: IPriorityQueue<ITask> = SimplePriorityQueue(),
) : SerialTaskScheduler(spaceName, lifecycle.coroutineScope, taskQueue) {

    init {
        lifecycle.doOnLifecycleEvent {
            if (lifecycle.isAtLeastState(minState)) {
                getCurrentRunningTask<ITask>()?.resume()
                resumeSchedule()
            } else if (it == Lifecycle.Event.ON_DESTROY) {
                cancelCurrentRunningTask()
                finishSchedule()
            } else {
                getCurrentRunningTask<ITask>()?.pause()
                pauseSchedule()
            }
        }
    }

    override fun taskRunTime(task: ITask, block: suspend CoroutineScope.() -> Unit): Job {
        return taskScope.launch {
            lifecycle.whenStateAtLeast(minState, block)
        }
    }

}