package com.overseas.common.utils.task

import com.overseas.common.utils.removeFirstIfCompat
import com.overseas.common.utils.removeIfCompat

interface IPriority {
    val priority: Int
}

interface IPriorityQueue<T : IPriority> {

    fun add(item: T)

    fun find(predicate: (T) -> Boolean): T?

    fun remove(item: T)

    fun removeFirstIf(predicate: (T) -> Boolean): T?

    fun removeIf(predicate: (T) -> Boolean): Boolean

    fun clear()

    fun isEmpty(): Boolean

}

open class DefaultPriorityComparator<T : IPriority> : Comparator<T> {
    override fun compare(o1: T, o2: T): Int {
        return o2.priority.compareTo(o1.priority)
    }
}

open class SimplePriorityQueue<T : IPriority>(
    private val comparator: Comparator<IPriority> = DefaultPriorityComparator()
) : IPriorityQueue<T> {

    private val queue by lazy {
        mutableListOf<T>()
    }

    override fun add(item: T) {
        synchronized(this) {
            queue.add(item)
            queue.sortWith(comparator)
        }
    }

    override fun find(predicate: (T) -> Boolean): T? {
        return synchronized(this) {
            queue.find(predicate)
        }
    }

    override fun remove(item: T) {
        synchronized(this) {
            queue.remove(item)
        }
    }

    override fun removeFirstIf(predicate: (T) -> Boolean): T? {
        return synchronized(this) {
            queue.removeFirstIfCompat(predicate)
        }
    }

    override fun removeIf(predicate: (T) -> Boolean): Boolean {
        return synchronized(this) {
            queue.removeIfCompat(predicate)
        }
    }

    override fun clear() {
        synchronized(this) {
            queue.clear()
        }
    }

    override fun isEmpty(): Boolean {
        return synchronized(this) {
            queue.isEmpty()
        }
    }

}