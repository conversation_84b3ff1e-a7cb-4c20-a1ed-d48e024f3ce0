package com.overseas.common.utils.task


import com.qyqy.ucoo.utils.LogUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicReference

open class SerialTaskScheduler(
    private val spaceName: String,
    protected val taskScope: CoroutineScope,
    protected val taskQueue: IPriorityQueue<ITask> = SimplePriorityQueue(),
) {

    private val scheduleState = AtomicReference<State>(State.IDLE)

    private val mObservable by lazy {
        TaskEventObservable()
    }

    fun startSchedule(resume: Boolean = true) {
        if (scheduleState.compareAndSet(State.IDLE, if (resume) State.RUNNING else State.PAUSE)) {
            log("开始任务调度")
            tryScheduleLatestTask()
        }
    }

    fun pauseSchedule(): Boolean {
        if (scheduleState.compareAndSet(State.RUNNING, State.PAUSE)) {
            log("暂停任务调度")
            return true
        }
        return false
    }

    fun resumeSchedule(): Boolean {
        if (scheduleState.compareAndSet(State.PAUSE, State.RUNNING)) {
            log("恢复任务调度")
            tryScheduleLatestTask()
            return true
        }
        return false
    }

    fun finishSchedule() {
        if (scheduleState.get() != State.DONE) {
            log("结束任务调度")
            scheduleState.set(State.DONE)
            taskQueue.clear()
        }
    }

    fun addTask(task: ITask) {
        if (scheduleState.get() != State.DONE) {
            log("添加新任务，${task.info}")
            taskQueue.add(task)
            tryScheduleLatestTask()
        }
    }

    fun removeTask(task: ITask) {
        if (scheduleState.get() != State.DONE) {
            val ret = removeFirstIf {
                it == task
            }
            log("移除任务，${ret}")
        }
    }

    fun removeFirstIf(predicate: (ITask) -> Boolean): ITask? {
        if (scheduleState.get() != State.DONE) {
            return taskQueue.removeFirstIf(predicate)
        }
        return null
    }

    fun removeTaskIf(predicate: (ITask) -> Boolean) {
        if (scheduleState.get() != State.DONE) {
            taskQueue.removeIf(predicate)
        }
    }

    fun clearAllTask() {
        if (scheduleState.get() != State.DONE) {
            taskQueue.clear()
        }
    }

    fun <T : ITask> getTask(predicate: (ITask) -> Boolean): T? {
        return findTask(true, predicate) as? T
    }

    fun isEmpty(): Boolean {
        if (scheduleState.get() != State.DONE) {
            return taskQueue.isEmpty()
        }
        return true
    }

    fun registerObserver(observer: TaskEventObserver) {
        kotlin.runCatching { mObservable.registerObserver(observer) }
    }

    fun unregisterObserver(observer: TaskEventObserver) {
        kotlin.runCatching { mObservable.unregisterObserver(observer) }
    }

    /**
     * 尝试调度执行最新任务
     */
    fun tryScheduleLatestTask() {
        log("尝试获取到新任务")
        if (scheduleState.get() != State.RUNNING) {
            return
        }
        val task = tryPollTask()
        if (task != null) {
            // 任务开始
            log("获取到新任务，准备开始执行, ${task.info}")
            taskRunTime(task) {
                executeTask(task)
            }.also {
                task.info.taskJob = it
                it.invokeOnCompletion {
                    task.info.taskJob = null
                    removeTask(task)
                    log("上一个任务结束，等待新任务")
                    tryScheduleLatestTask()  // 上一个任务结束，接着执行下一个任务
                }
            }
        } else {
            log("暂没有新任务")
        }
    }

    fun <T : ITask> getCurrentRunningTask(): T? {
        return getCurrentRunningTask(true) as? T
    }

    fun cancelCurrentRunningTask() {
        getCurrentRunningTask(true)?.info?.cancelTaskJob()
    }

    protected open fun taskRunTime(task: ITask, block: suspend CoroutineScope.() -> Unit): Job {
        return taskScope.launch {
            block()
        }
    }

    protected open fun getCurrentRunningTask(checkState: Boolean): ITask? {
        return findTask(checkState) {
            it.info.taskJob != null
        }
    }

    protected open fun findLatestTask(): ITask? {
        log("findLatestTask ${taskQueue.isEmpty()}")
        return findTask(false) {
            log("findLatestTask info ${it.info}")
            it.info.isReady && it.info.taskJob == null
        }
    }

    protected fun findTask(checkState: Boolean, predicate: (ITask) -> Boolean): ITask? {
        return if (!checkState || scheduleState.get() != State.DONE) {
            return taskQueue.find(predicate)
        } else null
    }

    protected fun TaskInfo.cancelTaskJob() {
        taskJob?.cancel(AbortTaskException())
        taskJob = null
    }

    protected fun log(msg: Any) {
        LogUtils.d("SerialTaskScheduler", "spaceName: $spaceName, $msg")
    }

    private fun tryPollTask(): ITask? {
        val task = getCurrentRunningTask(false)
        if (task != null) {
            // 任务还没真正开始调度
            if (!task.info.taskIsScheduled) {
                return null
            }
            // 检查任务是否完成，防止误判
            if (task.checkIsRunning()) {
                return null
            }
            val attribute = task.info
            if (attribute.taskJob?.isActive == true) {
                attribute.cancelTaskJob()
                return null
            } else {
                task.info.taskJob = null
                removeTask(task)
            }
        }
        return findLatestTask()
    }

    private suspend fun executeTask(task: ITask) {
        val info = task.info
        log("任务开始执行, $info")
        mObservable.notifyTaskStartEvent(task)
        var success = false
        try {
            task.info.taskIsScheduled = true
            val result = task.runTask()
            // 任务结束
            log("任务执行完毕, $info")
            mObservable.notifyTaskEndEvent(task, result)
            success = true
        } catch (e: Exception) { // 这里把[CancellationException]也捕获掉，不允许任务体取消任务调度
            // 任务异常
            log("任务执行异常, $info, $e")
            mObservable.notifyTaskErrorEvent(task, e)
        } finally {
            task.onFinish(success)
            log("释放任务资源, $info")
            taskScope.launch { // 释放任务资源
                task.release()
            }
        }
    }
}