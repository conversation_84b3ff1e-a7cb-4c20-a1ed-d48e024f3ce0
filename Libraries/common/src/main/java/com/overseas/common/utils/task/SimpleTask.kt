package com.overseas.common.utils.task

import android.app.Activity
import android.app.Application
import android.app.Dialog
import android.content.ContextWrapper
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.widget.PopupWindow
import androidx.annotation.CallSuper
import androidx.fragment.app.DialogFragment
import com.overseas.common.ext.asLifecycleOwner
import com.overseas.common.ext.doOnDestroy
import com.overseas.common.ext.isDestroyed
import com.overseas.common.ext.safeDismiss
import com.overseas.common.ext.safeViewLifecycleOwner
import com.overseas.common.utils.AwaitContinuation
import com.overseas.common.utils.IDialogEventObserver
import com.overseas.common.utils.appContext
import com.overseas.common.utils.awaitContinuation
import com.overseas.common.utils.noOpDelegate
import com.overseas.common.utils.resumeIfActive
import kotlinx.coroutines.suspendCancellableCoroutine
import java.lang.ref.WeakReference
import java.util.concurrent.atomic.AtomicInteger

abstract class AbsTask(override val info: TaskInfo) : ITask {

    private var listener: ((Any?) -> Unit)? = null

    private var pausedContinuation: AwaitContinuation? = null

    protected var paused: Boolean = false
        private set

    abstract fun doTask()

    /**
     * 结束任务
     */
    abstract fun endTask()

    open fun sendTaskFinished(result: Any? = null) {
        listener?.invoke(result)
    }

    private fun startTask(onEnd: (Any?) -> Unit) {
        listener = onEnd
        doTask()
    }

    override suspend fun runTask(): Any? {
        return suspendCancellableCoroutine {
            startTask { result ->
                listener = null
                // 这里可以考虑扩展下抛出异常 [it.resumeWithException()]
                it.resumeIfActive(result)
            }
            it.invokeOnCancellation { e ->
                if (e != null) {
                    endTask()
                }
            }
        }
    }

    override fun toString(): String {
        return "AbsTask(info=$info)"
    }

    @CallSuper
    override fun resume() {
        paused = false
        pausedContinuation?.resume()
        pausedContinuation = null
    }

    @CallSuper
    override fun pause() {
        paused = true
    }

    protected suspend fun checkPaused() {
        if (paused) {
            pausedContinuation = awaitContinuation()
            pausedContinuation?.suspendUntil()
        }
    }

}

class EmptyTask(info: TaskInfo, val taskBlock: () -> Unit) : AbsTask(info) {
    override fun doTask() {
        taskBlock()
    }

    fun setCompleted() {
        sendTaskFinished()
    }

    override fun endTask() {

    }
}

abstract class AndroidWidgetTask<W, T : W>(
    info: TaskInfo,
    private val runDismiss: (T?) -> Unit,
    private val runShow: (AndroidWidgetTask<*, *>) -> T?,
) : AbsTask(info) {

    var finishTaskIfDismiss = true

    protected var weakWidget: WeakReference<T>? = null

    abstract fun setOnDismissListener(widget: T, onDismiss: (Any?) -> Unit)

    override fun doTask() {
        val widget = runShow(this)
        if (widget == null) {
            sendTaskFinished(null)
            return
        }
        weakWidget = WeakReference(widget)
        setOnDismissListener(widget) {
            if (finishTaskIfDismiss) {
                sendTaskFinished(it)
            }
        }
    }

    override fun endTask() {
        runDismiss(weakWidget?.get())
    }

}

fun <T : Dialog> T.handleOnDismiss(onDismiss: (T) -> Unit) {
    var dispatch = false
    val listener = DialogInterface.OnDismissListener {
        if (!dispatch) {
            dispatch = true
            onDismiss(this)
        }
    }
    if (this is IDialogEventObserver) {
        this.addOnDismissListener(listener)
    } else {
        this.setOnDismissListener(listener)
    }
    ((this.context as? ContextWrapper)?.baseContext as? Activity)?.asLifecycleOwner?.doOnDestroy {
        this.dismiss()
        if (!dispatch) {
            dispatch = true
            onDismiss(this)
        }
    }
}

class DialogTask<T : Dialog>(
    info: TaskInfo,
    runDismiss: (T?) -> Unit = { it?.dismiss() },
    runShow: (AndroidWidgetTask<*, *>) -> T?,
) : AndroidWidgetTask<Dialog, T>(info, runDismiss, runShow) {

    override fun setOnDismissListener(widget: T, onDismiss: (Any?) -> Unit) {
        widget.handleOnDismiss {
            onDismiss(null)
        }
    }

    override fun checkIsRunning(): Boolean {
        return weakWidget?.get()?.isShowing == true
    }
}

class DialogFragmentTask<T : DialogFragment>(
    info: TaskInfo,
    runDismiss: (T?) -> Unit = { it?.safeDismiss() },
    runShow: (AndroidWidgetTask<*, *>) -> T?,
) : AndroidWidgetTask<DialogFragment, T>(info, runDismiss, runShow) {

    override fun setOnDismissListener(widget: T, onDismiss: (Any?) -> Unit) {
        var dispatch = false
        if (widget is IDialogEventObserver) {
            widget.addOnDismissListener {
                if (!dispatch) {
                    dispatch = true
                    onDismiss(null)
                }
            }
        }
        widget.safeViewLifecycleOwner.lifecycle.doOnDestroy {
            if (!dispatch) {
                dispatch = true
                onDismiss(null)
            }
        }
    }

    override fun checkIsRunning(): Boolean {
        return weakWidget?.get()?.isDestroyed == false
    }
}

class PopupWindowTask<T : PopupWindow>(
    info: TaskInfo,
    runDismiss: (T?) -> Unit = { it?.dismiss() },
    runShow: (AndroidWidgetTask<*, *>) -> T?,
) : AndroidWidgetTask<PopupWindow, T>(info, runDismiss, runShow) {

    override fun setOnDismissListener(widget: T, onDismiss: (Any?) -> Unit) {
        widget.setOnDismissListener {
            onDismiss(null)
        }
    }
}

class ActivityTask(
    info: TaskInfo,
    private val runDismiss: (Activity?) -> Unit = {
        it?.finish()
    },
    private val runShow: () -> Intent?,
) : AbsTask(info), Application.ActivityLifecycleCallbacks by noOpDelegate() {

    companion object {

        private const val KEY = "key_activity_task_identify"

        private val activityCounter by lazy {
            AtomicInteger(0)
        }
    }

    var finishTaskIfDismiss = true

    private val mApplication: Application = appContext

    private val mVersion = activityCounter.getAndIncrement()

    private var mTaskTarget: WeakReference<Activity>? = null

    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
        if (savedInstanceState == null) {
            if (activity.intent.getIntExtra(KEY, -1) == mVersion) {
                mTaskTarget = WeakReference(activity)
            }
        }
    }

    override fun onActivityPaused(activity: Activity) {
        checkActivityIsFinish(activity)
    }

    override fun onActivityStopped(activity: Activity) {
        checkActivityIsFinish(activity)
    }

    override fun onActivityDestroyed(activity: Activity) {
        checkActivityIsFinish(activity)
    }

    override fun doTask() {
        val intent = runShow()
        if (intent == null) {
            sendTaskFinished(null)
            return
        }
        intent.putExtra(KEY, mVersion)
        mApplication.registerActivityLifecycleCallbacks(this)
    }

    override fun endTask() {
        mTaskTarget?.get()?.also {
            runDismiss(it)
        }
    }

    private fun checkActivityIsFinish(activity: Activity) {
        if (activity.isFinishing || activity.isDestroyed) {
            if (activity.intent.getIntExtra(KEY, -1) == mVersion) {
                mApplication.unregisterActivityLifecycleCallbacks(this)
                if (finishTaskIfDismiss) {
                    sendTaskFinished()
                }
            }
        }
    }

}