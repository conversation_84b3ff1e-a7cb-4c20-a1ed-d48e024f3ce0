package com.overseas.common.utils.task

import android.database.Observable

abstract class TaskEventObserver {

    open fun onTaskStartEvent(task: ITask) {

    }

    open fun onTaskEndEvent(task: ITask, result: Any?) {

    }

    open fun onTaskErrorEvent(task: ITask?, throwable: Throwable) {

    }
}

internal class TaskEventObservable : Observable<TaskEventObserver>() {

    fun hasObservers(): Boolean {
        return mObservers.isNotEmpty()
    }

    fun notifyTaskStartEvent(task: ITask) {
        try {
            for (i in mObservers.indices.reversed()) {
                mObservers[i].onTaskStartEvent(task)
            }
        } catch (_: Exception) {
        }
    }

    fun notifyTaskEndEvent(task: ITask, reason: Any?) {
        try {
            for (i in mObservers.indices.reversed()) {
                mObservers[i].onTaskEndEvent(task, reason)
            }
        } catch (_: Exception) {
        }
    }

    fun notifyTaskErrorEvent(task: ITask, throwable: Throwable) {
        try {
            for (i in mObservers.indices.reversed()) {
                mObservers[i].onTaskErrorEvent(task, throwable)
            }
        } catch (_: Exception) {
        }
    }
}