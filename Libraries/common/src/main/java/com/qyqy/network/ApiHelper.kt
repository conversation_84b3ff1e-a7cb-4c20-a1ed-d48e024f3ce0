//package com.qyqy.network
//
//import kotlinx.serialization.ContextualSerializer
//import kotlinx.serialization.ExperimentalSerializationApi
//import kotlinx.serialization.InternalSerializationApi
//import kotlinx.serialization.KSerializer
//import kotlinx.serialization.SerialName
//import kotlinx.serialization.Serializable
//import kotlinx.serialization.descriptors.SerialDescriptor
//import kotlinx.serialization.encoding.Decoder
//import kotlinx.serialization.encoding.Encoder
//import kotlinx.serialization.json.Json
//import kotlinx.serialization.json.JsonObject
//import kotlinx.serialization.modules.serializersModuleOf
//import kotlinx.serialization.serializer
//import retrofit2.Retrofit
//import java.lang.reflect.InvocationHandler
//import java.lang.reflect.Method
//import java.lang.reflect.Proxy
//import java.util.concurrent.CancellationException
//
//
//open class ApiException constructor(val code: Int = 0, val msg: String, val toast: String? = null, val extra: JsonObject? = null) :
//    RuntimeException(msg) {
//    override fun toString(): String {
//        return "code: $code, ${super.toString()}"
//    }
//}
//
//object ApiCode {
//    const val STATUS_SUCCESS_BUT_DATA_IS_NULL = 700
//    const val STATUS_SUCCESS = 0
//    const val ACCESS_TOKEN_MISS = -2
//    const val ACCESS_TOKEN_EXPIRED = -3
//    const val REFRESH_TOKEN_EXPIRED = -4
//}
//
//@OptIn(ExperimentalSerializationApi::class)
//val sAppJson = Json {
//    ignoreUnknownKeys = true
//    isLenient = true
//    explicitNulls = false
//    coerceInputValues = true
//    serializersModule = serializersModuleOf(DynamicLookupSerializer)
//
//}
//
//@OptIn(ExperimentalSerializationApi::class)
//internal object DynamicLookupSerializer : KSerializer<Any> {
//
//    override val descriptor: SerialDescriptor =
//        ContextualSerializer(Any::class, null, emptyArray()).descriptor
//
//    @Suppress("UNCHECKED_CAST")
//    @OptIn(InternalSerializationApi::class)
//    override fun serialize(encoder: Encoder, value: Any) {
//        val actualSerializer =
//            encoder.serializersModule.getContextual(value::class) ?: value::class.serializer()
//        encoder.encodeSerializableValue(actualSerializer as KSerializer<Any>, value)
//    }
//
//    override fun deserialize(decoder: Decoder): Any {
//        error("Unsupported")
//    }
//}
//
//fun <T> createApi(host: String, service: Class<T>, isPreviewOnCompose: Boolean = false): T {
//    return if (isPreviewOnCompose) {
//        createFakeInterface(service)
//    } else {
//        NetManager.getService(host, service)
//    }
//}
//
//fun <T> createApi(service: Class<T>, isPreviewOnCompose: Boolean = false): T {
//    return if (isPreviewOnCompose) {
//        createFakeInterface(service)
//    } else {
//        NetManager.getService(service)
//    }
//}
//
//inline fun <reified A> createApi(host: String = "", isPreviewOnCompose: Boolean = false) =
//    if (isPreviewOnCompose) createFakeInterface(A::class.java)
//    else createApi(host, A::class.java)
//
//@Serializable
//data class ApiResponse<T>(
//    @SerialName("code")
//    val status: Int,
//    @SerialName("message")
//    val msg: String = "",
//    val data: T?,
//    @SerialName("toast")
//    val toast: String? = null,
//    @SerialName("redirect")
//    val redirect: String = "",
//    val extra: JsonObject?,
//) {
//    fun isOk() = status == 0
//}
//
//val ApiResponse<*>.isSuccessFul: Boolean
//    get() = status == ApiCode.STATUS_SUCCESS
//
//val Result<*>.apiException: ApiException?
//    get() = exceptionOrNull() as? ApiException
//
//val Throwable.apiException: ApiException?
//    get() = this as? ApiException
//
//inline fun <T, reified R> Result<T>.thenApi(transform: (T) -> ApiResponse<R>): Result<R> {
//    return fold({
//        runApiCatching {
//            transform(it)
//        }
//    }) {
//        Result.failure(it)
//    }
//}
//
//inline fun <reified T> runApiCatching(
//    dataIsNonNull: Boolean = true,
//    globalErrorHandleIntercepted: (ApiResponse<T>) -> Boolean = { false },
//    block: () -> ApiResponse<T>,
//): Result<T> {
//    return try {
//        val response = block()
//        parseApiResponse(dataIsNonNull, response, globalErrorHandleIntercepted)
//    } catch (e: CancellationException) {
//        throw e
//    } catch (e: Throwable) {
//        Result.failure(e)
//    }
//}
//
////region 错误处理
//
//// 无需要toast的错误码
//val listErrorCodeNotShowToast = listOf(-2, -3, -4, -11, -12, -13, -15, -16)
//
//inline fun <T> parseApiResponse(
//    dataIsNonNull: Boolean,
//    response: ApiResponse<T>,
//    globalErrorHandleIntercepted: (ApiResponse<T>) -> Boolean = { false },
//): Result<T> {
//    return if (response.isSuccessFul) {
//        if (dataIsNonNull && response.data != null) {
//            Result.success(response.data)
//        } else {
//            Result.failure(
//                ApiException(
//                    ApiCode.STATUS_SUCCESS_BUT_DATA_IS_NULL, "data is null, ${response.msg}", response.toast
//                )
//            )
//        }
//    } else {
//        if (!globalErrorHandleIntercepted(response)) {
//            _Internal.mHttpExceptionHandler?.handle(response)
//        }
//        // 避免msg被toast出来
//        Result.failure(
//            ApiException(
//                response.status,
//                if (listErrorCodeNotShowToast.contains(response.status)) "" else response.msg,
//                response.toast,
//                response.extra
//            )
//        )
//    }
//}
//
////endregion
//
//interface HttpExceptionHandler {
//    fun handle(response: ApiResponse<*>)
//}
//
//
//// 实现 InvocationHandler 接口
//class EmptyInvocationHandler : InvocationHandler {
//    override fun invoke(proxy: Any?, method: Method?, args: Array<out Any>?): Any {
//        return error("EmptyInvocationHandler")
//    }
//}
//
//fun <T> createFakeInterface(service: Class<T>): T {
//    return Proxy.newProxyInstance(
//        service.classLoader,
//        arrayOf<Class<*>>(service),
//        EmptyInvocationHandler()
//    ) as T
//}
