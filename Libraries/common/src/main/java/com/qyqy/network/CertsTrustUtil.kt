//package com.qyqy.network
//
//import android.annotation.SuppressLint
//import android.util.Log
//import okhttp3.OkHttpClient
//import java.security.SecureRandom
//import javax.net.ssl.SSLContext
//import javax.net.ssl.TrustManager
//import javax.net.ssl.X509TrustManager
//
//internal object CertsTrustUtil {
//
//    fun configCertsTrustStatus(builder: OkHttpClient.Builder, trustAll: Boolean) {
//        if (trustAll) {
//            return
//        }
//        val trustAllCerts: Array<TrustManager> = arrayOf(@SuppressLint("CustomX509TrustManager")
//        object : X509TrustManager {
//            @SuppressLint("TrustAllX509TrustManager")
//            override fun checkClientTrusted(chain: Array<out java.security.cert.X509Certificate>?, authType: String?) {
//                //nothing
//            }
//
//            @SuppressLint("TrustAllX509TrustManager")
//            override fun checkServerTrusted(chain: Array<out java.security.cert.X509Certificate>?, authType: String?) {
//                //nothing
//            }
//
//            override fun getAcceptedIssuers(): Array<out java.security.cert.X509Certificate>? = arrayOf()
//        })
//
//        try {
//            // Install the all-trusting trust manager
//            val sslContext = SSLContext.getInstance("SSL")
//            sslContext.init(null, trustAllCerts, SecureRandom())
//
//            // Create an ssl socket factory with our all-trusting manager
//            val sslSocketFactory = sslContext.socketFactory
//
//            builder.run {
//                sslSocketFactory(sslSocketFactory, trustAllCerts.first() as X509TrustManager)
//                hostnameVerifier { _, _ -> true }
//            }
//        } catch (e: Exception) {
//            Log.e("CertsTrustUtil", "configCertsTrustStatus: " + e.message)
//            e.printStackTrace()
//        }
//    }
//
//}