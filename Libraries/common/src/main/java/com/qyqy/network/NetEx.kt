//package com.qyqy.network
//
//import kotlinx.serialization.json.JsonElement
//import kotlinx.serialization.json.JsonNull
//import kotlinx.serialization.json.JsonObject
//import kotlinx.serialization.json.booleanOrNull
//import kotlinx.serialization.json.contentOrNull
//import kotlinx.serialization.json.decodeFromJsonElement
//import kotlinx.serialization.json.floatOrNull
//import kotlinx.serialization.json.intOrNull
//import kotlinx.serialization.json.jsonPrimitive
//import kotlinx.serialization.json.longOrNull
//import okhttp3.MediaType.Companion.toMediaType
//
///**
// *  @time 2024/7/9
// *  <AUTHOR>
// *  @package com.qyqy.network
// */
//val contentType = "application/json".toMediaType()
//
//const val TOKEN_NAME = "Access-Token"
//
//fun JsonObject.getOrNull(key: String): JsonElement? = get(key)?.takeIf {
//    it !is JsonNull
//}
//
//fun JsonObject.getStringOrNull(key: String): String? = getOrNull(key)?.jsonPrimitive?.contentOrNull
//
//fun JsonObject.getBoolOrNull(key: String): Boolean? = getOrNull(key)?.jsonPrimitive?.booleanOrNull
//
//fun JsonObject.getIntOrNull(key: String): Int? = getOrNull(key)?.jsonPrimitive?.intOrNull
//
//fun JsonObject.getLongOrNull(key: String): Long? = getOrNull(key)?.jsonPrimitive?.longOrNull
//
//fun JsonObject.getFloatOrNull(key: String): Float? = getOrNull(key)?.jsonPrimitive?.floatOrNull
//
//inline fun <reified T> JsonObject.parseValue(key: String): T? {
//    return getOrNull(key)?.let { sAppJson.decodeFromJsonElement<T>(it) }
//}
//
//inline fun <reified T> JsonObject.parseValue(key: String, default: T): T {
//    return getOrNull(key)?.let { sAppJson.decodeFromJsonElement<T>(it) } ?: default
//}