//package com.qyqy.network
//
//import com.jakewharton.retrofit2.converter.kotlinx.serialization.asConverterFactory
//import com.qyqy.ucoo.utils.LogUtils
//import okhttp3.ConnectionPool
//import okhttp3.Dispatcher
//import okhttp3.EventListener
//import okhttp3.OkHttpClient
//import retrofit2.Retrofit
//import java.util.concurrent.TimeUnit
//
///**
// *  @time 2024/7/9
// *  <AUTHOR>
// *  @package com.qyqy.network
// */
//object NetManager {
//
//    private var mBaseUrl = ""
//
//    private var eventListener: EventListener? = null
//
//    fun initNetwork(
//        url: String,
//        is_trust_all_certs: Boolean = false,
//        builder: OkHttpClient.Builder.() -> Unit
//    ) {
//        mBaseUrl = url
//        client = OkHttpClient.Builder().run {
//            eventListener?.apply {
//                eventListener(this)
//            }
//            connectionPool(sharePool)
//            dispatcher(shareDispatcher)
//
//            readTimeout(30, TimeUnit.SECONDS)
//            writeTimeout(30, TimeUnit.SECONDS)
//            connectTimeout(30, TimeUnit.SECONDS)
//
//            CertsTrustUtil.configCertsTrustStatus(this, is_trust_all_certs)
//
//            builder(this)
//
//            addNetworkInterceptor(
//                HttpLogInterceptor { msg ->
//                    LogUtils.d("NetManager", msg)
//                }.apply {
//                    setLevel(HttpLogInterceptor.Level.BODY)
//                }
//            )
//            build()
//        }
//    }
//
//    fun addExceptionHandler(handler: HttpExceptionHandler) {
//        _Internal.mHttpExceptionHandler = handler
//    }
//
//    //region okhttp builder
//    private val sharePool: ConnectionPool = ConnectionPool()
//
//    private val shareDispatcher: Dispatcher = Dispatcher()
//
//    private lateinit var client: OkHttpClient
//    //endregion
//
//    private val retrofitMap = hashMapOf<String, Retrofit>()
//
//    @Synchronized
//    fun <S> getService(serviceClass: Class<S>): S {
//        return getService(mBaseUrl, serviceClass)
//    }
//
//    @Synchronized
//    fun <S> getService(url: String, serviceClass: Class<S>): S {
//        val baseUrl = if (url.isEmpty()) mBaseUrl else url
//        return (
//            retrofitMap[baseUrl]
//                ?: Retrofit.Builder()
//                    .client(client)
//                    .baseUrl(baseUrl)
//                    .addConverterFactory(sAppJson.asConverterFactory(contentType))
//                    .build().apply {
//                        retrofitMap[baseUrl] = this
//                    }
//            ).create(serviceClass)
//    }
//}
