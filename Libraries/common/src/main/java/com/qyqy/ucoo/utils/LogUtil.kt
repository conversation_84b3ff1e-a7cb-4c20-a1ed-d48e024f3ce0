package com.qyqy.ucoo.utils

import android.content.Context
import android.util.Log
import com.elvishew.xlog.LogConfiguration
import com.elvishew.xlog.LogLevel
import com.elvishew.xlog.XLog
import com.elvishew.xlog.printer.AndroidPrinter
import com.elvishew.xlog.printer.Printer
import com.elvishew.xlog.printer.file.FilePrinter
import com.elvishew.xlog.printer.file.backup.NeverBackupStrategy
import com.elvishew.xlog.printer.file.clean.FileLastModifiedCleanStrategy
import com.elvishew.xlog.printer.file.naming.DateFileNameGenerator
import com.overseas.common.BuildConfig
import java.io.File

//@Deprecated(
//    message = "please use com.qyqy.ucoo.utils.LogUtils",
//    replaceWith = ReplaceWith("LogUtils", "com.qyqy.ucoo.utils.LogUtils")
//)
object LogUtil {
    lateinit var xLogPath: String

    /**
     * https://github.com/elvishew/xLog/blob/master/README_ZH.md
     */
    fun initLogger(app: Context) {
        xLogPath = with(
            runCatching { app.getExternalFilesDir("xlog") }.getOrNull() ?: File(
                app.filesDir,
                "xlog"
            )
        ) {
            absolutePath // 指定保存日志文件的路径
        }

        val config = LogConfiguration.Builder()
            .run {
//                if (!BuildConfig.DEBUG) {
//                    logLevel(LogLevel.ERROR)
//                } else {
                logLevel(LogLevel.ALL)
//                }
            }
            .build()

        val androidPrinter: Printer = AndroidPrinter(true) // 通过 android.util.Log 打印日志的打印器

        val filePrinter: Printer = FilePrinter.Builder(xLogPath)
            .fileNameGenerator(DateFileNameGenerator()) // 指定日志文件名生成器，默认为 ChangelessFileNameGenerator("log")
            .backupStrategy(NeverBackupStrategy()) // 指定日志文件备份策略，默认为 FileSizeBackupStrategy(1024 * 1024)
            .cleanStrategy(FileLastModifiedCleanStrategy(30 * 24 * 3600_000L)) // 指定日志文件清除策略，默认为 NeverCleanStrategy()
            .build()

        XLog.init( // 初始化 XLog
            config, // 指定日志配置，如果不指定，会默认使用 new LogConfiguration.Builder().build()
            androidPrinter, // 添加任意多的打印器。如果没有添加任何打印器，会默认使用 AndroidPrinter(Android)/ConsolePrinter(java)
            filePrinter
        )
    }

    fun log(
        level: Int,
        msg: String?,
        tag: String? = "LogUtil",
    ) {
        LogUtils.printLog(level, tag.orEmpty(), true, msg)
    }

    fun d(
        msg: String?,
        tag: String? = "LogUtil",
        dividerTop: Boolean = false,
        dividerBottom: Boolean = false,
    ) {
        log(Log.DEBUG, msg, tag)
    }

    fun i(
        msg: String?,
        tag: String? = "LogUtil",
        dividerTop: Boolean = false,
        dividerBottom: Boolean = false,
    ) {
        log(Log.INFO, msg, tag)
    }

    fun w(
        msg: String?,
        tag: String? = "LogUtil",
        dividerTop: Boolean = false,
        dividerBottom: Boolean = false,
    ) {
        log(Log.WARN, msg, tag)
    }

    fun e(
        msg: String?,
        tag: String? = "LogUtil",
        dividerTop: Boolean = false,
        dividerBottom: Boolean = false,
    ) {
        log(Log.ERROR, msg, tag)
    }
}

//region 特殊的需要声明tag的

interface LogTagOwner {
    val logTag: String
}

fun LogTagOwner.logd(
    msg: String,
) = LogUtil.d(msg, logTag)

fun LogTagOwner.logi(
    msg: String,
) = LogUtil.i(msg, logTag)

fun LogTagOwner.logw(
    msg: String,
) = LogUtil.w(msg, logTag)

fun LogTagOwner.loge(
    msg: String,
) = LogUtil.e(msg, logTag)

//endregion
