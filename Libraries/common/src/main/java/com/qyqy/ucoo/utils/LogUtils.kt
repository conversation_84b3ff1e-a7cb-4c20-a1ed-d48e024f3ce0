package com.qyqy.ucoo.utils

import android.util.Log
import com.elvishew.xlog.XLog
import com.overseas.common.BuildConfig
import com.overseas.common.utils.RecycleStringBuilder
import java.util.Formatter
import java.util.concurrent.ConcurrentHashMap

/**
 *  @time 2024/7/9
 *  <AUTHOR>
 *  @package com.qyqy.core
 *
 *  针对compose相关的代码, 只能使用使用LogUtils object的内置方法, 同时也没有办法自动生成Tag, 毕竟compose没有类
 *  后面想想是通过ASM做替换, 还是手动把上层XLog相关的代码替换
 */

//region 针对kotlin类的扩展方法

internal val concurrentHashMap = ConcurrentHashMap<String, String>()

val Any.TAG: String
    get() { // 用到模板tag的类,将tag保存到SharedPreferences,用开关开启输出
        // 1.如果是一个String,就返回自身
        if (this is String) {
            return this
        }
        val clazzName = this.javaClass.name
        // 防止过多的tag存储把内存撑爆了, 达到上限时清空一波
        // 可能会把内存撑爆, 但是把内存撑爆有点不可能 2*20*2000 = 400KB
        if (concurrentHashMap.size > 2000) {
            concurrentHashMap.clear()
        }
        val value = concurrentHashMap[clazzName]
        // 2.如果已经保存了,就直接返回value
        if (value != null) {
            return value
        }
        var tag = clazzName
        val start = tag.lastIndexOf(".")
        val end = tag.indexOf("\$", if (start < 0) 0 else start)
        if (start > 0 || end > 0) {
            tag = tag.substring(if (start < 0) 0 else start + 1, if (end < 0) tag.length else end)
        }
        concurrentHashMap[clazzName] = tag
        return tag
    }

fun Any.logV(vararg args: Any?) {
    LogUtils.printLog(Log.VERBOSE, TAG, true, *args)
}

fun Any.logD(vararg args: Any?) {
    LogUtils.printLog(Log.DEBUG, TAG, true, *args)
}

fun Any.logI(vararg args: Any?) {
    LogUtils.printLog(Log.INFO, TAG, true, *args)
}

fun Any.logW(vararg args: Any?) {
    LogUtils.printLog(Log.WARN, TAG, true, *args)
}

fun Any.logE(vararg args: Any?) {
    LogUtils.printLog(Log.ERROR, TAG, true, *args)
}

//endregion

//region internal

private fun isFormatStr(params: Array<out Any?>): Boolean {
    if (params.size > 1) {
        val firstStr = params[0]
        return firstStr is String && firstStr.contains("%")
    } else {
        return false
    }
}

private fun convertObj(it: Any?): String {
    return when (it) {
        null -> "null"
        is Collection<*> -> it.joinToString(",")
        is Array<*> -> it.joinToString(",")
        is Exception -> it.stackTraceToString()
        else -> it.toString()
    }
}

private fun getThreadInfo(): String {
    val currentThread = Thread.currentThread()
    val sbuilder = RecycleStringBuilder.obtain()
    sbuilder.append("[").append(currentThread.name).append("/").append(currentThread.priority).append("]")
    val result = sbuilder.toString()
    sbuilder.recycle()
    return result
}

private fun convertMessage(tag: String, appendThreadInfo: Boolean, argruments: Array<out Any?>): String {
    // 1.判断是否为format形式
    val isFormatStr = isFormatStr(argruments)
    val sBuilder = RecycleStringBuilder.obtain()
    if (appendThreadInfo) {
        sBuilder.append(getThreadInfo())
    }

    if (!isFormatStr && argruments.size == 1) {
        sBuilder.append(convertObj(argruments))
    } else {
        if (isFormatStr) {
            val params = arrayOfNulls<Any>(argruments.size - 1)
            argruments.forEachIndexed { index, it ->
                if (index > 0) {
                    params[index - 1] = convertObj(it)
                }
            }
            try {
                Formatter(sBuilder.sBuilder).format(argruments[0] as String, *params)
            } catch (e: Exception) {
                sBuilder.append("format failed. params = ")
                params.forEach { sBuilder.append(it) }
                sBuilder.append(e.message)
            }
        } else {
            argruments.forEachIndexed { index, value ->
                sBuilder.append(convertObj(value)).append(" ")
            }
        }
    }
    val message = sBuilder.toString()
    sBuilder.recycle()
    return message
}

//endregion

//region 针对compose和java相关的静态方法

object LogUtils {
    @JvmStatic
    fun v(tag: String, vararg args: Any?) {
        printLog(Log.VERBOSE, tag, true, *args)
    }

    @JvmStatic
    fun d(tag: String, vararg args: Any?) {
        printLog(Log.DEBUG, tag, true, *args)
    }

    @JvmStatic
    fun i(tag: String, vararg args: Any?) {
        printLog(Log.INFO, tag, true, *args)
    }

    @JvmStatic
    fun w(tag: String, vararg args: Any?) {
        printLog(Log.WARN, tag, true, *args)
    }

    @JvmStatic
    fun e(tag: String, vararg args: Any?) {
        printLog(Log.ERROR, tag, true, *args)
    }

    @JvmStatic
    fun printLog(priority: Int, tag: String, appendThreadInfo: Boolean, vararg args: Any?) {
        logCallbacks.forEach {
            it.onLog(priority, tag, "", *args)
        }
        if (priority < logPriority) {
            return
        }

        val finalMsg = convertMessage(tag, appendThreadInfo, args)

        if (args.isEmpty()) {
            return
        } else {
            XLog.tag(tag).log(priority, finalMsg)
        }
    }

    var logPriority: Int = if (BuildConfig.DEBUG) Log.VERBOSE else Log.ERROR
        set(value) {
            if (value > Log.ERROR || value < Log.VERBOSE) {
                throw IllegalArgumentException("priority must be between Log.VERBOSE and Log.ERROR")
            }
            field = value
        }

    //region 其他需要处理Log的拦截器
    internal val logCallbacks = LinkedHashSet<LogCallback>()

    fun interface LogCallback {
        fun onLog(priority: Int, tag: String, message: String, vararg args: Any?)
    }

    fun addCallback(callback: LogCallback) {
        logCallbacks.add(callback)
    }

    fun removeCallback(callback: LogCallback) {
        logCallbacks.remove(callback)
    }

//endregion
}

//endregion


