package com.qyqy.ucoo.utils

import android.R
import android.annotation.SuppressLint
import android.annotation.TargetApi
import android.app.Activity
import android.app.AppOpsManager
import android.app.Application
import android.app.Application.ActivityLifecycleCallbacks
import android.app.NotificationManager
import android.content.Context
import android.content.res.Resources
import android.graphics.PixelFormat
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.os.Build
import android.os.Build.VERSION_CODES
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.os.SystemClock
import android.provider.Settings
import android.util.Log
import android.util.TypedValue
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.WindowManager.BadTokenException
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityManager
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.StringRes
import com.overseas.common.utils.postToMainThread
import java.lang.ref.WeakReference
import java.lang.reflect.InvocationHandler
import java.lang.reflect.InvocationTargetException
import java.lang.reflect.Method
import java.lang.reflect.Proxy
import kotlin.concurrent.Volatile

/**
 * 从轮子哥getActivity里拿到的Toaster库
 * 使用kotlin写一遍
 * 把所有的类都集中到一个文件中, 方便引入.
 */
// -keep class com.qyqy.ucoo.ToastUtils {*;}
object ToastUtils {

    private lateinit var defaultStyle: IToastStyle<*>
    private lateinit var defaultStrategy: ToastStrategy
    private lateinit var sApplication: Application

    interface IToastStyle<V : View> {
        /**
         * 创建 Toast 视图
         */
        fun createView(context: Context): V

        /**
         * 获取 Toast 显示重心
         */
        fun getGravity(): Int {
            return Gravity.CENTER
        }

        /**
         * 获取 Toast 水平偏移
         */
        fun getXOffset(): Int {
            return 0
        }

        /**
         * 获取 Toast 垂直偏移
         */
        fun getYOffset(): Int {
            return 0
        }

        /**
         * 获取 Toast 水平间距
         */
        fun getHorizontalMargin(): Float {
            return 0f
        }

        /**
         * 获取 Toast 垂直间距
         */
        fun getVerticalMargin(): Float {
            return 0f
        }
    }

    interface IToast {
        /**
         * 显示
         */
        fun show()

        /**
         * 取消
         */
        fun cancel()

        /**
         * 设置文本
         */
        fun setText(id: Int)

        fun setText(text: CharSequence?)

        fun getView(): View?

        fun setView(view: View?)

        fun getDuration(): Int

        fun setDuration(duration: Int)

        /**
         * 设置重心偏移
         */
        fun setGravity(gravity: Int, xOffset: Int, yOffset: Int)

        /**
         * 获取显示重心
         */
        fun getGravity(): Int

        /**
         * 获取水平偏移
         */
        fun getXOffset(): Int

        /**
         * 获取垂直偏移
         */
        fun getYOffset(): Int

        /**
         * 设置屏幕间距
         */
        fun setMargin(horizontalMargin: Float, verticalMargin: Float)

        /**
         * 设置水平间距
         */
        fun getHorizontalMargin(): Float

        /**
         * 设置垂直间距
         */
        fun getVerticalMargin(): Float

        /**
         * 智能获取用于显示消息的 TextView
         */
        fun findMessageView(view: View): TextView {
            if (view is TextView) {
                if (view.getId() == View.NO_ID) {
                    view.setId(R.id.message)
                } else {
                    require(view.getId() == R.id.message) {
                        // 必须将 TextView 的 id 值设置成 android.R.id.message
                        // 否则 Android 11 手机上在后台 toast.setText 的时候会出现报错
                        // java.lang.RuntimeException: This Toast was not created with Toast.makeText()
                        "You must set the ID value of TextView to android.R.id.message"
                    }
                }
                return view
            }

            val messageView = view.findViewById<View>(R.id.message)
            if (messageView is TextView) {
                return messageView
            }

            // 如果设置的布局没有包含一个 TextView 则抛出异常，必须要包含一个 id 值为 message 的 TextView（xml 代码 android:id="@android:id/message"，java 代码 view.setId(android.R.id.message)）
            throw java.lang.IllegalArgumentException(
                "You must include a TextView with an ID value of message (xml code: android:id=\"@android:id/message\", java code: view.setId(android.R.id.message))"
            )
        }
    }

    //region toast实体类

    //region 基础
    abstract class CustomToast : IToast {
        /** Toast 消息 View  */
        private var mMessageView: TextView? = null
        private var mView: View? = null
        override fun setText(id: Int) {
            getView()?.let {
                setText(it.resources.getString(id))
            }
        }

        override fun setText(text: CharSequence?) {
            mMessageView?.setText(text)
        }

        override fun getView(): View? = mView
        override fun setView(view: View?) {
            mView = view
            if (view != null) {
                mMessageView = findMessageView(view)
            } else {
                mMessageView = null
            }
        }


        private var mGravity = 0
        private var xOffset = 0
        private var yOffset = 0
        private var horizontalMargin = 0f
        private var verticalMargin = 0f
        private var mDuration = 0

        override fun setDuration(duration: Int) {
            mDuration = duration
        }

        override fun getDuration(): Int = mDuration

        override fun setGravity(gravity: Int, xOffset: Int, yOffset: Int) {
            this.mGravity = gravity
            this.xOffset = xOffset
            this.yOffset = yOffset
        }

        override fun getXOffset(): Int = xOffset
        override fun getYOffset(): Int = yOffset

        override fun getHorizontalMargin(): Float = horizontalMargin
        override fun getVerticalMargin(): Float = verticalMargin
        override fun getGravity(): Int = mGravity

        override fun setMargin(horizontalMargin: Float, verticalMargin: Float) {
            this.horizontalMargin = horizontalMargin
            this.verticalMargin = verticalMargin
        }

        override fun findMessageView(view: View): TextView {
            return super.findMessageView(view)
        }

        var shortDuration = 2000
        var longDuration = 3500
        var animations = android.R.style.Animation_Toast
    }

    internal class WindowLifecycle : ActivityLifecycleCallbacks {
        /** 当前 Activity 对象  */
        private var mActivity: Activity? = null

        /** 当前 Application 对象  */
        private var mApplication: Application? = null

        /** 自定义 Toast 实现类  */
        private var mToastImpl: ToastImpl? = null

        constructor(activity: Activity) {
            mActivity = activity
        }

        constructor(application: Application) {
            mApplication = application
        }

        val windowManager: WindowManager?
            /**
             * 获取 WindowManager 对象
             */
            get() {
                if (mActivity != null) {
                    if (Build.VERSION.SDK_INT >= VERSION_CODES.JELLY_BEAN_MR1 && mActivity!!.isDestroyed) {
                        return null
                    }
                    return mActivity!!.windowManager
                } else if (mApplication != null) {
                    return mApplication!!.getSystemService(Context.WINDOW_SERVICE) as WindowManager
                }

                return null
            }

        /**
         * [Application.ActivityLifecycleCallbacks]
         */
        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {}

        override fun onActivityStarted(activity: Activity) {}

        override fun onActivityResumed(activity: Activity) {}

        // A 跳转 B 页面的生命周期方法执行顺序：
        // onPause(A) ---> onCreate(B) ---> onStart(B) ---> onResume(B) ---> onStop(A) ---> onDestroyed(A)
        override fun onActivityPaused(activity: Activity) {
            if (mActivity !== activity) {
                return
            }

            if (mToastImpl == null) {
                return
            }

            // 不能放在 onStop 或者 onDestroyed 方法中，因为此时新的 Activity 已经创建完成，必须在这个新的 Activity 未创建之前关闭这个 WindowManager
            // 调用取消显示会直接导致新的 Activity 的 onCreate 调用显示吐司可能显示不出来的问题，又或者有时候会立马显示然后立马消失的那种效果
            mToastImpl!!.cancel()
        }

        override fun onActivityStopped(activity: Activity) {}

        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}

        override fun onActivityDestroyed(activity: Activity) {
            if (mActivity !== activity) {
                return
            }

            if (mToastImpl != null) {
                mToastImpl!!.cancel()
            }

            unregister()
            mActivity = null
        }

        /**
         * 注册
         */
        fun register(impl: ToastImpl?) {
            mToastImpl = impl
            if (mActivity == null) {
                return
            }
            if (Build.VERSION.SDK_INT >= VERSION_CODES.Q) {
                mActivity!!.registerActivityLifecycleCallbacks(this)
            } else {
                mActivity!!.application.registerActivityLifecycleCallbacks(this)
            }
        }

        /**
         * 反注册
         */
        fun unregister() {
            mToastImpl = null
            if (mActivity == null) {
                return
            }
            if (Build.VERSION.SDK_INT >= VERSION_CODES.Q) {
                mActivity!!.unregisterActivityLifecycleCallbacks(this)
            } else {
                mActivity!!.application.unregisterActivityLifecycleCallbacks(this)
            }
        }
    }

    internal class ToastImpl private constructor(
        context: Context,
        /** 当前的吐司对象  */
        private val mToast: CustomToast
    ) {
        /** WindowManager 辅助类  */
        private var mWindowLifecycle: WindowLifecycle? = null

        /** 当前应用的包名  */
        private val mPackageName: String = context.packageName

        /** 当前是否已经显示  */
        var isShow: Boolean = false

        /** 当前是否全局显示  */
        private var mGlobalShow = false

        constructor(activity: Activity, toast: CustomToast) : this(activity as Context, toast) {
            mGlobalShow = false
            mWindowLifecycle = WindowLifecycle((activity))
        }

        constructor(application: Application, toast: CustomToast) : this(application as Context, toast) {
            mGlobalShow = true
            mWindowLifecycle = WindowLifecycle(application)
        }

        /***
         * 显示吐司弹窗
         */
        fun show() {
            if (isShow) {
                return
            }
            if (isMainThread) {
                mShowRunnable.run()
            } else {
                HANDLER.removeCallbacks(mShowRunnable)
                HANDLER.post(mShowRunnable)
            }
        }

        /**
         * 取消吐司弹窗
         */
        fun cancel() {
            if (!isShow) {
                return
            }
            HANDLER.removeCallbacks(mShowRunnable)
            if (isMainThread) {
                mCancelRunnable.run()
            } else {
                HANDLER.removeCallbacks(mCancelRunnable)
                HANDLER.post(mCancelRunnable)
            }
        }

        private val isMainThread: Boolean
            /**
             * 判断当前是否在主线程
             */
            get() = Looper.myLooper() == Looper.getMainLooper()

        /**
         * 发送无障碍事件
         */
        private fun trySendAccessibilityEvent(view: View?) {
            if (view == null) {
                return
            }
            val context = view.context
            val accessibilityManager =
                context.getSystemService(Context.ACCESSIBILITY_SERVICE) as AccessibilityManager
            if (!accessibilityManager.isEnabled) {
                return
            }
            val eventType = AccessibilityEvent.TYPE_NOTIFICATION_STATE_CHANGED
            val event: AccessibilityEvent
            if (Build.VERSION.SDK_INT >= VERSION_CODES.R) {
                event = AccessibilityEvent()
                event.eventType = eventType
            } else {
                event = AccessibilityEvent.obtain(eventType)
            }
            event.className = Toast::class.java.name
            event.packageName = context.packageName
            view.dispatchPopulateAccessibilityEvent(event)
            // 将 Toast 视为通知，因为它们用于向用户宣布短暂的信息
            accessibilityManager.sendAccessibilityEvent(event)
        }

        private val mShowRunnable: Runnable = object : Runnable {
            @SuppressLint("WrongConstant")
            override fun run() {
                val windowManager = mWindowLifecycle!!.windowManager ?: return

                val params = WindowManager.LayoutParams()
                params.height = WindowManager.LayoutParams.WRAP_CONTENT
                params.width = WindowManager.LayoutParams.WRAP_CONTENT
                params.format = PixelFormat.TRANSLUCENT
                params.flags = (
                        WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
                                or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                                or WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE
                        )
                params.packageName = mPackageName
                params.gravity = mToast.getGravity()
                params.x = mToast.getXOffset()
                params.y = mToast.getYOffset()
                params.verticalMargin = mToast.getVerticalMargin()
                params.horizontalMargin = mToast.getHorizontalMargin()
                params.windowAnimations = mToast.animations

                // 如果是全局显示
                if (mGlobalShow) {
                    if (Build.VERSION.SDK_INT >= VERSION_CODES.O) {
                        params.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                        // 在 type 等于 TYPE_APPLICATION_OVERLAY 的时候
                        // 不能添加 WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE 标记
                        // 否则会导致在 Android 13 上面会出现 Toast 布局被半透明化的效果
                        // Github issue 地址：https://github.com/getActivity/Toaster/issues/108
                        params.flags = params.flags and WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE.inv()
                    } else {
                        params.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
                    }
                }

                try {
                    windowManager.addView(mToast.getView(), params)
                    // 添加一个移除吐司的任务
                    HANDLER.postDelayed(
                        { cancel() },
                        (if (mToast.getDuration() == Toast.LENGTH_LONG) mToast.longDuration else mToast.shortDuration).toLong()
                    )
                    // 注册生命周期管控
                    mWindowLifecycle!!.register(this@ToastImpl)
                    // 当前已经显示
                    isShow = true
                    // 发送无障碍事件
                    trySendAccessibilityEvent(mToast.getView())
                } catch (e: IllegalStateException) {
                    // 如果这个 View 对象被重复添加到 WindowManager 则会抛出异常
                    // java.lang.IllegalStateException: View android.widget.TextView has already been added to the window manager.
                    // 如果 WindowManager 绑定的 Activity 已经销毁，则会抛出异常
                    // android.view.WindowManager$BadTokenException: Unable to add window -- token android.os.BinderProxy@ef1ccb6 is not valid; is your activity running?
                    e.printStackTrace()
                } catch (e: BadTokenException) {
                    e.printStackTrace()
                }
            }
        }

        private val mCancelRunnable: Runnable = object : Runnable {
            override fun run() {
                try {
                    val windowManager = mWindowLifecycle!!.windowManager ?: return

                    windowManager.removeViewImmediate(mToast.getView())
                } catch (e: java.lang.IllegalArgumentException) {
                    // 如果当前 WindowManager 没有附加这个 View 则会抛出异常
                    // java.lang.IllegalArgumentException: View=android.widget.TextView not attached to window manager
                    e.printStackTrace()
                } finally {
                    // 反注册生命周期管控
                    mWindowLifecycle!!.unregister()
                    // 当前没有显示
                    isShow = false
                }
            }
        }

        companion object {
            private val HANDLER = Handler(Looper.getMainLooper())
        }
    }
    //endregion

    open class ActivityToast(activity: Activity) : CustomToast() {
        /** Toast 实现类  */
        private val mToastImpl = ToastImpl(activity, this)

        override fun show() {
            // 替换成 WindowManager 来显示
            mToastImpl.show()
        }

        override fun cancel() {
            // 取消 WindowManager 的显示
            mToastImpl.cancel()
        }
    }

    open class SystemToast(application: Application) : Toast(application), IToast {
        /** 吐司消息 View  */
        private var mMessageView: TextView? = null

        override fun setView(view: View?) {
            super.setView(view)
            if (view == null) {
                mMessageView = null
            } else {
                mMessageView = findMessageView(view)
            }
        }

        override fun setText(text: CharSequence?) {
            super.setText(text)
            if (mMessageView == null) {
                return
            }
            mMessageView!!.text = text
        }
    }

    @TargetApi(VERSION_CODES.KITKAT)
    @SuppressWarnings("all")
    open class SafeToast(application: Application) : NotificationToast(application) {
        /** 是否已经 Hook 了一次 TN 内部类  */
        private var mHookTN = false

        override fun show() {
            hookToastTN()
            super.show()
        }

        private fun hookToastTN() {
            if (mHookTN) {
                return
            }
            mHookTN = true

            try {
                // 获取 Toast.mTN 字段对象
                val mTNField = Toast::class.java.getDeclaredField("mTN")
                mTNField.isAccessible = true
                val mTN = mTNField[this]

                // 获取 mTN 中的 mHandler 字段对象
                val mHandlerField = mTNField.type.getDeclaredField("mHandler")
                mHandlerField.isAccessible = true
                val mHandler = mHandlerField[mTN] as Handler

                // 如果这个对象已经被反射替换过了
                if (mHandler is SafeHandler) {
                    return
                }

                // 偷梁换柱
                mHandlerField[mTN] = SafeHandler(mHandler)
            } catch (e: IllegalAccessException) {
                // Android 9.0 上反射会出现报错
                // Accessing hidden field Landroid/widget/Toast;->mTN:Landroid/widget/Toast$TN;
                // java.lang.NoSuchFieldException: No field mTN in class Landroid/widget/Toast;
                e.printStackTrace()
            } catch (e: NoSuchFieldException) {
                e.printStackTrace()
            }
        }
    }

    open class GlobalToast(application: Application) : CustomToast() {
        /** Toast 实现类  */
        private val mToastImpl = ToastImpl(application, this)

        override fun show() {
            // 替换成 WindowManager 来显示
            mToastImpl.show()
        }

        override fun cancel() {
            // 取消 WindowManager 的显示
            mToastImpl.cancel()
        }
    }

    open class NotificationToast(application: Application) : SystemToast(application) {
        override fun show() {
            hookNotificationService()
            super.show()
        }

        companion object {
            /** 是否已经 Hook 了一次通知服务  */
            private var sHookService = false

            @SuppressLint("DiscouragedPrivateApi", "PrivateApi", "SoonBlockedPrivateApi")
            private fun hookNotificationService() {
                if (sHookService) {
                    return
                }
                sHookService = true
                try {
                    // 获取到 Toast 中的 getService 静态方法
                    val getService = Toast::class.java.getDeclaredMethod("getService")
                    getService.isAccessible = true
                    // 执行方法，会返回一个 INotificationManager$Stub$Proxy 类型的对象
                    val iNotificationManager = getService.invoke(null) ?: return
                    // 如果这个对象已经被动态代理过了，并且已经 Hook 过了，则不需要重复 Hook
                    if (Proxy.isProxyClass(iNotificationManager.javaClass) &&
                        Proxy.getInvocationHandler(iNotificationManager) is NotificationServiceProxy
                    ) {
                        return
                    }
                    val iNotificationManagerProxy = Proxy.newProxyInstance(
                        Thread.currentThread().contextClassLoader,
                        arrayOf(Class.forName("android.app.INotificationManager")),
                        NotificationServiceProxy(iNotificationManager)
                    )
                    // 将原来的 INotificationManager$Stub$Proxy 替换掉
                    val sService = Toast::class.java.getDeclaredField("sService")
                    sService.isAccessible = true
                    sService[null] = iNotificationManagerProxy
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    //endregion

    //region toast样式

    class CustomToastStyle @JvmOverloads constructor(id: Int, gravity: Int = Gravity.CENTER, xOffset: Int = 0, yOffset: Int = 0, horizontalMargin: Float = 0f, verticalMargin: Float = 0f) :
        IToastStyle<View> {
        private val mLayoutId = id
        private val mGravity = gravity
        private val mXOffset = xOffset
        private val mYOffset = yOffset
        private val mHorizontalMargin = horizontalMargin
        private val mVerticalMargin = verticalMargin

        override fun createView(context: Context): View {
            return LayoutInflater.from(context).inflate(mLayoutId, null)
        }

        override fun getGravity(): Int {
            return mGravity
        }

        override fun getXOffset(): Int {
            return mXOffset
        }

        override fun getYOffset(): Int {
            return mYOffset
        }

        override fun getHorizontalMargin(): Float {
            return mHorizontalMargin
        }

        override fun getVerticalMargin(): Float {
            return mVerticalMargin
        }
    }

    @Suppress("unused", "deprecation")
    open class BlackToastStyle : IToastStyle<View> {
        override fun createView(context: Context): View {
            val textView = TextView(context)
            textView.id = R.id.message
            textView.gravity = getTextGravity(context)
            textView.setTextColor(getTextColor(context))
            textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, getTextSize(context))

            val horizontalPadding = getHorizontalPadding(context)
            val verticalPadding = getVerticalPadding(context)

            // 适配布局反方向特性
            if (Build.VERSION.SDK_INT >= VERSION_CODES.JELLY_BEAN) {
                textView.setPaddingRelative(horizontalPadding, verticalPadding, horizontalPadding, verticalPadding)
            } else {
                textView.setPadding(horizontalPadding, verticalPadding, horizontalPadding, verticalPadding)
            }

            textView.layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)

            val backgroundDrawable = getBackgroundDrawable(context)
            // 设置背景
            if (Build.VERSION.SDK_INT >= VERSION_CODES.JELLY_BEAN) {
                textView.background = backgroundDrawable
            } else {
                textView.setBackgroundDrawable(backgroundDrawable)
            }

            // 设置 Z 轴阴影
            if (Build.VERSION.SDK_INT >= VERSION_CODES.LOLLIPOP) {
                textView.z = getTranslationZ(context)
            }

            return textView
        }

        protected open fun getTextGravity(context: Context): Int {
            return Gravity.CENTER
        }

        protected open fun getTextColor(context: Context): Int {
            return -0x11000001
        }

        protected open fun getTextSize(context: Context): Float {
            return TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_SP,
                14f,
                context.resources.displayMetrics
            )
        }

        protected open fun getHorizontalPadding(context: Context): Int {
            return TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP,
                24f,
                context.resources.displayMetrics
            ).toInt()
        }

        protected open fun getVerticalPadding(context: Context): Int {
            return TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP,
                16f,
                context.resources.displayMetrics
            ).toInt()
        }

        protected open fun getBackgroundDrawable(context: Context): Drawable {
            val drawable = GradientDrawable()
            // 设置颜色
            drawable.setColor(-0x4d000000)
            // 设置圆角
            drawable.cornerRadius = TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP,
                10f, context.resources.displayMetrics
            )
            return drawable
        }

        protected open fun getTranslationZ(context: Context): Float {
            return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 3f, context.resources.displayMetrics)
        }
    }

    @Suppress("unused")
    class LocationToastStyle @JvmOverloads constructor(
        private val mStyle: IToastStyle<*>,
        private val mGravity: Int,
        private val mXOffset: Int = 0,
        private val mYOffset: Int = 0,
        private val mHorizontalMargin: Float = 0f,
        private val mVerticalMargin: Float = 0f
    ) :
        IToastStyle<View> {
        override fun createView(context: Context): View {
            return mStyle.createView(context)
        }

        override fun getGravity(): Int {
            return mGravity
        }

        override fun getXOffset(): Int {
            return mXOffset
        }

        override fun getYOffset(): Int {
            return mYOffset
        }

        override fun getHorizontalMargin(): Float {
            return mHorizontalMargin
        }

        override fun getVerticalMargin(): Float {
            return mVerticalMargin
        }
    }

    class WhiteToastStyle : BlackToastStyle() {
        override fun getTextColor(context: Context): Int {
            return -0x45000000
        }

        override fun getBackgroundDrawable(context: Context): Drawable {
            val drawable = GradientDrawable()
            // 设置颜色
            drawable.setColor(-0x151516)
            // 设置圆角
            drawable.cornerRadius = TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP,
                10f, context.resources.displayMetrics
            )
            return drawable
        }
    }
    //endregion

    //region 工具

    internal class ActivityStack private constructor() : ActivityLifecycleCallbacks {
        /**
         * 注册 Activity 生命周期监听
         */
        fun register(application: Application?) {
            if (application == null) {
                return
            }
            application.registerActivityLifecycleCallbacks(this)
        }

        /** 前台 Activity 对象  */
        var foregroundActivity: Activity? = null
            private set

        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {}

        override fun onActivityStarted(activity: Activity) {}

        override fun onActivityResumed(activity: Activity) {
            foregroundActivity = activity
        }

        override fun onActivityPaused(activity: Activity) {
            if (foregroundActivity !== activity) {
                return
            }
            foregroundActivity = null
        }

        override fun onActivityStopped(activity: Activity) {}

        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}

        override fun onActivityDestroyed(activity: Activity) {}

        companion object {
            val instance by lazy {
                ActivityStack()
            }
        }
    }

    internal class ToastParams(
        val text: CharSequence?,
        val duration: Int = if ((text?.length ?: 0) > 20) Toast.LENGTH_LONG else Toast.LENGTH_SHORT,
        val delayMillis: Long = 0L,
        val crossPageShow: Boolean = false,
        val style: IToastStyle<*> = defaultStyle,
        val strategy: ToastStrategy = defaultStrategy,
    )

    internal class ToastStrategy @JvmOverloads constructor(
        /** 吐司显示策略  */
        private val mShowStrategyType: Int = SHOW_STRATEGY_TYPE_IMMEDIATELY,
        private val mApplication: Application
    ) {

        /** Toast 对象  */
        private var mToastReference: WeakReference<IToast>? = null

        /** 显示消息 Token  */
        private val mShowMessageToken = Any()

        /** 取消消息 Token  */
        private val mCancelMessageToken = Any()

        /** 上一个 Toast 显示的时间  */
        @Volatile
        private var mLastShowToastMillis: Long = 0

        init {
            when (mShowStrategyType) {
                SHOW_STRATEGY_TYPE_IMMEDIATELY,
                SHOW_STRATEGY_TYPE_QUEUE -> {
                }

                else -> throw IllegalArgumentException("Please don't pass non-existent toast show strategy")
            }
        }

        fun createToast(params: ToastParams): IToast {
            val foregroundActivity = foregroundActivity
            val toast = if (Build.VERSION.SDK_INT >= VERSION_CODES.M && Settings.canDrawOverlays(mApplication)) {
                // 如果有悬浮窗权限，就开启全局的 Toast
                GlobalToast(mApplication)
            } else if (!params.crossPageShow && isActivityAvailable(foregroundActivity)) {
                // 如果没有悬浮窗权限，而且不是全局的toast, 就开启一个依附于 Activity 的 Toast
                ActivityToast(foregroundActivity!!)
            } else if (Build.VERSION.SDK_INT == VERSION_CODES.N_MR1) {
                // 处理 Android 7.1 上 Toast 在主线程被阻塞后会导致报错的问题
                SafeToast(mApplication)
            } else if (Build.VERSION.SDK_INT < VERSION_CODES.Q && !areNotificationsEnabled(mApplication)) {
                // 如果当前处于android Q以下 且没有通知权限，就开启通知栏Toast
                // 处理 Toast 关闭通知栏权限之后无法弹出的问题
                // 通过查看和对比 NotificationManagerService 的源码
                // 发现这个问题已经在 Android 10 版本上面修复了
                // 但是 Toast 只能在前台显示，没有通知栏权限后台 Toast 仍然无法显示
                // 并且 Android 10 刚好禁止了 Hook 通知服务
                // 已经有通知栏权限，不需要 Hook 系统通知服务也能正常显示系统 Toast
                NotificationToast(mApplication)
            } else {
                SystemToast(mApplication)
            }
            if (isSupportToastStyle(toast) || !onlyShowSystemToastStyle()) {
                diyToastStyle(toast, params.style)
            }
            return toast
        }

        fun showToast(params: ToastParams) {
            when (mShowStrategyType) {
                SHOW_STRATEGY_TYPE_IMMEDIATELY -> {
                    // 移除之前未显示的 Toast 消息
                    HANDLER.removeCallbacksAndMessages(mShowMessageToken)
                    val uptimeMillis = SystemClock.uptimeMillis() + params.delayMillis + (if (params.crossPageShow) 0 else DEFAULT_DELAY_TIMEOUT)
                    HANDLER.postAtTime(ShowToastRunnable(params), mShowMessageToken, uptimeMillis)
                }

                SHOW_STRATEGY_TYPE_QUEUE -> {
                    // 计算出这个 Toast 显示时间
                    var showToastMillis = SystemClock.uptimeMillis() + params.delayMillis + (if (params.crossPageShow) 0 else DEFAULT_DELAY_TIMEOUT)
                    // 根据吐司的长短计算出等待时间
                    val waitMillis = generateToastWaitMillis(params).toLong()
                    // 如果当前显示的时间在上一个 Toast 的显示范围之内
                    // 那么就重新计算 Toast 的显示时间
                    if (showToastMillis < (mLastShowToastMillis + waitMillis)) {
                        showToastMillis = mLastShowToastMillis + waitMillis
                    }
                    HANDLER.postAtTime(ShowToastRunnable(params), mShowMessageToken, showToastMillis)
                    mLastShowToastMillis = showToastMillis
                }

                else -> {}
            }
        }

        fun cancelToast() {
            HANDLER.removeCallbacksAndMessages(mCancelMessageToken)
            val uptimeMillis = SystemClock.uptimeMillis()
            HANDLER.postAtTime(CancelToastRunnable(), mCancelMessageToken, uptimeMillis)
        }

        /**
         * 是否支持设置自定义 Toast 样式
         */
        protected fun isSupportToastStyle(toast: IToast?): Boolean {
            // targetSdkVersion >= 30 的情况下在后台显示自定义样式的 Toast 会被系统屏蔽，并且日志会输出以下警告：
            // Blocking custom toast from package com.xxx.xxx due to package not in the foreground
            // targetSdkVersion < 30 的情况下 new Toast，并且不设置视图显示，系统会抛出以下异常：
            // java.lang.RuntimeException: This Toast was not created with Toast.makeText()
            return toast is CustomToast || Build.VERSION.SDK_INT < VERSION_CODES.R || mApplication!!.applicationInfo.targetSdkVersion < VERSION_CODES.R
        }

        /**
         * 定制 Toast 的样式
         */
        protected fun diyToastStyle(toast: IToast, style: IToastStyle<*>) {
            toast.setView(style.createView(mApplication))
            toast.setGravity(style.getGravity(), style.getXOffset(), style.getYOffset())
            toast.setMargin(style.getHorizontalMargin(), style.getVerticalMargin())
        }

        /**
         * 生成 Toast 等待时间
         */
        protected fun generateToastWaitMillis(params: ToastParams): Int {
            if (params.duration == Toast.LENGTH_SHORT) {
                return 1000
            } else if (params.duration == Toast.LENGTH_LONG) {
                return 1500
            }
            return 0
        }

        /**
         * 显示任务
         */
        private inner class ShowToastRunnable(private val mToastParams: ToastParams) : Runnable {
            override fun run() {
                var toast: IToast? = null
                toast = mToastReference?.get()
                toast?.cancel()
                toast = createToast(mToastParams)
                // 为什么用 WeakReference，而不用 SoftReference ？
                // https://github.com/getActivity/Toaster/issues/79
                mToastReference = WeakReference(toast)
                toast.setDuration(mToastParams.duration)
                toast.setText(mToastParams.text)
                toast.show()
            }
        }

        /**
         * 取消任务
         */
        private inner class CancelToastRunnable : Runnable {
            override fun run() {
                var toast: IToast? = null
                if (mToastReference != null) {
                    toast = mToastReference!!.get()
                }

                if (toast == null) {
                    return
                }
                toast.cancel()
            }
        }

        /**
         * 当前是否只能显示系统 Toast 样式
         */
        protected fun onlyShowSystemToastStyle(): Boolean {
            // Github issue 地址：https://github.com/getActivity/Toaster/issues/103
            // Toast.CHANGE_TEXT_TOASTS_IN_THE_SYSTEM = 147798919L
            return isChangeEnabledCompat(147798919L)
        }

        @SuppressLint("PrivateApi")
        protected fun isChangeEnabledCompat(changeId: Long): Boolean {
            // 需要注意的是这个 api 是在 android 11 的时候出现的，反射前需要先判断好版本
            if (Build.VERSION.SDK_INT < VERSION_CODES.R) {
                return true
            }
            try {
                // 因为 Compatibility.isChangeEnabled() 普通应用根本调用不到，反射也不行
                // 通过 Toast.isSystemRenderedTextToast 也没有办法反射到
                // 最后发现反射 CompatChanges.isChangeEnabled 是可以的
                val clazz = Class.forName("android.app.compat.CompatChanges")
                val method = clazz.getMethod("isChangeEnabled", Long::class.javaPrimitiveType)
                method.isAccessible = true
                return method.invoke(null, changeId).toString().toBoolean()
            } catch (e: ClassNotFoundException) {
                e.printStackTrace()
            } catch (e: InvocationTargetException) {
                e.printStackTrace()
            } catch (e: NoSuchMethodException) {
                e.printStackTrace()
            } catch (e: IllegalAccessException) {
                e.printStackTrace()
            }
            return false
        }

        /**
         * 是否有通知栏权限
         */
        @SuppressLint("PrivateApi")
        protected fun areNotificationsEnabled(context: Context?): Boolean {
            if (Build.VERSION.SDK_INT >= VERSION_CODES.N) {
                return context!!.getSystemService(NotificationManager::class.java).areNotificationsEnabled()
            }

            if (Build.VERSION.SDK_INT >= VERSION_CODES.KITKAT) {
                // 参考 Support 库中的方法： NotificationManagerCompat.from(context).areNotificationsEnabled()
                val appOps = context!!.getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
                try {
                    val method = appOps.javaClass.getMethod(
                        "checkOpNoThrow",
                        Integer.TYPE,
                        Integer.TYPE,
                        String::class.java
                    )
                    val field = appOps.javaClass.getDeclaredField("OP_POST_NOTIFICATION")
                    val value = field[Int::class.java] as Int
                    return (
                            method.invoke(
                                appOps, value, context.applicationInfo.uid,
                                context.packageName
                            ) as Int
                            ) == AppOpsManager.MODE_ALLOWED
                } catch (e: NoSuchMethodException) {
                    e.printStackTrace()
                    return true
                } catch (e: NoSuchFieldException) {
                    e.printStackTrace()
                    return true
                } catch (e: InvocationTargetException) {
                    e.printStackTrace()
                    return true
                } catch (e: IllegalAccessException) {
                    e.printStackTrace()
                    return true
                } catch (e: RuntimeException) {
                    e.printStackTrace()
                    return true
                }
            }
            return true
        }

        protected val foregroundActivity: Activity?
            /**
             * 获取前台的 Activity
             */
            get() = ActivityStack.instance.foregroundActivity

        /**
         * Activity 是否可用
         */
        protected fun isActivityAvailable(activity: Activity?): Boolean {
            if (activity == null) {
                return false
            }

            if (activity.isFinishing) {
                return false
            }

            if (Build.VERSION.SDK_INT >= VERSION_CODES.JELLY_BEAN_MR1) {
                return !activity.isDestroyed
            }
            return true
        }

        companion object {
            /**
             * 即显即示模式（默认）
             *
             * 在发起多次 Toast 的显示请求情况下，显示下一个 Toast 之前
             * 会先立即取消上一个 Toast，保证当前显示 Toast 消息是最新的
             */
            const val SHOW_STRATEGY_TYPE_IMMEDIATELY: Int = 0

            /**
             * 不丢消息模式
             *
             * 在发起多次 Toast 的显示请求情况下，等待上一个 Toast 显示 1 秒或者 1.5 秒后
             * 然后再显示下一个 Toast，不按照 Toast 的显示时长来，因为那样等待时间会很长
             * 这样既能保证用户能看到每一条 Toast 消息，又能保证用户不会等得太久，速战速决
             */
            const val SHOW_STRATEGY_TYPE_QUEUE: Int = 1

            /** Handler 对象  */
            private val HANDLER = Handler(Looper.getMainLooper())

            /**
             * 默认延迟时间
             *
             * 延迟一段时间之后再执行，因为在没有通知栏权限的情况下，Toast 只能显示在当前 Activity 上面
             * 如果当前 Activity 在 showToast 之后立马进行 finish 了，那么这个时候 Toast 可能会显示不出来
             * 因为 Toast 会显示在销毁 Activity 界面上，而不会显示在新跳转的 Activity 上面
             */
            private const val DEFAULT_DELAY_TIMEOUT = 200
        }
    }

    internal class SafeHandler(private val mHandler: Handler) : Handler() {
        override fun handleMessage(msg: Message) {
            // 捕获这个异常，避免程序崩溃
            try {
                // 目前发现在 Android 7.1 主线程被阻塞之后弹吐司会导致崩溃，可使用 Thread.sleep(5000) 进行复现
                // 查看源码得知 Google 已经在 Android 8.0 已经修复了此问题
                // 主线程阻塞之后 Toast 也会被阻塞，Toast 因为显示超时导致 Window Token 失效
                mHandler.handleMessage(msg)
            } catch (e: BadTokenException) {
                // android.view.WindowManager$BadTokenException：Unable to add window -- token android.os.BinderProxy is not valid; is your activity running?
                // java.lang.IllegalStateException：View android.widget.TextView has already been added to the window manager.
                e.printStackTrace()
            } catch (e: java.lang.IllegalStateException) {
                e.printStackTrace()
            }
        }
    }

    internal class NotificationServiceProxy(
        /** 被代理的对象  */
        private val mRealObject: Any
    ) : InvocationHandler {
        @Throws(Throwable::class)
        override fun invoke(proxy: Any, method: Method, args: Array<Any>): Any? {
            when (method.name) {
                "enqueueToast", "enqueueToastEx", "cancelToast" ->
                    // 将包名修改成系统包名，这样就可以绕过系统的拦截
                    // 部分华为机将 enqueueToast 方法名修改成了 enqueueToastEx
                    args[0] = "android"

                else -> {}
            }
            // 使用动态代理
            return method.invoke(mRealObject, *args)
        }
    }

    //endregion

    fun init(application: Application) {
        this.sApplication = application
        this.defaultStrategy = ToastStrategy(mApplication = application)
        defaultStyle = BlackToastStyle()
    }

    /**
     * 显示一个短 Toast
     */
    fun showShort(@StringRes resId: Int, vararg formatArgs: Any) {
        showShort(stringIdToCharSequence(resId,formatArgs))
    }

    fun showShort(text: CharSequence?) {
        val params = ToastParams(text, Toast.LENGTH_SHORT)
        show(params)
    }

    /**
     * 显示一个长 Toast
     */
    fun showLong(@StringRes resId: Int, vararg formatArgs: Any) {
        showLong(stringIdToCharSequence(resId,formatArgs))
    }

    fun showLong(text: CharSequence?) {
        val params = ToastParams(text, Toast.LENGTH_LONG)
        show(params)
    }

    /**
     * 显示 Toast
     */
    fun show(id: Int) {
        show(stringIdToCharSequence(id))
    }

    fun show(text: CharSequence?) {
        val params = ToastParams(text)
        show(params)
    }

    private fun show(params: ToastParams) {
        if (!::sApplication.isInitialized) {
            Log.e("ToastUtils", "cannot show toast. cause application is null. please set init() first")
            return
        }
        if (params.text.isNullOrBlank()) {
            return
        }

        params.strategy.showToast(params)
    }

    private fun stringIdToCharSequence(id: Int, vararg formatArgs: Any): CharSequence {
        return try {
            // 如果这是一个资源 id
            sApplication.resources.getString(id,formatArgs)
        } catch (ignored: Exception) {
            // 如果这是一个 int 整数
            id.toString()
        }
    }
}

fun Throwable.toast() {
    toast(message)
}

fun toast(msg: String?, duration: Int = Toast.LENGTH_SHORT) {
    if (msg.isNullOrBlank()) {
        return
    }
    try {
        if(duration == Toast.LENGTH_SHORT){
            ToastUtils.showShort(msg)
        }else{
            ToastUtils.showLong(msg)
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }
}

fun toastRes(@StringRes resId: Int, vararg formatArgs: Any, duration: Int = Toast.LENGTH_SHORT) {
    if(duration == Toast.LENGTH_SHORT){
        ToastUtils.showShort(resId, *formatArgs)
    }else{
        ToastUtils.showLong(resId, *formatArgs)
    }
}