package com.qyqy.ucoo.utils

import android.view.View
import android.view.ViewGroup

/**
 *  @time 9/19/24
 *  <AUTHOR>
 *  @package com.qyqy.ucoo.utils
 */
@JvmName("ucooUpdateLayoutParams")
public inline fun <reified T : ViewGroup.LayoutParams> View.updateLayoutParams(block: T.() -> Unit) {
    try {
        val params = layoutParams as T
        block(params)
        layoutParams = params
    } catch (e: kotlin.NullPointerException) {
        LogUtils.w(TAG, "updateLayoutParams error, %s", e)
    }
}

@JvmName("ucooUpdateLayoutParamsWithoutReified")
public inline fun View.updateLayoutParams(block: ViewGroup.LayoutParams.() -> Unit) {
    updateLayoutParams<ViewGroup.LayoutParams>(block)
}