// Top-level build file where you can add configuration options common to all sub-projects/modules.
@file:Suppress("DSL_SCOPE_VIOLATION")

import io.gitlab.arturbosch.detekt.Detekt
import io.gitlab.arturbosch.detekt.extensions.DetektExtension


plugins {
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.android.library) apply false
    alias(libs.plugins.kotlin.android) apply false
    alias(libs.plugins.kotlin.parcelize) apply false
    alias(libs.plugins.kotlin.kapt) apply false
    alias(libs.plugins.kotlin.serialization) apply false
    alias(libs.plugins.devtools.ksp) apply false
    alias(libs.plugins.google.services) apply false
    alias(libs.plugins.firebase.crashlytics) apply false

    id("org.sonarqube").version("3.4.0.2513")
    id("io.gitlab.arturbosch.detekt").version("1.23.6")
}

subprojects {
    tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
        kotlinOptions {
            if (project.findProperty("composeCompilerReports") == "true") {
                freeCompilerArgs += listOf("-P", "plugin:androidx.compose.compiler.plugins.kotlin:reportsDestination=${project.buildDir.absolutePath}/compose_compiler")
            }
            if (project.findProperty("composeCompilerMetrics") == "true") {
                freeCompilerArgs += listOf("-P", "plugin:androidx.compose.compiler.plugins.kotlin:metricsDestination=${project.buildDir.absolutePath}/compose_compiler")
            }
            allWarningsAsErrors = false
            freeCompilerArgs += listOf(
                "-opt-in=androidx.compose.material3.ExperimentalMaterial3Api",
                "-opt-in=androidx.compose.foundation.ExperimentalFoundationApi",
                "-opt-in=androidx.compose.ui.ExperimentalComposeUiApi",
                "-opt-in=kotlinx.coroutines.ExperimentalCoroutinesApi",
                "-opt-in=com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi"
            )
        }

    }
//    configurations.all {
//        resolutionStrategy.eachDependency {
//
//            val requested = this.requested
//            when {
//                requested.group == "androidx.activity" && requested.module.name == "activity" -> {
//                    this.useVersion(libs.versions.activity.get())
//                }
//
//                requested.group == "androidx.fragment" && requested.module.name == "fragment" -> {
//                    this.useVersion(libs.versions.fragment.get())
//                }
//
//                requested.group == "androidx.media" && requested.module.name == "media" -> {
//                    this.useVersion("1.7.0")
//                }
//
//                requested.group == "androidx.core" -> {
//                    if (requested.module.name == "core") {
//                        this.useVersion(libs.versions.ktx.get())
//                    } else if (requested.module.name == "core-ktx") {
//                        this.useVersion(libs.versions.ktx.get())
//                    }
//                }
//
////                requested.group == "androidx.lifecycle" -> {
////                    val name = requested.module.name
////                    if (name.startsWith("lifecycle-livedata")) {
////                        this.useVersion(libs.versions.lifecycle.get())
////                    } else if (name.startsWith("lifecycle-viewmodel")) {
////                        this.useVersion(libs.versions.lifecycle.get())
////                    } else if (name.startsWith("lifecycle-runtime")) {
////                        this.useVersion(libs.versions.lifecycle.get())
////                    }
////                }
//
//                requested.group == "androidx.constraintlayout" && requested.module.name == "constraintlayout" -> {
//                    this.useVersion(libs.versions.constraintlayout.get())
//                }
//
//                requested.group == "androidx.exifinterface" && requested.module.name == "exifinterface" -> {
//                    this.useVersion("1.3.6")
//                }
//
//                requested.group == "androidx.annotation" && requested.module.name == "annotation-jvm" -> {
//                    this.useVersion("1.8.0")
//                }
//
//                requested.group == "androidx.browser" && requested.module.name == "browser" -> {
//                    this.useVersion("1.2.0")
//                }
//
//                requested.group == "androidx.collection" && requested.module.name == "collection-ktx" -> {
//                    this.useVersion("1.4.0")
//                }
//
//                requested.group == "com.google.code.gson" && requested.module.name == "gson" -> {
//                    this.useVersion("2.11.0")
//                }
//
//                requested.group == "org.jetbrains.kotlin" && requested.module.name == "kotlin-stdlib" -> {
//                    this.useVersion(libs.versions.kotlin.get())
//                }
//
//                requested.group == "org.jetbrains.kotlin" && requested.module.name == "kotlin-stdlib-jdk7" -> {
//                    this.useVersion(libs.versions.kotlin.get())
//                }
//
//                requested.group == "org.jetbrains.kotlin" && requested.module.name == "kotlin-stdlib-jdk8" -> {
//                    this.useVersion(libs.versions.kotlin.get())
//                }
//
//                requested.group == "org.jetbrains.kotlinx" -> {
//                    if (requested.module.name == "kotlinx-coroutines-android" || requested.module.name == "kotlinx-coroutines-core-jvm") {
//                        this.useVersion(libs.versions.coroutines.get())
//                    } else if (requested.module.name == "kotlinx-datetime-jvm") {
//                        this.useVersion(libs.versions.kotlinxDatetime.get())
//                    }
//                }
//            }
//        }
//    }

    //region detekt
    /**
     * 使用./gradlew {projectName}:detekt生成报告, 报告一般在build/reports/detekt下
     * 使用./gradlew {projectName}:detektBaseline生成基线后, 下次在扫描时会绕过上次文件中列出的问题, 只提示新增问题
     */
    apply(plugin = "io.gitlab.arturbosch.detekt")

    detekt {
        // 配置 detekt 版本
        toolVersion = "1.22.0"

        // 配置要使用的规则集
        config = files("${project.rootDir}/config/detekt/detekt.yml")

        // 配置要排除的文件路径
        ignoreFailures = false
        buildUponDefaultConfig = true
        parallel = true
        allRules = false
        // 代码检测的目录
        source = files(
            DetektExtension.DEFAULT_SRC_DIR_JAVA, // src/main/java
            DetektExtension.DEFAULT_SRC_DIR_KOTLIN, // src/main/kotlin
        )
        // "基线"，未来运行将自动忽略这些问题。只报告基线中未找到的新问题。
        baseline = file("$projectDir/detekt-baseline.xml")
        // 启用/禁用 detekt 报告
        tasks.withType<Detekt>().configureEach {
            reports {
                // 类似 checkstyle 的格式主要用于类似 Jenkins 等集成
                xml.required.set(true)
                // 在浏览器中查看
                html.required.set(true)
                // 类似控制台输出到文本
                txt.required.set(true)
                // 标准化 SARIF 格式 (https:sarifweb.azurewebsites.net) 以支持与 Github 代码扫描的集成
                sarif.required.set(true)
                // Markdown
                md.required.set(true)
            }
        }
    }

    //endregion

    tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
        // 可以选择在编译时失败，如果检测到问题
        kotlinOptions {
            freeCompilerArgs += listOf("-Xlint=warnings")
        }
    }
}

//region sonarqube

/**
 * 使用./gradlew sonarquebe或右侧gradle列表中的sonarqube生成报告并上传
 * 在指定的*************:9000登录sonarqube后台可查看代码检查结果
 */
sonarqube {
    properties {
        // Sonar服务器地址
        property("sonar.host.url", "http://*************:9000")
        property("sonar.token", "sqp_5322135c252ca132e9ea333d157f4c113e0e549b")
        property("sonar.sourceEncoding", "UTF-8")
        property("sonar.projectKey", "Android")
        property("sonar.projectName", "ucoo")
        // 需要扫描的上传检测代码的模块，可以选择也可以配置哪一些需要或者不需要上传的模块(这里指APP模块下面的java包里面的全部)
        property("sonar.sources", "src/main/java,src/main/kotlin")
        property("sonar.projectVersion", project.version)
        property("sonar.exclusions", "Libraries/**/*.*,lib-full/**/*.*")
        property("sonar.java.binaries", "build/intermediates/javac/**")
    }
    androidVariant = "officialCommonPayDebug"
}

//endregion