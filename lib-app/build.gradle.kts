import org.jetbrains.kotlin.konan.properties.Properties
import org.jetbrains.kotlin.konan.properties.loadProperties

plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.parcelize)
    alias(libs.plugins.kotlin.serialization)
    alias(libs.plugins.devtools.ksp)
    id("org.sonarqube")
    id("com.qyqy.plugin")
}

val keystorePropertiesPath: String = rootProject.file("keystore.properties").path
val keystoreProperties: Properties = loadProperties(keystorePropertiesPath)

android {
    namespace = "com.qyqy.ucoo"
    compileSdk = libs.versions.compileSdk.get().toInt()

    defaultConfig {
        minSdk = libs.versions.minSdk.get().toInt()
        targetSdk = libs.versions.targetSdk.get().toInt()

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        ksp {
            arg("room.incremental", "true")
            arg("room.schemaLocation", "$projectDir/schemas")
        }
    }

    packaging {
        resources.excludes += "DebugProbesKt.bin"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            manifestPlaceholders["firebase_analytics_enable"] = true
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
        debug {
            initWith(getByName("release"))
        }
        create("cupidDebug") {
            initWith(getByName("debug"))
            matchingFallbacks += "debug"
        }
        create("preRelease") {
            initWith(getByName("release"))
        }
    }

    flavorDimensions += listOf("channel", "pay")

    productFlavors {
        create("play") {
            dimension = "channel"
            ndk {
                abiFilters += listOf("armeabi-v7a", "arm64-v8a")
            }
            packaging {
                jniLibs {
                    useLegacyPackaging = true
                }
            }
        }
        create("official") {
            dimension = "channel"
        }
        create("googlePay") {
            dimension = "pay"
        }
        create("commonPay") {
            dimension = "pay"
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
        freeCompilerArgs = listOf("-Xcontext-receivers", "-Xstring-concat=inline")
    }

    buildFeatures {
        compose = true
    }

    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.14"
    }

    viewBinding {
        enable = true
    }
    sourceSets {
        getByName("main") {
            res {
                srcDirs("src/main/res", "src/main/res-cupid")
            }
        }
    }

}

androidComponents {
    beforeVariants { variantBuilder ->
        // To check for a certain build type, use variantBuilder.buildType == "<buildType>"
        val channel = variantBuilder.productFlavors[0].second
        val pay = variantBuilder.productFlavors[1].second
        if (channel == "play" && pay != "googlePay") {
            // Gradle ignores any variants that satisfy the conditions above.
            variantBuilder.enable = false
        }
    }
}

val commonPayImplementation by configurations
val playImplementation by configurations
val cupidDebugImplementation by configurations

dependencies {
    implementation(libs.androidx.webkit)
    compileOnly(fileTree(mapOf("dir" to "../Libraries/commonLibs", "include" to listOf("*.jar", "*.aar"))))
    api(project(":Libraries:common"))
    api(project(":Libraries:easyalbum"))
    api(project(":Libraries:EasyFloat"))
    api(project(":Libraries:SudMGPWrapper"))
    api(project(":Libraries:transcode"))
    api(project(":APNG4Android:apng"))
    api(project(":APNG4Android:avif"))
    api(project(":APNG4Android:awebp"))
    api(project(":APNG4Android:gif"))

    api(libs.androidx.activity)
    api(libs.androidx.fragment)
    api(libs.androidx.lifecycle)
    api(libs.androidx.lifecycle.process)
    api(libs.androidx.viewmodel)
    api(libs.androidx.livedata)
    api(libs.androidx.preference)
    api(libs.android.material)

    api(libs.androidautosize)

    api(libs.androidx.credentials)
    api(libs.androidx.credentials.play.services.auth)
    api(libs.googleid)
    api(libs.login.facebook)
    api(libs.linesdk)

    api(libs.glide)
//    api(libs.glide.compose)
    api(libs.glide.transformations)
    ksp(libs.glide.ksp)

    api(libs.oss.android.sdk)
    // 腾讯云 https://cloud.tencent.com/document/product/436/12159#.E6.96.B9.E5.BC.8F.E4.B8.80.EF.BC.9A.E8.87.AA.E5.8A.A8.E9.9B.86.E6.88.90.EF.BC.88.E6.8E.A8.E8.8D.90.EF.BC.89
    api(libs.cos.android.lite)

    // 腾讯rtc
    api(libs.tencent.voice)

    api(libs.yyeva)

//    api(libs.image.animation.awebp)
//    api(libs.image.animation.awebp.glide.plugin)

    api(libs.stfalconimageviewer)
    api(libs.switch.button)
    api(libs.image.cropper)
    api(libs.spannable)

    api(libs.androidx.room.runtime)
    api(libs.androidx.room.ktx)
    annotationProcessor(libs.androidx.room.compiler)
    ksp(libs.androidx.room.compiler)

    api(platform(libs.firebase.bom))
    api(libs.firebase.crashlytics.ktx)
    api(libs.firebase.analytics.ktx)

    // 支付
    api(libs.google.billing)
    api(libs.google.billing.ktx)
    // 微信支付
    api(libs.wechat.sdk.android.without.mta)

    // exo
    api(libs.androidx.media3.exoplayer)
    api(libs.androidx.media3.datasource.cronet)
    api(libs.androidx.media3.ui)
//    api(libs.androidx.media3.exoplayer.workmanager)
    api(libs.videocache)

    // https://developer.android.com/guFide/playcore/in-app-review?hl=zh-cn Google评分
    playImplementation(libs.google.play.review)
    playImplementation(libs.google.play.review.ktx)

    // compose
    api(platform(libs.compose.bom))
    api(libs.androidx.material3)
    api(libs.androidx.foundation)
    api(libs.androidx.ui)
    api(libs.androidx.ui.graphics)
    api(libs.androidx.animation)
    api(libs.androidx.ui.viewbinding)
    api(libs.androidx.ui.tooling.preview)
    debugImplementation(libs.androidx.ui.tooling)
    cupidDebugImplementation(libs.androidx.ui.tooling)

    api(libs.androidx.navigation.compose)
    api(libs.androidx.lifecycle.runtime.compose)
    api(libs.androidx.lifecycle.viewmodel.compose)
    api(libs.androidx.constraintlayout.compose)
    api(libs.kotlinx.collections.immutable)
    api(libs.accompanist.placeholder)
    api(libs.accompanist.drawablepainter)
    api(libs.compose.material3.pullrefresh)

    // adjust
    api(libs.adjust.android)
    api(libs.installreferrer)
    api(libs.play.services.ads.identifier)
    api(libs.play.services.appset)

    api(libs.refresh.layout)
    api(libs.phonetextwatcher)
    api(libs.jsbridge)
    // 多语言切换
    api(libs.multilanguages)
    // webview缓存
    api(libs.cachewebviewlib)
    // 下载器
    api(libs.download.manager)
    api(libs.viewpump)
    // leak canary
    debugImplementation(libs.leakcanary.android)
    api(libs.kotlinx.datetime)

    api(libs.imsdk.plus)
    api(libs.timquic.plugin)
    api(libs.timui.core)
    api(libs.timpush)
    api(libs.timpush.fcm)

    implementation("cn.thinkingdata.android:ThinkingAnalyticsSDK:3.0.2")
    implementation("io.github.dnspod:httpdns-sdk:4.10.1")

}

