<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- 录音权限，语音消息使用 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission
        android:name="android.permission.CAMERA"
        tools:ignore="PermissionImpliesUnsupportedChromeOsHardware" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
    <uses-permission
        android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION"
        tools:node="remove" />

    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />
    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />

    <queries>
        <package android:name="com.tencent.mm" />
    </queries>

    <application
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/Theme.UCOO"
        tools:targetApi="34">
        <activity
            android:name=".moment.detail.MomentDetailActivity"
            android:exported="false" />
        <activity
            android:name=".moment.topic.TopicActivity"
            android:exported="false" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                tools:node="remove" />
        </provider>

        <receiver android:name=".im.compat.IMNotificationHelper$ReplyReceiver" />
        <receiver android:name=".im.compat.IMNotificationHelper$NotificationDismissReceiver" />

        <activity
            android:name=".wxapi.WXPayEntryActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:taskAffinity="com.qyqy.uco"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name=".mine.dressup.MineDressUpActivity"
            android:exported="false" />
        <activity
            android:name=".home.search.SearchActivity"
            android:exported="false"
            android:windowSoftInputMode="stateVisible" />

        <service
            android:name=".VoiceLiveService"
            android:foregroundServiceType="microphone|mediaPlayback" />
        <service
            android:name="com.ishumei.smantifraud.IServiceImp"
            android:isolatedProcess="true" />

        <activity
            android:name=".setting.CharmActivity"
            android:exported="false" />
        <activity
            android:name=".tribe.setting.ModifyTribeNameActivity"
            android:exported="false" />
        <activity
            android:name=".tribe.JoinTribeApplyActivity"
            android:exported="false" />
        <activity
            android:name=".tribe.SelectTribeMemberActivity"
            android:exported="false" />
        <activity
            android:name=".tribe.TribeInfoActivity"
            android:exported="false" />
        <activity
            android:name=".tribe.TribeDetailActivity"
            android:exported="false" />
        <activity
            android:name=".tribe.TribeSquareActivity"
            android:exported="false" />
        <activity
            android:name=".tribe.TribeKickOutMemberActivity"
            android:exported="false" />
        <activity
            android:name=".tribe.TribeDepLeaderSetUpActivity"
            android:exported="false" />
        <activity
            android:name=".tribe.setting.TribeSettingActivity"
            android:exported="false"
            android:theme="@style/Theme.UCOO.2F2F2F" />
        <activity
            android:name=".moment.location.SelectLocationActivity"
            android:exported="false" />
        <activity
            android:name=".moment.publish.PublishMomentActivity"
            android:exported="true"
            android:windowSoftInputMode="stateVisible">
            <intent-filter>
                <action android:name="android.intent.action.SEND" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="image/*" />
            </intent-filter>
        </activity>

        <meta-data
            android:name="design_width_in_dp"
            android:value="375" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="812" /> <!-- facebook -->
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token" />

        <activity
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation" />
        <activity
            android:name="com.facebook.CustomTabActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="@string/fb_login_protocol_scheme" />
            </intent-filter>
        </activity> <!-- facebook -->
        <activity
            android:name=".home.WelcomeActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.UCOO.Start">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".home.HomeActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.UCOO.Transparent">
            <intent-filter>
                <action android:name="com.qyqy.ucoo.home.HomeActivity" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="ucoo" />
            </intent-filter>
        </activity>
        <activity
            android:name=".home.AssistantTaskActivity"
            android:launchMode="singleTop"
            android:theme="@style/MyTransparentTheme" />
        <activity
            android:name=".home.DeepLinkActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/MyTransparentTheme">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="ucooadjust" />
            </intent-filter>
        </activity>
        <activity
            android:name=".user.RechargeActivity"
            android:exported="false" />
        <activity
            android:name=".user.BillingRecordListActivity"
            android:exported="false" />
        <activity
            android:name=".home.WebActivity"
            android:exported="false" />
        <activity
            android:name=".utils.web.JsBridgeWebActivity"
            android:exported="false" />
        <activity
            android:name=".mine.EditRegionActivity"
            android:exported="false" />
        <activity
            android:name=".mine.EditAttractionActivity"
            android:exported="false" />
        <activity
            android:name=".mine.EditContentActivity"
            android:exported="false"
            android:theme="@style/Theme.UCOO.Variant" />
        <activity
            android:name=".mine.EditUserInfoActivity"
            android:exported="false" />
        <activity
            android:name=".mine.EditAlbumActivity"
            android:exported="false"
            android:theme="@style/Theme.UCOO.Variant" />
        <activity
            android:name=".mine.CropImageActivity"
            android:exported="false" />
        <activity
            android:name=".mine.FriendsActivity"
            android:exported="false" />
        <activity
            android:name=".setting.SettingActivity"
            android:exported="false"
            android:theme="@style/Theme.UCOO.Variant" />
        <activity
            android:name=".setting.AboutActivity"
            android:exported="false"
            android:theme="@style/Theme.UCOO.Variant" />
        <activity
            android:name=".setting.UserBlackListActivity"
            android:exported="false"
            android:theme="@style/Theme.UCOO.Variant" />
        <activity
            android:name=".setting.FeedbackActivity"
            android:exported="false"
            android:theme="@style/Theme.UCOO.Variant" />
        <activity
            android:name=".setting.ReportActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".compose.presentation.login.LoginActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".user.RegionListActivity"
            android:exported="false" />
        <activity
            android:name=".im.chat.ChatActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
        <activity
            android:name=".im.room.ChatRoomActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.UCOO"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
        <activity
            android:name=".im.room.MatchCpRoomActivity"
            android:exported="false"
            android:theme="@style/Theme.UCOO.Transparent" />
        <activity
            android:name=".im.match.MatchUserCallActivity"
            android:exported="false"
            android:theme="@style/Theme.UCOO.Transparent" />
        <activity
            android:name=".im.match.MatchUserAnswerActivity"
            android:exported="false"
            android:theme="@style/Theme.UCOO.Transparent" />

        <provider
            android:name=".component.MyFileProvider"
            android:authorities="${applicationId}.my_fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/my_fileprovider" />
        </provider>

        <activity
            android:name=".tribe.main.TribeActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
        <activity
            android:name=".im.chat.share.ShareMessageActivity"
            android:exported="false" />
        <activity
            android:name=".tribe.setting.ModifyTribeBuiltActivity"
            android:exported="false" />
        <activity
            android:name=".common.CommonTextEditActivity"
            android:exported="false" />
        <activity
            android:name=".setting.DebugActivity"
            android:exported="false"
            android:theme="@style/Theme.MaterialComponents">
            <intent-filter>
                <action android:name="com.qyqy.ucoo.setting.DebugActivity" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity android:name=".user.voice.VoiceCallActivity" />
        <activity android:name=".user.voice.VoiceCallIncomingActivity" />
        <activity android:name=".moment.MomentSquareActivity" />
        <activity android:name=".setting.account_bind.AccountBindActivity" />
        <activity android:name=".feat.mall.SilverMallActivity" />
        <activity android:name=".component.MultiTabPageActivity" />
        <activity android:name=".component.ListFragmentActivity" />
        <activity android:name=".mine.MineLevelActivity" />
        <activity android:name=".compose.presentation.ff.InviteFamilyActivity" />
        <activity android:name=".compose.presentation.ff.FamilySettingActivity" />
        <activity android:name=".compose.presentation.ComposeScreen" />
        <activity android:name=".feat.anonymous.AnonymousQuickChatMatchActivity" />
        <activity android:name=".feat.audio.VoiceGenerateActivity" />
        <activity android:name=".tribe.main.TribeRankActivity" />
        <activity
            android:name="com.qyqy.cupid.ui.CupidMainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Cupid"
            android:windowSoftInputMode="adjustResize" />

        <meta-data
            android:name="firebase_analytics_collection_enabled"
            android:value="${firebase_analytics_enable}"
            tools:replace="android:value" />
    </application>

</manifest>