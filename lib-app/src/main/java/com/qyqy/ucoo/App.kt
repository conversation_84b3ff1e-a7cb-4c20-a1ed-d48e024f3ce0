package com.qyqy.ucoo

import android.app.Activity
import android.app.Application
import android.content.ComponentCallbacks2
import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import android.util.Log
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.content.res.ResourcesCompat
import com.adjust.sdk.Adjust
import com.adjust.sdk.AdjustConfig
import com.adjust.sdk.AdjustEvent
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.analytics.ktx.logEvent
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.crashlytics.ktx.setCustomKeys
import com.google.firebase.ktx.Firebase
import com.hjq.language.MultiLanguages
import com.lzf.easyfloat.EasyFloat
import com.overseas.common.utils.EmptyFastKVLogger
import com.overseas.common.utils.FastKVLogger
import com.overseas.common.utils.IOExecutor
import com.overseas.common.utils.ProcessUtil
import com.overseas.common.utils.initUtils
import com.overseas.common.utils.isPreviewOnCompose
import com.qyqy.cupid.config.offline.OfflinePkgManager
import com.qyqy.cupid.ui.dialog.DialogQueue
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.ucoo.account.AppAccountManager
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.isInvalid
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.core.upgrade.UpgradeManager
import com.qyqy.ucoo.glide.GlideImageLoader
import com.qyqy.ucoo.glide.ninepatch.NinePathLoader
import com.qyqy.ucoo.im.IMManager
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.IMCompatListener
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.multilingual.AppLanguage
import com.qyqy.ucoo.user.video.feed.MediaPlayerCacheManager
import com.qyqy.ucoo.user.voice.VoiceCallHelper
import com.qyqy.ucoo.utils.CrashReporter
import com.qyqy.ucoo.utils.EmptyActivityLifecycleCallbacks
import com.qyqy.ucoo.utils.GoogleBillingHelper
import com.qyqy.ucoo.utils.LogUtil
import com.qyqy.ucoo.utils.LogUtils
import com.qyqy.ucoo.utils.NetworkManager
import com.qyqy.ucoo.utils.ShushuUtils
import com.qyqy.ucoo.utils.TaskHelper
import com.qyqy.ucoo.utils.ToastUtils
import com.qyqy.ucoo.utils.UCOOTribeManager
import com.qyqy.ucoo.utils.statistic.OnReportCallback
import com.qyqy.ucoo.utils.statistic.ReportObj
import com.qyqy.ucoo.utils.statistic.StatisticReporter
import com.qyqy.ucoo.utils.statistic.StatisticTracker
import com.yy.yyeva.util.ELog
import com.yy.yyeva.util.IELog
import io.fastkv.FastKVConfig
import io.github.album.AlbumStyle
import io.github.album.EasyAlbum
import io.github.album.interfaces.AlbumLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.asExecutor
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import me.jessyan.autosize.AutoSizeConfig
import ren.yale.android.cachewebviewlib.WebViewCacheInterceptor
import ren.yale.android.cachewebviewlib.WebViewCacheInterceptorInst
import java.io.File
import java.util.Locale
import java.util.concurrent.TimeUnit


private lateinit var _app: OpenApp

private var fromPreview = false

val MI_SANS_TYPEFACE by lazy {
    ResourcesCompat.getFont(app, R.font.mi_sans)
}

val D_DIN_TYPEFACE by lazy {
    ResourcesCompat.getFont(app, R.font.din_bold)
}

val app: OpenApp
    get() = if (isPreviewOnCompose && ::_app.isInitialized.not()) {
        fromPreview = true
        _app = object : OpenApp() {

        }
        _app
    } else {
        _app
    }

var sProcessName: String? = null
    private set

var sIsMainProgress: Boolean = false
    private set

val Locale.lanCode: String
    get() = toLanguageTag()

val Locale.lanCodeTag: String
    get() = "${language}-${country}"

val Locale.isChinese: Boolean
    get() {
        val tag = toLanguageTag()
        return ((tag.contains("zh") || tag.contains("Hans") || tag.contains("Hant"))
                && (tag.contains("CN") || tag.contains("HK") || tag.contains("MO") || tag.contains("TW")))
    }

val Locale.isTraditionalChinese: Boolean
    get() {
        return if (isChinese) {
            val tag = toLanguageTag()
            tag.contains("Hant") || tag.contains("HK") || tag.contains("TW")
        } else {
            false
        }
    }

val Locale.isSimplifiedChinese: Boolean
    get() {
        return if (isChinese) {
            !isTraditionalChinese
        } else {
            false
        }
    }

val isTraditionalChinese: Boolean
    get() = MultiLanguages.getAppLanguage().isTraditionalChinese

val accountManager: AppAccountManager
    get() = app.accountManager

abstract class OpenApp : Application() {

    lateinit var dialogQueue: DialogQueue<IDialogAction>
    lateinit var accountManager: AppAccountManager

    private var lazyInInitialed = false

    init {
        _app = this
        if (!fromPreview) {
            isPreviewOnCompose = false
        } else {
            accountManager = AppAccountManager(true).init()
            dialogQueue = DialogQueue()
        }
    }

    override fun attachBaseContext(base: Context) {
        if (!::_app.isInitialized) {
            _app = this
        }
        sProcessName = ProcessUtil.getProcessName(base)
        sIsMainProgress = sProcessName == BuildConfig.APPLICATION_ID
        if (sIsMainProgress) {
            try {
                super.attachBaseContext(AppLanguage.attachApplication(base))
            } catch (e: Exception) {
                super.attachBaseContext(base)
            }
        } else {
            super.attachBaseContext(base)
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        if (sIsMainProgress) {
            AppLanguage.onConfigurationChanged(newConfig)
        }
        super.onConfigurationChanged(newConfig)
    }

    override fun getResources(): Resources {
        return if (sIsMainProgress) {
            AppLanguage.getResources() ?: super.getResources()
        } else {
            super.getResources()
        }
    }

    override fun getExternalFilesDir(type: String?): File? {
        val file = super.getExternalFilesDir(type)
        return file ?: filesDir?.let { if (type == null) null else File(it, type) }
    }

    override fun onCreate() {
        super.onCreate()
        // 默认夜间模式
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
        initAllProcess()
        if (sIsMainProgress) {
            initMainProcess()
        } else {
            initOtherProcess(sProcessName)
        }
    }

    override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)
        if (!sIsMainProgress) {
            return
        }
        // Determine which lifecycle or system event is raised.
        when (level) {
            ComponentCallbacks2.TRIM_MEMORY_UI_HIDDEN -> {
                /*
                   Release any UI objects that currently hold memory.

                   The user interface moves to the background.
                */
            }

            ComponentCallbacks2.TRIM_MEMORY_RUNNING_MODERATE,
            ComponentCallbacks2.TRIM_MEMORY_RUNNING_LOW,
            ComponentCallbacks2.TRIM_MEMORY_RUNNING_CRITICAL,
                -> {
                /*
                   Release any memory your app doesn't need to run.

                   The device is running low on memory while the app is running.
                   The event raised indicates the severity of the memory-related event.
                   If the event is TRIM_MEMORY_RUNNING_CRITICAL, then the system
                   begins stopping background processes.
                */
                WebViewCacheInterceptorInst.getInstance().clearCache()
            }

            ComponentCallbacks2.TRIM_MEMORY_BACKGROUND,
            ComponentCallbacks2.TRIM_MEMORY_MODERATE,
            ComponentCallbacks2.TRIM_MEMORY_COMPLETE,
                -> {
                /*
                   Release as much memory as the process can.

                   The app is on the LRU list and the system is running low on memory.
                   The event raised indicates where the app sits within the LRU list.
                   If the event is TRIM_MEMORY_COMPLETE, the process is one of the
                   first to be terminated.
                */
            }

            else -> {
                /*
                  Release any non-critical data structures.

                  The app receives an unrecognized memory level value
                  from the system. Treat this as a generic low-memory message.
                */
            }
        }
    }

    override fun onLowMemory() {
        super.onLowMemory()
        WebViewCacheInterceptorInst.getInstance().clearCache()
    }

    private fun initAllProcess() {
        LogUtil.initLogger(this)
        //后续当本地发生warn或error导致程序没有达到预期时,上报firebase
        LogUtils.addCallback { priority, tag, _, args ->
            if (BuildConfig.DEBUG) {
                return@addCallback
            }
            if (priority == Log.WARN) {
                val message = args.contentToString()
                Firebase.analytics.logEvent("warn-log") {
                    param("type", "warn")
                    param("detial", message)
                }
            } else if (priority == Log.ERROR) {
                val message = args.contentToString()
                Firebase.analytics.logEvent("error-log") {
                    param("type", "error")
                    param("detial", message)
                }
            }
            if (tag.startsWith("event")) {
                val message = args.contentToString()
                Firebase.crashlytics.log(message);
            }
        }
        LogUtils.logPriority = if (isRelease) Log.ERROR else Log.VERBOSE
        ToastUtils.init(this)
        ELog.isDebug = BuildConfig.DEBUG
        ELog.log = object : IELog {

            override fun i(tag: String, msg: String) {
                LogUtils.i(tag, msg)
            }

            override fun d(tag: String, msg: String) {
                LogUtils.d(tag, msg)
            }

            override fun e(tag: String, msg: String) {
                LogUtils.i(tag, msg)
            }

            override fun e(tag: String, msg: String, tr: Throwable) {
                LogUtils.i(tag, msg, tr)
            }
        }
    }

    private fun initMainProcess() {
        FastKVConfig.setLogger(if (isRelease) EmptyFastKVLogger else FastKVLogger)
        FastKVConfig.setExecutor(Dispatchers.Default.asExecutor())
        UCOOEnvironment.init()
        Firebase.crashlytics.setCustomKey("flavor_channel", BuildConfig.FLAVOR_CHANNEL)
        Firebase.crashlytics.setCustomKey("flavor_pay", BuildConfig.FLAVOR_PAY)
//        val dnsConfigBuilder = DnsConfig.Builder()
//            //（必填）dns 解析 id，即授权 id，腾讯云官网（https://console.cloud.tencent.com/httpdns）申请获得，用于域名解析鉴权
//            .dnsId("1053")
//            //（必填）dns 解析 key，即授权 id 对应的密钥，在开发配置（https://console.cloud.tencent.com/httpdns/configure）中获得，用于域名解析鉴权
//            .dnsKey("TmDB9YgP")
//            //（可选）channel配置：基于 HTTP 请求的 DES 加密形式，默认为 desHttp()，另有 aesHttp()、https() 可选。（注意仅当选择 https 的 channel 需要选择 ************ 的dnsip并传入token，例如：.dnsIp('************').https().token('....') ）。
//            .desHttp()
//            //（可选）日志粒度，如开启Debug打印则传入"Log.VERBOSE"
//            .logLevel(if (BuildConfig.DEBUG) Log.VERBOSE else Log.ASSERT)
//            //（可选）预解析域名，请填写完整域名，填写形式："www.baidu.com", "www.qq.com"，建议不要设置太多预解析域名，当前限制为最多 8 个域名。仅在初始化时触发。
//            .preLookupDomains(AppInfo.apiHost)
//            //（可选）解析缓存自动刷新，请填写完整域名，填写形式："www.baidu.com", "www.qq.com"。配置的域名会在 TTL * 75% 时自动发起解析请求更新缓存，实现配置域名解析时始终命中缓存。此项建议不要设置太多域名，当前限制为最多 8 个域名。与预解析分开独立配置。
//            .persistentCacheDomains(AppInfo.apiHost)
//            //（可选）手动指定网络栈支持情况，仅进行 IPv4 解析传 1，仅进行 IPv6 解析传 2，进行 IPv4、IPv6 双栈解析传 3。默认为根据客户端本地网络栈支持情况发起对应的解析请求。
//            .setCustomNetStack(3)
//            //（可选）设置是否允许使用过期缓存，默认false，解析时先取未过期的缓存结果，不满足则等待解析请求完成后返回解析结果。
//            // 设置为true时，会直接返回缓存的解析结果，没有缓存则返回0;0，用户可使用localdns（InetAddress）进行兜底。且在无缓存结果或缓存已过期时，会异步发起解析请求更新缓存。因异步API（getAddrByNameAsync，getAddrsByNameAsync）逻辑在回调中始终返回未过期的解析结果，设置为true时，异步API不可使用。建议使用同步API （getAddrByName，getAddrsByName）。
//            .setUseExpiredIpEnable(false)
//            //（可选）设置是否启用本地缓存（Room），默认false
//            .setCachedIpEnable(false)
//            //（可选）设置域名解析请求超时时间，默认为2000ms
//            .timeoutMills(2000)
//            .build()
//
//        MSDKDnsResolver.getInstance().init(this, dnsConfigBuilder)
        NetworkManager.init(this)
        initUtils(this)
        AutoSizeConfig.getInstance().setLog(false)
        AutoSizeConfig.getInstance().isCustomFragment = true
        AppLanguage.init(this)
        accountManager = AppAccountManager().init()
        AppUserPartition.init()
        AppInfo.initSmAntiFraud()
        IMManager.init(this)
        registerActivityLifecycleCallbacks(ActivityLifecycle)
        UpgradeManager.init()
        initAdjust()
        initFirebase()
        initAlbum()
        EasyFloat.init(this)
        appActivityTaskScheduler.startSchedule(false)
        registerGlobalLoginEvent()
        delayInitialize()
        TaskHelper.registerEveryDaySayHiTask()
        OfflinePkgManager.startCheck()
        ShushuUtils.init(this)
        if (BuildConfig.DEBUG) {

            CrashReporter.init(this)
        }
    }

    fun lazyInitialForUCOO() {
        if (!lazyInInitialed) {
            lazyInInitialed = true

            initTracker()
            UCOOTribeManager.init()
            TaskHelper.registerOnlineDurationTask()
            VoiceCallHelper.registerVoiceCall()
            MediaPlayerCacheManager.init()
        }
    }


    private fun initFirebase() {
        appCoroutineScope.launch {
            sUserFlow.filter {
                !it.isInvalid()
            }.collectLatest {
                Firebase.crashlytics.setUserId(it.publicId)
                Firebase.crashlytics.setCustomKeys {
                    key("user_info", it.toString())
                }
                val analytics = Firebase.analytics
                analytics.setUserId(it.publicId)
                analytics.setUserProperty("age", it.age.toString())
                analytics.setUserProperty("gender", it.gender.toString())
                if (it is AppUser) {
                    analytics.setUserProperty("ip", it.ip)
                    analytics.setUserProperty("register_timestamps", it.registerTimeStamp.toString())
                }
            }
        }
    }

    private fun initTracker() {
        //埋点
        val trackEnable = isRelease
        val tracker = object : StatisticTracker {
            override fun track(eventName: String, extras: List<Pair<String, Any>>) {
                LogUtil.d("$eventName $extras", "app_tracker")
                if (trackEnable.not()) {
                    return
                }
                if (extras.isEmpty()) {
                    Firebase.analytics.logEvent(eventName, null)
                } else {
                    Firebase.analytics.logEvent(eventName) {
                        extras.forEach {
                            param(it.first, it.second.toString())
                        }
                    }
                }
            }
        }
        StatisticReporter.init(this, tracker, object : OnReportCallback {
            override fun onEnterReportObj(tracker: StatisticTracker, obj: ReportObj) {
                val reportExtras = obj.getReportExtras()
                reportExtras.run {
                    add("action" to "enter")
                }
                tracker.track(obj.reportKey, reportExtras)
            }

            override fun onExitReportObj(tracker: StatisticTracker, obj: ReportObj, durationStay: Long) {
                val reportExtras = obj.getReportExtras()
                reportExtras.run {
                    add("action" to "exit")
                    if (obj.isReportStayDuration()) {
                        add("durationStay" to TimeUnit.NANOSECONDS.toSeconds(durationStay))
                    }
                }
                tracker.track(obj.reportKey, reportExtras)
            }
        })
    }

    private fun initOtherProcess(name: String?) {

    }


    private fun initAlbum() {
        EasyAlbum.init(this)
        EasyAlbum.config()
            .setStyle(AlbumStyle())
            .setLogger(object : AlbumLogger {
                override fun d(tag: String?, message: String?) {
                    LogUtils.d(tag ?: "EasyAlbum", message)
                }

                override fun e(tag: String?, t: Throwable?) {
                    LogUtils.e(tag ?: "EasyAlbum", t)
                }

            })
            .setExecutor(IOExecutor)
            .setImageLoader(GlideImageLoader)
            .setDefaultFolderComparator { o1, o2 -> o1.name.compareTo(o2.name) }
    }

    private fun delayInitialize() {
        appCoroutineScope.launch {
            delay(500)

            launch(Dispatchers.IO) {
                delay(500)
                UCOOTribeManager.init()
                // 初始化google支付
                GoogleBillingHelper.initGoogleBilling()
                WebViewCacheInterceptorInst.getInstance().init(WebViewCacheInterceptor.Builder(app))
            }

            launch {
                // 初始化优质主播任务
                UIConfig.init()
            }

            NinePathLoader.preloadBubbleBitmap()
        }
    }

    private fun initAdjust() {
        //adjust
        val environment = if (isRelease) AdjustConfig.ENVIRONMENT_PRODUCTION else AdjustConfig.ENVIRONMENT_SANDBOX
        val config = AdjustConfig(this, BuildConfig.ADJUST_APP_TOKEN, environment).apply {
            if (isRelease) setLogLevel(com.adjust.sdk.LogLevel.SUPRESS) else setLogLevel(com.adjust.sdk.LogLevel.VERBOSE)
            setOnAttributionChangedListener {
                AppInfo.bindAdjustID(it.adid)
            }
        }
        Adjust.onCreate(config)
        Adjust.getGoogleAdId(app) {
            Firebase.analytics.setUserProperty("gaid", it.orEmpty())
        }
        registerActivityLifecycleCallbacks(object : EmptyActivityLifecycleCallbacks() {
            override fun onActivityResumed(activity: Activity) {
                Adjust.onResume()
            }

            override fun onActivityPaused(activity: Activity) {
                Adjust.onPause()
            }
        })

        Adjust.trackEvent(AdjustEvent("icb5ng"))
        Adjust.trackEvent(AdjustEvent("uhupjr"))

        IMCompatCore.addIMListener(object : IMCompatListener {

            override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
                if (message.cmd == MsgEventCmd.REPORT_ADJUST_EVENTS) {
                    val dataPack = message.parseDataJson<AdjustDataPack>() ?: return
                    Adjust.trackEvent(AdjustEvent(dataPack.token).apply {
                        revenue = dataPack.revenue?.toDoubleOrNull()
                        currency = dataPack.currency
                        callbackParameters = dataPack.callbackParams
                        partnerParameters = dataPack.partnerParams
                    })
                }
            }
        })
    }
}

@Serializable
private data class AdjustDataPack(
    @SerialName("event_token") val token: String,
    @SerialName("revenue") val revenue: String?,
    @SerialName("currency") val currency: String?,
    @SerialName("callback_params") val callbackParams: MutableMap<String, String>?,
    @SerialName("partner_params") val partnerParams: MutableMap<String, String>?,
)


