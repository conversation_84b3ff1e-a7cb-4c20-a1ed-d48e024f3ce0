package com.qyqy.ucoo.base

import androidx.activity.ComponentActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

interface UiState

object IdleState : UiState
data class UiStateData<S : IState>(val state: S) : UiState
data class ResultUiState<T>(val result: Result<T>) : UiState
sealed class LoadingState : IState, UiState {
    object Idle : LoadingState(), IIdle
    object Loading : LoadingState(), ILoading
}

sealed class RefreshState : IState {
    object Idle : RefreshState(), IIdle
    object Refreshing : RefreshState(), ILoading
}

sealed class LoadMoreState : IState {
    object Idle : LoadMoreState(), IIdle
    object Loading : LoadMoreState(), ILoading

    /**
     * 没有更多了
     */
    object Done : LoadMoreState(), IDone
}

sealed class DataState<DATA> : IValueState<DATA> {

    class Idle<DATA> : DataState<DATA>(), IIdle

    data class Success<DATA>(override val data: DATA) : DataState<DATA>(), IValue<DATA> {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as Success<*>

            return data == other.data
        }

        override fun hashCode(): Int {
            return data?.hashCode() ?: 0
        }
    }
}

fun <T> T.successDataState():DataState<T> = DataState.Success(this)

context (ComponentActivity)
        inline fun <UiState, R> StateFlow<UiState>.collectValue(
    crossinline transform: suspend (value: UiState) -> IValueState<R>,
    noinline action: suspend Flow<R>.() -> Unit
) {
    lifecycleScope.launch {
        <EMAIL> {
            transform(it)
        }.filter {
            it.isSuccess
        }.map {
            it.get()
        }.flowWithLifecycle(lifecycle)
            .action()
    }
}

context (Fragment)
        inline fun <UiState, R> StateFlow<UiState>.collectValue(
    crossinline transform: suspend (value: UiState) -> IValueState<R>,
    noinline action: suspend Flow<R>.() -> Unit
) {
    lifecycleScope.launch {
        <EMAIL> {
            transform(it)
        }.filter {
            it.isSuccess
        }.map {
            it.get()
        }.flowWithLifecycle(viewLifecycleOwner.lifecycle)
            .action()
    }
}

context (ComponentActivity)
        inline fun <UiState, R : IState> StateFlow<UiState>.collectLoading(
    crossinline transform: suspend (value: UiState) -> R,
    autoMode: Boolean = true,
    noinline action: suspend Flow<R>.() -> Unit
) {
    val activity = this@ComponentActivity as? BaseActivity
    lifecycleScope.launch {
        <EMAIL> {
            transform(it)
        }.flowWithLifecycle(lifecycle)
            .onEach {
                if (autoMode && activity != null && it is LoadingState) {
                    activity.toggleLoading(it.isLoading)
                }
            }
            .action()
    }
}

context (Fragment)
        inline fun <UiState, R : IState> StateFlow<UiState>.collectLoading(
    crossinline transform: suspend (value: UiState) -> R,
    autoMode: Boolean = true,
    noinline action: suspend Flow<R>.() -> Unit
) {
    val fragment = this@Fragment as? BaseFragment
    lifecycleScope.launch {
        <EMAIL> {
            transform(it)
        }.flowWithLifecycle(viewLifecycleOwner.lifecycle)
            .onEach {
                if (autoMode && fragment != null && it is LoadingState) {
                    fragment.toggleLoading(it.isLoading)
                }
            }
            .action()
    }
}
