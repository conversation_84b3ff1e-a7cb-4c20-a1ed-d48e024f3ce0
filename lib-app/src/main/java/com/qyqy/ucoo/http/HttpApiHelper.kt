package com.qyqy.ucoo.http

import android.util.Log
import com.google.gson.Gson
import com.jakewharton.retrofit2.converter.kotlinx.serialization.asConverterFactory
import com.overseas.common.utils.id2String
import com.overseas.common.utils.isPreviewOnCompose
import com.overseas.common.utils.postToMainThread
import com.qyqy.ucoo.ActivityLifecycle
import com.qyqy.ucoo.AppInfo
import com.qyqy.ucoo.AppUserPartition
import com.qyqy.ucoo.BuildConfig
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.asBase
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.presentation.member.MemberCenterScreenNavigator
import com.qyqy.ucoo.compose.ui.ThemeAlertDialog
import com.qyqy.ucoo.compose.ui.dialogs.showComposeDialog
import com.qyqy.ucoo.isProd
import com.qyqy.ucoo.isRelease
import com.qyqy.ucoo.mine.AlertStyleDialogFragment
import com.qyqy.ucoo.runWithTopActivity
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.user.PrivateRoomExperienceCouponDialogFragment
import com.qyqy.ucoo.user.RechargeDialogFragment
import com.qyqy.ucoo.utils.LogUtils
import com.qyqy.ucoo.utils.taskflow.globalTaskShowDialogFragment
import com.qyqy.ucoo.widget.dialog.AddFriendsDialogFragment
import com.qyqy.ucoo.widget.dialog.AddFriendsForPrivateDialogFragment
import com.qyqy.ucoo.widget.dialog.FirstClassGiftTipsWidget
import com.qyqy.ucoo.widget.orElse
import kotlinx.serialization.ContextualSerializer
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.InternalSerializationApi
import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.booleanOrNull
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.floatOrNull
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import kotlinx.serialization.json.longOrNull
import kotlinx.serialization.modules.serializersModuleOf
import kotlinx.serialization.serializer
import okhttp3.ConnectionPool
import okhttp3.Dispatcher
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import java.lang.reflect.InvocationHandler
import java.lang.reflect.Method
import java.lang.reflect.Proxy
import java.util.concurrent.CancellationException


val contentType = "application/json".toMediaType()

const val TOKEN_NAME = "Access-Token"

open class ApiException constructor(
    val code: Int = 0,
    val msg: String,
    val extra: JsonObject? = null,
    val errorMsg: String? = null,
) :
    RuntimeException(msg) {
    override fun toString(): String {
        return "code: $code, ${super.toString()}"
    }
}

class ReSignInException : ApiException(
    ApiCode.ACCESS_TOKEN_MISS, if (AppUserPartition.isUCOO) {
        id2String(R.string.please_log_in_again)
    } else {
        id2String(R.string.cpd_please_log_in_again)
    }
)

class MissAccessTokenException : ApiException(
    ApiCode.ACCESS_TOKEN_MISS, if (AppUserPartition.isUCOO) {
        id2String(R.string.please_log_in_again)
    } else {
        id2String(R.string.cpd_please_log_in_again)
    }
)

object ApiCode {
    const val STATUS_SUCCESS_BUT_DATA_IS_NULL = 700
    const val STATUS_SUCCESS = 0
    const val ACCESS_TOKEN_MISS = -2
    const val ACCESS_TOKEN_EXPIRED = -3
    const val REFRESH_TOKEN_EXPIRED = -4
}

fun JsonObject.getOrNull(key: String): JsonElement? = get(key)?.takeIf {
    it !is JsonNull
}

fun JsonObject.getStringOrNull(key: String): String? = getOrNull(key)?.jsonPrimitive?.contentOrNull

fun JsonObject.getBoolOrNull(key: String): Boolean? = getOrNull(key)?.jsonPrimitive?.booleanOrNull

fun JsonObject.getIntOrNull(key: String): Int? = getOrNull(key)?.jsonPrimitive?.intOrNull

fun JsonObject.getLongOrNull(key: String): Long? = getOrNull(key)?.jsonPrimitive?.longOrNull

fun JsonObject.getFloatOrNull(key: String): Float? = getOrNull(key)?.jsonPrimitive?.floatOrNull

inline fun <reified T> JsonObject.parseValue(key: String): T? {
    return try {
        getOrNull(key)?.let { sAppJson.decodeFromJsonElement<T>(it) }
    } catch (e: Exception) {
        if (!(isProd && isRelease)) {
            throw e
        }
        null
    }
}

inline fun <reified T> JsonObject.parseValue(key: String, default: T): T {
    return parseValue(key) ?: default
}

@OptIn(ExperimentalSerializationApi::class)
val sAppJson = Json {
    ignoreUnknownKeys = true
    isLenient = true
    explicitNulls = false
    coerceInputValues = true
    serializersModule = serializersModuleOf(DynamicLookupSerializer)
}

val gson by lazy {
    Gson()
}

@OptIn(ExperimentalSerializationApi::class)
internal object DynamicLookupSerializer : KSerializer<Any> {

    override val descriptor: SerialDescriptor =
        ContextualSerializer(Any::class, null, emptyArray()).descriptor

    @Suppress("UNCHECKED_CAST")
    @OptIn(InternalSerializationApi::class)
    override fun serialize(encoder: Encoder, value: Any) {
        val actualSerializer =
            encoder.serializersModule.getContextual(value::class) ?: value::class.serializer()
        encoder.encodeSerializableValue(actualSerializer as KSerializer<Any>, value)
    }

    override fun deserialize(decoder: Decoder): Any {
        error("Unsupported")
    }
}


private val sharePool: ConnectionPool = ConnectionPool()

private val shareDispatcher: Dispatcher = Dispatcher()
fun createShareOkHttpClientBuilder() = OkHttpClient.Builder()
    .connectionPool(sharePool)
    .dispatcher(shareDispatcher)

private val mLogInterceptor by lazy{
    HttpLogInterceptor { msg ->
        LogUtils.printLog(Log.INFO, "HttpApiHelper", false, msg)
    }.apply {
        setLevel(if (!isProd) HttpLogInterceptor.Level.BODY else HttpLogInterceptor.Level.NONE)
    }
}

@OptIn(ExperimentalSerializationApi::class)
private val sRetrofit: Retrofit by lazy {
    val okHttpClient = createShareOkHttpClientBuilder()
//        .dns(object : Dns{
//            override fun lookup(hostname: String): List<InetAddress> {
//                val ips: String = MSDKDnsResolver.getInstance().getAddrByName(hostname)
//                val ipArr = ips.split(";")
//
//                if (ipArr.isEmpty()) {
//                    return emptyList()
//                }
//                return ipArr.mapNotNull {  ip->
//                    if (ip == "0") {
//                        null
//                    } else {
//                        try {
//                            InetAddress.getByName(ip)
//                        } catch (e: UnknownHostException) {
//                            null
//                        }
//                    }
//                }
//            }
//
//        })
        .addInterceptor(TokenInterceptor())
        .addInterceptor(AuthInterceptor(BuildConfig.API_SIGN_ACCESS_KEY, BuildConfig.API_SIGN_SECRET_KEY))
        .addInterceptor(mLogInterceptor)
        .build()

    Retrofit.Builder().baseUrl(AppInfo.apiHost).client(okHttpClient)
        .addConverterFactory(sAppJson.asConverterFactory(contentType)).build()
}

fun <T> createApi(service: Class<T>): T {
    return if (isPreviewOnCompose) {
        createFakeInterface(service)
    } else {
        sRetrofit.create(service)
    }
}

fun <T> createApi(service: Class<T>, retrofit: Retrofit): T {
    return if (isPreviewOnCompose) {
        createFakeInterface(service)
    } else {
        retrofit.create(service)
    }
}

inline fun <reified A> createApi() = if (isPreviewOnCompose) createFakeInterface(A::class.java) else createApi(A::class.java)


@Serializable
data class ApiResponse<T>(
    val status: Int, val msg: String = "", val data: T?, val extra: JsonObject?,
)

val ApiResponse<*>.isSuccessFul: Boolean
    get() = status == ApiCode.STATUS_SUCCESS

val Result<*>.apiException: ApiException?
    get() = exceptionOrNull() as? ApiException

val Throwable.apiException: ApiException?
    get() = this as? ApiException

inline fun <T, reified R> Result<T>.thenApi(transform: (T) -> ApiResponse<R>): Result<R> {
    return fold({
        runApiCatching {
            transform(it)
        }
    }) {
        Result.failure(it)
    }
}

inline fun <reified T> runApiCatching(
    dataIsNonNull: Boolean = true,
    globalErrorHandleIntercepted: (ApiResponse<T>) -> Boolean = { false },
    block: () -> ApiResponse<T>,
): Result<T> {
    return try {
        val response = block()
        parseApiResponse(dataIsNonNull, response, globalErrorHandleIntercepted)
    } catch (e: CancellationException) {
        throw e
    } catch (e: Throwable) {
        LogUtils.w("HttpApiHelper", "ApiError: %s", e)
        Result.failure(e)
    }
}

// 无需要toast的错误码
/**
 * ```
 * -21 账号注销中
 * ```
 */
val listErrorCodeNotShowToast = listOf(-2, -3, -4, -11, -12, -13, -15, -16, -21, -2000)
inline fun <T> parseApiResponse(
    dataIsNonNull: Boolean,
    response: ApiResponse<T>,
    globalErrorHandleIntercepted: (ApiResponse<T>) -> Boolean = { false },
): Result<T> {
    return if (response.isSuccessFul) {
        if (dataIsNonNull && response.data != null) {
            Result.success(response.data)
        } else {
            Result.failure(
                ApiException(
                    ApiCode.STATUS_SUCCESS_BUT_DATA_IS_NULL, "data is null, ${response.msg}"
                )
            )
        }
    } else {
        if (!globalErrorHandleIntercepted(response)) {
            postToMainThread {
                if (AppUserPartition.isUCOO) {
                    handleGlobalApiCode(response)
                } else {
                    handleGlobalApiCodeForCupid(response)
                }
            }
        }
        // 避免msg被toast出来
        Result.failure(
            ApiException(
                response.status,
                if (listErrorCodeNotShowToast.contains(response.status)) "" else response.msg,
                response.extra,
                errorMsg = response.msg
            )
        )
    }
}

fun handleGlobalApiCode(response: ApiResponse<*>) {
    val errorCode = response.status
    ActivityLifecycle.startTopActivity?.asBase?.also {
        when (errorCode) {
            -11 -> { // need charge coin
                toast(response.msg)
                RechargeDialogFragment.newInstance(response.msg)
                    .show(it.supportFragmentManager, "RechargeDialogFragment")
            }

            -12 -> { // need vip
                MemberCenterScreenNavigator.navigate(
                    it,
                    response.msg,
                    response.extra?.getStringOrNull("view_event_name").orEmpty(),
                    response.extra?.getStringOrNull("action_event_name").orEmpty()
                )
            }

            -13 -> { // 需要添加好友, 发消息接口拦截
                response.extra?.takeIf { json ->
                    json.containsKey("action") && json["action"]?.jsonPrimitive?.content == "add_friends_popup"
                }?.get("target_user")?.also { json ->
                    val ab = response.extra["charge_guide_plan"]?.jsonPrimitive?.intOrNull.orElse(0)
                    val hint = response.extra["charge_hint"]?.jsonPrimitive?.content.orEmpty()
                    val user = sAppJson.decodeFromJsonElement<AppUser>(json).copy(
                        privateRoomId = response.extra["private_room_id"]?.jsonPrimitive?.intOrNull.orElse(0)
                    )
                    globalTaskShowDialogFragment(
                        AddFriendsForPrivateDialogFragment::class.java,
                        bundle = AddFriendsForPrivateDialogFragment.newBundle(response.msg, user, ab, hint),
                        tag = "AddFriendsForPrivateDialogFragment"
                    )
                } ?: run {
                    globalTaskShowDialogFragment(
                        AddFriendsDialogFragment::class.java,
                        bundle = AddFriendsDialogFragment.newBundle(response.msg),
                        tag = "AddFriendsDialogFragment"
                    )
                }
            }

            -15 -> { // need silver recharge
                toast(response.msg)
                AlertStyleDialogFragment.newInstance(
                    it.getString(R.string.你的银币余额不足),
                    R.drawable.ic_silver_bg,
                    it.getString(R.string.赠送银币福袋可获得银币),
                    it.getString(R.string.我知道了)
                ).show(it.supportFragmentManager, "need_silver_recharge")
            }

            -16 -> {
                val msg = response.msg
                val btnText = response.extra?.jsonObject?.let { obj -> obj["btn_text"]?.jsonPrimitive?.contentOrNull }.orEmpty()
                ActivityLifecycle.runWithTopActivity {
                    ThemeAlertDialog(this, content = msg, buttonText = btnText) {
                        AppLinkManager.open(this, "${AppLinkManager.BASE_URL}/main?tab=mine&mine_tab=relationship")
                    }.show()
                }
            }

            -100 -> { // need_extra_operation
                val type = response.extra?.get("extra_operation_type")?.jsonPrimitive?.intOrNull.orElse(0)
                if (type == 1) {
                    val user = response.extra?.get("target_user")?.let { element ->
                        sAppJson.decodeFromJsonElement<AppUser>(element).copy(
                            privateRoomId = response.extra["private_room_id"]?.jsonPrimitive?.intOrNull.orElse(0)
                        )
                    } ?: return
                    globalTaskShowDialogFragment(
                        PrivateRoomExperienceCouponDialogFragment::class.java,
                        bundle = PrivateRoomExperienceCouponDialogFragment.newBundle(user),
                        tag = "need_extra_operation"
                    )
                }
            }

            -101 -> {
                //2.42.0 新增通用错误类型JUMP_APPLINK
                response.extra?.let {
                    it.getOrNull("toast")?.jsonPrimitive?.contentOrNull?.also { toast(it) }
                    it.getOrNull("action_link")?.jsonPrimitive?.contentOrNull?.also { link ->
                        runWithTopActivity {
                            AppLinkManager.open(this, link)
                        }
                    }
                }
            }

            -2000 -> {
                showComposeDialog { dialog ->
                    FirstClassGiftTipsWidget() {
                        dialog.dismiss()
                    }
                }
            }
        }
    }
}

interface ApiExceptionHandleListener {

    fun handle(response: ApiResponse<*>)

}

var cupidGlobalApiExceptionHandleListener: ApiExceptionHandleListener? = null

fun handleGlobalApiCodeForCupid(response: ApiResponse<*>) {
    cupidGlobalApiExceptionHandleListener?.handle(response)
}


// 实现 InvocationHandler 接口
class EmptyInvocationHandler : InvocationHandler {
    override fun invoke(proxy: Any?, method: Method?, args: Array<out Any>?): Any {
        return error("EmptyInvocationHandler")
    }
}

fun <T> createFakeInterface(service: Class<T>): T {
    return Proxy.newProxyInstance(
        service.classLoader,
        arrayOf<Class<*>>(service),
        EmptyInvocationHandler()
    ) as T
}
