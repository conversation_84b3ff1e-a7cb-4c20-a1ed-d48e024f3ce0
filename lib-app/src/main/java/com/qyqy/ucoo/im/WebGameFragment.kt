package com.qyqy.ucoo.im

import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.bumptech.glide.Glide
import com.google.gson.JsonObject
import com.overseas.common.ext.safeViewLifecycleScope
import com.overseas.common.ext.viewBinding
import com.overseas.common.utils.dp
import com.qyqy.ucoo.BuildConfig
import com.qyqy.ucoo.R

import com.qyqy.ucoo.base.BaseFragment
import com.qyqy.ucoo.databinding.FragmentWebGameBinding
import com.qyqy.ucoo.utils.GiftEffectHelper
import com.qyqy.ucoo.utils.updateLayoutParams
import com.qyqy.ucoo.utils.web.JsBridgeWebFragment
import com.qyqy.ucoo.utils.web.handler.BaseBridgeHandler
import com.qyqy.ucoo.utils.web.handler.HandlerName
import com.smallbuer.jsbridge.core.CallBackFunction
import com.smallbuer.jsbridge.core.IWebView
import com.yy.yyeva.util.EvaConstant
import kotlinx.coroutines.launch
import kotlin.time.Duration.Companion.seconds

@Deprecated("Use JsBridgeWebFragment")
class WebGameFragment : BaseFragment(R.layout.fragment_web_game) {

    private val binding by viewBinding(FragmentWebGameBinding::bind)

    companion object {
        private const val KEY_URL = "key_url"

//        private const val DEV_GAME_URL = "https://api.test.ucoofun.com/h5/gold_racing_half"
//
//        private const val PROD_GAME_URL = "https://api.ucoofun.com/h5/gold_racing_half"

        val gameUrl: String
//            get() = if (isProd) PROD_GAME_URL else DEV_GAME_URL
            get() = "${BuildConfig.API_HOST}/h5/gold_racing_half"

        fun newInstance(url: String): WebGameFragment {
            val args = Bundle()
            val fragment = WebGameFragment()
            args.putString(KEY_URL, url)
            fragment.arguments = args
            return fragment
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.root.updateLayoutParams<ViewGroup.MarginLayoutParams> {
            height = 660.dp
        }
        binding.evaAnimView.setVideoMode(EvaConstant.VIDEO_MODE_SPLIT_HORIZONTAL)
        childFragmentManager.registerFragmentLifecycleCallbacks(object : FragmentManager.FragmentLifecycleCallbacks() {
            override fun onFragmentViewCreated(
                fm: FragmentManager,
                f: Fragment,
                v: View,
                savedInstanceState: Bundle?,
            ) {
                if (f is JsBridgeWebFragment) {
                    injectJsWebView(f.jsWebView)
                }
            }
        }, false)
    }

    private fun injectJsWebView(webView: IWebView) {

        webView.addHandlerLocal(HandlerName.WEB_VIDEO_EFFECT, object : BaseBridgeHandler() {

            override fun handlerV2(context: Context, data: JsonObject, callback: CallBackFunction) {
                val effectUrl = data.getString("effect_url")?.takeIf { it.isNotEmpty() } ?: return
                safeViewLifecycleScope.launch {
                    val file = GiftEffectHelper.getGiftEffectFile(effectUrl, Glide.with(this@WebGameFragment), 5.seconds)
                    if (file != null) {
                        binding.evaAnimView.stopPlay()
                        binding.evaAnimView.startPlay(file)
                    }
                }
            }
        })
    }
}