package com.qyqy.cupid

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.qyqy.cupid.ui.AppNavController
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.dialog.DialogButton
import com.qyqy.cupid.ui.dialog.DialogQueue
import com.qyqy.cupid.ui.dialog.FirstRechargeWithUserInfoDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.dialog.RichTextAlertDialog
import com.qyqy.cupid.ui.profile.RechargeDialog
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.presentation.room.RichText
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.DirectionState
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.http.ApiExceptionHandleListener
import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.http.getStringOrNull
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.message.entity.RichItem
import com.qyqy.ucoo.widget.orElse
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonPrimitive

class CupidGlobalApiExceptionHandle(
    private val appNavController: AppNavController,
    private val dialogQueue: DialogQueue<IDialogAction>,
) : ApiExceptionHandleListener {

    override fun handle(response: ApiResponse<*>) {
        val errorCode = response.status
        when (errorCode) {
            -11 -> { // need charge coin
//                toast(response.msg)
                dialogQueue.push(RechargeDialog, true)
            }

            -12 -> {
//                toast(response.msg)
                appNavController.navigate(CupidRouters.MEMBERSHIP_ACTIVE_PAGE)
            }

            -22->{//带用户信息的首充弹窗
                dialogQueue.push(FirstRechargeWithUserInfoDialog(response.extra.toString()),true)
            }
            -100 -> { // 通用弹窗
                val type = response.extra?.get("extra_operation_type")?.jsonPrimitive?.intOrNull.orElse(0)
                if (type != 0) {
                    when (type) {
                        10 -> {
                            val extra = response.extra ?: return
                            val title = extra.getStringOrNull("title")
                            val contents: List<RichItem> = buildList {
                                extra["content"]?.jsonArray?.forEach {
                                    add(sAppJson.decodeFromJsonElement<RichItem>(it))
                                }
                            }
                            val btnText = extra.getStringOrNull("btn_text")

                            dialogQueue.push(direction = DirectionState.CENTER) { dialog, onAction ->
                                RichTextAlertDialog(title = title, contents = contents, endButton = if (btnText != null) DialogButton(btnText) {
                                    dialog.dismiss()
                                } else null, modifier = Modifier.fillMaxWidth(0.8f))
                            }
                        }

                        else -> {

                        }
                    }
                }
            }
        }
    }
}
