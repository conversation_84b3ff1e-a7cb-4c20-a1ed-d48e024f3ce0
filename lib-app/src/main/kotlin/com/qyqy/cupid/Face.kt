package com.qyqy.cupid

import android.content.Context
import android.os.Build
import android.os.Bundle
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.orDefault
import com.tencent.cloud.huiyansdkface.facelight.api.WbCloudFaceContant
import com.tencent.cloud.huiyansdkface.facelight.api.WbCloudFaceVerifySdk
import com.tencent.cloud.huiyansdkface.facelight.api.listeners.WbCloudFaceVerifyLoginListener
import com.tencent.cloud.huiyansdkface.facelight.api.result.WbFaceError
import com.tencent.cloud.huiyansdkface.facelight.process.FaceVerifyStatus
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class FaceData(
    @SerialName("api_version")
    val apiVersion: String = "", // 1.0.0
    @SerialName("app_id")
    val appId: String = "", // abc
    @SerialName("face_id")
    val faceId: String = "", // 927389r9238
    val nonce: String = "", // xxxxxxxx
    @SerialName("order_no")
    val orderNo: String = "", // xxxxx
    val sign: String = "", // yyyyyyyyyyy
    val userid: String = "", // 23
)

object FaceKt {

    fun startFaceAuth(context: Context, data: FaceData, onResult: (Boolean) -> Unit) {
        val inputData = WbCloudFaceVerifySdk.InputData(
            data.faceId,
            data.orderNo,
            data.appId,
            data.apiVersion,
            data.nonce,
            data.userid,
            data.sign,
            FaceVerifyStatus.Mode.GRADE,
            context.getString(R.string.face_licence)
        )
        WbCloudFaceVerifySdk.getInstance().apply {
            val bundle = Bundle()
            //先将必填的 InputData 放入 Bundle 中
            bundle.putSerializable(WbCloudFaceContant.INPUT_DATA, inputData)
//            bundle.putSerializable(WbCloudFaceContant.IS_ABROAD, inputData)
            bundle.putString(WbCloudFaceContant.LANGUAGE, WbCloudFaceContant.LANGUAGE_JA)
            initSdk(context, bundle, object : WbCloudFaceVerifyLoginListener {
                override fun onLoginSuccess() {
                    startWbFaceVerifySdk(context) {
                        release()
                        if (it?.liveRate?.toInt().orDefault(0) > 80) {
                            onResult(true)
                        } else {
                            onResult(false)
                        }
                    }
                }

                override fun onLoginFailed(p0: WbFaceError?) {
                    release()
                    onResult(false)
                }
            })
        }
    }

    fun isDeviceX86(): Boolean {
        val supportedAbis = Build.SUPPORTED_ABIS
        return supportedAbis.contains("x86") || supportedAbis.contains("x86_64")
    }
}
