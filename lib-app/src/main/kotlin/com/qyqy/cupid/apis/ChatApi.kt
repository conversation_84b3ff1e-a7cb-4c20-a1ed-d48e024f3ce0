package com.qyqy.cupid.apis

import com.qyqy.cupid.config.ChatListConfigBean
import com.qyqy.cupid.data.intimateUserBean
import com.qyqy.ucoo.http.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.GET

interface ChatApi {
    @GET("api/privatechat/v1/chat/list/config")
    suspend fun getChatListConfig(): ApiResponse<ChatListConfigBean>

    @GET("/api/ucuser/v1/chatlist/intimate_score")
    suspend fun getIntimateScore(): ApiResponse<List<intimateUserBean>>
}