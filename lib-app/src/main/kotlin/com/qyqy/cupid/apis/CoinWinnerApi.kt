package com.qyqy.cupid.apis

import com.qyqy.cupid.data.CoinWinnerConfigBean
import com.qyqy.cupid.data.CoinWinnerDetail
import com.qyqy.ucoo.http.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

/**
 * 金币大玩家接口
 */
interface CoinWinnerApi {
    /**
     * 获取游戏配置
     *
     * @return
     */
    @GET("api/games/japancoinpk/v1/round/settings")
    suspend fun getCoinWinnerConfig(@Query("room_id") room_id: Int): ApiResponse<CoinWinnerConfigBean>

    /**
     * 创建金币大玩家游戏
     *
     * @param room_id 语音房id
     * @param base_coin 入场金额
     * @return
     */
    @POST("api/games/japancoinpk/v1/round/create")
    suspend fun createGame(
//        @Field("room_id") room_id: Int, @Field("base_coin") base_coin: Int
        @Body body: Map<String, @JvmSuppressWildcards Any>
    ): ApiResponse<CoinWinnerDetail>

    /**
     * 获取指定金币游戏的信息
     * room_id round_id 任意传一个就可以
     * 刚进房间查询可以传入 room_id
     * 同步游戏信息 可以传 round_id
     * @param room_id 语音房id
     * @param round_id 游戏id
     * @return
     */
    @GET("api/games/japancoinpk/v1/round/info")
    suspend fun getGameInfo(@Query("room_id") room_id: Int?, @Query("round_id") round_id: Int? = null): ApiResponse<CoinWinnerDetail>

    @POST("api/games/japancoinpk/v1/round/join")
    suspend fun joinGame(
//        @Field("room_id") room_id: Int, @Field("round_id") round_id: String
        @Body body: Map<String, @JvmSuppressWildcards Any>
    ): ApiResponse<CoinWinnerDetail>

    /**
     * 加注金额
     *
     * @param round_id 游戏id
     * @param coin 加注金额
     * @return
     */
    @POST("api/games/japancoinpk/v1/round/raise")
    suspend fun raiseGame(
//        @Field("round_id") round_id: String, @Field("coin") coin: Int
        @Body body: Map<String, @JvmSuppressWildcards Any>
    ): ApiResponse<JsonObject>

    /**
     * 关闭游戏
     *
     * @param round_id 游戏id
     * @return
     */
    @POST("api/games/japancoinpk/v1/round/close")
    suspend fun closeGame(
//        @Field("round_id") round_id: String,
        @Body body: Map<String, @JvmSuppressWildcards Any>
    ): ApiResponse<JsonObject>

    @POST("api/games/japancoinpk/v1/round/start")
    suspend fun startGame(
//        @Field("round_id") round_id: String
        @Body body: Map<String, @JvmSuppressWildcards Any>
    ): ApiResponse<JsonObject>
}