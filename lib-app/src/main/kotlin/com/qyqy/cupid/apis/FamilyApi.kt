package com.qyqy.cupid.apis

import com.qyqy.cupid.data.FamilyConfig
import com.qyqy.cupid.data.FamilyEncourageInfo
import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.tribe.bean.MemberList
import com.qyqy.ucoo.tribe.bean.ResultTribeCreate
import com.qyqy.ucoo.tribe.bean.TribeDepLeaderData
import com.qyqy.ucoo.tribe.bean.TribeInfo
import com.qyqy.ucoo.tribe.bean.TribeList
import com.qyqy.ucoo.tribe.bean.TribeMemberData
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

/**
 *  @time 8/19/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.apis
 */
interface FamilyApi {
    @POST("api/tribe/v1/event/report")
    suspend fun tribeReport(@Body map: Map<String, String>): ApiResponse<JsonObject>

    /**
     *
    参数
    参数	类型	是否必传	默认值
    last_id	整数	False	0
     */
    @GET("api/tribe/v1/tribe/list")
    suspend fun getTribeSquare(@Query("last_id") last_id: Int = 0): ApiResponse<TribeList>

    /**
     * 创建部落
     *
    name	非空字符串，最长100个字符	True
    avatar_url	非空字符串，最长400个字符	True
     */
    @POST("api/tribe/v1/tribe/create")
    suspend fun createTribe(@Body body: Map<String, @JvmSuppressWildcards Any>):
            ApiResponse<ResultTribeCreate>

    /**
     *   【部落基本信息属性组】
     *
    参数
    参数	类型	是否必传	默认值
    tribe_id	整数	True
     */
    @GET("api/tribe/v1/tribe/detail")
    suspend fun tribeDetail(@Query("tribe_id") tribe_id: String): ApiResponse<TribeInfo>

    /**
     *  更新部落信息
     *  tribe_id	整数	True
    name	非空字符串，最长100个字符	False
    avatar_url	非空字符串，最长400个字符	False
    bulletin	非空字符串，最长1000个字符	False
     *
     */
    @POST("api/tribe/v1/tribe/update")
    suspend fun updateTribeDetail(@Body body: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>

    /**
     *  销毁部落
     *
     *  tribe_id
     */
    @POST("api/tribe/v1/tribe/destroy")
    suspend fun destroyTribe(@Body body: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>

    /**
     *  申请加入部落
     *
     *  tribe_id
     *
     *    申请加入部落
    return: {"msg": "已发送申请，请等待首领同意"}
     */
    @POST("api/tribe/v1/member/apply")
    suspend fun joinTribe(@Body body: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>


    /**
     * tribe_id	整数	True
    last_member_id	整数	False	0
    last_member_is_online	真假值	False	True
    获取部落成员列表，仅获取普通成员，不包括首领和副首领
    return: {
    "members": [
    {
    "member_id": 5,
    "is_online": true,
    "role": 0,    #(0, "普通成员"),(5, "副首领"),(10, "首领"),
    "user": {
    【用户基本信息属性组】
    }
    }...
    ]
     */
    @GET("api/tribe/v1/member/list")
    suspend fun getMemberList(
        @Query("tribe_id") tribe_id: String,
        @Query("last_member_id") last_member_id: String,
        @Query("last_member_is_online") last_member_is_online: Boolean
    ): ApiResponse<MemberList>


    @GET("api/tribe/v1/member/rolelist")
    suspend fun getMemberListWithRoles(
        @Query("tribe_id") tribe_id: String,
        @Query("roles") roles: String,
        @Query("last_member_id") last_member_id: String,
    ): ApiResponse<MemberList>

    /**
     *   踢出成员
     *
     *  tribe_id	整数	True
    member_id	整数	True
     */
    @POST("api/tribe/v1/member/kick")
    suspend fun kickMember(@Body body: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>

    /**
     *   退出部落
     *
     * tribe_id	整数	True
     */
    @POST("api/tribe/v1/member/quit")
    suspend fun quitTribe(@Body body: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>

    /**
     *   获取部落副首领列表，仅获取副首领
     *   tribe_id	整数	True
     */
    @GET("api/tribe/v1/admin/list")
    suspend fun getDepLeaderList(@Query("tribe_id") tribe_id: String): ApiResponse<TribeDepLeaderData>

    /**
     *   设置/取消副首领
     *
     * tribe_id	整数	True
    member_id	整数	True
    is_admin	真假值	True
     */
    @POST("api/tribe/v1/admin/set")
    suspend fun setCancelDepLeader(@Body body: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>


    /**
     *     获取部落成员列表，仅获取普通成员，不包括首领和副首领
     * tribe_id	整数	True
    last_apply_id	整数	False	0
     */
    @GET("api/tribe/v1/memberapply/list")
    suspend fun getApplyList(
        @Query("tribe_id") tribe_id: String,
        @Query("last_apply_id") last_apply_id: String = "0"
    ): ApiResponse<TribeMemberData>

    /**
     *     同意/拒绝成员申请
     *
    参数
    参数	类型	是否必传	默认值
    tribe_id	整数	True
    userid	整数	True
    agreed	真假值	True
     */
    @POST("api/tribe/v1/memberapply/agree")
    suspend fun agreeMemberApply(@Body body: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>

    @POST("api/tribe/v1/joininvite/accept")
    suspend fun acceptInvite(@Body body: Map<String, String>): ApiResponse<JsonObject>

    @GET("api/tribe/v1/tribe/conf")
    suspend fun getTribeConfig(): ApiResponse<FamilyConfig>

    @GET("api/tribe/v1/tribe/encourage/info")
    suspend fun getEncourageInfo(): ApiResponse<FamilyEncourageInfo>
}