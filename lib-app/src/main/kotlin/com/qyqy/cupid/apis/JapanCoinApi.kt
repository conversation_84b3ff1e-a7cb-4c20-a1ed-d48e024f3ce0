package com.qyqy.cupid.apis

import com.qyqy.cupid.data.UserCoinConfig
import com.qyqy.cupid.data.UserIncomeConfig
import com.qyqy.ucoo.http.ApiResponse
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST

/**
 *  @time 2024/7/31
 *  <AUTHOR>
 *  @package com.qyqy.cupid.apis
 */
interface JapanCoinApi {
    @GET("api/ucuser/v1/japan/coin_page/conf")
    suspend fun getCoinConfig(): ApiResponse<UserCoinConfig>

    @GET("api/ucuser/v1/japan/income_page/conf")
    suspend fun getIncomeConfig(): ApiResponse<UserIncomeConfig>

    @POST("api/wallet/v1/japan/withdraw/create")
    suspend fun getWithdrawList(@Body map: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>

    @POST("api/wallet/v1/japan/withdraw/create")
    suspend fun createWithdraw(@Body map: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>

    @GET("api/itasks/v1/daily/tasks/multiplier_help")
    suspend fun getLineHelp(): ApiResponse<LineHelp>

}

@Serializable
data class LineHelp(
    @SerialName("line_id")
    val lineId: String = "",
    val title: String = "",
    val content: String = "",
    val tips: String = "",
)