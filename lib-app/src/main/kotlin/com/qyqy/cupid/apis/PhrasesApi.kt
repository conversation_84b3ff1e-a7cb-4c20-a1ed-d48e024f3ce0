package com.qyqy.cupid.apis

import com.qyqy.cupid.data.PhrasesSettingBean
import com.qyqy.ucoo.http.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface PhrasesApi {
    @GET("api/privatechat/v1/japan/prologue/settings")
    suspend fun getPhrasesSettings(): ApiResponse<PhrasesSettingBean>

    /**
     * 添加开场白
     * param - content str 开场白内容
     * @param map
     * @return
     */
    @POST("api/privatechat/v1/japan/prologue/add")
    suspend fun addPhrases(@Body map: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>

    /**
     * 删除开场白
     * param - prologue_id int 开场白id
     * @param map
     * @return
     */
    @POST("api/privatechat/v1/japan/prologue/delete")
    suspend fun deletePhrases(@Body map: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>

    /**
     * say hi的时候调用
     * param - target_userid int 发送目标用户的id
     *
     * @param map
     * @return
     */
    @POST("api/privatechat/v1/japan/prologue/send")
    suspend fun sendPhrases(@Body map: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>

    @GET("api/privatechat/v1/japan/prologue/my_prologues")
    suspend fun getMyPhrases(@Query("target_userid") id:Int): ApiResponse<JsonObject>

    @GET("api/privatechat/v1/japan/prologue/entrance")
    suspend fun getPhrasesFloatWidget(): ApiResponse<PhrasesSettingBean.FloatWidgetSetting>
}