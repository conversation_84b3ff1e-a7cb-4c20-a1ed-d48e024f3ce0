package com.qyqy.cupid.apis

import com.qyqy.cupid.FaceData
import com.qyqy.cupid.data.WithdrawList
import com.qyqy.ucoo.http.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

/**
 *  @time 9/11/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.apis
 */
interface WithdrawApi {
    @POST("api/wallet/v2/japan/withdraw/create")
    suspend fun createWithdrawOrder(@Body map: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>

    @POST("api/wallet/v2/japan/withdraw/account")
    suspend fun createWithdrawAccount(@Body map: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>

    @POST("api/wallet/v2/japan/withdraw/help")
    suspend fun getWithdrawHelp(@Body map: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>

    @GET("api/wallet/v2/japan/withdraw/types")
    suspend fun getWithdrawTypes(): ApiResponse<WithdrawList>

    @GET("api/wallet/v2/japan/withdraw/history")
    suspend fun getWithdrawRecords(@Query("last_id") id: Int, @Query("withdraw_type") type: Int? = null): ApiResponse<JsonObject>

    @POST("api/wallet/v2/japan/withdraw/precheck")
    suspend fun precheckWithdraw(): ApiResponse<JsonObject>

    @POST("/api/ucuser/v2/realuser/apply")
    suspend fun createRealAuth(@Body map: Map<String, @JvmSuppressWildcards Any>): ApiResponse<FaceData>

    @POST("/api/ucuser/v2/realuser/check")
    suspend fun completeRealAuth(@Body map: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>

}