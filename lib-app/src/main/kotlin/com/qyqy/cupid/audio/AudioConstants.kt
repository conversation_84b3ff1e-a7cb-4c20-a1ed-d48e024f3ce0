package com.qyqy.cupid.audio

object AudioConstants {

    private const val DEFAULT_AUDIO_RECORD_MAX_TIME = 60

    const val ERROR_STATUS_IS_AUDIO_RECORDING = -1003

    const val ERROR_MIC_PERMISSION_REFUSED = -1004

    const val ERROR_RECORD_FAILED = -2003

    const val ERROR_CODE_MIC_IS_BEING_USED: Int = -100

    val isEnableSoundMessageSpeakerMode: Boolean
        get() = true

    val audioRecordMaxTime: Int
        get() = DEFAULT_AUDIO_RECORD_MAX_TIME
}

val appBusinessScene: String?
    get() = null
