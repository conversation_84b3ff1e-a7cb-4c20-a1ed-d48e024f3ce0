package com.qyqy.cupid.audio

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.media.MediaPlayer
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.util.Log
import androidx.core.app.ActivityCompat
import com.qyqy.ucoo.compose.getCacheFile
import java.io.File
import kotlin.math.log10

object AudioRecorder {

    private val TAG: String = AudioRecorder::class.java.simpleName

    private const val MAGIC_NUMBER = 500
    private const val MIN_RECORD_DURATION = 1000
    private const val UPDATE_AMPLITUDE_PERIOD = 100

    private const val DEFAULT_MAX_AMPLITUDE = 100.0

    private const val AUDIO_TYPE = "audio"

    private val updateAmplitudeHandler: Handler = Handler(Looper.getMainLooper())

    private lateinit var recorder: IAudioRecorder

    init {
        init()
    }

    private fun init() {
        val useAIDenoiseRecorder = false

        if (useAIDenoiseRecorder) {
//            recorder = AIDenoiseAudioRecordImpl()
        } else {
            recorder = SystemAudioRecordImpl()
        }
    }

    fun startRecord(context: Context, audioFilePath: String? = null, callback: AudioRecorderCallback?) {
        if (appBusinessScene != null) {
            val errorMessage = "其他功能正在使用麦克风，无法录制"
            Log.w(TAG, errorMessage)
            callback?.onFailed(AudioConstants.ERROR_CODE_MIC_IS_BEING_USED, errorMessage)
            return
        }

        val applicationContext = context.applicationContext


        val path = audioFilePath?.takeIf {
            it.isNotEmpty()
        } ?: "${applicationContext.getCacheFile(AUDIO_TYPE).absolutePath}${File.separator}${System.currentTimeMillis()}.m4a"

        val internalCallback: AudioRecorderInternalCallback = object : AudioRecorderInternalCallback {
            override fun onFinished() {
                updateAmplitudeHandler.removeCallbacksAndMessages(null)
                if (callback != null) {
                    if (!File(path).exists()) {
                        callback.onFailed(AudioConstants.ERROR_RECORD_FAILED, "录音失败，请重试")
                    } else {
                        callback.onFinished(path)
                    }
                }
            }

            override fun onStarted() {
                callback?.onStarted()
                updateMicStatus(callback)
            }

            override fun onFailed(errorCode: Int, errorMessage: String) {
                updateAmplitudeHandler.removeCallbacksAndMessages(null)
                callback?.onFailed(errorCode, errorMessage)
            }

            override fun onCanceled() {
                val file = File(path)
                val isSuccess: Boolean = !file.exists() || file.isFile() && file.delete()
                if (!isSuccess) {
                    Log.w(TAG, "cancel record, delete audio file failed")
                }
                callback?.onCanceled()
            }
        }
        if (PackageManager.PERMISSION_GRANTED != ActivityCompat.checkSelfPermission(
                applicationContext,
                Manifest.permission.RECORD_AUDIO
            )
        ) {
            callback?.onFailed(AudioConstants.ERROR_MIC_PERMISSION_REFUSED, "没有录音权限")
        } else {
            recorder.startRecord(applicationContext, path, internalCallback)
        }
    }

    fun stopRecord() {
        recorder.stopRecord()
    }

    fun cancelRecord() {
        recorder.cancelRecord()
    }

    fun getDuration(audioPath: String?): Int {
        if (TextUtils.isEmpty(audioPath)) {
            return 0
        }
        var duration = 0

        // Get the real audio length by initializing the player
        try {
            val mp = MediaPlayer()
            mp.setDataSource(audioPath)
            mp.prepare()
            duration = mp.duration

            // the length is processed to achieve the effect of rounding
            duration = if (duration < MIN_RECORD_DURATION) {
                0
            } else {
                duration + MAGIC_NUMBER
            }
        } catch (e: Exception) {
            Log.w(TAG, "getDuration failed", e)
        }
        if (duration < 0) {
            duration = 0
        }
        return duration
    }

    private fun updateMicStatus(callback: AudioRecorderCallback?) {
        if (callback != null) {
            val ratio = recorder.maxAmplitude
            var db = 0.0
            if (ratio > 1) {
                db = 20 * log10(ratio)
            }
            callback.onVoiceDb(db)
            updateAmplitudeHandler.postDelayed({ updateMicStatus(callback) }, UPDATE_AMPLITUDE_PERIOD.toLong())
        }
    }

    interface AudioRecorderCallback {
        fun onStarted()

        fun onFinished(outputPath: String)

        fun onFailed(errorCode: Int, errorMessage: String)

        fun onCanceled()

        fun onVoiceDb(db: Double) {}
    }

    interface IAudioRecorder {
        fun startRecord(context: Context, outputPath: String, callback: AudioRecorderInternalCallback?)

        fun stopRecord()

        fun cancelRecord()

        val isRecording: Boolean

        val maxAmplitude: Double
            get() = DEFAULT_MAX_AMPLITUDE
    }

    interface AudioRecorderInternalCallback {
        fun onStarted()

        fun onFinished()

        fun onCanceled()

        fun onFailed(errorCode: Int, errorMessage: String)
    }
}