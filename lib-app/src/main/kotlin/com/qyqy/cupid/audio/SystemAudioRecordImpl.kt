package com.qyqy.cupid.audio

import android.content.Context
import android.media.MediaRecorder
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.qyqy.ucoo.toast


class SystemAudioRecordImpl : AudioRecorder.IAudioRecorder {

    private var callback: AudioRecorder.AudioRecorderInternalCallback? = null

    private val handler = Handler(Looper.getMainLooper())

    private var recorder: MediaRecorder? = null

    override var isRecording = false
        private set

    override val maxAmplitude: Double
        get() = recorder?.maxAmplitude?.toDouble() ?: super.maxAmplitude

    override fun startRecord(context: Context, outputPath: String, callback: AudioRecorder.AudioRecorderInternalCallback?) {
        Log.i(TAG, "startRecord output path is $outputPath")
        if (isRecording) {
            Log.w(TAG, "startRecord failed, is in recording")
            callback?.onFailed(AudioConstants.ERROR_STATUS_IS_AUDIO_RECORDING, "其他功能正在使用麦克风，无法录制")
            return
        }
        this.callback = callback
        isRecording = true
        try {
            recorder = MediaRecorder().apply {
                setAudioSource(MediaRecorder.AudioSource.MIC)
                setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)
                setOutputFile(outputPath)
                setAudioEncoder(MediaRecorder.AudioEncoder.AAC)
                prepare()
                start()
            }
            handler.removeCallbacksAndMessages(null)
            handler.postDelayed(Runnable {
                if (!isRecording) {
                    return@Runnable
                }
                stopRecord()
                toast("已达到最大语音长度")
            }, AudioConstants.audioRecordMaxTime.times(1000).minus(200).toLong())
            // 最长录制时间
            this.callback?.onStarted()
        } catch (e: Exception) {
            Log.e(TAG, "record failed " + e.message)
            isRecording = false
            this.callback?.onFailed(AudioConstants.ERROR_RECORD_FAILED, "录音失败，请重试")
            this.callback = null
        }
    }

    override fun stopRecord() {
        if (recorder == null || !isRecording) {
            return
        }
        handler.removeCallbacksAndMessages(null)
        recorder?.release()
        recorder = null
        isRecording = false
        callback?.onFinished()
        callback = null
    }

    override fun cancelRecord() {
        if (recorder == null || !isRecording) {
            return
        }
        handler.removeCallbacksAndMessages(null)
        recorder?.release()
        recorder = null
        isRecording = false
        callback?.onCanceled()
        callback = null
    }


    companion object {
        private const val TAG = "SystemAudioRecordImpl"
    }
}