package com.qyqy.cupid.config

import com.qyqy.cupid.apis.ChatApi
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

// {
//    "show_audioroom_entry": true,    // 是否显示语音房入口
//    "show_tribe_entry": true,        // 是否显示部落/家族入口
// }
@Serializable
data class ChatListConfigBean(
    @SerialName("show_audioroom_entry") val showAudioroomEntry: Boolean = true,
    @SerialName("show_tribe_entry") val showTribeEntry: Boolean = true
)

object ChatListConfig {
    private val api = createApi<ChatApi>()
    private val _config: MutableStateFlow<ChatListConfigBean?> = MutableStateFlow(null)
    val config: StateFlow<ChatListConfigBean?> = _config.asStateFlow()

    private val _intimacyMap: MutableStateFlow<Map<Int, Int>> = MutableStateFlow(mapOf())
    val intimacyMap: StateFlow<Map<Int, Int>> = _intimacyMap.asStateFlow()

//    val intimacyList = remember {
//        mutableStateOf(listOf<intimateUserBean>())
//    }
//
//    LaunchedEffect(key1 = Unit) {
//        runApiCatching {
//            createApi<ChatApi>().getIntimateScore()
//        }.onSuccess {
//            intimacyList.value = it
//        }
//    }


    fun init() {
        appCoroutineScope.launch {
            runApiCatching {
                api.getChatListConfig()
            }.onSuccess {
                _config.value = it
            }
        }

        appCoroutineScope.launch {
            runApiCatching {
                api.getIntimateScore()
            }.onSuccess { list ->
                _intimacyMap.value = buildMap {
                    list.forEach {
                        put(it.userid, it.intimateScore)
                    }
                }
            }
        }
    }

    fun clear(){
        _intimacyMap.value = mapOf()
    }
}
