package com.qyqy.cupid.config

import com.qyqy.ucoo.isProd

object CupidConst {
    object URL {
        private val BASE_URL: String
            get() = if (isProd) "https://api.ucoofun.com" else "https://api.test.ucoofun.com"

        /**
         * 用户协议
         */
        val USER_SERVICE = "${BASE_URL}/h5/ja/user_service"

        /**
         * 隐私政策
         */
        val PRIVACY_POLICY = "${BASE_URL}/h5/ja/privacy_policy"

        /**
         * 会员协议
         */
        val VIP_SERVICE = "$BASE_URL/h5/ja/vip_service"

        /**
         * 会员连续订阅协议
         */
        val VIP_SUB_SERVICE = "$BASE_URL/h5/ja/vip_sub_service"

        /**
         * cp榜单
         */
        val CP_RANK = "$BASE_URL/h5/japan/cp_rank"
    }
}