package com.qyqy.cupid.config.offline


import androidx.webkit.WebViewAssetLoader
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class OfflinePkgConfig(
    @SerialName("games")
    val games: List<Game> = listOf(),
    val interceptors: List<Interceptor> = emptyList(),
    @SerialName("update_time")
    val updateTime: String = ""
) {
    @Serializable
    data class Game(
        @SerialName("download_url")
        val downloadUrl: String = "",
        @SerialName("name")
        val name: String = "",
        @SerialName("version")
        val version: String = ""
    )

    @Serializable
    data class Interceptor(
        @SerialName("game_name")
        val gameName: String = "",
        @SerialName("params")
        val params: List<Param> = listOf(),
        @SerialName("remote_path")
        val remotePath: String = ""
    )

    @Serializable
    data class Param(
        @SerialName("key")
        val key: String = "",
        @SerialName("value")
        val value: String = ""
    )
}