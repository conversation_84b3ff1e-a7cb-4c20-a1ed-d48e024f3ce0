package com.qyqy.cupid.config.offline

import android.content.Context
import android.net.Uri
import android.webkit.WebResourceResponse
import android.webkit.WebView
import androidx.webkit.WebViewAssetLoader
import androidx.webkit.WebViewAssetLoader.InternalStoragePathHandler
import com.coolerfall.download.Helper
import com.hjq.language.MultiLanguages
import com.qyqy.cupid.utils.ZipUtil
import com.qyqy.ucoo.UCOOEnvironment
import com.qyqy.ucoo.UCOOEnvironmentInstance
import com.qyqy.ucoo.app
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.http.createShareOkHttpClientBuilder
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.utils.LogUtils
import com.qyqy.ucoo.utils.download.Downloader
import com.qyqy.ucoo.utils.download.Request
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import java.io.File
import java.util.concurrent.TimeUnit

object OfflinePkgManager {
    private data class LocalGame(
        val gameName: String,
        val exist: Boolean,
        val webViewAssetLoader: WebViewAssetLoader?,
    )

    private const val TAG = "OfflinePkgManager"
    private const val DEV_CONFIG_URL =
        "https://qyqy-china-big-file2-1319886479.cos.ap-shanghai.myqcloud.com/games/develop/game_conf.json"
    private const val REL_CONFIG_URL =
        "https://qyqy-china-big-file-1319886479.cos.ap-singapore.myqcloud.com/games/release/game_conf.json"
    private const val STAGING_CONFIG_URL =
        "https://qyqy-china-big-file2-1319886479.cos.ap-shanghai.myqcloud.com/games/staging/game_conf.json"

    private val configFileName = "game_conf.json"

    const val DOMAIN_UCOO_GAME = "ucoogame.com"
    private var offlinePkgConfig: OfflinePkgConfig? = null
    private val env: String
        get() = when (UCOOEnvironment.currentEnv()) {
            UCOOEnvironmentInstance.DEBUG -> "develop"
            UCOOEnvironmentInstance.RELEASE -> "release"
            else -> "staging"
        }

    private val mapGame = mutableMapOf<String, LocalGame>()

    fun interceptRequest(view: WebView?, uri: Uri?): WebResourceResponse? {
        val pkgConfig = offlinePkgConfig
        if (uri != null && view != null && pkgConfig != null) {
            try {
                if (DOMAIN_UCOO_GAME == uri.host) {
                    val gameName = uri.path?.let { path ->
                        val str = path.removePrefix("/")
                        val index = str.indexOfFirst { it == '/' }
                        if (index > -1) {
                            str.substring(0, index)
                        } else {
                            null
                        }
                    }
                    LogUtils.d(TAG, "intercept == url:$uri=>gameName:$gameName")
                    LogUtils.d(
                        TAG,
                        "intercept == host:${uri.host},path:${uri.path},authority:${uri.authority}=>$gameName"
                    )
                    return mapGame[gameName]?.webViewAssetLoader?.shouldInterceptRequest(uri)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return null
    }

    private fun isGameExist(gameName: String) = mapGame[gameName]?.exist == true

    fun getLocalUrl(url: String?): String? {
        val pkgConfig = offlinePkgConfig ?: return url
        val uri = Uri.parse(url) ?: return url
        val inter = pkgConfig.interceptors.find { it.remotePath == uri.path }
        return if (inter == null) {
            url
        } else {
            val gameName = inter.gameName
            //验证文件完整
            return if (isGameExist(gameName)) {
                val localUrl = "https://$DOMAIN_UCOO_GAME/${inter.gameName}/index.html"
                //拼接参数
                val builder = Uri.parse(localUrl).buildUpon()
                if (inter.params.isNotEmpty()) {
                    inter.params.forEach {
                        builder.appendQueryParameter(it.key, it.value)
                    }
                    val newUrl = builder.build().toString()
                    LogUtils.d(TAG, "url changed:[$url]=>[$newUrl]")
                    newUrl
                }
                builder.appendQueryParameter(
                    "app-language",
                    MultiLanguages.getAppLanguage().toLanguageTag()
                )
                builder.build().toString()
            } else {
                LogUtils.d(TAG, "$gameName NOT EXIST")
                null
            }
        }
    }

    fun startCheck() {
        val confUrl = when (UCOOEnvironment.currentEnv()) {
            UCOOEnvironmentInstance.DEBUG -> DEV_CONFIG_URL
            UCOOEnvironmentInstance.RELEASE -> REL_CONFIG_URL
            else -> STAGING_CONFIG_URL
        }
        appCoroutineScope.launch(Dispatchers.IO) {
            val request = okhttp3.Request.Builder()
                .url(confUrl)
                .build()
            val client = createShareOkHttpClientBuilder()
                .connectTimeout(Helper.DEFAULT_CONNECT_TIMEOUT.toLong(), TimeUnit.MILLISECONDS)
                .readTimeout(Helper.DEFAULT_READ_TIMEOUT.toLong(), TimeUnit.MILLISECONDS)
                .writeTimeout(Helper.DEFAULT_WRITE_TIMEOUT.toLong(), TimeUnit.MILLISECONDS)
                .build()
            val resp = try {
                client.newCall(request).execute()
            } catch (e: Exception) {
                return@launch
            }
            if (resp.isSuccessful) {
                val json = resp.body?.string() ?: return@launch
                val config = sAppJson.decodeFromString<OfflinePkgConfig>(json)

                val context: Context = app
                val dirGame = File(context.filesDir, "games/$env")
                if (!dirGame.exists()) {
                    dirGame.mkdirs()
                }
                val gamesNeedUpdate = mutableListOf<OfflinePkgConfig.Game>()
                try {
                    var f: File
                    config.games.forEach { game ->
                        //游戏各自的配置
                        f = File(dirGame, "${game.name}/config.json")
                        if (f.exists()) {
                            //对比版本，版本不一致则下载
                            try {
                                val g =
                                    sAppJson.decodeFromString<OfflinePkgConfig.Game>(f.readText())
                                LogUtils.d(
                                    TAG,
                                    "[${game.name}] remote version=>${game.version},local version=>${g.version}"
                                )
                                if (g.version != game.version) {
                                    gamesNeedUpdate.add(game)
                                }
                            } catch (e: Exception) {
                                LogUtils.e(TAG, e.message.orEmpty())
                                gamesNeedUpdate.add(game)
                            }
                        } else {
                            LogUtils.d(TAG, "config file not exist, ${f.absolutePath}")
                            gamesNeedUpdate.add(game)
                        }
                    }
                    //下载游戏
                    LogUtils.d(TAG, "gamesNeedUpdate: ${gamesNeedUpdate.map { it.name }}")
                    gamesNeedUpdate.forEach { g ->
                        downloadGame(g, dirGame)
                    }
                    //更新配置
                    writeConfig(dirGame, json)
                    offlinePkgConfig = config
                    checkGames(context, config.games)
                } catch (e: Exception) {
                    e.printStackTrace()
                    LogUtils.e(TAG, e.message.orEmpty())
                }
            } else {
                LogUtils.e(TAG, "request config error")
            }
        }
    }

    private suspend fun checkGames(context: Context, games: List<OfflinePkgConfig.Game>) =
        withContext(Dispatchers.IO) {
            mapGame.clear()
            games.forEach { game ->
                val gameName = game.name
                val fConfig = File(context.filesDir, "games/$env/$gameName/config.json")
                val fEntry = File(context.filesDir, "games/$env/$gameName/dist/index.html")
                val exist = fConfig.exists() && fEntry.exists()
                val publicDir = File(context.filesDir, "games/$env/$gameName/dist")
                val wsl = if (exist) WebViewAssetLoader.Builder()
                    .setDomain(DOMAIN_UCOO_GAME)
                    .addPathHandler("/$gameName/", InternalStoragePathHandler(context, publicDir))
                    .build()
                else
                    null
                mapGame[gameName] = LocalGame(gameName, exist, wsl)
            }
        }

    private suspend fun downloadGame(game: OfflinePkgConfig.Game, dirGame: File) {
        val gameFolder = File(dirGame, game.name)
        if (gameFolder.exists()) {
            gameFolder.deleteRecursively()
        }
        gameFolder.mkdirs()
        try {
            val uri = Uri.parse(game.downloadUrl)
            val destGameZip =
                File(gameFolder, uri?.lastPathSegment ?: "${game.name}-${game.version}.zip")
            if (!destGameZip.exists()) {
                withContext(Dispatchers.IO) {
                    destGameZip.createNewFile()
                }
            }
            //下载游戏包
            val result = Downloader.create(gameFolder.absolutePath)
                .execute(
                    Request.Builder(game.downloadUrl)
                        .apply {
                            fileName(destGameZip.name)
                        }.build()
                )
            if (result.isSuccess) {
                LogUtils.i(TAG, "下载游戏成功[${game.name}]")
                //解压游戏包
                LogUtils.i(TAG, "开始解压游戏[${game.name}]")
                destGameZip.parentFile?.let {
                    ZipUtil.upZipFile(
                        result.getOrThrow(),
                        it.absolutePath
                    )
                }
                LogUtils.i(
                    TAG,
                    "解压游戏成功[${game.name}]=>文件夹${destGameZip.parentFile?.absolutePath}"
                )
                //写入游戏配置
                val confFile = File(dirGame, "${game.name}/config.json")
                if (!confFile.exists()) {
                    withContext(Dispatchers.IO) {
                        confFile.createNewFile()
                    }
                }
                confFile.writeText(sAppJson.encodeToString(game))
                LogUtils.i(TAG, "游戏配置写入成功[${game.name}]：$game")
            } else {
                LogUtils.e(
                    TAG,
                    "下载游戏失败[${game.name}]：" + result.exceptionOrNull()?.message.orEmpty()
                )
            }
        } catch (e: Exception) {
            e.printStackTrace()
            LogUtils.e(TAG, "下载游戏失败：" + e.message.orEmpty())
        }
    }

    private fun writeConfig(dirGame: File, json: String) {
        val configFile = File(dirGame, configFileName)
        if (!configFile.exists()) {
            configFile.createNewFile()
        }
        configFile.writeText(json)
    }

}