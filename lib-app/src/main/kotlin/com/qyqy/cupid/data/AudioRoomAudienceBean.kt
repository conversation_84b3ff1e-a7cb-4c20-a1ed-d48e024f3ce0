package com.qyqy.cupid.data


import android.os.Parcelable
import android.os.SystemClock
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
@Serializable
data class AudioRoomAudienceBean(
    @SerialName("gift_task")
    val giftTask: GiftTask = GiftTask(),
    @SerialName("interact_task")
    val interactTask: InteractTask = InteractTask(),
    @SerialName("is_finished")
    val isFinished: Boolean = false
) : Parcelable {
    @Keep
    @Parcelize
    @Serializable
    data class GiftTask(
        @SerialName("gold_coin_cnt")
        val goldCoinCnt: Int = 0
    ): Parcelable

    @Keep
    @Parcelize
    @Serializable
    data class InteractTask(
        @SerialName("interact_seconds")
        val interactSeconds: Long = 0L,
        @SerialName("interact_status")
        val interactStatus: Int = 0,
    ): Parcelable
}