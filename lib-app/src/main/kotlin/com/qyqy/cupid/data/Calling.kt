package com.qyqy.cupid.data

import android.os.SystemClock
import androidx.compose.runtime.Stable
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.im.bean.RtcToken
import com.qyqy.ucoo.im.message.entity.RichItem
import com.qyqy.ucoo.utils.rtc.AppRtcManager
import com.qyqy.ucoo.utils.rtc.RtcScenario
import com.qyqy.ucoo.utils.rtc.VideoRtcTokenProviderImpl
import com.qyqy.ucoo.utils.rtc.engine.TencentRtcFactory
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.serialization.json.JsonObject


enum class CallMode {

    OnlyVoice,

    OnlyVideo,

    Mixed;

    fun isOnlyVoice() = this == OnlyVoice
}

interface ICallInfo {

    val callId: String

    val collapsed: Boolean

    val selfUser: User

    val mode: CallMode

    val rtcType: Int

    val rtcConfig: JsonObject?

}

data class C2CCallInfo constructor(
    override val callId: String,
    override val selfUser: User,
    val targetUser: User,
    override val mode: CallMode = CallMode.OnlyVideo,
    override val rtcType: Int = 3,
    override val rtcConfig: JsonObject? = null,
    override val collapsed: Boolean = false, // 是否收起最小化
    val supportCameraEnable: Boolean = true, // 是否支持开关摄像头，不支持就不能关闭
    val targetCameraEnable: Boolean = true, // 对方摄像头
    val selfCameraEnable: Boolean = true, // 自己摄像头
    val selfUseFrontCamera: Boolean = true, // 自己是否使用前置摄像头
    val microphoneEnable: Boolean = true, // 麦克风
    val speakerEnable: Boolean = true, // 扬声器
    val targetJoined: Boolean = false,
    val targetHasCameraFrame: Boolean = false,
) : ICallInfo


data class InCallInfo(
    val startElapsedRealtime: Long = SystemClock.elapsedRealtime(),
    val payCallFinishElapsedRealtime: Long = SystemClock.elapsedRealtime(),
    val coinPayCallPerMinute: Int = 0,
    val showFreeCallTime: Boolean = false,
    val freeCallFinishElapsedRealtime: Long = SystemClock.elapsedRealtime(),
)


sealed interface CallState<out C : ICallInfo> {

    /**
     * 空闲等待状态
     */
    data object Idea : CallState<Nothing>

    /**
     * 呼叫中的状态
     */
    sealed interface ICalling<out C : ICallInfo> : CallState<C>, ICallInfo {

        val info: C

        override val callId: String
            get() = info.callId

        override val collapsed: Boolean
            get() = info.collapsed

        override val selfUser: User
            get() = info.selfUser

        override val mode: CallMode
            get() = info.mode

        override val rtcType: Int
            get() = info.rtcType

        override val rtcConfig: JsonObject?
            get() = info.rtcConfig

        val channelId: String

        val rtcToken: String

        fun copySelf(info: @UnsafeVariance C): ICalling<C>

    }

    /**
     * 呼叫中
     */
    sealed class Calling<out C : ICallInfo> : ICalling<C> {

        abstract val callInConfirming: Boolean

        abstract val hintContent: String

        abstract val hintLeadContent: List<RichItem>?
    }

    /**
     * 被呼方呼叫中
     */
    @Stable
    data class Incoming<out C : ICallInfo> constructor(
        override val channelId: String,
        override val rtcToken: String,
        override val info: C,
        override val callInConfirming: Boolean,
        override val hintContent: String = "",
        override val hintLeadContent: List<RichItem>? = null,
    ) : Calling<C>() {

        override fun copySelf(info: @UnsafeVariance C): ICalling<C> {
            return copy(info = info)
        }
    }

    @Stable
    data class Matching<out C : ICallInfo> constructor(
        override val channelId: String,
        override val rtcToken: String,
        override val info: C,
    ) : ICalling<C> {

        override fun copySelf(info: @UnsafeVariance C): ICalling<C> {
            return copy(info = info)
        }
    }

    sealed interface IRtcOwner {
        val rtcManager: AppRtcManager
    }

    sealed interface ICallingPage<out C : ICallInfo> : IRtcOwner, ICalling<C>

    /**
     * 主叫方呼叫中
     */
    @Stable
    data class Outgoing<out C : ICallInfo> constructor(
        override val channelId: String,
        override val rtcToken: String,
        override val info: C,
        override val callInConfirming: Boolean,
        override val hintContent: String = "",
        override val rtcManager: AppRtcManager = AppRtcManager(
            tag = info.callId,
            rtcProvider = VideoRtcTokenProviderImpl,
            factory = TencentRtcFactory(
                scenario = if (info.mode.isOnlyVoice()) RtcScenario.OneToOneAudio else RtcScenario.OneToOneVideo,
                enableAudioVolumeIndication = false,
                jsonConfig = info.rtcConfig
            ),
            autoPCMCapture = false,
        ),
    ) : Calling<C>(), ICallingPage<C> {

        override val hintLeadContent: List<RichItem>?
            get() = null

        override fun copySelf(info: @UnsafeVariance C): ICalling<C> {
            return copy(info = info)
        }
    }

    /**
     * 通话中
     */
    @Stable
    data class InCall<out C : ICallInfo> constructor(
        override val channelId: String,
        override val rtcToken: String,
        override val info: C,
        val inCall: InCallInfo = InCallInfo(),
        @IgnoredOnParcel override val rtcManager: AppRtcManager = AppRtcManager(
            tag = info.callId,
            rtcProvider = VideoRtcTokenProviderImpl,
            factory = TencentRtcFactory(
                scenario = if (info.mode.isOnlyVoice()) RtcScenario.OneToOneAudio else RtcScenario.OneToOneVideo,
                enableAudioVolumeIndication = false,
                jsonConfig = info.rtcConfig
            ),
            autoPCMCapture = false,
        ),
        private var joined: Boolean = false,
    ) : ICallingPage<C> {

        val startElapsedRealtime: Long
            get() = inCall.startElapsedRealtime

        init {
            if (!joined) {
                joined = true
                rtcManager.joinChannel(RtcToken(channelId, rtcToken), true)
            }
        }

        override fun copySelf(info: @UnsafeVariance C): ICalling<C> {
            return copy(info = info)
        }
    }
}