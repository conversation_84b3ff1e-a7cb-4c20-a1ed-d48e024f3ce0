package com.qyqy.cupid.data


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import androidx.annotation.Keep
import com.qyqy.cupid.widgets.wheel.common.core.WheelData

@Keep
@Serializable
data class CityData(
    @SerialName("cityList")
    val city: List<Area> = listOf(),
    @SerialName("en")
    val en: String = "",
    @SerialName("id")
    val id: String = "",
    @SerialName("kana")
    val kana: String = "",
    @SerialName("name")
    val name: String = "",
    @SerialName("short")
    val short: String = ""
) : WheelData {
    override val uniqueId: String = name

    override fun children(): List<WheelData> {
        return city
    }

    @Keep
    @Serializable
    data class Area(
        @SerialName("name")
        val areaName: String = "",
        @SerialName("code")
        val areaCode: String = ""
    ) : WheelData {
        override val uniqueId: String = areaName

        override fun children(): List<WheelData>? = null
    }
}