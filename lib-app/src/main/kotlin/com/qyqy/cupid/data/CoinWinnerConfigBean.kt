package com.qyqy.cupid.data


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import androidx.annotation.Keep

@Keep
@Serializable
data class CoinWinnerConfigBean(
    @SerialName("base_coins")
    val baseCoins: List<Int> = listOf(),
    @SerialName("can_create")
    val canCreate: Boolean = false,
    @SerialName("player_count_limit")
    val playerCountLimit: Int = 0,
    @SerialName("raise_multiplier")
    val raiseMultiplier: Int = 0,
    @SerialName("toast")
    val toast: String = "",
    @SerialName("rule")
    val gameRule: String = ""
)