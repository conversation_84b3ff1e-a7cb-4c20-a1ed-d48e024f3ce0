package com.qyqy.cupid.data


import androidx.annotation.Keep
import com.overseas.common.sntp.SNTPManager
import com.overseas.common.utils.isPreviewOnCompose
import com.qyqy.ucoo.utils.logW
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient
import kotlinx.serialization.json.JsonObject

@Keep
@Serializable
data class CoinWinnerDetail(
    @SerialName("players")
    val players: List<CoinWinnerPlayer> = listOf(),
    @SerialName("raise_coins")
    val raiseCoins: List<Int> = listOf(),
    @SerialName("round")
    val round: Round = Round(),
    @Transient
    val ts: Long = System.currentTimeMillis(),
) {

    @Transient
    var winner: CoinWinnerUser? = null
        get() {
            return if (round.getGameStatus() == CoinWinnerState.Finished) players.find { it.loseStageNum == 0 }?.player else null
        }

    @Transient
    var loser: CoinWinnerUser? = null
        get() {
            return players.find { it.loseStageNum == round.stageNum }?.player
        }

    @Transient
    var reward_coin: Int? = null

    @Keep
    @Serializable
    data class Round(
        @SerialName("id")
        val id: Int = -1,
        @SerialName("owner")
        val owner: CoinWinnerUser = CoinWinnerUser(),
        @SerialName("audioroom")
        val audioroom: Audioroom = Audioroom(),
        @SerialName("base_coin")
        val baseCoin: Int = 0,
        @SerialName("winner")
        val winner: CoinWinnerUser? = null,

        @SerialName("total_reward")
        val totalReward: Int = 0,
        @SerialName("owner_reward")
        val ownerReward: Int = 0,
        @SerialName("final_reward")
        val finalReward: Int = 0,

        @SerialName("fee")
        val fee: Int = 0,
        @SerialName("status")
        val status: Int = 0,

        @SerialName("start_game_timestamp")
        val startGameTimestamp: Long = 0,
        @SerialName("end_game_timestamp")
        val endGameTimestamp: Long = 0,

        @SerialName("stage_num")
        val stageNum: Int = 0,

        @SerialName("current_stage_start_timestamp")
        val currentStageStartTimestamp: Long = 0,
        @SerialName("current_stage_end_timestamp")
        val currentStageEndTimestamp: Long = 0,
    ) {
        @Keep
        @Serializable
        data class Audioroom(
            @SerialName("id")
            val id: Int = 0,
            @SerialName("room_locked")
            val roomLocked: Boolean = false,
            @SerialName("text_only")
            val textOnly: Boolean = false,
            @SerialName("title")
            val title: String = "",
        )

        fun getGameStatus(): CoinWinnerState {
            return when (status) {
                CoinWinnerState.Pending.status -> CoinWinnerState.Pending
                CoinWinnerState.Waiting.status -> CoinWinnerState.Waiting
                CoinWinnerState.Raising.status -> CoinWinnerState.Raising
                CoinWinnerState.Rnnouncing.status -> CoinWinnerState.Rnnouncing
                CoinWinnerState.Finished.status -> CoinWinnerState.Finished
                CoinWinnerState.Abort.status -> CoinWinnerState.Abort
                else -> {
                    logW("cannot parse the type, %s", this)
                    CoinWinnerState.Unknown
                }
            }
        }

    }

    //unusual
    fun copyOther(other: CoinWinnerDetail?) {
//        this.winner = other?.winner ?: this.winner
//        this.loser = other?.loser ?: this.loser
        this.reward_coin = other?.reward_coin ?: this.reward_coin
    }

    fun getUserAngle(user: CoinWinnerUser): Pair<Float, Float> {
//        val totalReward = getCurrentPercentCoin()
        val availablePlayers = getAvailablePlayers()
        val single = 360f / availablePlayers.size
        var angle = 0f
        var userSetion = Pair(0f, 0f)
        availablePlayers.forEach {
//            if (isUserAvailable(it)) {
//                val setion = Pair(angle, angle + (it.betCoin / totalReward.toFloat()) * 360f)
            val setion = Pair(angle, angle + single)
            if (it.player.userid != user.userid) {
//                    angle += (it.betCoin / totalReward.toFloat()) * 360f
                angle += single
            } else {
                userSetion = setion
                return@forEach
            }
//            }
        }
        return userSetion
    }

    fun contains(userid: Int): Boolean {
        if (isPreviewOnCompose) {
            return true
        }
        return players.any { it.player.userid == userid }
    }

    fun isUserAvailable(user: CoinWinnerPlayer): Boolean {
        return user.status == 1 || user.status == 2 || user.status == 3//等待 / 游戏中 / 赢家   三种状态下保留几率
                ||
                ((round.status == CoinWinnerState.Rnnouncing.status || round.status == CoinWinnerState.Finished.status)
                        && user.player.userid == (loser?.userid ?: -1))//中途输掉 / 赢家出来  两种状态下, 保留上一次输掉的人的几率
    }

    fun getUserPercent(userid: Int): Float {
        val user = players.find { it.player.userid == userid }
        if (user != null) {
            return user.winRate / 100f
        }

        var totalCoin = round.totalReward
        var userCoin = 0
        players.forEach {
            //上一轮输掉的要可以显示
            if (isUserAvailable(it)) {
                if (it.player.userid == userid) {
                    userCoin = it.betCoin
                }
            } else {
                totalCoin -= it.betCoin
            }
        }
        return userCoin / totalCoin.toFloat()
    }

    fun getAvailablePlayers(): List<CoinWinnerPlayer> {
        return players.filter { isUserAvailable(it) }
    }

    fun getRotationTime(): Int = (round.currentStageEndTimestamp - (SNTPManager.now() / 1000)).toInt()

    private fun getCurrentPercentCoin(): Int {
        var totalCoin = round.totalReward
        players.forEach {
            //上一轮输掉的要可以显示
            if (!isUserAvailable(it)) {
                totalCoin -= it.betCoin
            }
        }
        return totalCoin
    }
}

@Keep
@Serializable
data class CoinWinnerUser(
    @SerialName("age")
    val age: Int = 0,
    @SerialName("avatar_frame")
    val avatarFrame: String = "",
    @SerialName("avatar_url")
    val avatarUrl: String = "",
    @SerialName("country_flag")
    val countryFlag: String = "",
    @SerialName("exp_level_info")
    val expLevelInfo: ExpLevelInfo = ExpLevelInfo(),
    @SerialName("gender")
    val gender: Int = 0,
    @SerialName("height")
    val height: Int = 0,
    @SerialName("level")
    val level: Int = 0,
//            @SerialName("medal")
//            val medal: Any = Any(),
    @SerialName("medal_list")
    val medalList: List<JsonObject> = listOf(),
    @SerialName("nickname")
    val nickname: String = "",
    @SerialName("public_id")
    val publicId: String = "",
    @SerialName("userid")
    val userid: Int = 0,
) {
    @Keep
    @Serializable
    data class ExpLevelInfo(
        @SerialName("charm_level")
        val charmLevel: Int = 0,
        @SerialName("wealth_level")
        val wealthLevel: Int = 0,
    )
}

/**
 * @property player
 * @property betCoin
 * @property winCoin
 * @property status
 *    //1 等待
 *     //2 游戏中
 *     //3 赢家
 *     //4 输家
 *     //10 中止
 * @property betDetails
 * @property loseStageNum
 */
@Keep
@Serializable
data class CoinWinnerPlayer(
    @SerialName("player")
    val player: CoinWinnerUser = CoinWinnerUser(),
    @SerialName("bet_coin")
    val betCoin: Int = 0,
    @SerialName("win_coin")
    val winCoin: Int = 0,
    @SerialName("status")
    val status: Int = 0,
    @SerialName("bet_detail")
    val betDetails: List<Int>,
    @SerialName("lose_stage_num")
    val loseStageNum: Int = -1, //在第几回合输掉了比赛
    @SerialName("win_rate")
    val winRate: Int = 0,
)

enum class CoinWinnerState(val status: Int) {
    Pending(1),//等待玩家加入
    Waiting(2),//2个或2个以上, 等待游戏开始
    Raising(3),//加注中
    Rnnouncing(4),//宣布结果
    Finished(10),//结束
    Abort(20),//中断
    Unknown(-1)//未知
}

