package com.qyqy.cupid.data


import android.os.Parcelable
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
@Serializable
data class ComboBean(
    @SerialName("combo_id")
    val comboId: String = "",
    @SerialName("big_reward")
    val bigReward: Int = 0,
    @SerialName("coin")
    val coin: Int = -1,
    @SerialName("count")
    val count: Int = 0,
    @SerialName("cnt_per_receiver")
    val cntPerReceiver: Int = 0,
    @SerialName("combo_count")
    val comboCount: Int = 0,
    @SerialName("total_coin")
    val totalCoin: Int = 0,
    @SerialName("jackpot")
    val jackpot: Int = 0,
) : Parcelable {
    var gift_id: Int = -1
}