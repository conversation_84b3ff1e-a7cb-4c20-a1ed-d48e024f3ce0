package com.qyqy.cupid.data

import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.INVALID_USER
import com.qyqy.ucoo.account.isBoy
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class CoupleInfo(
    @SerialName("is_cp")
    val isCp: Boolean = false, // false
    @SerialName("private_room_id")
    val privateRoomId: Int? = null, // null
    @SerialName("self_has_cp")
    val selfHasCp: Boolean = false, // false
    @SerialName("self_user_info")
    val selfUserInfo: AppUser = INVALID_USER, // 456
    @SerialName("show_dress")
    val showDress: Boolean = false, // true
    @SerialName("target_user_has_cp")
    val targetHasCp: Boolean = false, // false
    @SerialName("target_user_info")
    val targetUserInfo: AppUser = INVALID_USER, // 133
) {

    val visible: Boolean
        get() = isCp || (!selfHasCp && !targetHasCp && selfUserInfo.gender != targetUserInfo.gender)

    companion object {
        val empty: CoupleInfo
            get() = CoupleInfo()
    }
}
