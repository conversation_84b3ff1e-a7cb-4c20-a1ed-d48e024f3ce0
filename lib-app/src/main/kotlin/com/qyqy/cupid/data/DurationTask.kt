package com.qyqy.cupid.data

import android.os.SystemClock
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import kotlinx.serialization.Serializable

import kotlinx.serialization.SerialName
import kotlinx.serialization.Transient

@Serializable
data class DurationTaskList(@SerialName("task_list") val taskList: List<DurationTaskInfo>?)

@Serializable
data class DurationTaskInfo(@SerialName("task_info") val taskInfo: DurationTask)

@Serializable
data class DurationTask(
    @SerialName("all_finished")
    val allFinished: Boolean = false, // false
    @SerialName("series_id")
    val seriesId: Int = 0, // 22
    @SerialName("series_title")
    val seriesTitle: String = "", // 今日のタスク
    @SerialName("series_type")
    val seriesType: Int = 0, // 4
    val tasks: List<Task> = listOf(),
    @SerialName("today_finished")
    val todayFinished: Boolean = false, // false
) {

    var pausedAdvanced by mutableStateOf(if (todayFinished) true else seriesType == 2)

    @Transient
    var timerElapsedRealtime: Long = tasks.lastOrNull()?.run {
        if (todayFinished) {
            progressValue.times(1000L)
        } else {
            if (seriesType == 2) {
                progressValue.toLong()
            } else {
                SystemClock.elapsedRealtime().minus(progressValue.times(1000))
            }
        }
    } ?: -1
}

@Serializable
data class Task(
    @SerialName("condition_times")
    val conditionTimes: Int = 0, // 3600
    @SerialName("condition_type")
    val conditionType: Int = 0, // 27
    val desc: String = "", // 60分
    val extra: Extra = Extra(),
    val finished: Boolean = false, // false
    val id: Int = 0, // 62
    val prize: String = "", // +100
    @SerialName("prize_type")
    val prizeType: Int = 0, // 7
    val progress: String = "", // 3:05
    @SerialName("progress_value")
    val progressValue: Int = 0, // 185
    val title: String = "", // 今日の合計オンライン時間
) {
    @Transient
    val conditionMinute = conditionTimes.div(60)

    @Transient
    val countDownElapsedRealtime: Long = if (finished || progressValue >= conditionTimes) {
        0
    } else {
        SystemClock.elapsedRealtime().plus(conditionTimes.minus(progressValue).times(1000))
    }
}

@Serializable
data class Extra(
    val hint: String = "",
    @SerialName("prize_icon")
    val prizeIcon: String = "", // https://media.ucoofun.com/opsite/itasks/jp-check-in-12456-diamond.webp
)