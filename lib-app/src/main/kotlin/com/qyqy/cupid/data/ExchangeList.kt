package com.qyqy.cupid.data


import androidx.annotation.Keep
import com.qyqy.ucoo.compose.state.IEmptyOwner
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Keep
@Serializable
data class ExchangeList(
    @SerialName("has_more")
    val hasMore: Boolean = false,
    @SerialName("items")
    val items: List<Item> = listOf()
):IEmptyOwner {
    @Keep
    @Serializable
    data class Item(
        @SerialName("amount")
        val amount: Int = 0,
        @SerialName("cost")
        val cost: Int = 0,
        @SerialName("id")
        val id: Int = 0,
        @SerialName("timestamp")
        val timestamp: Int = 0,
        @SerialName("type")
        val type: Int = 0
    )
}