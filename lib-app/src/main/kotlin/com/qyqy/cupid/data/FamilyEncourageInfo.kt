package com.qyqy.cupid.data


import androidx.annotation.Keep
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Keep
@Serializable
data class FamilyEncourageInfo(
    @SerialName("bonus_amount")
    val bonusAmount: Int = 0,
    @SerialName("bonus_type")
    val bonusType: Int = 0,
    @SerialName("digest")
    val digest: List<Digest> = listOf(),
    @SerialName("popup_msg")
    val popupMsg: List<PopupMsg> = listOf(),
    @SerialName("popup_actions")
    val actions: List<EncourageAction> = listOf()
) {
    @Keep
    @Serializable
    data class Digest(
        @SerialName("client_jump_link")
        val clientJumpLink: String = "",
        @SerialName("rich_text")
        val richText: String = "",
        @SerialName("type")
        val type: Int = 0
    )

    @Keep
    @Serializable
    data class PopupMsg(
        @SerialName("client_jump_link")
        val clientJumpLink: String = "",
        @SerialName("rich_text")
        val richText: String = "",
        @SerialName("type")
        val type: Int = 0
    )

    @Keep
    @Serializable
    data class EncourageAction(
        @SerialName("action_type")
        val actionType: Int = 2,
        @SerialName("action_text")
        val actionText: String = "",
    )
}