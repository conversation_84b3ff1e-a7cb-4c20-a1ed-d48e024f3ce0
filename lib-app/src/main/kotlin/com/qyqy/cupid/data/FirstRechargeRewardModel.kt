package com.qyqy.cupid.data


import android.os.Parcelable
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import androidx.annotation.Keep
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.Transient
import kotlin.text.Typography.dollar

/**
{
"is_enable": true,
"title": "初チャージ特典",
"rights": [
{
"text": "100コイン追加ボーナス",
"icon": "https:\/\/media.ucoofun.com\/opsite\/common\/japan_first_charge_award_coin.png"
},
{
"text": "分币不刷\nメダル",
"icon": "https:\/\/media.ucoofun.com\/opsite\/prop\/icon\/%E5%8B%8B%E7%AB%A0%E5%88%86%E5%B8%81%E4%B8%8D%E5%88%B73x.png"
},
{
"text": "小丑\nギフト",
"icon": "https:\/\/media.ucoofun.com\/opsite\/gift\/icon\/NO.104357__%E5%B0%8F%E4%B8%91%E7%83%AD%E6%B0%94%E7%90%83MP4_h4p0BET.png"
}
],
"version": 2,
"goods": [
{
"fk_channel": 1,
"charge_items": [
{
"product_id": "com.qyqy.ucoo.fcpackage.20250201",
"currency_mark": "￥",
"dollar": "1.99",
"top_rights": {
"icon": "https:\/\/media.ucoofun.com\/opsite%2Fwallet%2Fjp_first_charge_coin_icon.png",
"text": "3000コイン"
},
"sub_rights": [
{
"text": "CP盲盒\nギフト",
"icon": "https:\/\/media.ucoofun.com\/opsite\/gift\/icon\/%E7%9B%B2%E7%9B%92%E7%A4%BC%E7%89%A9%E5%9B%BE%E7%89%87.png?x-oss-process=image\/format,webp",
"value_coin_cnt": 0
},
{
"text": "“龙年好运”\nアイコンフレーム",
"icon": "https:\/\/media.ucoofun.com\/opsite\/prop\/icon\/%E6%B0%94%E6%B3%A1-%E5%86%A0%E5%AF%8C%E8%B1%AA3x_3_o4EaJYm.png?x-oss-process=image\/format,webp",
"value_coin_cnt": 0
},
{
"text": "“UCOO小老板”\nメダル",
"icon": "https:\/\/media.ucoofun.com\/opsite\/prop\/icon\/%E5%8B%8B%E7%AB%A0%E4%B8%BA%E4%BD%A0%E5%85%A5%E8%BF%B713x_1_mO66uxr.png?x-oss-process=image\/format,webp",
"value_coin_cnt": 0
}
],
"currency_number": "15",
"currency_unit": "元",
"fk_link": ""
}
],
"is_others": false,
"name": "Google リチャージ",
"desc": "",
"icon": "https:\/\/media.ucoofun.com\/opsite\/cashier\/charge_googlepay_icon.png",
"call_type": 1,
"discount": null
},
{
"fk_channel": 4,
"charge_items": [
{
"product_id": "ucoo.paypal.fcpackage.20250202",
"dollar": "1.99",
"currency_mark": "￥",
"currency_number": "15",
"currency_unit": "元",
"top_rights": {
"icon": "https:\/\/media.ucoofun.com\/opsite%2Fwallet%2Fjp_first_charge_coin_icon.png",
"text": "3000コイン"
},
"sub_rights": [
{
"text": "CP盲盒\nギフト",
"icon": "https:\/\/media.ucoofun.com\/opsite\/gift\/icon\/%E7%9B%B2%E7%9B%92%E7%A4%BC%E7%89%A9%E5%9B%BE%E7%89%87.png?x-oss-process=image\/format,webp",
"value_coin_cnt": 0
},
{
"text": "“龙年好运”\nアイコンフレーム",
"icon": "https:\/\/media.ucoofun.com\/opsite\/prop\/icon\/%E6%B0%94%E6%B3%A1-%E5%86%A0%E5%AF%8C%E8%B1%AA3x_3_o4EaJYm.png?x-oss-process=image\/format,webp",
"value_coin_cnt": 0
},
{
"text": "“UCOO小老板”\nメダル",
"icon": "https:\/\/media.ucoofun.com\/opsite\/prop\/icon\/%E5%8B%8B%E7%AB%A0%E4%B8%BA%E4%BD%A0%E5%85%A5%E8%BF%B713x_1_mO66uxr.png?x-oss-process=image\/format,webp",
"value_coin_cnt": 0
}
],
"fk_link": ""
}
],
"is_others": false,
"name": "アリペイ",
"desc": "Alipay ユーザーが利用可能",
"icon": "https:\/\/media.ucoofun.com\/opsite\/cashier\/charge_alipay_icon.png",
"call_type": 4,
"discount": null
},
{
"fk_channel": 5,
"charge_items": [
{
"product_id": "ucoo.payermax.fcpackage.20250202",
"dollar": "3.99",
"currency_mark": "$",
"currency_number": "3.99",
"currency_unit": "元",
"top_rights": {
"icon": "https:\/\/media.ucoofun.com\/opsite%2Fwallet%2Fjp_first_charge_coin_icon.png",
"text": "3000コイン"
},
"sub_rights": [
{
"text": "CP盲盒\nギフト",
"icon": "https:\/\/media.ucoofun.com\/opsite\/gift\/icon\/%E7%9B%B2%E7%9B%92%E7%A4%BC%E7%89%A9%E5%9B%BE%E7%89%87.png?x-oss-process=image\/format,webp",
"value_coin_cnt": 0
},
{
"text": "“龙年好运”\nアイコンフレーム",
"icon": "https:\/\/media.ucoofun.com\/opsite\/prop\/icon\/%E6%B0%94%E6%B3%A1-%E5%86%A0%E5%AF%8C%E8%B1%AA3x_3_o4EaJYm.png?x-oss-process=image\/format,webp",
"value_coin_cnt": 0
},
{
"text": "“UCOO小老板”\nメダル",
"icon": "https:\/\/media.ucoofun.com\/opsite\/prop\/icon\/%E5%8B%8B%E7%AB%A0%E4%B8%BA%E4%BD%A0%E5%85%A5%E8%BF%B713x_1_mO66uxr.png?x-oss-process=image\/format,webp",
"value_coin_cnt": 0
}
],
"fk_link": ""
}
],
"is_others": false,
"name": "銀行カード",
"desc": "ビザ\/マスター",
"icon": "https:\/\/media.ucoofun.com\/opsite\/cashier\/charge_card_icon.png",
"call_type": 7,
"discount": null
}
]
}
 */
@Keep
@Serializable
data class FirstRechargeRewardModel(
    @SerialName("goods")
    val payWays: List<PayWay> = emptyList(),
    @SerialName("is_enable")
    val isEnable: Boolean = false,
    @SerialName("rights")
    val rights: List<RewardItem> = emptyList(),
    @SerialName("title")
    val title: String = "",
    @SerialName("version")
    val version: Int = 0,
    @SerialName("order_type")
    val orderType: Int = 0,
    @SerialName("display_cost_japan_coin_cnt")
    val displayCostJapanCoinCnt:Int = 0,
    @SerialName("display_value_japan_coin_cnt")
    val displayValueJapanCoinCnt:Int = 0
) {
    /**
     * 支付方式
     * @property callType
     * @property chargeItems
     * @property desc
     * @property discount
     * @property fkChannel
     * @property icon
     * @property isOthers
     * @property name
     */
    @Keep
    @Serializable
    data class PayWay(
        @SerialName("call_type")
        val callType: Int = 0,
        @SerialName("charge_items")
        val chargeItems: List<ChargeItem> = listOf(),
        @SerialName("desc")
        val desc: String = "",
        @SerialName("discount")
        val discount: Discount? = null,
        @SerialName("fk_channel")
        val fkChannel: Int = 0,
        @SerialName("icon")
        val icon: String = "",
        @SerialName("is_others")
        val isOthers: Boolean = false,
        @SerialName("name")
        val name: String = "",

        ) {
        @Keep
        @Serializable
        data class ChargeItem(
            @SerialName("currency_mark")
            val currencyMark: String = "",
            @SerialName("currency_number")
            val currencyNumber: String = "",
            @SerialName("currency_unit")
            val currencyUnit: String = "",
            @SerialName("dollar")
            val dollar: String = "",
            @SerialName("fk_link")
            val fkLink: String = "",
            @SerialName("product_id")
            val productId: String = "",
            @SerialName("sub_rights")
            val subRights: List<RewardItem> = listOf(),
            @SerialName("top_rights")
            val topRights: RewardItem? = null,
            @Transient
            private var googlePrice: String? = null,
        ) {
            @IgnoredOnParcel
            var transformPrice: String = formatPrice()
                private set

            fun setGooglePrice(price: String) {
                googlePrice = price
                transformPrice = formatPrice()
            }

            private fun formatPrice() = run {
                buildString {
                    if (googlePrice.isNullOrEmpty()) {
                        if (currencyMark.isNotEmpty() && currencyNumber.isNotEmpty()) {
                            append("$currencyMark ")
                            append(currencyNumber)
                        } else {
                            append("$ ")
                            append(Typography.dollar)
                        }
                    } else {
                        append(googlePrice)
                    }
                }
            }
        }
    }

    /**
     * 折扣
     *
     * @property name
     */
    @Parcelize
    @Serializable
    data class Discount(
        val name: String,
    ) : Parcelable

    /**
     * 奖励物品
     *
     * @property icon
     * @property text
     */
    @Keep
    @Serializable
    data class RewardItem(
        @SerialName("icon")
        val icon: String = "",
        @SerialName("text")
        val text: String = ""
    )
}