package com.qyqy.cupid.data


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import androidx.annotation.Keep

@Keep
@Serializable
data class PhrasesSettingBean(
    @SerialName("default_prologue")
    val defaultPrologue: DefaultPrologue = DefaultPrologue(),
    @SerialName("is_left")
    val isLeft: Boolean = false,
    @SerialName("my_prologue")
    val myPrologue: List<MyPrologue> = listOf(),
    @SerialName("prologue_reward")
    val prologueReward: Int = 0,
    @SerialName("usage_url")
    val usageUrl: String = "",
    @SerialName("is_finish_task")
    val isFinishTask: Boolean = true
) {
    @Keep
    @Serializable
    data class DefaultPrologue(
        @SerialName("advice1")
        val advice1: String = "",
        @SerialName("advice2")
        val advice2: String = "",
        @SerialName("advice3")
        val advice3: String = "",
        @SerialName("templates")
        val templates: List<String> = listOf(),
        @SerialName("title")
        val title: String = ""
    )

    @Keep
    @Serializable
    data class MyPrologue(
        @SerialName("content")
        val content: String = "",
        @SerialName("id")
        val id: Int = 0,
        @SerialName("label")
        val label: String = "",
        @SerialName("reply_cnt")
        val replyCnt: Int = 0,
        @SerialName("reply_ratio")
        val replyRatio: String = "",
        @SerialName("send_cnt")
        val sendCnt: Int = 0,
        @SerialName("user_id")
        val userId: Int = 0
    )

    @Keep
    @Serializable
    data class FloatWidgetSetting(
        @SerialName("is_show")
        val isShow: Boolean = false,
        @SerialName("reward")
        val reward: Int = 0,
    )
}