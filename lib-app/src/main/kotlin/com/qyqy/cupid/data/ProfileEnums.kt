package com.qyqy.cupid.data


import android.os.Parcelable
import androidx.compose.runtime.Stable
import com.qyqy.cupid.widgets.wheel.common.core.WheelData
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

typealias ProfileEnum = List<EnumEntity>

@Stable
@Serializable
data class ProfileEnums(
    @SerialName("body_type")
    val bodyType: ProfileEnum = emptyList(),
    @SerialName("dating_hope")
    val datingHope: ProfileEnum = emptyList(),
    @SerialName("educational_history")
    val educationalHistory: ProfileEnum = emptyList(),
    @SerialName("job")
    val job: ProfileEnum = emptyList(),
    @SerialName("marital_history")
    val maritalHistory: ProfileEnum = emptyList(),
    @SerialName("marriage_intention")
    val marriageIntention: ProfileEnum = emptyList(),
    @SerialName("tobacco")
    val tobacco: ProfileEnum = emptyList()
)

@Parcelize
@Serializable
data class EnumEntity(val code: String = "", val name: String = "") : WheelData, Parcelable {
    @IgnoredOnParcel
    override val uniqueId: String = name
}

val EnumEntity.isEdit: Boolean
    get() = code != "0"

@Parcelize
@Serializable
data class NativeProfile(
    @SerialName("city_code")
    val cityCode: EnumEntity = EnumEntity(),
    @SerialName("birth_city_code")
    val birthCityCode: EnumEntity = EnumEntity(),
    @SerialName("body_type")
    val bodyType: EnumEntity = EnumEntity(),
    @SerialName("dating_hope")
    val datingHope: EnumEntity = EnumEntity(),
    @SerialName("educational_history")
    val educationalHistory: EnumEntity = EnumEntity(),
    @SerialName("job")
    val job: EnumEntity = EnumEntity(),
    @SerialName("marital_history")
    val maritalHistory: EnumEntity = EnumEntity(),
    @SerialName("marriage_intention")
    val marriageIntention: EnumEntity = EnumEntity(),
    @SerialName("tobacco")
    val tobacco: EnumEntity = EnumEntity()
) : Parcelable

