package com.qyqy.cupid.data


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import androidx.annotation.Keep

@Keep
@Serializable
data class RealNameTipBean(
    @SerialName("btn_text")
    val btnText: String = "",
    @SerialName("content")
    val content: String = "",
    @SerialName("extra_operation_type")
    val extraOperationType: Int = 0,
    @SerialName("link")
    val link: String = ""
)