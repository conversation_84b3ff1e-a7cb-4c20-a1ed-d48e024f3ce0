package com.qyqy.cupid.data

typealias ResultData<T> = Triple<Boolean, T?, Throwable?>

val ResultData<*>.isLoading: Boolean
    get() = this.first

fun <T> ResultData<T>.data(): T? = this.second

val ResultData<*>.error: Throwable?
    get() = this.third

fun <T> resultData() = ResultData(false, null, null)

fun <T> ResultData<T>.setLoading(isLoading: Boolean) = this.copy(first = isLoading)

fun <T> ResultData<T>.setData(data: T) = this.copy(false, data, null)

fun <T> ResultData<T>.setError(throwable: Throwable) = this.copy(false, null, third = throwable)