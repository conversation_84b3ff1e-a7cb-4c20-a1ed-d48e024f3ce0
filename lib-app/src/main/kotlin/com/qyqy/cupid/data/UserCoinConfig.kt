package com.qyqy.cupid.data


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import androidx.annotation.Keep

@Keep
@Serializable
data class UserCoinConfig(
    @SerialName("game")
    val game: Game = Game(),
    @SerialName("game_enabled")
    val gameEnabled: Boolean = false
) {
    @Keep
    @Serializable
    data class Game(
        @SerialName("items")
        val items: List<Item> = listOf(),
        @SerialName("title")
        val title: String = ""
    ) {
        @Keep
        @Serializable
        data class Item(
            @SerialName("description")
            val description: String = "",
            @SerialName("icon")
            val icon: String = "",
            @SerialName("image")
            val image: String = "",
            @SerialName("link")
            val link: String = "",
            @SerialName("name")
            val name: String = ""
        )
    }
}