package com.qyqy.cupid.data


import androidx.annotation.Keep
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Keep
@Serializable
data class UserIncomeConfig(
    @SerialName("diamond_help")
    val diamondHelp: DiamondHelp = DiamondHelp(),
    @SerialName("invite")
    val invite: Invite = Invite(),
    @SerialName("invite_enabled")
    val inviteEnabled: Boolean = false
) {
    @Keep
    @Serializable
    data class DiamondHelp(
        @SerialName("btn_text")
        val btnText: String = "",
        @SerialName("content")
        val content: String = ""
    )

    @Keep
    @Serializable
    data class Invite(
        @SerialName("desc")
        val desc: String = "",
        @SerialName("link")
        val link: String = "",
        @SerialName("title")
        val title: String = ""
    )
}