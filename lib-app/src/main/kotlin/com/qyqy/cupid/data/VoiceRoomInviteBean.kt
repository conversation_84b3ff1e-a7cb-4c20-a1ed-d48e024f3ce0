package com.qyqy.cupid.data


import android.os.Parcelable
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
@Serializable
data class VoiceRoomInviteBean(
    @SerialName("id")
    val id: Int = -1,
    @SerialName("inuse_mic_cnt")
    val inuseMicCnt: Int = 0,
    @SerialName("member_cnt")
    val memberCnt: Int = 0,
    @SerialName("owner")
    val owner: Owner = Owner(),
    @SerialName("recommend_user_list")
    val recommendUserList: List<RecommendUser> = listOf(),
    @SerialName("room_mode")
    val roomMode: Int = 0,
    @SerialName("title")
    val title: String = ""
) : Parcelable {
    @Keep
    @Parcelize
    @Serializable
    data class Owner(
        @SerialName("avatar_url")
        val avatarUrl: String = "",
        @SerialName("nickname")
        val nickname: String = "",
        @SerialName("userid")
        val userid: Int = 0
    ) : Parcelable

    @Keep
    @Parcelize
    @Serializable
    data class RecommendUser(
        @SerialName("avatar_url")
        val avatarUrl: String = "",
        @SerialName("gender")
        val gender: Int = 0,
        @SerialName("nickname")
        val nickname: String = "",
        @SerialName("userid")
        val userid: Int = 0
    ) : Parcelable
}