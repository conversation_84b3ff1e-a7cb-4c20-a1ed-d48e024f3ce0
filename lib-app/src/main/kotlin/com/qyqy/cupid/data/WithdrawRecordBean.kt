package com.qyqy.cupid.data


import androidx.annotation.Keep
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Keep
@Serializable
data class WithdrawRecordBean(
    @SerialName("amount")
    val amount: String = "0.00",
    @SerialName("create_time")
    val createTime: Long = 0,
    @SerialName("fee")
    val fee: String = "0.00",
    @SerialName("final_amount")
    val finalAmount: String = "0.00",
    @SerialName("id")
    val id: Int = 0,
    @SerialName("status")
    val status: Int = 0
) {

}