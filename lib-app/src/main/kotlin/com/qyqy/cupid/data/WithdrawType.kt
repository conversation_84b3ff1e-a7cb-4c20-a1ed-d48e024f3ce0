package com.qyqy.cupid.data


import androidx.annotation.Keep
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject

@Keep
@Serializable
data class WithdrawList(
    @SerialName("withdraw_types")
    val data: List<WithdrawType> = listOf(),
    @SerialName("hint")
    val hint: String
) {
    @Keep
    @Serializable
    data class WithdrawType(
        @SerialName("account")
        val account: JsonObject? = null,
        @SerialName("desc")
        val desc: String = "",
        @SerialName("icon")
        val icon: String = "",
        @SerialName("id")
        val id: Int = 0,
        @SerialName("name")
        val name: String = "",
        @SerialName("type")
        val type: Int = 0,
        @SerialName("jump_link")
        val jumpLink: String = ""
    )
}