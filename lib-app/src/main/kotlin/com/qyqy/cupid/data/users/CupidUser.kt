package com.qyqy.cupid.data.users

import com.qyqy.ucoo.account.Album
import com.qyqy.ucoo.account.BasicUser
import com.qyqy.ucoo.http.parseValue
import com.qyqy.ucoo.http.sAppJson
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement



@Serializable
data class BasicUserImpl(
    override val id: String = "",
    override val nickname: String,
    override val gender: Int = 0,
    override val birthday: String = "",
    override val age: Int = 0,
    override val height: Int = 0,
    @SerialName("avatar_url")
    override val avatarUrl: String = ""
) : BasicUser

interface JsonObjectWrapper {
    val jsonObject: JsonObject
}

interface IAlbumHolder : JsonObjectWrapper {
    val album: List<Album>
}

class AlbumHolder(
    override val jsonObject: JsonObject,
    override val album: List<Album> = jsonObject.parseValue<List<Album>>("album", emptyList())
) : IAlbumHolder

open class UserObject(override val jsonObject: JsonObject) : JsonObjectWrapper,
    BasicUser by sAppJson.decodeFromJsonElement<BasicUserImpl>(jsonObject)




