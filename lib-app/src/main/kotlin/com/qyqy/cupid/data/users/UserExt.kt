package com.qyqy.cupid.data.users

import androidx.annotation.StringRes
import com.qyqy.cupid.data.isEdit
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.BasicUser
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.isSelf

@get:StringRes
val BasicUser.introEmptyRes: Int
    get() = if (isSelf) {
        R.string.cupid_hint_intro
    } else {
        if (isBoy) R.string.cupid_male_no_intro else R.string.cupid_female_no_intro
    }

val User.completeCountRateStr: String
    get() {
        var completeCount = 0
        if (height > 0) {
            completeCount++
        }
        nativeProfile?.let {
            if (it.job.isEdit) {
                completeCount++
            }
            if (it.tobacco.isEdit) {
                completeCount++
            }
            if (it.cityCode.isEdit) {
                completeCount++
            }
            if (it.bodyType.isEdit) {
                completeCount++
            }
            if (it.datingHope.isEdit) {
                completeCount++
            }
            if (it.maritalHistory.isEdit) {
                completeCount++
            }
            if (it.marriageIntention.isEdit) {
                completeCount++
            }
            if (it.birthCityCode.isEdit) {
                completeCount++
            }
            if (it.educationalHistory.isEdit) {
                completeCount++
            }
        }

        return "$completeCount/10"
    }