package com.qyqy.cupid.event

object IMEvent {

    // 完成任务活动奖励
    const val TASK_REWARD = "japan_task_finished"

    const val REGISTER_MALE_REWARD = "japan_male_register_bonus_popup"
    const val REGISTER_FEMALE_REWARD = "japan_female_register_bonus_popup"

    const val EXCHANGE_CONTACT = "exchange_contact"

    const val FAMILY_MIC_TASK_IN_VOICE_ROOM = "tribe_common_mic_status_sync"

    const val FAMILY_MIC_TASK_IN_VOICE_ROOM_DONE = "tribe_common_mic_task_done"

    const val JAPAN_FIRST_CHARGE_AWARD = "japan_first_charge_award"

    const val PRIVATECHAT_JP_FEMALE_FIRST_REPLY_MSG = "privatechat_jp_female_first_reply_msg"

    const val JAPAN_WITHDRAW_COUPON_CODE = "japan_withdraw_coupon_code"

    const val JAPAN_AUDIOROOM_OWNER_GET_MIC_POPUP_SHARE = "japan_audioroom_owner_get_mic_popup_share"

    const val PRIVATECHAT_JP_INTIMATE_GIFT_MSG = "privatechat_jp_intimate_gift_msg"

    const val PRIVATECHAT_JP_TRANSFER_RECORD = "transfer_record"

    const val VOICE_MIC_ABANDONED = "mic_abandoned"

    const val CP_INTER_PRIVATE_ROOM = "cp_inter_private_room"

    const val AUDIOROOM_WISHLIST_CHANGE = "audioroom_wishlist_change"

    const val INTIMATE_SCORE_CHANGE = "intimate_score_change"
}
