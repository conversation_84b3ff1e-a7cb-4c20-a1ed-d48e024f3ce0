package com.qyqy.cupid.event

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.dp
import com.qyqy.cupid.data.AudioRoomAudiencePopup
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.CupidViewModel
import com.qyqy.cupid.ui.dialog.AlertDialogContent
import com.qyqy.cupid.ui.dialog.ContentAlertDialog
import com.qyqy.cupid.ui.dialog.CupidTaskRewardDialog
import com.qyqy.cupid.ui.dialog.DialogButton
import com.qyqy.cupid.ui.dialog.DialogQueue
import com.qyqy.cupid.ui.dialog.JoinToCoupleRoom
import com.qyqy.cupid.ui.dialog.LiveDailyTaskDialog
import com.qyqy.cupid.ui.dialog.RegisterRewardDialog
import com.qyqy.cupid.ui.dialog.TaskRewardDialogContent
import com.qyqy.cupid.widgets.RecommendUserWidget
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.INVALID_USER
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.presentation.sign_tasks.SignTaskManager
import com.qyqy.ucoo.compose.ui.DirectionState
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.WatchRecvNewCustomMessage
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.utils.takeIsNotEmpty

@Composable
fun RegisterMessagePushGlobalDialog(viewModel: CupidViewModel) {
    val updatedViewModel by rememberUpdatedState(newValue = viewModel)
    WatchRecvNewCustomMessage {
        handleGlobalMessage(updatedViewModel, it)
    }
}

private fun handleGlobalMessage(viewModel: CupidViewModel, msg: UCCustomMessage) {
    val appNavController = viewModel.appNavController
    val dialogQueue = viewModel.dialogQueue
    val voiceLiveHelper = viewModel.voiceLiveHelper
    val isSystemMessage = msg.isSystemMsg

    when (msg.cmd) {
        IMEvent.TASK_REWARD -> {
            if (!isSystemMessage) {
                return
            }
            showTaskRewardDialog(dialogQueue, msg)
        }

        IMEvent.REGISTER_MALE_REWARD -> {
            if (!isSystemMessage) {
                return
            }
            showRegisterRewardDialog(dialogQueue, false, msg)
        }

        IMEvent.REGISTER_FEMALE_REWARD -> {
            if (!isSystemMessage) {
                return
            }
            showRegisterRewardDialog(dialogQueue, true, msg)
        }

        IMEvent.FAMILY_MIC_TASK_IN_VOICE_ROOM_DONE -> {
            if (!isSystemMessage) {
                return
            }
            showFamilyTaskRewardDialog(dialogQueue, msg)
        }
//2.40.0 与iOS同步后发现此弹窗不需要, 信令携带的信息仅作为消息展示
        IMEvent.JAPAN_FIRST_CHARGE_AWARD -> {
            UIConfig.firstRechargeRewardState.value = null
        }

        IMEvent.PRIVATECHAT_JP_FEMALE_FIRST_REPLY_MSG -> {
            dialogQueue.pushCenterDialog { dialog, _ ->
                TaskRewardDialogContent(
                    rewardStr = msg.getJsonInt("amt", 0).toString(),
                    rewardTips = msg.getJsonString("content").orEmpty(),
                    rewardIcon = msg.getJsonString("image").takeIsNotEmpty() ?: R.drawable.ic_cpd_task_diamond,
                    button = DialogButton(msg.getJsonString("btn_text").orEmpty()) {
                        dialog.dismiss()
                        appNavController.navigateByLink(msg.getJsonString("link").orEmpty())
                    }
                )
            }
        }

        IMEvent.CP_INTER_PRIVATE_ROOM -> {
            if (!isSystemMessage) {
                return
            }
            val roomId = msg.getJsonInt("private_room_id") ?: return
            val user = (msg.getJsonValue<AppUser>("newbie_user")?.takeIf { !it.isSelf } ?: msg.getJsonValue<AppUser>("service_user")?.takeIf { !it.isSelf }) ?: return
            dialogQueue.pushCenterDialog(true) { dialog, _ ->
                JoinToCoupleRoom(user) {
                    dialog.dismiss()
                    voiceLiveHelper.joinVoiceLiveRoom(roomId, isPrivate = true)
                }
            }
        }

        //家族相关
        MsgEventCmd.AUDIOROOM_AUDIENCE_TASK_BONUS_POPUP -> {
            val bean = msg.parseDataJson<AudioRoomAudiencePopup>() ?: return
            dialogQueue.push(LiveDailyTaskDialog(bean), true)
        }

        MsgEventCmd.privatechat_recommend_popup -> {

            val cityLabel = msg.getJsonString("city_label")

            val desc = msg.getJsonString("desc").orEmpty()

            val career = msg.getJsonString("career")

            val user = msg.getJsonValue<AppUser>("user", INVALID_USER)

            val list = buildList {
                if (!cityLabel.isNullOrEmpty()) {
                    add(cityLabel)
                }

                if (user.height > 0) {
                    add("${user.height}cm")
                }

                if (!career.isNullOrEmpty()) {
                    add(career)
                }
            }

            dialogQueue.push(direction = DirectionState.TOP, immediatelyShow = true) { dialog, onAction ->
                RecommendUserWidget(
                    user = user,
                    desc = desc,
                    labelList = list,
                    buttonText = if (user.isBoy) stringResource(id = R.string.cpd去聊天) else stringResource(id = R.string.cpd接受邀请),
                    onDismiss = {
                        dialog.dismiss()
                    }
                ) {
                    dialog.dismiss()
                    AppLinkManager.controller?.navigate(CupidRouters.C2CChat, arguments = mapOf("user" to user))
                }
            }
        }
    }
}

private fun showTaskRewardDialog(dialogQueue: DialogQueue<*>, msg: UCCustomMessage) {
    val rewardType = msg.getJsonInt("prize_type")
    val rewardValue = msg.getJsonString("prize_info").orEmpty()
    val rewardDesc = msg.getJsonString("message").orEmpty()
    if (rewardType == 5 || rewardType == 7) {
        SignTaskManager.refreshTasks()
        dialogQueue.push(
            CupidTaskRewardDialog(
                rewardImage = if (rewardType == 5) {
                    R.drawable.ic_cpd_task_coin
                } else {
                    R.drawable.ic_cpd_task_diamond
                }, rewardValue = rewardValue, rewardDesc = rewardDesc
            )
        )
    }
}

private fun showRegisterRewardDialog(dialogQueue: DialogQueue<*>, isFemale: Boolean, msg: UCCustomMessage) {
    if (isFemale) {
        val title = msg.getJsonString("desc").orEmpty()
        val button = msg.getJsonString("btn_text").orEmpty()
        val count = msg.getJsonInt("count", 0)
        val mainIMG = msg.getJsonString("main_img").orEmpty()
        val link = msg.getJsonString("link").orEmpty()
        dialogQueue.push(
            RegisterRewardDialog(
                title = title,
                button = button,
                count = count,
                isFemale = isFemale,
                link = link,
                backgroundIMG = mainIMG
            )
        )
    } else {
        val title = msg.getJsonString("desc").orEmpty()
        val button = msg.getJsonString("btn_text").orEmpty()
        val count = msg.getJsonInt("count", 0)
        dialogQueue.push(RegisterRewardDialog(title = title, button = button, count = count, isFemale = isFemale))
    }
}

private fun showFamilyTaskRewardDialog(dialogQueue: DialogQueue<*>, msg: UCCustomMessage) {
    dialogQueue.pushCenterDialog { dialog, onAction ->
        AlertDialogContent(
            icon = {
                Image(
                    painter = painterResource(id = R.drawable.ic_cpd_experience),
                    contentDescription = null,
                    modifier = Modifier.size(48.dp)
                )
            },
            title = msg.getJsonString("title")?.let {
                buildAnnotatedString { append(it) }
            },
            content = msg.getJsonString("desc")?.let {
                buildAnnotatedString { append(it) }
            },
            startButtonText = null,
            startButtonClick = null,
            endButtonText = stringResource(id = R.string.cpd我知道了),
            endButtonClick = { dialog.dismiss() },
            iconSetTop = false,
        )
    }
}