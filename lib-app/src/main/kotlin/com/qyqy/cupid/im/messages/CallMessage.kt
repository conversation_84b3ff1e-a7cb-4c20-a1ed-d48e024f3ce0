package com.qyqy.cupid.im.messages

import android.content.res.Configuration
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.data.CallMode
import com.qyqy.cupid.ui.IC2CIMAction
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.cupid.ui.home.message.icons.ActionIcons
import com.qyqy.cupid.ui.home.message.icons.VideoCall
import com.qyqy.cupid.ui.home.message.icons.VoiceCall
import com.qyqy.cupid.ui.home.message.icons.VoiceHangup
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.compose.isEditOnCompose
import com.qyqy.ucoo.compose.presentation.room.RichText
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.UCVideoCallMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.im.message.entity.RichItem
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.user.video.ICallSettlement


data object CallMessageContent : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCVideoCallMessage.Other>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCVideoCallMessage.Other>, onAction: IIMAction) {
        C2CCallContent(entry, onAction)
    }
}


/**
 * 通话消息
 */
@Composable
private fun C2CCallContent(entry: UIMessageEntry<UCVideoCallMessage.Other>, onAction: IIMAction = IIMAction.Empty) {
    MessageScaffold(entry = entry, showReadStatus = false, onAction = onAction) {
        val message = entry.message
        val isVoice = message.notice.isVoice
        val settlement: ICallSettlement = message.notice
        C2CMessageThemeBubble(entry = entry) {
            if (message.isSelf) {
                Text(
                    text = when (settlement.reasonCode) {
                        -50 -> stringResource(id = R.string.cpd未接通)
                        -60 -> stringResource(id = R.string.cpd已取消)
                        -70 -> stringResource(id = R.string.cpd对方已挂断)
                        else -> settlement.reason.ifEmpty {
                            stringResource(id = R.string.cpd通话时长)
                        }
                    },
                    fontSize = 14.sp,
                )
                Spacer(modifier = Modifier.width(4.dp))
                Icon(
                    imageVector = if (isVoice) ActionIcons.VoiceHangup else ActionIcons.VideoCall,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp),
                )
            } else {
                Text(
                    text = when (settlement.reasonCode) {
                        -50 -> stringResource(id = R.string.cpd未接通)
                        -60 -> stringResource(id = R.string.cpd对方已取消)
                        -70 -> stringResource(id = R.string.cpd已挂断)
                        else -> settlement.reason.ifEmpty {
                            stringResource(id = R.string.cpd通话时长)
                        }
                    },
                    fontSize = 14.sp,
                )
                Spacer(modifier = Modifier.width(4.dp))
                Icon(
                    imageVector = if (isVoice) ActionIcons.VoiceHangup else ActionIcons.VideoCall,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp),
                )
            }
        }
    }
}


@Preview
@Composable
private fun PreviewCall() {
//    C2CCallContent(
//        Message.obtain(
//            "1",
//            Conversation.ConversationType.PRIVATE,
//            AppPersistMessage.obtain(MsgEventCmd.VIDEO_CALL_FINISH, "哈哈哈哈")
//        ).toMessageItem(userForPreview)!!
//    )
}

data object StartCallTipMessageContent : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCCustomMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction) {
        StartCallTipContent(entry, onAction)
    }
}

/**
 * 通话消息
 */
@Composable
private fun StartCallTipContent(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction = IIMAction.Empty) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 36.dp),
        contentAlignment = Alignment.Center
    ) {
        val message = entry.message
        val targetUser = message.getJsonValue<AppUser>("video_hqu") ?: message.getJsonValue<AppUser>("hqu")
        val isVideoHqu = targetUser?.id == sUser.id

        val richHint = remember {
            message.getJsonValue<List<RichItem>>(
                if (isVideoHqu) "video_hqu_hint_rich_text" else "normal_user_hint_rich_text",
                emptyList()
            )
        }

        val richButton = if (isEditOnCompose) {
            "发起语音"
        } else {
            remember {
                message.getJsonString(if (isVideoHqu) "video_hqu_btn_text" else "normal_user_btn_text").orEmpty()
            }
        }


        val isVoice = remember {
            message.getJsonBoolean("only_audio", true)
        }
//        1、增加参数 only_audio (bool值)，表示是否是语音通话。
//        2、增加参数 normal_user_btn_text、video_hqu_btn_text，表示按钮文本。
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White, RoundedCornerShape(12.dp))
                .padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            if (isEditOnCompose) {
                Text(
                    text = "恭喜你，亲密度达到688，已解锁语音通话功能，快去发起语音通话和ta畅聊吧，每满1分钟消耗35金币",
                    color = Color(0xFF4E5969),
                    fontSize = 14.sp
                )
            } else {
                RichText(rich = richHint, color = Color(0xFF4E5969), fontSize = 14.sp)
            }


            Row(
                modifier = Modifier
                    .padding(top = 12.dp, bottom = 4.dp)
                    .sizeIn(minWidth = 180.dp, minHeight = 36.dp)
                    .clip(RoundedCornerShape(18.dp))
                    .background(Color(0xFFFF5E8B))
                    .clickable {
                        (onAction as? IC2CIMAction)?.onStartC2CCall(if (isVoice) CallMode.OnlyVoice else CallMode.OnlyVideo)
                    }
                    .padding(horizontal = 10.dp, vertical = 6.dp),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = if (isVoice) ActionIcons.VoiceCall else ActionIcons.VideoCall,
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(16.dp, 16.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(text = richButton, fontSize = 14.sp, color = Color.White, textAlign = TextAlign.Center)
            }

        }
    }
}

@Preview(uiMode = Configuration.UI_MODE_NIGHT_YES, showBackground = true)
@Composable
private fun PreviewStartCallTipContent() {
//    StartCallTipContent(
//        Message.obtain(
//            "1",
//            Conversation.ConversationType.PRIVATE,
//            AppPersistMessage.obtain(MsgEventCmd.GUIDE_VIDEO_CHAT, "哈哈哈哈")
//        ).toMessageItem(userForPreview)!!
//    )
}