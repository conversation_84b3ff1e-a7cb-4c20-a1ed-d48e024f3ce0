package com.qyqy.cupid.im.messages

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.data.CoinWinnerDetail
import com.qyqy.cupid.data.CoinWinnerUser
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.compose.presentation.room.SpanText
import com.qyqy.ucoo.compose.presentation.room.buildTextSpan
import com.qyqy.ucoo.compose.presentation.room.inlineTextContent
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.sUser

object CoinPKMessage : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCCustomMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction) {
        val msg = entry.message
        when (msg.cmd) {
            MsgEventCmd.COIN_PK_CREATE -> {
                val coinWinnerDetail = msg.parseDataJson<CoinWinnerDetail>() ?: return
                CoinPKCreateWidget(coinWinnerDetail, onAction)
            }

            MsgEventCmd.COIN_PK_WIN -> {
                val winPlayer = msg.getJsonValue<CoinWinnerUser>("win_player")
                val rewardCoin = msg.getJsonInt("reward_coin", 0)
                CoinPKWinWidget(winPlayer, false, rewardCoin)
            }

            MsgEventCmd.COIN_PK_OWNER_REWARD -> {
                val ownerPlayer = msg.getJsonValue<CoinWinnerUser>("owner")
                val rewardCoin = msg.getJsonInt("bonus_coin", 0)
                if (ownerPlayer?.userid == sUser.userId) {
                    CoinPKWinWidget(ownerPlayer, true, rewardCoin)
                }
            }

            else -> {}
        }
    }
}

@Composable
private fun CoinPKCreateWidget(info: CoinWinnerDetail, onAction: IIMAction? = null) {
    val density = LocalDensity.current
    SpanText(
        textSpan = buildTextSpan {
            withStyle(SpanStyle(color = Color(0xFF12E08F))) {
                append(stringResource(id = R.string.cpd房主开启游戏))
            }
            it.appendInlineContent(inlineTextContent(
                key = "click", density = density, width = 54, height = 16, paddingValues = PaddingValues(start = 4.dp)
            ) { modifier ->
                Box(
                    modifier = Modifier
                        .heightIn(16.dp)
                        .background(
                            brush = Brush.horizontalGradient(
                                listOf(Color(0xFF945EFF), Color(0xFFD55EFF))
                            ), shape = CircleShape
                        )
                        .padding(horizontal = 4.dp)
                        .click {
                            onAction?.onClickCoinPKItem(info.round.id)
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        stringResource(id = R.string.cpd加入游戏),
                        color = Color.White,
                        fontSize = 10.sp,
                        lineHeight = 10.sp
                    )
                }
            })
        }, modifier = Modifier
            .fillMaxWidth(0.75f)
            .background(color = Color(0x33000000), shape = RoundedCornerShape(8.dp))
            .padding(8.dp), fontSize = 14.sp
    )
}

@Composable
private fun CoinPKWinWidget(winPlayer: CoinWinnerUser?, isOwner: Boolean, coin: Int) {
    SpanText(
        textSpan = buildTextSpan {
            if (isOwner) {
                val text = stringResource(id = R.string.cpd恭喜房主在游戏中获得, coin)
                append(text)
                val start = text.indexOf(coin.toString(), 0)
                val end = start + coin.toString().length

                addStyle(SpanStyle(color = Color(0xFFFFE974)), start, end)
            } else {
                val userName = winPlayer?.nickname ?: "unknown user"
                val text = stringResource(id = R.string.cpd恭喜某人在游戏中获得, userName, coin)
                append(text)

                val coinStart = text.indexOf(coin.toString(), 0)
                val coinEnd = coinStart + coin.toString().length

                addStyle(SpanStyle(color = Color(0xFFFFE974)), coinStart, coinEnd)

                val nameStart = text.indexOf(userName, 0)
                val nameEnd = nameStart + userName.length

                addStyle(SpanStyle(color = Color(0xFFFFE974)), nameStart, nameEnd)
            }
        }, modifier = Modifier
            .fillMaxWidth(0.75f)
            .background(color = Color(0x33000000), shape = RoundedCornerShape(8.dp))
            .padding(8.dp),
        color = Color.White, fontSize = 14.sp, lineHeight = 14.sp
    )
}

@Composable
@Preview
private fun CoinPKPreview() {
    Column {
        CoinPKCreateWidget(CoinWinnerDetail())
        Spacer(modifier = Modifier.height(20.dp))
        CoinPKWinWidget(CoinWinnerUser(nickname = "幼儿园班花"), false, 100)
        Spacer(modifier = Modifier.height(10.dp))
        CoinPKWinWidget(CoinWinnerUser(nickname = "房主"), true, 10)
    }
}