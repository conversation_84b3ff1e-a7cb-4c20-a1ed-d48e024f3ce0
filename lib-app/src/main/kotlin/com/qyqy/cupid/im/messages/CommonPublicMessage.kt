package com.qyqy.cupid.im.messages

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.ColorPainter
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.ucoo.compose.presentation.room.RichText
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.im.message.entity.UserRichList

data object CommonPublicMessage : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCCustomMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction) {
        val message = entry.message
        val list = message.getJsonValue<List<UserRichList>>("messages")
        val richList = list?.getOrNull(entry.requireMsgIndex)?.richText ?: return
        val fontSize = with(LocalDensity.current) {
            14.dp.toPx().toSp()
        }
        RoomMessageLayout(entry = entry, onAction = onAction) {
            MessageThemeBubble(
                entry = entry,
                defaultMsgTheme = MessageTheme(
                    painter = ColorPainter(Color(0x33000000)),
                    paddingValues = PaddingValues(horizontal = 8.dp, vertical = 13.dp),
                    shape = Shapes.small,
                    contentColor = Color.White,
                    fontSize = fontSize,
                    left = true
                )
            ) {
                RichText(rich = richList, fontSize = 14.sp)
            }
        }
    }
}