package com.qyqy.cupid.im.messages

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.utils.OnClick

data object CpdRedPacketMessage : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCCustomMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction) {
        val message = entry.message
        val greetings = message.getJsonString("red_packet_greets").orEmpty()
        RoomMessageLayout(entry = entry, onAction = onAction) {
            RedPacketItem(title = greetings) {
                (onAction as? IVoiceLiveAction)?.onMessageClicked(message)
            }
        }
    }
}

@Preview
@Composable
private fun Preview() {
    RedPacketItem("Hello")
}

@Composable
private fun RedPacketItem(title: String, onClick: OnClick = {}) {
    Row(
        modifier = Modifier
            .width(200.dp)
            .noEffectClickable(onClick = onClick)
            .background(Brush.linearGradient(listOf(Color(0xFFFFE07C), Color(0xFFFF1E6F))), RoundedCornerShape(6.dp))
            .padding(vertical = 10.dp)
            .padding(end = 10.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(id = R.drawable.cpd_ic_small_rp),
            contentDescription = "rp",
            modifier = Modifier.size(36.dp)
        )
        Column(modifier = Modifier) {
            Text(text = title, fontSize = 12.sp, color = Color.White)
            Spacer(modifier = Modifier.height(2.dp))
            Text(text = stringResource(id = R.string.cpd_get_red_package), fontSize = 10.sp, color = Color.White)
        }
    }
}