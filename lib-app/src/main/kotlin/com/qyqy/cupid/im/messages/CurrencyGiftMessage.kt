package com.qyqy.cupid.im.messages

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry

data object CurrencyGiftMessageContent : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCCustomMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction) {
        val message = entry.message
        MessageScaffold(entry = entry, onAction = onAction) {
            val targetUser = message.getJsonValue<AppUser>("to_user")
            val isDiamond = message.getJsonInt("account_type") == 22
            val value = message.getJsonInt("actual_transfer_cnt", 0)

            val shape = if (entry.isSelf) {
                RoundedCornerShape(topStart = 12.dp, topEnd = 0.dp, bottomEnd = 12.dp, bottomStart = 12.dp)
            } else {
                RoundedCornerShape(topStart = 0.dp, topEnd = 12.dp, bottomEnd = 12.dp, bottomStart = 12.dp)
            }

            Row(
                modifier = Modifier
                    .height(72.dp)
                    .widthIn(max = 275.dp, min = 204.dp)
                    .background(
                        brush = Brush.horizontalGradient(
                            if (isDiamond) {
                                listOf(Color(0xFFFFF3F9), Color(0xFFFFE0EF))
                            } else {
                                listOf(Color(0xFFFFFCF3), Color(0xFFFFFAE0))
                            }
                        ),
                        shape = shape
                    )
                    .border(0.5.dp, if (isDiamond) Color(0xFFFFC8E9) else Color(0xFFFFB71A), shape),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(id = if (!isDiamond) R.drawable.ic_cpd_currency_coin else R.drawable.ic_cpd_currency_diamond),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(start = 8.dp)
                        .size(48.dp)
                )

                Column(
                    modifier = Modifier
                        .align(Alignment.CenterVertically)
                        .padding(start = 7.dp, end = 12.dp)
                ) {
                    Text(
                        text = buildAnnotatedString {
                            append(stringResource(id = R.string.cpd送给))
                            val nickname = targetUser?.nickname.orEmpty()
                            withStyle(SpanStyle(color = if (!isDiamond) Color(0xFFFFB71A) else Color(0xFFFF5E8B))) {
                                append(" $nickname")
                            }
                        },
                        fontSize = 16.sp,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )

                    Spacer(modifier = Modifier.height(3.5.dp))

                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Text(
                            text = "$value",
                            color = if (!isDiamond) Color(0xFFFFB71A) else Color(0xFFFF5E8B),
                            fontSize = 12.sp,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )

                        Image(
                            painter = painterResource(id = if (!isDiamond) R.drawable.ic_cpd_coin else R.drawable.ic_cpd_diamond),
                            contentDescription = null,
                            modifier = Modifier
                                .padding(start = 3.dp)
                                .size(12.dp)
                        )
                    }
                }
            }
        }
    }
}
