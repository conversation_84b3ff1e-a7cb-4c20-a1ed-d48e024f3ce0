package com.qyqy.cupid.im.messages

import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.IC2CAction
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.utils.takeIsNotEmpty


data object ExchangeLineMessageContent : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCCustomMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction) {
        C2CExchangeLine(entry, modifier = Modifier.noEffectClickable(enabled = !entry.isSelf) {
            (onAction as? IC2CAction)?.onShowExchangeLineRequestDialog()
        }, onAction)
    }
}

/**
 * 单聊文本消息
 */

@Composable
private fun C2CExchangeLine(
    entry: UIMessageEntry<UCCustomMessage>,
    modifier: Modifier = Modifier,
    onAction: IIMAction = IIMAction.Empty,
) {
    MessageScaffold(entry = entry, onAction = onAction) {
        C2CMessageThemeBubble(
            entry = entry,
            modifier = modifier,
            bubbleColor = Color.White,
            bubbleContentColor = Color(0xFF1D2129),
        ) {
            val extraMessage = entry.message
            ComposeImage(
                model = extraMessage.getJsonString("icon").takeIsNotEmpty() ?: painterResource(id = R.drawable.ic_cpd_line_app),
                modifier = Modifier
                    .padding(end = 8.dp)
                    .size(48.dp),
            )
            AppText(
                text = extraMessage.getJsonString("text").takeIsNotEmpty() ?: "LINEの連絡先交換を申請",
                fontSize = 14.sp,
            )
        }
    }
}


@Preview
@Composable
private fun PreviewC2CExchangeLine() {
//    C2CExchangeLine(
//        Message.obtain(
//            "1",
//            Conversation.ConversationType.PRIVATE,
//            AppPersistMessage.obtain(IMEvent.EXCHANGE_CONTACT, "")
//        ).toMessageItem(userForPreview)!!
//    )
}