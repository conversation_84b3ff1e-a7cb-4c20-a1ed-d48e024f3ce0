package com.qyqy.cupid.im.messages

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.GiftExtra
import com.qyqy.cupid.ui.IC2CAction
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.cupid.ui.home.message.icons.ActionIcons
import com.qyqy.cupid.ui.home.message.icons.Gift
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.http.getBoolOrNull
import com.qyqy.ucoo.im.bean.CPGift
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.im.compat.getExtraString
import com.qyqy.ucoo.sUser
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject


data object GiftGuideMessageContent : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCCustomMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction) {
        GiftGuideContent(entry, onAction)
    }
}

/**
 * 单聊文本消息
 */

@Composable
private fun GiftGuideContent(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction = IIMAction.Empty) {
    val message = entry.message
    Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.TopCenter) {
        Column(
            modifier = Modifier
                .fillMaxWidth(0.84f)
                .widthIn(max = 310.dp)
                .background(Color.White, RoundedCornerShape(12.dp))
                .padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            val content = remember {
                message.getJsonString("content").orEmpty()
            }
            val buttonText = remember {
                message.getJsonString("btn_text").orEmpty()
            }
            val imageIcon = remember {
                message.getJsonString("image").orEmpty()
            }

            val hasSent = remember(entry.refresh) {
                val receiverExtra = message.getExtraString("receiver_extra")
                if (receiverExtra.isNotBlank()) {
                    val receiverJsonObj = Json.decodeFromString<JsonObject>(receiverExtra)
                    receiverJsonObj.getBoolOrNull("has_sent") ?: false
                } else {
                    false
                }
            }

            ComposeImage(model = imageIcon, modifier = Modifier.size(56.dp))

            Text(
                text = content,
                color = Color(0xFF4E5969),
                fontSize = 14.sp,
            )

            Row(
                modifier = Modifier
                    .widthIn(min = 180.dp)
                    .height(36.dp)
                    .background(color = if (hasSent) Color(0xFFE5E6EB) else Color(0xFFFF5E8B), shape = CircleShape)
                    .noEffectClickable(enabled = !hasSent) {
                        message
                            .getJsonValue<CPGift>("gift_info")
                            ?.also {
                                (onAction as? IC2CAction)?.onSendGift(it, 1, GiftExtra(isIntimate = !sUser.isBoy))
                            }
                    }
                    .padding(horizontal = 10.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(5.dp, Alignment.CenterHorizontally)
            ) {

                if (!hasSent) {
                    Icon(
                        imageVector = ActionIcons.Gift,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp),
                        tint = Color.White
                    )
                }

                Text(
                    text = if (hasSent) stringResource(id = R.string.cpd已赠送) else buttonText,
                    color = if (hasSent) Color(0xFF86909C) else Color.White,
                    fontSize = 14.sp,
                )
            }
        }
    }
}


@Preview
@Composable
private fun PreviewGiftGuideContent() {
//    PreviewCupidTheme {
//        GiftGuideContent(
//            Message.obtain(
//                "1",
//                Conversation.ConversationType.PRIVATE,
//                AppPersistMessage.obtain(MsgEventCmd.PRIVATE_SYSTEM_HINT_V2, "哈哈哈哈")
//            ).toMessageItem(userForPreview)!!
//        )
//    }
}