package com.qyqy.cupid.im.messages

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.im.compat.UCGiftMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry


data object GiftMessageContent : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCGiftMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCGiftMessage>, onAction: IIMAction) {
        C2CGiftContent(entry, onAction)
    }
}



@Composable
private fun C2CGiftContent(entry: UIMessageEntry<UCGiftMessage>, onAction: IIMAction = IIMAction.Empty) {
    MessageScaffold(entry = entry, onAction = onAction) {
        val message = entry.message
        C2CMessageThemeBubble(
            entry = entry,
            childModifier = Modifier
                .widthIn(min = 240.dp)
                .border(0.5.dp, Color(0xFFF1F1F1), message.bubbleShape),
            bubbleColor = Color(0xFFFFFFFF),
            bubbleContentColor = Color(0xFF1D2129),
        ) {

            val giftModel = message.gift

            ComposeImage(
                model = giftModel.gift.icon,
                modifier = Modifier.size(48.dp)
            )

            Column(
                modifier = Modifier
                    .align(Alignment.CenterVertically)
                    .padding(start = 12.dp)
            ) {
                Text(
                    text = buildAnnotatedString {
                        append(stringResource(id = R.string.cpd送给))

                        val user = giftModel.receivers.first()
                        withStyle(
                            SpanStyle(
                                color = if (user.isBoy) {
                                    Color(0xFF5EA8FF)
                                } else {
                                    Color(0xFFFF5E8B)
                                }
                            )
                        ) {
                            append(" ${user.nickname}")
                        }
                    },
                    fontSize = 16.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                Spacer(modifier = Modifier.height(1.dp))

                Text(
                    text = "${giftModel.gift.name}X${giftModel.count}",
                    color = Color(0xFF86909C),
                    fontSize = 12.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    }
}

@Preview
@Composable
private fun PreviewGift() {
//    C2CGiftContent(
//        Message.obtain(
//            "1",
//            Conversation.ConversationType.PRIVATE,
//            AppPersistMessage.obtain(MsgEventCmd.GIVE_GIFT, "").apply {
//                giftModel = GiftWrapper(sender = userForPreview, receivers = listOf(userForPreview), gift = Gift(desc = ""))
//            }
//        ).toMessageItem(userForPreview)!!
//    )
}
