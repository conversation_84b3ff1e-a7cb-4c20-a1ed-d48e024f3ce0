package com.qyqy.cupid.im.messages

import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.cupid.ui.profile.guard.GuardListViewModel
import com.qyqy.cupid.utils.navigateToGuardPage
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.INVALID_USER
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry

data object GuardMessageContent : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCCustomMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction) {
        C2CTextContent(entry, onAction)
    }
}

/**
 * 单聊文本消息
 */
@Composable
private fun C2CTextContent(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction = IIMAction.Empty) {
    MessageScaffold(entry = entry, onAction = onAction) {
        val message = entry.message

        /**
         * "change_type": 1,    // 1成为守护，2被守护，3失去守护身份
         * "guard_threshold": 3000  // 失去守护时，要多少金币成为守护
         */
        val context = LocalContext.current
        val nav = LocalAppNavController.current
        val text = remember(message.id) {
            val targetUser = message.getJsonValue<AppUser>("target_user") ?: INVALID_USER
            val targetName = targetUser.nickname.takeIf { n -> n.isNotEmpty() } ?: "User"
            val changeType = message.getJsonInt("guard_threshold",3000)
            val guardThreshold = message.getJsonInt("guard_threshold",3000)
            val s1 = when (changeType) {
                1 -> context.getString(R.string.cpd_msg_guard_2, targetName)
                2 -> context.getString(R.string.cpd_msg_guard_1, targetName)
                else -> context.getString(R.string.cpd_guard_expired, targetName, guardThreshold)
            }
            val s2 = when (changeType) {
                1, 2 -> context.getString(R.string.cpd_click_check)
                else -> context.getString(R.string.cpd_go_guard)
            }
            buildAnnotatedString {
                try {
                    val str = "$s1$s2"
                    append(str)
                    var start = str.indexOf(targetName)
                    var end = start + targetName.length
                    addStyle(SpanStyle(Color(0xFFFF5E8B)), start, end)
                    addLink(clickable = LinkAnnotation.Clickable("user") {
                        nav.navigateToProfile(targetUser.id)
                    }, start, end)
                    start = str.indexOf(s2)
                    end = start + s2.length
                    addStyle(SpanStyle(Color(0xFF57A9FB), textDecoration = TextDecoration.Underline), start, end)
                    addLink(clickable = LinkAnnotation.Clickable("check") {
                        when (changeType) {
                            1 -> nav.navigateToGuardPage(GuardListViewModel.GuardType.OUT)
                            2 -> nav.navigateToGuardPage()
                            else -> nav.navigate(CupidRouters.C2CChat, mapOf("user" to targetUser))
                        }
                    }, start, end)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
        C2CMessageThemeBubble(entry = entry) {
            Text(
                text = text,
                fontSize = 14.sp,
            )
        }
    }
}