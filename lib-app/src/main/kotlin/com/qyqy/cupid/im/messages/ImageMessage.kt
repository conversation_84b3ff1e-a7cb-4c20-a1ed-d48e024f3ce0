package com.qyqy.cupid.im.messages

import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.core.util.SizeFCompat
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.photo.LocalPhotoPreviewState
import com.qyqy.ucoo.compose.ui.photo.previewKeyModifier
import com.qyqy.ucoo.im.compat.UCImageMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.im.compat.chat.utils.UIMessageUtils


data object ImageMessageContent : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCImageMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCImageMessage>, onAction: IIMAction) {
        val density = LocalDensity.current
        val message = entry.message
        val imageElem = message.previewElem

        val (widthDp, heightDp) = remember(imageElem, density) {
            if (imageElem.width > 0 && imageElem.height > 0) {
                val size = UIMessageUtils.resizeImage(originalSize = with(density) {
                    SizeFCompat(imageElem.width.toDp().value, imageElem.height.toDp().value)
                })
                size.width.dp to size.height.dp
            } else {
                Dp.Unspecified to Dp.Unspecified
            }
        }

        val previewState = LocalPhotoPreviewState.current

        MessageScaffold(entry = entry, onAction = onAction) {
            ComposeImage(
                model = imageElem.localUri ?: imageElem.url,
                modifier = Modifier
                    .size(width = widthDp, height = heightDp)
                    .previewKeyModifier(previewState, message.id)
                    .sizeIn(maxWidth = UIMessageUtils.MaxWidthDp.dp, maxHeight = UIMessageUtils.MaxHeightDp.dp)
                    .clip(MaterialTheme.shapes.small)
                    .combinedClickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null,
                        onLongClick = {
                            it.showMessageMenu(message)
                        }, onClick = {
                            onAction.onPreview(message)
                        }
                    ),
                contentScale = ContentScale.Fit
            )
        }
    }

}


@Preview
@Composable
private fun PreviewImage() {
//    val message = MessageExt.createImageMessage(Uri.parse(""), 100, 100)
//    C2CImageContent(
//        messageItem = MessageExt.fakerMessage(message).toMessageItem(userForPreview)!!,
//        modifier = Modifier.sizeIn(maxWidth = maxWidthDp.dp, maxHeight = maxHeightDp.dp)
//    )
}