package com.qyqy.cupid.im.messages

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.IC2CAction
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.cupid.ui.home.HomeSubPage
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.im.bean.Gift
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.utils.takeIsNotEmpty

data object InviteToCoupleMessage : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCCustomMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction) {
        val message = entry.message
        val sendUser = if (message.cmd == MsgEventCmd.GIVE_CONFESSION_GIFT) {
            message.getJsonValue<AppUser>("inviter")
        } else {
            message.getJsonValue<AppUser>("invitee")
        }
        val targetUser = if (message.cmd == MsgEventCmd.GIVE_CONFESSION_GIFT) {
            message.getJsonValue<AppUser>("invitee")
        } else {
            message.getJsonValue<AppUser>("inviter")
        }
        val isSelfSent = targetUser?.isSelf == false

        val content = remember {
            if (message.cmd == MsgEventCmd.GIVE_CONFESSION_GIFT) {
                if (isSelfSent) {
                    message.getJsonString("inviter_msg")
                } else {
                    message.getJsonString("invitee_msg")
                }
            } else {
                message.summary
            }.orEmpty()
        }
        Content(content, sendUser ?: entry.user, targetUser ?: sUser) {
            if (message.cmd == MsgEventCmd.GIVE_CONFESSION_GIFT) {
                if (!isSelfSent) {
                    Column(
                        modifier = Modifier
                            .padding(top = 10.dp)
                            .widthIn(min = 148.dp)
                            .height(44.dp)
                            .background(Brush.horizontalGradient(listOf(Color(0xFFFF8ECB), Color(0xFFFF4794))), CircleShape)
                            .clip(CircleShape)
                            .clickable {
                                message
                                    .getJsonString("invite_code")
                                    ?.takeIsNotEmpty()
                                    ?.apply {
                                        (onAction as? IC2CAction)?.onAgreeCpInvite(this)
                                    }
                            }
                            .padding(horizontal = 12.dp),
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = stringResource(id = R.string.cpd回赠礼物),
                            color = Color(0xFFFFF7FB),
                            fontSize = 16.sp,
                            lineHeight = 16.sp
                        )

                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Text(
                                text = if (sUser.isBoy) message.getJsonValue<Gift>("gift")?.price?.toString()
                                    .orEmpty() else stringResource(id = R.string.cpd免费),
                                color = Color(0xFFFFF7FB),
                                fontSize = 12.sp,
                                lineHeight = 12.sp
                            )
                            if (sUser.isBoy) {
                                Image(
                                    painter = painterResource(id = R.drawable.ic_cpd_coin),
                                    contentDescription = null,
                                    modifier = Modifier
                                        .padding(start = 3.dp)
                                        .size(12.dp)
                                )
                            }
                        }
                    }

                    Row(
                        modifier = Modifier
                            .padding(top = 6.dp)
                            .noEffectClickable {
                                (onAction as? IC2CAction)?.goBecomeCouplePage()
                            },
                        verticalAlignment = Alignment.CenterVertically
                    ) {

                        Image(
                            painter = painterResource(id = R.drawable.ic_cpd_cp_zone_qa),
                            contentDescription = null,
                            colorFilter = ColorFilter.tint(Color(0xFFFF4795)),
                            modifier = Modifier.size(14.dp),
                        )

                        Text(
                            text = stringResource(id = R.string.cpd了解情侣玩法),
                            color = Color(0xFFFF4795),
                            fontSize = 12.sp,
                            lineHeight = 12.sp
                        )
                    }
                }
            } else {
                Column(
                    modifier = Modifier
                        .padding(top = 10.dp)
                        .widthIn(min = 148.dp)
                        .height(40.dp)
                        .background(Brush.horizontalGradient(listOf(Color(0xFFFF8ECB), Color(0xFFFF4794))), CircleShape)
                        .clip(CircleShape)
                        .clickable {
                            AppLinkManager.controller?.navigateByLink("${AppLinkManager.BASE_URL}/home?sub_route_page=${HomeSubPage.Mine.t}")
                        }
                        .padding(horizontal = 12.dp),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(text = stringResource(id = R.string.cpd查看情侣空间), color = Color(0xFFFFF7FB), fontSize = 16.sp)
                }
            }
        }
    }
}


@Composable
private fun Content(text: String, user: User, targetUser: User, content: @Composable ColumnScope.() -> Unit) {
    Box(modifier = Modifier.fillMaxWidth()) {
        Column(
            modifier = Modifier
                .align(Alignment.TopCenter)
                .padding(top = 16.dp)
                .width(270.dp)
                .background(Brush.verticalGradient(listOf(Color(0xFFFF80D4), Color(0xFFFFDAF0))), Shapes.corner12)
                .border(2.dp, Color(0x99FFFFFF), Shapes.corner12)
                .padding(start = 16.dp, top = 58.dp, end = 16.dp, bottom = 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = text,
                fontSize = 15.sp,
                color = Color(0xFFB40020),
                textAlign = TextAlign.Center
            )
            content()
        }


        Box(modifier = Modifier.align(Alignment.TopCenter)) {
            Row(horizontalArrangement = Arrangement.spacedBy((-8).dp)) {
                CircleComposeImage(
                    model = user.avatarUrl,
                    modifier = Modifier.size(64.dp),
                    borderStroke = BorderStroke(2.dp, Color.White)
                )

                CircleComposeImage(
                    model = targetUser.avatarUrl,
                    modifier = Modifier.size(64.dp),
                    borderStroke = BorderStroke(2.dp, Color.White)
                )
            }

            Image(
                painter = painterResource(id = R.drawable.ic_cpd_cp_heart),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .size(40.dp)
            )
        }
    }
}