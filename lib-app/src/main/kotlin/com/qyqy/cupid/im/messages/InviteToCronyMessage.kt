package com.qyqy.cupid.im.messages

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.compose.colorFromHex
import com.qyqy.ucoo.compose.orDefault
import com.qyqy.ucoo.compose.presentation.ff.InviteManager
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.im.bean.InviteInfo
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.im.compat.getExtraBoolean
import com.qyqy.ucoo.im.message.MsgEventCmd
import kotlinx.coroutines.launch

data object ReceiveInviteToCronyMessage : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCCustomMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction) {
        val message = entry.message
        val accepted = remember(entry.refresh) {
            message.getExtraBoolean("accepted", false)
        }
        ReceiveInviteToCronyMessageContent(accepted, message.parseDataJson<InviteInfo>())
    }
}



@Composable
private fun ReceiveInviteToCronyMessageContent(
    accepted: Boolean,
    inviteInfo: InviteInfo?,
) {
    Box(
        modifier = Modifier.fillMaxWidth(),
        contentAlignment = Alignment.TopCenter
    ) {
        Box(
            modifier = Modifier
                .size(270.dp, 220.dp)
                .padding(top = 16.dp)
                .paint(painter = painterResource(id = R.drawable.bg_been_invite_to_family), contentScale = ContentScale.FillBounds)
        ) {
            Column(
                modifier = Modifier
                    .offset(y = (-16).dp)
                    .fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Row(horizontalArrangement = Arrangement.spacedBy((-8).dp)) {
                    CircleComposeImage(
                        model = inviteInfo?.inviter?.avatarUrl.orEmpty(), modifier = Modifier
                            .size(64.dp)
                            .border(1.5.dp, Color.White, CircleShape)
                    )
                    CircleComposeImage(
                        model = inviteInfo?.invitee?.avatarUrl.orEmpty(), modifier = Modifier
                            .size(64.dp)
                            .border(1.5.dp, Color.White, CircleShape)
                    )
                }

                Text(
                    text = stringResource(id = R.string.cpd邀请你成为我的密友),
                    modifier = Modifier.padding(top = 8.dp),
                    color = Color(0xFF721F4E),
                    fontSize = 16.sp,
                    textAlign = TextAlign.Center,
                    lineHeight = 32.sp
                )

                Row(
                    modifier = Modifier.padding(top = 24.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "${stringResource(id = R.string.cpd关系标签)}：",
                        fontSize = 14.sp,
                        color = Color(0xFF721F4E)
                    )

                    Box(
                        modifier = Modifier
                            .height(20.dp)
                            .background(
                                color = inviteInfo?.tag?.labelBgColor
                                    ?.colorFromHex()
                                    .orDefault(Color.Transparent),
                                shape = Shapes.extraSmall
                            )
                            .padding(horizontal = 6.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = inviteInfo?.tag?.name.orEmpty(),
                            modifier = Modifier.align(Alignment.Center),
                            fontSize = 13.sp,
                            lineHeight = 13.sp,
                            color = inviteInfo?.tag?.smallFontColor?.colorFromHex().orDefault(Color.Transparent)
                        )
                    }
                }

                val scope = rememberCoroutineScope()
                val loadingState = LocalContentLoading.current
                AppButton(
                    text = stringResource(id = if (accepted) R.string.cpd已接受 else R.string.cpd接受邀请),
                    brush = Brush.horizontalGradient(listOf(Color(0xFFFFA7F1), Color(0xFFFF4EAE))),
                    color = Color.White,
                    fontSize = 14.sp,
                    modifier = Modifier
                        .padding(top = 20.dp)
                        .size(190.dp, 36.dp),
                    enabled = !accepted,
                ) {
                    inviteInfo ?: return@AppButton
                    scope.launch {
                        loadingState.value = true
                        InviteManager.acceptInvite(inviteInfo.inviteId)
                        loadingState.value = false
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewReceiveInviteToCronyMessageContent() {
    PreviewCupidTheme {
        ReceiveInviteToCronyMessageContent(false, null)
    }
}

data object SendInviteToCronyMessage : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCCustomMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction) {
        SendInviteToCronyMessageContent(entry.message.parseDataJson<InviteInfo>())
    }
}



@Composable
private fun SendInviteToCronyMessageContent(
    inviteInfo: InviteInfo?,
) {
    Box(
        modifier = Modifier.fillMaxWidth(),
        contentAlignment = Alignment.TopCenter
    ) {
        Box(
            modifier = Modifier
                .size(270.dp, 176.dp)
                .padding(top = 16.dp)
                .paint(painter = painterResource(id = R.drawable.bg_invite_someone), contentScale = ContentScale.FillBounds)
        ) {
            Column(
                modifier = Modifier
                    .offset(y = (-16).dp)
                    .fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {

                Row(horizontalArrangement = Arrangement.spacedBy((-8).dp)) {
                    CircleComposeImage(
                        model = inviteInfo?.inviter?.avatarUrl.orEmpty(), modifier = Modifier
                            .size(64.dp)
                            .border(1.5.dp, Color.White, CircleShape)
                    )
                    CircleComposeImage(
                        model = inviteInfo?.invitee?.avatarUrl.orEmpty(), modifier = Modifier
                            .size(64.dp)
                            .border(1.5.dp, Color.White, CircleShape)
                    )
                }

                Text(
                    text = buildAnnotatedString {
                        val name = inviteInfo?.invitee?.nickname.orEmpty()
                        val content = stringResource(id = R.string.cpd邀请成为我的密友, name)
                        append(content)
                        val start = content.indexOf(name)
                        if (start != -1) {
                            addStyle(SpanStyle(color = Color(0xFFFC50B0)), start, start + name.length)
                        }
                    },
                    modifier = Modifier
                        .padding(top = 8.dp)
                        .height(32.dp),
                    color = Color(0xFF721F4E),
                    fontSize = 16.sp,
                    textAlign = TextAlign.Center,
                    lineHeight = 32.sp,
                )

                Row(
                    modifier = Modifier.padding(top = 24.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "${stringResource(id = R.string.cpd关系标签)}：",
                        fontSize = 14.sp,
                        color = Color(0xFF721F4E)
                    )

                    Box(
                        modifier = Modifier
                            .height(20.dp)
                            .background(
                                color = inviteInfo?.tag?.labelBgColor
                                    ?.colorFromHex()
                                    .orDefault(Color.Transparent),
                                shape = Shapes.extraSmall
                            )
                            .padding(horizontal = 6.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = inviteInfo?.tag?.name.orEmpty(),
                            modifier = Modifier.align(Alignment.Center),
                            fontSize = 13.sp,
                            lineHeight = 13.sp,
                            color = inviteInfo?.tag?.smallFontColor?.colorFromHex().orDefault(Color.Transparent)
                        )
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewSendInviteToCronyMessageContent() {
    PreviewCupidTheme {
        SendInviteToCronyMessageContent(null)
    }
}

data object ToCronyResultTipMessage : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCCustomMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction) {
        val message = entry.message
        val sendUser = message.getJsonValue<AppUser>("inviter")
        val targetUser = message.getJsonValue<AppUser>("invitee")
        val content = stringResource(
            id = if (message.cmd == MsgEventCmd.ACCEPT_TO_JOIN_RELATIVE_GROUP) {
                R.string.cpd恭喜x成为x密友
            } else {
                R.string.cpdx和x解除密友
            },
            targetUser?.nickname.orEmpty(),
            sendUser?.nickname.orEmpty()
        )
        Box(
            modifier = Modifier
                .fillMaxWidth()
        ) {
            Text(
                text = content,
                modifier = Modifier
                    .align(Alignment.Center)
                    .widthIn(max = 312.dp)
                    .background(Color(0xFFF6EBF0), Shapes.small)
                    .padding(12.dp),
                fontSize = 16.sp,
                color = Color(0xFFFF5E8B)
            )
        }
    }
}