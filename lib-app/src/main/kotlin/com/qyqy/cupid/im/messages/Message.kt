package com.qyqy.cupid.im.messages

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.graphics.Rect
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.Stable
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.painter.ColorPainter
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.graphics.toColorInt
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import com.google.accompanist.drawablepainter.DrawablePainter
import com.overseas.common.sntp.SNTPManager
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.cupid.widgets.LevelComposeView
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.ChatBubble
import com.qyqy.ucoo.app
import com.qyqy.ucoo.compose.LaunchOnceEffect
import com.qyqy.ucoo.compose.isEditOnCompose
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.room.RichText
import com.qyqy.ucoo.compose.presentation.room.TextSpan
import com.qyqy.ucoo.compose.presentation.room.buildTextSpan
import com.qyqy.ucoo.compose.presentation.room.inlineTextContent
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.glide.SourceDensity
import com.qyqy.ucoo.glide.ninepatch.NinePatchPainter
import com.qyqy.ucoo.glide.ninepatch.NinePathLoader
import com.qyqy.ucoo.glide.ninepatch.bitmapToNinePatchPainter
import com.qyqy.ucoo.glide.rememberPainterByUrl
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.UCInstanceMessage
import com.qyqy.ucoo.im.compat.UCTextMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.im.compat.chat.utils.UIMessageUtils
import com.qyqy.ucoo.im.compat.getExtraBoolean
import com.qyqy.ucoo.im.compat.isFailure
import com.qyqy.ucoo.im.compat.isSending
import com.qyqy.ucoo.im.compat.isSent
import com.qyqy.ucoo.toast
import kotlinx.coroutines.launch
import java.util.concurrent.TimeUnit

val UCInstanceMessage.bubbleColor
    get() = if (isSelf) Color(0xFFFF5E8B) else Color(0xFFFFFFFF)

val UCInstanceMessage.bubbleContentColor
    get() = if (isSelf) Color(0xFFFFFFFF) else Color(0xFF1D2129)

val UCInstanceMessage.bubbleShape
    get() = if (isSelf) RoundedCornerShape(topStart = 12.dp, topEnd = 0.dp, bottomEnd = 12.dp, bottomStart = 12.dp)
    else RoundedCornerShape(topStart = 0.dp, topEnd = 12.dp, bottomEnd = 12.dp, bottomStart = 12.dp)

@Stable
sealed class MsgLayoutContent {
    @Composable
    abstract fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction)
}

@OptIn( ExperimentalFoundationApi::class)
@Composable
fun MessageScaffold(
    entry: UIMessageEntry<*>,
    onAction: IIMAction,
    edgeSpace: Dp = 35.dp,
    showReadStatus: Boolean = true,
    content: @Composable RowScope.(MenuActionScope) -> Unit,
) {
    val message = entry.message
    val isSelf = message.isSelf
    val context = LocalContext.current
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .run {
                if (isSelf) {
                    padding(start = edgeSpace)
                } else {
                    padding(end = edgeSpace)
                }
            }, horizontalArrangement = if (isSelf) Arrangement.End else Arrangement.Start
    ) {
        val menuActionScope = remember(context) {
            MenuActionScope(context)
        }
        if (isSelf) {
            Column(modifier = Modifier.weight(1f, false), horizontalAlignment = Alignment.End) {
                Row {
                    MessageStatus(
                        message = message,
                        showReadStatus = showReadStatus,
                        onAction = onAction,
                        modifier = Modifier
                            .align(Alignment.CenterVertically)
                            .padding(end = 4.dp),
                    )
                    Row(
                        modifier = Modifier.combinedClickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null,
                            onLongClick = {
                                menuActionScope.showMessageMenu(message)
                            },
                            onClick = {})
                    ) {
                        content(menuActionScope)
                        MessageContextMenu(menuActionScope.menuListState)
                    }
                }
                MessageReminder(entry, Modifier.padding(top = 5.dp))
            }
            ComposeImage(model = entry.user.avatarUrl, modifier = Modifier
                .padding(start = 8.dp)
                .size(40.dp)
                .clip(CircleShape)
                .clickable {
                    onAction.navigateToProfile(entry.user.id)
                })
            Spacer(modifier = Modifier.width(16.dp))
        } else {
            Spacer(modifier = Modifier.width(16.dp))
            ComposeImage(model = entry.user.avatarUrl, modifier = Modifier
                .padding(end = 8.dp)
                .size(40.dp)
                .clip(CircleShape)
                .clickable {
                    onAction.navigateToProfile(entry.user.id)
                })
            Column(modifier = Modifier.weight(1f, false), horizontalAlignment = Alignment.Start) {
                Row(
                    modifier = Modifier.combinedClickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null,
                        onLongClick = {
                            menuActionScope.showMessageMenu(message)
                        },
                        onClick = {})
                ) {
                    content(menuActionScope)
                    MessageContextMenu(menuActionScope.menuListState)
                }
                MessageReminder(entry, Modifier.padding(top = 5.dp))
            }
        }
    }
}

class MenuActionScope(private val context: Context) {

    val menuListState = mutableStateOf<List<TextMenuAction>?>(null)

    fun showMessageMenu(message: UCInstanceMessage) {
        if (message.isSelf) {
            menuListState.value = buildList {
                if (message is UCTextMessage) {
                    add(TextMenuAction(R.string.cpd复制) {
                        val clipboard = context.applicationContext.getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager
                        val clip = ClipData.newPlainText("message", message.text)
                        clipboard?.setPrimaryClip(clip)
                        menuListState.value = null
                    })
                }
                if (message.isSent && message.timestamp >= SNTPManager.now() - TimeUnit.MINUTES.toMillis(5)) {
                    add(TextMenuAction(R.string.cpd撤回) {
                        menuListState.value = null
                        if (message.timestamp < SNTPManager.now() - TimeUnit.MINUTES.toMillis(5)) {
                            toast(app.getString(R.string.cpd_condition_recall))
                            return@TextMenuAction
                        }
                        IMCompatCore.recallMessage(message.base)
                    })
                }
                add(TextMenuAction(R.string.cpd_delete) {
                    IMCompatCore.deleteMessage(message.base)
                    menuListState.value = null
                })
            }
        } else {
            menuListState.value = buildList {
                if (message is UCTextMessage) {
                    add(TextMenuAction(R.string.cpd复制) {
                        val clipboard = context.applicationContext.getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager
                        val clip = ClipData.newPlainText("message", message.text)
                        clipboard?.setPrimaryClip(clip)
                        menuListState.value = null
                    })
                }
                add(TextMenuAction(R.string.cpd_delete) {
                    IMCompatCore.deleteMessage(message.base)
                    menuListState.value = null
                })
            }
        }
    }
}

@Composable
inline fun RowMessageBubble(
    message: UCInstanceMessage,
    modifier: Modifier = Modifier,
    bubbleColor: Color = message.bubbleColor,
    bubbleContentColor: Color = message.bubbleContentColor,
    bubbleShape: Shape = message.bubbleShape,
    crossinline content: @Composable RowScope.() -> Unit,
) {
    Row(
        modifier = modifier
            .background(bubbleColor, bubbleShape)
            .padding(horizontal = 16.dp, vertical = 9.5.dp),
        content = {
            CompositionLocalProvider(LocalContentColor provides bubbleContentColor) {
                content()
            }
        },
        verticalAlignment = Alignment.CenterVertically
    )
}

@Composable
inline fun ColumnMessageBubble(
    message: UCInstanceMessage,
    modifier: Modifier = Modifier,
    bubbleColor: Color = message.bubbleColor,
    bubbleContentColor: Color = message.bubbleContentColor,
    bubbleShape: Shape = message.bubbleShape,
    crossinline content: @Composable ColumnScope.() -> Unit,
) {
    Column(
        modifier = modifier
            .background(bubbleColor, bubbleShape)
            .padding(horizontal = 16.dp, vertical = 9.5.dp),
        content = {
            CompositionLocalProvider(LocalContentColor provides bubbleContentColor) {
                content()
            }
        },
        horizontalAlignment = Alignment.CenterHorizontally
    )
}


@Composable
private fun MessageStatus(message: UCInstanceMessage, showReadStatus: Boolean, modifier: Modifier = Modifier, onAction: IIMAction) {
    if (isEditOnCompose) {
        return
    }

    when {
        message.isSending -> {
            CircularProgressIndicator(
                modifier = modifier.size(16.dp), color = MaterialTheme.colorScheme.primary, strokeWidth = 2.dp
            )
        }

        message.isFailure -> {
            Image(
                painter = painterResource(id = R.drawable.ic_send_msg_failure),
                contentDescription = null,
                modifier = modifier
                    .size(16.dp)
                    .noEffectClickable {
                        onAction.onResendMessage(message)
                    },
            )
        }

        message.getExtraBoolean(UIMessageUtils.CONTENT_ILLEGAL, false) -> Unit

        showReadStatus && message.isC2CRead -> {
            Image(
                painter = painterResource(id = R.drawable.ic_cpd_msg_read),
                contentDescription = null,
                modifier = modifier.size(16.dp),
            )
        }

        showReadStatus && message.isSelf -> {
            Image(
                painter = painterResource(id = R.drawable.ic_cpd_msg_unread),
                contentDescription = null,
                modifier = modifier.size(16.dp),
            )
        }
    }
}


@Composable
fun MessageReminder(entry: UIMessageEntry<*>, modifier: Modifier) {
    val extra = entry.extra?.expansionExtra?.hint
    if (extra?.hintVisible == true) {
        val hintRichList = extra.hintRichList
        if (hintRichList != null) {
            RichText(
                rich = hintRichList,
                modifier = modifier,
                color = if (entry.isSelf) Color(0xFFFF5E8B) else Color(0xFF86909C),
                fontSize = 11.sp,
                textAlign = if (entry.isSelf) TextAlign.End else TextAlign.Start,
                lineHeight = 13.5.sp
            )
        } else {
            val text = extra.hintText
            if (text != null) {
                Text(
                    text = text,
                    modifier = modifier,
                    color = if (entry.isSelf) Color(0xFFFF5E8B) else Color(0xFF86909C),
                    fontSize = 11.sp,
                    textAlign = if (entry.isSelf) TextAlign.End else TextAlign.Start,
                    lineHeight = 13.5.sp
                )
            }
        }
    }
}


@Composable
fun roomUserNameTextSpan(user: AppUser, appendText: String? = null, color: Color = Color.White): TextSpan {
    val density = LocalDensity.current
    val paddingValues = PaddingValues(start = 4.dp, top = 2.dp, bottom = 2.dp)
    return remember(density, user.nickname, user.expLevelInfo, user.medalList, appendText) {
        buildTextSpan {
            withStyle(style = SpanStyle(color = color)) {
                append(user.nickname)
            }

            it.appendInlineContent(
                inlineTextContent(
                    key = "level",
                    density = density,
                    width = 84,
                    height = 16,
                    paddingValues = paddingValues
                ) { modifier ->
                    LevelComposeView(user = user, modifier = modifier)
                }
            )

            user.medalList.forEachIndexed { index, medal ->
                it.appendInlineContent(inlineTextContent(
                    key = "medal_$index",
                    density = density,
                    width = medal.width,
                    height = medal.height,
                    paddingValues = paddingValues
                ) { modifier ->
                    ComposeImage(
                        model = medal.icon, modifier = modifier
                    )
                })
            }

            if (!appendText.isNullOrEmpty()) {
                append(" ")
                append(appendText)
            }
        }
    }
}

@Stable
data class MessageTheme(
    val painter: Painter,
    val paddingValues: PaddingValues,
    val shape: Shape?,
    val contentColor: Color,
    val fontSize: TextUnit,
    val left: Boolean = true,
)

data class MessageSkin(
    val background: String,
    val paddingRect: Rect,
    val textColor: String,
)

@Composable
inline fun C2CMessageThemeBubble(
    entry: UIMessageEntry<*>,
    modifier: Modifier = Modifier,
    childModifier: Modifier = Modifier,
    bubbleColor: Color = entry.message.bubbleColor,
    bubbleContentColor: Color = entry.message.bubbleContentColor,
    bubbleShape: Shape = entry.message.bubbleShape,
    crossinline content: @Composable RowScope.() -> Unit,
) {
    MessageThemeBubble(
        entry = entry,
        defaultMsgTheme = MessageTheme(
            painter = ColorPainter(bubbleColor),
            paddingValues = PaddingValues(horizontal = 16.dp, vertical = 9.5.dp),
            shape = bubbleShape,
            contentColor = bubbleContentColor,
            fontSize = 14.sp,
            left = !entry.isSelf
        ),
        modifier = modifier,
        childModifier = childModifier,
        content = content,
    )
}

@Composable
inline fun MessageThemeBubble(
    entry: UIMessageEntry<*>,
    defaultMsgTheme: MessageTheme,
    modifier: Modifier = Modifier,
    childModifier: Modifier = Modifier,
    crossinline content: @Composable RowScope.() -> Unit,
) {
    val chatBubble = if (entry.message is UCTextMessage) {
        entry.user.bubble
    } else {
        null
    }
    MessageThemeBubble(chatBubble, defaultMsgTheme, modifier, childModifier, content)
}


@Composable
inline fun MessageThemeBubble(
    chatBubble: ChatBubble?,
    defaultMsgTheme: MessageTheme,
    modifier: Modifier = Modifier,
    childModifier: Modifier = Modifier,
    crossinline content: @Composable RowScope.() -> Unit,
) {
    val left = defaultMsgTheme.left
    var hasSkin = false
    val messageTheme = run {
        if (chatBubble != null) {
            val url = if (left) {
                chatBubble.leftImg
            } else {
                chatBubble.rightImg
            }
            if (url.isNotEmpty()) {
                LaunchOnceEffect {
                    NinePathLoader.recordChatBubble(chatBubble)
                }
                hasSkin = true
                return@run rememberSkinMessageTheme(
                    MessageSkin(
                        url, if (left) {
                            chatBubble.leftPadding
                        } else {
                            chatBubble.rightPadding
                        }, chatBubble.fontColor
                    ),
                    defaultMsgTheme
                ).value
            }
        }
        defaultMsgTheme
    }

    Box(modifier) {
        MessageThemeBubble(childModifier, messageTheme, content)

        if (hasSkin) {
            val options = remember {
                RequestOptions.bitmapTransform(SourceDensity(NinePathLoader.PIC_DENSITY_DPI)).override(Target.SIZE_ORIGINAL)
            }

            val images = if (left) chatBubble?.startImages else chatBubble?.endImages

            images?.getOrNull(0)?.takeIf {
                it.isNotEmpty()
            }?.let {
                rememberPainterByUrl(it, options).value
            }?.also { painter ->
                Image(
                    painter = painter,
                    contentDescription = null,
                    modifier = Modifier.align(Alignment.TopStart),
                    contentScale = ContentScale.Fit
                )
            }

            images?.getOrNull(1)?.takeIf {
                it.isNotEmpty()
            }?.let {
                rememberPainterByUrl(it, options).value
            }?.also { painter ->
                Image(
                    painter = painter,
                    contentDescription = null,
                    modifier = Modifier.align(Alignment.TopEnd),
                    contentScale = ContentScale.Fit
                )
            }

            images?.getOrNull(2)?.takeIf {
                it.isNotEmpty()
            }?.let {
                rememberPainterByUrl(it, options).value
            }?.also { painter ->
                Image(
                    painter = painter,
                    contentDescription = null,
                    modifier = Modifier.align(Alignment.BottomStart),
                    contentScale = ContentScale.Fit
                )
            }

            images?.getOrNull(3)?.takeIf {
                it.isNotEmpty()
            }?.let {
                rememberPainterByUrl(it, options).value
            }?.also { painter ->
                Image(
                    painter = painter,
                    contentDescription = null,
                    modifier = Modifier.align(Alignment.BottomEnd),
                    contentScale = ContentScale.Fit
                )
            }
        }
    }

}

@Composable
inline fun MessageThemeBubble(
    modifier: Modifier,
    messageTheme: MessageTheme,
    crossinline content: @Composable RowScope.() -> Unit,
) {
    Row(
        modifier = modifier
            .run {
                val shape = messageTheme.shape
                if (shape != null) {
                    clip(shape)
                } else {
                    this
                }
            }
            .run {
                if (isEditOnCompose) {
                    background((messageTheme.painter as ColorPainter).color)
                } else {
                    paint(messageTheme.painter, contentScale = ContentScale.FillBounds)
                }
            }
            .padding(messageTheme.paddingValues),
        verticalAlignment = Alignment.CenterVertically
    ) {
        val mergedStyle = LocalTextStyle.current.merge(TextStyle(color = messageTheme.contentColor, fontSize = messageTheme.fontSize))
        CompositionLocalProvider(
            LocalContentColor provides messageTheme.contentColor, LocalTextStyle provides mergedStyle
        ) {
            content()
        }
    }
}

@Composable
fun rememberSkinMessageTheme(msgSkin: MessageSkin, defaultMsgTheme: MessageTheme): State<MessageTheme> {
    val context = LocalContext.current
    val density = LocalDensity.current
    val scope = rememberCoroutineScope()
    return remember(msgSkin) {
        val cache = NinePathLoader.getChatBubbleCache(msgSkin.background)
        if (cache != null) {
            mutableStateOf(cache.bitmapToNinePatchPainter(context, density).toMessageTheme(msgSkin, defaultMsgTheme))
        } else {
            mutableStateOf(defaultMsgTheme).also { state ->
                scope.launch {
                    val ninePatch = NinePathLoader.load(msgSkin.background, msgSkin.paddingRect)?.bitmapToNinePatchPainter(context, density)
                    if (ninePatch != null) {
                        state.value = ninePatch.toMessageTheme(msgSkin, defaultMsgTheme)
                    }
                }
            }
        }
    }
}

private fun NinePatchPainter.toMessageTheme(msgSkin: MessageSkin, defaultMsgTheme: MessageTheme) = MessageTheme(
    DrawablePainter(drawable),
    padding,
    null,
    try {
        Color(msgSkin.textColor.toColorInt())
    } catch (e: Throwable) {
        defaultMsgTheme.contentColor
    },
    defaultMsgTheme.fontSize,
)