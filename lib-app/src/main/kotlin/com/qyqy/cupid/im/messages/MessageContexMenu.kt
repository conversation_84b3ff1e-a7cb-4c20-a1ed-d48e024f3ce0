package com.qyqy.cupid.im.messages

import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.GenericShape
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.RoundRect
import androidx.compose.ui.geometry.toRect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Outline
import androidx.compose.ui.graphics.addOutline
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.IntRect
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupPositionProvider
import androidx.compose.ui.window.PopupProperties
import com.qyqy.ucoo.compose.noEffectClickable
import kotlin.math.roundToInt

data class TextMenuAction(
    val textResId: Int,
    val onAction: () -> Unit
)

@Composable
fun MessageContextMenu(menuListState: MutableState<List<TextMenuAction>?>) {
    val menuList = menuListState.value
    if (menuList.isNullOrEmpty()) {
        return
    }

    ContextMenu(onDismissRequest = {
        menuListState.value = null
    }) {
        Row(
            modifier = Modifier
                .padding(vertical = 6.dp)
                .height(IntrinsicSize.Min),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            menuList.forEachIndexed { index, menu ->
                if (index != 0) {
                    VerticalDivider(modifier = Modifier.padding(vertical = 2.dp), thickness = 0.3.dp, color = Color(0x8084868B))
                }
                Text(
                    text = stringResource(id = menu.textResId),
                    modifier = Modifier
                        .noEffectClickable(onClick = menu.onAction)
                        .padding(horizontal = 12.dp),
                    color = Color(0xFF1D2129),
                    fontSize = 13.5.sp
                )
            }
        }
    }
}

@Composable
fun ContextMenu(
    onDismissRequest: (() -> Unit),
    content: @Composable ColumnScope.() -> Unit
) {
    var anchorCenter by remember {
        mutableIntStateOf(-1)
    }

    var topMenu by remember {
        mutableStateOf(true)
    }

    val popupPositionProvider = remember {

        object : PopupPositionProvider {

            override fun calculatePosition(
                anchorBounds: IntRect,
                windowSize: IntSize,
                layoutDirection: LayoutDirection,
                popupContentSize: IntSize
            ): IntOffset {

                val anchorAlignmentPoint = Alignment.TopCenter.align(
                    IntSize.Zero,
                    anchorBounds.size,
                    layoutDirection
                )
                val popupAlignmentPoint = -Alignment.TopCenter.align(
                    IntSize.Zero,
                    popupContentSize,
                    layoutDirection
                )

                val result = anchorBounds.topLeft +
                        anchorAlignmentPoint +
                        popupAlignmentPoint


                val x = if (result.x < 0) {
                    -result.x
                } else {
                    val x = windowSize.width.minus(result.x.plus(popupContentSize.width))
                    if (x < 0) {
                        x
                    } else {
                        0
                    }
                }

                val y = if (result.y >= popupContentSize.height.plus(15).plus(100)) {
                    topMenu = true
                    -popupContentSize.height.plus(15)
                } else {
                    topMenu = false
                    anchorBounds.height.plus(15)
                }

                return result.plus(IntOffset(x, y)).also {
                    anchorCenter = if (x == 0) {
                        -1
                    } else {
                        anchorBounds.left.plus(anchorBounds.width.div(2)).minus(it.x)
                    }
                }
            }
        }
    }

    Popup(
        popupPositionProvider = popupPositionProvider,
        onDismissRequest = onDismissRequest,
        properties = PopupProperties(
            clippingEnabled = true,
            focusable = true,
        )
    ) {

        val popupShape by with(LocalDensity.current) {
            derivedStateOf {
                val radius = 6.dp.toPx()
                val caretWidth = 12.dp.toPx()
                val caretHeight = 6.dp.toPx()
                GenericShape { size, layoutDirection ->
                    val caretLeft = if (anchorCenter == -1) {
                        size.width.minus(caretWidth).div(2)
                    } else {
                        anchorCenter.minus(caretWidth.div(2))
                    }.coerceIn(radius, size.width.minus(radius.plus(caretWidth)))

                    val rectH = size.height.minus(caretHeight)

                    if (topMenu) {
                        addOutline(
                            Outline.Rounded(
                                RoundRect(
                                    rect = size.copy(height = rectH).toRect(),
                                    topLeft = CornerRadius(if (layoutDirection == LayoutDirection.Ltr) radius else radius),
                                    topRight = CornerRadius(if (layoutDirection == LayoutDirection.Ltr) radius else radius),
                                    bottomRight = CornerRadius(if (layoutDirection == LayoutDirection.Ltr) radius else radius),
                                    bottomLeft = CornerRadius(if (layoutDirection == LayoutDirection.Ltr) radius else radius)
                                )
                            )
                        )
                        moveTo(caretLeft, rectH)
                        lineTo(caretLeft.plus(caretWidth.div(2)), rectH.plus(caretHeight))
                        lineTo(caretLeft.plus(caretWidth), rectH)
                    } else {
                        addOutline(
                            Outline.Rounded(
                                RoundRect(
                                    rect = Rect(Offset(0f, caretHeight), size.copy(height = rectH)),
                                    topLeft = CornerRadius(if (layoutDirection == LayoutDirection.Ltr) radius else radius),
                                    topRight = CornerRadius(if (layoutDirection == LayoutDirection.Ltr) radius else radius),
                                    bottomRight = CornerRadius(if (layoutDirection == LayoutDirection.Ltr) radius else radius),
                                    bottomLeft = CornerRadius(if (layoutDirection == LayoutDirection.Ltr) radius else radius)
                                )
                            )
                        )
                        moveTo(caretLeft, caretHeight)
                        lineTo(caretLeft.plus(caretWidth.div(2)), 0f)
                        lineTo(caretLeft.plus(caretWidth), caretHeight)
                    }
                }
            }
        }

        ElevatedCard(
            shape = popupShape,
            colors = CardDefaults.elevatedCardColors(
                containerColor = Color.White
            ),
            elevation = CardDefaults.elevatedCardElevation(
                defaultElevation = 1.dp
            ),
        ) {
            if (!topMenu) {
                Spacer(modifier = Modifier.height(6.dp))
            }

            content()

            if (topMenu) {
                Spacer(modifier = Modifier.height(6.dp))
            }
        }
    }
}

@Preview
@Composable
private fun Preview() {

    var offsetX by remember { mutableStateOf(0f) }
    var offsetY by remember { mutableStateOf(0f) }

    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(top = 200.dp, start = 150.dp)
                .offset { IntOffset(offsetX.roundToInt(), offsetY.roundToInt()) }
                .width(100.dp)
                .height(150.dp)
                .background(Color.Yellow)
                .pointerInput(Unit) {
                    detectDragGestures { change, dragAmount ->
                        change.consume()
                        offsetX += dragAmount.x
                        offsetY += dragAmount.y
                    }
                },
        ) {
            val showState = remember {
                mutableStateOf(true)
            }
            ContextMenu(onDismissRequest = {
                showState.value = false
            }) {
                Row(
                    modifier = Modifier
                        .padding(vertical = 6.dp)
                        .height(IntrinsicSize.Min),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    repeat(4) {
                        if (it != 0) {
                            VerticalDivider(thickness = 0.5.dp, color = Color(0x80959BA7))
                        }
                        Text(text = "复制", modifier = Modifier.padding(horizontal = 12.dp))
                    }
                }
            }
        }
    }
}