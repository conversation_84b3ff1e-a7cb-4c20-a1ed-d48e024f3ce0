package com.qyqy.cupid.im.messages

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry


data object RecallMessageContent : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<*>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<*>, onAction: IIMAction) {
        RecallMessageContent(entry, onAction)
    }
}

/**
 * 单聊文本消息
 */
@Composable
private fun RecallMessageContent(entry: UIMessageEntry<*>, onAction: IIMAction = IIMAction.Empty) {
    val text = if (entry.isSelf) {
        stringResource(id = R.string.cpd_你撤回了一条消息)
    } else if (entry.message.isC2CMsg) {
        stringResource(id = R.string.cpd_对方撤回了一条消息)
    } else {
        stringResource(id = R.string.cpd_撤回了一条消息, entry.user.nickname)
    }
    Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
        AppText(
            text = text,
            color = Color(0xFF86909C),
            fontSize = 12.sp
        )
    }
}
