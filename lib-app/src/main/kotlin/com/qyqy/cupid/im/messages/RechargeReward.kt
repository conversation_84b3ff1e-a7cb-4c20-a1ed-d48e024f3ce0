package com.qyqy.cupid.im.messages

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry

data object RechargeRewardMessageContent : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCCustomMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction) {
        MessageScaffold(entry = entry, onAction = onAction) {
            ColumnMessageBubble(message = entry.message) {
                val content = remember {
                    entry.message.getJsonString("content").orEmpty()
                }
                val buttonText = remember {
                    entry.message.getJsonString("btn_txt").orEmpty()
                }

                Text(text = content, fontSize = 12.sp)

                AppButton(
                    text = buttonText,
                    modifier = Modifier
                        .padding(start = 4.dp, top = 4.dp)
                        .widthIn(min = 56.dp)
                        .height(24.dp),
                    background = Color.Transparent,
                    color = Color(0xFFFF5E8B),
                    fontSize = 14.sp,
                    border = BorderStroke(0.5.dp, Color(0xFFFF5E8B)),
                    contentPadding = PaddingValues(horizontal = 16.dp, vertical = 5.dp),
                    onClick = {
                        AppLinkManager.controller?.navigateByLink(entry.message.getJsonString("jump_link").orEmpty())
                    }
                )
            }
        }
    }
}