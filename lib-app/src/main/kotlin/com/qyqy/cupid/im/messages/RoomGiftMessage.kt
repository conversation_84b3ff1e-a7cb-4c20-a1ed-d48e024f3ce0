package com.qyqy.cupid.im.messages

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.ColorPainter
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.room.SpanText
import com.qyqy.ucoo.compose.presentation.room.buildTextSpan
import com.qyqy.ucoo.compose.presentation.room.inlineTextContent
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.im.compat.UCGiftMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry


data object RoomGiftMessage : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCGiftMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCGiftMessage>, onAction: IIMAction) {
        val targetUser = entry.message.gift.receivers.getOrNull(entry.requireMsgIndex)
        RoomGiftMessageContent(entry, targetUser, onAction)
    }
}

/**
 * 单聊文本消息
 */

@Composable
private fun RoomGiftMessageContent(entry: UIMessageEntry<UCGiftMessage>, targetUser: User?, onAction: IIMAction = IIMAction.Empty) {
    val density = LocalDensity.current
    Column(modifier = Modifier.padding(end = 35.dp)) {
        Row {
            CircleComposeImage(
                model = entry.user.avatarUrl,
                modifier = Modifier
                    .size(32.dp)
                    .noEffectClickable {
                        (onAction as? IVoiceLiveAction)?.showUserInfoPanel(entry.user)
                    }
            )
            SpanText(
                textSpan = roomUserNameTextSpan(entry.user as AppUser),
                modifier = Modifier.padding(start = 4.dp),
                lineHeight = with(density) {
                    32.dp.toPx().toSp()
                },
                color = Color.White,
                fontSize = 14.sp
            )
        }

        val giftModel = entry.message.gift

        val fontSize = with(density) {
            14.dp.toPx().toSp()
        }

        MessageThemeBubble(
            entry = entry,
            defaultMsgTheme = MessageTheme(
                painter = ColorPainter(Color(0x33000000)),
                paddingValues = PaddingValues(horizontal = 8.dp, vertical = 13.dp),
                shape = Shapes.small,
                contentColor = Color.White,
                fontSize = fontSize,
                left = true
            ),
            modifier = Modifier.padding(start = 36.dp)
        ) {
            SpanText(
                textSpan = buildTextSpan {

                    val receiverName = targetUser?.nickname ?: stringResource(id = R.string.cpd所有人)
                    val count = giftModel.count.coerceAtLeast(1).toString()
                    val giftName = giftModel.gift.name

                    val content = if (giftModel.isBlinxBox) {
                        stringResource(id = R.string.cpd语音房送盲盒, receiverName, count, giftModel.blindboxName.orEmpty(), giftName)
                    } else if (giftModel.isCupidLuckyGift) {
                        stringResource(id = R.string.cpd语音房送幸运礼物, receiverName, count, giftName, giftModel.comboCoin)
                    } else {
                        stringResource(id = R.string.cpd语音房送礼物, receiverName, count, giftName)
                    }

                    append(content)

                    var start = content.indexOf(receiverName, 0)
                    var end = start.plus(receiverName.length)

                    addStyle(SpanStyle(color = Color(0xFFFFEE56)), start, end)

                    if (giftModel.isBlinxBox) {
                        start = content.indexOf(giftName, end)
                        end = start.plus(giftName.length)
                        addStyle(SpanStyle(color = Color(0xFF50FFF3)), start, end)
                    }
                    if (!giftModel.isCupidLuckyGift) {
                        it.appendInlineContent(inlineTextContent(
                            key = "gift", density = density, width = 20, paddingValues = PaddingValues(start = 4.dp)
                        ) { modifier ->
                            ComposeImage(model = giftModel.gift.icon, modifier)
                        })
                    }

                    append(" ")
                },
            )
        }
    }
}


@Preview
@Composable
private fun PreviewRoomGiftMessageContent() {
//    RoomGiftMessageContent(
//        Message.obtain(
//            "1",
//            Conversation.ConversationType.PRIVATE,
//            AppPersistMessage.obtain(MsgEventCmd.GIVE_GIFT, "").apply {
//                giftModel = GiftWrapper(sender = userForPreview, receivers = listOf(userForPreview), gift = Gift(desc = "", name = "凤舞九天"))
//            }
//        ).toMessageItem(userForPreview)!!,
//        userForPreview,
//    )
}