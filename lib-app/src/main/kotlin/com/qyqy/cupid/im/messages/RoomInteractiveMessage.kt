package com.qyqy.cupid.im.messages

import androidx.compose.animation.core.Animatable
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.painter.ColorPainter
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.viewinterop.NoOpUpdate
import com.overseas.common.sntp.SNTPManager
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.room.SpanText
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.EvaAnimState
import com.qyqy.ucoo.im.compat.UCEmojiMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.widget.LuckyNumberView
import com.yy.yyeva.EvaAnimConfig
import com.yy.yyeva.inter.IEvaAnimListener
import com.yy.yyeva.util.EvaConstant
import com.yy.yyeva.util.ScaleType
import com.yy.yyeva.view.EvaAnimViewV3
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.joinAll
import kotlinx.coroutines.launch
import kotlin.math.abs
import kotlin.random.Random


data object RoomInteractiveMessage : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCEmojiMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCEmojiMessage>, onAction: IIMAction) {
        val message = entry.message
        val resList = if (message.type == UCEmojiMessage.TYPE_DICE) {
            intArrayOf(
                R.drawable.dice_action_0,
                R.drawable.dice_action_1,
                R.drawable.dice_action_2,
                R.drawable.dice_action_3,
            )
        } else {
            intArrayOf(
                R.drawable.effect_scissor,
                R.drawable.effect_stone,
                R.drawable.effect_cloth,
            )
        }

        val resultResId = if (message.type == UCEmojiMessage.TYPE_DICE) {
            when (message.value.toIntOrNull() ?: 1) {
                2 -> R.drawable.dice_2
                3 -> R.drawable.dice_3
                4 -> R.drawable.dice_4
                5 -> R.drawable.dice_5
                6 -> R.drawable.dice_6
                else -> R.drawable.dice_1
            }
        } else {
            when (message.value.toIntOrNull() ?: 1) {
                2 -> R.drawable.effect_stone
                3 -> R.drawable.effect_cloth
                else -> R.drawable.effect_scissor
            }
        }

        val isListened = remember(message.timestamp) {
            abs(message.timestamp.minus(SNTPManager.now())) >= 300_000 //  超过5分钟
        }

        var count by rememberSaveable {
            mutableIntStateOf(-1)
        }

        var isPlaying by rememberSaveable {
            mutableStateOf(false)
        }

        var interactiveId by remember {
            mutableIntStateOf(resultResId)
        }

        if (!isListened && !isPlaying && !message.isPlayed) { // 直接渲染结果
            LaunchedEffect(key1 = Unit) {
                if (message.type == UCEmojiMessage.TYPE_DICE) {
                    while (isActive && count++ <= 14) {
                        interactiveId = resList[count.rem(resList.size)]
                        delay(120)
                    }
                    interactiveId = resultResId
                } else {
                    var exclude = 3
                    while (isActive && count++ < 9) {
                        var index = Random.nextInt(3)
                        if (exclude == index) {
                            index = index.plus(1).rem(3)
                        }
                        interactiveId = resList[index]
                        exclude = index
                        delay(450)
                    }
                    interactiveId = resultResId
                }
                isPlaying = true
                message.isPlayed = true
            }
        }

        RoomInteractiveMessageContent(entry, onAction) {
            Image(painter = painterResource(id = interactiveId), contentDescription = null, modifier = Modifier.size(40.dp))
        }
    }
}

data object RoomInteractiveEffectMessage : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCEmojiMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCEmojiMessage>, onAction: IIMAction) {
        val density = LocalDensity.current
        val message = entry.message

        val asset = remember(message.type) {
            EvaAnimState.Source.Asset(
                when (message.type) {
                    UCEmojiMessage.TYPE_NUMBER_3 -> "effect_lucky_number_3.mp4"
                    UCEmojiMessage.TYPE_NUMBER_5 -> "effect_lucky_number_5.mp4"
                    else -> "effect_lucky_number_1.mp4"
                }
            )
        }

        val isListened = remember(message.timestamp) {
            abs(message.timestamp.minus(SNTPManager.now())) >= 300_000 //  超过5分钟
        }

        var isPlaying by rememberSaveable {
            mutableStateOf(false)
        }

        /**
         * 0 待播放
         * 1 正在播放
         * 2 转场
         * 3 完成
         */
        var state by remember {
            mutableIntStateOf(if (isListened || isPlaying || message.isPlayed) 3 else 0)
        }

        RoomInteractiveMessageContent(entry, onAction) {
            Box(
                modifier = Modifier
                    .size(80.dp, 40.dp)
            ) {
                val alphaAnimation1 = remember { Animatable(1f) }
                val scaleAnimation1 = remember { Animatable(1f) }
                if (state != 3) {
                    AndroidView(
                        factory = {
                            EvaAnimViewV3(it).apply {
                                setLastFrame(true)
                                setScaleType(ScaleType.CENTER_CROP)
                                setVideoMode(EvaConstant.VIDEO_MODE_SPLIT_HORIZONTAL)
                            }
                        },
                        modifier = Modifier
                            .fillMaxSize()
                            .graphicsLayer {
                                if (state != 2) {
                                    this.scaleX = 1f
                                    this.scaleY = 1f
                                    this.alpha = 1f
                                } else {
                                    this.scaleX = scaleAnimation1.value
                                    this.scaleY = scaleAnimation1.value
                                    this.alpha = alphaAnimation1.value
                                }
                            },
                        onReset = {
                            if (state != 0) {
                                it.setAnimListener(null)
                                if (it.isRunning()) {
                                    it.stopPlay()
                                }
                                state = 3
                                isPlaying = true
                                message.isPlayed = true
                            }
                        }
                    ) {
                        if (state == 0) {
                            state = 1
                            if (it.isRunning()) {
                                it.stopPlay()
                            }
                            val assetManager = it.context.assets
                            it.setAnimListener(object : IEvaAnimListener {
                                override fun onFailed(errorType: Int, errorMsg: String?) {
                                    if (state == 1) {
                                        state = 2
                                        if (!message.isPlayed) {
                                            message.isPlayed = true
                                        }
                                    }
                                }

                                override fun onVideoComplete(lastFrame: Boolean) {
                                    if (state == 1) {
                                        state = 2
                                        if (!message.isPlayed) {
                                            message.isPlayed = true
                                        }
                                    }
                                }

                                override fun onVideoRender(frameIndex: Int, config: EvaAnimConfig?) {
                                    if (state == 1 && frameIndex >= 45) {
                                        state = 2
                                    }
                                }

                                override fun onVideoDestroy() = Unit
                                override fun onVideoRestart() = Unit
                                override fun onVideoStart(isRestart: Boolean) = Unit

                            })
                            it.startPlay(assetManager, asset.assetPath)
                        }
                    }
                }

                val alphaAnimation2 = remember { Animatable(0f) }
                val scaleAnimation2 = remember { Animatable(0.2f) }

                if (state != 0 && state != 1) {
                    LaunchedEffect(Unit) {
                        isPlaying = true
                        message.isPlayed = true
                    }
                    AndroidView(
                        factory = {
                            LuckyNumberView(it).also { v ->
                                with(density) {
                                    v.setAttr(2.dp.toPx(), 15.dp.toPx())
                                }
                            }
                        },
                        modifier = Modifier
                            .fillMaxSize()
                            .graphicsLayer {
                                if (state == 3) {
                                    this.scaleX = 1f
                                    this.scaleY = 1f
                                    this.alpha = 1f
                                } else {
                                    this.scaleX = scaleAnimation2.value
                                    this.scaleY = scaleAnimation2.value
                                    this.alpha = alphaAnimation2.value
                                }
                            },
                        onReset = NoOpUpdate
                    ) {
                        it.setNumber(message.value)
                    }
                }

                if (state == 2) {
                    LaunchedEffect(key1 = Unit) {
                        listOf(
                            launch {
                                alphaAnimation1.animateTo(0f)
                            },
                            launch {
                                scaleAnimation1.animateTo(0.2f)
                            }
                        ).joinAll()

                        listOf(
                            launch {
                                alphaAnimation2.animateTo(1f)
                            },
                            launch {
                                scaleAnimation2.animateTo(1f)
                            }
                        ).joinAll()

                        state = 3
                    }
                }
            }
        }
    }
}


/**
 * 单聊文本消息
 */

@Composable
private fun RoomInteractiveMessageContent(
    entry: UIMessageEntry<UCEmojiMessage>,
    onAction: IIMAction = IIMAction.Empty,
    content: @Composable RowScope.() -> Unit,
) {
    val density = LocalDensity.current
    Column(modifier = Modifier.padding(end = 35.dp)) {
        Row {
            CircleComposeImage(
                model = entry.user.avatarUrl,
                modifier = Modifier
                    .size(32.dp)
                    .noEffectClickable {
                        (onAction as? IVoiceLiveAction)?.showUserInfoPanel(entry.user)
                    }
            )
            SpanText(
                textSpan = roomUserNameTextSpan(entry.user as AppUser),
                modifier = Modifier.padding(start = 4.dp),
                lineHeight = with(density) {
                    32.dp.toPx().toSp()
                },
                color = Color.White,
                fontSize = 14.sp
            )
        }

        val fontSize = with(density) {
            14.dp.toPx().toSp()
        }

        MessageThemeBubble(
            entry = entry,
            defaultMsgTheme = MessageTheme(
                painter = ColorPainter(Color(0x33000000)),
                paddingValues = PaddingValues(horizontal = 8.dp, vertical = 13.dp),
                shape = Shapes.small,
                contentColor = Color.White,
                fontSize = fontSize,
                left = true
            ),
            modifier = Modifier.padding(start = 36.dp),
            content = content,
        )
    }
}