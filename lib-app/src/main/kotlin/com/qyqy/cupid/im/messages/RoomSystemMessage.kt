package com.qyqy.cupid.im.messages

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.room.SpanText
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.im.message.MsgEventCmd

@Composable
fun SystemAnnouncementMessage() {
    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(4.dp))
            .background(Color(0x4D000000))
            .padding(12.dp)
    ) {
        Text(
            text = stringResource(id = R.string.cpd_system_announcement_message),
            color = Color.White.copy(alpha = 0.5f),
            fontSize = 12.sp,
            lineHeight = 14.sp
        )
    }
}

data object RoomMemberChange : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCCustomMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction) {
        RoomMemberChangeContent(entry, onAction)
    }
}

/**
 * 单聊文本消息
 */

@Composable
private fun RoomMemberChangeContent(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction = IIMAction.Empty) {
    val density = LocalDensity.current
    Row(
        modifier = Modifier
            .padding(end = 35.dp)
            .background(Color(0x33000000), Shapes.small)
            .padding(8.dp)
    ) {
        val isEnter = entry.message.cmd == MsgEventCmd.USER_ENTRANCE
        CircleComposeImage(
            model = entry.user.avatarUrl,
            modifier = Modifier
                .size(32.dp)
                .noEffectClickable {
                    (onAction as? IVoiceLiveAction)?.showUserInfoPanel(entry.user)
                }
        )

        SpanText(
            textSpan = roomUserNameTextSpan(
                user = entry.user as AppUser,
                appendText = if (isEnter) stringResource(id = R.string.cpd_entered_room) else stringResource(id = R.string.cpd_exit_room),
                color = Color(0xFFFFEE56)
            ),
            modifier = Modifier.padding(start = 4.dp),
            lineHeight = with(density) {
                32.dp.toPx().toSp()
            },
            color = Color.White,
            fontSize = 14.sp
        )
    }
}


@Preview
@Composable
private fun PreviewRoomMemberChangeContent() {
//    RoomMemberChangeContent(
//        Message.obtain(
//            "1",
//            Conversation.ConversationType.CHATROOM,
//            AppPersistMessage.obtain(MsgEventCmd.GUIDE_VIDEO_CHAT, "哈哈哈哈")
//        ).toMessageItem(userForPreview)!!
//    )
}

data object RoomSystemMessageContent : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCCustomMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction) {
        val message = entry.message
        Box(
            modifier = Modifier
                .clip(RoundedCornerShape(4.dp))
                .background(Color(0x4D000000))
                .padding(vertical = 3.dp, horizontal = 6.dp)
        ) {
            Text(
                text = remember(message) {
                    buildAnnotatedString {
                        append(message.summary)
                    }
                },
                color = Color(0xFFFFA5A5),
                fontSize = 12.sp,
                lineHeight = 14.sp
            )
        }
    }

}
