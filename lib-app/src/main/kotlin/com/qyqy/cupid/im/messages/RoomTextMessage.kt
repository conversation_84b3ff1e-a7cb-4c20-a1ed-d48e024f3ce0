package com.qyqy.cupid.im.messages

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.ColorPainter
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.room.SpanText
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.UCTextMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry


data object RoomTextMessage : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCTextMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCTextMessage>, onAction: IIMAction) {
        RoomTextMessageContent(entry, onAction)
    }
}

/**
 * 单聊文本消息
 */

@Composable
private fun RoomTextMessageContent(entry: UIMessageEntry<UCTextMessage>, onAction: IIMAction = IIMAction.Empty) {
    val density = LocalDensity.current
    Column(modifier = Modifier.padding(end = 35.dp)) {
        Row {
            CircleComposeImage(
                model = entry.user.avatarUrl,
                modifier = Modifier
                    .size(32.dp)
                    .noEffectClickable {
                        (onAction as? IVoiceLiveAction)?.showUserInfoPanel(entry.user)
                    }
            )
            SpanText(
                textSpan = roomUserNameTextSpan(entry.user as AppUser),
                modifier = Modifier.padding(start = 4.dp),
                lineHeight = with(density) {
                    32.dp.toPx().toSp()
                },
                color = Color.White,
                fontSize = 14.sp
            )
        }

        val fontSize = with(density) {
            14.dp.toPx().toSp()
        }

        MessageThemeBubble(
            entry = entry,
            defaultMsgTheme = MessageTheme(
                painter = ColorPainter(Color(0x33000000)),
                paddingValues = PaddingValues(horizontal = 8.dp, vertical = 13.dp),
                shape = Shapes.small,
                contentColor = Color.White,
                fontSize = fontSize,
                left = true
            ),
            modifier = Modifier.padding(start = 36.dp)
        ) {
            Text(text = entry.message.text)
        }
    }
}


@Composable
fun RoomMessageLayout(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction = IIMAction.Empty, content: @Composable () -> Unit) {
    val density = LocalDensity.current
    Column(modifier = Modifier.padding(end = 35.dp)) {
        Row {
            CircleComposeImage(
                model = entry.user.avatarUrl,
                modifier = Modifier
                    .size(32.dp)
                    .noEffectClickable {
                        (onAction as? IVoiceLiveAction)?.showUserInfoPanel(entry.user)
                    }
            )
            SpanText(
                textSpan = roomUserNameTextSpan(entry.user as AppUser),
                modifier = Modifier.padding(start = 4.dp),
                lineHeight = with(density) {
                    32.dp.toPx().toSp()
                },
                color = Color.White,
                fontSize = 14.sp
            )
        }
        Box(modifier = Modifier.padding(start = 36.dp, top = 8.dp)) {
            content()
        }
    }
}


@Preview
@Composable
private fun PreviewRoomTextMessageContent() {
//    RoomTextMessageContent(
//        Message.obtain(
//            "1",
//            Conversation.ConversationType.CHATROOM,
//            MessageExt.createTextMessage("哈哈哈哈哈哈哈哈哈哈哈")
//        ).toMessageItem(userForPreview)!!
//    )
}