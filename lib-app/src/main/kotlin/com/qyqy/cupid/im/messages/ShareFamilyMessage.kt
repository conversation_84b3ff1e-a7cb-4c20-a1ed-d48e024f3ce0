

package com.qyqy.cupid.im.messages

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.ui.IC2CAction
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.http.getOrNull
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.im.compat.getExtraString
import com.qyqy.ucoo.tribe.bean.Tribe
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.booleanOrNull
import kotlinx.serialization.json.jsonPrimitive

/**
 *  @time 8/23/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.home.message
 */
data object ShareFamilyMessageContent : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCCustomMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction) {
        C2CShareFamilyContent(entry, onAction)
    }
}


/**
 * 单聊家族邀请消息
 */
@Composable
private fun C2CShareFamilyContent(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction = IIMAction.Empty) {
    val message = entry.message
    val tribe = message.getJsonValue<Tribe>("tribe") ?: return
    val contentText = message.summary
    //是否正在申请中
    val receiverExtra = message.getExtraString("receiver_extra")
    val isApply = remember(receiverExtra) {
        if (receiverExtra.isNotBlank()) {
            val receiverJsonObj = Json.decodeFromString<JsonObject>(receiverExtra)
            receiverJsonObj.getOrNull("is_apply")?.jsonPrimitive?.booleanOrNull ?: false
        } else {
            false
        }
    }
    MessageScaffold(entry = entry, onAction = onAction) {
        Column(
            modifier = Modifier
                .background(
                    color = Color.White, shape = RoundedCornerShape(8.dp)
                )
                .padding(8.dp),
            horizontalAlignment = Alignment.End
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically, modifier = Modifier
                    .widthIn(220.dp, 240.dp)
                    .padding(vertical = 4.dp)
            ) {
                ComposeImage(
                    model = tribe.avatarUrl, modifier = Modifier
                        .padding(4.dp)
                        .size(48.dp)
                        .clip(RoundedCornerShape(8.dp))
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column(
                    verticalArrangement = Arrangement.Center,
                    modifier = Modifier.heightIn(min = 64.dp)
                ) {
                    Text(
                        text = contentText ?: "",
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        color = CpdColors.FF1D2129, fontSize = 14.sp
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = tribe.name ?: "",
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis,
                        color = CpdColors.FF86909C, fontSize = 12.sp,
                    )
                    if (!entry.isSelf) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Spacer(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(0.2.dp)
                                .background(color = CpdColors.FF86909C)
                        )
                    }
                }
            }


            if (!entry.isSelf) {
                Spacer(modifier = Modifier.height(8.dp))
                AppButton(
                    text = if (isApply) stringResource(id = R.string.cpd_family_request_done) else stringResource(id = R.string.cpd_family_request_join),
                    border = BorderStroke(1.dp, color = if (isApply) Color(0xFFE5E6EB) else CpdColors.FFFF5E8B),
                    background = Color.White,
                    textStyle = TextStyle(color = if (isApply) Color(0xff86909C) else CpdColors.FFFF5E8B, fontSize = 14.sp),
                    contentPadding = PaddingValues(horizontal = 12.dp),
                    enabled = !isApply,
                    modifier = Modifier.height(26.dp),
                    onClick = {
                        (onAction as? IC2CAction)?.onRequestJoinFamily(tribe.id)
                    }
                )
            }
        }
    }
}


@Preview(showBackground = true)
@Composable
private fun ShareFamilyPreview() {
//    val data = ("{\n" +
//            "    \"cmd\": \"tribe_invite\",\n" +
//            "    \"data\": {\n" +
//            "        \"sender\": {\n" +
//            "            \"userid\": 2477,\n" +
//            "            \"public_id\": \"103231\",\n" +
//            "            \"nickname\": \"哈哈哈哈\",\n" +
//            "            \"avatar_url\": \"https://res.cloudinary.com/xzwzztest/image/upload/v1651413210/avatar/cc14a7db-bc9d-492c-94c1-048dc52bf60e.jpg?x-oss-process=image/format,webp\",\n" +
//            "            \"gender\": 1,\n" +
//            "            \"age\": 24,\n" +
//            "            \"height\": 0,\n" +
//            "            \"avatar_frame\": \"\",\n" +
//            "            \"medal\": null,\n" +
//            "            \"medal_list\": [],\n" +
//            "            \"level\": 0,\n" +
//            "            \"country_flag\": \"https://media.ucoofun.com/opsite%2Fcountryflag%2FL_slices%2FJP.png\",\n" +
//            "            \"is_member\": true\n" +
//            "        },\n" +
//            "        \"receiver\": {\n" +
//            "            \"userid\": 3175,\n" +
//            "            \"public_id\": \"104180\",\n" +
//            "            \"nickname\": \"google　あっこうんｔ\",\n" +
//            "            \"avatar_url\": \"https://s.test.ucoofun.com/aacegn?x-oss-process=image/format,webp\",\n" +
//            "            \"gender\": 2,\n" +
//            "            \"age\": 24,\n" +
//            "            \"height\": 0,\n" +
//            "            \"avatar_frame\": \"\",\n" +
//            "            \"medal\": null,\n" +
//            "            \"medal_list\": [],\n" +
//            "            \"level\": 0,\n" +
//            "            \"country_flag\": \"https://media.ucoofun.com/opsite%2Fcountryflag%2FL_slices%2FHK.png\",\n" +
//            "            \"is_member\": false\n" +
//            "        },\n" +
//            "        \"tribe\": {\n" +
//            "            \"id\": 295,\n" +
//            "            \"name\": \"沃德发\",\n" +
//            "            \"avatar_url\": \"https://media.ucoofun.com/mobileclient/android/moment/1722722366250_3020ADEC9007F5192776.jpg\",\n" +
//            "            \"public_id\": \"100303\"\n" +
//            "        },\n" +
//            "        \"invite_code\": \"60ekpxjz\",\n" +
//            "        \"digest\": \"[邀请加入家族]\"\n" +
//            "    },\n" +
//            "    \"user\": {\n" +
//            "        \"id\": \"2477\",\n" +
//            "        \"name\": \"哈哈哈哈\",\n" +
//            "        \"portrait\": \"https://res.cloudinary.com/xzwzztest/image/upload/v1651413210/avatar/cc14a7db-bc9d-492c-94c1-048dc52bf60e.jpg\",\n" +
//            "        \"extra\": \"{\\\"is_member\\\": true, \\\"gender\\\": 1, \\\"public_cp\\\": null, \\\"cp_extra_info\\\": null, \\\"avatar_frame\\\": \\\"\\\", \\\"medal\\\": null, \\\"medal_list\\\": [], \\\"level\\\": 0}\"\n" +
//            "    }\n" +
//            "}").toByteArray()
//    C2CShareFamilyContent(
//        Message.obtain(
//            "1",
//            Conversation.ConversationType.PRIVATE,
//            AppEventMessage(data)
//        ).toMessageItem(userForPreview)!!
//    )
}