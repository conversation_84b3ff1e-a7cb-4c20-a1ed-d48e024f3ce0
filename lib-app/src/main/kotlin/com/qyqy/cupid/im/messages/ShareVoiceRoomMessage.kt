

package com.qyqy.cupid.im.messages

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.overseas.common.utils.isPreviewOnCompose
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.ui.CupidViewModel
import com.qyqy.cupid.ui.IC2CAction
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.asComponentActivity
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.im.bean.AudioRoom
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry

/**
 *  @time 9/2/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.im.messages
 */
data object ShareVoiceRoomMessageContent : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCCustomMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction) {
        C2CShareVoiceRoomContent(entry = entry, 0, onAction)
    }
}


/**
 * 单聊家族邀请消息
 * @param chatType 0私聊 1家族
 */
@Composable
fun C2CShareVoiceRoomContent(entry: UIMessageEntry<UCCustomMessage>, chatType: Int, onAction: IIMAction = IIMAction.Empty) {
    val message = entry.message
    val audioRoom = message.getJsonValue<AudioRoom>("audioroom") ?: return

    val voiceLiveHelper = if (onAction == IIMAction.Empty && !isPreviewOnCompose) {
        val activity = LocalContext.current.asComponentActivity!!
        val mainViewModel = viewModel<CupidViewModel>(viewModelStoreOwner = activity)
        mainViewModel.voiceLiveHelper
    } else {
        null
    }

    MessageScaffold(entry = entry, onAction = onAction, showReadStatus = chatType == 0) {
        Column(
            modifier = Modifier
                .width(236.dp)
                .background(color = Color.White, shape = RoundedCornerShape(8.dp))
                .click {
                    if (onAction == IIMAction.Empty) {
                        voiceLiveHelper?.joinVoiceLiveRoom(audioRoom.id, true)
                    } else {
                        (onAction as? IC2CAction)?.onRequestJoinVoiceRoom(audioRoom.id)
                    }
                }
                .padding(8.dp),
            horizontalAlignment = Alignment.Start
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(vertical = 4.dp),
            ) {
                if (chatType == 1 && entry.isSelf) {
                    Text(
                        text = stringResource(id = R.string.cpd_invite_join_room, audioRoom.title),
                        modifier = Modifier.weight(1f),
                        color = CpdColors.FF1D2129,
                        fontSize = 14.sp,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                    Spacer(modifier = Modifier.width(5.dp))
                    ComposeImage(
                        model = audioRoom.owner.avatarUrl, modifier = Modifier
                            .size(48.dp)
                            .clip(CircleShape)
                    )
                } else {
                    ComposeImage(
                        model = audioRoom.owner.avatarUrl, modifier = Modifier
                            .size(48.dp)
                            .clip(CircleShape)
                    )

                    Spacer(modifier = Modifier.width(5.dp))

                    Text(
                        text = stringResource(id = R.string.cpd_invite_join_room, audioRoom.title),
                        modifier = Modifier.weight(1f),
                        color = CpdColors.FF1D2129,
                        fontSize = 14.sp,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            Spacer(modifier = Modifier.height(8.dp))
            HorizontalDivider(thickness = 0.5.dp)
            Spacer(modifier = Modifier.height(10.dp))
            Text(message.getJsonString("action_text") ?: stringResource(id = R.string.cpd_join_now), color = CpdColors.FFFF5E8B, fontSize = 12.sp)
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun ShareVoiceRoomPreView() {
//    val data =
//        ("{\"cmd\": \"audioroom_share\", \"data\": {\"sender\": {\"userid\": 2574, \"public_id\": \"103419\", \"nickname\": \"\\u8001\\u7239\", \"avatar_url\": \"https://s.test.ucoofun.com/aacedt?x-oss-process=image/format,webp\", \"gender\": 1, \"age\": 24, \"height\": 170, \"avatar_frame\": \"\", \"medal\": null, \"medal_list\": [], \"level\": 25, \"country_flag\": \"https://media.ucoofun.com/opsite%2Fcountryflag%2FL_slices%2FJP.png\", \"is_member\": true}, \"audioroom\": {\"id\": 192, \"title\": \"11\\u6d4b\\u8bd5\\u6539\\u6807\\u9898\", \"room_locked\": false, \"text_only\": false, \"owner\": {\"userid\": 2574, \"public_id\": \"103419\", \"nickname\": \"\\u8001\\u7239\", \"avatar_url\": \"https://s.test.ucoofun.com/aacedt?x-oss-process=image/format,webp\", \"gender\": 1, \"age\": 24, \"height\": 170, \"avatar_frame\": \"\", \"medal\": null, \"medal_list\": [], \"level\": 25, \"country_flag\": \"https://media.ucoofun.com/opsite%2Fcountryflag%2FL_slices%2FJP.png\"}}, \"hint\": \"\\u300c\\u30dc\\u30a4\\u30b9\\u30eb\\u30fc\\u30e0\\u3078\\u306e\\u62db\\u5f85\\u300d\", \"action_text\": \"\\u30af\\u30ea\\u30c3\\u30af\\u3057\\u3066\\u5165\\u529b\\u3057\\u3066\\u304f\\u3060\\u3055\\u3044\", \"digest\": \"\\u300c\\u30dc\\u30a4\\u30b9\\u30eb\\u30fc\\u30e0\\u3078\\u306e\\u62db\\u5f85\\u300d\"}, \"user\": {\"id\": \"2574\", \"name\": \"\\u8001\\u7239\", \"portrait\": \"https://s.test.ucoofun.com/aacedt\", \"extra\": \"{\\\"is_member\\\": true, \\\"gender\\\": 1, \\\"public_cp\\\": null, \\\"cp_extra_info\\\": null, \\\"avatar_frame\\\": \\\"\\\", \\\"medal\\\": null, \\\"medal_list\\\": [], \\\"level\\\": 25}\"}}").toByteArray()
//    C2CShareVoiceRoomContent(
//        Message.obtain(
//            "1",
//            Conversation.ConversationType.PRIVATE,
//            AppEventMessage(data)
//        ).toMessageItem(userForPreview)!!,
//        1
//    )
}