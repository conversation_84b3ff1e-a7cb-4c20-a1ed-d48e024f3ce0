package com.qyqy.cupid.im.messages

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.text.HtmlCompat
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.ucoo.compose.ui.toAnnotatedString
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.utils.takeIsNotEmpty


data object SystemTipMessageContent : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCCustomMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction) {
        TipMessageContent(entry, onAction)
    }
}

/**
 * 单聊文本消息
 */
@Composable
private fun TipMessageContent(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction = IIMAction.Empty) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        contentAlignment = Alignment.Center
    ) {
        val message = entry.message
        val htmlText = remember(message) {
            val content = message.getJsonString("rich_text").takeIsNotEmpty()
                ?: message.getJsonString("text").takeIsNotEmpty()
                ?: message.getContentText()
            try {
                HtmlCompat.fromHtml(content.orEmpty(), HtmlCompat.FROM_HTML_OPTION_USE_CSS_COLORS).toAnnotatedString()
            } catch (e: Exception) {
                buildAnnotatedString { append(content.orEmpty()) }
            }
        }
        Text(
            text = htmlText,
            modifier = Modifier
                .clip(RoundedCornerShape(8.dp))
                .background(Color(0xFFF1F2F3))
                .padding(8.dp),
            color = Color(0xFF86909C),
            fontSize = 12.sp,
            textAlign = TextAlign.Center
        )
    }
}


@Preview
@Composable
private fun PreviewTipMessageContent() {
//    TipMessageContent(
//        Message.obtain(
//            "1",
//            Conversation.ConversationType.PRIVATE,
//            AppPersistMessage.obtain(MsgEventCmd.PRIVATE_SYSTEM_HINT_V2, "哈哈哈哈")
//        ).toMessageItem(userForPreview)!!
//    )
}