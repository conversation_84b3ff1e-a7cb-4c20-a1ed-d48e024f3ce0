package com.qyqy.cupid.im.messages

import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.tooling.preview.Preview
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.ucoo.im.compat.UCTextMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry

data object TextMessageContent : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCTextMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCTextMessage>, onAction: IIMAction) {
        C2CTextContent(entry, onAction)
    }
}

/**
 * 单聊文本消息
 */
@Composable
private fun C2CTextContent(entry: UIMessageEntry<UCTextMessage>, onAction: IIMAction = IIMAction.Empty) {
    MessageScaffold(entry = entry, onAction = onAction) {
        C2CMessageThemeBubble(entry = entry) {
            Text(text = entry.message.text)
        }
    }
}


@Preview
@Composable
private fun PreviewText() {
//    C2CTextContent(
//        Message.obtain(
//            "1",
//            Conversation.ConversationType.PRIVATE,
//            MessageExt.createTextMessage("哈哈哈哈哈哈哈哈哈哈哈")
//        ).toMessageItem(userForPreview)!!
//    )
}