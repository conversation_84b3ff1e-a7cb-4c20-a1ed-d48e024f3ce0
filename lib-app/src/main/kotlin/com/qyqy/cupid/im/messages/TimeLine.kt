package com.qyqy.cupid.im.messages

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.ucoo.UserPartition
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.im.compat.UCTimestampMessage
import com.qyqy.ucoo.im.compat.chat.FakerMessageEntry
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.multilingual.currentLocale


data object TimeLineContent : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as FakerMessageEntry<UCTimestampMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: FakerMessageEntry<UCTimestampMessage>, onAction: IIMAction) {
        val context = LocalContext.current
        val locale = LocalConfiguration.current.currentLocale
        val time = remember(context, locale) {
            entry.message.getMessageTimeFormatText(context, locale, UserPartition.Cupid)
        }
        Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
            AppText(
                text = time,
                color = Color(0xFF86909C),
                fontSize = 12.sp
            )
        }
    }
}
