package com.qyqy.cupid.im.messages

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry

data object TxTImgContent : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCCustomMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction) {
        val nav = LocalAppNavController.current
        val message = entry.message
        val link = message.getJsonString("link").orEmpty().ifEmpty {
            message.getJsonString("link_for_android").orEmpty()
        }
        val title = message.getJsonString("content").orEmpty()
        val image = message.getJsonString("image").orEmpty()
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .click(onClick = {
                    nav.navigateByLink(link)
                })
                .clip(Shapes.extraSmall)
        ) {
            ComposeImage(
                model = image, modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(343 / 146f)
            )
            Text(
                text = title,
                modifier = Modifier
                    .background(Color.White)
                    .padding(12.dp),
                color = MaterialTheme.colorScheme.onSecondary,
                fontSize = 16.sp
            )
        }

    }
}

@Preview
@Composable
private fun Preview() {
//    val c = TxTImgContent("", "这里文案超出可换行...不做字符限制，运营同学来控制字数", userForPreview.avatarUrl)
//    PreviewCupidTheme {
//        Box(
//            modifier = Modifier
//                .fillMaxWidth()
//                .background(Color.Gray)
//        ) {
//            c.ContentUI {
//
//            }
//        }
//    }
}