package com.qyqy.cupid.im.messages

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.ucoo.compose.presentation.room.LocalMsgAudioPlayer
import com.qyqy.ucoo.compose.presentation.voice.VoiceIcon
import com.qyqy.ucoo.im.compat.UCVoiceMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.utils.OnClick


data object VoiceMessageContent : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCVoiceMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCVoiceMessage>, onAction: IIMAction) {
        val msgAudioPlayer = LocalMsgAudioPlayer.current
        val message = entry.message
        val isDownloading by remember {
            derivedStateOf {
                msgAudioPlayer.isDownloading(entry.key.toString())
            }
        }
        val isPlaying by remember {
            derivedStateOf {
                msgAudioPlayer.isPlaying(entry.key.toString())
            }
        }

        val read by remember {
            derivedStateOf {
                isPlaying || message.isPlayed
            }
        }

        if (isPlaying) {
            LaunchedEffect(key1 = message) {
                message.isPlayed = true
            }
        }

        C2CVoiceContent(
            entry = entry,
            isDownloading = isDownloading,
            isPlaying = isPlaying,
            read = read,
            onClick = {
                msgAudioPlayer.startAudio(entry.key.toString(), message)
            },
            onAction = onAction
        )
    }
}

/**
 * 单聊文本消息
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun C2CVoiceContent(
    entry: UIMessageEntry<UCVoiceMessage>,
    isDownloading: Boolean,
    isPlaying: Boolean,
    read: Boolean,
    onClick: OnClick = {},
    onAction: IIMAction = IIMAction.Empty,
) {
    MessageScaffold(entry = entry, onAction = onAction) {
        val message = entry.message
        C2CMessageThemeBubble(
            entry = entry,
            modifier = Modifier.combinedClickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null,
                onLongClick = {
                    it.showMessageMenu(message)
                },
                onClick = onClick
            )
        ) {
            val duration = message.duration
            if (message.isSelf) {
                if (isDownloading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(12.dp),
                        color = LocalContentColor.current,
                        strokeWidth = 1.dp
                    )
                }
                Spacer(modifier = Modifier.width(24.dp.plus(duration.times(1.2f).dp)))
                Text(text = "$duration ″", fontSize = 14.sp)
                Spacer(modifier = Modifier.width(4.dp))
                VoiceIcon(
                    modifier = Modifier
                        .size(20.dp)
                        .rotate(180f),
                    color = LocalContentColor.current,
                    anim = isPlaying
                )
            } else {
                VoiceIcon(modifier = Modifier.size(20.dp), color = LocalContentColor.current, anim = isPlaying)
                Spacer(modifier = Modifier.width(4.dp))
                Text(text = "$duration ″", fontSize = 14.sp)
                Spacer(modifier = Modifier.width(24.dp.plus(duration.times(1.2f).dp)))
                if (isDownloading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(12.dp),
                        color = LocalContentColor.current,
                        strokeWidth = 1.dp
                    )
                }
            }
        }

        if (!message.isSelf && !read) {
            Spacer(
                modifier = Modifier
                    .align(Alignment.CenterVertically)
                    .padding(start = 6.dp)
                    .size(6.dp)
                    .clip(CircleShape)
                    .background(Color(0xFFF76560))
            )
        }
    }
}

@Preview
@Composable
private fun PreviewVoice() {
//    C2CVoiceContent(
//        messageItem = MessageExt.fakerMessage(
//            message = MessageExt.createVoiceMessage(Uri.parse("/"), 5)
//        ).toMessageItem(userForPreview)!!,
//        isDownloading = false,
//        isPlaying = false,
//        read = false
//    )
}