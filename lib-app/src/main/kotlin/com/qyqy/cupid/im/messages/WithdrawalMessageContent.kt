package com.qyqy.cupid.im.messages

import android.content.ClipData
import android.content.Context
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.presentation.room.SpanText
import com.qyqy.ucoo.compose.presentation.room.buildTextSpan
import com.qyqy.ucoo.compose.presentation.room.inlineTextContent
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.toastRes

data object WithdrawalMessageContent : MsgLayoutContent() {

    @Composable
    override fun LazyItemScope.Content(entry: MessageEntry<*>, onAction: IIMAction) {
        Content(entry as UIMessageEntry<UCCustomMessage>, onAction)
    }

    @Composable
    fun LazyItemScope.Content(entry: UIMessageEntry<UCCustomMessage>, onAction: IIMAction) {
        val density = LocalDensity.current
        val context = LocalContext.current
        MessageScaffold(entry = entry, onAction = onAction) {
            ColumnMessageBubble(message = entry.message) {
                val message = entry.message
                val content = remember {
                    message.getJsonString("content").orEmpty()
                }
                val code = remember {
                    message.getJsonString("code").orEmpty()
                }

                Text(text = content, fontSize = 14.sp, color = colorResource(id = R.color.FF1D2129))

                Spacer(modifier = Modifier.height(10.dp))

                SpanText(
                    textSpan = buildTextSpan {
                        append(stringResource(id = R.string.cpd代码))
                        append(code)
                        it.appendInlineContent(inlineTextContent(
                            key = "qa",
                            density = density,
                            width = 14,
                            height = 14,
                            paddingValues = PaddingValues(horizontal = 3.dp)
                        ) { modifier ->
                            Icon(
                                painter = painterResource(id = R.drawable.ic_cpd_copy_code),
                                contentDescription = "qa",
                                modifier = modifier
                            )
                        })
                    },
                    modifier = Modifier
                        .align(Alignment.Start)
                        .clickable {
                            val clipboard =
                                context.applicationContext.getSystemService(Context.CLIPBOARD_SERVICE) as? android.content.ClipboardManager
                            val clip = ClipData.newPlainText("code", code)
                            clipboard?.setPrimaryClip(clip)
                            toastRes(R.string.cpd复制成功)
                        },
                    fontSize = 14.sp, color = colorResource(id = R.color.FF1D2129), fontWeight = FontWeight.Medium
                )
            }
        }
    }
}