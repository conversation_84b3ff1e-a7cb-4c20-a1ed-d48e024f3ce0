package com.qyqy.cupid.im.panel.audio

import android.Manifest
import android.os.SystemClock
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.gestures.verticalDrag
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.app.ActivityCompat
import com.qyqy.cupid.audio.AudioConstants
import com.qyqy.cupid.audio.AudioRecorder
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.cupid.ui.home.message.icons.ActionIcons
import com.qyqy.cupid.ui.home.message.icons.Audio
import com.qyqy.ucoo.R
import com.qyqy.ucoo.asActivity
import com.qyqy.ucoo.im.compat.MessageBundle
import com.qyqy.ucoo.im.inputpanel.audio.AudioPlayManager
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.toastRes
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.coroutines.cancellation.CancellationException
import kotlin.math.roundToInt

private var recorderStartTime = 0L

private var requestPermissionStartTime = 0L

const val PERMISSION_DIALOG_THRESHOLD = 200L

@Composable
fun AudioRecordPanel(
    modifier: Modifier = Modifier,
    onAction: IIMAction,
) {
    val context = LocalContext.current

    val scope = rememberCoroutineScope()

    val cancelOffset = with(LocalDensity.current) {
        remember {
            -40.dp.toPx()
        }
    }

    val launcher = rememberLauncherForActivityResult(ActivityResultContracts.RequestPermission()) {
        if (!it) {
            if (
                SystemClock.elapsedRealtime().minus(requestPermissionStartTime) < PERMISSION_DIALOG_THRESHOLD
                && !ActivityCompat.shouldShowRequestPermissionRationale(context.asActivity!!, Manifest.permission.RECORD_AUDIO)
            ) {
                toastRes(R.string.请到设置中授予录音权限)
            } else {
                toastRes(R.string.需要录音权限才能使用语音消息)
            }
        }
    }

    DisposableEffect(key1 = AudioRecorder) {
        onDispose {
            AudioRecorder.cancelRecord()
        }
    }

    var state by remember {
        // 0：未开始 -》按住说话
        // 1：录音中上滑取消 -》上滑取消
        // 2：录音中松手取消 -》松手取消
        mutableIntStateOf(0)
    }

    var duration by remember {
        mutableIntStateOf(0)
    }

    var sweepAngle by remember {
        mutableFloatStateOf(0f)
    }

    val onStartRecord = remember {
        {
            AudioRecorder.startRecord(context, callback = object : AudioRecorder.AudioRecorderCallback {
                override fun onStarted() {
                    // 200 毫秒调整，大概是录音机启动时长
                    recorderStartTime = SystemClock.elapsedRealtime().plus(200)
                    AudioPlayManager.getInstance().stopPlay()
                }

                override fun onFinished(outputPath: String) {
                    state = 0
                    scope.launch {
                        val seconds = withContext(Dispatchers.IO) {
                            AudioRecorder.getDuration(outputPath).div(1000)
                        }
                        if (seconds <= 0) {
                            toastRes(R.string.录音时间太短)
                        } else {
                            onAction.onSendMessage(MessageBundle.Voice.create(outputPath, seconds))
                        }
                    }
                }

                override fun onFailed(errorCode: Int, errorMessage: String) {
                    state = 0
                    toast(errorMessage)
                    if (errorCode == AudioConstants.ERROR_MIC_PERMISSION_REFUSED) {
                        requestPermissionStartTime = SystemClock.elapsedRealtime()
                        launcher.launch(Manifest.permission.RECORD_AUDIO)
                    }
                }

                override fun onCanceled() {
                    state = 0
                }

                override fun onVoiceDb(db: Double) {
                    duration = SystemClock.elapsedRealtime().minus(recorderStartTime).div(1000f).roundToInt().coerceIn(0, 60)
                    sweepAngle = 360.times(SystemClock.elapsedRealtime().minus(recorderStartTime).div(60000f)).coerceIn(0f, 360f)
                }

            })
        }
    }

    val tipText by remember {
        derivedStateOf {
            when (state) {
                0 -> {
                    context.getString(R.string.按住说话)
                }

                1 -> {
                    "${context.getString(R.string.上滑取消)} $duration″"
                }

                else -> {
                    "${context.getString(R.string.松手取消)} $duration″"
                }
            }
        }
    }

    val tipColor by remember {
        derivedStateOf {
            if (state == 2) {
                Color(0xFFF53F3F)
            } else {
                Color(0xFF1D2129)
            }
        }
    }

    val color by remember {
        derivedStateOf {
            if (state == 0) {
                Color(0xFF57CDFF)
            } else {
                Color(0xFFFAFAFA)
            }
        }
    }

    val imageVector by remember {
        derivedStateOf {
            if (state == 2) {
                ActionIcons.Audio
            } else {
                ActionIcons.Audio
            }
        }
    }

    val tintColor by remember {
        derivedStateOf {
            when (state) {
                0 -> {
                    Color.White
                }

                2 -> {
                    Color(0xFFF53F3F)
                }

                else -> {
                    Color(0xFF1D2129)
                }
            }
        }
    }

    Column(
        modifier = modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(text = tipText, modifier = Modifier.padding(top = 30.dp), color = tipColor, fontSize = 14.sp)
        Box(
            modifier = Modifier
                .padding(top = 16.dp)
                .size(88.dp)
                .clip(CircleShape)
                .background(color)
                .drawBehind {
                    if (state != 0) {
                        drawArc(
                            color = Color(0xFF57CDFF),
                            startAngle = -90f, // 12点位置
                            sweepAngle = sweepAngle,
                            useCenter = false,
                            style = Stroke(4.dp.toPx()),
                        )
                    }
                }
                .pointerInput(key1 = Unit) {
                    awaitEachGesture {
                        val id = awaitFirstDown().id
                        state = 1
                        onStartRecord()
                        verticalDrag(id) {
                            if (state != 0) {
                                state = if (it.position.y < cancelOffset) { // 已到可以取消的距离
                                    2
                                } else {
                                    1
                                }
                            } else { // 录音已结束
                                throw CancellationException()
                            }
                        }
                        if (state != 0) {
                            if (state == 2) {
                                AudioRecorder.cancelRecord()
                            } else {
                                AudioRecorder.stopRecord()
                            }
                            state = 0
                        }
                    }
                }
        ) {
            Icon(
                imageVector = imageVector,
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.Center)
                    .size(32.dp),
                tint = tintColor,
            )
        }
    }
}