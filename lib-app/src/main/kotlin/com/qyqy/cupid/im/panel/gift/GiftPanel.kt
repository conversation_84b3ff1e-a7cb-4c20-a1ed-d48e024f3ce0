package com.qyqy.cupid.im.panel.gift

import android.os.Parcelable
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.AnimationVector1D
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.GenericShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Badge
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.RoundRect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.geometry.toRect
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Outline
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.addOutline
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.coerceAtLeast
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.data.ComboBean
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.GiftExtra
import com.qyqy.cupid.ui.IGiftAction
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.dialog.AnimatedDialog
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.home.message.formatCount
import com.qyqy.cupid.ui.touchPressing
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.keepLastNonNullState
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppScrollableTabRow
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.FillComposeImage
import com.qyqy.ucoo.compose.ui.PopupActionMenu
import com.qyqy.ucoo.compose.ui.rememberOverscrollFlingBehavior
import com.qyqy.ucoo.im.bean.CPGift
import com.qyqy.ucoo.im.bean.Gift
import com.qyqy.ucoo.im.bean.GiftWrapper
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue
import kotlinx.serialization.Serializable
import java.math.BigDecimal
import java.math.RoundingMode

@Parcelize
data class C2CBottomGiftPanelDialog(
    val position: GiftPosition?,
    val giftListModelState: @RawValue State<GiftListModel>,
) : AnimatedDialog<IGiftAction>(), Parcelable {

    override val restoreState: Boolean
        get() = true

    @Composable
    override fun Content(dialog: IDialog, onAction: IGiftAction?) {
        C2CBottomGiftPanel(position, giftListModelState, {
            onAction?.onShowRechargePanel()
        }) { gift, count, extra ->
            onAction?.onSendGift(gift, count, extra)
        }
    }
}

@Composable
fun C2CBottomGiftPanel(
    position: GiftPosition?,
    giftListModelState: State<GiftListModel>,
    onRecharge: OnClick,
    onSend: (CPGift, Int, GiftExtra) -> Unit,
) {
    GiftPanelScaffold { onSelectedChange ->
        val density = LocalDensity.current
        Box(
            modifier = Modifier
                .background(Color(0xFF292929), RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                .padding(16.dp)
                .padding(
                    bottom = with(density) {
                        WindowInsets.navigationBars
                            .getBottom(this)
                            .toDp()
                    }.coerceAtLeast(15.dp)
                )
        ) {
            GiftPanelContent(
                giftModel = giftListModelState.value,
                selectCountList = intArrayOf(1, 5, 10, 20, 50, 100),
                onRecharge = onRecharge,
                onSelectedChange = onSelectedChange,
                onSend = { v1, v2, v3 ->
                    onSend(v1, v2, v3)
                    return@GiftPanelContent true
                },
                giftPosition = position,
            )
        }
    }
}


@Composable
fun GiftPanelScaffold(giftListModel: GiftListModel? = null, content: @Composable ColumnScope.((CPGift?) -> Unit) -> Unit) {
    Column {
        var bannerItem by rememberSaveable {
            mutableStateOf<Pair<String, String>?>(null)
        }
        var selectedGift by rememberSaveable {
            mutableStateOf<CPGift?>(null)
        }
        val showItem = keepLastNonNullState(newState = bannerItem)
        AnimatedVisibility(
            visible = bannerItem != null,
            modifier = Modifier.padding(start = 16.dp, end = 16.dp, bottom = 8.dp)
        ) {
            showItem ?: return@AnimatedVisibility

            val navController = LocalAppNavController.current

            FillComposeImage(
                model = showItem.first,
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(Shapes.corner12)
                    .noEffectClickable(enabled = bannerItem != null) {
                        navController.navigateWeb(showItem.second)
                    },
                fillHeight = false
            )
        }
        selectedGift?.let { gift ->
            if (gift.isLuckyBall) {
                val controller = LocalAppNavController.current
                val comboListinfo = giftListModel?.comboList?.find { it.id == gift.id }
                LuckGiftContent(giftListModel?.comboInfo ?: ComboBean(
                    jackpot =
                    comboListinfo?.jackpot ?: selectedGift?.jackpot ?: -1
                ),
                    modifier = Modifier.click {
                        giftListModel?.comboJumpLink?.let {
                            controller.navigateByLink(it)
                        }
                    })
            }
        }
        content {
            selectedGift = it
            bannerItem = if (it?.bannerImg?.isNotEmpty() == true) {
                Pair(it.bannerImg, it.bannerLink)
            } else {
                null
            }
        }
    }
}

@Serializable
@Parcelize
data class GiftPosition(
    val giftId: Int,
    val indexType: Int = 0,  // 0=优先背包，1=仅背包，2=按tab顺序
    val tabId: Int? = null,
) : Parcelable

/**
 * 幸运礼物的顶部内容栏
 *
 */
@OptIn( ExperimentalAnimationApi::class)
@Composable
fun LuckGiftContent(comboBean: ComboBean, modifier: Modifier) {
    Box(modifier = modifier) {
        Row(
            verticalAlignment = Alignment.Bottom,
            modifier = Modifier
                .paint(
                    painter = painterResource(id = R.drawable.cpd_luckgift_background),
                    contentScale = ContentScale.FillWidth,
                    alignment = Alignment.TopCenter
                )
                .height(76.dp)
                .padding(6.dp, 0.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .weight(1f)
                    .height(57.dp)
                    .background(
                        brush = Brush.horizontalGradient(
                            if (comboBean.coin == 0) listOf(Color(0xff52d5ff), Color(0xffa129ff), Color(0x007545ff)) else listOf(
                                Color.Transparent,
                                Color.Transparent
                            )
                        ), shape = RoundedCornerShape(topStart = 7.dp)
                    )
            ) {
                ComposeImage(
                    model = if (comboBean.coin == 0) R.drawable.cpd_lose_light else R.drawable.cpd_lose_grey,
                    modifier = Modifier.size(48.dp)
                )
                if (comboBean.coin == 0) {
                    Text(stringResource(id = R.string.cpd输了), color = if (comboBean.coin == 0) Color.White else Color(0x80ffffff), fontSize = 16.sp)
                }
            }
            Row(
                horizontalArrangement = Arrangement.End,
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .weight(1f)
                    .height(57.dp)
                    .background(
                        brush = Brush.horizontalGradient(
                            if (comboBean.coin > 0) listOf(Color(0x007545FF), Color(0xFFFF2969), Color(0xFFFFFD3D)) else listOf(
                                Color.Transparent,
                                Color.Transparent
                            )
                        ), shape = RoundedCornerShape(topEnd = 7.dp)
                    )
            ) {
                if (comboBean.coin > 0) {
                    Text(
                        stringResource(id = R.string.cpd赢了, comboBean.coin),
                        color = if (comboBean.coin > 0) Color.White else Color(0x80ffffff),
                        fontSize = 16.sp,
                        lineHeight = 16.sp,
                        textAlign = TextAlign.Center
                    )
                }
                ComposeImage(
                    model = if (comboBean.coin > 0) R.drawable.cpd_won_light else R.drawable.cpd_won_grey,
                    modifier = Modifier.size(62.dp, 49.dp)
                )
            }

//            Text(
//                "ルール", modifier = Modifier
//                    .padding(top = 16.dp)
//                    .background(
//                        color = Color(0xff4701c4),
//                        shape = RoundedCornerShape(12.dp, 0.dp, 0.dp, 8.dp)
//                    )
//                    .padding(horizontal = 5.dp, vertical = 3.dp),
//                fontSize = 10.sp, color = Color(0xffffee47)
//            )
        }
        ComposeImage(
            model = R.drawable.ic_jackpot_rule,
            modifier = Modifier.align(Alignment.TopStart).padding(top = 16.dp)
        )
        //center jackpot number
        Box(
            modifier = Modifier
                .size(141.dp, 64.dp)
                .paint(
                    painter = painterResource(id = R.drawable.cpd_jackpot_bg),
                    contentScale = ContentScale.FillWidth,
                    alignment = Alignment.TopCenter
                )
                .align(Alignment.TopCenter)
        ) {
            var jackpot by remember(comboBean) {
                mutableStateOf(comboBean.jackpot)
            }
            val coinAnim = animateIntAsState(
                targetValue = jackpot,
                animationSpec = tween(durationMillis = 1000, easing = FastOutSlowInEasing)
            )

            Text(
                coinAnim.value.toString(),
                fontSize = 16.sp, lineHeight = 16.sp, fontWeight = FontWeight.SemiBold,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 6.dp),
                style = TextStyle(
                    brush = Brush.verticalGradient(
                        listOf(
                            Color(0xffff8a00),
                            Color(0xffffffa1)
                        ),
                    )
                )
            )
        }
        //右边的累计获得
        if (comboBean.totalCoin > 0) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .padding(end = 12.dp)
                    .size(86.dp, 44.dp)
                    .paint(
                        painter = painterResource(id = R.drawable.cpd_jackpot_got),
                        contentScale = ContentScale.FillWidth,
                        alignment = Alignment.TopCenter
                    )
                    .padding(top = 4.dp)
                    .align(Alignment.TopEnd)
            ) {
                Text(stringResource(id = R.string.cpd累计获得), color = Color(0xfffffb3d), fontSize = 10.sp, lineHeight = 10.sp)
                Text("X ${comboBean.totalCoin}", color = Color(0xfffffb3d), fontSize = 16.sp, lineHeight = 16.sp, fontWeight = FontWeight.Medium)
            }
        }
    }
}


@Composable
fun GiftPanelContent(
    giftModel: GiftListModel,
    selectCountList: IntArray = remember {
        intArrayOf(1, 5, 10, 20, 50, 100)
    },
    onRecharge: OnClick = {},
    onSelectedChange: (CPGift?) -> Unit = {},
    onSend: (CPGift, Int, GiftExtra) -> Boolean = { _, _, _ -> true },
    onComboDone: () -> Unit = {},
    giftPosition: GiftPosition? = null,
) {

    val position = if (giftPosition != null && giftPosition.giftId == -1) null else giftPosition

    val density = LocalDensity.current


    Box {
        //连击计时动画
        val anim = remember { Animatable(0f) }  //1
        val isCombining = remember {
            mutableStateOf(false)
        }

        var giftCount by rememberSaveable {
            mutableIntStateOf(1)
        }

        val showCountPop = rememberSaveable {
            mutableStateOf(false)
        }

        val initialPage = if (giftModel.preview) {
            -1
        } else {
            remember {
                if (position != null) {
                    if (position.tabId != null) {
                        giftModel.list.indexOfFirst { it.tabId == position.tabId }.takeIf { it != -1 } ?: 0
                    } else {
                        when (position.indexType) {
                            0 -> {
                                val index = giftModel.list.indexOfFirst { it.tabId == -1 && it.gifts.any { it.id == position.giftId } }
                                if (index != -1) {
                                    index
                                } else {
                                    giftModel.list.indexOfFirst { it.tabId != -1 && it.gifts.any { it.id == position.giftId } }.takeIf { it != -1 }
                                        ?: 0
                                }
                            }

                            1 -> {
                                giftModel.list.indexOfFirst { it.tabId == -1 }.takeIf { it != -1 } ?: 0
                            }

                            else -> {
                                giftModel.list.indexOfFirst { it.gifts.any { it.id == position.giftId } }.takeIf { it != -1 } ?: 0
                            }
                        }
                    }
                } else {
                    0
                }
            }
        }

        val pagerState = if (giftModel.preview) {
            rememberPagerState {
                1
            }
        } else {
            rememberPagerState(initialPage = initialPage) {
                giftModel.list.size
            }
        }

        LaunchedEffect(key1 = pagerState.currentPage) {

        }

        val selectedItemState = rememberSaveable(giftModel.preview, pagerState.settledPage) {
            onSelectedChange(null)
            mutableStateOf<CPGift?>(null)
        }

        var selectedItem by selectedItemState

        val popupShape = with(density) {
            remember {
                val radius = 8.dp.toPx()
                val caretLeft = 8.dp.toPx()
                val caretWidth = 12.dp.toPx()
                val caretHeight = 6.dp.toPx()
                GenericShape { size, layoutDirection ->
                    val rectH = size.height.minus(caretHeight)
                    addOutline(
                        Outline.Rounded(
                            RoundRect(
                                rect = size.copy(height = rectH).toRect(),
                                topLeft = CornerRadius(if (layoutDirection == LayoutDirection.Ltr) radius else radius),
                                topRight = CornerRadius(if (layoutDirection == LayoutDirection.Ltr) radius else radius),
                                bottomRight = CornerRadius(if (layoutDirection == LayoutDirection.Ltr) radius else radius),
                                bottomLeft = CornerRadius(if (layoutDirection == LayoutDirection.Ltr) radius else radius)
                            )
                        )
                    )
                    moveTo(caretLeft, rectH)
                    lineTo(caretLeft.plus(caretWidth.div(2)), rectH.plus(caretHeight))
                    lineTo(caretLeft.plus(caretWidth), rectH)
                }
            }
        }

        Column(modifier = Modifier.fillMaxWidth()) {

            if (selectCountList.isNotEmpty()) {
                PopupActionMenu(
                    expanded = showCountPop,
                    modifier = Modifier
                        .width(80.dp)
                        .background(Color(0xCC000000), popupShape)
                        .graphicsLayer {
                            shadowElevation = with(density) {
                                10.dp.toPx()
                            }
                            shape = popupShape
                        }
                        .padding(top = 4.dp, bottom = 10.dp),
                    verticalArrangement = Arrangement.spacedBy(5.dp),
                    alignment = Alignment.BottomEnd,
                    offset = LocalDensity.current.run {
                        IntOffset((-30).dp.roundToPx(), (-36).dp.roundToPx())
                    },
                ) {
                    selectCountList.forEach {
                        Text(
                            text = "$it",
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    giftCount = it
                                    showCountPop.value = false
                                },
                            color = Color.White,
                            fontSize = 14.sp,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                if (giftModel.preview) {
                    repeat(3) {
                        Spacer(
                            modifier = Modifier
                                .size(60.dp, 28.dp)
                                .clip(RoundedCornerShape(2.dp))
                                .background(Color(0xFF3A3A3A))
                        )
                    }
                } else {
                    AppScrollableTabRow(
                        tabs = giftModel.list,
                        pagerState = pagerState,
                        modifier = Modifier
                            .animateContentSize()
                            .weight(1f)
                            .height(28.dp),
                        indicatorColor = Color(0xFFFF5E8B),
                        fontSize = 14.sp,
                        tabHeight = 28.dp,
                        edgePadding = 8.dp,
                        spacePadding = 20.dp,
                    )

                    val descItemState = remember(selectedItemState) {
                        derivedStateOf {
                            selectedItem?.descIcon?.takeIf {
                                it.isNotEmpty()
                            }?.let {
                                it to selectedItem?.descLink.orEmpty()
                            }
                        }
                    }

                    val descItem = descItemState.value
                    if (descItem != null) {
                        val navController = LocalAppNavController.current
                        FillComposeImage(
                            model = descItem.first,
                            modifier = Modifier
                                .height(28.dp)
                                .noEffectClickable {
                                    navController.navigateWeb(descItem.second)
                                },
                            fillHeight = true,
                        )
                    }
                }
            }

            HorizontalPager(
                state = pagerState,
                modifier = Modifier
                    .padding(top = 12.dp)
                    .fillMaxWidth(),
                beyondViewportPageCount = 2,
                verticalAlignment = Alignment.Top,
                key = {
                    if (giftModel.preview) {
                        "preview"
                    } else {
                        "giftPanel-${giftModel.list[it].tabId}"
                    }
                }
            ) { page ->

                val giftTab = giftModel.list.getOrNull(page)
                val listItem = giftTab?.gifts.orEmpty()
                val state = if (position == null || giftModel.preview || page != initialPage) {
                    rememberLazyGridState()
                } else {
                    rememberLazyGridState(remember(giftModel) {
                        listItem.forEachIndexed { index, gift ->
                            if (gift.id == position.giftId) {
                                selectedItem = gift
                                onSelectedChange(gift)
                                return@remember index
                            }
                        }
                        0
                    })
                }
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(260.dp)
                ) {
                    LazyVerticalGrid(
                        columns = GridCells.Fixed(4),
                        modifier = Modifier.fillMaxSize(),
                        state = state,
                        horizontalArrangement = Arrangement.spacedBy(5.dp),
                        verticalArrangement = Arrangement.spacedBy(4.dp),
                        userScrollEnabled = !giftModel.preview,
                        flingBehavior = rememberOverscrollFlingBehavior { state },
                    ) {
                        if (giftModel.preview) {
                            items(12, key = { "preview_$it" }, contentType = { 0 }) {
                                Spacer(
                                    modifier = Modifier
                                        .height(104.dp)
                                        .clip(RoundedCornerShape(8.dp))
                                        .background(Color(0xFF3A3A3A))
                                )
                            }
                        } else {
                            items(listItem, contentType = { 1 }) { item ->
                                val selected = item.id == selectedItem?.id
                                Column(
                                    modifier = Modifier
                                        .height(104.dp)
                                        .clip(RoundedCornerShape(8.dp))
                                        .run {
                                            if (selected) {
                                                background(Color(0x14FFFFFF)).border(1.dp, Color(0xFFFF5E8B), RoundedCornerShape(8.dp))
                                            } else {
                                                this
                                            }
                                        }
                                        .clickable {
                                            selectedItem = item
                                            onSelectedChange(item)
                                        },
                                    horizontalAlignment = Alignment.CenterHorizontally,
                                ) {
                                    Spacer(modifier = Modifier.height(6.dp))

                                    val badgeNumber = item.giftCount
                                    Box(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .height(58.dp)
                                    ) {
                                        ComposeImage(
                                            model = item.icon, modifier = Modifier
                                                .align(Alignment.Center)
                                                .size(58.dp)
                                        )
                                        Badge(
                                            modifier = Modifier
                                                .align(Alignment.TopEnd)
                                                .alpha(if (badgeNumber > 0) 1f else 0f),
                                            containerColor = Color(0xFFF76560),
                                            contentColor = Color.White,
                                        ) {
                                            Text(text = badgeNumber.formatCount())
                                        }
                                    }
                                    Spacer(modifier = Modifier.height(4.dp))
                                    AutoSizeText(
                                        text = item.name,
                                        color = Color.White,
                                        fontSize = 12.sp,
                                        maxLines = 1,
                                        minTextSize = 10.sp,
                                        maxTextSize = 12.sp
                                    )
                                    Spacer(modifier = Modifier.height(3.dp))
                                    Row(verticalAlignment = Alignment.CenterVertically) {
                                        Image(
                                            painter = painterResource(id = R.drawable.ic_cpd_coin),
                                            contentDescription = null,
                                            modifier = Modifier.size(10.dp)
                                        )
                                        Spacer(modifier = Modifier.width(2.dp))
                                        AppText(text = "${item.price}", color = Color(0x73FFFFFF), fontSize = 10.sp)
                                    }
                                }
                            }
                        }
                    }
                    if (!giftModel.preview && listItem.isEmpty()) {
                        Column(
                            modifier = Modifier
                                .align(Alignment.Center)
                                .padding(bottom = 20.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_cpd_empty_gift_list),
                                contentDescription = null,
                                modifier = Modifier
                                    .padding(top = 45.dp)
                                    .size(120.dp)
                            )

                            Text(
                                text = stringResource(id = R.string.cpd什么都没有),
                                color = colorResource(id = R.color.white_alpha_30),
                                fontSize = 12.sp
                            )
                        }
                    }
                }
            }

            Row(
                modifier = Modifier
                    .padding(top = 15.dp)
                    .height(48.dp)
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    modifier = Modifier
                        .padding(end = 10.dp)
                        .weight(1f),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(painter = painterResource(id = R.drawable.ic_cpd_coin), contentDescription = null, Modifier.size(20.dp))
                    AppText(
                        text = formatCoinCount(giftModel.balance),
                        modifier = Modifier
                            .padding(start = 4.dp)
                            .weight(1f, false),
                        color = Color(0xFFFFFFFF),
                        fontSize = 14.sp,
                        maxLines = 1,
                    )

                    Text(
                        text = stringResource(id = R.string.cpd_charge),
                        modifier = Modifier
                            .padding(start = 12.dp)
                            .noEffectClickable(onClick = onRecharge),
                        color = Color(0xFFFF5E8B),
                        fontSize = 14.sp,
                        lineHeight = 14.sp,
                    )

                    Icon(
                        painter = painterResource(id = R.drawable.ic_arrow_right),
                        contentDescription = null,
                        modifier = Modifier.size(12.dp),
                        tint = Color(0xFFFF5E8B)
                    )
                }


                if (!isCombining.value) {
                    Row(
                        modifier = Modifier
                            .height(28.dp)
                            .clip(CircleShape)
                            .border(1.dp, Color(0xFFFF5E8B), CircleShape)
                            .noEffectClickable(enabled = !giftModel.preview) {
                                showCountPop.value = true
                            },
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Spacer(modifier = Modifier.width(7.dp))
                        Image(
                            painter = painterResource(id = R.drawable.ic_arrow_left),
                            contentDescription = null,
                            modifier = Modifier.size(12.dp)
                        )
                        Spacer(modifier = Modifier.width(3.dp))
                        AppText(
                            text = giftCount.toString(),
                            modifier = Modifier.widthIn(min = 24.dp),
                            color = Color.White,
                            fontSize = 12.sp,
                            textAlign = TextAlign.Center
                        )
                        Spacer(modifier = Modifier.width(3.dp))
                        AppButton(
                            text = stringResource(id = R.string.cpd_txt_give),
                            modifier = Modifier
                                .width(60.dp)
                                .fillMaxHeight(),
                            color = Color.White,
                            background = Color(0xFFFF5E8B),
                            fontSize = 12.sp,
                            contentPadding = PaddingValues(horizontal = 5.dp),
                            enabled = !giftModel.preview
                        ) {
                            val giftListItem = giftModel.list[pagerState.settledPage]
                            val good = giftListItem.gifts.find {
                                it.id == selectedItem?.id
                            }
                            if (good != null) {
                                onSend(good, giftCount, GiftExtra(giftListItem.tabId == -1))
                            } else {
                                toastRes(R.string.cpd_please_select_gift)
                                selectedItem = null
                                onSelectedChange(null)
                            }
                        }
                    }
                }
            }
        }

        //当选中item改变时需要判断是否重置连击
        LaunchedEffect(key1 = selectedItem) {
            if (giftModel.comboInfo != null) {
                onComboDone()
            }
        }

        DisposableEffect(key1 = Unit) {
            onDispose {
                onComboDone()
            }
        }

        LuckyGiftCombineButton(
            comboInfo = giftModel.comboInfo,
            modifier = Modifier.align(Alignment.BottomEnd),
            anim = anim,
            isCombining = isCombining
        ) { isFinish ->
            if (isFinish) {
                onComboDone()
            } else {
                val giftListItem = giftModel.list[pagerState.settledPage]
                val good = giftListItem.gifts.find {
                    it.id == selectedItem?.id
                }
                if (good != null) {
                    onSend(good, giftCount, GiftExtra(giftListItem.tabId == -1, comboId = giftModel.comboInfo?.comboId ?: ""))
                } else {
                    toastRes(R.string.cpd_please_select_gift)
                    selectedItem = null
                    onSelectedChange(null)
                }
            }
        }
    }
}

@Composable
private fun LuckyGiftCombineButton(
    comboInfo: ComboBean?,
    modifier: Modifier = Modifier,
    anim: Animatable<Float, AnimationVector1D>,
    isCombining: MutableState<Boolean>,
    onSendEvent: (Boolean) -> Unit,
) {
    val scope = rememberCoroutineScope()

    LaunchedEffect(key1 = comboInfo?.comboId) {
        if (comboInfo != null) {
            scope.launch {
                isCombining.value = true
                anim.stop()
                anim.snapTo(0f)
                anim.animateTo(360f, tween(5000, easing = LinearEasing)) {
                    if (this.value == this.targetValue) {
                        isCombining.value = false
                        onSendEvent(true)
                    }
                }
            }
        } else {
            isCombining.value = false
            anim.stop()
            anim.snapTo(0f)
        }
    }
    val comboBean = comboInfo ?: return
    if (isCombining.value) {
        //是否被按住?
        var isButtonPressing by remember {
            mutableStateOf(false)
        }
        //中间的字体大小
        val centerTipWidth by animateDpAsState(
            targetValue = if (isButtonPressing) 65.dp else 80.dp,
            animationSpec = tween(durationMillis = 100, easing = LinearEasing), label = ""
        )
        Box(modifier = modifier) {
            Box(modifier = Modifier
                .padding(top = 10.dp)
                //方案一: 需要等待上一次结果返回, 可能会出现网络差的情况下点击次数对不上
//                .click {
//                    val giftListItem = giftModel.list[pagerState.settledPage]
//                    val good = giftListItem.gifts.find {
//                        it.id == selectedItem?.id
//                    }
//                    if (good != null) {
//                        onSend(good, giftCount, GiftExtra(giftListItem.tabId == -1, comboId = comboInfo?.comboId ?: ""))
//                    } else {
//                        toastRes(R.string.cpd_please_select_gift)
//                    }
//                }
                //方案二: 不等待, 点多少次就送多少次, 在本地状态结束时直接finishCombo
                .touchPressing(anim, isCombining, pressCallback = {
                    isButtonPressing = it
                }) { isTap, hasDone ->
                    if (hasDone) {
                        onSendEvent(true)
                    } else {
                        onSendEvent(false)
//                        //这里可以根据it判断是单击还是连击
//                        val giftListItem = giftModel.list[pagerState.settledPage]
//                        val good = giftListItem.gifts.find {
//                            it.id == selectedItem?.id
//                        }
//                        if (good != null) {
//                            return@touchPressing onSend(good, giftCount, GiftExtra(giftListItem.tabId == -1, comboId = ""))
//                        } else {
//                            toastRes(R.string.cpd_please_select_gift)
//                            return@touchPressing false
//                        }
                    }

                    return@touchPressing true
                }
                .drawWithCache {
                    val path = Path()
                    onDrawBehind {
                        path.reset()
                        path.addArc(Rect(Offset(0f, 0f), size = Size(96.dp.toPx(), 96.dp.toPx())), -90f, anim.value)
                        drawPath(path, color = Color(0xFFFFE24A), style = Stroke(width = 4.dp.toPx(), cap = StrokeCap.Round))
                    }
                }
            ) {
                val transition = rememberInfiniteTransition(label = "active_anim")
                val fraction by transition.animateFloat(
                    initialValue = 0f,
                    targetValue = 360f,
                    animationSpec = infiniteRepeatable(
                        tween(1500, easing = LinearEasing),
                        repeatMode = RepeatMode.Restart
                    ),
                    label = "background"
                )
                ComposeImage(model = R.drawable.cpd_gift_combine_background, modifier = Modifier.rotate(fraction))
                ComposeImage(
                    model = R.drawable.cpd_gift_combine_text,
                    modifier = Modifier
                        .align(Alignment.Center)
                        .width(centerTipWidth)
                        .aspectRatio(2.85714286f)
                )
            }
            Text(
                "x${comboBean.comboCount}", color = CpdColors.FFFF5E8B,
                lineHeight = 12.sp,
                fontSize = 12.sp,
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .background(
                        Color.White, shape = CircleShape
                    )
                    .border(1.dp, CpdColors.FFFF5E8B, shape = CircleShape)
                    .padding(8.dp, 4.dp)
            )
        }
    }
}


private fun formatCoinCount(count: Int): String {
    return if (count < 10000) {
        count.toString()
    } else {
        BigDecimal(count).divide(BigDecimal(10000.0)).setScale(1, RoundingMode.FLOOR).toString() + "w"
    }
}

@Preview
@Composable
private fun PreviewLuckyGiftCombineButton() {
    val anim = remember { Animatable(0f) }  //1
    val isCombining = remember {
        mutableStateOf(false)
    }
    LuckyGiftCombineButton(
        comboInfo = ComboBean(comboId = "11", comboCount = 13, totalCoin = 100, coin = 10),
        anim = anim,
        isCombining = isCombining
    ) {

    }
}

@Preview
@Composable
private fun PreviewGiftPanel() {
    PreviewCupidTheme {
        Box(
            modifier = Modifier
                .background(Color(0xFF292929), RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                .padding(16.dp)
        ) {
            GiftPanelContent(
                GiftListModel(
                    true, comboInfo = ComboBean(
                        "1", 100, 10, 10, 10, 10, 100, 100
                    )
                ), intArrayOf()
            )
        }
    }
}


@Composable
fun GiftBannerView(giftModel: GiftWrapper) {
    Row(
        modifier = Modifier
            .padding(start = 10.dp)
            .size(256.dp, 48.dp)
            .clip(RoundedCornerShape(topStart = 24.dp, bottomStart = 24.dp))
            .background(brush = Brush.horizontalGradient(listOf(Color(0xFFFF9EC7), Color(0xFFFF96E1), Color(0x00FF8EFA)))),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Spacer(modifier = Modifier.width(6.dp))

        CircleComposeImage(model = giftModel.sender.avatarUrl, modifier = Modifier.size(36.dp))

        Spacer(modifier = Modifier.width(4.dp))

        Column(
            modifier = Modifier.weight(1f)
        ) {

            AppText(
                text = giftModel.sender.nickname,
                modifier = Modifier.basicMarquee(),
                color = Color.White,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                maxLines = 1
            )
            val receiverTarget = if (giftModel.receivers.isEmpty()) stringResource(R.string.cpd_all_people) else giftModel.receiverName
            Text(
                text = "${stringResource(id = R.string.cpd送给)}${receiverTarget}${giftModel.gift.name}",
                modifier = Modifier.basicMarquee(),
                color = Color.White,
                fontSize = 12.sp,
                maxLines = 1
            )
        }

        Spacer(modifier = Modifier.width(4.dp))

        ComposeImage(model = giftModel.gift.icon, modifier = Modifier.size(36.dp))

        Spacer(modifier = Modifier.width(4.dp))

        Text(text = "x${giftModel.count}", color = Color(0xFFFF5E8B), fontSize = 16.sp, fontWeight = FontWeight.Medium)

        Spacer(modifier = Modifier.width(8.dp))

    }
}

@Preview
@Composable
private fun PreviewGiftBanner() {
    GiftBannerView(GiftWrapper(sender = userForPreview, receivers = listOf(userForPreview), gift = Gift(desc = "")))
}