package com.qyqy.cupid.im.panel.gift

import android.graphics.Bitmap
import android.os.Parcelable
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.MutableTransitionState
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.produceState
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.overseas.common.utils.AwaitContinuation
import com.overseas.common.utils.awaitContinuation
import com.qyqy.cupid.data.ComboBean
import com.qyqy.cupid.ui.GiftExtra
import com.qyqy.ucoo.AppUserPartition
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.http.getIntOrNull
import com.qyqy.ucoo.http.getOrNull
import com.qyqy.ucoo.http.getStringOrNull
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.bean.CPGift
import com.qyqy.ucoo.im.bean.GiftTab
import com.qyqy.ucoo.im.bean.GiftWrapper
import com.qyqy.ucoo.im.compat.ConversationType
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.IMCompatListener
import com.qyqy.ucoo.im.compat.MsgFilter
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.UCGiftMessage
import com.qyqy.ucoo.im.compat.UCInstanceMessage
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.sUserFlow
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.user.gift.GiftRepository
import com.qyqy.ucoo.utils.GiftEffectHelper
import com.qyqy.ucoo.utils.YYEVAResourceFetcher
import com.qyqy.ucoo.utils.logW
import com.qyqy.ucoo.utils.takeIsNotEmpty
import com.yy.yyeva.EvaAnimConfig
import com.yy.yyeva.inter.IEvaAnimListener
import com.yy.yyeva.inter.IEvaFetchResource
import com.yy.yyeva.mix.EvaResource
import com.yy.yyeva.mix.EvaSrc
import com.yy.yyeva.util.EvaConstant
import com.yy.yyeva.util.ScaleType
import com.yy.yyeva.view.EvaAnimViewV3
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.selects.select
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement
import java.io.File
import java.util.UUID
import kotlin.time.Duration.Companion.seconds

/**
 * 礼物场景
 */
enum class GiftScene {
    Group,//部落、家族
    ROOM,//语音房
    CP//语音房
}

/**
 *
 * @param [bid] 业务id,单聊为userid,家族为家族id
 * @param [rcId] 融云会话id
 *
 */
class GiftViewModel constructor(
    private val bid: String,
    val type: ConversationType,
    val rcId: String = bid
) : ViewModel() {

    private val giftRepository = GiftRepository()

    /**
     * 通道有好几个, 但是对应的播放动效的组件只有一个
     *
     * 讲道理这个地方应该只存放礼物的特效
     *
     * 但是为了方便把进场的特效也放进去了
     * 1礼物特效和3进场特效共用一个YYEVA组件
     * 2横幅使用原生动画
     * 在2.41.0 为之前的逻辑做添补,添加一个新的yyeva组件
     */

    //1. 礼物特效通道
    private val giftEffectChannel: Channel<GiftEffect> = Channel()

    //2. 礼物横幅特效
    private val giftBannerChannel: Channel<GiftBanner> = Channel()

    //3. 进场特效
    private val enterEffectChannel: Channel<GiftEffect> = Channel()

    private val normalEffectChannel = Channel<GiftEffect>()

    private val normalEffectFlow = normalEffectChannel.receiveAsFlow()

    //礼物横幅不配合yyeva组件
    private val giftBannerFlow = giftBannerChannel.receiveAsFlow()

    val giftListModelState = mutableStateOf(GiftListModel())

    //region 消息处理

    private val msgListener = object : IMCompatListener {

        override val filter: MsgFilter = MsgFilter(rcId)

        override fun onRecvNewMessage(message: UCInstanceMessage, offline: Boolean) {
            handleRecvNewMessage(message)
        }

        override fun onPlayHistoryGiftEffect(messages: List<UCGiftMessage>) {
            messages.forEach {
                handleRecvNewMessage(it)
            }
        }

    }

    private fun handleRecvNewMessage(msg: UCInstanceMessage) {
        if (msg is UCGiftMessage) {
            handleGiftMsg(msg)
        } else if (msg is UCCustomMessage) {
            when (msg.cmd) {
                MsgEventCmd.USER_ENTRANCE -> {
                    val sendUser = msg.getJsonValue<AppUser>("user")
                    val effectUrl = sendUser?.entryEffect?.takeIsNotEmpty()
                    val entranceBanner = sendUser?.entranceBanner?.takeIsNotEmpty()

                    viewModelScope.launch {

                        val list = coroutineScope {
                            listOf(
                                async {
                                    GiftEffectHelper.getGiftEffectFile(effectUrl, null, 15.seconds)
                                },
                                async {
                                    GiftEffectHelper.getGiftEffectFile(entranceBanner, null, 15.seconds)
                                }
                            )
                        }.awaitAll().filterNotNull()

                        list.let {
                            if (list.isEmpty()) {
                                return@let
                            }
                            if (list.size == 2) {
                                enterEffectChannel.send(GiftEffect(queue1 = listOf(list[0]), queue2 = listOf(list[1]), extra = sendUser))
                            } else {
                                enterEffectChannel.send(GiftEffect(list[0], extra = sendUser))
                            }
                        }
                    }
                }
            }
        }
    }

    private fun handleGiftMsg(msg: UCGiftMessage) {
        val giftModel = msg.gift
        viewModelScope.launch {
            val url = giftModel.gift.effectFile
            if (url.isNotEmpty()) {
                if (giftModel.isBlinxBox && giftModel.blindboxEffectFile.isNotEmpty()) {
                    launch {
                        val list = coroutineScope {
                            listOf(
                                async {
                                    GiftEffectHelper.getGiftEffectFile(url, null, 15.seconds)
                                },
                                async {
                                    GiftEffectHelper.getGiftEffectFile(
                                        giftModel.blindboxEffectFile,
                                        null,
                                        15.seconds
                                    )
                                }
                            )
                        }.awaitAll().filterNotNull()

                        if (list.size == 2) {
                            giftEffectChannel.send(
                                GiftEffect(
                                    list[0],
                                    list[1],
                                    msg,
                                    awaitContinuation()
                                )
                            )
                        }
                    }
                } else {
                    launch {
                        GiftEffectHelper.getGiftEffectFile(
                            giftModel.gift.effectFile,
                            null,
                            15.seconds
                        )?.also {
                            giftEffectChannel.send(GiftEffect(it, null, msg, awaitContinuation()))
                        }
                    }
                }
            }

            launch {
                giftBannerChannel.send(GiftBanner(giftModel, awaitContinuation()))
            }
        }
    }

    //endregion

    init {
        fetchGift()

        viewModelScope.launch {
            launch {
                sUserFlow.distinctUntilChangedBy {
                    it.balance
                }.collectLatest {
                    giftListModelState.value = giftListModelState.value.copy(balance = it.balance)
                }
            }

            launch {
                while (isActive) {
                    select {
                        enterEffectChannel.onReceive { // 优先级更高
                            normalEffectChannel.send(it)
                            it.await()
                        }
                        giftEffectChannel.onReceive {
                            normalEffectChannel.send(it)
                            it.await()
                        }
                    }
                }
            }
        }

        IMCompatCore.addIMListener(msgListener)
    }

    override fun onCleared() {
        IMCompatCore.removeIMListener(msgListener)
        if (AppUserPartition.isCupid) {

            //结束的时候用全局的coroutineScope，避免在onCleared中挂起
            finishGiftCombo()
        }
    }

    fun fetchGift() {
        viewModelScope.launch {
            /**
             * 为什么iOS有scene参数，而Android没有？
             */
//            val method = if (AppUserPartition.isCupid) {
//                val senceType = when (type) {
//                    ConversationType.GROUP -> 1
//                    ConversationType.CHATROOM -> 2
//                    else -> null
//                }
//                val senceID = if (senceType != null) bid else null
//                giftRepository.getGiftList(senceType, senceID)
//            } else {
//                giftRepository.getGiftList()
//            }

            //done 2.41.0 OK这个版本全部加上sence相关

            val senceType = when (type) {
                ConversationType.GROUP -> 1
                ConversationType.CHATROOM -> 2
                ConversationType.C2C -> 4
                else -> null
            }
            val senceID = if (senceType != 0) bid else null

            giftRepository.getGiftList(senceType, senceID).onSuccess {
                giftListModelState.value = giftListModelState.value.copy(
                    preview = false,
                    balance = it.balance,
                    list = it.tabs
                )
            }
        }
    }

    fun sendGift(gift: CPGift, count: Int, extra: GiftExtra) {
        viewModelScope.launch {
            giftRepository.sendGiftOnSingle(
                bid,
                gift.id,
                count,
                extra.fromPacket,
                extra.greetings,
                extra.isIntimate
            )
                .onSuccess { json ->
                    handleSendGiftResult(json, gift, count, extra)
                }
                .toastError()
        }
    }

    fun sendGiftToGroup(targets: List<String>, gift: CPGift, count: Int, extra: GiftExtra) {
        sendGiftAt(
            giftScene = GiftScene.Group,
            targets = targets,
            gift = gift,
            count = count,
            extra = extra
        )
    }

    fun sendGiftAt(
        giftScene: GiftScene,
        targets: List<String>,
        gift: CPGift,
        count: Int,
        extra: GiftExtra,
    ) {
        viewModelScope.launch {
            val id = bid.toIntOrNull() ?: return@launch
            when (giftScene) {
                GiftScene.Group -> giftRepository.sendGiftOnTribe(
                    id,
                    targets.toSet(),
                    gift.id,
                    count,
                    extra.fromPacket,
                    extra.greetings,
                    extra.comboId
                )
                    .onSuccess { json ->
                        handleSendGiftResult(json, gift, count, extra)
                    }
                    .toastError()

                GiftScene.ROOM -> giftRepository.sendGiftOnRoom(
                    id,
                    targets.toSet(),
                    gift.id,
                    count,
                    extra.fromPacket,
                    extra.greetings,
                    extra.comboId
                )
                    .onSuccess { json ->
                        handleSendGiftResult(json, gift, count, extra)
                    }
                    .toastError()

                GiftScene.CP -> giftRepository.sendGiftOnCouple(
                    id,
                    targets.toSet(),
                    gift.id,
                    count,
                    extra.fromPacket,
                    extra.greetings,
                    extra.comboId
                )
                    .onSuccess { json ->
                        handleSendGiftResult(json, gift, count, extra)
                    }
                    .toastError()
            }
        }
    }


    //region 幸运礼物逻辑
    @Volatile
    private var comboInfoRefreshJob: Job? = null
    private val JOB_LOCK = Any()

    /**
     * 获取幸运礼物, 10s刷新一次
     * 实时更新
     *
     */
    fun startRefreshComboGiftInfo() {
        synchronized(JOB_LOCK) {
            if (AppUserPartition.isCupid) {
                comboInfoRefreshJob?.cancel()
                comboInfoRefreshJob = null
                comboInfoRefreshJob = viewModelScope.launch {
                    while (true) {
                        try {
                            giftRepository.getComboGiftInfo().onSuccess {
                                val giftElements = it.getOrNull("gifts")
                                    ?: throw IllegalArgumentException("不需要刷新幸运礼物, 幸运礼物是空的")
                                val newComboGifts =
                                    sAppJson.decodeFromJsonElement<List<CPGift>>(giftElements)
                                //            val currentGifts = giftListModelState.value.list.toMutableList()
                                //
                                //            currentGifts.forEach { tab ->
                                //                if (tab.tabType == GiftTab.TYPE_LUCKY) {
                                //                    newComboGifts.forEach { newV ->
                                //                        val pos = tab.gifts.indexOfFirst { it.id == newV.id }
                                //                        if (pos != -1) {
                                //                            tab.gifts.set(pos, newV)
                                //                        }
                                //                    }
                                //                }
                                //            }
                                giftListModelState.value =
                                    giftListModelState.value.copy(
                                        comboJumpLink = it.getStringOrNull(
                                            "jump_link"
                                        ) ?: "", comboList = newComboGifts
                                    )
                            }
                            delay(10_000L)
                        } catch (e: IllegalArgumentException) {
                            logW(e)
                            break
                        }
                    }
                }
            }
        }
    }

    fun stopRefreshComboGift() {
        synchronized(JOB_LOCK) {
            comboInfoRefreshJob?.cancel()
            comboInfoRefreshJob = null
        }
    }

    @Volatile
    private var isComboFinishing = false

    @Synchronized
    fun finishGiftCombo() {
        if (!isComboFinishing) {
            val comboInfo = giftListModelState.value.comboInfo
            if (comboInfo == null || comboInfo.comboId.isEmpty()) {
                return
            }
            isComboFinishing = true
            giftListModelState.value.also {
                giftListModelState.value = it.copy(comboInfo = null)
            }
            appCoroutineScope.launch {
                val senceType = when (type) {
                    ConversationType.GROUP -> 1
                    ConversationType.CHATROOM -> 2
                    else -> null
                }
                if (senceType == null) {
                    logW("not supported scene type: => %s", type)
                    return@launch
                }
                giftRepository.finishGiftCombo(
                    comboInfo.gift_id,
                    comboInfo.comboId,
                    senceType,
                    bid
                ).onSuccess {
                    isComboFinishing = false
                }.onFailure {
                    isComboFinishing = false
                }
            }
        }
    }
    //endregion

    private fun handleSendGiftResult(json: JsonObject, gift: CPGift, count: Int, extra: GiftExtra) {
        json.getIntOrNull("balance")?.also { balance ->
            giftListModelState.value.also {
                if (extra.fromPacket) {
                    giftListModelState.value = it.copy(balance = balance, list = it.list.map { tab ->
                        if (tab.tabId == -1) {
                            tab.copy(gifts = tab.gifts.mapNotNull { item ->
                                if (gift.id == item.id) {
                                    if (item.giftCount > count) {
                                        item.copy(giftCount = item.giftCount - count)
                                    } else {
                                        null
                                    }
                                } else {
                                    item
                                }
                            }.toMutableList())
                        } else {
                            tab
                        }
                    })
                } else {
                    giftListModelState.value = it.copy(balance = balance)
                }
            }
        }
        json.getStringOrNull("toast_msg").takeIsNotEmpty()?.also {
            toast(it)
        }
        json.getOrNull("combo_info")?.let {
            val comboBean = sAppJson.decodeFromJsonElement<ComboBean>(it)
            comboBean.gift_id = gift.id
            giftListModelState.value.also {
                giftListModelState.value =
                    it.copy(comboInfo = if (comboBean.comboId.isNotBlank()) comboBean else null)
            }
        }
    }

    //region 礼物组件
    /**
     * 2.41.0 新增更新
     * 为了同时运行普通动效和进场横幅动效, 将GiftEffect对象中的动效文件分为两个queue
     * 保留原来的单个file参数
     *
     * todo 如果进场特效和礼物特效在同一个队列中, 那还需要保留[enterEffectChannel]属性吗?
     */
    @Composable
    fun GiftEffectView() {
        //普通礼物特效
        GiftEffectForFlow(flow = normalEffectFlow)
        //礼物计数横幅
        GiftBannerView()
    }

    /**
     * 礼物横幅组件
     */
    @Composable
    private fun GiftBannerView() {
        val banner by giftBannerFlow.run {
            produceState<GiftBanner?>(null, this) {
                collect {
                    <EMAIL> = it
                    it.await()
                }
            }
        }

        val scope = rememberCoroutineScope()

        banner?.apply {
            val state = remember(this) {
                MutableTransitionState(false).apply {
                    // Start the animation immediately.
                    targetState = true
                }
            }

            AnimatedVisibility(
                visibleState = state,
                modifier = Modifier.padding(top = 180.dp),
                enter = slideInHorizontally(initialOffsetX = { -it }),
                exit = fadeOut() + slideOutHorizontally(targetOffsetX = { -it }),
            ) {
                GiftBannerView(giftModel)
            }

            LaunchedEffect(this) {
                delay(3500)
                state.targetState = false

                snapshotFlow {
                    state.isIdle && !state.currentState
                }.filter { it }.onEach {
                    resume()
                }.launchIn(scope)
            }
        }
    }

    @Composable
    private fun GiftEffectForFlow(flow: Flow<GiftEffect>) {
        val effect by flow.run {
            produceState<GiftEffect?>(null, this) {
                collect {
                    <EMAIL> = it
                }
            }
        }

        effect?.apply {
            //总进度
            val totalCount by remember {
                derivedStateOf {
                    var cnt = 0
                    if (queue1.isNullOrEmpty()) {
                        cnt++
                    }
                    if (queue2.isNullOrEmpty()) {
                        cnt++
                    }
                    cnt
                }
            }
            //已完成进度
            var doneCount by remember(msg) {
                mutableStateOf(0)
            }

            val ctx = LocalContext.current

            val evaResourceFetcher by remember(msg) {
                mutableStateOf(YYEVAResourceFetcher(mapOf("user" to this.extra as? User), ctx))
            }

            queue1?.let { effects ->
                EvaEffectView("$uuid-queue1", *effects.toTypedArray(),
                    iEvaFetchResource = evaResourceFetcher,
                    onStart = { hasPlayed ->
                        isPlayed = true
                        if (hasPlayed && msg != null && !msg.isPlayed) {
                            msg.isPlayed = true
                        }
                    }) {
                    doneCount++
                }
            }

            queue2?.let { effects ->
                EvaEffectView("$uuid-queue2", *effects.toTypedArray(),
                    iEvaFetchResource = evaResourceFetcher,
                    onStart = { hasPlayed ->
                        isPlayed = true
                        if (hasPlayed && msg != null && !msg.isPlayed) {
                            msg.isPlayed = true
                        }
                    }) {
                    doneCount++
                }
            }

            LaunchedEffect(key1 = doneCount) {
                if (doneCount >= totalCount) {
                    resume()
                }
            }
        }
    }

    /**
     * 动效组件
     *
     * @param key 标记运行的动效GiftEffect
     * @param files 需要运行的动效文件列表
     * @param iEvaFetchResource yyeva中的动效资源提供者, 如果不传的话有可能动效不播放
     * @param onStart 动效开始播放prepare = true时说明已经执行过startPlay, =false 时则反之
     * @param onComplete 所有的动效文件播放完毕
     */
    @Composable
    private fun EvaEffectView(
        key: Any?,
        vararg files: File?,
        iEvaFetchResource: IEvaFetchResource? = null,
        onStart: (prepared: Boolean) -> Unit = {},
        onComplete: () -> Unit = {}
    ) {
        val playQueue = remember(key) {
            mutableListOf(*files)
        }
        val playStatus = remember(key) {
            mutableStateOf(false)
        }

        val evaResource by remember(iEvaFetchResource) {
            derivedStateOf {
                iEvaFetchResource
                    ?: object : IEvaFetchResource {
                        override fun releaseSrc(resources: List<EvaResource>) {

                        }

                        override fun setImage(resource: EvaResource, result: (Bitmap?, EvaSrc.FitType?) -> Unit) {
                            result(null, null)
                        }

                        override fun setText(resource: EvaResource, result: (EvaResource) -> Unit) {
                            //未定义
                            result(resource)
                        }

                    }
            }
        }

        AndroidView(
            factory = {
                EvaAnimViewV3(it).apply {
                    setVideoMode(EvaConstant.VIDEO_MODE_SPLIT_HORIZONTAL)
                    setScaleType(ScaleType.FIT_CENTER)
                }
            },
            modifier = Modifier.fillMaxSize(),
            onReset = {
                it.stopPlay()
                it.setAnimListener(null)
            },
            onRelease = {
                it.stopPlay()
                it.setAnimListener(null)
                it.setFetchResource(null)
            }
        ) {
            if (playStatus.value) {
                return@AndroidView
            }
            playStatus.value = true
            onStart(false)
            it.setAnimListener(null)
            it.setFetchResource(evaResource)
            it.stopPlay()
            it.setAnimListener(object : IEvaAnimListener {
                override fun onFailed(errorType: Int, errorMsg: String?) {
                    onStart(true)
                    onComplete()
                }

                override fun onVideoStart(isRestart: Boolean) {
                    onStart(true)
                }

                override fun onVideoComplete(lastFrame: Boolean) {
                    var nextFile = playQueue.removeFirstOrNull()

                    while (nextFile == null) {
                        nextFile = playQueue.removeFirstOrNull()
                        if (nextFile == null && playQueue.isEmpty()) {
                            break
                        }
                    }

                    if (nextFile != null) {
                        it.startPlay(nextFile)
                    } else {
                        onComplete()
                    }
                }

                override fun onVideoDestroy() = Unit

                override fun onVideoRender(frameIndex: Int, config: EvaAnimConfig?) = Unit

                override fun onVideoRestart() = Unit

            })

            var next = playQueue.removeFirstOrNull()

            while (next == null) {
                next = playQueue.removeFirstOrNull()
                if (next == null && playQueue.isEmpty()) {
                    break
                }
            }
            if (next != null) {
                it.startPlay(next)
            } else {
                onComplete()
            }
        }
    }
    //endregion
}

@Stable
@Parcelize
data class GiftListModel(
    val preview: Boolean = true,
    val balance: Int = 0,
    val list: List<GiftTab> = emptyList(),
    val comboInfo: ComboBean? = null,
    val comboJumpLink: String = "",
    val comboList: List<CPGift> = emptyList(),
) : Parcelable {
    fun getGift(giftID: Int): Pair<GiftTab, CPGift>? {
        if (giftID == -1) {
            return null
        }
        list.forEach {
            val gift = it.gifts.find { it.id == giftID }
            if (gift != null) {
                return Pair(it, gift)
            }
        }
        return null
    }
}


@Stable
private data class GiftEffect(
    val queue1: List<File?>? = null,
    val queue2: List<File?>? = null,
    val msg: UCGiftMessage? = null,
    val extra: Any? = null,
    private val taskAwait: AwaitContinuation = awaitContinuation(),
    val uuid: String = UUID.randomUUID().toString(),
) {
    //普通礼物
    constructor(file: File, boxFile: File? = null, msg: UCGiftMessage? = null, extra: Any?, taskAwait: AwaitContinuation = awaitContinuation()) : this(
        queue1 = listOf(boxFile, file),
        msg = msg,
        taskAwait = taskAwait,
        extra = extra
    )

    var isPlayed = false

    suspend fun await() {
        taskAwait.suspendUntilWithTimeout(15_000) // 默认最长动画15秒
    }

    fun resume() {
        taskAwait.resume(Unit)
    }
}


@Stable
private data class GiftBanner(
    val giftModel: GiftWrapper,
    private val taskAwait: AwaitContinuation,
) {

    suspend fun await() {
        taskAwait.suspendUntilWithTimeout(5_000) // 默认最长动画5秒
    }

    fun resume() {
        taskAwait.resume(Unit)
    }
}
