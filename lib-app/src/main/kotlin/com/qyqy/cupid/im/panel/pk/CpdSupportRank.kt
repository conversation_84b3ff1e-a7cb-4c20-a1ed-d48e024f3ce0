package com.qyqy.cupid.im.panel.pk

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.bean.IRankItem
import com.qyqy.ucoo.bean.RankItem
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.utils.OnClick


/**
 * PK模式助威榜
 */
@Composable
fun CpdSupportRank(title: String, desc: String, data: List<IRankItem>, onUserAvatarClick: (User) -> Unit = { _ -> }) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .height(420.dp)
            .background(MaterialTheme.colorScheme.onSecondary, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.height(20.dp))
        Text(
            text = title,
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.Center,
            fontSize = 16.sp,
            color = Color.White,
            fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.height(20.dp))
        Text(text = desc, color = Color.White.copy(alpha = 0.5f), fontSize = 12.sp)
        Spacer(modifier = Modifier.height(8.dp))
        LazyColumn(modifier = Modifier.fillMaxSize()) {
            items(data) { item ->
                UserItem(index = item.index, user = item.user, score = item.value) {
                    onUserAvatarClick.invoke(item.user)
                }
            }
        }
    }
}

@Composable
fun UserItem(index: Int, user: User, score: Int, onUserAvatarClick: OnClick = {}) {
    val color = remember(index) {
        when (index) {
            1 -> Color(0xFFF53F3F)
            2 -> Color(0xFFFF7D00)
            3 -> Color(0xFFFFC700)
            else -> Color(0xFF8D8E8E)
        }
    }
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "$index",
            fontSize = 20.sp,
            color = color,
            modifier = Modifier.width(44.dp),
            textAlign = TextAlign.Center
        )
        ComposeImage(
            model = user.avatarUrl, modifier = Modifier
                .size(48.dp)
                .border(2.dp, color, CircleShape)
                .click(onClick = onUserAvatarClick)
                .clip(CircleShape)
        )
        Text(
            text = user.nickname, fontSize = 14.sp, color = Color.White, modifier = Modifier
                .padding(start = 8.dp)
                .weight(1f),
            overflow = TextOverflow.Ellipsis,
            maxLines = 1
        )
        Text(
            text = "$score", fontSize = 14.sp, color = Color.White, modifier = Modifier
                .weight(1f),
            textAlign = TextAlign.End
        )
        Spacer(modifier = Modifier.width(5.dp))
        Image(
            painter = painterResource(id = R.drawable.cpd_ic_support),
            contentDescription = "sp",
            modifier = Modifier.size(18.dp)
        )
        Spacer(modifier = Modifier.width(18.dp))
    }
}

@Preview
@Composable
private fun Preview() {
    PreviewCupidTheme {
        val list = remember {
            buildList {
                repeat(10) {
                    add(RankItem(userForPreview, 10 * (it + 1), it + 1))
                }
            }
        }
        CpdSupportRank(title = "本轮助威榜", desc = "1金币礼物增加1助威值，10银币礼物增加1助威值", data = list)
    }
}