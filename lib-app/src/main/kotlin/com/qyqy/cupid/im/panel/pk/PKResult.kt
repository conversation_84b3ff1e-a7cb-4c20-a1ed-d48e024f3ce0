package com.qyqy.cupid.im.panel.pk

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.qyqy.cupid.ui.live.PKResult
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.utils.OnClick

@Composable
fun PKResultPanel(
    result: PKResult,
    onClick: OnClick = {},
) {
    Box(contentAlignment = Alignment.TopCenter) {
        Image(
            painter = painterResource(id = R.drawable.cpd_ic_victory),
            contentDescription = "result",
            modifier = Modifier
                .size(132.dp, 96.dp)
                .zIndex(1f),
            contentScale = ContentScale.Crop
        )
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 24.dp)
                .background(
                    brush = Brush.verticalGradient(listOf(Color(0xFFFFF7CB), Color(0xFFFFA41C))),
                    shape = RoundedCornerShape(16.dp)
                )
                .padding(top = 74.dp)
                .padding(horizontal = 24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = if (result.win < 0) stringResource(R.string.cpd_victory_msg_blue) else stringResource(R.string.cpd_victory_msg_red),
                color = Color(0xFFAF680E),
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(12.dp))
            Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                UserColumn(
                    user = result.bestContributor,
                    labelImageRes = R.drawable.cpd_ic_label_send,
                    score = result.contributionScore,
                    scoreIconRes = R.drawable.cpd_ic_support
                )

                UserColumn(
                    user = result.pkMvp,
                    labelImageRes = R.drawable.cpd_ic_label_receive,
                    score = result.mvpScore,
                    scoreIconRes = R.drawable.cpd_ic_heart
                )
            }

            Spacer(modifier = Modifier.height(20.dp))
            AppButton(
                text = stringResource(id = R.string.cpd_confirm),
                fontSize = 16.sp,
                color = Color.White,
                modifier = Modifier
                    .widthIn(min = 174.dp)
                    .background(Color(0xFFAF680E), Shapes.chip),
                onClick = onClick
            )
            Spacer(modifier = Modifier.height(20.dp))
        }
    }
}

@Composable
private fun UserColumn(user: User, @DrawableRes labelImageRes: Int, score: Int, @DrawableRes scoreIconRes: Int) {
    Column(modifier = Modifier.width(88.dp), horizontalAlignment = Alignment.CenterHorizontally) {

        Box(
            modifier = Modifier
                .size(88.dp, 98.dp)
        ) {
            ComposeImage(
                model = user.avatarUrl, modifier = Modifier
                    .size(88.dp)
                    .border(2.dp, Color.White, CircleShape)
                    .clip(CircleShape)
            )
            Image(
                painter = painterResource(id = labelImageRes),
                contentDescription = "label",
                modifier = Modifier
                    .size(72.dp, 20.dp)
                    .align(Alignment.BottomCenter)
            )
        }
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = user.nickname,
            color = Color(0xFF834900),
            fontSize = 14.sp,
            modifier = Modifier
                .fillMaxWidth(),
            textAlign = TextAlign.Center,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
        Spacer(modifier = Modifier.height(6.dp))
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(painter = painterResource(id = scoreIconRes), contentDescription = "sc", modifier = Modifier.size(16.dp))
            Text(text = score.toString(), color = Color(0xFF834900), fontSize = 12.sp, modifier = Modifier.padding(start = 5.dp))
        }
    }
}

@Preview
@Composable
private fun PKResultPreview() {
    PKResultPanel(PKResult(1, userForPreview, 999, userForPreview, 888))
}