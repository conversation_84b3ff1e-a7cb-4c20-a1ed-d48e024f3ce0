package com.qyqy.cupid.im.panel.pk

import androidx.compose.runtime.Composable
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.dialog.NormalDialog
import com.qyqy.cupid.ui.live.PKResult
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.utils.OnClick

/**
 * PK结果dialog
 */
class PKResultDialog(
    private val result: PKResult,
) : NormalDialog<IDialogAction>() {
    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        PKResultPanel(result = result) {
            dialog.dismiss()
        }
    }
}