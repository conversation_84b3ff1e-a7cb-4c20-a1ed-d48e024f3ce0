package com.qyqy.cupid.im.panel.pk

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.produceState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.dialog.AnimatedDialog
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.state.EmptyView
import com.qyqy.ucoo.compose.state.ReqState
import com.qyqy.ucoo.compose.state.errorMessage
import com.qyqy.ucoo.compose.state.getDataOrNull
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.im.room.ChatRoomApi
import com.qyqy.ucoo.im.room.support.SupportRankInfo

class PKSupportDialog(val roomId: Int, private val isBlueTeam: Boolean) : AnimatedDialog<IDialogAction>() {
    private val api = createApi<ChatRoomApi>()

    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        val initValue: ReqState = ReqState.Loading
        val nav = LocalAppNavController.current
        val state by produceState(initialValue = initValue) {
            val side = if (isBlueTeam) 1 else 2
            val result = runApiCatching { api.fetchPkContributionBillboard(side, roomId) }
                .map {
                    SupportRankInfo.pareFrom(it)
                }
            value = if (result.isSuccess) {
                ReqState.Success(result.getOrNull())
            } else {
                ReqState.Error(result.exceptionOrNull())
            }
        }

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(420.dp)
                .background(
                    MaterialTheme.colorScheme.onSecondary, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp),
                ),
            contentAlignment = Alignment.Center
        ) {
            when (state) {
                is ReqState.Error -> {
                    Text(text = state.errorMessage, color = Color.White)
                }

                ReqState.Loading -> CircularProgressIndicator(modifier = Modifier.size(48.dp))
                is ReqState.Success -> {
                    val data = state.getDataOrNull<SupportRankInfo>()
                    val list = data?.list.orEmpty()
                    if (list.isNotEmpty()) {

                        CpdSupportRank(
                            title = stringResource(R.string.cpd_title_pk_support),
                            desc = data?.desc.orEmpty(),
                            data = list
                        ) {
                            dialog.dismiss()
                            nav.navigateToProfile(it.id)
                        }
                    } else {
                        EmptyView(modifier = Modifier.fillMaxSize())
                    }
                }
            }
        }
    }
}