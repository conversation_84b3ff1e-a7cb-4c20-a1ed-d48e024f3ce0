package com.qyqy.cupid.model

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.cupid.data.AudioRoomAudienceBean
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getOrNull
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.IMCompatListener
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.im.room.ChatRoomApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement

class AudioRoomDailyViewModel(initialData: JsonObject?) : ViewModel(), IMCompatListener {
    private val _dailyTask = MutableStateFlow<AudioRoomAudienceBean?>(null)
    val dailyTask = _dailyTask.asStateFlow()

    private val chatRoomApi by lazy {
        createApi<ChatRoomApi>()
    }

    init {
        IMCompatCore.addIMListener(this)
        if (initialData != null) {
            viewModelScope.launch(Dispatchers.Default) {
                parseTask(initialData)
            }
        } else {
            refreshAudienceInfo()
        }
    }

    override fun onCleared() {
        super.onCleared()
        IMCompatCore.removeIMListener(this)
    }

    fun refreshAudienceInfo() {
        viewModelScope.launch {
            runApiCatching {
                chatRoomApi.getAudioRoomAudienceInfo()
            }.onSuccess {
                parseTask(it)
            }
        }
    }

    fun parseTask(it: JsonObject) {
        val cpTask = it.getOrNull("cp_task")?.let {
            sAppJson.decodeFromJsonElement<AudioRoomAudienceBean>(it)
        }
        val relativeTask = it.getOrNull("relative_task")?.let {
            sAppJson.decodeFromJsonElement<AudioRoomAudienceBean>(it)
        }

        if (cpTask != null && !cpTask.isFinished) {
            _dailyTask.value = cpTask
        } else if (relativeTask != null && !relativeTask.isFinished) {
            _dailyTask.value = relativeTask
        } else {
            _dailyTask.value = relativeTask ?: cpTask
        }
    }

    override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
        if (message.cmd == MsgEventCmd.AUDIOROOM_AUDIENCE_TASK_STATUS_SYNC) {
            parseTask(message.customJson)
        }
    }

}