package com.qyqy.cupid.model

import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.ViewModel
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.state.Empty
import com.qyqy.ucoo.compose.state.IEmptyOwner
import com.qyqy.ucoo.compose.state.UIState
import com.qyqy.ucoo.compose.state.toUIData
import com.qyqy.ucoo.compose.ui.LoadResult
import com.qyqy.ucoo.compose.ui.PaginateState
import com.qyqy.ucoo.compose.ui.rememberPaginateState
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.user.WalletApi
import java.text.SimpleDateFormat
import java.util.Locale


interface ITypeRecordItem {
    val id: Any
    val titleText: String
    val subTitleText: String
    val change: String
    val typeIcon: Any
}

data class TypeRecordItem(
    override val id: Int,
    override val titleText: String,
    override val subTitleText: String,
    override val change: String,
    override val typeIcon: Int,
) : ITypeRecordItem

data class TypeRecordList(
    val list: List<ITypeRecordItem>,
) : IEmptyOwner {

    override val empty: Empty?
        @Composable
        get() = if (list.isEmpty()) {
            Empty(stringResource(id = R.string.cpd什么都没有), R.drawable.cupid_empty)
        } else {
            null
        }
}


/**
 *  @time 2024/7/31
 *  <AUTHOR>
 *  @package com.qyqy.cupid.model
 */
class CoinHistoryViewModel(private val type: Int) : ViewModel() {

    private val api by lazy(LazyThreadSafetyMode.NONE) {
        createApi(WalletApi::class.java)
    }

    private var _uiState by mutableStateOf<UIState<TypeRecordList>>(UIState.Loading(""))

    private lateinit var _paginateState: PaginateState<Int>

    val uiState: UIState<TypeRecordList>
        get() = _uiState

    val isRefreshing by derivedStateOf {
        _uiState.isLoading
    }

    @Composable
    fun attachLazyWidget(listState: LazyListState): PaginateState<Int> {
        val paginateState = rememberPaginateState<Int>(nextEnable = false)

        _paginateState = paginateState

        if (!paginateState.nextEnable) {
            LaunchedEffect(Unit) {
                paginateState.loadTriggerOnce(true, null, true) {
                    getLoadResult(true, it.key)
                }
                paginateState.nextEnable = true
            }
        }

        paginateState.LaunchAttach(listState = listState) { pagingScope ->
            val key = pagingScope.key
            getLoadResult(false, key)
        }
        return paginateState
    }

    fun refresh() {
        if (!::_paginateState.isInitialized) {
            return
        }
        _paginateState.nextEnable = false
    }

    fun retry() {
        if (!::_paginateState.isInitialized) {
            return
        }
        if (uiState !is UIState.Data) {
            _uiState = UIState.Loading("")
            _paginateState.retryNext()
        }
    }

    private suspend fun getLoadResult(refresh: Boolean, key: Int?): LoadResult<Int> {
        return getTypeHistory(type, key).fold(
            {
                val data = uiState
                _uiState = if (!refresh && data is UIState.Data) {
                    data.value.copy(list = buildList {
                        addAll(data.value.list)
                        addAll(it.list)
                    })
                } else {
                    it
                }.toUIData()
                LoadResult.Page(if (it.list.isEmpty()) null else it.list.last().id as? Int)
            }
        ) {
            if (uiState !is UIState.Data) {
                _uiState = UIState.Error(Empty(R.string.cpd加载失败点击重试, R.drawable.cupid_empty), throwable = it)
            }
            LoadResult.Error(it)
        }
    }

    private suspend fun getTypeHistory(type: Int, key: Int?): Result<TypeRecordList> {
        return runApiCatching {
            api.getBillListV2(type, key)
        }.map {
            TypeRecordList(
                it.records.map { item ->
                    TypeRecordItem(
                        item.id,
                        item.changeReason,
                        SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(item.createTimestamp.times(1000L)),
                        item.changeAmount.toString(),
                        if (type == 21) {
                            R.drawable.ic_cpd_coin
                        } else {
                            R.drawable.ic_cpd_diamond
                        }
                    )
                }
            )
        }
    }
}