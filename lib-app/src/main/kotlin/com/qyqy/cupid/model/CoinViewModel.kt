package com.qyqy.cupid.model

import androidx.compose.runtime.derivedStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.cupid.apis.JapanCoinApi
import com.qyqy.cupid.apis.LineHelp
import com.qyqy.cupid.data.UserCoinConfig
import com.qyqy.cupid.utils.CupidTaskManager
import com.qyqy.ucoo.accountManager
import com.qyqy.ucoo.compose.presentation.sign_tasks.TaskSeries
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

/**
 *  @time 2024/7/27
 *  <AUTHOR>
 *  @package com.qyqy.cupid.model
 *  首页 - 金币页面viewModel
 */
class CoinViewModel : ViewModel() {
    private val coinApi by lazy {
        createApi(JapanCoinApi::class.java)
    }

    private val _coinConfig = MutableStateFlow(UserCoinConfig())
    val mCoinConfig = _coinConfig.asStateFlow()

    private val _refreshFlow = MutableStateFlow(false)
    val refreshFlow = _refreshFlow.asStateFlow()

    val taskManager: CupidTaskManager = CupidTaskManager(true, 5)

    val allSeriesTasks = taskManager.activityTaskList

    init {
        refresh(true)
    }

    fun refresh(isInit: Boolean = false) {
        taskManager.refreshTasks(viewModelScope)
        refreshCoinConfig()
//        if(!isInit){
        accountManager.refreshSelfUserByRemote()
//        }
    }

    private fun refreshCoinConfig() {
        viewModelScope.launch {
            _refreshFlow.value = true
            runApiCatching {
                coinApi.getCoinConfig()
            }.onSuccess {
                _refreshFlow.value = false
                _coinConfig.value = it
            }.onFailure {
                _refreshFlow.value = false
                it.printStackTrace()
            }
        }
    }

    suspend fun getLineHelp(): LineHelp? {
        return runApiCatching { coinApi.getLineHelp() }.getOrNull()
    }

}