package com.qyqy.cupid.model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.cupid.apis.CoinWinnerApi
import com.qyqy.cupid.data.CoinWinnerConfigBean
import com.qyqy.cupid.data.CoinWinnerDetail
import com.qyqy.cupid.data.CoinWinnerState
import com.qyqy.cupid.utils.ImageCache
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getStringOrNull
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.IMCompatListener
import com.qyqy.ucoo.im.compat.MsgFilter
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.utils.logI
import com.qyqy.ucoo.utils.toast
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class CoinWinnerViewModel(val room_id: Int) : ViewModel() {
    private val api by lazy {
        createApi<CoinWinnerApi>()
    }

    private val _gameConfig = MutableStateFlow<CoinWinnerConfigBean?>(null)
    val gameConfig = _gameConfig.asStateFlow()

    private val _gameDetail = MutableStateFlow<CoinWinnerDetail?>(null)
    val gameDetail = _gameDetail.asStateFlow()

    var isGameShowing by mutableStateOf(false)

    init {
        refreshCurrentGameDetail(true)
        viewModelScope.launch {
            while (true) {
                delay(10_000)
                _gameDetail.value?.let {
                    if (System.currentTimeMillis() - it.ts > 10_000 && it.round.getGameStatus() != CoinWinnerState.Finished) {
                        refreshCurrentGameDetail()
                    }
                }
            }
        }
    }

    private fun refreshCurrentGameDetail(isInit: Boolean = false) {
        viewModelScope.launch {
            runApiCatching {
                api.getGameInfo(room_id)
            }.onSuccess {
                if (isInit) {
                    if (it.round.status <= 2) {
                        isGameShowing = true
                    } else {
                        isGameShowing = it.players.find { it.player.userid == sUser.userId } != null
                    }
                }
                updateGameDetail(if (it.round.id == -1) null else it)

            }
        }
    }

    /**
     * 从消息体里点击参加
     *
     * 吐了啊
     *
     * @param roundID 游戏轮次id
     *
     * 是本轮游戏就直接判断
     * 不是本轮游戏就先请求接口再判断
     */
    fun clickJoinFromMessage(roundID: Int) {
        val currentRound = _gameDetail.value?.round
        if (currentRound?.id == roundID) {
            when (currentRound.status) {
                CoinWinnerState.Finished.status -> {
                    toastRes(R.string.cpd_金币游戏已结束)
                }

                CoinWinnerState.Abort.status -> {
                    toastRes(R.string.cpd_金币游戏关闭金币返还)
                }

                else -> {
                    isGameShowing = true
                }
            }
        } else {
            viewModelScope.launch {
                runApiCatching {
                    api.getGameInfo(null, roundID)
                }.onSuccess {
                    when (it.round.status) {
                        CoinWinnerState.Finished.status -> {
                            toastRes(R.string.cpd_金币游戏已结束)
                        }

                        CoinWinnerState.Abort.status -> {
                            toastRes(R.string.cpd_金币游戏关闭金币返还)
                        }

                        else -> {
                        }
                    }
                }
            }
        }
    }

    //刷新游戏配置
    fun refreshGameConfig(force: Boolean = false) {
        if (!force && _gameConfig.value != null) {
            return
        }
        _gameConfig.value = null
        viewModelScope.launch {
            runApiCatching {
                api.getCoinWinnerConfig(room_id)
            }.onSuccess {
                _gameConfig.value = it
            }.onFailure {
                _gameConfig.value = CoinWinnerConfigBean(listOf(), playerCountLimit = -1)
            }.toastError()
        }
    }

    /**
     * 创建游戏(房主)
     *
     * @param base_coin 最低金额
     */
    fun createGame(base_coin: Int) {
        viewModelScope.launch {
            runApiCatching {
                api.createGame(
                    mapOf(
                        "room_id" to room_id,
                        "base_coin" to base_coin
                    )
                )
            }.onSuccess {
                updateGameDetail(it)
                isGameShowing = true
            }.toastError()
        }
    }

    /**
     * 关闭游戏(房主)
     *
     */
    fun closeGame() {
        val bean = _gameDetail.value ?: return
        viewModelScope.launch {
            runApiCatching {
                api.closeGame(
                    mapOf(
                        "round_id" to bean.round.id
                    )
                )
            }.onSuccess {
                it.getStringOrNull("toast")?.also { toast(it) }
                _gameConfig.value = null
                updateGameDetail(null)
            }.toastError()
        }
    }

    /**
     * 开始游戏(房主)
     *
     */
    fun startGame() {
        val bean = _gameDetail.value ?: return
        viewModelScope.launch {
            runApiCatching {
                api.startGame(
                    mapOf(
                        "round_id" to bean.round.id
                    )
                )
            }.toastError()
        }
    }

    /**
     * 加入游戏
     *
     */
    fun joinGame() {
        val bean = _gameDetail.value ?: return
        viewModelScope.launch {
            runApiCatching {
                api.joinGame(
                    mapOf(
                        "round_id" to bean.round.id
                    )
                )
            }.onSuccess {
                updateGameDetail(it)
            }.toastError()
        }
    }

    /**
     * 加注
     *
     * @param coin 加注金额
     */
    fun raiseGame(coin: Int) {
        val bean = _gameDetail.value ?: return
        viewModelScope.launch {
            runApiCatching {
                api.raiseGame(
                    mapOf(
                        "round_id" to bean.round.id,
                        "coin" to coin
                    )
                )
            }.onSuccess {
//                updateGameDetail(it)
            }.toastError()
        }
    }

    /**
     * 关闭游戏页面
     *
     */
    fun closeWindow() {
        _gameDetail.value = null
    }

    private fun updateGameDetail(newValue: CoinWinnerDetail?) {
        newValue?.copyOther(_gameDetail.value)
        _gameDetail.value = newValue
    }

    private fun onReceiveMessage(msg: UCCustomMessage) {
        logI("receive new Message:%s", msg)
        when (msg.cmd) {
            MsgEventCmd.COIN_PK_CREATE -> {
                val data = msg.parseDataJson<CoinWinnerDetail>()
                updateGameDetail(data)
            }

            MsgEventCmd.COIN_PK_JOIN -> {
                val data = msg.parseDataJson<CoinWinnerDetail>()
                updateGameDetail(data)
            }

            MsgEventCmd.COIN_PK_CLOSE -> {
                updateGameDetail(null)
            }
            //状态变化
            MsgEventCmd.COIN_PK_STAGE_CHANGE -> {
                val data = msg.parseDataJson<CoinWinnerDetail>()
                logI("状态变化至%s", data?.round?.getGameStatus())
                updateGameDetail(data)
            }
            //被淘汰
            MsgEventCmd.COIN_PK_LOSE -> {
                val data = msg.parseDataJson<CoinWinnerDetail>()
                updateGameDetail(data)
            }

            MsgEventCmd.COIN_PK_WIN -> {
                val data = msg.parseDataJson<CoinWinnerDetail>()
                updateGameDetail(data)
            }

            MsgEventCmd.COIN_PK_RAISE -> {
                val data = msg.parseDataJson<CoinWinnerDetail>()
                updateGameDetail(data)
            }
        }

    }

    private val msgListener by lazy {
        object : IMCompatListener {

            override val filter: MsgFilter = MsgFilter(room_id.toString())

            override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
                onReceiveMessage(message)
            }
        }
    }

    init {
        IMCompatCore.addIMListener(msgListener)
    }

    override fun onCleared() {
        super.onCleared()
        IMCompatCore.removeIMListener(msgListener)
        ImageCache.clear()
    }
}
