package com.qyqy.cupid.model

import androidx.lifecycle.viewModelScope
import com.qyqy.cupid.apis.FamilyApi
import com.qyqy.cupid.utils.BroadcastEvent
import com.qyqy.cupid.utils.CupidBroadcast
import com.qyqy.cupid.utils.CupidEventName
import com.qyqy.cupid.utils.CupidFamilyManager
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.PageDataViewModel
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.state.StateViewModelWithIntPage
import com.qyqy.ucoo.compose.state.LiState
import com.qyqy.ucoo.compose.state.LiStateInfo
import com.qyqy.ucoo.core.page.PageLoader
import com.qyqy.ucoo.http.ApiException
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.tribe.bean.Tribe
import com.qyqy.ucoo.tribe.bean.TribeMemberItem
import com.qyqy.ucoo.utils.LogUtil
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject

/**
 * 部落大厅
 */
class FamilySquarePageViewModel(private val stateInfo: LiStateInfo = LiStateInfo()) : PageDataViewModel<Tribe>(),
    LiState by stateInfo {

    private val api by lazy {
        createApi(FamilyApi::class.java)
    }

    override suspend fun getPageData(
        currentPage: Int,
        pageSize: Int,
        pageLoader: PageLoader<Tribe>,
    ): List<Tribe> {
        val firstPage = pageLoader.isFirstPage(currentPage)
        if (firstPage) {
            stateInfo.setRefresh(true)
            stateInfo.setHasMore(true)
        } else {
            stateInfo.setLoading(true)
        }
        val lastId = if (firstPage) {
            0
        } else {
            getDataList().lastOrNull()?.id ?: 0
        }

        val result = runApiCatching {
            api.getTribeSquare(lastId)
        }.getOrNull()

        val myTribe = result?.myTribe
        if (myTribe == null) {
            CupidFamilyManager.updateMyFamily(null)
        } else {
            //此处myTribe服务端 没有返回rc_group_id，容易出错
            CupidFamilyManager.updateMyFamily(myTribe.copy(relationWithMe = 10))
        }

        return (result?.tribes ?: emptyList()).also {
            stateInfo.setLoaded(true)
            stateInfo.setHasMore(it.isNotEmpty())
            stateInfo.setRefresh(false)
            stateInfo.setLoading(false)
            LogUtil.d("tribeSize:${it.size}", "retrofit")
        }
    }

}

class FamilyPageViewModel(val tribeId: String) : StateViewModelWithIntPage<TribeMemberItem>() {
    private val api by lazy {
        createApi(FamilyApi::class.java)
    }
    private var lastId = 0

    init {
        refresh()
        viewModelScope.launch {
            CupidBroadcast.flow
                .distinctUntilChanged { old, new ->
                    return@distinctUntilChanged old.eventName == new.eventName && old.value == new.value
                }
                .collectLatest {
                    when (it.eventName) {
                        CupidEventName.UPDATE_MEMBER_INFO -> {
                            (it.value as? TribeMemberItem)?.let { member ->
                                updateItem({ member.member_id == it.member_id }) {
                                    it.copy(role = member.role)
                                }
                            }
                        }

                        CupidEventName.KICK_MEMBER -> {
                            val memberid = it.value as Int
                            removeItems {
                                it.member_id == memberid
                            }
                        }

                        CupidEventName.MEMBER_KICKED,
                        CupidEventName.MEMBER_QUITED -> {
                            val userid = it.value as? Int
                            removeItems {
                                it.user.userid == userid
                            }
                        }

                        else -> {}
                    }
                }
        }
    }

    override suspend fun loadData(pageNum: Int): Result<List<TribeMemberItem>> {
        val id = if (pageNum == firstPage) 0 else lastId
        val onlineStatus = if (pageNum == firstPage) true else listFlow.value.lastOrNull()?.is_online ?: true

        val result = runApiCatching { api.getMemberList(tribeId, id.toString(), onlineStatus) }
        val adminsResult = if (firstPage == pageNum) {
            val firstData = runApiCatching {
                api.tribeDetail(tribeId)
            }.getOrNull()
            firstData?.firstPageMembers?.filter { it.isAdmin || it.isOwner } ?: emptyList()
        } else {
            emptyList()
        }

        return if (result.isSuccess || adminsResult.isNotEmpty()) {
            val finalList = mutableListOf<TribeMemberItem>()
            finalList.addAll(adminsResult)
            val memberItems = result.getOrNull()?.members ?: emptyList()
            lastId = memberItems.lastOrNull()?.member_id ?: lastId
            finalList.addAll(memberItems)
            Result.success(finalList)
        } else {
            Result.failure<List<TribeMemberItem>>(result.exceptionOrNull() ?: ApiException(msg = "error")).also {
                toast(it.exceptionOrNull()?.message ?: "")
            }
        }
    }

}

/**
 * 获取成员列表
 */
class FamilyMemberListViewModel(
    val tribeId: String,
    roles: List<Int>,
    lazyLoad: Boolean = true,
    registerCallback: Boolean = false
) : StateViewModelWithIntPage<TribeMemberItem>() {

    private val paramsRoles = roles.joinToString(",")

    private val api by lazy {
        createApi(FamilyApi::class.java)
    }

    init {
        if (!lazyLoad) {
            refresh()
        }
        if (registerCallback) {
            viewModelScope.launch {
                CupidBroadcast.flow
                    .distinctUntilChanged { old, new ->
                        return@distinctUntilChanged old.eventName == new.eventName && old.value == new.value
                    }
                    .collectLatest {
                        when (it.eventName) {
                            CupidEventName.UPDATE_MEMBER_INFO -> {
                                (it.value as? TribeMemberItem)?.let { member ->
                                    updateItem({ member.member_id == it.member_id }) {
                                        it.copy(role = member.role)
                                    }
                                }
                            }

                            CupidEventName.KICK_MEMBER -> {
                                val memberid = it.value as Int
                                removeItems {
                                    it.member_id == memberid
                                }
                            }

                            CupidEventName.MEMBER_KICKED,
                            CupidEventName.MEMBER_QUITED -> {
                                val userid = it.value as? Int
                                removeItems {
                                    it.user.userid == userid
                                }
                            }

                            else -> {}
                        }
                    }
            }
        }
    }

    override suspend fun loadData(pageNum: Int): Result<List<TribeMemberItem>> {
        val id = if (pageNum == firstPage) 0 else listFlow.value.lastOrNull()?.member_id ?: 0

        val result = runApiCatching { api.getMemberListWithRoles(tribeId, paramsRoles, id.toString()) }

        return if (result.isSuccess) {
            Result.success(result.getOrNull()?.members ?: emptyList())
        } else {
            Result.failure<List<TribeMemberItem>>(result.exceptionOrNull() ?: ApiException(msg = "error")).also {
                toast(it.exceptionOrNull()?.message ?: "")
            }
        }
    }

    suspend fun kickMember(memberId: Int): Result<JsonObject> {
        return runApiCatching {
            api.kickMember(mapOf("tribe_id" to tribeId, "member_id" to memberId))
        }.onSuccess {
            toastRes(R.string.cpd_kickout_succeed)
            remove(memberId)
            CupidBroadcast.sendEvent(BroadcastEvent(CupidEventName.KICK_MEMBER, memberId))
            CupidFamilyManager.updateFamily {
                it.copy(memberCnt = (it.memberCnt ?: 1) - 1)
            }
        }.toastError()
    }

    private fun remove(memberId: Int) {
        removeItems {
            it.member_id == memberId
        }
    }

    suspend fun setAdminMember(memberId: Int, isAdmin: Boolean): Result<JsonObject> {
        return runApiCatching {
            api.setCancelDepLeader(
                mapOf(
                    "tribe_id" to tribeId,
                    "member_id" to memberId,
                    "is_admin" to isAdmin.toString()
                )
            )
        }.onSuccess {
            if (isAdmin) {
                toastRes(R.string.cpd_setadmin_succeed)
            } else {
                toastRes(R.string.cpd_canceladmin_succeed)
            }
            updateItem({ it.member_id == memberId }) {
                val item = it.copy(role = if (isAdmin) TribeMemberItem.ROLE_ADMIN else TribeMemberItem.ROLE_COMMON)
                CupidBroadcast.sendEvent(BroadcastEvent(CupidEventName.UPDATE_MEMBER_INFO, item))
                item
            }
        }.toastError()
    }
}


/**
 * 加入部落申请
 */
class FamilyJoinApplyViewModel(
    val tribeId: String,
) : StateViewModelWithIntPage<TribeMemberItem>() {

    private val api by lazy {
        createApi(FamilyApi::class.java)
    }

    override suspend fun loadData(pageNum: Int): Result<List<TribeMemberItem>> {
        val id = if (pageNum == firstPage) 0 else listFlow.value.lastOrNull()?.apply_id ?: 0

        val result = runApiCatching { api.getApplyList(tribeId, id.toString()) }

        return if (result.isSuccess) {
            Result.success(result.getOrNull()?.applies ?: emptyList())
        } else {
            Result.failure<List<TribeMemberItem>>(result.exceptionOrNull() ?: ApiException(msg = "error")).also {
                toast(it.exceptionOrNull()?.message ?: "")
            }
        }
    }

    fun applyJoin(userid: Int, agreed: Boolean) {
        viewModelScope.launch {
            runApiCatching {
                api.agreeMemberApply(
                    mapOf(
                        "tribe_id" to tribeId,
                        "userid" to userid,
                        "agreed" to agreed.toString()
                    )
                )
            }.onSuccess {
                updateItem({ it.user.userid == userid }) {
                    it.done(agreed)
                }
                CupidFamilyManager.updateFamily {
                    it.copy(memberCnt = (it.memberCnt ?: 1) + (if (agreed) 1 else (-1)), member_apply_wait_cnt = (it.member_apply_wait_cnt ?: 1) - 1)
                }
            }
        }
    }

    /**
     * 解散家族
     *
     */
    suspend fun disbandFamily(): Result<JsonObject>? {
        val bean = CupidFamilyManager.myTribe ?: return null
        if (bean.isOwner) {
            return runApiCatching {
                api.destroyTribe(mapOf("tribe_id" to bean.id))
            }
        } else {
            return null
        }
    }
}
