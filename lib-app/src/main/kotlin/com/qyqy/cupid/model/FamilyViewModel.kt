package com.qyqy.cupid.model

import androidx.lifecycle.viewModelScope
import com.qyqy.cupid.apis.FamilyApi
import com.qyqy.cupid.data.FamilyConfig
import com.qyqy.cupid.utils.CupidFamilyManager
import com.qyqy.ucoo.AppUserPartition

import com.qyqy.ucoo.R
import com.qyqy.ucoo.app
import com.qyqy.ucoo.base.BaseViewModel
import com.qyqy.ucoo.base.IdleState
import com.qyqy.ucoo.base.LoadingState
import com.qyqy.ucoo.base.UiState
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.core.oss.Uploader
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.im.compat.ConversationType
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.MessageBundle
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.mine.MediaInfo
import com.qyqy.ucoo.mine.url
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.tribe.TribeManageEffect
import com.qyqy.ucoo.tribe.TribeManagerEvent
import com.qyqy.ucoo.tribe.bean.ResultTribeCreate
import com.qyqy.ucoo.tribe.bean.TribeInfo
import com.qyqy.ucoo.user.UserRepository
import com.qyqy.ucoo.utils.UCOOTribeManager
import io.github.album.MediaData
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

/**
 *  @time 8/19/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.model
 */
class FamilyViewModel(val familyId: String = "") : BaseViewModel<TribeManagerEvent, UiState, TribeManageEffect>() {
    private val _familyBean = MutableStateFlow(TribeInfo(iAmOwner = false, iAmAdmin = false))
    val familyFlow = combine(_familyBean, CupidFamilyManager.tribeInfoFlow) { cur, mine ->
        if (mine != null && familyId == mine.id.toString()) {
            mine
        } else {
            cur
        }
    }.stateIn(viewModelScope, SharingStarted.Eagerly, TribeInfo(iAmOwner = false, iAmAdmin = false))

    private val _familyCreatorConfig = MutableStateFlow(FamilyConfig(false, 0))
    val familyCreatorConfig = _familyCreatorConfig.asStateFlow()

    //是否加载中
    private val _isLoading = MutableStateFlow(false)
    val isLoading = _isLoading.asStateFlow()
    private val userRepository = UserRepository()

    private val api: FamilyApi

    init {
        api = createApi(FamilyApi::class.java)
        if (familyId.isNotBlank()) {
            viewModelScope.launch {
                CupidFamilyManager.tribeFlow.collectLatest {
                    if (it == null) {
                        refresh()
                    }
                }
            }
        }
    }

    //region 需要家族id的方法

    /**
     * 刷新当前选择的家族
     *
     */
    fun refresh() {
        if (familyId.isBlank()) {
            return
        }
        viewModelScope.launch {
            runApiCatching {
                api.tribeDetail(familyId)
            }.onSuccess {
                if (it.isMyTribe) {
                    CupidFamilyManager.updateFamilyInfo(it)
                }
                _familyBean.value = it
            }.toastError()
        }
    }

    /**
     * 加入家族
     */
    fun joinFamily() {
        val bean = familyFlow.value
        if (!bean.isMyTribe) {
            viewModelScope.launch {
                _isLoading.emit(true)

                runApiCatching {
                    api.joinTribe(mapOf("tribe_id" to bean.id))
                }.onSuccess {
                    refresh()
                }.toastError()

                _isLoading.emit(false)
            }
        }
    }

    /**
     * 更新群组头像
     */
    fun updateFamilyAvatar(imgs: List<MediaData>) {
        viewModelScope.launch {
            _isLoading.emit(true)
            val list = Uploader.uploadImages(imgs, "avatar")
            val mediaInfo = list?.firstOrNull()
            if (mediaInfo != null) {
                val newBean = familyFlow.value.copy(avatarUrl = mediaInfo.url)
                runApiCatching {
                    api.updateTribeDetail(
                        mapOf(
                            "tribe_id" to familyId,
                            "avatar_url" to mediaInfo.url
                        )
                    )
                }.onSuccess {
                    CupidFamilyManager.updateFamily {
                        it.copy(avatarUrl = newBean.avatarUrl)
                    }
                }.toastError()
            } else {
                if (AppUserPartition.isUCOO) {
                    toastRes(R.string.upload_image_failed)
                } else {
                    toastRes(R.string.cpd_upload_image_failed)
                }
            }
            _isLoading.emit(false)
        }
    }

    /**
     * 更新家族信息
     *
     * @param familyName 家族名称
     * @param familyIntro 家族介绍
     * @param dontDisturb 是否开启勿扰
     */
    fun updateFamilyInfo(
        familyName: String? = null,
        familyIntro: String? = null,
        dontDisturb: Boolean? = null,
        callback: () -> Unit = {}
    ) {
        viewModelScope.launch {
            _isLoading.emit(true)
            var newBean = familyFlow.value
            runApiCatching {
                api.updateTribeDetail(
                    buildMap {
                        put("tribe_id", familyId)
                        if (familyName != null) {
                            put("name", familyName)
                            newBean = newBean.copy(name = familyName)
                        }
                        if (familyIntro != null) {
                            put("bulletin", familyIntro)
                            newBean = newBean.copy(bulletin = familyIntro)
                        }
                        if (dontDisturb != null) {
                            put("enable_dont_disturb", dontDisturb.toString())
                            newBean = newBean.copy(dontDisturbEnable = dontDisturb)
                        }
                    }
                )
            }.onSuccess {
                CupidFamilyManager.updateFamilyInfo(newBean)
                callback()
                if (dontDisturb != null) {
                    onDisturbStatusUpdated(dontDisturb)
                }
            }.toastError()
            _isLoading.emit(false)
        }
    }

    /**
     * 解散家族
     *
     */
    fun disbandFamily(callback: () -> Unit) {
        val bean = familyFlow.value
        if (bean.iAmOwner) {
            viewModelScope.launch {
                _isLoading.value = true
                runApiCatching {
                    api.destroyTribe(mapOf("tribe_id" to bean.id))
                }.onSuccess {
                    callback()
                    _isLoading.value = false
                }.onFailure {
                    _isLoading.value = false
                }.toastError()

            }
        }
    }

    /**
     * 退出家族
     *
     */
    fun exitFamily(callback: () -> Unit) {
        val bean = familyFlow.value ?: return
        if (bean.isMyTribe && !bean.iAmOwner) {
            viewModelScope.launch {
                _isLoading.value = true

                runApiCatching {
                    api.quitTribe(mapOf("tribe_id" to bean.id))
                }.onSuccess {
                    CupidFamilyManager.exitFamily()
//                    CupidFamilyManager.onReceiveCustomMessage(
//                        MsgEventCmd.MEMBER_QUIT,
//                        "{\"user\":${
//                            sAppJson.encodeToJsonElement<TribeUser>(
//                                TribeUser(
//                                    userid = sUser.userId,
//                                    avatarUrl = sUser.avatarUrl,
//                                    age = sUser.age,
//                                    nickname = sUser.nickname,
//                                    publicId = sUser.publicId,
//                                    gender = sUser.gender,
//                                    isMember = false
//                                )
//                            ).toString()
//                        }}"
//                    )
                    callback()
                    _isLoading.value = false
                }.onFailure {
                    _isLoading.value = false
                }.toastError()

            }
        }
    }

    /**
     * 升级融云的免打扰
     *
     * @param enableDontDisturb 是否开启免打扰
     */
    private fun onDisturbStatusUpdated(enableDontDisturb: Boolean) {
        val targetId = familyFlow.value.conversationId ?: return
        IMCompatCore.setConversationReceiveMessageOpt(targetId, ConversationType.GROUP, enableDontDisturb)
    }

    //endregion

    /**
     * 创建家族, 通过FamilyCreator调用
     *
     * @param event
     */
    private fun createTribe(event: TribeManagerEvent.Create) {
        viewModelScope.launch {
            setState { LoadingState.Loading }
            val list = userRepository.uploadMultiImage(listOf(event.avatar))
            val info: MediaInfo? = list?.firstOrNull()
            info?.let {
                runApiCatching {
                    api.createTribe(
                        mapOf(
                            "name" to event.tribeName,
                            "avatar_url" to it.url
                        )
                    )
                }
                    .onSuccess { ret: ResultTribeCreate ->
                        val result =
                            runApiCatching { api.tribeDetail(ret.tribeId.toString()) }
                        val tribeInfo: TribeInfo? = result.getOrNull()
                        tribeInfo?.let {
                            UCOOTribeManager.updateTribeInfo(it)
                            setEffect { TribeManageEffect.Toast(app.getString(R.string.create_success)) }

                            val bundle = MessageBundle.Custom.create(
                                cmd = MsgEventCmd.TRIBE_CUSTOM_CONTENT,
                                data = buildJsonObject {
                                    put("value", it.publicId.orEmpty())
                                    put("name", it.name.orEmpty())
                                },
                                summary = app.getString(R.string.cpd_family_created_title)
                            )

                            val conversationId = it.conversationId.orEmpty()
                            IMCompatCore.insertLocalMessage(conversationId, ConversationType.GROUP, bundle)

                            setEffect { TribeManageEffect.CreateFinished(it) }
                            setState { LoadingState.Idle }
                        } ?: run {
                            setEffect { TribeManageEffect.Toast(app.getString(R.string.create_failed)) }
                        }
                    }.onFailure {
                        setEffect { TribeManageEffect.CreateFailed }
                        setState { LoadingState.Idle }
                    }
                    .toastError()
            } ?: run {
                setEffect {
                    TribeManageEffect.Toast(if (AppUserPartition.isUCOO) app.getString(R.string.upload_image_failed) else app.getString(R.string.cpd_upload_image_failed))
                }
                setState { LoadingState.Idle }
            }
        }
    }

    override fun createInitialState(): UiState = IdleState

    override fun handleEvent(event: TribeManagerEvent) {
        when (event) {
            is TribeManagerEvent.Create -> createTribe(event)
            is TribeManagerEvent.Destroy -> {}
            is TribeManagerEvent.Update -> {}
        }
    }

    fun refreshConfig() {
        viewModelScope.launch {
            runApiCatching {
                api.getTribeConfig()
            }.onSuccess {
                _familyCreatorConfig.value = it
            }.onFailure {

            }.toastError()
        }
    }
}