package com.qyqy.cupid.model

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.bean.FeedbackQABean
import com.qyqy.ucoo.http.getOrNull
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.user.UserRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.json.decodeFromJsonElement

class FeedbackViewModel : ViewModel() {
    val userRepository by lazy {
        UserRepository()
    }

    private val _feedbackqa = MutableStateFlow(listOf<FeedbackQABean>())
    val feedbackqaList = _feedbackqa.asStateFlow()

    suspend fun updateFeedback(content: String) = userRepository.postSuggest(content)

    fun getFeedbackQA() {
        viewModelScope.launch {
//            _feedbackqa.emit(userRepository.getFeedbackQA())
            userRepository.getQAList().onSuccess {
                it.getOrNull("qa_list")?.let {
                    _feedbackqa.emit(sAppJson.decodeFromJsonElement<List<FeedbackQABean>>(it))
                }
            }.onFailure {
                // do nothing
            }
        }
    }
}