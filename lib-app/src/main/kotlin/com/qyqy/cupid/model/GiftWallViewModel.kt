package com.qyqy.cupid.model

import com.qyqy.ucoo.AppUserPartition
import com.qyqy.ucoo.UserPartition
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.R
import com.qyqy.ucoo.app
import com.qyqy.ucoo.compose.data.BottomRadiusItem
import com.qyqy.ucoo.compose.data.CategoryGiftWall
import com.qyqy.ucoo.compose.data.CategoryTitleItem
import com.qyqy.ucoo.compose.data.GiftItem
import com.qyqy.ucoo.compose.data.SpaceItem
import com.qyqy.ucoo.compose.data.SpanItem
import com.qyqy.ucoo.compose.data.TopRadiusItem
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.bean.BlindboxGift
import com.qyqy.ucoo.im.bean.MyGift
import com.qyqy.ucoo.user.UserRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonArray

/**
 *  @time 9/4/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.model
 */
class GiftWallViewModel(val userId: String) : ViewModel() {
    private val userRepository: UserRepository = UserRepository()

    private val _giftWallData = MutableStateFlow(listOf<CategoryGiftWall>())
    val giftWallData = _giftWallData.asStateFlow()

    fun refresh() {
        viewModelScope.launch {
            val result = userRepository.getUserGiftWall(userId).mapCatching {
                getGiftList(it)
            }.getOrNull() ?: listOf()
            _giftWallData.emit(result)
        }
    }

    private fun getGiftList(jsonObject: JsonObject): List<CategoryGiftWall> {
        val blindboxGiftList = jsonObject["blindbox_gifts"]?.jsonArray?.let {
            sAppJson.decodeFromJsonElement<List<BlindboxGift>>(it)
        }.orEmpty()

        val normalBlindboxGiftList =
            jsonObject["normal_blindbox_gifts"]?.jsonArray?.let {
                sAppJson.decodeFromJsonElement<List<BlindboxGift>>(it)
            }.orEmpty()
        val commonGiftList = jsonObject["gift_wall"]?.jsonArray?.let {
            sAppJson.decodeFromJsonElement<List<MyGift>>(it)
        }.orEmpty()

        return buildList {
            add(
                CategoryGiftWall(if (AppUserPartition.isUCOO) {
                    app.getString(R.string.盲盒礼物)
                } else {
                    app.getString(R.string.cpd盲盒礼物)
                }, buildList {
                    blindboxGiftList.forEach {
                        add(TopRadiusItem)
                        add(CategoryTitleItem(it.seriesName))
                        it.gifts.forEach { gift ->
                            add(GiftItem(gift.gift, gift.count))
                        }
                        val lastLineCount = it.gifts.size.rem(4)
                        if (lastLineCount != 0) {
                            val leftSpan = 4.minus(lastLineCount)
                            add(size.minus(lastLineCount), SpanItem(leftSpan))
                            add(SpanItem(leftSpan))
                        }
                        add(BottomRadiusItem)
                        add(SpaceItem(16))
                    }

                    normalBlindboxGiftList.forEach {
                        add(TopRadiusItem)
                        add(CategoryTitleItem(it.seriesName))
                        it.gifts.forEach { gift ->
                            add(GiftItem(gift.gift, gift.count))
                        }
                        val lastLineCount = it.gifts.size.rem(4)
                        if (lastLineCount != 0) {
                            val leftSpan = 4.minus(lastLineCount)
                            add(size.minus(lastLineCount), SpanItem(leftSpan))
                            add(SpanItem(leftSpan))
                        }
                        add(BottomRadiusItem)
                        add(SpaceItem(16))
                    }
                })
            )

            val text = if (AppUserPartition.isUCOO) {
                app.getString(R.string.common_gift)
            } else {
                app.getString(R.string.cpd_common_gift)
            }

            add(CategoryGiftWall(text, buildList {
                add(TopRadiusItem)
                add(CategoryTitleItem(text))
                commonGiftList.forEach { gift ->
                    add(GiftItem(gift.gift, gift.count))
                }
                val lastLineCount = commonGiftList.size.rem(4)
                if (lastLineCount != 0) {
                    val leftSpan = 4.minus(lastLineCount)
                    add(size.minus(lastLineCount), SpanItem(leftSpan))
                    add(SpanItem(leftSpan))
                }
                add(BottomRadiusItem)
            }))
        }
    }
}