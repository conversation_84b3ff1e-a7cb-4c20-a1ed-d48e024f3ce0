package com.qyqy.cupid.model

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.cupid.apis.JapanCoinApi
import com.qyqy.cupid.apis.LineHelp
import com.qyqy.cupid.data.OldWithdrawType
import com.qyqy.cupid.data.UserIncomeConfig
import com.qyqy.cupid.utils.CupidTaskManager
import com.qyqy.ucoo.accountManager
import com.qyqy.ucoo.compose.presentation.game.ExchangeItem
import com.qyqy.ucoo.compose.presentation.game.GameDiamondApi
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonArray

/**
 *  @time 2024/7/31
 *  <AUTHOR>
 *  @package com.qyqy.cupid.model
 *  收益页IncomePage的ViewModel
 */
class IncomeViewModel : ViewModel() {
    private val diamondApi by lazy {
        createApi(GameDiamondApi::class.java)
    }

    private val coinApi by lazy {
        createApi(JapanCoinApi::class.java)
    }

    val taskManager: CupidTaskManager = CupidTaskManager(true,7)

    //所有钻石可兑换的东西
    private val _diamondExchangeList = MutableStateFlow(listOf<ExchangeItem>())
    val diamondExchangeList = _diamondExchangeList.asStateFlow()

    private val _incomeConfig = MutableStateFlow<UserIncomeConfig?>(null)
    val mIncomeConfig = _incomeConfig.asStateFlow()

    private val _withdrawWays = MutableStateFlow<List<OldWithdrawType>>(listOf())
    val withdrawWays = _withdrawWays.asStateFlow()

    val allSeriesTasks = taskManager.activityTaskList


    private val _refreshFlow = MutableStateFlow(false)
    val refreshFlow = _refreshFlow.asStateFlow()

    init {
        refresh()
    }

    fun refresh() {
        refreshDiamondExchangeList()
        requestIncomeConfig()
        taskManager.refreshTasks(viewModelScope)
//        if (!isInit) {
        accountManager.refreshSelfUserByRemote()
//        }
    }

    suspend fun refreshWithdrawWays(): Result<JsonObject> {
        return runApiCatching {
            coinApi.getWithdrawList(mapOf("confirm" to "false", "withdraw_type" to -1))
        }.onSuccess {
            val result = it["withdraw_types"]?.jsonArray?.let { sAppJson.decodeFromJsonElement<List<OldWithdrawType>>(it) } ?: listOf()
            _withdrawWays.value = result
        }.onFailure {

        }
    }

    suspend fun createWithdrawOrder(type: Int): Result<JsonObject> {
        return runApiCatching {
            coinApi.createWithdraw(mapOf("confirm" to "true", "withdraw_type" to type))
        }
    }

    /**
     *     class ExchangePriceType(DescribedEnum):
     *         DIAMOND = 1, '钻石'
     *     JAPAN_DIAMOND = 2, '日本钻石'
     *
     *
     *     class ItemTypeEnum(DescribedEnum):
     *         AVATAR_FRAME = 1, '头像框', receive_prop
     *     MEDAL = 2, '勋章', receive_prop
     *     ROOM_ENTER_EFFECT = 3, '进场特效', receive_prop
     *     # 这里的道具枚举值预留
     *     CHAT_BUBBLE = 5, '聊天气泡', receive_prop
     *
     *     GOLD_COIN = 20, '金币', receive_gold_coin
     *
     *     JAPAN_COIN = 100, '日本金币', exchange_japan_coin
     *     JAPAN_CASH = 101, '日本现金', exchange_japan_cash
     *
     */
    private fun refreshDiamondExchangeList() {
        viewModelScope.launch {
            runApiCatching {
                diamondApi.fetchExchangeList(2)
            }.onSuccess {
                _diamondExchangeList.value = it.items
            }
        }
    }

    private fun requestIncomeConfig() {
        viewModelScope.launch {
            runApiCatching {
                _refreshFlow.value = true
                coinApi.getIncomeConfig()
            }.onSuccess {
                _incomeConfig.value = it
                _refreshFlow.value = false
            }.onFailure {
                _refreshFlow.value = false
            }
        }
    }

    suspend fun exchangeDiamond(item: ExchangeItem): Result<JsonObject> {
        return runApiCatching {
            diamondApi.postDiamondExchange(mapOf("item_id" to item.id.toString()))
        }
    }

    suspend fun getLineHelp(): LineHelp? {
        return runApiCatching { coinApi.getLineHelp() }.getOrNull()
    }
}