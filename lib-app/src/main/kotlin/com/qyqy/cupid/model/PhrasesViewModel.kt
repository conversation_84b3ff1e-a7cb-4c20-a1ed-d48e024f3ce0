package com.qyqy.cupid.model

import androidx.lifecycle.viewModelScope
import com.overseas.common.utils.isPreviewOnCompose
import com.qyqy.cupid.apis.PhrasesApi
import com.qyqy.cupid.data.PhrasesSettingBean
import com.qyqy.cupid.data.WithdrawRecordBean
import com.qyqy.ucoo.compose.state.StateViewModelWithIntPage
import com.qyqy.ucoo.http.ApiException
import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getBoolOrNull
import com.qyqy.ucoo.http.getOrNull
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.toast
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement

class PhrasesViewModel : StateViewModelWithIntPage<PhrasesSettingBean.MyPrologue>() {
    private val api by lazy {
        createApi<PhrasesApi>()
    }

    private val _phrasesSetting = MutableStateFlow<PhrasesSettingBean?>(null)
    val phrasesSetting = _phrasesSetting.asStateFlow()

    private val _phrasesList = MutableStateFlow<List<PhrasesSettingBean.MyPrologue>?>(null)
    val phrasesList = _phrasesList.asStateFlow()

    private val _floatWidget = MutableStateFlow<PhrasesSettingBean.FloatWidgetSetting?>(null)
    val floatWidget = _floatWidget.asStateFlow()

    init {
        if (isPreviewOnCompose) {
            _phrasesSetting.value = PhrasesSettingBean()
//            _phrasesSetting.value = PhrasesSettingBean(defaultPrologue = PhrasesSettingBean.DefaultPrologue(title = "あいさつ言葉がまだ設定されていません、下記の通りに設定することができます、最大11通を設定できます",
//                advice1 = "1.優しくておもしろいあいさつ言葉を設定して、相手の返信率を高めることができます、下記の例を参考してください（完全に同じ文書ではなく、色々異なる文書を書いてくださいね）",
//                advice2 = "2.エロい・広告などコンテンツを入れる場合、アカウントが停止されます",
//                advice3 = "3.あいさつ言葉を完了した後、それぞれ異なる文書に対して、返答率などデータがまとめられます、そのデートを準じて一番いいあいさつ言葉を更新しましょう。わたし-設定-あいさつ言葉設定から編集できます",
//                templates = listOf(  "趣味は何ですか？教えてくださいね！","気になったのでメッセージしてみました！", "初めまして！よかったらお話しませんか (＾_＾)")
//            ))
        }
    }

    override suspend fun loadData(pageNum: Int): Result<List<PhrasesSettingBean.MyPrologue>> {
        if (pageNum != 1) {
            return Result.success(emptyList())
        }

        if (isPreviewOnCompose) {
            return Result.success(listOf())
        }

        val result = runApiCatching { api.getPhrasesSettings() }

        return if (result.isSuccess) {
            _phrasesSetting.value = result.getOrNull()
            Result.success(result.getOrNull()?.myPrologue ?: emptyList())
        } else {
            Result.failure<List<PhrasesSettingBean.MyPrologue>>(
                result.exceptionOrNull() ?: ApiException(msg = "error")
            ).also {
                toast(it.exceptionOrNull()?.message ?: "")
            }
        }
    }

    suspend fun addPhrases(content: String): Result<JsonObject> {
        return runApiCatching { api.addPhrases(mapOf("content" to content)) }
    }

    suspend fun deletePhrases(id: Int): Result<JsonObject> {
        return runApiCatching { api.deletePhrases(mapOf("prologue_id" to id)) }
    }

    suspend fun sendPhrases(id: Int): ApiResponse<JsonObject> {
        return api.sendPhrases(mapOf("target_userid" to id))
    }

    fun refreshPhrasesList(targetId: Int) {
        viewModelScope.launch {
            runApiCatching { api.getMyPhrases(targetId) }
                .onSuccess { result ->
                    if (result.getBoolOrNull("is_show") == true) {
                        val list = result.get("list")?.let {
                            sAppJson.decodeFromJsonElement<List<PhrasesSettingBean.MyPrologue>>(it)
                        } ?: listOf()
                        _phrasesList.value = list
                    }
                }
        }
    }

    fun refreshFloatWidgetSetting() {
        viewModelScope.launch {
            runApiCatching { api.getPhrasesFloatWidget() }
                .onSuccess {
                    _floatWidget.value = it
                }
        }
    }
}
