package com.qyqy.cupid.model

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.cupid.ui.UIEffect
import com.qyqy.ucoo.R
import com.qyqy.ucoo.config.ConfigApi
import com.qyqy.ucoo.core.oss.Uploader
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.utils.LogUtils
import com.qyqy.ucoo.utils.TAG
import io.github.album.MediaData
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString

/**
 *  @time 2024/7/19
 *  <AUTHOR>
 *  @package com.qyqy.keya.model
 *  举报ReportPage的viewModel
 */
class ReportViewModel() : ViewModel() {
    // 图片或视频列表
    private val _mediaList = MutableStateFlow(emptyList<MediaData>())
    val mediaResources = _mediaList.asStateFlow()

    private var uploadJob: Job? = null
        set(value) {
            field = value
            if (value != null) {
                _uploadingState.value = true
            } else {
                _uploadingState.value = false
            }
        }
    private val _uploadingState = MutableStateFlow(false)
    val uploading = _uploadingState.asStateFlow()

    private val _uploadResult = MutableStateFlow(false)
    val uploadResult = _uploadResult.asSharedFlow()

    private var reportType: Int = 0
    private var reportId: String = ""
    private var reportCategory: Int = 0

    /**
     * 设置提交模板(防止之后会出现好几种举报类型)
     *
     * @param type 举报类型
     * @param id 举报对应的id
     */
    fun setReportTemplate(category: Int, type: Int, id: String) {
        this.reportType = type
        this.reportId = id
        this.reportCategory = category
    }

    /**
     * 更新选择的媒体资源
     * 这个地方不能全部替换, 如果Matisse选择时直接返回, 会在这个地方给一个空的List, 全部替换会导致之前的数据全部被清空
     * @param data 媒体资源(基于Matisse)
     */
    fun updateMediaResources(data: List<MediaData>, replaceAll: Boolean = false) {
        if (replaceAll) {
            _mediaList.value = data.toList()
        } else {
            val newList = _mediaList.value.toMutableList()
            data.forEach {
                if (!newList.contains(it)) {
                    newList.add(it)
                }
            }
            _mediaList.value = newList
        }
    }

    /**
     * 删除列表中的媒体资源
     *
     * @param index 媒体资源下标
     */
    fun removeMediaResources(index: Int) {
        val newList = _mediaList.value.toMutableList()
        newList.removeAt(index)
        _mediaList.value = newList
    }

    private val commonApi = createApi(ConfigApi::class.java)

    /**
     * 上传所有内容
     *
     * @param textValue 文本信息
     * @return 网络请求结果
     */
    fun submitReport(textValue: String) {
        uploadJob = viewModelScope.launch {
            val uploadImages = Uploader.uploadMedias(_mediaList.value, "accusate") ?: return@launch
            runApiCatching {
                commonApi.reportAccusate(
                    buildMap {
                        put("target_type", reportType)
                        put("target_id", reportId)
                        put("accusation_type", reportCategory)
                        val content = textValue
                        if (content.isNotBlank()) {
                            put("note", content)
                        }
                        if (uploadImages.isNotEmpty()) {
                            put("media_list", sAppJson.encodeToString(uploadImages))
                        }
                    }
                )
            }.onSuccess {
                toastRes(R.string.cpd_report_success)
                uploadJob = null
                _uploadResult.value = true
            }.onFailure {
                uploadJob = null
            }
        }
    }

    fun cancelUpload() {
        uploadJob?.cancel()
        uploadJob = null
        LogUtils.i(TAG, "cancel upload images.")
    }
}