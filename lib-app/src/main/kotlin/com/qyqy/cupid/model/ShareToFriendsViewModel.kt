package com.qyqy.cupid.model

import androidx.compose.runtime.MutableState
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.account.UserInfo
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.im.chat.share.ShareApi
import com.qyqy.ucoo.mine.FriendsContract
import com.qyqy.ucoo.mine.FriendsViewModel
import kotlinx.coroutines.launch

/**
 *  @time 8/23/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.model
 *  1 部落/家族 2 私聊 3 群聊
 */
class ShareToFriendsViewModel(val shareType: Int) : FriendsViewModel() {
    private val shareApi by lazy {
        createApi(ShareApi::class.java)
    }

    fun shareFamily(userInfo: UserInfo, hasInvited: MutableState<Boolean>? = null) {
        viewModelScope.launch {
            runApiCatching {
                //因为 部落/家族 只会有一个,所有不需要传分享id
                shareApi.shareTribe(buildMap {
                    put("invite_type", shareType.toString())
                    put("target_user_id", userInfo.id)
                })
            }.onSuccess {
                hasInvited?.value = true
                updateUser({ it.id == userInfo.id }) {
                    it.copy(hasInvited = true)
                }
            }.toastError()
        }
    }

    fun shareAudioRoom(room_id: String, userInfo: UserInfo, hasInvited: MutableState<Boolean>? = null) {
        viewModelScope.launch {
            runApiCatching {
                shareApi.shareAudioRoom(buildMap {
                    put("share_type", shareType.toString())
                    put("room_id", room_id)
                    put("target_user_id", userInfo.id)
                })
            }.onSuccess {
                hasInvited?.value = true
                updateUser({ it.id == userInfo.id }) {
                    it.copy(hasInvited = true)
                }
            }.toastError()
        }
    }

    private fun updateUser(predicate: (UserInfo) -> Boolean, block: (UserInfo) -> UserInfo) {
        setState {
            val listState1 = this.listState
            val ls = if (listState1 is FriendsContract.FriendsState.Success) {
                val newList = listState1.list.toMutableList()
                val pos = newList.indexOfFirst(predicate)
                newList[pos] = block(newList[pos])
                listState1.copy(list = newList)
            } else listState1
            copy(listState = ls)
        }
    }
}