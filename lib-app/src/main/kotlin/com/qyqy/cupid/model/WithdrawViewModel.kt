package com.qyqy.cupid.model

import android.content.Context
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.cupid.FaceKt
import com.qyqy.cupid.apis.WithdrawApi
import com.qyqy.cupid.data.WithdrawList
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.http.apiException
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getStringOrNull
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.utils.takeIsNotEmpty
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonPrimitive

/**
 *  @time 9/11/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.model
 *  提现
 */
class WithdrawViewModel : ViewModel() {

    sealed class Event {
        data class Error(val msg: String) : Event()

        data class RealAuth(
            val content: String,
            val confirmButton: String,
            val cancelButton: String,
        ) : Event()
    }

    private val _withdrawWays = MutableStateFlow<WithdrawList>(WithdrawList(listOf(), ""))
    val withdrawWays = _withdrawWays.asStateFlow()

    private val _withdrawEvent = MutableSharedFlow<Event>()
    val withdrawEvent = _withdrawEvent.asSharedFlow()

    private val api by lazy {
        createApi<WithdrawApi>()
    }

    suspend fun precheckWithdraw() {
        _withdrawWays.value = WithdrawList(listOf(), "")
        runApiCatching(globalErrorHandleIntercepted = {
            it.status == -100
        }) {
            api.precheckWithdraw()
        }.onFailure { e ->
            e.apiException?.takeIf {
                it.code == -100 // 私密小屋需要送礼
            }?.extra?.takeIf {
                it["extra_operation_type"]?.jsonPrimitive?.intOrNull == 11
            }?.also {
                viewModelScope.launch {
                    _withdrawEvent.emit(
                        Event.RealAuth(
                            it.getStringOrNull("content").orEmpty(),
                            it.getStringOrNull("confirm_btn_text").orEmpty(),
                            it.getStringOrNull("cancel_btn_text").orEmpty()
                        )
                    )
                }
            } ?: run {
                viewModelScope.launch {
                    _withdrawEvent.emit(Event.Error(e.message.orEmpty()))
                }
            }
        }.onSuccess {
            it.getStringOrNull("jump_link")?.takeIsNotEmpty()?.also { link ->
                AppLinkManager.controller?.navigateByLink(link)
            }

            try {
                delay(300)
            } finally {
                withContext(NonCancellable) {
                    _withdrawEvent.emit(Event.Error("")) // 只是为了关闭弹窗
                }
            }
            // 不再使用原生弹窗了
//            refreshWithdrawWays()
        }
    }

    fun startRealAuth(context: Context, dialog: IDialog) {
        viewModelScope.launch {
            runApiCatching { api.createRealAuth(mapOf("scene_type" to "0103")) }.onSuccess {
                FaceKt.startFaceAuth(context, it) { success ->
                    dialog.dismiss()
                    if (success) {
                        viewModelScope.launch {
                            runApiCatching { api.completeRealAuth(mapOf("order_no" to it.orderNo)) }.onSuccess { ret ->
                                toast(ret.getStringOrNull("toast"))
                            }.toastError()
                        }
                    } else {
                        toastRes(R.string.cpd人脸核身检测失败)
                    }
                }
            }.onFailure {
                dialog.dismiss()
            }.toastError()
        }
    }

    private suspend fun refreshWithdrawWays() {
        runApiCatching {
            api.getWithdrawTypes()
        }.onSuccess {
            _withdrawWays.value = it
        }.onFailure { e ->
            viewModelScope.launch {
                _withdrawEvent.emit(Event.Error(e.message.orEmpty()))
            }
        }
    }

    suspend fun createWithdrawOrder(type: Int): Result<JsonObject> {
        return runApiCatching {
            api.createWithdrawOrder(mapOf("withdraw_type" to type))
        }
    }

    suspend fun getWithdrawHelper(): Result<JsonObject> {
        return runApiCatching {
            api.getWithdrawHelp(mapOf())
        }
    }

    suspend fun createWithdrawAccount(type: Int, params: List<Pair<String, String>>): Result<JsonObject> {
        return runApiCatching {
            val map = buildMap<String, Any> {
                putAll(params.toMap())
                put("withdraw_type", type)
            }
            api.createWithdrawAccount(map)
        }
    }
}