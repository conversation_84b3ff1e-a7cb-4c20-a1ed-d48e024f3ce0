package com.qyqy.cupid.theme

import androidx.compose.ui.graphics.Color

/**
 *  @time 8/16/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.theme
 */
//<color name="FF86909C">#FF86909C</color>
//<color name="FFC9CDD4">#FFC9CDD4</color>
//<color name="FF4E5969">#FF4E5969</color>
//<color name="FFFF5E8B">#FFFF5E8B</color>
//<color name="FFF1F2F3">#FFF1F2F3</color>
//<color name="FF1D2129">#FF1D2129</color>
//<color name="FFFFE58C">#FFFFE58C</color>
//<color name="FFFFB71A">#FFFFB71A</color>
//<color name="FFF5F7F9">#FFF5F7F9</color>
//<color name="A501D2129">#501D2129</color>
//<color name="FF000000">#FF000000</color>
//<color name="FF5C3A0E">#FF5C3A0E</color>
//<color name="FF86150E">#FF86150E</color>
//<color name="FFF0F0F0">#FFF0F0F0</color>
//<color name="FF606161">#FF606161</color>
//<color name="FFFAFAFA">#FFFAFAFA</color>
//<color name="FFE5E6EB">#FFE5E6EB</color>
//<color name="FFFFE666">#FFFFE666</color>
object CpdColors {
    val FFFF5E8B = Color(0xFFFF5E8B)  //cupid 主色调
    val FFFFCFDC = Color(0xFFFFCFDC)  //cupid 浅色调
    val FFFFEAF0 = Color(0xFFFFEAF0)
    val FF86909C = Color(0xFF86909C)  //文字用的最多的颜色
    val FF1D2129 = Color(0xFF1D2129)  //文字用的第二多的颜色
    val FFF1F2F3 = Color(0xFFF1F2F3)  //用的最多的淡色
    val FFC9CDD4 = Color(0xFFC9CDD4)
    val FF4E5969 = Color(0xFF4E5969)
    val FF5C3A0E = Color(0xFF5C3A0E)
    val FFE3C4A8 = Color(0xFFE3C4A8)
    val FFECDFB9 = Color(0xFFECDFB9)
    val FF3D241F = Color(0xFF3D241F)
    val FFF5F7F9 = Color(0xFFF5F7F9)
    val FFF0F0F0 = Color(0xFFF0F0F0)
    val FFFFF3F6 = Color(0xFFFFF3F6)
    val FFFAFAFA = Color(0xFFFAFAFA)
    val FFFFB71A = Color(0xFFFFB71A)
}