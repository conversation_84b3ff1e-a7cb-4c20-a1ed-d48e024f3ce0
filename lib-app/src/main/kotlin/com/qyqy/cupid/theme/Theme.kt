package com.qyqy.cupid.theme

import com.qyqy.ucoo.LocalUserPartition
import com.qyqy.ucoo.UserPartition
import androidx.collection.mutableScatterMapOf
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.navigation.compose.rememberNavController
import com.qyqy.cupid.ui.AppNavController
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.NavArgument
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.dialog.rememberSimpleDialogQueue
import com.qyqy.cupid.widgets.LocalDragOverlapScope
import com.qyqy.cupid.widgets.rememberDragOverlapScope
import com.qyqy.ucoo.compose.ui.LoadingLayout


object CupidColors {
    val Divider = Color(0xFFF1F2F3)
}

@Composable
fun CupidTheme(content: @Composable () -> Unit) {
    val colorScheme = MaterialTheme.colorScheme.copy(
        primary = Color(0xFFFF5E8B),
        onPrimary = Color.White,
        surface = Color.White,
        onSurface = Color(0xFF222222),
        onSurfaceVariant = Color(0xFF86909C),
        background = Color(0xFFF5F7F9),
        inverseSurface = Color(0xFF292929),
        inverseOnSurface = Color.White,
        onBackground = Color(0xFF222222),
        onSecondary = Color(0xFF1D2129)
    )

    MaterialTheme(colorScheme = colorScheme, typography = cupidTypography(), content = content)
}

@Composable
fun PreviewCupidTheme(content: @Composable () -> Unit) {
    val colorScheme =
        MaterialTheme.colorScheme.copy(
            primary = Color(0xFFFF5E8B),
            onPrimary = Color.White,
            surface = Color.Black,
            onSurface = Color(0xFF222222),
            onSurfaceVariant = Color(0xFF86909C),
            background = Color(0xFFF5F7F9),
            onBackground = Color(0xFF222222),
            onSecondary = Color(0xFF1D2129)
        )

    val navController = rememberNavController()

    val arguments = mutableScatterMapOf<String, NavArgument>()
    val scope = rememberCoroutineScope()
    val appNavController = remember(navController) {
        AppNavController(navController, arguments, scope)
    }

    val dialogQueue = rememberSimpleDialogQueue()

    CompositionLocalProvider(
        LocalAppNavController provides appNavController,
        LocalUserPartition provides UserPartition.Cupid,
        LocalDialogQueue provides dialogQueue,
        LocalDragOverlapScope provides rememberDragOverlapScope(),
    ) {
        MaterialTheme(colorScheme = colorScheme, typography = cupidTypography()) {
            Surface {
                LoadingLayout {
                    content()
                }
            }
        }
    }
}

@Composable
fun getNavigationPadding(
    minimumPadding: Dp = 0.dp,
    density: Density = LocalDensity.current,
): Dp {
    return with(density) {
        WindowInsets.navigationBars
            .getBottom(density)
            .toDp()
    }.coerceAtLeast(minimumPadding)
}

@Composable
fun Modifier.navigationPadding(
    minimumPadding: Dp = 0.dp,
    density: Density = LocalDensity.current,
) = padding(bottom = getNavigationPadding(minimumPadding, density))

