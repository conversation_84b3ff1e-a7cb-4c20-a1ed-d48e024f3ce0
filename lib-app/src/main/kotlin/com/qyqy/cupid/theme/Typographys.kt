package com.qyqy.cupid.theme

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp

/**
 * 不建议这里设置颜色，可能会影响组件期望颜色，看代码会改变LocalTextStyle
 */
@Composable
fun cupidTypography() = MaterialTheme.typography.copy(
    displaySmall = TextStyle(fontSize = 10.sp),
    bodyMedium = TextStyle(fontSize = 14.sp),
    bodyLarge = MaterialTheme.typography.bodyLarge.copy(color = Color.Unspecified),
    headlineMedium = TextStyle(fontSize = 16.sp, fontWeight = FontWeight.Medium),
    titleLarge = TextStyle(fontSize = 18.sp, fontWeight = FontWeight.Medium),
    labelMedium = TextStyle(color = Color(0xFF86909C), fontSize = 14.sp),
)