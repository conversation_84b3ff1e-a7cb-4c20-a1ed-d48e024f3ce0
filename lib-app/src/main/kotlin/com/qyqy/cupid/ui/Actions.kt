package com.qyqy.cupid.ui

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.Stable
import androidx.navigation.NavOptions
import androidx.navigation.Navigator
import com.overseas.common.utils.noOpDelegate
import com.qyqy.cupid.data.CallMode
import com.qyqy.cupid.data.PhrasesSettingBean
import com.qyqy.cupid.im.panel.gift.GiftPosition
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.home.mine.edit.ChangeableProperty
import com.qyqy.cupid.ui.live.InputTextState
import com.qyqy.cupid.ui.live.RoomButton
import com.qyqy.cupid.ui.relations.RelationType
import com.qyqy.cupid.ui.relations.Relations
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.bean.PrivilegedGiftRemindBean
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.im.bean.CPGift
import com.qyqy.ucoo.im.compat.MessageBundle
import com.qyqy.ucoo.im.compat.UCInstanceMessage

interface INavAction {

    fun onNavigateTo(
        name: String,
        arguments: Map<String, Any>? = null,
        navigatorExtras: Navigator.Extras? = null,
        navOptions: NavOptions? = null,
    ) {
        onNavigateTo(DestinationRoute(name, arguments, navigatorExtras, navOptions))
    }

    fun onNavigateTo(destination: DestinationRoute)

    fun onNavigateToWeb(
        url: String,
        navigatorExtras: Navigator.Extras? = null,
        navOptions: NavOptions? = null,
    ) {
        onNavigateTo(CupidRouters.Web, mapOf("url" to url))
    }
}

interface IHomeAction : INavAction {
    companion object {
        val Empty = object : IHomeAction by noOpDelegate() {}
    }
}

fun INavAction.navigateToProfile(userId: String) {
    onNavigateTo(profileDestination(userId))
}

fun INavAction.navigateToRelationPage(@RelationType type: Int = Relations.FOCUS) {
    onNavigateTo(CupidRouters.RELATION_PAGE, arguments = mapOf(Const.KEY_TYPE to type))
}

fun INavAction.navigatorToProfileEdit(editors: ChangeableProperty = ChangeableProperty.NONE) {
    onNavigateTo(CupidRouters.PROFILE_EDIT, arguments = mapOf("showEditor" to editors.postKey))
}

interface IIMAction : INavAction {

    companion object {
        val Empty = object : IC2CAction by noOpDelegate() {}
    }

    fun onSendMessage(message: MessageBundle)

    fun onSendMultipleMessage(messages: List<MessageBundle>)

    fun onResendMessage(message: UCInstanceMessage)

    fun onPreview(message: UCInstanceMessage) = Unit

    fun onClickCoinPKItem(roundID: Int) = Unit
}

data class GiftExtra(
    val fromPacket: Boolean = false,
    val greetings: String = "",
    val isIntimate: Boolean = false,
    val comboId: String = "",
)

interface IGiftAction : IDialogAction {

    fun onShowGiftPanel(position: GiftPosition? = null)

    fun onSendGift(gift: CPGift, count: Int, extra: GiftExtra = GiftExtra()) = Unit

    fun onSendGiftTo(targets: List<String>, gift: CPGift, count: Int, extra: GiftExtra = GiftExtra()) = Unit

    fun onSendGideComboFinish() = Unit

    fun onShowRechargePanel()

    fun shouldLuckyDataChange(should: Boolean) = Unit
}

interface IUserMenuAction : IDialogAction {
    fun onShowReport()

    fun onShowBlack()

    fun onBlack(dialog: IDialog)
}

interface IC2CIMAction : IIMAction {

    fun onStartC2CCall(mode: CallMode) = Unit
}

@Stable
interface IC2CAction : IC2CIMAction, IGiftAction, IUserMenuAction {

    companion object {
        val Empty = object : IC2CAction by noOpDelegate() {}
    }

    fun onShowExchangeLineRequestDialog()

    fun onRequestExchangeLine()

    fun onShowIntimacyRuleDialog()

    fun onRequestJoinFamily(familyId: Int)

    fun onRequestJoinVoiceRoom(roomId: Int)

    fun onChatPhrasesClicked(phrases: PhrasesSettingBean.MyPrologue)

    fun onCurrencyGift(coinGiftEnable: Boolean, diamondGiftEnable: Boolean)

    fun onConfirmCurrencyGift(orderId: String)

    fun onAgreeCpInvite(code: String)

    fun goBecomeCouplePage()
}

interface IVoiceLiveAction : IIMAction, IGiftAction {

    companion object {
        val Empty = object : IVoiceLiveAction by noOpDelegate() {}
    }

    fun upMic(index: Int = -1)

    fun downMic()

    fun toggleMicrophone()

    fun collapseRoom()

    fun exitRoom()

    fun showUserInfoPanel(userId: String, giftPosition: GiftPosition? = null) {
        showUserInfoPanel(AppUser(id = userId), giftPosition)
    }

    fun showUserInfoPanel(user: User, giftPosition: GiftPosition? = null)

    //分享房间
    fun showShareRoomPanel()

    fun shareToFamily()

    fun shareToFriend()

    fun showMessagePanel()

    //region 语音房设置

    //显示语音房更多设置
    fun showRoomMangePanel()

    //显示语音房编辑信息
    fun showRoomInfoManagePanel()

    fun showRoomModeManagePanel()

    //举报房间
    fun reportRoom()

    //拉黑房间
    fun showBlackRoomDialog()

    //导航至管理员页面
    fun navigateToAdministrator()

    //导航至黑名单页面
    fun navigateToBlackList()

    fun showLockRoomDialog()

    fun toggleRoomLock(dialog: IDialog, setLock: Boolean, password: String? = null)

    fun onRoomNameUpdate(newName: String, dialog: IDialog)

    fun onEditNotice()
    //endregion

    fun atUser(user: User)

    fun setInputTextState(inputState: InputTextState)

    fun onClickNormalRoomButton(button: RoomButton.Normal)

    fun inviteUpMic(user: User)

    fun kickDownMic(user: User)

    fun showManageUserMenu(userState: MutableState<AppUser>)

    fun followUser(userState: MutableState<AppUser>, isFollow: Boolean)

    fun blackUserInRoom(userState: MutableState<AppUser>, isBlack: Boolean)

    fun updateRoomAdmin(userId: Int, isAdmin: Boolean)

    fun gotoGiftWallPage(userId: Int)

    fun gotoFamilyDetail(familyId: Int)

    fun onMessageClicked(message: UCInstanceMessage)

    fun onCoinWinnerCreate(base_coin: Int)
}

@Stable
interface UIEffect

@Stable
sealed interface IMEffect : UIEffect {
    data object OnRevNewMessage : IMEffect

    data class OnShowRechargeDialog(val title: String, val hint: String) : IMEffect

    //私聊送礼引导
    data class OnShowUnuseGiftDialog(val bean: PrivilegedGiftRemindBean) : IMEffect
}

