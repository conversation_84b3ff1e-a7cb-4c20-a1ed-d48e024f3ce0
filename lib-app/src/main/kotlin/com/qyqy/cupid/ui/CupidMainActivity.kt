package com.qyqy.cupid.ui

import android.os.Build
import android.os.Bundle
import android.util.Log
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import com.qyqy.ucoo.LocalUserPartition
import com.qyqy.ucoo.UserPartition
import androidx.collection.mutableScatterMapOf
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableIntState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.hjq.language.MultiLanguages
import com.overseas.common.ext.launch
import com.overseas.common.ext.startActivity
import com.qyqy.cupid.CupidGlobalApiExceptionHandle
import com.qyqy.cupid.event.AppActionEvent
import com.qyqy.cupid.event.AppDialogEvent
import com.qyqy.cupid.event.RegisterMessagePushGlobalDialog
import com.qyqy.cupid.im.panel.gift.GiftPosition
import com.qyqy.cupid.theme.CupidTheme
import com.qyqy.cupid.ui.call.RegisterC2CCalling
import com.qyqy.cupid.ui.coin.DiamondHistoryPage
import com.qyqy.cupid.ui.coin.TopupHistoryPage
import com.qyqy.cupid.ui.coin.WithdrawInfoPage
import com.qyqy.cupid.ui.coin.WithdrawRecordPage
import com.qyqy.cupid.ui.dialog.AccountBindDialog
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.dialog.WithdrawDialog
import com.qyqy.cupid.ui.dialog.rememberSimpleDialogQueue
import com.qyqy.cupid.ui.global.CupidPendant
import com.qyqy.cupid.ui.global.ScreenLocation
import com.qyqy.cupid.ui.global.getHomePosition
import com.qyqy.cupid.ui.global.getLocation
import com.qyqy.cupid.ui.home.SearchPage
import com.qyqy.cupid.ui.home.TestPage
import com.qyqy.cupid.ui.home.homeScreen
import com.qyqy.cupid.ui.home.message.C2CChatConst
import com.qyqy.cupid.ui.home.message.C2CChatScreen
import com.qyqy.cupid.ui.home.message.PhrasesPage
import com.qyqy.cupid.ui.home.mine.BlackListScreen
import com.qyqy.cupid.ui.home.mine.MyCoinPage
import com.qyqy.cupid.ui.home.mine.crony.BecomeToCoupleScreen
import com.qyqy.cupid.ui.home.mine.crony.CronyManagePage
import com.qyqy.cupid.ui.home.mine.crony.InviteMyCronyPage
import com.qyqy.cupid.ui.home.mine.crony.InviteToCronyPage
import com.qyqy.cupid.ui.home.mine.dressup.DressUpListScreen
import com.qyqy.cupid.ui.home.mine.dressup.DressUpMallScreen
import com.qyqy.cupid.ui.home.mine.dressup.DressUpScreen
import com.qyqy.cupid.ui.home.mine.edit.AlbumEditScreen
import com.qyqy.cupid.ui.home.mine.edit.ChangeableProperty
import com.qyqy.cupid.ui.home.mine.edit.CupidEditProfilePage
import com.qyqy.cupid.ui.home.mine.edit.getChangeablePropertyType
import com.qyqy.cupid.ui.home.mine.visitors.VisitorsPage
import com.qyqy.cupid.ui.live.RegisterVoiceLiveChatRoomFloat
import com.qyqy.cupid.ui.live.VoiceLiveChatRoomScreen
import com.qyqy.cupid.ui.live.VoiceLiveHelper
import com.qyqy.cupid.ui.live.settings.CupidBackgroundSettingScreenRouter
import com.qyqy.cupid.ui.live.settings.VoiceRoomAdminListPage
import com.qyqy.cupid.ui.live.settings.VoiceRoomBlackListPage
import com.qyqy.cupid.ui.membership.MembershipActivePage
import com.qyqy.cupid.ui.profile.CupidProfileScreen
import com.qyqy.cupid.ui.profile.GiftProfileScreen
import com.qyqy.cupid.ui.profile.RechargeDialog
import com.qyqy.cupid.ui.profile.guard.GuardListViewModel
import com.qyqy.cupid.ui.profile.guard.MyGuardScreen
import com.qyqy.cupid.ui.relations.RelationsScreen
import com.qyqy.cupid.ui.relations.family.FamilyAdminManageScreen
import com.qyqy.cupid.ui.relations.family.FamilyAdminSetScreen
import com.qyqy.cupid.ui.relations.family.FamilyApplyScreen
import com.qyqy.cupid.ui.relations.family.FamilyDetailPage
import com.qyqy.cupid.ui.relations.family.FamilyHomeScreen
import com.qyqy.cupid.ui.relations.family.FamilyRemoveMemberScreen
import com.qyqy.cupid.ui.relations.family.FamilySettingPage
import com.qyqy.cupid.ui.relations.family.FamilySquareScreen
import com.qyqy.cupid.ui.relations.family.rank.FamilyRankScreen
import com.qyqy.cupid.ui.setting.AboutUsPage
import com.qyqy.cupid.ui.setting.AddPhrasesPage
import com.qyqy.cupid.ui.setting.FeedbackPage
import com.qyqy.cupid.ui.setting.ReportPage
import com.qyqy.cupid.ui.setting.ReportStartPage
import com.qyqy.cupid.ui.setting.SettingsPage
import com.qyqy.cupid.ui.share.ShareToFriendPage
import com.qyqy.cupid.utils.AppEnvironment
import com.qyqy.cupid.utils.AppUsageUtil
import com.qyqy.cupid.utils.CupidFamilyManager
import com.qyqy.cupid.utils.FlowEventBus
import com.qyqy.cupid.utils.eventbus.LocalEventBus
import com.qyqy.cupid.utils.eventbus.appEventBus
import com.qyqy.cupid.utils.startEnvironment
import com.qyqy.cupid.widgets.CupidGlobalVoiceRoomInviteWidget
import com.qyqy.cupid.widgets.LocalDragOverlapScope
import com.qyqy.cupid.widgets.rememberDragOverlapScope
import com.qyqy.cupid.widgets.webview.WebComposeView
import com.qyqy.ucoo.BuildConfig
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.isInvalid
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.app
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.LaunchOnceEffect
import com.qyqy.ucoo.compose.presentation.CupidGlobalMessageNotification
import com.qyqy.ucoo.compose.presentation.login.LoginActivity
import com.qyqy.ucoo.compose.ui.AppearanceStatusBars
import com.qyqy.ucoo.compose.ui.LoadingLayout
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.core.rtm.IRtmMsgHandler
import com.qyqy.ucoo.core.rtm.RtmManager
import com.qyqy.ucoo.core.rtm.RtmMsgHandler
import com.qyqy.ucoo.flavor.AppOpenHandler
import com.qyqy.ucoo.http.cupidGlobalApiExceptionHandleListener
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.sAccountTokenFlow
import com.qyqy.ucoo.sIsLoginFlow
import com.qyqy.ucoo.sIsSignIn
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.user.UserManager
import com.qyqy.ucoo.utils.EntityCallback
import com.qyqy.ucoo.utils.LogUtils
import com.qyqy.ucoo.utils.web.handler.WebBridgeHandler
import com.qyqy.ucoo.utils.web.putValueByUrl
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.invoke
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class CupidMainActivity : BaseActivity() {

    private val globalViewModel by viewModels<CupidViewModel>()

    override val darkMode: Boolean
        get() = false

    override val from: Int
        get() = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        CupidFamilyManager.init()

        val rtmMsgHandler: IRtmMsgHandler = RtmMsgHandler(false)

        // 初始化
        RtmManager.message.onEach {
            rtmMsgHandler.handleMessage(it)
        }.launchIn(lifecycleScope)

        launch {
            sIsLoginFlow.flowWithLifecycle(lifecycle)
                .filter { !it }
                .collectLatest { _ ->
                    LoginActivity::class.java.startActivity()
                    finish()
                }
        }


        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            window.isNavigationBarContrastEnforced = false
            window.isStatusBarContrastEnforced = false
        }

        val rootArguments = mutableScatterMapOf<String, NavArgument>()

        setContent {
            AppUsageUtil.CollectUseDay()
            val navController = rememberNavController()
            val scope = rememberCoroutineScope()
            val appNavController = remember(navController) {
                AppNavController(navController, rootArguments, scope).also {
                    it.bindStartDestination(CupidRouters.HOME)
                }
            }

            val env = remember {
                AppEnvironment(appNavController)
            }
            LaunchedEffect(key1 = Unit) {
                startEnvironment(env)
            }

            val dialogQueue = rememberSimpleDialogQueue(autoEnable = false)

            LaunchedEffect(key1 = Unit) {
                app.accountManager.accountBindFlow
                    .filter { it.needBindOutsideAccount }
                    .distinctUntilChanged()
                    .collectLatest { info ->
                        if (BuildConfig.DEBUG) {

                        } else {
                            dialogQueue.push(AccountBindDialog(info))
                        }
                    }
            }

            app.dialogQueue = dialogQueue

            // 创建CupidViewModel
            val viewModel = viewModel<CupidViewModel>()
            viewModel.bind(appNavController, dialogQueue)


            AppLinkManager.controller = appNavController

            val homeTState = remember {
                mutableIntStateOf(0)
            }

            val dragOverlapScope = rememberDragOverlapScope()

            CompositionLocalProvider(
                LocalAppNavController provides appNavController,
                LocalUserPartition provides UserPartition.Cupid,
                LocalDialogQueue provides dialogQueue,
                LocalDragOverlapScope provides dragOverlapScope,
            ) {
                CupidTheme {
                    LoadingLayout(modifier = Modifier.fillMaxSize()) {
                        CupidNavigationGraph(appNavController = appNavController) {
                            homeTState.intValue = it
                        }

                        CupidGlobalMessageNotification()

                        CupidGlobalVoiceRoomInviteWidget()

                        dialogQueue.DialogContent()

                        RegisterMessagePushGlobalDialog(globalViewModel)

                        Box(
                            modifier = Modifier
                                .padding(bottom = 56.dp)
                                .navigationBarsPadding()
                                .fillMaxSize()
                        ) {
                            FloatingWidget(viewModel, homeTState)
                        }

                        RegisterC2CCalling(viewModel)
                    }
                }
            }
            LaunchedEffect(key1 = Unit) {
                AppOpenHandler.getCupidOpenHandler()?.onCupidAppOpen(dialogQueue)
            }

            DisposableEffect(appNavController, dialogQueue) {
                cupidGlobalApiExceptionHandleListener = CupidGlobalApiExceptionHandle(appNavController, dialogQueue)

                onDispose {
                    cupidGlobalApiExceptionHandleListener = null
                }
            }
        }

        launch {
            repeatOnLifecycle(Lifecycle.State.CREATED) {
                FlowEventBus.normalEventFlow.collect { event ->
                    if (event is AppActionEvent) {
                        when (event.action) {
                            "joinAudioRoom" -> {
                                VoiceLiveHelper.joinActionArguments = event.params
                                val ret = event.getParam("roomId")?.let {
                                    globalViewModel.voiceLiveHelper.joinVoiceLiveRoom(it.toInt())
                                }
                                if (ret == true) {
                                    VoiceLiveHelper.joinActionArguments = null
                                    when (event.getParam("sub_action")?.toIntOrNull()) {
                                        C2CChatConst.ACTION_SHOW_GIFT -> {
                                            delay(150)
                                            Dispatchers.Default.invoke {
                                                event.getParam("position")?.let {
                                                    sAppJson.decodeFromString<GiftPosition>(it)
                                                }
                                            }.also {
                                                appEventBus.runListenerFunc(GiftListener.Key) {
                                                    showGiftPanel(it)
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            "joinCpRoom" -> {
                                VoiceLiveHelper.joinActionArguments = event.params
                                val ret = event.getParam("roomId")?.let {
                                    globalViewModel.voiceLiveHelper.joinVoiceLiveRoom(it.toInt(), isPrivate = true)
                                }
                                if (ret == true) {
                                    VoiceLiveHelper.joinActionArguments = null
                                    when (event.getParam("sub_action")?.toIntOrNull()) {
                                        C2CChatConst.ACTION_SHOW_GIFT -> {
                                            delay(150)
                                            Dispatchers.Default.invoke {
                                                event.getParam("position")?.let {
                                                    sAppJson.decodeFromString<GiftPosition>(it)
                                                }
                                            }.also {
                                                appEventBus.runListenerFunc(GiftListener.Key) {
                                                    showGiftPanel(it)
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            else -> {}
                        }
                    } else if (event is AppDialogEvent) {
                        when (event.action) {
                            "showWithdraw" -> WithdrawDialog
                            "recharge" -> RechargeDialog
                            else -> null
                        }?.let {
                            globalViewModel.dialogQueue.push(it)
                        }
                    }
                }
            }
        }

        launch {
            delay(5000)
            if (sIsSignIn) {
                UserManager.sayHi()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        AppLinkManager.controller = null
    }

    @Composable
    private fun BoxScope.FloatingWidget(
        viewModel: CupidViewModel,
        homeTState: MutableIntState,
    ) {
        val currentEntry by viewModel.appNavController.composeNav.currentBackStackEntryAsState()
        val homePage by homeTState
        val location by remember {
            derivedStateOf {
                val route = currentEntry?.destination?.route
                if (route == null) {
                    ScreenLocation.OTHER
                } else {
                    if (route.startsWith(CupidRouters.HOME)) {
                        homePage.getHomePosition()
                    } else {
                        currentEntry.getLocation()
                    }
                }
            }
        }

        RegisterVoiceLiveChatRoomFloat(
            viewModel = viewModel,
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(bottom = 10.dp)
        )

        CupidPendant(
            location = location,
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(bottom = 10.dp)
        )
    }

    override fun onAccountTokenStatusChange(isInvalid: Boolean) {
        // do nothing
    }

    override fun onResume() {
        super.onResume()
        lifecycleScope.launch {
            delay(1000)
            UIConfig.checkRefresh()
        }
    }

}

@Composable
fun CupidNavigationGraph(appNavController: AppNavController, onEntityCallback: EntityCallback<Int>) {
    val onAction = object : IHomeAction {
        override fun onNavigateTo(destination: DestinationRoute) {
            appNavController.navigate(destination)
        }
    }

    NavHost(
        navController = appNavController.composeNav,
        startDestination = appNavController.startDestination,
        enterTransition = {
            slideInHorizontally(initialOffsetX = { it })
        },
        exitTransition = {
            slideOutHorizontally(targetOffsetX = { -it })
        },
        popEnterTransition = {
            slideInHorizontally(initialOffsetX = { -it })
        },
        popExitTransition = {
            slideOutHorizontally(targetOffsetX = { it })
        },
    ) {
        navigationX(CupidRouters.HOME) { navEntry, start ->
            homeScreen(onAction = onAction, onEntityCallback = onEntityCallback)
        }

        navigationX(CupidRouters.C2CChat) { navEntry, start ->
            val id = navEntry.getArgumentOrDefault(key = "userId") {
                ""
            }
            val user = navEntry.getArgumentOrDefault(key = "user") {
                UserManager.getMemoryUserCacheById(id) ?: AppUser(id = id)
            }
            if (user.isInvalid() || user.isSelf) {
                LaunchOnceEffect {
                    appNavController.popBackStack()
                }
                return@navigationX SubRouteNavImpl.Empty
            }

            val action: Int = navEntry.getArgumentOrDefault(key = "action") {
                0
            }

            val position = navEntry.getArgumentOrDefault(key = "position") {
                ""
            }

            val subRouteNav = remember {
                SubRouteNavImpl.C2C(MutableSharedFlow())
            }

            val effects = subRouteNav.effects

            if (action > 0) {
                LaunchOnceEffect {
                    delay(300)
                    if (action == C2CChatConst.ACTION_SHOW_GIFT) {
                        if (position.isNotEmpty()) {
                            withContext(Dispatchers.IO) {
                                sAppJson.decodeFromString<GiftPosition?>(position)
                            }?.also {
                                effects.emit(it)
                                return@LaunchOnceEffect
                            }
                        }
                    }
                    effects.emit(action)
                }
            }

            C2CChatScreen(user, effects)

            subRouteNav
        }

        composableX(CupidRouters.PROFILE) {
            CupidProfileScreen(
                it.getArgumentOrDefault(key = "userId") {
                    ""
                },
                onAction
            )
        }

        composableX(CupidRouters.PROFILE_EDIT) {
            val propertyStr = it.getArgumentOrDefault(key = "showEditor") {
                ""
            }
            val editor: ChangeableProperty = getChangeablePropertyType(propertyStr)
            CupidEditProfilePage(onAction, editor)
        }

        composableX(CupidRouters.BLACK_LIST) {
            BlackListScreen(onAction)
        }

        composableX(CupidRouters.RELATION_PAGE) {
            val visibleRelationType: Int = it.getArgument(key = Const.KEY_TYPE)
            RelationsScreen(visibleRelationType = visibleRelationType, onAction)
        }

        composableX(CupidRouters.SETTINGS) {
            SettingsPage()
        }

        composableX(CupidRouters.REPORT_START) {
            ReportStartPage(
                type = it.getArgument(key = "type"),
                id = it.getArgument(key = "id")
            )
        }

        composableX(CupidRouters.REPORT) {
            ReportPage(
                category = it.getArgument(key = "category"),
                type = it.getArgument(key = "type"),
                id = it.getArgument(key = "id")
            )
        }

        composableX(CupidRouters.FEEDBACK) {
            FeedbackPage()
        }

        composableX(CupidRouters.ABOUT) {
            AboutUsPage()
        }

        composableX(CupidRouters.TOPUP_HISTORY) {
            TopupHistoryPage()
        }

        composableX(CupidRouters.DIAMOND_HISTORY) {
            DiamondHistoryPage()
        }

        composableX(CupidRouters.ALBUM_EDIT) {
            AlbumEditScreen()
        }

        //region 家族相关

        // 家族广场
        composableX(CupidRouters.FAMILY_SQUARE) {
            val effects = remember {
                MutableSharedFlow<String>()
            }
            val action = it.getArgumentOrDefault(key = "action") { "" }

            if (action.isNotEmpty()) {
                LaunchOnceEffect {
                    delay(300)
                    effects.emit(action)
                }
            }
            FamilySquareScreen(true, effects)
        }

        composableX(CupidRouters.FAMILY_HOME) {
            AppearanceStatusBars(isLight = true)
            FamilyHomeScreen()
        }

        // 家族详情
        composableX(CupidRouters.FAMILY_DETAIL) {
            AppearanceStatusBars(isLight = true)
            FamilyDetailPage(it.getArgument("family_id"))
        }

        // 家族设置
        composableX(CupidRouters.FAMILY_SETTINGS) {
            AppearanceStatusBars(isLight = true)
            FamilySettingPage(it.getArgument(key = "family_id"))
        }

        // 下面五种都不需要传入家族id, 因为每个人同一时间 有且仅有一个家族
        // 邀请加入家族
        composableX(CupidRouters.SHARE_TO_FRIENDS) {
            AppearanceStatusBars(isLight = true)
            ShareToFriendPage(
                onAction = onAction,
                shareSourceType = it.getArgument(key = "sourceType"),
                voiceRoomId = it.getArgument(key = "voiceRoomId")
            )
        }
        // 家族副族长列表
        composableX(CupidRouters.FAMILY_ADMIN_MANAGER) {
            AppearanceStatusBars(isLight = true)
            FamilyAdminManageScreen(it.getArgument(key = "family_id"))
        }
        // 添加家族副族长列表
        composableX(CupidRouters.FAMILY_ADMIN_MANAGE_ADD) {
            AppearanceStatusBars(isLight = true)
            FamilyAdminSetScreen(it.getArgument(key = "family_id"))
        }
        composableX(CupidRouters.FAMILY_ADMIN_MEMBER_REMOVE) {
            AppearanceStatusBars(isLight = true)
            FamilyRemoveMemberScreen(it.getArgument(key = "family"))
        }

        composableX(CupidRouters.FAMILY_ADMIN_APPLY) {
            AppearanceStatusBars(isLight = true)
            FamilyApplyScreen()
        }

        //endregion

        //region 语音房相关

        composableX(CupidRouters.VOICE_LIVE_ROOM) {
            val eventBus = LocalEventBus.current
            LaunchOnceEffect {
                when (it.navArgument?.getArgument<Int>("sub_action")) {
                    C2CChatConst.ACTION_SHOW_GIFT -> {
                        delay(350)
                        Dispatchers.Default.invoke {
                            it.navArgument.getArgument<GiftPosition>(key = "position")
                        }.also {
                            eventBus.runListenerFunc(GiftListener.Key) {
                                showGiftPanel(it)
                            }
                        }
                    }
                }
            }

            VoiceLiveChatRoomScreen()
        }

        composableX(CupidRouters.VOICE_BACKGROUND) {
            CupidBackgroundSettingScreenRouter(it.getArgument(key = "roomId"), null)
        }

        composableX(CupidRouters.VOICE_ADMIN_LIST) {
            VoiceRoomAdminListPage(roomId = it.getArgument(key = "id"))
        }
        composableX(CupidRouters.VOICE_BLACK_LIST) {
            VoiceRoomBlackListPage(roomId = it.getArgument(key = "id"))
        }

        //endregion

        //region 2.28.0
        composableX(CupidRouters.GIFT_WALL) {
            GiftProfileScreen(userId = it.getArgument(key = "userId"))
        }
        //endregion

        //region 2.29.0
        composableX(CupidRouters.WITHDRAW) {
            WithdrawInfoPage(it.getArgument(key = "info"))
        }

        composableX(CupidRouters.WITHDRAW_RECORDS) {
            WithdrawRecordPage()
        }
        //endregion

        composableX(CupidRouters.DRESS_UP) { backEntry ->
            val handler = backEntry.backStackEntry.savedStateHandle

            val type by handler.getStateFlow("propType", -1).collectAsStateWithLifecycle()
            val id by handler.getStateFlow("propId", -1).collectAsStateWithLifecycle()
            val time by handler.getStateFlow("propId", 0L).collectAsStateWithLifecycle()

            val propType = type.takeIf { it != -1 } ?: backEntry.getArgumentOrDefault(key = "propType") {
                -1
            }
            val propId = id.takeIf { it != -1 } ?: backEntry.getArgumentOrDefault(key = "propId") {
                -1
            }
            //用于刷新
            val serial by rememberSaveable(time) {
                mutableLongStateOf(time)
            }
            val params by remember(propType, propId) {
                mutableStateOf(propType to propId)
            }
            LogUtils.d("xct", "$propType,$propId,$serial")
            DressUpScreen(propType = params.first, propId = params.second, serial)
        }

        composableX(CupidRouters.MY_GUARD) {
            MyGuardScreen(it.getArgumentOrDefault(key = "type") { GuardListViewModel.GuardType.IN })
        }

        composableX(CupidRouters.FAMILY_RANK) {
            FamilyRankScreen()
        }

        composableX(CupidRouters.Web) {
            val url: String = it.getArgumentOrDefault(key = "url") {
                ""
            }
            if (url.isEmpty()) {
                appNavController.popBackStack()
                return@composableX
            }
            WebBridgeHandler.init()
            WebComposeView(
                url.putValueByUrl("app-language", MultiLanguages.getAppLanguage().toLanguageTag()),
                modifier = Modifier.fillMaxSize()
            ) {
                appNavController.popBackStack()
            }
        }

        composableX(CupidRouters.PAGE_TEST) {
            TestPage()
        }

        composableX(CupidRouters.MyCoin) {
            MyCoinPage()
        }

        composableX(CupidRouters.Search) {
            SearchPage()
        }

        composableX(CupidRouters.MEMBERSHIP_ACTIVE_PAGE) {
            val from = it.getArgumentOrDefault(key = "from") {
                "jp_need_vip"
            }
            MembershipActivePage(from)
        }

        composableX(CupidRouters.VISITORS) {
            VisitorsPage()
        }

        composableX(CupidRouters.CHAT_PHRASES) {
            PhrasesPage()
        }
        composableX(CupidRouters.CHAT_PHRASES_ADD) {
            AddPhrasesPage()
        }

        composableX(CupidRouters.DRESS_UP_MALL) {
            DressUpMallScreen()
        }

        composableX(CupidRouters.DRESS_UP_LIST) {
            DressUpListScreen(title = it.getArgumentOrDefault(key = "title") {
                ""
            }, propType = it.getArgumentOrDefault(key = "propType") {
                1
            })
        }

        composableX(CupidRouters.INVITE_TO_CRONY) {
            val tagId: Int = it.getArgument(key = Const.KEY_ID)
            InviteToCronyPage(tagId = tagId)
        }

        composableX(CupidRouters.CRONY_MANAGE) {
            CronyManagePage()
        }

        composableX(CupidRouters.BECOME_CP) {
            BecomeToCoupleScreen(sUser, it.getArgument(key = Const.KEY_DATA))
        }

        composableX(CupidRouters.INVITE_MY_CRONY) {
            InviteMyCronyPage(it.getArgument(key = "roomId"))
        }
    }
}
