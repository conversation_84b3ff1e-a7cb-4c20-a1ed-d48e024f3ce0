package com.qyqy.cupid.ui

object CupidRouters {

    const val HOME_SUBPAGE_FAMILY_ID = "16"

    const val HOME = "/home"

    const val C2CChat = "/c2c"

    const val PROFILE = "/profile"

    const val PROFILE_EDIT = "/profile_edit"

    const val BLACK_LIST = "/black_list"

    //关注，粉丝，好友列表
    const val RELATION_PAGE = "/relation_page"

    const val SETTINGS = "/settings"

    const val FEEDBACK = "/feedback"

    const val ABOUT = "/about"

    const val REPORT_START = "/report/start"

    const val REPORT = "/report/submit"

    const val TOPUP_HISTORY = "/topup/history"

    const val DIAMOND_HISTORY = "/diamond/history"

    const val ALBUM_EDIT = "/album_edit"

    //region 家族相关

    /**
     * 家族广场
     */
    const val FAMILY_SQUARE = "/family/square"

    /**
     * 家族主页
     */
    const val FAMILY_HOME = "/family/home"

    //家族详情
    const val FAMILY_DETAIL = "/family/detail"

    //家族设置
    const val FAMILY_SETTINGS = "/family/settings"


    const val FAMILY_ADMIN_MANAGER = "/family/manager"
    const val FAMILY_ADMIN_MANAGE_ADD = "/family/manager/add"
    const val FAMILY_ADMIN_MEMBER_REMOVE = "/family/member/remove"
    const val FAMILY_ADMIN_APPLY = "/family/member/apply"
    //endregion

    //region 语音房


    const val VOICE_LIVE_ROOM = "/live/voice"

    const val VOICE_BACKGROUND = "/voiceroom/background"

    //语音房黑名单
    const val VOICE_BLACK_LIST = "/voiceroom/manage/list"

    //语音房管理员
    const val VOICE_ADMIN_LIST = "/voiceroom/manage/administrator"

    //endregion

    //礼物墙
    const val GIFT_WALL = "/gift/wall"

    //发起提现
    const val WITHDRAW = "/wallet/withdraw"

    //提现记录
    const val WITHDRAW_RECORDS = "/wallet/withdraw/records"

    //分享至单聊
    const val SHARE_TO_FRIENDS = "/share/friend"

    // 我的装扮
    const val DRESS_UP = "/dress_up"

    //我的守护
    const val MY_GUARD = "/my_guard"

    /**
     * 家族活跃排行
     */
    const val FAMILY_RANK = "/family_rank"

    const val PAGE_TEST = "/test"

    const val Web = "/webview"

    const val MyCoin = "/mycoin"

    const val Search = "/search"

    /**
     * 会员开通页
     */
    const val MEMBERSHIP_ACTIVE_PAGE = "/membership_active_page"

    /**
     * 访客
     */
    const val VISITORS = "/visitors"


    const val CHAT_PHRASES = "/message/phrases"
    const val CHAT_PHRASES_ADD = "/message/phrases/add"

    /**
     * 装扮商城
     */
    const val DRESS_UP_MALL = "/dress_up_mall"

    const val DRESS_UP_LIST = "/dress_up_mall/list"

    /**
     * 邀请成为密友
     */
    const val INVITE_TO_CRONY = "/invite_to/crony"

    /**
     * 密友管理
     */
    const val CRONY_MANAGE = "/crony/manage"

    /**
     * 成为情侣
     */
    const val BECOME_CP = "/become/cp"

    /**
     * 邀请我的密友
     */
    const val INVITE_MY_CRONY = "/invite/mycrony"
}