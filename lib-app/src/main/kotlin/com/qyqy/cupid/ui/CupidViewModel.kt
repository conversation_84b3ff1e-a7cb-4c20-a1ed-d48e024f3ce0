package com.qyqy.cupid.ui

import android.os.SystemClock
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ProcessLifecycleOwner
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.overseas.common.ext.lifecycleFlow
import com.qyqy.cupid.data.DurationTaskInfo
import com.qyqy.cupid.ui.call.C2CCallingHelper
import com.qyqy.cupid.ui.dialog.DialogQueue
import com.qyqy.cupid.ui.live.VoiceLiveHelper
import com.qyqy.ucoo.R
import com.qyqy.ucoo.app
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.utils.TaskApi
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

sealed class MutexFeature(val type: Int) {

    abstract val tip: String

    abstract fun checkMutex(current: MutexFeature): String?

    data object Call : MutexFeature(0) {

        override val tip: String
            get() = app.getString(R.string.cpd当前正在通话中)

        override fun checkMutex(current: MutexFeature): String? {
            return current.tip
        }
    }

    data object Live : MutexFeature(1) {

        override val tip: String
            get() = app.getString(R.string.cpd当前正在语音房互动)

        override fun checkMutex(current: MutexFeature): String? {
            if (current.type == type) {
                return null
            }
            return current.tip
        }
    }
}

interface IMutexFeature {

    fun start()

    fun clear()

    fun getCurrentMutexFeature(): MutexFeature?
}

class CupidViewModel(savedStateHandle: SavedStateHandle) : ViewModel() {

    lateinit var appNavController: AppNavController

    lateinit var dialogQueue: DialogQueue<*>

    val c2cCallingHelper = C2CCallingHelper(viewModelScope, savedStateHandle, this)

    val voiceLiveHelper = VoiceLiveHelper(viewModelScope, this)

    private var initialized = false

    fun bind(appNavController: AppNavController, dialogQueue: DialogQueue<*>) {
        this.appNavController = appNavController
        this.dialogQueue = dialogQueue
        if (!initialized) {
            initialized = true
            c2cCallingHelper.start()
            voiceLiveHelper.start()
        }
    }

    init {
        ProcessLifecycleOwner.get().lifecycle.lifecycleFlow()
            .onEach { event ->
                if (event.targetState.isAtLeast(Lifecycle.State.STARTED)) { // 前台
                    onAppForeground()
                }
            }
            .launchIn(appCoroutineScope)
    }

    override fun onCleared() {
        c2cCallingHelper.clear()
        voiceLiveHelper.clear()
    }

    fun checkFeature(feature: MutexFeature): String? {
        val current: MutexFeature? = c2cCallingHelper.getCurrentMutexFeature() ?: voiceLiveHelper.getCurrentMutexFeature()
        if (current == null) {
            return null
        }
        return feature.checkMutex(current)
    }

    private val taskApi = createApi<TaskApi>()

    var taskInfo by mutableStateOf<List<DurationTaskInfo>?>(null)
        private set

    fun requestCupidDurationTasks() {
        viewModelScope.launch {
            runApiCatching {
                taskApi.requestCupidDurationTasks()
            }.onSuccess { value ->
                taskInfo = value.taskList
                checkCompletedCupidDurationTask(value.taskList)
            }
        }
    }

    private fun onAppForeground() {
        taskInfo?.also {
            checkCompletedCupidDurationTask(it)
        } ?: run {
            requestCupidDurationTasks()
        }
    }

    private var tempJob: Job? = null

    private fun checkCompletedCupidDurationTask(taskInfo: List<DurationTaskInfo>?) {
        taskInfo?.find {
            !it.taskInfo.todayFinished && it.taskInfo.seriesType == 4
        }?.taskInfo?.tasks?.find { // 在线时长任务，寻找已完成未上报的任务上报
            !it.finished
        }?.also {
            if (it.progressValue >= it.conditionTimes) {
                postCompletedCupidDurationTask()
            } else {
                viewModelScope.launch {
                    delay(it.conditionTimes.minus(it.progressValue).times(1000L))
                    postCompletedCupidDurationTask()
                }
            }
        }

        tempJob?.cancel()
        tempJob = null
        taskInfo?.find { !it.taskInfo.todayFinished && it.taskInfo.seriesType == 2 && it.taskInfo.tasks.isNotEmpty() }?.taskInfo?.apply {
            tempJob = viewModelScope.launch { // 上麦任务，计时启动停止切换控制
                var progressValue = tasks.last().progressValue

                snapshotFlow {
                    voiceLiveHelper.voiceLiveValue?.currentRoom?.selfInMic == true
                }.collectLatest {
                    if (it) {
                        timerElapsedRealtime = SystemClock.elapsedRealtime().minus(progressValue.times(1000))
                        pausedAdvanced = false
                        while (isActive) {
                            delay(1000)
                            progressValue += 1
                        }
                    } else {
                        timerElapsedRealtime = progressValue.times(1000).toLong()
                        pausedAdvanced = true
                    }
                }
            }
        }
    }

    private fun postCompletedCupidDurationTask() {
        viewModelScope.launch {
            runApiCatching {
                taskApi.postCompletedCupidDurationTask()
            }.onSuccess { value ->
                taskInfo = value.taskList
                checkCompletedCupidDurationTask(value.taskList)
            }
        }
    }
}