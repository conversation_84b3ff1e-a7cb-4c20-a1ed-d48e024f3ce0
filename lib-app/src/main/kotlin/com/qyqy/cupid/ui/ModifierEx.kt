package com.qyqy.cupid.ui

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.AnimationVector1D
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.gestures.waitForUpOrCancellation
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import kotlinx.coroutines.launch
import kotlin.math.roundToInt

/**
 * 长按以及单次点击事件
 *
 * @param anim 传入的动画类
 * @param isRunning 是否正在运行, 根据这个判断是否需要显示连击按钮
 * @param block 点击或连击事件
 * @return
 */
@Composable
fun Modifier.touchPressing(
    anim: Animatable<Float, AnimationVector1D>,
    isRunning: MutableState<Boolean>,
    maxMillisSecond: Int = 5000,
    animMaxValue: Int = 360,
    pressCallback: (isPress: Boolean) -> Unit = {},
    block: (isTap: Boolean, isDone: Boolean) -> Boolean = { _, _ -> true }
): Modifier {
    val scope = rememberCoroutineScope()
    val perMS by remember(maxMillisSecond, animMaxValue) {
        derivedStateOf {
            maxMillisSecond / animMaxValue
        }
    }
    return pointerInput(this) {
        awaitEachGesture {
            awaitFirstDown()
            val downTime = System.currentTimeMillis()
            pressCallback(true)
            val job = if (isRunning.value) {
                scope.launch {
                    while (isRunning.value) {
                        anim.stop()
                        anim.snapTo(0f)
                        anim.animateTo(animMaxValue / 8f, tween((animMaxValue / 8f * perMS).toInt(), easing = LinearEasing)) {
                            if (this.value == this.targetValue) {
                                block(false, false)
                            }
                        }
                    }
                }
            } else {
                null
            }
            val up = waitForUpOrCancellation()
            pressCallback(false)
            job?.cancel()
            //根据间隔判断是否为一次点击事件
            val isTapEvent = System.currentTimeMillis() - downTime < 500

            if (up != null) {
                scope.launch {
                    anim.stop()
                    if (isTapEvent) {
                        if (!block(true, false)) {
                            return@launch
                        }
                    }
//                    else{
//                        anim.snapTo(0f)
//                    }
                    if (!isRunning.value) {
                        isRunning.value = isTapEvent
                    }

                    anim.animateTo(animMaxValue.toFloat(), tween(maxMillisSecond - (anim.value * perMS).roundToInt(), easing = LinearEasing)) {
                        if (this.value == this.targetValue) {
                            scope.launch {
                                anim.snapTo(0f)
                                isRunning.value = false
                                block(false, true)
                            }
                        }
                    }
                }
            }
        }
    }
}