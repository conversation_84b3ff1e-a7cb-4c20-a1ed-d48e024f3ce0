package com.qyqy.cupid.ui


import android.content.Context
import android.net.Uri
import android.os.Bundle
import android.os.Parcelable
import android.os.SystemClock
import androidx.collection.MutableScatterMap
import androidx.collection.mutableScatterMapOf
import androidx.compose.animation.AnimatedContentScope
import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.MutableIntState
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavDeepLink
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.NavOptions
import androidx.navigation.Navigator
import androidx.navigation.compose.composable
import com.qyqy.cupid.event.AppActionEvent
import com.qyqy.cupid.event.AppDialogEvent
import com.qyqy.cupid.event.AppLinkEvent
import com.qyqy.cupid.im.panel.gift.GiftPosition
import com.qyqy.cupid.ui.NavResult.Companion.RESULT_CANCEL
import com.qyqy.cupid.ui.NavResult.Companion.RESULT_DEFAULT
import com.qyqy.cupid.ui.NavResult.Companion.RESULT_OK
import com.qyqy.cupid.ui.home.HomeSubPage
import com.qyqy.cupid.ui.home.message.C2CChatConst
import com.qyqy.cupid.utils.FlowEventBus
import com.qyqy.cupid.utils.eventbus.EventBus
import com.qyqy.cupid.utils.eventbus.StickyEvent
import com.qyqy.cupid.widgets.webview.WebDialog
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.app
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.component.WebFrameInfo
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.map.Container
import com.qyqy.ucoo.utils.LogUtils
import com.qyqy.ucoo.utils.logW
import com.qyqy.ucoo.utils.takeIsNotEmpty
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.parcelize.Parcelize
import java.io.Serializable
import java.net.URLDecoder
import java.util.UUID


private const val suffix = "/{argumentId}"

val LocalAppNavController = staticCompositionLocalOf<AppNavController> {
    error("")
}

@Stable
class AppNavController(
    val composeNav: NavHostController,
    val rootArguments: MutableScatterMap<String, NavArgument>,
    val scope: CoroutineScope,
) {

    lateinit var startDestination: String
        private set

    lateinit var startDestinationKey: String
        private set

    fun navigateWeb(
        url: String,
        navigatorExtras: Navigator.Extras? = null,
        navOptions: NavOptions? = null,
    ) {
        navigate(CupidRouters.Web, mapOf("url" to url), navigatorExtras, navOptions)
    }

    fun bindStartDestination(
        startDestination: String,
        arguments: Map<String, Any>? = null,
    ) {
        val key = UUID.randomUUID().toString()
        val routeArguments = rootArguments.getOrPut(key) {
            NavArgument(mutableScatterMapOf())
        }
        arguments?.apply {
            routeArguments.putArguments(this)
        }
        startDestinationKey = key
        this.startDestination = navKey(startDestination, key)
    }

    /**
     * 导航到指定目的地
     *
     * @param name 目的地名称
     * @param arguments 携带参数
     * mode = 1时会检查当前回退栈是否存在,如果已经存在则直接服用
     *
     * @param navigatorExtras
     * @param navOptions
     * @return
     */
    fun navigate(
        name: String,
        arguments: Map<String, Any>? = null,
        navigatorExtras: Navigator.Extras? = null,
        navOptions: NavOptions? = null,
    ): Boolean {
        LogUtils.i("event-cpd-navigate", "route:%s  params:%s", name, arguments ?: "null")
        if ((arguments?.get("mode") ?: "0") == "1") {
            try {
                val realRoute = navRoute(name)
                composeNav.getBackStackEntry(realRoute)
                return popBackStack(name, false)
            } catch (e: Exception) {
                if (e.message?.startsWith("No destination") == true) {
                    //栈内没有对应的页面
                } else {
                    //栈内有对应的页面, 但是弹出失败了
                    logW("launchSingleTask failed. %s", e)
                }
            }
        }
        return navigate(DestinationRoute(name, arguments, navigatorExtras, navOptions))
    }

    /**
     * 导航到指定目的地
     *
     * @param destination 目的地
     * @return 是否导航成功
     */
    fun navigate(destination: DestinationRoute): Boolean {
        val result = composeNav.navigate(rootArguments, destination)
        composeNav.currentBackStackEntry?.let {
            FlowEventBus.initWithComposeNavigation(it)
        }
        return result == 1
    }

    /**
     * 从navigation中弹出一个页面
     *
     * @return 是否弹出成功
     */
    fun popBackStack(): Boolean {
        return popBackStackWithResult(null, "")
    }

    /**
     * 从navigation中弹出一个页面(携带参数版本)
     *
     * @param route 弹出页面的目的地
     * @param inclusive 是否弹出route 本身
     * @param saveState 保存状态?
     * @return 是否弹出成功
     */
    fun popBackStack(route: String, inclusive: Boolean = false, saveState: Boolean = false): Boolean {
        return popBackStackWithResult(route, "", null, inclusive, saveState)
    }

    /**
     * 获取上一个页面 路径
     */
    fun getPrevRoute() = composeNav.previousBackStackEntry?.destination?.route

    /**
     * 当前回退栈是否包含指定页面?
     * @param route 指定页面路径
     * @return 是否包含
     */
    fun contains(route: String): Boolean {
        return try {
            composeNav.getBackStackEntry(navRoute(route))
            true
        } catch (e: IllegalArgumentException) {
            false
        }
    }

    //region 可监听返回结果
    fun navigateForResult(
        name: String,
        requestKey: String = NavResult.DEFAULT_KEY,
        arguments: Map<String, Any>? = null,
        navigatorExtras: Navigator.Extras? = null,
        navOptions: NavOptions? = null,
        context: Context? = null,
    ): StateFlow<NavResult?> {
        val arg = (arguments?.toMutableMap()?.apply { put("request_key", requestKey) }) ?: mapOf(
            "request_key" to requestKey
        )
        val stateHandle = composeNav.currentBackStackEntry!!.savedStateHandle
        navigate(DestinationRoute(name, arg, navigatorExtras, navOptions))
        return stateHandle.getStateFlow(
            requestKey,
            NavResult(requestKey, Bundle.EMPTY, RESULT_DEFAULT)
        )
    }

    fun popBackStackWithResult(
        route: String? = null,
        requestKey: String = NavResult.DEFAULT_KEY,
        result: Bundle? = null,
        inclusive: Boolean = true,
        saveState: Boolean = false,
    ): Boolean {
        //1. 获取上一个实例的savedStateHandle
        val stateHandle = this.composeNav.previousBackStackEntry?.savedStateHandle

        val navRet = if (route == null) {
            this.composeNav.popBackStack()
        } else {
            this.composeNav.popBackStack(navRoute(route), inclusive, saveState)
        }

        LogUtils.i("event-popback", "回退至 %s ", route ?: (this.composeNav.currentBackStackEntry?.destination?.route))

        if (requestKey.isNotBlank()) {
            stateHandle?.set(
                requestKey, if (result == null)
                    NavResult(requestKey, Bundle.EMPTY, RESULT_CANCEL)
                else NavResult(requestKey, result, RESULT_OK)
            )
        }
        return navRet
    }

    @Composable
    fun OnNavigateResult(onResult: (NavResult) -> Unit) {
        val saveStateHandle = this.composeNav.currentBackStackEntry?.savedStateHandle
        saveStateHandle?.keys()?.forEach { key ->
            if (saveStateHandle.get<NavResult>(key) != null) {
                val ret = saveStateHandle.getStateFlow<NavResult>(key, NavResult.DEFAULT_NAV_RESULT).collectAsStateWithLifecycle()
                onResult(ret.value)
                this.composeNav.currentBackStackEntry?.savedStateHandle?.remove<NavResult>(key)
            }
        }
    }

    //endregion

    fun navigateByLink(linkUrl: String) = navigateByLink(linkUrl, null)

    /**
     * 通过link去跳转到指定的compose页面,如果有可能也可以跳转到原生页面
     *
     * @param linkUrl link路径
     * @param context 用于原生跳转的context, 根据情况考虑, 不传时是绝对不会走到原生路径
     */
    fun navigateByLink(linkUrl: String, container: Container?): Boolean {
        LogUtils.d("app_nav", "navigate by link: %s", linkUrl)
        if (linkUrl.startsWith(AppLinkManager.BASE_URL)) {
            // 解析URI字符串
            val uri = Uri.parse(linkUrl)
            val path = uri.path.orEmpty()
            // 获取参数并存入HashMap
            val params = HashMap<String, String>()
            val queryParams = uri.queryParameterNames
            for (key in queryParams) {
                params[key] = uri.getQueryParameter(key) ?: ""
            }
            params["uuid"] = UUID.randomUUID().toString()

            when (path) {

                "/webview" -> {
                    val url = uri.getQueryParameter("url")?.let { URLDecoder.decode(it) }.orEmpty()
                    navigateWeb(url)
                }

                "/web_frame" -> {
                    val json = params["info"]?.let { URLDecoder.decode(it) }.takeIsNotEmpty() ?: return false
                    val info = sAppJson.decodeFromString<WebFrameInfo>(json)
                    val dq = app.dialogQueue

                    dq.push(webDialog(info), true)
                }

                "/link_request" -> {
                }

                "/common" -> {//通用,不跳转页面的action
                    params.get("action")?.let {
                        FlowEventBus.send(AppActionEvent(it, params))
                    }
                }

                "/dialog" -> {
                    params.get("action")?.let {
                        FlowEventBus.send(AppDialogEvent(it, params))
                    }
                }

                "/user_home" -> {
                    params["userid"]?.let {
                        navigate(CupidRouters.PROFILE, mapOf("userId" to it))
                    }
                }

                else -> {
                    return appNavRoute(path, params)
                }
            }
        } else if (linkUrl.startsWith("http")) {
            if (container?.t != 1) {
                navigateWeb(linkUrl)
            } else {
                // 其他类型，好像是弹窗，cupid目前暂时不支持
            }
        } else {
            navigateWeb("https://$linkUrl")
        }
        return true
    }

    fun webDialog(info: WebFrameInfo) = WebDialog(
        url = info.targetUrl,
        alignment = when (info.gravity) {
            "top" -> Alignment.TopCenter
            "bottom" -> Alignment.BottomCenter
            else -> Alignment.Center
        },
        cancelable = info.cancelable,
        modifier = Modifier
            .then(if (info.width == -1) Modifier.fillMaxWidth() else Modifier.width(info.width.dp))
            .then(if (info.height == -1) Modifier.fillMaxHeight() else Modifier.height(info.height.dp))
            .then(info.radius?.let {
                Modifier.clip(
                    RoundedCornerShape(
                        topStart = it.leftTop.dp,
                        topEnd = it.rightTop.dp,
                        bottomEnd = it.rightBottom.dp,
                        bottomStart = it.leftBottom.dp
                    )
                )
            } ?: Modifier)
    )

    private fun appNavRoute(
        path: String,
        params: Map<String, String>? = null,
    ): Boolean {
        var result = false
        if (path == CupidRouters.HOME) {
            // 如果主页不在栈顶，则弹出到栈顶
            if (popBackStack(path)) {
                appCoroutineScope.launch {
                    delay(100) // 需要等待主页可组合项重建
                    appNavRoute(path, params)
                }
                return true
            }
            val stackEntry = composeNav.getBackStackEntry(navRoute(path))
            val key = stackEntry.getArgumentsKey(this)
            params?.get(SUB_ROUTE_PAGE)?.also { route ->
                rootArguments[key]?.getArgument<ISubRouteNav>(SUB_ROUTE_KEY)?.also { subRouteNav ->
                    result = subRouteNav.navigate(stackEntry.lifecycleScope, route, params)
                    LogUtils.i("event-cpd-switchTab", "tab:%s  params:%s", route, (params ?: "null"))
                }
            }
        } else {
            val current = composeNav.currentBackStackEntry
            if (current?.destination?.route?.toCupidRoute() == path) {
                val navArgument = rootArguments[current.getArgumentsKey(this)]
                if (params != null && navArgument != null && matchRoute(path, params, navArgument)) {
                    navArgument.getArgument<ISubRouteNav>(SUB_ROUTE_KEY)?.also { subRouteNav ->
                        val route = params[SUB_ROUTE_PAGE].orEmpty()
                        result = subRouteNav.navigate(current.lifecycleScope, route, params)
                        LogUtils.i("event-cpd-switchTab", "tab:%s  params:%s", route, (params ?: "null"))
                    }
                } else {
                    result = navigate(path, params)
                }
            } else {
                result = navigate(path, params)
            }
        }

        if (result) {
            params?.also {
                FlowEventBus.send(AppLinkEvent(path, params.toMap()))
            }
        }

        return result
    }
    //endregion
}

@Parcelize
data class NavResult(val requestKey: String, val data: Bundle, val responseCode: Int) : Serializable, Parcelable {
    companion object {
        const val RESULT_DEFAULT = -1
        const val RESULT_OK = 0
        const val RESULT_CANCEL = 1
        const val RESULT_FAIL = 2
        val DEFAULT_KEY = "defaultResultKey"

        val DEFAULT_NAV_RESULT = NavResult("", Bundle.EMPTY, RESULT_DEFAULT)
    }
}

fun navRoute(route: String) = "$route$suffix"

fun String.toCupidRoute() = removeSuffix(suffix)

private fun navKey(route: String, key: String) = "$route/$key"

fun NavGraphBuilder.composableX(
    route: String,
    deepLinks: List<NavDeepLink> = emptyList(),
    enterTransition: (@JvmSuppressWildcards
    AnimatedContentTransitionScope<NavBackStackEntry>.() -> EnterTransition?)? = null,
    exitTransition: (@JvmSuppressWildcards
    AnimatedContentTransitionScope<NavBackStackEntry>.() -> ExitTransition?)? = null,
    popEnterTransition: (@JvmSuppressWildcards
    AnimatedContentTransitionScope<NavBackStackEntry>.() -> EnterTransition?)? =
        enterTransition,
    popExitTransition: (@JvmSuppressWildcards
    AnimatedContentTransitionScope<NavBackStackEntry>.() -> ExitTransition?)? =
        exitTransition,
    content: @Composable AnimatedContentScope.(NavArgumentBackStackEntry) -> Unit,
) {
    composable(
        route = navRoute(route),
        deepLinks = deepLinks,
        enterTransition = enterTransition,
        exitTransition = exitTransition,
        popEnterTransition = popEnterTransition,
        popExitTransition = popExitTransition,
    ) {
        val controller = LocalAppNavController.current
        val rootArguments = controller.rootArguments
        val key = it.getArgumentsKey(controller)
        val navArgumentBackStackEntry = NavArgumentBackStackEntry(
            key = key,
            backStackEntry = it,
            navArgument = rootArguments[key]
        )
        DisposableEffect(key1 = Unit) {
            if (rootArguments[key] == null) {
                rootArguments[key] = NavArgument(mutableScatterMapOf())
            }
            onDispose {
                rootArguments.remove(key = key)
            }
        }
        content(navArgumentBackStackEntry)
    }
}


data class DestinationRoute(
    val name: String,
    val arguments: Map<String, Any>? = null,
    val navigatorExtras: Navigator.Extras? = null,
    val navOptions: NavOptions? = null,
)

fun NavHostController.navigate(
    rootArguments: MutableScatterMap<String, NavArgument>,
    destination: DestinationRoute,
): Int {
    val key = UUID.randomUUID().toString()
    val routeArguments = rootArguments.getOrPut(key) {
        NavArgument(mutableScatterMapOf())
    }
    destination.arguments?.apply {
        routeArguments.putArguments(this)
    }
    return try {
        navigate(navKey(destination.name, key), destination.navOptions, destination.navigatorExtras)
        1
    } catch (e: IllegalArgumentException) {
        if (e.message?.contains("cannot be found in the") == true) {
            //如果是找不到这个页面，则返回false
            0
        } else {
            -1
        }
    }
}

/**
 * 个人主页
 */
fun profileDestination(userId: String) = DestinationRoute(CupidRouters.PROFILE, arguments = mapOf("userId" to userId))

fun AppNavController.navigateToProfile(userId: String) {
    navigate(profileDestination(userId))
}


private const val SUB_ROUTE_PAGE = "sub_route_page"

private const val SUB_ROUTE_KEY = "sub_route_key"

interface ISubRouteNav {

    val current: String?
        get() = null

    fun navigate(scope: CoroutineScope, route: String, arguments: Map<String, String>? = null): Boolean
}

fun NavBackStackEntry.getSubRouteNav(controller: AppNavController): ISubRouteNav? {
    val key = getArgumentsKey(controller)
    return controller.rootArguments[key]?.getArgument<ISubRouteNav>(SUB_ROUTE_KEY)
}


fun NavBackStackEntry.getArgumentsKey(controller: AppNavController): String {
    return arguments?.getString("argumentId") ?: controller.startDestinationKey
}

private fun matchRoute(route: String, params: Map<String, String>, navArgument: NavArgument): Boolean {
    return when (route) {
        CupidRouters.C2CChat -> {
            val id = params["userId"]
            id == navArgument.getArgument<String>("userId") || id == navArgument.getArgument<AppUser>("user")?.id
        }

        else -> false
    }
}


fun NavGraphBuilder.navigationX(
    route: String,
    deepLinks: List<NavDeepLink> = emptyList(),
    enterTransition: (@JvmSuppressWildcards
    AnimatedContentTransitionScope<NavBackStackEntry>.() -> EnterTransition?)? = null,
    exitTransition: (@JvmSuppressWildcards
    AnimatedContentTransitionScope<NavBackStackEntry>.() -> ExitTransition?)? = null,
    popEnterTransition: (@JvmSuppressWildcards
    AnimatedContentTransitionScope<NavBackStackEntry>.() -> EnterTransition?)? = enterTransition,
    popExitTransition: (@JvmSuppressWildcards
    AnimatedContentTransitionScope<NavBackStackEntry>.() -> ExitTransition?)? = exitTransition,
    content: @Composable AnimatedContentScope.(NavArgumentBackStackEntry, String) -> ISubRouteNav,
) {
    composableX(
        route = route,
        deepLinks = deepLinks,
        enterTransition = enterTransition,
        exitTransition = exitTransition,
        popEnterTransition = popEnterTransition,
        popExitTransition = popExitTransition
    ) {
        val subRouteState = content(it, it.getArgumentOrDefault(SUB_ROUTE_PAGE) {
            ""
        })
        val rootArguments = LocalAppNavController.current.rootArguments
        val key = it.key
        SideEffect {
            val routeArguments = rootArguments.getOrPut(key) {
                NavArgument(mutableScatterMapOf())
            }
            routeArguments.putArguments(mapOf(SUB_ROUTE_KEY to subRouteState))
        }
    }
}

@Stable
sealed class SubRouteNavImpl : ISubRouteNav {

    class Home(
        private val subPages: List<HomeSubPage>,
        private val currentTState: MutableIntState,
        private val eventBus: EventBus,
    ) : SubRouteNavImpl() {
        override val current: String
            get() = currentTState.intValue.toString()

        override fun navigate(scope: CoroutineScope, route: String, arguments: Map<String, String>?): Boolean {
            var willGoPage = subPages.find { it.t.toString() == route }
            if (willGoPage == null) {
                // 路由兼容，金币与收益页面合并
                if (route == HomeSubPage.Gold.t.toString() || route == HomeSubPage.Income.t.toString()) {
                    willGoPage = subPages.find { it.t.toString() == HomeSubPage.NewCoin.t.toString() }
                }
                if (willGoPage != null) { // 定位到金币或者收益页面
                    eventBus.addStickyEvent(NavListener.NavKey, "${NavListener.HOME_SUB_NAV}$route", true)
                }
            }
            if (willGoPage != null) {
                arguments?.get("action")?.takeIf { it.isNotEmpty() }?.also {
                    scope.launch {
                        delay(200)
                        eventBus.addStickyEvent(NavListener.NavKey, "${NavListener.HOME_SUB_NAV}$it", true)
                    }
                }
            }
            return if (willGoPage != null) {
                currentTState.intValue = willGoPage.t
                true
            } else {
                false
            }
        }
    }

    class C2C(val effects: MutableSharedFlow<Any>) : SubRouteNavImpl() {

        override fun navigate(scope: CoroutineScope, route: String, arguments: Map<String, String>?): Boolean {
            val action = arguments?.get("action")?.toIntOrNull()
            if (action != null) {
                scope.launch {
                    if (action == C2CChatConst.ACTION_SHOW_GIFT) {
                        val position = arguments["position"]
                        if (!position.isNullOrEmpty()) {
                            withContext(Dispatchers.IO) {
                                sAppJson.decodeFromString<GiftPosition?>(position)
                            }?.also {
                                effects.emit(it)
                                return@launch
                            }
                        }
                    }
                    effects.emit(action)
                }
            }
            return true
        }

    }

    object Empty : ISubRouteNav {

        override fun navigate(scope: CoroutineScope, route: String, arguments: Map<String, String>?): Boolean {
            return false
        }
    }

}

sealed interface FloatStatus {

    /**
     * @param adaptive 是否是自适应收起，自动操作，不是用户主动收起
     */
    data class Collapsed(val adaptive: Boolean) : FloatStatus

    data object Expanded : FloatStatus
}

abstract class NavListener(private val matchPrefix: String) : EventBus.Listener {

    companion object NavKey : EventBus.Key<NavListener> {
        const val HOME_SUB_NAV = "home/sub/nav/"
    }

    override val key: EventBus.Key<*> = NavKey


    final override fun handleStickyEvent(sticky: StickyEvent): Boolean {
        if (SystemClock.elapsedRealtime().minus(sticky.time) >= 10_000) { // 超时路由不予处理
            return true
        }
        val path = sticky.event
        if (path.isEmpty()) {
            return true
        }
        if (path.startsWith(matchPrefix)) {
            return handleNav(path.substring(matchPrefix.length))
        }
        return false
    }

    abstract fun handleNav(route: String): Boolean

}

abstract class GiftListener : EventBus.Listener {

    companion object Key : EventBus.Key<GiftListener>

    final override val key: EventBus.Key<*> = Key

    abstract fun showGiftPanel(giftPosition: GiftPosition?)
}