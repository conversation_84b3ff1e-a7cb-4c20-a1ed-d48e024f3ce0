package com.qyqy.cupid.ui

import android.os.Parcelable
import androidx.collection.MutableScatterMap
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisallowComposableCalls
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.Stable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.Saver
import androidx.compose.runtime.saveable.autoSaver
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.navigation.NavBackStackEntry
import com.qyqy.ucoo.http.sAppJson

@Stable
class NavArgument(
    val params: MutableScatterMap<String, Any>,
) {

    fun putArguments(arguments: Map<String, Any>) = params.putAll(arguments)

    inline fun <reified T> getArgument(key: String): T? {
        val value = params[key] ?: return null
        if (value is T) {
            return value
        }
        return if (value is String) {
            when (T::class) {
                Int::class -> {
                    value.toIntOrNull() as? T?
                }

                Float::class -> {
                    value.toFloatOrNull() as? T?
                }

                Boolean::class -> {
                    try {
                        value.toBoolean()
                    } catch (e: Exception) {
                        null
                    } as? T?
                }

                else -> {
                    try {
                        sAppJson.decodeFromString<T>(value)
                    } catch (e: Exception) {
                        null
                    }
                }
            }
        } else {
            if (T::class == String::class) {
                value.toString() as? T?
            } else {
                null
            }
        }
    }

}

@Stable
data class NavArgumentBackStackEntry(
    val key: String,
    val backStackEntry: NavBackStackEntry,
    val navArgument: NavArgument?,
) {

    @Composable
    inline fun <reified T : Any> getArgument(key: String, saver: Saver<T, out Any>? = autoSaver()): T = if (saver == null) {
        remember(key) {
            navArgument!!.getArgument<T>(key = key) as T
        }
    } else {
        rememberSaveable(key, saver) {
            navArgument!!.getArgument<T>(key = key) as T
        }
    }

    @Composable
    inline fun <reified T : Any> getArgumentOrDefault(
        key: String,
        saver: Saver<T, out Any>? = autoSaver(),
        crossinline default: @DisallowComposableCalls () -> T,
    ): T = if (saver == null) {
        remember {
            navArgument!!.getArgument<T>(key = key) ?: default()
        }
    } else {
        rememberSaveable(key, saver) {
            navArgument!!.getArgument<T>(key = key) ?: default()
        }
    }

    @Composable
    inline fun <reified T : Any> getArgumentOrNull(
        key: String,
    ): T? = remember {
        navArgument!!.getArgument<T>(key = key)
    }
}

