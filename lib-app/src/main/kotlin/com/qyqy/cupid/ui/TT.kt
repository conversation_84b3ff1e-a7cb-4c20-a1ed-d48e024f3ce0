package com.qyqy.cupid.ui

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.GenericShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.RoundRect
import androidx.compose.ui.geometry.toRect
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Outline
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.addOutline
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.data.DurationTask
import com.qyqy.cupid.data.DurationTaskInfo
import com.qyqy.cupid.data.Task
import com.qyqy.cupid.ui.call.CallTimer
import com.qyqy.cupid.ui.call.formatMillisecondsToTimeString
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.home.HomeSubPage
import com.qyqy.cupid.ui.relations.family.FamilySquareAction
import com.qyqy.cupid.widgets.AlignHorizontalContainer
import com.qyqy.ucoo.R
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.multilingual.currentLocale
import com.qyqy.ucoo.utils.OnClick


@Composable
private fun DurationTaskDialogContent(taskInfo: List<DurationTaskInfo>, onClose: OnClick = {}) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color(0xFFFF4D62), shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .padding(bottom = 50.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(44.dp)
        ) {
            Text(text = stringResource(id = R.string.cpd时长任务), modifier = Modifier.align(Alignment.Center), color = Color.White, fontSize = 18.sp)
            IconButton(
                onClick = onClose, modifier = Modifier
                    .padding(end = 8.dp)
                    .align(Alignment.CenterEnd)
            ) {
                Icon(painter = painterResource(id = R.drawable.ic_cpd_close), contentDescription = "close", tint = Color.White)
            }
        }

        taskInfo.forEach {
            if (it.taskInfo.seriesType == 2) {
                AdvancedDurationTaskItem(taskInfo = it.taskInfo, onClose = onClose)
            } else if (it.taskInfo.seriesType == 4) {
                OnlineDurationTaskItem(taskInfo = it.taskInfo)
            }
        }
    }
}


@Composable
private fun AdvancedDurationTaskItem(taskInfo: DurationTask, onClose: OnClick = {}) {
    Column(
        modifier = Modifier
            .padding(top = 12.dp, start = 16.dp, end = 16.dp)
            .heightIn(max = 300.dp)
            .background(color = Color.White, shape = Shapes.small)
            .verticalScroll(rememberScrollState())
            .padding(bottom = 12.dp, start = 12.dp, end = 12.dp),
        verticalArrangement = Arrangement.spacedBy(6.dp)
    ) {

        TaskTimer(taskInfo.seriesTitle, stringResource(id = R.string.cpd累计时长), taskInfo.pausedAdvanced, taskInfo.timerElapsedRealtime)

        taskInfo.tasks.forEachIndexed { index, it ->
            if (index != 0) {
                HorizontalDivider(modifier = Modifier.padding(top = 5.dp, bottom = 10.dp), thickness = 0.3.dp, color = Color.Gray.copy(0.3f))
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {

                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = it.title,
                        modifier = Modifier.basicMarquee(Int.MAX_VALUE),
                        color = Color(0xFF3A1920),
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        ComposeImage(
                            model = it.extra.prizeIcon,
                            preview = painterResource(id = R.drawable.ic_cpd_diamond),
                            modifier = Modifier.size(18.dp)
                        )

                        Text(
                            text = it.prize,
                            modifier = Modifier
                                .padding(start = 3.dp)
                                .basicMarquee(Int.MAX_VALUE),
                            color = Color(0xFFFF5E8B),
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                        )
                    }
                }

                Spacer(modifier = Modifier.width(8.dp))

                AppButton(
                    text = if (it.finished) stringResource(id = R.string.cpd已完成) else stringResource(id = R.string.cpd去完成),
                    modifier = Modifier
                        .size(72.dp, 28.dp),
                    background = Color(0xFFFF5E8B),
                    color = Color.White,
                    contentPadding = PaddingValues(horizontal = 4.dp),
                    enabled = !it.finished,
                    onClick = {
                        AppLinkManager.controller?.navigateByLink("${AppLinkManager.BASE_URL}/home?sub_route_page=${HomeSubPage.Family.t}&action=${FamilySquareAction.SHOW_AUDIO_CHANNEL}")
                        onClose()
                    },
                )
            }
        }
    }
}

@Composable
private fun OnlineDurationTaskItem(taskInfo: DurationTask) {
    Column(
        modifier = Modifier
            .padding(top = 16.dp, start = 16.dp, end = 16.dp)
            .background(Color.White, shape = Shapes.small)
            .padding(horizontal = 12.dp)
    ) {

        val l = taskInfo.tasks
        val currentItem = taskInfo.tasks.lastOrNull()

        var c by remember(l) {
            mutableIntStateOf(currentItem?.progressValue?.div(60) ?: 0)
        }

        TaskTimer(taskInfo.seriesTitle, stringResource(id = R.string.cpd累计在线), taskInfo.pausedAdvanced, taskInfo.timerElapsedRealtime) {
            c = it.div(60_000).toInt()
        }

        if (currentItem == null) {
            return
        }

        val rewardShape = with(LocalDensity.current) {
            remember {
                val radius = 4.dp.toPx()
                val caretLeft = 30.dp.toPx()
                val caretWidth = 12.dp.toPx()
                val caretHeight = 6.dp.toPx()
                GenericShape { size, layoutDirection ->
                    val rectH = size.height.minus(caretHeight)
                    addOutline(
                        Outline.Rounded(
                            RoundRect(
                                rect = size.copy(height = rectH).toRect(),
                                topLeft = CornerRadius(if (layoutDirection == androidx.compose.ui.unit.LayoutDirection.Ltr) radius else radius),
                                topRight = CornerRadius(if (layoutDirection == androidx.compose.ui.unit.LayoutDirection.Ltr) radius else radius),
                                bottomRight = CornerRadius(if (layoutDirection == androidx.compose.ui.unit.LayoutDirection.Ltr) radius else radius),
                                bottomLeft = CornerRadius(if (layoutDirection == androidx.compose.ui.unit.LayoutDirection.Ltr) radius else radius)
                            )
                        )
                    )
                    moveTo(caretLeft, rectH)
                    lineTo(caretLeft.plus(caretWidth.div(2)), rectH.plus(caretHeight))
                    lineTo(caretLeft.plus(caretWidth), rectH)
                }
            }
        }

        val f1 = 16 / 52f
        val f2 = 36 / 107.5f
        val f3 = 35.5f / 107.5f

        val list by remember(l) {
            derivedStateOf {
                buildList {
                    var curValue = 0f
                    l.forEachIndexed { index, item ->
                        val value = item.conditionMinute
                        if (index == 0) {
                            curValue += (value - 0) * f1
                            add(TaskProgress.SidesSpace((c / curValue).coerceAtMost(1f), RoundedCornerShape(topStart = 12.dp, bottomStart = 12.dp)))
                        } else {
                            val lastItem = l[index - 1]
                            var oldValue = curValue
                            curValue += (value - lastItem.conditionMinute) * f2
                            if (c >= oldValue) {
                                if (c <= curValue) {
                                    add(TaskProgress.Item(lastItem, (c - oldValue) / (curValue - oldValue) / 2 + 0.5f))
                                } else {
                                    add(TaskProgress.Item(lastItem, 1f))
                                }
                            }

                            oldValue = curValue
                            curValue += (value - lastItem.conditionMinute) * f3
                            if (c > oldValue) {
                                add(TaskProgress.GapSpace(((c - oldValue) / (curValue - oldValue)).coerceAtMost(1f)))
                            } else {
                                add(TaskProgress.GapSpace(0f))
                            }
                        }

                        val oldValue = curValue
                        curValue = value.toFloat()
                        if (c < curValue) {
                            add(TaskProgress.Item(item, (c - oldValue) / (curValue - oldValue) / 2))
                        } else if (index >= l.lastIndex) {
                            add(TaskProgress.Item(item, 1f))
                        }
                    }

                    add(TaskProgress.SidesSpace(if (c >= curValue) 1f else 0f, RoundedCornerShape(topEnd = 12.dp, bottomEnd = 12.dp)))
                }
            }
        }

        LazyRow(modifier = Modifier.padding(top = 5.dp, bottom = 12.dp)) {
            items(
                items = list,
                contentType = {
                    it is TaskProgress.ISpace
                },
            ) {
                when (it) {
                    is TaskProgress.SidesSpace -> {
                        TaskItem2(16.dp, it.fraction, it.shape)
                    }

                    is TaskProgress.GapSpace -> {
                        TaskItem2(35.5.dp, it.fraction)
                    }

                    is TaskProgress.Item -> {
                        TaskItem1(rewardShape, it.value, it.fraction)
                    }
                }
            }
        }
    }
}

@Composable
private fun TaskTimer(title: String, timerPrefix: String, paused: Boolean, elapsedRealtime: Long, onTimer: (Long) -> Unit = {}) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(40.dp),
    ) {
        Text(
            text = title,
            modifier = Modifier.align(Alignment.CenterStart),
            color = Color(0xFF1D2129),
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium
        )


        if (elapsedRealtime != -1L) {
            val locale = LocalConfiguration.current.currentLocale
            if (paused) {
                Text(
                    text = "$timerPrefix${elapsedRealtime.formatMillisecondsToTimeString(locale)}",
                    fontSize = 14.sp,
                    color = Color(0xFFF53F3F),
                    modifier = Modifier.align(Alignment.CenterEnd)
                )
            } else {
                CallTimer(isCountdown = false, elapsedRealtime = elapsedRealtime) {
                    Text(
                        text = "$timerPrefix${it.formatMillisecondsToTimeString(locale)}",
                        fontSize = 14.sp,
                        color = Color(0xFFF53F3F),
                        modifier = Modifier.align(Alignment.CenterEnd)
                    )
                    onTimer(it)
                }
            }
        }
    }
}


@Composable
private fun TaskItem1(
    rewardShape: Shape,
    task: Task,
    fraction: Float,
    shape: Shape = RectangleShape,
) {
    Column(modifier = Modifier.width(72.dp)) {
        Row(
            modifier = Modifier
                .padding(bottom = 4.dp)
                .size(width = 72.dp, height = 42.dp)
                .background(color = if (fraction >= 0.5f) Color(0xFFFFE7EE) else Color(0xFFF6F6F6), shape = rewardShape)
                .padding(bottom = 4.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {

            ComposeImage(
                model = task.extra.prizeIcon,
                preview = painterResource(id = R.drawable.ic_cpd_diamond),
                modifier = Modifier.size(18.dp)
            )

            Text(
                text = task.prize,
                modifier = Modifier
                    .padding(start = 3.dp)
                    .basicMarquee(Int.MAX_VALUE),
                color = Color(0xFFFF5E8B),
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
            )
        }

        Box(
            modifier = Modifier
                .size(72.dp, 16.dp)
                .padding(vertical = 4.dp)
                .background(color = Color(0xFFFFE7EE)),
            contentAlignment = Alignment.CenterStart
        ) {
            Spacer(
                modifier = Modifier
                    .animateContentSize()
                    .fillMaxWidth(fraction)
                    .height(8.dp)
                    .background(color = Color(0xFFFF5E8B), shape = shape)
            )
            Image(
                painter = painterResource(
                    id = if (fraction >= 0.5f) {
                        R.drawable.ic_cpd_checked_2
                    } else {
                        R.drawable.ic_cpd_unchecked_2
                    }
                ),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.Center)
                    .requiredSize(16.dp)
            )
        }

        Text(
            text = task.desc,
            modifier = Modifier
                .padding(top = 5.dp)
                .align(Alignment.CenterHorizontally),
            color = Color(0xFF86909C),
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
        )
    }

}

@Composable
private fun TaskItem2(width: Dp, fraction: Float, shape: Shape = RectangleShape) {
    Column {
        Spacer(
            modifier = Modifier
                .padding(bottom = 4.dp)
                .height(height = 42.dp),
        )

        Box(
            modifier = Modifier
                .size(width = width, height = 16.dp)
                .padding(vertical = 4.dp)
                .clip(shape)
                .background(color = Color(0xFFFFE7EE)),
            contentAlignment = Alignment.CenterStart
        ) {
            Spacer(
                modifier = Modifier
                    .animateContentSize()
                    .fillMaxWidth(fraction)
                    .height(8.dp)
                    .background(color = Color(0xFFFF5E8B))
            )
        }
    }
}

private sealed interface TaskProgress {

    val fraction: Float

    sealed interface ISpace : TaskProgress

    data class SidesSpace(
        override val fraction: Float,
        val shape: Shape,
    ) : ISpace

    data class GapSpace(
        override val fraction: Float,
    ) : ISpace

    data class Item(
        val value: Task,
        override val fraction: Float,
    ) : TaskProgress
}

@Preview(widthDp = 375)
@Composable
private fun PreviewDurationTaskDialogContent() {
    DurationTaskDialogContent(
        listOf(
            DurationTaskInfo(
                DurationTask(
                    seriesType = 4, seriesTitle = "时长任务", tasks = listOf(
                        Task(conditionTimes = 3600, progressValue = 500, desc = "60分钟", prize = "+100"),
                        Task(conditionTimes = 7200, progressValue = 500, desc = "120分钟", prize = "+100"),
                        Task(conditionTimes = 14400, progressValue = 500, desc = "180分钟", prize = "+100"),
                        Task(conditionTimes = 30000, progressValue = 500, desc = "500分钟", prize = "+100"),
                    )
                )
            ),
            DurationTaskInfo(
                DurationTask(
                    seriesType = 2, seriesTitle = "进阶任务", tasks = listOf(
                        Task(conditionTimes = 3600, progressValue = 500, title = "语音房累计上麦时长达到10分钟", desc = "60分钟", prize = "+100"),
                        Task(conditionTimes = 7200, progressValue = 500, title = "语音房累计上麦时长达到10分钟", desc = "120分钟", prize = "+100"),
                        Task(conditionTimes = 14400, progressValue = 500, title = "语音房累计上麦时长达到10分钟", desc = "180分钟", prize = "+100"),
                        Task(conditionTimes = 30000, progressValue = 500, title = "语音房累计上麦时长达到10分钟", desc = "500分钟", prize = "+100"),
                    )
                )
            )
        )
    )
}

@Composable
fun DurationTaskFloat(
    taskInfo: List<DurationTaskInfo>?,
    modifier: Modifier,
) {
    val visible = !taskInfo.isNullOrEmpty()
    AlignHorizontalContainer(
        visible = visible,
        modifier = modifier,
        tag = "DurationTaskFloat"
    ) {
        val dialogQueue = LocalDialogQueue.current
        DurationTaskFloatContent(taskInfo = taskInfo.orEmpty(), modifier = Modifier.noEffectClickable(enabled = visible) {
            dialogQueue.push { dialog, _ ->
                DurationTaskDialogContent(taskInfo = taskInfo.orEmpty()) {
                    dialog.dismiss()
                }
            }
        })
    }
}


@Composable
private fun DurationTaskFloatContent(taskInfo: List<DurationTaskInfo>, modifier: Modifier = Modifier) {
    val locale = LocalConfiguration.current.currentLocale
    val itemList = taskInfo.find { it.taskInfo.seriesType == 4 }
    Column(
        modifier = Modifier
            .size(68.dp, 90.dp)
            .background(Color(0xFFFF4D62), Shapes.corner12)
            .then(modifier),
    ) {
        AutoSizeText(
            text = itemList?.taskInfo?.seriesTitle.orEmpty().ifEmpty { "时长任务" },
            modifier = Modifier
                .padding(top = 5.dp, start = 4.dp, end = 4.dp)
                .align(Alignment.CenterHorizontally),
            color = Color.White,
            fontSize = 12.sp,
            lineHeight = 14.sp,
            fontWeight = FontWeight.SemiBold,
            alignment = Alignment.Center,
            maxLines = 2,
        )

        Spacer(modifier = Modifier.weight(1f))

        if (itemList != null && !itemList.taskInfo.todayFinished && itemList.taskInfo.tasks.isNotEmpty()) {
            val item = itemList.taskInfo.tasks.find { !it.finished }
            if (item != null) {
                Column(
                    modifier = Modifier
                        .align(Alignment.CenterHorizontally)
                        .padding(bottom = 4.dp)
                        .size(60.dp, 48.dp)
                        .background(
                            brush = Brush.verticalGradient(listOf(Color(0xFFFDFFFB), Color(0xFFF8E1E3))),
                            shape = Shapes.small
                        ),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.SpaceEvenly
                ) {
                    Row(
                        modifier = Modifier,
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center,
                    ) {
                        CallTimer(isCountdown = true, elapsedRealtime = item.countDownElapsedRealtime) {
                            val time = it.formatMillisecondsToTimeString(locale).split(":")
                            Box(
                                modifier = Modifier
                                    .size(18.dp)
                                    .background(Color(0xFF1D2129), Shapes.extraSmall)
                                    .padding(horizontal = 1.dp)
                            ) {
                                AutoSizeText(
                                    text = time[0],
                                    modifier = Modifier.align(Alignment.Center),
                                    color = Color.White,
                                    fontSize = 12.sp,
                                    lineHeight = 12.sp,
                                    fontWeight = FontWeight.SemiBold
                                )
                            }

                            Text(
                                text = ":",
                                modifier = Modifier.padding(start = 3.dp, end = 3.dp, bottom = 2.dp),
                                color = Color(0xFF1D2129),
                                fontSize = 12.sp,
                                lineHeight = 12.sp,
                                fontWeight = FontWeight.SemiBold
                            )

                            Box(
                                modifier = Modifier
                                    .size(18.dp)
                                    .background(Color(0xFF1D2129), Shapes.extraSmall)
                            ) {
                                Text(
                                    text = time[1],
                                    modifier = Modifier.align(Alignment.Center),
                                    color = Color.White,
                                    fontSize = 12.sp,
                                    lineHeight = 12.sp,
                                    fontWeight = FontWeight.SemiBold
                                )
                            }
                        }
                    }

                    Row(
                        modifier = Modifier,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        ComposeImage(
                            model = item.extra.prizeIcon,
                            preview = painterResource(id = R.drawable.ic_cpd_diamond),
                            modifier = Modifier.size(12.dp)
                        )

                        Text(
                            text = item.prize,
                            modifier = Modifier
                                .padding(start = 3.dp)
                                .basicMarquee(Int.MAX_VALUE),
                            color = Color(0xFFFF5E8B),
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            lineHeight = 12.sp
                        )
                    }
                }
                return@Column
            }
        }

        Image(
            painter = painterResource(id = R.drawable.ic_cpd_task_finished),
            contentDescription = null,
            modifier = Modifier
                .align(Alignment.CenterHorizontally)
                .size(24.dp),
        )

        Text(
            text = stringResource(id = R.string.cpd今日任务已完成),
            modifier = Modifier
                .padding(top = 2.dp, bottom = 4.dp, start = 4.dp, end = 4.dp)
                .align(Alignment.CenterHorizontally),
            fontSize = 11.sp,
            color = Color.White,
            textAlign = TextAlign.Center
        )
    }
}

@Preview
@Composable
private fun PreviewDurationTaskFloat() {
    DurationTaskFloatContent(
        listOf(
            DurationTaskInfo(
                DurationTask(
                    seriesType = 4, seriesTitle = "时长任务", tasks = listOf(
                        Task(conditionTimes = 3600, progressValue = 500, desc = "60分钟", prize = "+100"),
                        Task(conditionTimes = 7200, progressValue = 500, desc = "120分钟", prize = "+100"),
                        Task(conditionTimes = 14400, progressValue = 500, desc = "180分钟", prize = "+100"),
                        Task(conditionTimes = 30000, progressValue = 500, desc = "500分钟", prize = "+100"),
                    )
                )
            ),
            DurationTaskInfo(
                DurationTask(
                    seriesType = 2, seriesTitle = "进阶任务", tasks = listOf(
                        Task(conditionTimes = 3600, progressValue = 500, title = "语音房累计上麦时长达到10分钟", desc = "60分钟", prize = "+100"),
                        Task(conditionTimes = 7200, progressValue = 500, title = "语音房累计上麦时长达到10分钟", desc = "120分钟", prize = "+100"),
                        Task(conditionTimes = 14400, progressValue = 500, title = "语音房累计上麦时长达到10分钟", desc = "180分钟", prize = "+100"),
                        Task(conditionTimes = 30000, progressValue = 500, title = "语音房累计上麦时长达到10分钟", desc = "500分钟", prize = "+100"),
                    )
                )
            )
        )
    )
}