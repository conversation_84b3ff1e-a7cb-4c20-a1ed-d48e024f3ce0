package com.qyqy.cupid.ui.call

import android.view.View
import androidx.compose.runtime.Stable
import com.overseas.common.utils.noOpDelegate
import com.qyqy.cupid.data.C2CCallInfo
import com.qyqy.ucoo.R

data class ActionButtonItem(
    val icon: Int,
    val text: Int,
    val enabled: Boolean = true,
    val onClick: () -> Unit = {},
)

fun C2CCallInfo.getActionButtons(onAction: IVoiceCallingAction) = arrayOf(
    ActionButtonItem(
        icon = if (microphoneEnable) R.drawable.ic_call_on_microphone else R.drawable.ic_call_off_microphone,
        text = if (microphoneEnable) R.string.cpd_microphone_on else R.string.cpd_microphone_off,
        onClick = onAction::toggleMicrophone,
    ),
    ActionButtonItem(
        icon = R.drawable.ic_call_hang_up,
        text = R.string.cpd_hang_up,
        onClick = onAction::hangup,
    ),
    ActionButtonItem(
        icon = if (speakerEnable) R.drawable.ic_call_on_speaker else R.drawable.ic_call_off_speaker,
        text = if (speakerEnable) R.string.cpd_speaker_on else R.string.cpd_speaker_off,
        onClick = onAction::toggleSpeaker,
    ),
)

fun C2CCallInfo.getActionButtons(onAction: IVideoCallingAction) = buildList {
    if (supportCameraEnable) {
        add(
            ActionButtonItem(
                icon = if (selfCameraEnable) R.drawable.ic_call_on_camera else R.drawable.ic_call_off_camera,
                text = if (selfCameraEnable) R.string.cpd_camera_on else R.string.cpd_camera_off,
                onClick = onAction::toggleCamera,
            )
        )
    }

    add(
        ActionButtonItem(
            icon = R.drawable.ic_call_hang_up,
            text = R.string.cpd_hang_up,
            onClick = onAction::hangup,
        )
    )

    if (supportCameraEnable && !selfCameraEnable) {
        add(
            ActionButtonItem(
                icon = R.drawable.ic_call_flip_camera_disable,
                text = R.string.cpd_camera_flip,
                enabled = false,
                onClick = onAction::flipCamera,
            )
        )
    } else {
        add(
            ActionButtonItem(
                icon = if (selfUseFrontCamera) R.drawable.ic_call_flip_camera_front else R.drawable.ic_call_flip_camera,
                text = R.string.cpd_camera_flip,
                onClick = onAction::flipCamera,
            )
        )
    }
}.toTypedArray()


@Stable
interface IVoiceCallingAction {

    companion object {
        val Empty = object : IVoiceCallingAction by noOpDelegate() {}
    }

    fun toggleMicrophone()

    fun toggleSpeaker()

    fun hangup()

    fun collapse()

    fun expand()

    fun freeTimeOver()
}

@Stable
interface IVideoCallingAction : IVoiceCallingAction {

    companion object {
        val Empty = object : IVideoCallingAction by noOpDelegate() {}
    }

    fun toggleCamera()

    fun flipCamera()

    fun toggleVideoPreview(uid: String?, view: View?)
}

