package com.qyqy.cupid.ui.call

import android.Manifest
import android.os.SystemClock
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.Stable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.qyqy.cupid.data.C2CCallInfo
import com.qyqy.cupid.data.CallMode
import com.qyqy.cupid.data.CallState
import com.qyqy.cupid.data.CallState.IRtcOwner
import com.qyqy.cupid.data.InCallInfo
import com.qyqy.cupid.ui.CupidViewModel
import com.qyqy.cupid.ui.IMutexFeature
import com.qyqy.cupid.ui.MutexFeature
import com.qyqy.cupid.ui.dialog.BalanceNotEnoughToStartCall
import com.qyqy.cupid.ui.dialog.ContentAlertDialog
import com.qyqy.cupid.ui.dialog.DialogButton
import com.qyqy.cupid.ui.dialog.DialogQueue
import com.qyqy.cupid.ui.dialog.InsufficientIntimacyToStartCall
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.dialog.TargetBalanceNotEnoughToStartCall
import com.qyqy.cupid.utils.CallRingHelper
import com.qyqy.cupid.widgets.PermissionLauncher
import com.qyqy.cupid.widgets.rememberPermissionLauncher
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.http.ApiException
import com.qyqy.ucoo.http.getIntOrNull
import com.qyqy.ucoo.http.getOrNull
import com.qyqy.ucoo.http.getStringOrNull
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.IMCompatListener
import com.qyqy.ucoo.im.compat.IMUtils
import com.qyqy.ucoo.im.compat.UCVideoCallMessage
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.user.video.VideoCall
import com.qyqy.ucoo.user.video.VideoCallApi
import com.qyqy.ucoo.utils.rtc.IRtcUpdateListener
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonObject

private const val KEY = "unFinishCallId"

class C2CCallingHelper(
    private val viewModelScope: CoroutineScope,
    private val savedStateHandle: SavedStateHandle,
    private val viewModel: CupidViewModel,
) : IMutexFeature {

    var value: CallState<*>
        @Deprecated(
            message = "禁止访问此属性",
            replaceWith = ReplaceWith("c2cCallState.value"),
            level = DeprecationLevel.HIDDEN
        ) get() = c2cCallStateFlow.value
        set(value) {
            c2cCallStateFlow.value = value
        }

    private val callApiHelper = CallingApiHelper()

    /**
     * 状态提升，避免activity重建，导致状态丢失，以及rtc引擎销毁
     */
    private val c2cCallStateFlow = MutableStateFlow<CallState<*>>(CallState.Idea)

    private val unFinishCallIdFlow = savedStateHandle.getStateFlow(KEY, "")

    private var job: Job? = null

    private val listener = object : IMCompatListener {

        override fun onVideoCallMessage(message: UCVideoCallMessage, offline: Boolean) {
            when (message) {
                is UCVideoCallMessage.Invite -> {
                    onReceivedC2CCall(message, sUser)
                }

                is UCVideoCallMessage.Start -> {
                    onC2CCallEvent(message)
                }

                is UCVideoCallMessage.Match -> {
                    onC2CCallEvent(message)
                }

                is UCVideoCallMessage.Other -> {
                    onC2CCallEvent(message)
                }
            }
        }
    }

    private val rtcListener = object : IRtcUpdateListener {

        override fun onStreamFirstFrameUpdate(uid: String, streamId: String, video: Boolean) {
            if (!video) {
                return
            }
            val curState = c2cCallStateFlow.value
            if (curState is CallState.ICalling<*>) {
                val info = curState.info
                if (info is C2CCallInfo && info.targetUser.id == uid && !info.targetJoined) {
                    value = curState.copySelf(info = info.copy(targetJoined = true))
                }
            }
        }

        override fun onCameraFirstFrameUpdate(uid: String, streamId: String, self: Boolean) {
            viewModelScope.launch {
                delay(60)
                val curState = c2cCallStateFlow.value
                if (curState is CallState.ICalling<*>) {
                    val info = curState.info
                    if (info is C2CCallInfo && info.targetUser.id == uid && !info.targetHasCameraFrame) {
                        value = curState.copySelf(info = info.copy(targetHasCameraFrame = true))
                    }
                }
            }
        }

        override fun onVideoCameraStateUpdate(uid: String, streamId: String, muted: Boolean) {
            val enabled = !muted
            val curState = c2cCallStateFlow.value
            if (curState is CallState.ICalling<*>) {
                val info = curState.info
                if (info is C2CCallInfo && info.targetUser.id == uid && info.targetCameraEnable != enabled) {
                    value = curState.copySelf(
                        info = info.copy(
                            targetCameraEnable = enabled, targetHasCameraFrame = if (!enabled) false else info.targetHasCameraFrame
                        ),
                    )
                }
            }
        }
    }

    val onAction = object : IVideoCallingAction {

        private fun toIdea(callId: String) {
            val state = c2cCallStateFlow.value
            if (state is CallState.ICalling<*> && state.callId == callId) {
                if (state is IRtcOwner) {
                    state.rtcManager.levelChannel()
                }
                value = CallState.Idea
            }
        }

        override fun toggleCamera() {
            val state = c2cCallStateFlow.value
            if (state is CallState.ICalling<*> && state is IRtcOwner) {
                val info = state.info
                if (info is C2CCallInfo) {
                    state.rtcManager.enableCamera(!info.selfCameraEnable)
                    value = state.copySelf(info.copy(selfCameraEnable = !info.selfCameraEnable))
                }
            }
        }

        override fun flipCamera() {
            val state = c2cCallStateFlow.value
            if (state is CallState.ICalling<*> && state is IRtcOwner) {
                val info = state.info
                if (info is C2CCallInfo) {
                    state.rtcManager.useFrontCamera(!info.selfUseFrontCamera)
                    value = state.copySelf(info.copy(selfUseFrontCamera = !info.selfUseFrontCamera))
                }
            }
        }

        override fun toggleVideoPreview(uid: String?, view: View?) {
            val state = c2cCallStateFlow.value
            if (state is CallState.ICalling<*> && state is IRtcOwner) {
                if (uid == null) {
                    if (view != null) {
                        state.rtcManager.startVideoPreview(view)
                    }
                } else {
                    if (view != null) {
                        state.rtcManager.startPlayingStream(uid, view)
                    }
                }
            }
        }

        override fun toggleMicrophone() {
            val state = c2cCallStateFlow.value
            if (state is CallState.ICalling<*> && state is IRtcOwner) {
                val info = state.info
                if (info is C2CCallInfo) {
                    state.rtcManager.setEnableMuteAudio(info.microphoneEnable)
                    value = state.copySelf(info.copy(microphoneEnable = !info.microphoneEnable))
                }
            }
        }

        override fun toggleSpeaker() {
            val state = c2cCallStateFlow.value
            if (state is CallState.ICalling<*> && state is IRtcOwner) {
                val info = state.info
                if (info is C2CCallInfo) {
                    state.rtcManager.setEnableSpeakerphone(!info.speakerEnable)
                    value = state.copySelf(info.copy(speakerEnable = !info.speakerEnable))
                }
            }
        }

        override fun hangup() {
            val state = c2cCallStateFlow.value
            if (state is CallState.ICalling<*>) {
                viewModelScope.launch {
                    withContext(NonCancellable) { callApiHelper.finishCurrentCall(state) }.onSuccess {
                        resetUnFinishCallId(state.callId)
                    }
                    toIdea(state.callId)
                }
            }
        }

        override fun collapse() {
            val state = c2cCallStateFlow.value
            if (state is CallState.ICalling<*>) {
                val info = state.info
                if (info is C2CCallInfo && !info.collapsed) {
                    value = state.copySelf(info.copy(collapsed = true))
                }
            }
        }

        override fun expand() {
            val state = c2cCallStateFlow.value
            if (state is CallState.ICalling<*>) {
                val info = state.info
                if (info is C2CCallInfo && info.collapsed) {
                    value = state.copySelf(info.copy(collapsed = false))
                }
            }
        }

        override fun freeTimeOver() {
            val state = c2cCallStateFlow.value
            if (state is CallState.InCall<*>) {
                value = state.copy(inCall = state.inCall.copy(showFreeCallTime = false))
            }
        }
    }


    override fun start() {
        // 保证异常通话及时结束
        viewModelScope.launch {
            c2cCallStateFlow.combine(unFinishCallIdFlow) { callState, unFinishCallId ->
                val currentCallId = (callState as? CallState.ICalling)?.callId.orEmpty()
                if (unFinishCallId.isNotEmpty() && unFinishCallId != currentCallId) {
                    unFinishCallId
                } else {
                    null
                }
            }.filterNotNull().collectLatest { callId ->
                while (true) {
                    delay(3000)
                    if (withContext(NonCancellable) { callApiHelper.hangupCall(callId).isSuccess }) {
                        resetUnFinishCallId(callId)
                        break
                    }
                }
            }
        }

        // 保证接受到VIDEO_CALL_START再开始通话
        viewModelScope.launch {
            c2cCallStateFlow.filterIsInstance<CallState.ICalling<*>>().map {
                if (it is CallState.Calling<*>) {
                    it.callInConfirming
                } else {
                    false
                }
            }.collectLatest {
                if (it) {
                    // 开启10s超时等待[MsgEventCmd.VIDEO_CALL_START]消息，超时结束后还无法收到此消息，则自动结束
                    delay(10000)
                    onAction.hangup()
                }
            }
        }

        // 视频通话
        viewModelScope.launch {
            c2cCallStateFlow.filter {
                val state = it as? CallState.ICalling
                state is IRtcOwner && !state.info.mode.isOnlyVoice()
            }.filterIsInstance<IRtcOwner>().distinctUntilChanged().collectLatest {
                it.rtcManager.setRtcUpdateListener(listener = rtcListener)
                try {
                    delay(Long.MAX_VALUE)
                } finally {
                    it.rtcManager.setRtcUpdateListener(listener = null)
                }
            }
        }

        // 通话结束取消任务
        viewModelScope.launch {
            c2cCallStateFlow.filterIsInstance<CallState.Idea>().collectLatest {
                job?.cancel()
                job = null
            }
        }

        viewModelScope.launch {
            c2cCallStateFlow.map { state ->
                if (state is CallState.Incoming<*>) {
                    1
                } else if (state is CallState.Outgoing<*>) {
                    2
                } else {
                    0
                }
            }.distinctUntilChanged().collectLatest {
                if (it == 0) {
                    CallRingHelper.stopRing()
                } else if (it == 1) {
                    CallRingHelper.startRing(withVibrator = true)
                } else if (it == 2) {
                    CallRingHelper.startRing(withVibrator = false)
                }
            }
        }
        IMCompatCore.addIMListener(listener)
    }

    override fun clear() {
        IMCompatCore.removeIMListener(listener)
    }

    override fun getCurrentMutexFeature(): MutexFeature? {
        if (c2cCallStateFlow.value is CallState.Idea) {
            return null
        }
        return MutexFeature.Call
    }

    @Composable
    fun rememberC2cCallState() = c2cCallStateFlow.collectAsStateWithLifecycle()

    /**
     * 接听通话
     */
    suspend fun answerCall(callId: String, withConfirm: Boolean): Result<JsonObject> {
        return callApiHelper.answerCall(callId, withConfirm)
    }

    fun resetUnFinishCallId(callId: String) {
        if (savedStateHandle.get<String>(KEY) == callId) {
            savedStateHandle[KEY] = ""
        }
    }

    private fun setUnFinishCallId(callId: String) {
        if (savedStateHandle.get<String>(KEY) == "") {
            savedStateHandle[KEY] = callId
        }
    }

    @Composable
    fun rememberCallingLauncher(targetUserState: MutableState<User?>, fromRecommend: Boolean = false): PermissionLauncher<Unit> {
        val scope = rememberCoroutineScope()
        val dialogQueue = LocalDialogQueue.current
        return rememberPermissionLauncher {
            val mode = if (it.contains(Manifest.permission.CAMERA)) {
                CallMode.OnlyVideo
            } else {
                CallMode.OnlyVoice
            }
            targetUserState.value?.also { user ->
                startCalling(user, mode, fromRecommend, dialogQueue, scope)
            }
        }
    }

    @Composable
    fun rememberCallingLauncher(targetUser: User, fromRecommend: Boolean = false): PermissionLauncher<Unit> {
        val scope = rememberCoroutineScope()
        val dialogQueue = LocalDialogQueue.current
        return rememberPermissionLauncher {
            val mode = if (it.contains(Manifest.permission.CAMERA)) {
                CallMode.OnlyVideo
            } else {
                CallMode.OnlyVoice
            }
            startCalling(targetUser, mode, fromRecommend, dialogQueue, scope)
        }
    }

    private fun startCalling(targetUser: User, mode: CallMode, fromRecommend: Boolean, dialogQueue: DialogQueue<*>, scope: CoroutineScope) {
        scope.launch {
            startC2CCall(targetUser, mode, fromRecommend).onFailure { e ->
                if (e is ApiException) {
                    val type = e.extra?.getIntOrNull("t")
                    val msg = e.extra?.getStringOrNull("msg")
                    if (type == 1 && !msg.isNullOrEmpty()) {
                        dialogQueue.push(InsufficientIntimacyToStartCall(targetUser.id, msg))
                        return@onFailure
                    }
                    if (type == 2 && !msg.isNullOrEmpty()) {
                        dialogQueue.push(BalanceNotEnoughToStartCall(msg))
                        return@onFailure
                    }
                    if (type == 3 && !msg.isNullOrEmpty()) {
                        dialogQueue.push(TargetBalanceNotEnoughToStartCall(msg))
                        return@onFailure
                    }
                    if (type == 4 && !msg.isNullOrEmpty()) {
                        dialogQueue.pushCenterDialog(true) { dialog, _ ->
                            ContentAlertDialog(
                                content = msg,
                                startButton = DialogButton(stringResource(id = R.string.cpd再想想)) {
                                    dialog.dismiss()
                                },
                                endButton = DialogButton(stringResource(id = R.string.cpd发起通话)) {
                                    startCalling(targetUser, mode, fromRecommend, dialogQueue, scope)
                                    dialog.dismiss()
                                },
                            )
                        }
                        return@onFailure
                    }
                }
                e.toast()
            }
        }
    }

    private suspend fun startC2CCall(
        user: User,
        mode: CallMode,
        fromRecommend: Boolean,
    ): Result<VideoCall> {
        val message = c2cCallPreCheck()
        if (message != null) {
            return Result.failure(IllegalStateException(message))
        }
        val param = buildMap {
            put("target_userid", user.id)
            put("only_audio", mode.isOnlyVoice().toString())
            if (fromRecommend) {
                put("scene_type", "8")
                put("scene_id", "0")
            }
        }
        return runApiCatching(globalErrorHandleIntercepted = {
            it.status == -100
        }) {
            VideoCallApi.instance.canMakeVideoCall(param)
        }.fold({
            runApiCatching {
                VideoCallApi.instance.makeVideoCall(param)
            }
        }, { e ->
            Result.failure(e)
        }).onSuccess {
            IMUtils.launchOnMain {
                onSentC2CCall(it = it, mode = mode, selfUser = sUser, targetUser = user)
            }
        }
    }

    private fun c2cCallPreCheck(): String? {
        return viewModel.checkFeature(MutexFeature.Call)
    }

    private fun onReceivedC2CCall(message: UCVideoCallMessage.Invite, selfUser: User) {
        message.invite.also {
            val state = c2cCallStateFlow.value
            if (state != CallState.Idea) { //  当前正在通话中，自动挂断，对方会送到忙线
                callApiHelper.autoReject(viewModelScope, it.callId)
                return
            }
            c2cCallStateFlow.value = CallState.Incoming(
                channelId = it.channelId,
                rtcToken = it.rtcToken,
                info = C2CCallInfo(
                    callId = it.callId,
                    selfUser = selfUser,
                    targetUser = it.inviter,
                    mode = if (it.isVoice) CallMode.OnlyVoice else CallMode.OnlyVideo,
                    rtcType = it.rtcType,
                    rtcConfig = message.rawInstanceMessage.customJson.getOrNull("rtc_config")?.jsonObject,
                    supportCameraEnable = it.supportCameraEnable
                ),
                callInConfirming = false,
                hintContent = it.hint,
                hintLeadContent = it.inviteeRichHint,
            )
            setUnFinishCallId(it.callId)
            viewModel.dialogQueue.push(
                IncomeCallDialog(it.uiVersion, it.canFreeVideoCall)
            )
        }
    }

    private fun onSentC2CCall(it: VideoCall, mode: CallMode, selfUser: User, targetUser: User) {
        val state = c2cCallStateFlow.value
        if (state != CallState.Idea) { //  当前正在通话中
            return
        }
        c2cCallStateFlow.value = CallState.Outgoing(
            channelId = it.channelId,
            rtcToken = it.rtcToken,
            info = C2CCallInfo(
                callId = it.callId,
                selfUser = selfUser,
                targetUser = targetUser,
                mode = mode,
                rtcType = it.rtcType,
                rtcConfig = it.rtcConfig,
                supportCameraEnable = it.supportCameraEnable
            ),
            callInConfirming = false,
            hintContent = it.hint,
        ).also {
            if (!mode.isOnlyVoice()) {
                it.rtcManager.enableCamera(true) // 开启预览
            }
        }
        setUnFinishCallId(it.callId)
    }

    private fun onC2CCallEvent(message: UCVideoCallMessage) {
        val state = c2cCallStateFlow.value
        val cmd = message.cmd
        if (message is UCVideoCallMessage.Other) {
            when (cmd) {
                MsgEventCmd.VIDEO_CALL_REFUSE,
                MsgEventCmd.VIDEO_CALL_CANCEL,
                MsgEventCmd.VIDEO_CALL_BUSY,
                MsgEventCmd.VIDEO_CALL_TIMEOUT,
                MsgEventCmd.VIDEO_CALL_FINISH,
                -> {
                    message.notice.also {
                        resetUnFinishCallId(it.callId)
                        if (state is CallState.ICalling<*> && state.callId == it.callId) {
                            c2cCallStateFlow.value = callingToFinish(cmd, state)
                        }
                    }
                }

                MsgEventCmd.VIDEO_CALL_WILL_OUT_OF_BALANCE -> {
                    message.notice.also {
                        if (state is CallState.InCall && state.callId == it.callId) { // 通话中，余额不足
                            c2cCallStateFlow.value = state.copy(
                                inCall = state.inCall.copy(
                                    payCallFinishElapsedRealtime = SystemClock.elapsedRealtime()
                                        .plus(it.countDownSeconds.times(1000)),
                                    coinPayCallPerMinute = it.coinCostPerMinute
                                )
                            )
                            viewModel.dialogQueue.push(NotEnoughBalanceForCallDialog)
                        }
                    }
                }

                MsgEventCmd.VIDEO_CALL_ACCOUNT_INFO -> {
                    message.notice.also {
                        if (state is CallState.InCall && state.callId == it.callId) { //
                            c2cCallStateFlow.value = state.copy(
                                inCall = state.inCall.copy(
                                    showFreeCallTime = it.accountType == 1,
                                    freeCallFinishElapsedRealtime = SystemClock.elapsedRealtime().plus(it.accountAmount.times(1000)),
                                )
                            )
                        }
                    }
                }
            }
        } else {
            when (cmd) {
                MsgEventCmd.VIDEO_CALL_ANSWER -> {
                    (message as? UCVideoCallMessage.Start)?.notice?.also {
                        if (state is CallState.Outgoing<*> && state.callId == it.callId) { // 对方接听
                            // 收到接听消息后，需要调用一个确认接口，确认此次通话
                            c2cCallStateFlow.value = state.copy(callInConfirming = true)
                            job?.cancel()
                            job = viewModelScope.launch {
                                while (isActive) {
                                    val curState = c2cCallStateFlow.value
                                    if (curState is CallState.Outgoing<*> && curState.callId == it.callId) {
                                        val ret = callApiHelper.confirmCall(it.callId)
                                        if (!ret.isSuccess) {
                                            delay(3000)
                                            continue
                                        }
                                    }
                                    break
                                }
                            }
                        }
                    } ?: (message as? UCVideoCallMessage.Match)?.notice?.also {
                        if (c2cCallPreCheck() != null) { //暂时不适合接电话，自动挂断电话
                            viewModelScope.launch {
                                callApiHelper.hangupCall(it.callId)
                            }
                            return
                        }
                        if (state is CallState.Idea) {
                            // 收到接听消息后，需要调用一个确认接口，确认此次通话
                            c2cCallStateFlow.value = CallState.Matching(
                                channelId = it.channelId,
                                rtcToken = it.rtcToken,
                                info = C2CCallInfo(
                                    callId = it.callId,
                                    selfUser = sUser,
                                    targetUser = it.inviter,
                                    mode = CallMode.OnlyVoice,
                                    rtcType = it.rtcType,
                                    rtcConfig = message.rawInstanceMessage.customJson.getOrNull("rtc_config")?.jsonObject,
                                    supportCameraEnable = false
                                ),
                            )
                            job?.cancel()
                            job = viewModelScope.launch {
                                var retry = false
                                while (isActive) {
                                    val curState = c2cCallStateFlow.value
                                    if (curState is CallState.Matching<*> && curState.callId == it.callId) {
                                        val ret = callApiHelper.confirmCall(it.callId)
                                        if (!retry || !ret.isSuccess) {
                                            delay(3000)
                                            retry = true
                                            continue
                                        }
                                    }
                                    break
                                }

                                delay(10_000)
                                val curState = c2cCallStateFlow.value
                                if (curState is CallState.Matching<*> && curState.callId == it.callId) {
                                    onAction.hangup()
                                }
                            }
                        }
                    }
                }

                MsgEventCmd.VIDEO_CALL_START -> {
                    ((message as? UCVideoCallMessage.Start)?.notice ?: (message as? UCVideoCallMessage.Match)?.notice)?.also {
                        if (state is CallState.ICalling<*> && state.callId == it.callId) { // 已取消
                            when (state) {
                                is CallState.Matching<*> -> {
                                    c2cCallStateFlow.value = CallState.InCall(
                                        channelId = state.channelId,
                                        rtcToken = state.rtcToken,
                                        info = state.info,
                                    )
                                }

                                is CallState.Incoming<*> -> {
                                    c2cCallStateFlow.value = CallState.InCall(
                                        channelId = state.channelId,
                                        rtcToken = state.rtcToken,
                                        info = state.info,
                                    )
                                }

                                is CallState.Outgoing<*> -> {
                                    c2cCallStateFlow.value = CallState.InCall(
                                        channelId = state.channelId,
                                        rtcToken = state.rtcToken,
                                        info = state.info,
                                        inCall = InCallInfo(
                                            showFreeCallTime = it.accountType == 1,
                                            freeCallFinishElapsedRealtime = SystemClock.elapsedRealtime()
                                                .plus(it.accountAmount.times(1000)),
                                        ),
                                        rtcManager = state.rtcManager
                                    )
                                }

                                else -> Unit
                            }
                        }
                    }
                }
            }
        }
    }
}

private fun callingToFinish(cmd: String, state: CallState.ICalling<*>): CallState<*> {
    when (state) {
        is CallState.Matching<*> -> {

        }

        is CallState.Incoming<*> -> {  // 对方取消

        }

        is CallState.Outgoing<*> -> { // 对方拒绝，呼叫超时，忙线(对方忙线，一般是正在通话中)

        }

        is CallState.InCall -> { // 用户主动挂断，系统挂断

        }
    }
    if (state is IRtcOwner) {
        state.rtcManager.levelChannel()
    }
    return CallState.Idea
}


@Stable
class CallingApiHelper {

    private val callingApi get() = VideoCallApi.instance


    /**
     * 正在通话中，再收到新的通话邀请，自动拒绝
     */
    fun autoReject(coroutineScope: CoroutineScope, callId: String) {
        coroutineScope.launch {
            runApiCatching {
                callingApi.answerVideoCall(mapOf("conversation_id" to callId, "accept" to "False", "refuse_reason" to "2"))
            }
        }
    }

    /**
     * 接听通话
     */
    suspend fun answerCall(callId: String, withConfirm: Boolean): Result<JsonObject> {
        return runApiCatching(globalErrorHandleIntercepted = {
            it.status == -100
        }) {
            callingApi.answerVideoCall(
                mapOf(
                    "conversation_id" to callId,
                    "accept" to "True",
                    "refuse_reason" to "0",
                    "with_confirm" to withConfirm.toString(),
                )
            )
        }
    }

    /**
     * 确认通话，由发起方确认
     */
    suspend fun confirmCall(callId: String): Result<JsonObject> {
        return runApiCatching {
            callingApi.confirmVideoCall(mapOf("conversation_id" to callId))
        }
    }

    /**
     * 挂断通话
     */
    suspend fun hangupCall(callId: String): Result<JsonObject> {
        return runApiCatching {
            callingApi.hangupVideoCall(mapOf("conversation_id" to callId))
        }
    }

    suspend fun finishCurrentCall(state: CallState.ICalling<*>): Result<JsonObject> {
        val callId = state.callId
        return runApiCatching {
            if (state is CallState.Incoming<*> && !state.callInConfirming) {
                callingApi.answerVideoCall(mapOf("conversation_id" to callId, "accept" to "False", "refuse_reason" to "1"))
            } else {
                callingApi.hangupVideoCall(mapOf("conversation_id" to callId))
            }
        }
    }

}
