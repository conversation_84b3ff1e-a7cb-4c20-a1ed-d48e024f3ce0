package com.qyqy.cupid.ui.call

import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionLayout
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.saveable.rememberSaveableStateHolder
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.compose.currentBackStackEntryAsState
import com.qyqy.cupid.data.C2CCallInfo
import com.qyqy.cupid.data.CallMode
import com.qyqy.cupid.data.CallState
import com.qyqy.cupid.im.panel.gift.C2CBottomGiftPanelDialog
import com.qyqy.cupid.im.panel.gift.GiftPosition
import com.qyqy.cupid.im.panel.gift.GiftViewModel
import com.qyqy.cupid.ui.CupidViewModel
import com.qyqy.cupid.ui.GiftExtra
import com.qyqy.cupid.ui.IGiftAction
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.dialog.rememberDialogQueue
import com.qyqy.cupid.ui.profile.RechargeDialog
import com.qyqy.cupid.widgets.autoDraggable
import com.qyqy.ucoo.R
import com.qyqy.ucoo.ReportExposureCompose
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.compose.keepLastNonNullState
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppearanceStatusBars
import com.qyqy.ucoo.im.bean.CPGift
import com.qyqy.ucoo.im.compat.ConversationType
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter

@Composable
fun CallingScaffold(
    modifier: Modifier = Modifier,
    background: @Composable BoxScope.() -> Unit,
    front: @Composable BoxScope.() -> Unit,
    topContent: @Composable ColumnScope.() -> Unit,
    leadTopContent: @Composable ColumnScope.() -> Unit,
    leadBottomContent: @Composable ColumnScope.() -> Unit,
    bottomContent: @Composable ColumnScope.() -> Unit,
) {
    Box(modifier = modifier.fillMaxSize()) {

        background()

        Column(
            modifier = Modifier
                .fillMaxSize()
                .navigationBarsPadding(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            topContent()

            Spacer(modifier = Modifier.height(100.dp))

            leadTopContent()

            Spacer(modifier = Modifier.weight(1f))

            leadBottomContent()

            Spacer(modifier = Modifier.height(30.dp))

            bottomContent()

            Spacer(modifier = Modifier.height(60.dp))
        }

        front()
    }
}

@OptIn(ExperimentalSharedTransitionApi::class)
@Composable
fun RegisterC2CCalling(viewModel: CupidViewModel) {

    val c2cCallingHelper = viewModel.c2cCallingHelper

    val c2cCallState = c2cCallingHelper.rememberC2cCallState()

    val _state = c2cCallState.value as? CallState.ICallingPage

    val isVisible = _state != null

    val state = keepLastNonNullState(_state)

    if (_state != null) {
        DisposableEffect(key1 = Unit) {
            onDispose {
                if (c2cCallState.value is CallState.ICallingPage) {
                    c2cCallingHelper.onAction.hangup()
                }
            }
        }
    }

    SharedTransitionLayout {
        AnimatedVisibility(
            visible = isVisible,
            enter = slideInVertically(initialOffsetY = { it }),
            exit = slideOutVertically(targetOffsetY = { it }),
        ) {
            state ?: return@AnimatedVisibility

            val saveableStateHolder = rememberSaveableStateHolder()
            ReportExposureCompose(exposureName = TracePoints.VISIT_HOME_VOICE_CALL_DIALOG) {
                AnimatedContent(targetState = state.collapsed, label = "", transitionSpec = {
                    EnterTransition.None togetherWith ExitTransition.None
                }) { collapsed ->
                    Box(modifier = Modifier.fillMaxSize()) {
                        if (collapsed) {
                            saveableStateHolder.SaveableStateProvider(key = "call_floating_view") {
                                if (state.mode == CallMode.OnlyVoice) {
                                    @Suppress("UNCHECKED_CAST") C2CVoiceCallingFloat(
                                        callState = state as CallState.ICalling<C2CCallInfo>,
                                        onAction = c2cCallingHelper.onAction,
                                        modifier = Modifier
                                            .align(Alignment.BottomEnd)
                                            .padding(bottom = 10.dp)
                                            .autoDraggable(
                                                suggestionGap = 3.dp,
                                                horizontalAlignPadding = 8.dp
                                            )
                                            .sharedBounds(
                                                sharedContentState = rememberSharedContentState(key = "voiceCallFloat"),
                                                animatedVisibilityScope = this@AnimatedContent,
                                                clipInOverlayDuringTransition = OverlayClip(Shapes.corner12),
                                            )
                                    )
                                } else {
                                    @Suppress("UNCHECKED_CAST") C2CVideoCallingFloat(
                                        callState = state as CallState.ICalling<C2CCallInfo>,
                                        onAction = c2cCallingHelper.onAction,
                                        modifier = Modifier
                                            .align(Alignment.BottomEnd)
                                            .padding(bottom = 10.dp)
                                            .autoDraggable(
                                                suggestionGap = 3.dp,
                                                horizontalAlignPadding = 8.dp
                                            )
                                            .sharedBounds(
                                                sharedContentState = rememberSharedContentState(key = "videoCallFloat"),
                                                animatedVisibilityScope = this@AnimatedContent,
                                                clipInOverlayDuringTransition = OverlayClip(Shapes.corner12),
                                            )
                                    )
                                }
                            }
                        } else {

                            AppearanceStatusBars(isLight = false, autoRestore = true)

                            BackHandler {
                                c2cCallingHelper.onAction.collapse()
                            }

                            val backstackState =
                                LocalAppNavController.current.composeNav.currentBackStackEntryAsState()

                            LaunchedEffect(key1 = backstackState) {
                                val topBackstack = backstackState.value

                                snapshotFlow {
                                    val cur = backstackState.value
                                    cur != null && cur.id != topBackstack?.id
                                }.filter { it }.collectLatest {
                                    c2cCallingHelper.onAction.collapse()
                                }
                            }
                            val giftViewModel = viewModel<GiftViewModel>(initializer = {
                                GiftViewModel(
                                    (state.info as C2CCallInfo).targetUser.id,
                                    ConversationType.C2C
                                )
                            })

                            val dialogQueue = rememberDialogQueue<IGiftAction>()

                            val action = object : IGiftAction {

                                override fun onShowGiftPanel(position: GiftPosition?) {
                                    giftViewModel.fetchGift()
                                    dialogQueue.push(
                                        C2CBottomGiftPanelDialog(
                                            position,
                                            giftViewModel.giftListModelState
                                        )
                                    )
                                }

                                override fun onSendGift(
                                    gift: CPGift,
                                    count: Int,
                                    extra: GiftExtra
                                ) {
                                    giftViewModel.sendGift(gift, count, extra)
                                }

                                override fun onShowRechargePanel() {
                                    dialogQueue.push(RechargeDialog, true)
                                }
                            }
                            dialogQueue.DialogContent(action)

                            val gift: @Composable BoxScope.() -> Unit = {
                                if (state is CallState.InCall) {
                                    Image(
                                        painter = painterResource(id = R.drawable.ic_call_gift),
                                        contentDescription = null,
                                        modifier = Modifier
                                            .align(Alignment.BottomEnd)
                                            .padding(bottom = 185.dp, end = 16.dp)
                                            .navigationBarsPadding()
                                            .noEffectClickable(onClick = action::onShowGiftPanel),
                                        contentScale = ContentScale.FillHeight,
                                    )
                                    giftViewModel.GiftEffectView()
                                }
                            }

                            if (state.mode == CallMode.OnlyVoice) {
                                @Suppress("UNCHECKED_CAST") C2CVoiceCallingPage(
                                    callState = state as CallState.ICalling<C2CCallInfo>,
                                    onAction = c2cCallingHelper.onAction,
                                    modifier = Modifier.sharedBounds(
                                        sharedContentState = rememberSharedContentState(key = "voiceCallFloat"),
                                        animatedVisibilityScope = this@AnimatedContent,
                                    ),
                                    front = gift
                                )
                            } else {
                                @Suppress("UNCHECKED_CAST") C2CVideoCallingPage(
                                    callState = state as CallState.ICalling<C2CCallInfo>,
                                    onAction = c2cCallingHelper.onAction,
                                    modifier = Modifier.sharedBounds(
                                        sharedContentState = rememberSharedContentState(key = "videoCallFloat"),
                                        animatedVisibilityScope = this@AnimatedContent,
                                    ),
                                    front = gift
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}
