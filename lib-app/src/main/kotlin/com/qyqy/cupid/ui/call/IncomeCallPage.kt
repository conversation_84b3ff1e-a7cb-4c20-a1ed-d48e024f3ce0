package com.qyqy.cupid.ui.call

import android.Manifest
import android.os.Parcelable
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.data.C2CCallInfo
import com.qyqy.cupid.data.CallMode
import com.qyqy.cupid.data.CallState
import com.qyqy.cupid.ui.CupidViewModel
import com.qyqy.cupid.ui.dialog.ContentAlertDialog
import com.qyqy.cupid.ui.dialog.DialogButton
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.SimpleAnimatedDialog
import com.qyqy.cupid.utils.CallRingHelper
import com.qyqy.cupid.widgets.rememberPermissionLauncher
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.asComponentActivity
import com.qyqy.ucoo.compose.keepLastNonNullState
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.presentation.room.RichText
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AgeGender
import com.qyqy.ucoo.compose.ui.AnyPopDialogProperties
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.DirectionState
import com.qyqy.ucoo.http.ApiException
import com.qyqy.ucoo.http.getIntOrNull
import com.qyqy.ucoo.http.getStringOrNull
import com.qyqy.ucoo.toast
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize

@Parcelize
data class IncomeCallDialog(private val uiVersion: Int, private val canFreeCall: Boolean) : SimpleAnimatedDialog(), Parcelable {

    override val properties: AnyPopDialogProperties
        get() = AnyPopDialogProperties(
            direction = if (uiVersion == 2) {
                DirectionState.BOTTOM
            } else {
                DirectionState.CENTER
            }
        )

    @Composable
    override fun Content(dialog: IDialog) {

        val activity = LocalContext.current.asComponentActivity!!

        val viewModel = viewModel<CupidViewModel>(viewModelStoreOwner = activity)

        val c2cCallingHelper = viewModel.c2cCallingHelper

        val c2cCallState = c2cCallingHelper.rememberC2cCallState()

        val _state = c2cCallState.value as? CallState.Incoming

        val nonNullState = keepLastNonNullState(_state)

        val state = if (_state == null) {
            LaunchedEffect(key1 = Unit) {
                dialog.dismiss()
            }
            if (nonNullState == null) {
                return
            }
            nonNullState
        } else {
            _state
        }

        val coroutineScope = rememberCoroutineScope()

        val onAction by rememberUpdatedState(newValue = c2cCallingHelper.onAction)
        DisposableEffect(key1 = Unit) {
            onDispose {
                if (c2cCallState.value is CallState.Incoming) {
                    onAction.hangup()
                }
            }
        }

        val permissions = remember(state.info.mode) {
            if (state.info.mode == CallMode.OnlyVoice) {
                arrayOf(Manifest.permission.RECORD_AUDIO)
            } else {
                arrayOf(Manifest.permission.RECORD_AUDIO, Manifest.permission.CAMERA)
            }
        }

        fun answerCall(withConfirm: Boolean) {
            // 同意接听，调用同意接听接口，接口成功后继续阻塞等待[MsgEventCmd.VIDEO_CALL_START]消息，期间不能关闭弹窗或者是点击按钮，收到以后才算接通成功，进入通话页面
            // 如果接口调用失败，toast提示，用户可以再继续点击同意
            val curState = c2cCallState.value
            if (curState is CallState.Incoming) {
                coroutineScope.launch {
                    c2cCallingHelper.answerCall(state.callId, withConfirm).onSuccess {
                        val curState = c2cCallState.value
                        if (curState is CallState.Incoming) {
                            c2cCallingHelper.value = curState.copy(callInConfirming = true)
                        }
                    }.onFailure { e ->
                        val curState = c2cCallState.value
                        if (curState is CallState.Incoming) {
                            c2cCallingHelper.value = curState.copy(callInConfirming = false)
                        }
                        if (e is ApiException) {
                            val type = e.extra?.getIntOrNull("t")
                            val msg = e.extra?.getStringOrNull("msg")
                            if (type == 1 && !msg.isNullOrEmpty()) {
                                viewModel.dialogQueue.pushCenterDialog(true) { d, _ ->
                                    ContentAlertDialog(
                                        content = msg,
                                        startButton = DialogButton(stringResource(id = R.string.cpd拒绝)) {
                                            d.dismiss()
                                            dialog.dismiss()
                                        },
                                        endButton = DialogButton(stringResource(id = R.string.cpd_answer)) {
                                            d.dismiss()
                                            answerCall(false)
                                        },
                                    )
                                }
                                return@onFailure
                            }
                        }
                        e.toast()
                    }
                }
            }
        }

        val launcher = rememberPermissionLauncher { _ ->
            answerCall(true)
        }

        if (uiVersion == 2) {
            IncomeCallContentV2(state, canFreeCall, onReject = {
                dialog.dismiss()
            }, onAnswer = {
                launcher.launch(permissions)
            })
        } else {
            IncomeCallContent(state, canFreeCall, onReject = {
                dialog.dismiss()
            }, onAnswer = {
                launcher.launch(permissions)
            })
        }
    }
}



@Composable
fun IncomeCallContent(callState: CallState.Incoming<*>, canFreeCall: Boolean, onReject: () -> Unit, onAnswer: () -> Unit) {
    val info = callState.info as C2CCallInfo
    Column(
        modifier = Modifier
            .padding(horizontal = 36.dp)
            .fillMaxWidth()
            .widthIn(max = 278.dp)
            .clip(RoundedCornerShape(16.dp))
            .background(Color.White)
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        CircleComposeImage(model = info.targetUser.avatarUrl, modifier = Modifier.size(72.dp))
        AutoSizeText(
            text = info.targetUser.nickname,
            modifier = Modifier.padding(top = 6.dp, bottom = 12.dp),
            color = Color(0xFF1D2129),
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
        )
        Text(
            text = callState.hintContent,
            color = Color(0xFF4E5969),
            fontSize = 14.sp,
            lineHeight = 16.sp
        )

        if (callState.hintLeadContent != null) {
            Spacer(modifier = Modifier.height(12.dp))
            RichText(
                rich = callState.hintLeadContent,
                color = Color(0xFF86909C),
                fontSize = 12.sp,
                lineHeight = 14.sp
            )
        }

        Spacer(modifier = Modifier.height(12.dp))
        Row(modifier = Modifier.fillMaxWidth()) {
            Row(
                modifier = Modifier
                    .weight(1f)
                    .height(36.dp)
                    .clip(CircleShape)
                    .background(Color(0xFFF76560))
                    .clickable(onClick = onReject),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp, Alignment.CenterHorizontally)
            ) {
                Icon(
                    painter = painterResource(
                        id = if (info.mode == CallMode.OnlyVoice) {
                            R.drawable.ic_voice_call_reject_answer
                        } else {
                            R.drawable.ic_video_call_reject_answer
                        }
                    ),
                    contentDescription = null,
                    tint = Color.White
                )
                Text(text = stringResource(id = R.string.cpd_hang_up), color = Color.White, fontSize = 14.sp)
            }

            Spacer(modifier = Modifier.width(12.dp))

            Row(
                modifier = Modifier
                    .weight(1f)
                    .height(36.dp)
                    .clip(CircleShape)
                    .background(Color(0xFF0FC363))
                    .clickable(enabled = !callState.callInConfirming, onClick = onAnswer),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp, Alignment.CenterHorizontally)
            ) {
                if (callState.callInConfirming) {
                    CircularProgressIndicator(
                        modifier = Modifier
                            .requiredSize(16.dp),
                        color = Color.White,
                        strokeWidth = 2.dp
                    )
                } else {
                    Icon(
                        painter = painterResource(
                            id = if (info.mode == CallMode.OnlyVoice) {
                                R.drawable.ic_voice_call_agree_answer
                            } else {
                                R.drawable.ic_video_call_agree_answer
                            }
                        ),
                        contentDescription = null,
                        tint = Color.White
                    )
                }
                Text(
                    text = if (callState.callInConfirming) {
                        stringResource(id = R.string.cpd接通中)
                    } else {
                        if (canFreeCall) {
                            stringResource(id = R.string.cpd_free_answer)
                        } else {
                            stringResource(id = R.string.cpd_answer)
                        }
                    },
                    color = Color.White,
                    fontSize = 14.sp
                )
            }
        }
    }
}

@Preview
@Composable
private fun PreviewIncomeCallContent() {
    IncomeCallContent(
        CallState.Incoming(
            channelId = "",
            rtcToken = "",
            info = C2CCallInfo(
                callId = "",
                selfUser = userForPreview,
                targetUser = userForPreview
            ),
            callInConfirming = false
        ), false, {}, {})
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun IncomeCallContentV2(
    callState: CallState.Incoming<*>,
    canFreeCall: Boolean,
    onReject: () -> Unit = {},
    onAnswer: () -> Unit = {},
) {
    val info = callState.info as C2CCallInfo
    val user = info.targetUser
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(brush = Brush.horizontalGradient(listOf(Color(0xFFFFEEF2), Color(0xFFFFF9D4))))
            .padding(horizontal = 32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        CenterAlignedTopAppBar(
            title = {
                Text(text = stringResource(id = R.string.cpd_语音通话邀请), fontSize = 18.sp, fontWeight = FontWeight.Medium)
            },
            colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
        )

        Row(
            modifier = Modifier
                .padding(top = 15.dp)
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(modifier = Modifier.size(48.dp)) {
                CircleComposeImage(model = user.avatarUrl)

                Spacer(
                    modifier = Modifier
                        .size(10.dp)
                        .clip(CircleShape)
                        .background(Color.White)
                        .padding(2.dp)
                        .clip(CircleShape)
                        .background(Color(0xFF18E046))
                        .align(Alignment.BottomEnd)
                )
            }

            Column(
                modifier = Modifier
                    .padding(start = 8.dp)
                    .weight(1f)
            ) {
                Row(verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.Start) {
                    Text(
                        modifier = Modifier.weight(1f, false),
                        text = user.nickname,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    AgeGender(age = user.age, isBoy = user.isBoy)
                }
                Spacer(modifier = Modifier.height(4.dp))
                //标签
                val tags = remember(user.locationLabel, user.height, user.career) {
                    listOf(
                        user.locationLabel,
                        if (user.height > 0) "${user.height}cm" else "",
                        user.career
                    ).filter { it.isNotEmpty() }
                        .joinToString(separator = " | ")
                }
                if (tags.isNotEmpty()) {
                    Text(
                        text = tags,
                        fontSize = 12.sp,
                        lineHeight = 14.sp,
                        color = Color(0xFF4E5969),
                    )
                }
            }
        }

        val pagerState = rememberPagerState {
            user.albumList.size + 1
        }

        HorizontalPager(
            state = pagerState,
            modifier = Modifier
                .padding(top = 20.dp)
                .fillMaxWidth()
                .aspectRatio(311f / 416f)
                .clip(Shapes.small)
        ) {
            if (it == 0) {
                ComposeImage(model = user.avatarUrl, modifier = Modifier.fillMaxSize())
            } else {
                ComposeImage(model = user.albumList[it.minus(1)].url, modifier = Modifier.fillMaxSize())
            }
        }

        Row(modifier = Modifier.padding(top = 12.dp), horizontalArrangement = Arrangement.spacedBy(8.dp)) {
            repeat(pagerState.pageCount) {
                Spacer(
                    modifier = Modifier
                        .size(10.dp)
                        .background(
                            Color(
                                if (pagerState.currentPage == it) {
                                    0xFFFF5E8B
                                } else {
                                    0x59000000
                                }
                            ), CircleShape
                        )
                )
            }
        }

        if (callState.hintLeadContent != null) {
            RichText(
                rich = callState.hintLeadContent,
                modifier = Modifier.padding(top = 24.dp),
                color = Color(0xFF86909C),
                fontSize = 12.sp,
                lineHeight = 14.sp
            )
        }

        Row(
            modifier = Modifier
                .padding(top = 32.dp)
                .fillMaxWidth()
        ) {
            Row(
                modifier = Modifier
                    .weight(1f)
                    .height(44.dp)
                    .clip(CircleShape)
                    .background(Color(0xFFF76560))
                    .clickable(onClick = onReject),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp, Alignment.CenterHorizontally)
            ) {
                Icon(
                    painter = painterResource(
                        id = if (info.mode == CallMode.OnlyVoice) {
                            R.drawable.ic_voice_call_reject_answer
                        } else {
                            R.drawable.ic_video_call_reject_answer
                        }
                    ),
                    contentDescription = null,
                    tint = Color.White
                )
                Text(text = stringResource(id = R.string.cpd_hang_up), color = Color.White, fontSize = 14.sp)
            }

            Spacer(modifier = Modifier.width(12.dp))

            Row(
                modifier = Modifier
                    .weight(1f)
                    .height(44.dp)
                    .clip(CircleShape)
                    .background(Color(0xFF0FC363))
                    .clickable(enabled = !callState.callInConfirming, onClick = onAnswer),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp, Alignment.CenterHorizontally)
            ) {
                if (callState.callInConfirming) {
                    CircularProgressIndicator(
                        modifier = Modifier
                            .requiredSize(16.dp),
                        color = Color.White,
                        strokeWidth = 2.dp
                    )
                } else {
                    Icon(
                        painter = painterResource(
                            id = if (info.mode == CallMode.OnlyVoice) {
                                R.drawable.ic_voice_call_agree_answer
                            } else {
                                R.drawable.ic_video_call_agree_answer
                            }
                        ),
                        contentDescription = null,
                        tint = Color.White
                    )
                }
                Text(
                    text = if (callState.callInConfirming) {
                        stringResource(id = R.string.cpd接通中)
                    } else {
                        if (canFreeCall) {
                            stringResource(id = R.string.cpd_free_answer)
                        } else {
                            stringResource(id = R.string.cpd_answer)
                        }
                    },
                    color = Color.White,
                    fontSize = 14.sp
                )
            }
        }
    }
}

@Preview
@Composable
private fun PreviewIncomeCallContentV2() {
    IncomeCallContentV2(
        CallState.Incoming(
            channelId = "",
            rtcToken = "",
            info = C2CCallInfo(
                callId = "",
                selfUser = userForPreview,
                targetUser = userForPreview.copy(
                    nickname = "阿哈哈哈哈哈哈",
                    shortIntro = "hello intro阿哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈啊hello intro阿哈哈哈哈哈哈",
                    height = 180
                )
            ),
            callInConfirming = false
        ),
        false
    )
}