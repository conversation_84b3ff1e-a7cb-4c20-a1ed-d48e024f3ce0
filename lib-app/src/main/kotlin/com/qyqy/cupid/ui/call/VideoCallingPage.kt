package com.qyqy.cupid.ui.call

import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.data.C2CCallInfo
import com.qyqy.cupid.data.CallState
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.widgets.autoDraggable
import com.qyqy.cupid.widgets.draggableToSide
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.enableClick
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.CircleComposeImage


@Composable
fun C2CVideoCallingPage(
    callState: CallState.ICalling<C2CCallInfo>,
    onAction: IVideoCallingAction,
    modifier: Modifier = Modifier,
    front: @Composable BoxScope.() -> Unit = {},
) {
    CallingScaffold(
        modifier = modifier
            .background(Color.Black)
            .enableClick(),
        background = {
            PrimaryVideoPreview(callState = callState, onAction = onAction)
            SecondaryVideoPreview(
                callState = callState,
                onAction = onAction,
                modifier = Modifier
                    .padding(top = 92.dp, end = 4.dp)
                    .size(94.dp, 202.dp)
                    .draggableToSide(orientation = Orientation.Horizontal, padding = 4.dp, enabled = true)
            )
        },
        front = front,
        topContent = {
            CallingTitleBar(callState = callState, showInCallTimer = true, onCollapse = onAction::collapse)
        },
        leadTopContent = {
            if (
                callState !is CallState.InCall
                || !callState.info.targetCameraEnable
                || !callState.info.targetJoined
                || !callState.info.targetHasCameraFrame
            ) {
                val user = callState.info.targetUser
                CallingTargetAvatar(
                    avatar = user.avatarUrl,
                    name = user.nickname,
                )
            }
        },
        leadBottomContent = {
            if (callState is CallState.InCall) {
                FreeCallTimer(callState.inCall, onAction)
            } else {
                callState as CallState.Outgoing
                Text(
                    text = callState.hintContent,
                    modifier = Modifier.padding(horizontal = 20.dp),
                    color = Color.White,
                    fontSize = 13.sp,
                    textAlign = TextAlign.Center,
                )
            }
        }
    ) {
        val buttons = remember(callState.info, onAction) {
            callState.info.getActionButtons(onAction)
        }
        CallingActionButtons(buttons = buttons)
    }
}

@Composable
private fun PrimaryVideoPreview(
    callState: CallState.ICalling<C2CCallInfo>,
    onAction: IVideoCallingAction,
) {
    val videoPreviewing = if (callState is CallState.InCall && callState.info.targetJoined) {
        callState.info.targetCameraEnable
    } else {
        callState.info.selfCameraEnable
    }

    val livePreview by rememberLiveVideoPreview(modifier = Modifier.fillMaxSize())

    if (videoPreviewing) {
        // 通话中，显示对方的预览
        if (livePreview != null) {
            val uid = if (callState is CallState.InCall && callState.info.targetJoined) {
                callState.info.targetUser.id
            } else {
                null
            }
            DisposableEffect(key1 = livePreview, key2 = uid) {
                onAction.toggleVideoPreview(uid, livePreview)
                onDispose {
                    onAction.toggleVideoPreview(uid, null)
                }
            }
        }
    }

    val showBackground = if (callState is CallState.InCall && callState.info.targetJoined) {
        !callState.info.targetCameraEnable || !callState.info.targetHasCameraFrame
    } else {
        !callState.info.selfCameraEnable
    }

    if (showBackground) {
        CallingBlurBackground(
            url = if (callState is CallState.InCall && callState.info.targetJoined) {
                callState.info.targetUser.avatarUrl
            } else {
                callState.info.selfUser.avatarUrl
            },
            modifier = Modifier.fillMaxSize()
        )
    }
}

@Composable
private fun BoxScope.SecondaryVideoPreview(
    callState: CallState.ICalling<C2CCallInfo>,
    onAction: IVideoCallingAction,
    modifier: Modifier,
) {
    if (callState is CallState.InCall && callState.info.targetJoined && callState.info.selfCameraEnable) {
        val dragLivePreview by rememberLiveVideoPreview(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .then(modifier)
        )
        if (dragLivePreview != null) {
            DisposableEffect(key1 = dragLivePreview) {
                onAction.toggleVideoPreview(null, dragLivePreview)
                onDispose {
                    onAction.toggleVideoPreview(null, null)
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewC2CVideoCallingPage() {
    C2CVideoCallingPage(
        callState = CallState.Outgoing(
            channelId = "",
            rtcToken = "",
            info = C2CCallInfo(
                callId = "",
                selfUser = userForPreview,
                targetUser = userForPreview
            ),
            callInConfirming = false
        ),
        onAction = IVideoCallingAction.Empty,
    )
}



@Composable
fun C2CVideoCallingFloat(
    callState: CallState.ICalling<C2CCallInfo>,
    onAction: IVideoCallingAction,
    modifier: Modifier = Modifier,
) {
    ElevatedCard(
        modifier = modifier
            .size(84.dp, 126.dp)
            .noEffectClickable(enabled = callState.collapsed) {
                onAction.expand()
            },
        shape = Shapes.corner12,
        colors = CardDefaults.elevatedCardColors(
            containerColor = Color.Black
        ),
        elevation = CardDefaults.elevatedCardElevation(
            defaultElevation = 3.dp
        ),
    ) {
        Box(
            modifier = Modifier.fillMaxWidth(),
            contentAlignment = Alignment.BottomCenter
        ) {
            PrimaryVideoPreview(callState, onAction)

            if (
                callState !is CallState.InCall
                || !callState.info.targetCameraEnable
                || !callState.info.targetJoined
                || !callState.info.targetHasCameraFrame
            ) {
                CircleComposeImage(
                    model = callState.info.targetUser.avatarUrl,
                    modifier = Modifier
                        .align(Alignment.Center)
                        .size(48.dp)
                )
            }

            SecondaryVideoPreview(
                callState = callState,
                onAction = onAction,
                modifier = Modifier
                    .padding(top = 2.dp, end = 2.dp)
                    .clip(Shapes.corner12)
                    .size(36.dp, 36.dp)
            )

            if (callState is CallState.InCall) {
                InCallTimer(
                    inCall = callState.inCall,
                    modifier = Modifier
                        .padding(horizontal = 12.dp)
                        .padding(bottom = 10.dp),
                    style = LocalTextStyle.current.copy(
                        color = Color.White,
                        fontSize = 12.sp
                    )
                )
            } else {
                callState as CallState.Outgoing
                AutoSizeText(
                    text = if (callState.callInConfirming) {
                        stringResource(id = R.string.cpd接通中)
                    } else {
                        stringResource(id = R.string.cpd等待接听)
                    },
                    modifier = Modifier
                        .padding(horizontal = 6.dp)
                        .padding(bottom = 10.dp),
                    color = Color.White,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    alignment = Alignment.Center,
                )
            }
        }
    }
}

@Preview
@Composable
private fun PreviewC2CVideoCallingFloat() {
    PreviewCupidTheme {
        Box(modifier = Modifier.fillMaxSize()) {
            C2CVideoCallingFloat(
                callState = CallState.Outgoing(
                    channelId = "",
                    rtcToken = "",
                    info = C2CCallInfo(
                        callId = "",
                        selfUser = userForPreview,
                        targetUser = userForPreview
                    ),
                    callInConfirming = false
                ),
                onAction = IVideoCallingAction.Empty,
            )
        }
    }
}