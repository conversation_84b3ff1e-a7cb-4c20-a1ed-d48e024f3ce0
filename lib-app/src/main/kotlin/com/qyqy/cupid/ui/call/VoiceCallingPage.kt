package com.qyqy.cupid.ui.call

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.data.C2CCallInfo
import com.qyqy.cupid.data.CallState
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.enableClick
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AutoSizeText

@Composable
fun C2CVoiceCallingPage(
    callState: CallState.ICalling<C2CCallInfo>,
    onAction: IVoiceCallingAction,
    modifier: Modifier = Modifier,
    front: @Composable BoxScope.() -> Unit = {},
) {
    CallingScaffold(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black)
            .enableClick(),
        background = {
            CallingBlurBackground(
                url = callState.info.targetUser.avatarUrl,
                modifier = Modifier.fillMaxSize()
            )
        },
        front = front,
        topContent = {
            CallingTitleBar(callState = callState, showInCallTimer = true, onCollapse = onAction::collapse)
        },
        leadTopContent = {
            val user = callState.info.targetUser
            CallingTargetAvatar(
                avatar = user.avatarUrl,
                name = user.nickname,
            )
        },
        leadBottomContent = {
            if (callState is CallState.InCall) {
                FreeCallTimer(callState.inCall, onAction)
            } else {
                callState as CallState.Outgoing
                Text(
                    text = callState.hintContent,
                    modifier = Modifier.padding(horizontal = 20.dp),
                    color = Color.White,
                    fontSize = 13.sp,
                    textAlign = TextAlign.Center,
                )
            }
        }
    ) {
        val buttons = remember(callState.info, onAction) {
            callState.info.getActionButtons(onAction)
        }
        CallingActionButtons(buttons = buttons)
    }
}

@Preview
@Composable
private fun PreviewC2CVoiceCallingPage() {
    C2CVoiceCallingPage(
        callState = CallState.Outgoing(
            channelId = "",
            rtcToken = "",
            info = C2CCallInfo(
                callId = "",
                selfUser = userForPreview,
                targetUser = userForPreview
            ),
            false,
        ),
        onAction = IVoiceCallingAction.Empty,
    )
}

@Composable
fun C2CVoiceCallingFloat(
    callState: CallState.ICalling<C2CCallInfo>,
    onAction: IVoiceCallingAction,
    modifier: Modifier = Modifier,
) {
    ElevatedCard(
        modifier = modifier
            .size(74.dp, 74.dp)
            .noEffectClickable(enabled = callState.collapsed) {
                onAction.expand()
            },
        shape = Shapes.corner12,
        colors = CardDefaults.elevatedCardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.elevatedCardElevation(
            defaultElevation = 3.dp
        ),
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(5.dp, Alignment.CenterVertically)
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_call_in),
                modifier = Modifier.size(24.dp),
                contentDescription = null,
                tint = Color(0xFF0FC363)
            )
            if (callState is CallState.InCall) {
                InCallTimer(
                    inCall = callState.inCall,
                    modifier = Modifier.padding(horizontal = 12.dp),
                    style = LocalTextStyle.current.copy(
                        color = Color(0xFF0FC363),
                        fontSize = 14.sp
                    )
                )
            } else {
                callState as CallState.Outgoing
                AutoSizeText(
                    text = if (callState.callInConfirming) {
                        stringResource(id = R.string.cpd接通中)
                    } else {
                        stringResource(id = R.string.cpd等待接听)
                    },
                    modifier = Modifier.padding(horizontal = 6.dp),
                    color = Color(0xFF0FC363),
                    fontSize = 13.sp,
                    fontWeight = FontWeight.Medium,
                    alignment = Alignment.Center,
                    maxLines = 2
                )
            }
        }
    }
}

@Preview
@Composable
private fun PreviewC2CVoiceCallingFloat() {
    PreviewCupidTheme {
        Box(modifier = Modifier.fillMaxSize()) {
            C2CVoiceCallingFloat(
                callState = CallState.Outgoing(
                    channelId = "",
                    rtcToken = "",
                    info = C2CCallInfo(
                        callId = "",
                        selfUser = userForPreview,
                        targetUser = userForPreview
                    ),
                    callInConfirming = false
                ),
                onAction = IVoiceCallingAction.Empty,
            )
        }
    }
}