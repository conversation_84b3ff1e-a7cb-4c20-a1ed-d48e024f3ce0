package com.qyqy.cupid.ui.call

import android.os.Parcelable
import android.os.SystemClock
import android.view.TextureView
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.viewmodel.compose.viewModel
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.qyqy.cupid.data.C2CCallInfo
import com.qyqy.cupid.data.CallState
import com.qyqy.cupid.data.InCallInfo
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.CupidViewModel
import com.qyqy.cupid.ui.dialog.AlertDialogContent2
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.dialog.SimpleDialog
import com.qyqy.cupid.ui.profile.RechargeDialog
import com.qyqy.ucoo.R
import com.qyqy.ucoo.asComponentActivity
import com.qyqy.ucoo.compose.keepLastNonNullState
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.multilingual.currentLocale
import com.qyqy.ucoo.utils.OnClick
import com.qyqy.ucoo.utils.blur.BlurTransformation
import jp.wasabeef.glide.transformations.ColorFilterTransformation
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.parcelize.Parcelize
import java.util.Locale
import kotlin.math.roundToLong

@Composable
fun FreeCallTimer(
    inCall: InCallInfo,
    onAction: IVoiceCallingAction,
    modifier: Modifier = Modifier,
    style: TextStyle = LocalTextStyle.current.copy(color = Color.White, fontSize = 12.sp),
) {
    if (!inCall.showFreeCallTime) {
        return
    }
    CallTimer(isCountdown = true, elapsedRealtime = inCall.freeCallFinishElapsedRealtime) { left ->
        val text = stringResource(id = R.string.cpd免费通话时长还剩, left.plus(500).div(1000))

        AutoSizeText(text = buildAnnotatedString {
            append(text)
            "\\d+".toRegex().find(text)?.also {
                addStyle(SpanStyle(color = Color(0xFFFF5E8B)), it.range.first, it.range.last.plus(1))
            }
        }, modifier = modifier, style = style)

        if (left <= 0) {
            LaunchedEffect(key1 = Unit) {
                onAction.freeTimeOver()
            }
        }
    }
}

@Composable
fun InCallTimer(
    inCall: InCallInfo,
    modifier: Modifier = Modifier,
    style: TextStyle = LocalTextStyle.current.copy(color = Color.White, fontSize = 14.sp),
) {
    val locale = LocalConfiguration.current.currentLocale
    CallTimer(isCountdown = false, elapsedRealtime = inCall.startElapsedRealtime) {
        AutoSizeText(text = it.formatMillisecondsToTimeString(locale), modifier = modifier, style = style)
    }
}

@Composable
inline fun CallTimer(
    isCountdown: Boolean,
    elapsedRealtime: Long,
    onTimer: @Composable (Long) -> Unit,
) {

    val calculate = {
        if (isCountdown) {
            elapsedRealtime.minus(SystemClock.elapsedRealtime())
        } else {
            SystemClock.elapsedRealtime().minus(elapsedRealtime)
        }.coerceAtLeast(0)
    }

    var time by remember(isCountdown, elapsedRealtime) {
        mutableLongStateOf(calculate())
    }
    LaunchedEffect(key1 = isCountdown, key2 = elapsedRealtime) {
        while (isActive) {
            if (isCountdown && time < 800) {
                delay(time)
                time = 0
                break
            } else {
                delay(1000)
            }
            time = calculate()
        }
    }
    onTimer(time)
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CallingTitleBar(callState: CallState.ICalling<*>, showInCallTimer: Boolean, onCollapse: OnClick = {}) {
    CenterAlignedTopAppBar(
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent),
        title = {
            if (callState is CallState.InCall<*>) {
                if (showInCallTimer) {
                    InCallTimer(callState.inCall)
                }
            } else {
                callState as CallState.Outgoing
                if (callState.callInConfirming) {
                    Text(text = stringResource(id = R.string.cpd接通中), fontSize = 14.sp, color = Color.White)
                } else {
                    Text(text = stringResource(id = R.string.cpd正在等对方应答), fontSize = 14.sp, color = Color.White)
                }
            }
        },
        navigationIcon = {
            IconButton(onClick = onCollapse) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_smaller),
                    contentDescription = "collapse",
                    tint = Color.White,
                )
            }
        },
    )
}

fun Long.formatMillisecondsToTimeString(locale: Locale): String {
    if (this <= 0) {
        return "00:00"
    }
    // 将毫秒转换为秒，并进行四舍五入
    val totalSeconds = (this / 1000.0).roundToLong()

    // 计算小时和分钟
    val minutes = totalSeconds / 60
    val seconds = totalSeconds % 60

    // 返回格式化后的字符串
    return String.format(locale, "%02d:%02d", minutes, seconds)
}

@Preview
@Composable
private fun PreviewCallingTitleBar() {
    PreviewCupidTheme {
        Surface(color = Color(0xFF19130A)) {
            CallingTitleBar(
                CallState.Outgoing(
                    channelId = "",
                    rtcToken = "",
                    info = C2CCallInfo(
                        callId = "",
                        selfUser = userForPreview,
                        targetUser = userForPreview
                    ),
                    callInConfirming = false
                ),
                false
            )
        }
    }
}


@Composable
fun ColumnScope.CallingTargetAvatar(avatar: String, name: String) {
    CircleComposeImage(
        model = avatar,
        modifier = Modifier.size(96.dp)
    )

    Text(
        text = name,
        modifier = Modifier.padding(top = 8.dp, start = 20.dp, end = 20.dp),
        fontSize = 20.sp,
        color = Color.White,
        textAlign = TextAlign.Center,
    )
}

@Preview
@Composable
private fun PreviewCallingTargetAvatar() {
    PreviewCupidTheme {
        Surface(color = Color(0xFF19130A)) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                CallingTargetAvatar(
                    avatar = userForPreview.avatarUrl,
                    name = userForPreview.nickname
                )
            }
        }
    }
}

@Composable
fun CallingActionButtons(buttons: Array<ActionButtonItem>) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 10.dp),
        horizontalArrangement = Arrangement.SpaceAround
    ) {
        buttons.forEach {
            Column(
                modifier = Modifier.noEffectClickable(enabled = it.enabled, onClick = it.onClick),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Image(
                    painter = painterResource(id = it.icon),
                    contentDescription = null,
                    modifier = Modifier.size(68.dp)
                )
                Spacer(modifier = Modifier.height(6.dp))
                AutoSizeText(
                    text = stringResource(id = it.text),
                    modifier = Modifier.size(76.dp, 24.dp),
                    fontSize = 14.sp,
                    color = if (it.enabled) Color.White else Color.White.copy(alpha = 0.5f),
                    alignment = Alignment.Center,
                )
            }
        }
    }
}


@Preview
@Composable
private fun PreviewCallingActionButtons() {
    PreviewCupidTheme {
        Surface(color = Color(0xFF19130A)) {
            CallingActionButtons(
                arrayOf(
                    ActionButtonItem(R.drawable.ic_call_on_camera, R.string.hang_up),
                    ActionButtonItem(R.drawable.ic_call_on_camera, R.string.hang_up),
                )
            )
        }
    }
}


@Composable
fun CallingBlurBackground(url: String, modifier: Modifier = Modifier) {
    val context = LocalContext.current
    ComposeImage(
        model = url,
        modifier = modifier,
        loading = null,
    ) {
        it.transform(
            CenterCrop(),
            BlurTransformation(10, 16),
            ColorFilterTransformation(context.getColor(R.color.black_alpha_50))
        )
    }
}

@Preview
@Composable
private fun PreviewCallingBlurBackground() {
    CallingBlurBackground("")
}


@Composable
fun rememberLiveVideoPreview(modifier: Modifier = Modifier): State<TextureView?> {
    val liveVideoPreview = remember {
        mutableStateOf<TextureView?>(null)
    }
    AndroidView(
        factory = {
            TextureView(it).apply {
                liveVideoPreview.value = this
            }
        },
        modifier = modifier
    )
    return liveVideoPreview
}

@Parcelize
data object NotEnoughBalanceForCallDialog : SimpleDialog(), Parcelable {

    override val properties: DialogProperties
        get() = DialogProperties(dismissOnClickOutside = false)

    @Composable
    override fun Content(dialog: IDialog) {

        val activity = LocalContext.current.asComponentActivity!!

        val viewModel = viewModel<CupidViewModel>(viewModelStoreOwner = activity)

        val c2cCallingHelper = viewModel.c2cCallingHelper

        val c2cCallState = c2cCallingHelper.rememberC2cCallState()

        val _state = c2cCallState.value as? CallState.InCall

        val nonNullState = keepLastNonNullState(_state)

        val state = if (_state == null) {
            LaunchedEffect(key1 = Unit) {
                dialog.dismiss()
            }
            if (nonNullState == null) {
                return
            }
            nonNullState
        } else {
            _state
        }

        val dialogQueue = LocalDialogQueue.current
        val inCall = state.inCall
        CallTimer(isCountdown = true, elapsedRealtime = inCall.payCallFinishElapsedRealtime) { left ->
            val text = stringResource(
                id = R.string.cpd您的账户余额仅能支持您再通话,
                left.plus(500).div(1000),
                state.inCall.coinPayCallPerMinute
            )
            AlertDialogContent2(
                icon = null,
                title = buildAnnotatedString {
                    append(stringResource(id = R.string.cpd金币余额不足))
                },
                content = buildAnnotatedString {
                    append(text)
                    "\\d+".toRegex().find(text)?.also {
                        addStyle(SpanStyle(color = Color(0xFFFF5E8B)), it.range.first, it.range.last.plus(1))
                    }
                },
                topButtonText = stringResource(id = R.string.cpd立即充值),
                topButtonClick = {
                    dialogQueue.push(RechargeDialog, true)
                },
                bottomButtonText = stringResource(id = R.string.cpd以后再说),
                bottomButtonClick = {
                    dialog.dismiss()
                },
            )

            if (left <= 0) {
                dialog.dismiss()
            }
        }
    }
}




