package com.qyqy.cupid.ui.coin

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.ui.NavListener
import com.qyqy.cupid.ui.home.HomeSubPage
import com.qyqy.cupid.utils.eventbus.EventBusListener
import com.qyqy.cupid.widgets.CpdAppTabRow
import com.qyqy.ucoo.R
import com.qyqy.ucoo.ReportExposureCompose
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.compose.ui.AppTab
import kotlinx.coroutines.launch

@Composable
fun CoinModuleTab() {

    val scope = rememberCoroutineScope()
    val pagerState = rememberPagerState {
        2
    }
    val context = LocalContext.current
    val titles = remember(context) {
        listOf(AppTab(context.getString(R.string.cupid_income)), AppTab(context.getString(R.string.cupid_gold)))
    }

//    diamond2cash
    val listener = remember(scope) {
        object : NavListener(HOME_SUB_NAV) {
            override fun handleNav(route: String): Boolean {
                when (route) {
                    HomeSubPage.Income.t.toString(), "diamond2cash" -> {
                        scope.launch {
                            pagerState.scrollToPage(0)
                        }
                    }

                    HomeSubPage.Gold.t.toString() -> {
                        scope.launch {
                            pagerState.scrollToPage(1)
                        }
                    }

                    else -> {}
                }
                return false
            }
        }
    }
    EventBusListener(listener = listener)

    Column(
        modifier = Modifier
            .paint(
                painter = painterResource(id = R.drawable.cupid_header_home),
                contentScale = ContentScale.FillWidth,
                alignment = Alignment.TopCenter
            )
            .fillMaxSize()
            .statusBarsPadding()
    ) {

        Box(
            modifier = Modifier.fillMaxWidth(),
            contentAlignment = Alignment.TopCenter
        ) {
            CpdAppTabRow(
                tabs = titles,
                pagerState = pagerState,
                indicatorColor = CpdColors.FFFF5E8B,
                tabSelectedColor = CpdColors.FF1D2129,
                tabUnSelectedColor = CpdColors.FF86909C,
                modifier = Modifier.fillMaxWidth(0.4f)
            )
        }

        HorizontalPager(
            state = pagerState,
            modifier = Modifier
                .padding(top = 10.dp)
                .fillMaxSize()
        ) { pageIndex ->
            when (pageIndex) {
                0 -> IncomePage()
                else -> ReportExposureCompose(exposureName = TracePoints.COIN_VISIT){
                    CoinPage()
                }
            }
        }
    }

}