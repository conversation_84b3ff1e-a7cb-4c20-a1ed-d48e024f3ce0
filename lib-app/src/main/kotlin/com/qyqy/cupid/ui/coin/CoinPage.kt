@file:OptIn( ExperimentalLayoutApi::class, ExperimentalMaterial3Api::class)

package com.qyqy.cupid.ui.coin

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ElevatedButton
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.data.UserCoinConfig
import com.qyqy.cupid.model.CoinViewModel
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.dialog.CupidTaskRewardDialog
import com.qyqy.cupid.ui.dialog.LineNumCopyDialogContent
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.global.BannerView
import com.qyqy.cupid.ui.global.ScreenLocation
import com.qyqy.cupid.ui.profile.RechargeDialog
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.presentation.sign_tasks.Task
import com.qyqy.ucoo.compose.presentation.sign_tasks.TaskSeries
import com.qyqy.ucoo.compose.state.PullRefreshBox
import com.qyqy.ucoo.compose.theme.D_DIN
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.http.getIntOrNull
import com.qyqy.ucoo.sUserFlow
import eu.bambooapps.material3.pullrefresh.PullRefreshIndicatorDefaults
import kotlinx.coroutines.launch
import kotlinx.serialization.json.jsonPrimitive

/**
 *  @time 2024/7/26
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.coin
 *  tab 2 金币页, 包含签到什么的
 */

//region 事件
sealed interface ICoinAction

@Stable
sealed interface CoinAction : ICoinAction {

    data object DailySignIn : CoinAction
    data class GoNewbieTask(val task: Task) : CoinAction
    data class GoGameTask(val game: UserCoinConfig.Game.Item) : CoinAction
    data object Gocharge : CoinAction
    data object GoChargeDetail : CoinAction // 明细记录
}
//endregion

/**
 * 日区金币页(一般情况下男性用户会进入此页)
 *
 */
@Composable
fun CoinPage(modifier: Modifier = Modifier) {
    val viewModel = viewModel<CoinViewModel>()

    val loadingFlag = LocalContentLoading.current
    val controller = LocalAppNavController.current
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    val user by sUserFlow.collectAsStateWithLifecycle()

    val isRefreshing by viewModel.refreshFlow.collectAsStateWithLifecycle()

    val coinConfig = viewModel.mCoinConfig.collectAsStateWithLifecycle()

    val tasksInfos by remember {
        derivedStateOf {
            viewModel.allSeriesTasks.filter { it.seriesType != TaskSeries.SERIES_TYPE_SIGN }
        }
    }

    val signInfo by remember {
        derivedStateOf {
            viewModel.allSeriesTasks.find { it.seriesType == TaskSeries.SERIES_TYPE_SIGN } ?: TaskSeries()
        }
    }

    val dialogQueue = LocalDialogQueue.current

    PullRefreshBox(
        modifier = Modifier
            .fillMaxSize()
            .then(modifier),
        isRefreshing = isRefreshing,
        onRefresh = { viewModel.refresh() },
        colors = PullRefreshIndicatorDefaults.colors(
            contentColor = MaterialTheme.colorScheme.primary,
            containerColor = Color.White
        )
    ) {
        CoinPageWidget(
            user,
            signInfo = signInfo,
            tasksInfos = tasksInfos,
            coinConfig = coinConfig.value,
            onHelp = {
                scope.launch {
                    viewModel.getLineHelp()?.apply {
                        dialogQueue.pushCenterDialog { dialog, _ ->
                            LineNumCopyDialogContent(
                                title = title,
                                content = content,
                                hintTitle = tips,
                                lineId = lineId
                            ) {
                                dialog.dismiss()
                            }
                        }
                    }
                }
            },
        ) { action ->
            when (action) {
                CoinAction.DailySignIn -> {
                    scope.launch {
                        loadingFlag.value = true
                        viewModel.taskManager.startSign(signInfo.seriesId) {
                            val title = it.get("message")?.jsonPrimitive?.content ?: ""
                            val info = it.get("prize_info")?.jsonPrimitive?.content ?: ""
                            val type = it.getIntOrNull("prize_type") ?: 0
                            dialogQueue.push(
                                CupidTaskRewardDialog(
                                    if (type == 5) R.drawable.ic_cpd_task_coin else R.drawable.ic_cpd_task_diamond,
                                    info,
                                    title
                                )
                            )
                        }
                        loadingFlag.value = false
                    }
                }

                is CoinAction.GoGameTask -> {
                    Analytics.reportClickEvent(TracePoints.COIN_GAME_CLICK, extra = "{\"link\":\"${action.game.link}\"}")
                    controller.navigateWeb(action.game.link)
                }

                is CoinAction.GoNewbieTask -> {
                    Analytics.reportClickEvent(TracePoints.COIN_TASK_CLICK, extra = "{\"id\":\"${action.task.id}\"}")
                    scope.launch {
                        loadingFlag.value = true
                        viewModel.taskManager.gotoCompleteTask(action.task.id)
                        loadingFlag.value = false
                    }
                }

                CoinAction.Gocharge -> {
                    Analytics.reportClickEvent(TracePoints.COIN_CHARGE_CLICK)
                    dialogQueue.push(RechargeDialog)
                }

                CoinAction.GoChargeDetail -> {
                    controller.navigate(CupidRouters.TOPUP_HISTORY)
                }
            }
            true
        }
    }
}

@Composable
private fun CoinPageWidget(
    user: User,
    signInfo: TaskSeries,
    tasksInfos: List<TaskSeries>,
    coinConfig: UserCoinConfig,
    onHelp: () -> Unit = {},
    onAction: (ICoinAction) -> Any = {},
) {
    val dialogQueue = LocalDialogQueue.current

    Box {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            // 1. 我的金币持有
            Box(
                modifier = Modifier
                    .padding(start = 16.dp, end = 16.dp)
                    .background(
                        color = Color.White,
                        shape = RoundedCornerShape(12.dp)
                    )
            ) {
                ConstraintLayout(
                    modifier = Modifier
                        .padding(
                            horizontal = 14.dp,
                            vertical = 20.dp
                        )
                        .fillMaxWidth()
                ) {
                    val (coinIcon, coinTitle, coinNumber, coinButton, coinDetail) = createRefs()
                    ComposeImage(
                        model = R.drawable.ic_cpd_coin,
                        modifier = Modifier
                            .constrainAs(coinIcon) {
                                top.linkTo(parent.top)
                                start.linkTo(parent.start)
                                bottom.linkTo(parent.bottom)
                            }
                            .size(48.dp)
                    )

                    Text(
                        text = stringResource(id = R.string.cupid_mine_gold),
                        style = MaterialTheme.typography.titleMedium.copy(
                            color = colorResource(id = R.color.FF1D2129),
                        ),
                        modifier = Modifier.constrainAs(coinTitle) {
                            start.linkTo(coinIcon.end, margin = 14.dp)
                            top.linkTo(coinIcon.top, margin = -8.dp)
                        }
                    )

                    AutoSizeText(
                        text = user.balance.toString(),
                        style = TextStyle(
                            color = colorResource(id = R.color.FF1D2129),
                            fontWeight = FontWeight.Bold,
                            fontSize = 32.sp,
                            fontFamily = FontFamily.D_DIN,
                        ),
                        modifier = Modifier.constrainAs(coinNumber) {
                            start.linkTo(coinTitle.start)
                            end.linkTo(coinDetail.start, margin = 10.dp)
                            bottom.linkTo(coinIcon.bottom, margin = (-8).dp)
                            width = Dimension.fillToConstraints
                        },
                        maxLines = 1,
                        maxTextSize = 32.sp,
                        lineHeight = 32.sp
                    )

                    ElevatedButton(
                        onClick = {
                            onAction(CoinAction.Gocharge)
                        },
                        modifier = Modifier
                            .constrainAs(coinButton) {
                                top.linkTo(coinIcon.top, margin = -8.dp)
                                end.linkTo(parent.end)
                            }
                            .height(28.dp),
                        contentPadding = PaddingValues(horizontal = 8.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(id = R.color.FFFF5E8B),
                            contentColor = Color.White
                        )
                    ) {
                        Text(text = stringResource(id = R.string.cpd_charge))
                    }

                    Row(
                        modifier = Modifier
                            .constrainAs(coinDetail) {
                                bottom.linkTo(coinIcon.bottom, margin = -4.dp)
                                end.linkTo(parent.end)
                            }
                            .click {
                                onAction(CoinAction.GoChargeDetail)
                            },
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = stringResource(id = R.string.cupid_income_exchange_history),
                            style = MaterialTheme.typography.labelMedium.copy(
                                color = colorResource(id = R.color.FF86909C),
                            )
                        )
                        ComposeImage(model = R.drawable.ic_cpd_arrow_right)
                    }
                }
            }

            BannerView(
                location = ScreenLocation.GOLD,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .padding(top = 8.dp)
            )

            AnimatedVisibility(visible = signInfo.tasks.isNotEmpty()) {
                // 2. 签到领金币
                SignInWidget(signInfo, modifier = Modifier.padding(top = 16.dp)) { // 点击签到领金币按钮时
                    Analytics.reportClickEvent(TracePoints.COIN_SIGN_CLICK)
                    dialogQueue.pushCenterDialog { dialog, _ ->
                        SigninDialogContent(task = signInfo, onDismiss = { }) {
                            if (onAction(CoinAction.DailySignIn) as Boolean) {
                                dialog.dismiss()
                            }
                        }
                    }
                }
            }

            tasksInfos.forEach { info ->
                AnimatedVisibility(visible = info.tasks.isNotEmpty()) {
//                Spacer(modifier = Modifier.height(16.dp))

                    // 3.做任务领金币
                    NewbieTaskWidget(
                        info,
                        modifier = Modifier.padding(top = 16.dp),
                        onHelp = onHelp,
                    ) { task ->
                        onAction(CoinAction.GoNewbieTask(task))
                    }
                }
            }

            AnimatedVisibility(visible = coinConfig.gameEnabled) {
                // 4.玩游戏领金币
                GameCoinWidget(coinConfig.game, modifier = Modifier.padding(top = 16.dp)) {
                    onAction(CoinAction.GoGameTask(it))
                }
            }
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

//region preview

@Composable
@Preview(
    "金币页面",
    widthDp = 375,
    heightDp = 812,
    locale = "ja-rJP",
)
private fun CoinScreenPreview() {
    CoinPageWidget(
        user = userForPreview,
        TaskSeries(),
        coinConfig = UserCoinConfig(gameEnabled = true),
        tasksInfos = listOf()
    )
}

//endregion
