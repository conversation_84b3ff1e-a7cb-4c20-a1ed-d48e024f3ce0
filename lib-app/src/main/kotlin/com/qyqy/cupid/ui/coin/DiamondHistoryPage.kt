package com.qyqy.cupid.ui.coin

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.qyqy.ucoo.R

@Composable
fun DiamondHistoryPage() {
    TypeRecordHistoryPage(title = stringResource(id = R.string.cpd钻石明细记录), type = 22)
}

@Composable
@Preview(showBackground = true)
private fun TopupDiamondHistoryPage() {
    DiamondHistoryPage()
}