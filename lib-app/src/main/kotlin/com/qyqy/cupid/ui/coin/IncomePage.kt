@file:OptIn( ExperimentalMaterial3Api::class)

package com.qyqy.cupid.ui.coin

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ElevatedButton
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.data.UserIncomeConfig
import com.qyqy.cupid.model.IncomeViewModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.theme.CupidTheme
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.NavListener
import com.qyqy.cupid.ui.dialog.CupidTaskRewardDialog
import com.qyqy.cupid.ui.dialog.LineNumCopyDialogContent
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.dialog.WithdrawDialog
import com.qyqy.cupid.ui.global.BannerView
import com.qyqy.cupid.ui.global.ScreenLocation
import com.qyqy.cupid.utils.eventbus.EventBusListener
import com.qyqy.cupid.widgets.click
import com.qyqy.cupid.widgets.composeClick
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.accountManager
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.presentation.game.ExchangeItem
import com.qyqy.ucoo.compose.presentation.sign_tasks.Task
import com.qyqy.ucoo.compose.presentation.sign_tasks.TaskSeries
import com.qyqy.ucoo.compose.state.PullRefreshBox
import com.qyqy.ucoo.compose.ui.AnimatedDialog
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.VerticalGrid
import com.qyqy.ucoo.http.getIntOrNull
import com.qyqy.ucoo.sUserFlow
import com.qyqy.ucoo.toastRes
import eu.bambooapps.material3.pullrefresh.PullRefreshIndicatorDefaults
import kotlinx.coroutines.launch
import kotlinx.serialization.json.jsonPrimitive


/**
 *  @time 2024/7/26
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.coin
 */

//region 事件
@Stable
sealed interface IncomeAction {
    data object Withdraw : IncomeAction
    data class Diamoind2Coin(val option: ExchangeItem) : IncomeAction
    data class Diamoind2Cash(val option: ExchangeItem) : IncomeAction

    data class GoInvite(val url: String) : IncomeAction
    data object DailySignIn : IncomeAction
    data class NewbieTaskEvent(val task: Task) : IncomeAction
    data object GoChargeDetail : IncomeAction
    data object GoWithdrawDetail : IncomeAction

    data object showLoading : IncomeAction
    data object hideLoading : IncomeAction
}
//endregion

/**
 * 日区收益页(一般只为女性用户显示)
 */
@Composable
fun IncomePage(modifier: Modifier = Modifier) {
    val viewModel = viewModel<IncomeViewModel>()

    val scope = rememberCoroutineScope()
    val loadingFlag = LocalContentLoading.current
    val user by sUserFlow.collectAsStateWithLifecycle()
    val controller = LocalAppNavController.current

    val isRefreshing by viewModel.refreshFlow.collectAsStateWithLifecycle()

    val incomeConfig = viewModel.mIncomeConfig.collectAsStateWithLifecycle()
    val diamondExchangeList = viewModel.diamondExchangeList.collectAsStateWithLifecycle()

    val tasksInfos by remember {
        derivedStateOf {
            viewModel.allSeriesTasks.filter { it.seriesType != TaskSeries.SERIES_TYPE_SIGN }
        }
    }

    val signInfo by remember {
        derivedStateOf {
            viewModel.allSeriesTasks.find { it.seriesType == TaskSeries.SERIES_TYPE_SIGN } ?: TaskSeries()
        }
    }


    val dialogQueue = LocalDialogQueue.current

    PullRefreshBox(
        modifier = Modifier
            .fillMaxSize()
            .then(modifier),
        isRefreshing = isRefreshing,
        onRefresh = { viewModel.refresh() },
        colors = PullRefreshIndicatorDefaults.colors(
            contentColor = MaterialTheme.colorScheme.primary,
            containerColor = Color.White
        )
    ) {
        IncomePageWidget(
            user,
            signInfo = signInfo,
            tasksInfos = tasksInfos,
            incomeConfig = incomeConfig.value,
            diamondExchangeList = diamondExchangeList.value,
            onHelp = {
                scope.launch {
                    viewModel.getLineHelp()?.apply {
                        dialogQueue.pushCenterDialog { dialog, _ ->
                            LineNumCopyDialogContent(
                                title = title,
                                content = content,
                                hintTitle = tips,
                                lineId = lineId
                            ) {
                                dialog.dismiss()
                            }
                        }
                    }
                }
            }
        ) { action ->
            when (action) {
                IncomeAction.DailySignIn -> {
                    scope.launch {
                        loadingFlag.value = true
                        viewModel.taskManager.startSign(signInfo.seriesId) {
                            val title = it.get("message")?.jsonPrimitive?.content ?: ""
                            val info = it.get("prize_info")?.jsonPrimitive?.content ?: ""
                            val type = it.getIntOrNull("prize_type") ?: 0
                            dialogQueue.push(
                                CupidTaskRewardDialog(
                                    if (type == 5) R.drawable.ic_cpd_task_coin else R.drawable.ic_cpd_task_diamond,
                                    info, title
                                )
                            )
                        }
                        loadingFlag.value = false
                    }
                    return@IncomePageWidget true
                }

                is IncomeAction.Withdraw -> {
                    dialogQueue.push(WithdrawDialog)
                }

                is IncomeAction.Diamoind2Cash -> {
                    scope.launch {
                        loadingFlag.value = true
                        viewModel.exchangeDiamond(action.option).onSuccess { json ->
                            json.getIntOrNull("diamond_balance")?.let { diamond ->
                                //因为返回的是个int, 而这个字段在8.7日被改成了string, 不能直接加了,直接刷新去取吧
                                accountManager.refreshSelfUserByRemote()
//                                app.accountManager.updateSelfUser {
//                                    this.diamond = diamond
//                                    this.cash += action.option.number
//                                }
                                toastRes(R.string.cupid_exchange_success)
                            }
                        }.toastError()
                        loadingFlag.value = false
                    }
                    return@IncomePageWidget true
                }

                is IncomeAction.Diamoind2Coin -> {
                    scope.launch {
                        loadingFlag.value = true
                        viewModel.exchangeDiamond(action.option).onSuccess { json ->
                            json.getIntOrNull("diamond_balance")?.let { diamond ->
//                                app.accountManager.updateSelfUser {
//                                    this.diamond = diamond
//                                }
                                //这个地方也刷新好了,因为没有返回金币字段, 其他地方都不刷新
                                accountManager.refreshSelfUserByRemote()
                                toastRes(R.string.cupid_exchange_success)
                            }
                        }.toastError()
                        loadingFlag.value = false
                    }
                    return@IncomePageWidget true
                }

                IncomeAction.GoChargeDetail -> {
                    controller.navigate(CupidRouters.DIAMOND_HISTORY)
                }

                is IncomeAction.NewbieTaskEvent -> {
                    scope.launch {
                        loadingFlag.value = true
                        viewModel.taskManager.gotoCompleteTask(action.task.id)
                        loadingFlag.value = false
                    }
                }

                is IncomeAction.GoInvite -> {
                    controller.navigateWeb(action.url)
                }

                is IncomeAction.showLoading -> {
                    loadingFlag.value = true
                }

                is IncomeAction.hideLoading -> {
                    loadingFlag.value = false
                }

                IncomeAction.GoWithdrawDetail -> {
                    controller.navigate(CupidRouters.WITHDRAW_RECORDS)
                }
            }
            false
        }

    }

}


@Composable
private fun IncomePageWidget(
    user: User,
    signInfo: TaskSeries,
    tasksInfos: List<TaskSeries>,
    diamondExchangeList: List<ExchangeItem> = listOf(),
    incomeConfig: UserIncomeConfig? = UserIncomeConfig(),
    onHelp: () -> Unit = {},
    onAction: (IncomeAction) -> Any = {},
) {
    val scope = rememberCoroutineScope()
    //控制钻石兑换金币弹窗
    var isShowDiamond2CashDialog by remember {
        mutableStateOf(false)
    }
    //控制钻石兑换金币弹窗
    var isShowDiamond2CoinDialog by remember {
        mutableStateOf(false)
    }
    //钻石说明弹窗
    var isShowDiamondExplainDialog by remember {
        mutableStateOf(false)
    }

    val dialogQueue = LocalDialogQueue.current

    val listener = remember(scope) {
        object : NavListener(HOME_SUB_NAV) {
            override fun handleNav(route: String): Boolean {
                when (route) {
                    "diamond2cash" -> {
                        isShowDiamond2CashDialog = true
                        return true
                    }

                    else -> {}
                }
                return false
            }
        }
    }
    EventBusListener(listener = listener)


    Box {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            //1. 我的钻石
            Row(
                modifier = Modifier
                    .padding(start = 16.dp, end = 16.dp)
                    .background(color = Color.White, shape = RoundedCornerShape(12.dp))
                    .padding(horizontal = 14.dp, vertical = 12.dp)
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                ComposeImage(
                    model = R.drawable.ic_cpd_diamond, modifier = Modifier.size(48.dp)
                )

                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(horizontal = 14.dp)
                ) {
                    Text(
                        stringResource(id = R.string.cupid_income_has_diamond),
                        style = MaterialTheme.typography.titleMedium.copy(
                            color = colorResource(id = R.color.FF1D2129),
                        )
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    AutoSizeText(
                        user.diamond.toString(),
                        color = colorResource(id = R.color.FF1D2129),
                        fontWeight = FontWeight.Bold,
                        fontSize = 32.sp,
                        alignment = Alignment.TopStart,
                        modifier = Modifier.fillMaxWidth(),
                        maxLines = 1
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.click(noEffect = true) {
                        isShowDiamondExplainDialog = true
                    }) {
                        Text(
                            stringResource(id = R.string.cpd钻石说明),
                            color = colorResource(id = R.color.FF86909C),
                            fontSize = 12.sp,
                            modifier = Modifier
                                .weight(1f, false)
                        )
                        ComposeImage(model = R.drawable.ic_cpd_diamond_explain, modifier = Modifier.size(12.dp))
                    }
                }

                Column(horizontalAlignment = Alignment.End) {
                    ElevatedButton(
                        onClick = composeClick {
                            isShowDiamond2CashDialog = true
                        }, modifier = Modifier
                            .height(30.dp), contentPadding = PaddingValues(horizontal = 8.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(id = R.color.FFFF5E8B),
                            contentColor = Color.White
                        ),
                        elevation = null
                    ) {
                        Text(
                            stringResource(id = R.string.cupid_income_exchange_cash),
                            style = MaterialTheme.typography.titleSmall,
                            fontSize = 14.sp
                        )
                    }
                    Spacer(modifier = Modifier.height(8.dp))
                    ElevatedButton(
                        onClick = composeClick {
                            isShowDiamond2CoinDialog = true
                        }, modifier = Modifier
                            .height(30.dp), contentPadding = PaddingValues(horizontal = 8.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.White,
                            contentColor = colorResource(id = R.color.FFFF5E8B),
                        ),
                        border = BorderStroke(1.dp, colorResource(id = R.color.FFFF5E8B)),
                        elevation = null
                    ) {
                        Text(
                            stringResource(id = R.string.cupid_income_exchange_coin),
                            style = MaterialTheme.typography.titleSmall,
                            fontSize = 14.sp
                        )
                    }
                    Spacer(modifier = Modifier.height(8.dp))
                    Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.click {
                        onAction(IncomeAction.GoChargeDetail)
                    }) {
                        Text(
                            stringResource(id = R.string.cupid_income_exchange_history), style = MaterialTheme.typography.labelMedium.copy(
                                color = colorResource(id = R.color.FF86909C),
                            ),
                            fontSize = 12.sp
                        )
                        ComposeImage(model = R.drawable.ic_cpd_arrow_right)
                    }

                }
            }

            Spacer(modifier = Modifier.height(16.dp))
            //2. 我的现金
            Row(
                modifier = Modifier
                    .padding(start = 16.dp, end = 16.dp)
                    .background(
                        color = Color.White, shape = RoundedCornerShape(12.dp)
                    )
                    .padding(12.dp)
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                ComposeImage(
                    model = R.drawable.ic_cpd_cash, modifier = Modifier.size(48.dp)
                )
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(horizontal = 14.dp)
                ) {
                    Text(
                        stringResource(id = R.string.cupid_income_has_cash),
                        style = MaterialTheme.typography.titleMedium.copy(
                            color = colorResource(id = R.color.FF1D2129),
                        )
                    )
                    Spacer(modifier = Modifier.height(8.dp))

                    Row(verticalAlignment = Alignment.Bottom) {
                        AutoSizeText(
                            user.cash,
                            color = colorResource(id = R.color.FF1D2129),
                            fontWeight = FontWeight.Bold,
                            fontSize = 32.sp,
                            minTextSize = 12.sp,
                            alignment = Alignment.BottomStart,
                            maxLines = 1,
                            modifier = Modifier.weight(1f, false)
                        )
                        Text(
                            stringResource(id = R.string.cpd_cash_unit_en),
                            color = colorResource(id = R.color.FF86909C),
                            fontWeight = FontWeight.Medium,
                            fontSize = 12.sp,
                            modifier = Modifier
                        )

//                        AutoSizeText(
//                            buildAnnotatedString {
//                                withStyle(
//                                    SpanStyle(
//                                        color = colorResource(id = R.color.FF1D2129),
//                                        fontWeight = FontWeight.Bold,
//                                        fontSize = 32.sp,
//                                        fontFamily = D_DIN,
//                                    )
//                                ) {
//                                    append(user.cash)
//                                }
//                                append("JPY")
//                            },
//                            color = colorResource(id = R.color.FF86909C),
//                            fontWeight = FontWeight.Medium,
//                            fontSize = 12.sp,
//                            alignment = Alignment.BottomStart,
//                            lineHeight = 34.sp,
//                            //会不会有一行放不下的情况呢
//                            maxLines = 2,
//                            modifier = Modifier.weight(1f),
//                        )
                    }
                }
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    ElevatedButton(
                        onClick = composeClick {
                            scope.launch {
                                onAction(IncomeAction.Withdraw)
                            }
                        }, modifier = Modifier
                            .height(30.dp), contentPadding = PaddingValues(horizontal = 8.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(id = R.color.FFFF5E8B),
                            contentColor = Color.White
                        ),
                        elevation = null
                    ) {
                        Text(
                            stringResource(id = R.string.cpd_request_withdraw),
                            style = MaterialTheme.typography.titleSmall,
                            fontSize = 14.sp
                        )

                    }
                    Spacer(modifier = Modifier.height(12.dp))
                    Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.click {
                        onAction(IncomeAction.GoWithdrawDetail)
                    }) {
                        Text(
                            stringResource(id = R.string.cupid_income_exchange_history), style = MaterialTheme.typography.labelMedium.copy(
                                color = colorResource(id = R.color.FF86909C),
                            ),
                            fontSize = 12.sp
                        )
                        ComposeImage(model = R.drawable.ic_cpd_arrow_right)
                    }
                }
            }

            BannerView(
                location = ScreenLocation.INCOME,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .padding(top = 8.dp)
            )

            AnimatedVisibility(visible = incomeConfig != null && incomeConfig.inviteEnabled) {
                val config = incomeConfig?.invite
//                Spacer(modifier = Modifier.height(16.dp))
                Column(
                    modifier = Modifier
                        .padding(top = 16.dp)
                        .padding(horizontal = 16.dp)
                        .paint(
                            painter = painterResource(id = R.drawable.ic_income_invite_background)
                        ),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        config?.title ?: "",
                        style = MaterialTheme.typography.titleSmall.copy(
                            color = Color.White,
                            fontSize = 12.sp,
                            lineHeight = 12.sp,
                            fontWeight = FontWeight.Medium
                        ),
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                            .widthIn(150.dp, 180.dp)
                            .padding(vertical = 4.dp),
                        minLines = 2,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                    Spacer(modifier = Modifier.height(12.dp))
                    Text(
                        config?.desc ?: "",
                        style = MaterialTheme.typography.labelMedium.copy(
                            color = colorResource(id = R.color.FF86909C)
                        ),
                        modifier = Modifier
                            .weight(1f)
                            .padding(horizontal = 24.dp),
                        textAlign = TextAlign.Center,
                        overflow = TextOverflow.Ellipsis
                    )
                    Spacer(modifier = Modifier.height(12.dp))
                    AppButton(text = stringResource(id = R.string.cupid_invite_now),
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(35.dp)
                            .padding(horizontal = 16.dp),
                        color = Color.White,
                        background = CpdColors.FFFF5E8B,
                        onClick = composeClick {
                            onAction(IncomeAction.GoInvite(config?.link ?: ""))
                        })
                    Spacer(modifier = Modifier.height(12.dp))
                }
//                //3.邀请好友
//                Box(
//                    modifier = Modifier
//                        .padding(top = 16.dp)
//                        .fillMaxWidth()
//                        .padding(start = 16.dp, end = 16.dp)
//                        .background(
//                            color = Color.White, shape = RoundedCornerShape(12.dp)
//                        )
//                ) {
//                    Row(
//                        verticalAlignment = Alignment.CenterVertically,
//                        modifier = Modifier
//                            .padding(
//                                horizontal = 14.dp,
//                                vertical = 20.dp
//                            )
//                    ) {
//                        Column(modifier = Modifier.weight(1f)) {
//                            Text(
//                                config?.title ?: "", style = MaterialTheme.typography.titleSmall.copy(
//                                    color = colorResource(id = R.color.FF1D2129),
//                                )
//                            )
//                            Spacer(modifier = Modifier.width(12.dp))
//                            Text(
//                                config?.desc ?: "",
//                                style = MaterialTheme.typography.labelMedium.copy(color = colorResource(id = R.color.FF86909C))
//                            )
//                        }
//                        ElevatedButton(
//                            onClick = composeClick {
//                                onAction(IncomeAction.GoInvite(config?.link ?: ""))
//                            }, modifier = Modifier
//                                .height(30.dp), contentPadding = PaddingValues(horizontal = 8.dp),
//                            colors = ButtonDefaults.buttonColors(
//                                containerColor = colorResource(id = R.color.FFFF5E8B),
//                                contentColor = Color.White
//                            ),
//                            elevation = null
//                        ) {
//                            Text(stringResource(id = R.string.cupid_invite_now), style = MaterialTheme.typography.titleSmall)
//                        }
//                    }
//                }
            }

            AnimatedVisibility(visible = signInfo.tasks.isNotEmpty()) {
                //4. 签到领金币
                SignInWidget(signInfo, modifier = Modifier.padding(top = 16.dp)) {
                    dialogQueue.pushCenterDialog {  dialog, _ ->
                        SigninDialogContent(task = signInfo, onDismiss = { }) {
                            if (onAction(IncomeAction.DailySignIn) as Boolean) {
                                dialog.dismiss()
                            }
                        }
                    }
                }
            }

            tasksInfos.forEach { info ->
                AnimatedVisibility(visible = info.tasks.isNotEmpty()) {
//                Spacer(modifier = Modifier.height(16.dp))
                    //5.做任务领金币
                    NewbieTaskWidget(
                        info,
                        modifier = Modifier.padding(top = 16.dp),
                        onHelp = onHelp,
                    ) { task ->
                        onAction(IncomeAction.NewbieTaskEvent(task))
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))
        }

        //region 各类弹窗

        if (isShowDiamond2CashDialog) {
            var isShowDiamond2CashActiveClose by remember { mutableStateOf(false) }
            AnimatedDialog(
                isActiveClose = isShowDiamond2CashActiveClose,
                onDismiss = { isShowDiamond2CashDialog = false }
            ) {
                Diamond2CashWidget(diamondExchangeList.filter { it.itemType == 101 }, {
                    isShowDiamond2CashActiveClose = true
                }) {
                    onAction(it)
                }
            }
        }

        if (isShowDiamond2CoinDialog) {
            var isShowDiamond2CoinActiveClose by remember { mutableStateOf(false) }
            AnimatedDialog(
                isActiveClose = isShowDiamond2CoinActiveClose,
                onDismiss = { isShowDiamond2CoinDialog = false }
            ) {
                Diamond2CoinWidget(diamondExchangeList.filter { it.itemType == 100 }, { isShowDiamond2CoinActiveClose = true }) {
                    onAction(it)
                }
            }
        }

        if (isShowDiamondExplainDialog) {
            Dialog(onDismissRequest = { isShowDiamondExplainDialog = false }) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth(0.72f)
                        .background(color = Color.White, shape = RoundedCornerShape(8.dp))
                        .padding(horizontal = 16.dp, vertical = 20.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = incomeConfig?.diamondHelp?.content ?: "",
                        style = MaterialTheme.typography.bodyMedium.copy(
                            color = colorResource(id = R.color.FF606161),
                            textAlign = TextAlign.Center
                        ),
                        fontSize = 15.sp,
                        modifier = Modifier.width(238.dp)
                    )
                    Spacer(modifier = Modifier.height(20.dp))
                    ElevatedButton(
                        onClick = composeClick {
                            isShowDiamondExplainDialog = false
                        },
                        modifier = Modifier
                            .height(36.dp)
                            .widthIn(160.dp),
                        contentPadding = PaddingValues(horizontal = 12.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(id = R.color.FFFF5E8B),
                            contentColor = Color.White
                        ),
                        elevation = null
                    ) {
                        Text(incomeConfig?.diamondHelp?.btnText ?: "", fontSize = 16.sp)
                    }
                }
            }
        }

        //endregion
    }
}


//region Dialog content

@Composable
private fun DialogContainer(title: String, onClose: () -> Unit, content: @Composable (ColumnScope) -> Unit) {
    Column(
        modifier = Modifier
            .click(noEffect = true) { }
            .background(
                color = Color.White,
                shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)
            )
    ) {
        Box(modifier = Modifier.height(44.dp)) {
            Text(
                title, style = MaterialTheme.typography.titleMedium.copy(fontSize = 18.sp, color = colorResource(id = R.color.FF1D2129)),
                modifier = Modifier.align(Alignment.Center)
            )
            HorizontalDivider(color = colorResource(id = R.color.FFF0F0F0), modifier = Modifier.align(Alignment.BottomCenter))
            ComposeImage(
                model = R.drawable.ic_cpd_close_grey, modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 16.dp)
                    .size(20.dp)
                    .click {
                        onClose()
                    }
            )
        }
        content(this)
    }
}


@Composable
private fun Diamond2CashWidget(
    data: List<ExchangeItem>,
    onClose: () -> Unit = {},
    onAction: (IncomeAction) -> Any = {},
) {
    var selectedIdx by remember {
        mutableIntStateOf(0)
    }

    DialogContainer(title = stringResource(id = R.string.cupid_income_exchange_cash_title), onClose = onClose) {
        VerticalGrid(columns = 3, horizontalSpace = 8.dp, verticalSpace = 8.dp, modifier = Modifier.padding(horizontal = 16.dp, vertical = 20.dp)) {
            data.forEachIndexed { index, option ->
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .click {
                            selectedIdx = index
                        }
                        .background(
                            color = if (index == selectedIdx) colorResource(id = R.color.FFFF5E8B).copy(0.14f) else colorResource(id = R.color.FFF1F2F3),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .border(
                            1.dp,
                            color = if (index == selectedIdx) colorResource(id = R.color.FFFF5E8B) else Color.Transparent,
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(vertical = 16.dp)
                ) {
                    Row(horizontalArrangement = Arrangement.Center, verticalAlignment = Alignment.CenterVertically) {
                        ComposeImage(model = R.drawable.ic_cpd_cash, modifier = Modifier.size(16.dp))
                        Spacer(modifier = Modifier.width(2.dp))
                        Text(
                            option.number.toString(), style = TextStyle(
                                fontWeight = FontWeight.SemiBold,
                                fontSize = 16.sp,
                                color = colorResource(id = R.color.FF1D2129)
                            )
                        )
                    }
                    Spacer(modifier = Modifier.height(1.5.dp))
                    Text(
                        text = stringResource(id = R.string.cupid_income_exchange_diamond_condition, option.price),
                        style = MaterialTheme.typography.labelMedium.copy(
                            color = colorResource(id = R.color.FF86909C)
                        ),
                        fontSize = 12.sp,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
        ElevatedButton(
            onClick = composeClick {
                //有几率返回的data为empty, 做个判断先
                if (selectedIdx < data.size) {
                    if (onAction(IncomeAction.Diamoind2Cash(data[selectedIdx])) as Boolean) {
                        onClose()
                    }
                } else {
                    onClose()
                }
            }, modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth()
                .height(44.dp),
            contentPadding = PaddingValues(horizontal = 8.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = colorResource(id = R.color.FFFF5E8B),
                contentColor = Color.White
            ),
            elevation = null
        ) {
            Text(stringResource(id = R.string.cupid_income_exchange_cash_confirm), style = MaterialTheme.typography.titleSmall)
        }
    }
}

@Composable
private fun Diamond2CoinWidget(
    data: List<ExchangeItem>,
    onClose: () -> Unit = {},
    onAction: (IncomeAction) -> Any = {},
) {
    var selectedIdx by remember {
        mutableStateOf(0)
    }
    DialogContainer(title = stringResource(id = R.string.cupid_income_exchange_coin_title), onClose = onClose) {
        VerticalGrid(columns = 3, horizontalSpace = 8.dp, verticalSpace = 8.dp, modifier = Modifier.padding(horizontal = 16.dp, vertical = 20.dp)) {
            data.forEachIndexed { index, option ->
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .click {
                            selectedIdx = index
                        }
                        .background(
                            color = if (index == selectedIdx) colorResource(id = R.color.FFFF5E8B).copy(0.14f) else colorResource(id = R.color.FFF1F2F3),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .border(
                            1.dp,
                            color = if (index == selectedIdx) colorResource(id = R.color.FFFF5E8B) else Color.Transparent,
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(vertical = 16.dp)
                ) {
                    Row(horizontalArrangement = Arrangement.Center, verticalAlignment = Alignment.CenterVertically) {
                        ComposeImage(model = R.drawable.ic_cpd_coin, modifier = Modifier.size(16.dp))
                        Spacer(modifier = Modifier.width(2.dp))
                        Text(
                            option.number.toString(), style = TextStyle(
                                fontWeight = FontWeight.SemiBold,
                                fontSize = 16.sp,
                                color = colorResource(id = R.color.FF1D2129)
                            )
                        )
                    }
                    Spacer(modifier = Modifier.height(1.5.dp))
                    Text(
                        text = stringResource(id = R.string.cupid_income_exchange_diamond_condition, option.price),
                        style = MaterialTheme.typography.labelMedium.copy(
                            color = colorResource(id = R.color.FF86909C)
                        ),
                        fontSize = 12.sp,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
        ElevatedButton(
            onClick = composeClick {
                if (selectedIdx < data.size) {
                    if (onAction(IncomeAction.Diamoind2Coin(data[selectedIdx])) as Boolean) {
                        onClose()
                    }
                } else {
                    onClose()
                }
            }, modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth()
                .height(44.dp),
            contentPadding = PaddingValues(horizontal = 8.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = colorResource(id = R.color.FFFF5E8B),
                contentColor = Color.White
            ),
            elevation = null
        ) {
            Text(stringResource(id = R.string.cupid_income_exchange_coin_confirm), style = MaterialTheme.typography.titleSmall)
        }
    }
}

//endregion

//region preview
@Composable
@Preview(name = "钻石换现金", showBackground = true)
private fun Diamond2CashPreview() {
    Box(modifier = Modifier.padding(4.dp)) {
        Diamond2CashWidget(
            listOf()
        )
    }
}

@Composable
@Preview(name = "钻石换金币", showBackground = true)
private fun Diamond2CoinPreview() {
    Box(modifier = Modifier.padding(4.dp)) {
        Diamond2CoinWidget(
            listOf()
        )
    }
}

@Composable
@Preview(
    "收益页面",
    widthDp = 375,
    heightDp = 812,
    locale = "ja-rJP",
    showBackground = true
)
private fun IncomeScreenPreview() {
    PreviewCupidTheme {
        IncomePageWidget(
            userForPreview,
            TaskSeries(),
            listOf(),
            incomeConfig = UserIncomeConfig(
                inviteEnabled = true,
                invite = UserIncomeConfig.Invite(
                    title = "友達を招待して登録し、\n" +
                            "ダイヤをゲット",
                    desc = "友達を1人招待するごとに、5000ダイヤが報酬としてもらえます。 友達がUCOOでコインをチャージしたり、ダイヤをゲットしたりすると、あなたはダイヤがもらえます。",
                    link = "www.baidu.com"
                ),
                diamondHelp = UserIncomeConfig.DiamondHelp("今すぐ招待", "这个是弹窗内容")
            )
        )
    }
}

//endregion
