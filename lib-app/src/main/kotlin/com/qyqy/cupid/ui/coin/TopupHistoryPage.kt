package com.qyqy.cupid.ui.coin

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.qyqy.ucoo.R

/**
 *  @time 2024/7/31
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.coin
 */
@Composable
fun TopupHistoryPage() {
    TypeRecordHistoryPage(title =  stringResource(id = R.string.cpd金币明细记录), type = 21)
}

@Composable
@Preview(showBackground = true)
private fun TopupHistoryPagePreview() {
    TopupHistoryPage()
}