package com.qyqy.cupid.ui.coin

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.model.CoinHistoryViewModel
import com.qyqy.cupid.model.ITypeRecordItem
import com.qyqy.cupid.model.TypeRecordItem
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.state.IEmpty
import com.qyqy.ucoo.compose.state.PullRefreshBox
import com.qyqy.ucoo.compose.state.UIState
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.PaginateState
import com.qyqy.ucoo.compose.ui.overScrollVertical
import com.qyqy.ucoo.compose.ui.rememberOverscrollFlingBehavior
import com.qyqy.ucoo.compose.ui.rememberPaginateState
import eu.bambooapps.material3.pullrefresh.PullRefreshIndicatorDefaults


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TypeRecordHistoryPage(title: String, type: Int) {

    val lazyListState = rememberLazyListState()

    val viewModel = viewModel<CoinHistoryViewModel>(initializer = {
        CoinHistoryViewModel(type)
    })

    val paginateState = viewModel.attachLazyWidget(listState = lazyListState)

    val uiState = viewModel.uiState

    val isRefreshing = viewModel.isRefreshing

    Column(modifier = Modifier.fillMaxSize()) {

        CupidAppBar(title = title)

        PullRefreshBox(
            modifier = Modifier.fillMaxSize(),
            isRefreshing = isRefreshing,
            onRefresh = {
                viewModel.refresh()
            },
            colors = PullRefreshIndicatorDefaults.colors(
                contentColor = MaterialTheme.colorScheme.primary,
                containerColor = Color.White
            ),
        ) {
            ContentList(uiState.data?.list, paginateState, lazyListState)
            if (uiState is UIState.Error) {
                EmptyOrFailureContent(uiState, Modifier.noEffectClickable {
                    viewModel.retry()
                })
            } else {
                uiState.data?.empty?.also {
                    EmptyOrFailureContent(it)
                }
            }
        }
    }
}

@Composable
private fun EmptyOrFailureContent(empty: IEmpty, modifier: Modifier = Modifier) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .then(modifier),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        empty.icon?.also {
            Image(painter = it, contentDescription = null)
        }

        empty.text?.also {
            Text(
                text = it,
                color = Color(0xFF86909C),
                fontSize = 16.sp
            )
        }
    }
}


@Composable
private fun ContentList(
    list: List<ITypeRecordItem>?,
    paginateState: PaginateState<Int> = rememberPaginateState(),
    lazyListState: LazyListState = rememberLazyListState(),
) {
    val isLoading by remember {
        derivedStateOf {
            paginateState.nextLoadState.isLoading
        }
    }

    val isFailure by remember {
        derivedStateOf {
            paginateState.nextLoadState.isFailure
        }
    }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .overScrollVertical(),
        state = lazyListState,
        flingBehavior = rememberOverscrollFlingBehavior { lazyListState }
    ) {
        if (list.isNullOrEmpty()) {
            return@LazyColumn
        }

        itemsIndexed(list, key = { index, item -> "ContentList-${item.id}" }, contentType = { _, _ -> 0 }) { index, item ->
            TypeRecordItemContent(item)
            HorizontalDivider(thickness = 0.5.dp, color = Color(0x14000000))
        }

        if (isLoading) {
            item(key = "isLoading", contentType = 1) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(30.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(5.dp, Alignment.CenterHorizontally),
                ) {
                    CircularProgressIndicator(modifier = Modifier.size(16.dp), color = Color(0xFF86909C), strokeWidth = 1.dp)
                    Text(text = stringResource(id = R.string.cpd加载中), color = Color(0xFF86909C), fontSize = 12.sp)
                }
            }
        } else if (isFailure) {
            item(key = "isFailure", contentType = 2) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(30.dp)
                        .noEffectClickable {
                            paginateState.retry()
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = stringResource(id = R.string.cpd加载失败点击重试),
                        color = colorResource(id = R.color.red_300),
                        fontSize = 12.sp
                    )
                }
            }
        }
    }
}



@Composable
private fun TypeRecordItemContent(item: ITypeRecordItem) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 12.dp)
    ) {
        val (title, time, moneyBox) = createRefs()
        Text(
            text = item.titleText,
            color = colorResource(id = R.color.FF1D2129),
            modifier = Modifier.constrainAs(title) {
                top.linkTo(parent.top)
                bottom.linkTo(time.top)
                start.linkTo(parent.start)
                end.linkTo(moneyBox.start, margin = 15.dp)
                width = Dimension.fillToConstraints
            },
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium
        )
        Text(
            text = item.subTitleText,
            color = colorResource(id = R.color.FF86909C),
            fontSize = 12.sp,
            modifier = Modifier.constrainAs(time) {
                start.linkTo(parent.start)
                bottom.linkTo(parent.bottom)
                top.linkTo(title.bottom, margin = 8.dp)
            }
        )
        Row(
            modifier = Modifier.constrainAs(moneyBox) {
                end.linkTo(parent.end)
                bottom.linkTo(parent.bottom)
                top.linkTo(parent.top)
            },
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                item.change,
                style = TextStyle(
                    color = colorResource(id = R.color.FF1D2129),
                    fontSize = 16.sp, fontWeight = FontWeight.Medium
                )
            )
            Spacer(modifier = Modifier.width(5.dp))
            ComposeImage(model = item.typeIcon, modifier = Modifier.size(20.dp))
        }
    }
}


@Preview
@Composable
private fun PreviewTypeRecordItemContent() {
    TypeRecordItemContent(
        TypeRecordItem(
            1,
            "但是难逢敌手发的啥那发多少分多少大师傅你的三第三方大师傅你阿斯蒂芬",
            "都是风景的飞机",
            "1",
            R.drawable.ic_cpd_coin
        )
    )
}