@file:OptIn( ExperimentalLayoutApi::class)

package com.qyqy.cupid.ui.coin

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ElevatedButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.qyqy.cupid.data.UserCoinConfig
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.dialog.ContentAlertDialog
import com.qyqy.cupid.ui.dialog.DialogButton
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.widgets.composeClick
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.room.SpanText
import com.qyqy.ucoo.compose.presentation.room.buildTextSpan
import com.qyqy.ucoo.compose.presentation.room.inlineTextContent
import com.qyqy.ucoo.compose.presentation.sign_tasks.Task
import com.qyqy.ucoo.compose.presentation.sign_tasks.TaskSeries
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.FillComposeImage
import com.qyqy.ucoo.compose.ui.VerticalGrid
import com.qyqy.ucoo.http.gson
import com.qyqy.ucoo.http.sAppJson

/**
 *  @time 2024/7/26
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.coin
 */

//region 签到领金币

@Composable
fun SignInWidget(task: TaskSeries, modifier: Modifier = Modifier, clickSignin: () -> Unit) {
    val moneyTypeColor = if (task.prizeType == 5) colorResource(id = R.color.FFFFB71A) else colorResource(id = R.color.FFFF5E8B)
    val checkedImg = if (task.prizeType == 5) R.drawable.ic_cpd_sign_checked else R.drawable.ic_cpd_sign_diamond_checked

    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(start = 16.dp, end = 16.dp)
            .background(
                color = Color.White, shape = RoundedCornerShape(12.dp)
            )
            .padding(16.dp)
    ) {
        Text(
            task.seriesTitle, style = MaterialTheme.typography.titleMedium.copy(
                color = colorResource(id = R.color.FF1D2129),
            )
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            task.treasureTotips, style = MaterialTheme.typography.titleSmall.copy(
                color = colorResource(id = R.color.FF86909C),
            )
        )
        Spacer(modifier = Modifier.height(16.dp))
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            task.tasks.forEachIndexed { index, reward ->
                Column(
                    modifier = Modifier
                        .size(36.dp, 56.dp)
                        .background(
                            if (reward.finished) moneyTypeColor.copy(alpha = 0.33f)
                            else colorResource(id = R.color.FFF1F2F3),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .border(1.dp, if (reward.finished) moneyTypeColor else Color.Transparent, shape = RoundedCornerShape(8.dp))
                        .padding(vertical = 3.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.SpaceEvenly
                ) {
                    FillComposeImage(
                        model = reward.extra.prizeIcon,
                        modifier = Modifier.height(16.dp),
                        fillHeight = false,
                    )

                    if (reward.finished) {
                        ComposeImage(model = checkedImg, modifier = Modifier.size(16.dp))
                    } else {
                        Text(
                            reward.title, style = MaterialTheme.typography.labelSmall.copy(
                                color = colorResource(id = R.color.FF86909C),
                            )
                        )
                    }
                }
            }
        }
        Spacer(modifier = Modifier.height(16.dp))

        Button(
            onClick = composeClick {
                clickSignin()
            }, modifier = Modifier
                .fillMaxWidth()
                .height(36.dp), contentPadding = PaddingValues(horizontal = 10.dp),
            enabled = !task.todayFinished,
            colors = ButtonDefaults.buttonColors(
                containerColor = moneyTypeColor,
                disabledContainerColor = moneyTypeColor.copy(alpha = 0.5f),
                contentColor = Color.White
            )
        ) {
            AutoSizeText(
                text = stringResource(id = if (task.todayFinished) R.string.cupid_signin_done else R.string.cupid_signin_get),
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White,
                fontSize = 14.sp
            )
        }
    }
}

//endregion

//region 做任务领金币
@Composable
fun NewbieTaskWidget(
    task: TaskSeries,
    modifier: Modifier = Modifier,
    onHelp: () -> Unit = {},
    onAction: (Task) -> Unit,
) {
    val density = LocalDensity.current
    val dialogQueue = LocalDialogQueue.current
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(start = 16.dp, end = 16.dp)
            .background(
                color = Color.White, shape = RoundedCornerShape(12.dp)
            )
            .padding(top = 16.dp, start = 16.dp, end = 16.dp)
    ) {
        Text(
            text = task.seriesTitle,
            style = MaterialTheme.typography.titleMedium.copy(color = colorResource(id = R.color.FF1D2129), fontSize = 16.sp)
        )
        Spacer(modifier = Modifier.height(8.dp))
        if (task.treasureTotips.isNotEmpty()) {
            Text(
                text = task.treasureTotips,
                style = MaterialTheme.typography.titleSmall.copy(color = colorResource(id = R.color.FF86909C)),
                fontSize = 14.sp
            )

            Spacer(modifier = Modifier.height(6.dp))
        }

        if (task.lineHelp.isNotEmpty()) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(62.dp)
                    .padding(vertical = 10.dp)
            ) {
                Text(
                    text = task.lineHelp,
                    modifier = Modifier
                        .weight(1f)
                        .padding(end = 10.dp),
                    fontSize = 14.sp,
                    color = colorResource(id = R.color.FF1D2129)
                )

                ElevatedButton(
                    onClick = composeClick(onClick = onHelp),
                    modifier = Modifier
                        .widthIn(72.dp)
                        .height(28.dp),
                    contentPadding = PaddingValues(horizontal = 8.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = colorResource(id = R.color.FFFF5E8B),
                        contentColor = Color.White,
                        disabledContainerColor = colorResource(id = R.color.FFFF5E8B).copy(alpha = 0.5f),
                        disabledContentColor = Color.White.copy(alpha = 0.5f)
                    ),
                    enabled = true,
                    elevation = null
                ) {
                    AutoSizeText(
                        text = stringResource(id = R.string.cpd查看),
                        fontSize = 14.sp,
                    )
                }
            }
        }

        task.tasks.forEachIndexed { index, bean ->
            if (index != 0 || task.lineHelp.isNotEmpty()) {
                Spacer(
                    modifier = Modifier
                        .height(0.5.dp)
                        .fillMaxWidth()
                        .background(color = colorResource(id = R.color.FF000000).copy(alpha = 0.14f))
                )
            }
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(62.dp)
                    .padding(vertical = 10.dp)
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    SpanText(
                        textSpan = buildTextSpan {
                            append(bean.title)
                            if (!bean.extra.hint.isNullOrEmpty()) {
                                it.appendInlineContent(
                                    inlineTextContent(
                                        key = "qa",
                                        density = density,
                                        width = 16,
                                        height = 16,
                                        paddingValues = PaddingValues(horizontal = 3.dp)
                                    ) { modifier ->
                                        Icon(
                                            painter = painterResource(id = R.drawable.ic_cp_zone_qa),
                                            contentDescription = "qa",
                                            tint = Color(0xFF1D2129),
                                            modifier = modifier.noEffectClickable {
                                                dialogQueue.pushCenterDialog { dialog, _ ->
                                                    ContentAlertDialog(
                                                        content = bean.extra.hint,
                                                        endButton = DialogButton(stringResource(id = R.string.cpd我知道了)) {
                                                            dialog.dismiss()
                                                        }
                                                    )
                                                }
                                            }
                                        )
                                    }
                                )
                            }
                        },
                        style = MaterialTheme.typography.titleMedium.copy(
                            color = colorResource(id = R.color.FF1D2129),
                            fontSize = 14.sp
                        )
                    )
                    if (bean.progress.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(text = bean.progress, fontSize = 12.sp, color = colorResource(id = R.color.FF86909C))
                    }
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
//                        if (task.seriesType == TaskSeries.SERIES_TYPE_DAILY) {//如果是收益
//                            Text(stringResource(id = R.string.cpd完成后获得), fontSize = 12.sp, color = colorResource(id = R.color.FF1D2129))
//                            Spacer(modifier = Modifier.width(5.dp))
//                        }
                        ComposeImage(model = bean.extra.prizeIcon, modifier = Modifier.size(18.dp))
                        Spacer(modifier = Modifier.width(6.dp))
                        Text(text = bean.prize, style = MaterialTheme.typography.labelMedium.copy(color = colorResource(id = R.color.FFFF5E8B)))
                    }
                }
                ElevatedButton(
                    onClick = composeClick { onAction(bean) },
                    modifier = Modifier
                        .widthIn(72.dp)
                        .height(28.dp), contentPadding = PaddingValues(horizontal = 8.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = colorResource(id = R.color.FFFF5E8B),
                        contentColor = Color.White,
                        disabledContainerColor = colorResource(id = R.color.FFFF5E8B).copy(alpha = 0.5f),
                        disabledContentColor = Color.White.copy(alpha = 0.5f)
                    ),
                    enabled = !bean.finished,
                    elevation = null
                ) {
                    AutoSizeText(
                        text = if (bean.finished) stringResource(id = R.string.cpd已完成) else stringResource(id = R.string.cpd去完成),
                        fontSize = 14.sp,
                    )
                }
            }

        }
    }
}

//endregion

//region 玩游戏领金币
@Composable
fun GameCoinWidget(
    gameBean: UserCoinConfig.Game,
    modifier: Modifier = Modifier,
    onAction: (UserCoinConfig.Game.Item) -> Unit,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(start = 16.dp, end = 16.dp)
            .background(
                color = Color.White, shape = RoundedCornerShape(12.dp)
            )
            .padding(top = 16.dp, start = 16.dp, end = 16.dp)
    ) {
        Text(
            gameBean.title, style = MaterialTheme.typography.titleMedium.copy(
                color = colorResource(id = R.color.FF1D2129),
            )
        )
        VerticalGrid(columns = 2, horizontalSpace = 15.dp, verticalSpace = 15.dp, modifier = Modifier.padding(vertical = 15.dp)) {
            gameBean.items.forEach {
                Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier.clickable {
                    onAction(it)
                }) {
                    ComposeImage(
                        model = it.image, modifier = Modifier
                            .size(148.dp)
                            .clip(RoundedCornerShape(6.dp))
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        it.name, style = MaterialTheme.typography.labelLarge.copy(
                            color = colorResource(id = R.color.FF1D2129)
                        )
                    )
                }
            }
        }
    }
}

//endregion

//region 签到弹窗

@Composable
fun SigninDialogWidget(task: TaskSeries, isSigninDialogShow: Boolean, onDismiss: () -> Unit, clickSign: () -> Unit) {
    if (isSigninDialogShow) {
        Dialog(onDismissRequest = { onDismiss() }) {
            SigninDialogContent(task, onDismiss, clickSign)
        }
    }
}

@Composable
fun SigninDialogContent(
    task: TaskSeries, onDismiss: () -> Unit = {}, clickSign: () -> Unit = {},
) {
    val moneyTypeColor = if (task.prizeType == 5) colorResource(id = R.color.FFFFB71A) else colorResource(id = R.color.FFFF5E8B)
    val checkedImg = if (task.prizeType == 5) R.drawable.ic_cpd_sign_checked else R.drawable.ic_cpd_sign_diamond_checked
    val titleColor = if (task.prizeType == 5) colorResource(id = R.color.FF5C3A0E) else colorResource(id = R.color.FF86150E)

    Box(
        modifier = Modifier
            .width(290.dp)
    ) {
        Column(
            modifier = Modifier
                .align(Alignment.TopCenter)
                .paint(
                    painterResource(id = if (task.prizeType == 5) R.drawable.ic_cpd_signin_background else R.drawable.ic_cpd_signin_diamond_background),
                    contentScale = ContentScale.FillBounds,
                    alignment = Alignment.TopStart
                ),
            horizontalAlignment = Alignment.CenterHorizontally,

            ) {
            Text(
                text = task.seriesTitle,
                style = TextStyle(
                    fontSize = 18.sp,
                    color = titleColor,
                    fontWeight = FontWeight.SemiBold,
                    textAlign = TextAlign.Center
                ), modifier = Modifier.padding(start = 20.dp, end = 20.dp, top = 15.dp)
            )
            Spacer(modifier = Modifier.height(5.dp))
            Text(
                text = task.finishedTotips,
                style = MaterialTheme.typography.titleMedium.copy(
                    fontSize = 12.sp,
                    color = titleColor,
                    textAlign = TextAlign.Center
                ),
                modifier = Modifier.padding(start = 20.dp, end = 20.dp),
                lineHeight = 13.sp,
                minLines = 2,
            )
            Spacer(modifier = Modifier.height(16.dp))
            FlowRow(
                verticalArrangement = Arrangement.spacedBy(6.dp),
                horizontalArrangement = Arrangement.spacedBy(6.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                maxItemsInEachRow = 4
            ) {
                task.tasks.forEachIndexed { i, bean ->
                    Box(modifier = Modifier.weight(if (!bean.extra.bigGoldBox) 1f else 2f)) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(72.dp)
                                .background(
                                    if (bean.finished) moneyTypeColor.copy(0.3f)
                                    else colorResource(id = R.color.FFF1F2F3),
                                    shape = RoundedCornerShape(6.dp)
                                )
                                .border(
                                    1.dp, if (bean.finished) moneyTypeColor else Color.Transparent, shape = RoundedCornerShape(8.dp)
                                )
                                .padding(vertical = 8.dp),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(1.5.dp, Alignment.CenterVertically)
                        ) {
                            ComposeImage(
                                model = bean.extra.prizeIcon,
                                contentScale = ContentScale.Fit,
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                            if (bean.finished) {
                                ComposeImage(
                                    model = checkedImg,
                                    modifier = Modifier.size(16.dp)
                                )
                            } else {
                                Text(
                                    bean.prize, style = MaterialTheme.typography.labelMedium.copy(
                                        color = colorResource(id = R.color.FF86909C),
                                    ), textAlign = TextAlign.Center,
                                    fontSize = 12.sp
                                )
                            }
                        }
                        Text(
                            text = "${i + 1}", modifier = Modifier
                                .background(colorResource(id = R.color.FFFF5E8B), RoundedCornerShape(topStart = 6.dp, bottomEnd = 6.dp))
                                .sizeIn(14.dp),
                            style = TextStyle(color = Color.White, fontSize = 12.sp, textAlign = TextAlign.Center)
                        )
                    }
                }
            }
            Text(
                text = task.treasureTotips,
                modifier = Modifier.padding(vertical = 12.dp, horizontal = 15.dp),
                style = MaterialTheme.typography.labelMedium.copy(
                    color = moneyTypeColor,
                ),
                fontSize = 12.sp,
                textAlign = TextAlign.Center
            )
            Button(
                onClick = composeClick {
                    clickSign()
                }, modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 20.dp)
                    .heightIn(36.dp),
                contentPadding = PaddingValues(horizontal = 12.dp),
                enabled = !task.todayFinished,
                colors = ButtonDefaults.buttonColors(
                    containerColor = moneyTypeColor,
                    disabledContainerColor = moneyTypeColor.copy(0.5f),
                    contentColor = Color.White
                ),
                elevation = null
            ) {
                AutoSizeText(
                    text = stringResource(R.string.cupid_signin_get),
                    style = MaterialTheme.typography.bodyMedium,
                    alignment = Alignment.Center,
                    maxLines = 1,
                    color = Color.White,
                    fontSize = 14.sp
                )
            }
            Spacer(modifier = Modifier.height(20.dp))
        }
        ComposeImage(
            model = R.drawable.ic_cpd_close,
            modifier = Modifier
                .padding(12.dp)
                .align(Alignment.TopEnd)
                .clickable { onDismiss() }
        )
    }
}

//endregion

@Composable
@Preview(
    "签到弹窗",
    locale = "ja-rJP",
)
private fun SigninDialogPreview() {
    val bean = gson.fromJson(
        "{\n" +
                "    \"series_id\": 13,\n" +
                "    \"series_title\": \"サインインしてゴールドコインを受け取ります\",\n" +
                "    \"series_type\": 3,\n" +
                "    \"tasks\": [\n" +
                "        {\n" +
                "            \"id\": 31,\n" +
                "            \"title\": \"初日\",\n" +
                "            \"desc\": \"その日のチェックインを完了する\",\n" +
                "            \"prize\": \"5金\",\n" +
                "            \"prize_type\": 5,\n" +
                "            \"extra\": {\n" +
                "                \"hint\": \"\",\n" +
                "                \"task_icon\": \"\",\n" +
                "                \"prize_icon\": \"https://media.ucoofun.com/opsite/itasks/jp-check-in-12456-coin.webp\",\n" +
                "                \"big_gold_box\": false,\n" +
                "                \"predecessors\": null,\n" +
                "                \"twd_prize_value\": null\n" +
                "            },\n" +
                "            \"finished\": false,\n" +
                "            \"progress\": \"\",\n" +
                "            \"condition_type\": 1,\n" +
                "            \"condition_times\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": 32,\n" +
                "            \"title\": \"次の日\",\n" +
                "            \"desc\": \"その日のチェックインを完了する\",\n" +
                "            \"prize\": \"10金\",\n" +
                "            \"prize_type\": 5,\n" +
                "            \"extra\": {\n" +
                "                \"hint\": \"\",\n" +
                "                \"task_icon\": \"\",\n" +
                "                \"prize_icon\": \"https://media.ucoofun.com/opsite/itasks/jp-check-in-12456-coin.webp\",\n" +
                "                \"big_gold_box\": false,\n" +
                "                \"predecessors\": null,\n" +
                "                \"twd_prize_value\": null\n" +
                "            },\n" +
                "            \"finished\": false,\n" +
                "            \"progress\": \"\",\n" +
                "            \"condition_type\": 1,\n" +
                "            \"condition_times\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": 33,\n" +
                "            \"title\": \"三日目\",\n" +
                "            \"desc\": \"その日のチェックインを完了する\",\n" +
                "            \"prize\": \"大きな宝箱\\n50金貨\",\n" +
                "            \"prize_type\": 6,\n" +
                "            \"extra\": {\n" +
                "                \"hint\": \"\",\n" +
                "                \"task_icon\": \"\",\n" +
                "                \"prize_icon\": \"https://media.ucoofun.com/opsite/itasks/jp-check-in-3-coin.webp\",\n" +
                "                \"big_gold_box\": false,\n" +
                "                \"predecessors\": null,\n" +
                "                \"twd_prize_value\": null\n" +
                "            },\n" +
                "            \"finished\": false,\n" +
                "            \"progress\": \"\",\n" +
                "            \"condition_type\": 1,\n" +
                "            \"condition_times\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": 34,\n" +
                "            \"title\": \"4日目\",\n" +
                "            \"desc\": \"その日のチェックインを完了する\",\n" +
                "            \"prize\": \"15金\",\n" +
                "            \"prize_type\": 5,\n" +
                "            \"extra\": {\n" +
                "                \"hint\": \"\",\n" +
                "                \"task_icon\": \"\",\n" +
                "                \"prize_icon\": \"https://media.ucoofun.com/opsite/itasks/jp-check-in-12456-coin.webp\",\n" +
                "                \"big_gold_box\": false,\n" +
                "                \"predecessors\": null,\n" +
                "                \"twd_prize_value\": null\n" +
                "            },\n" +
                "            \"finished\": false,\n" +
                "            \"progress\": \"\",\n" +
                "            \"condition_type\": 1,\n" +
                "            \"condition_times\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": 35,\n" +
                "            \"title\": \"5日目\",\n" +
                "            \"desc\": \"その日のチェックインを完了する\",\n" +
                "            \"prize\": \"20金\",\n" +
                "            \"prize_type\": 5,\n" +
                "            \"extra\": {\n" +
                "                \"hint\": \"\",\n" +
                "                \"task_icon\": \"\",\n" +
                "                \"prize_icon\": \"https://media.ucoofun.com/opsite/itasks/jp-check-in-12456-coin.webp\",\n" +
                "                \"big_gold_box\": false,\n" +
                "                \"predecessors\": null,\n" +
                "                \"twd_prize_value\": null\n" +
                "            },\n" +
                "            \"finished\": false,\n" +
                "            \"progress\": \"\",\n" +
                "            \"condition_type\": 1,\n" +
                "            \"condition_times\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": 36,\n" +
                "            \"title\": \"6日目\",\n" +
                "            \"desc\": \"その日のチェックインを完了する\",\n" +
                "            \"prize\": \"25金\",\n" +
                "            \"prize_type\": 5,\n" +
                "            \"extra\": {\n" +
                "                \"hint\": \"\",\n" +
                "                \"task_icon\": \"\",\n" +
                "                \"prize_icon\": \"https://media.ucoofun.com/opsite/itasks/jp-check-in-12456-coin.webp\",\n" +
                "                \"big_gold_box\": false,\n" +
                "                \"predecessors\": null,\n" +
                "                \"twd_prize_value\": null\n" +
                "            },\n" +
                "            \"finished\": false,\n" +
                "            \"progress\": \"\",\n" +
                "            \"condition_type\": 1,\n" +
                "            \"condition_times\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": 37,\n" +
                "            \"title\": \"7日目\",\n" +
                "            \"desc\": \"その日のチェックインを完了する\",\n" +
                "            \"prize\": \"スーパーサプライズ宝箱\\n最高報酬 5000 金貨\",\n" +
                "            \"prize_type\": 6,\n" +
                "            \"extra\": {\n" +
                "                \"hint\": \"\",\n" +
                "                \"task_icon\": \"\",\n" +
                "                \"prize_icon\": \"https://media.ucoofun.com/opsite/itasks/jp-check-in-7-coin.webp\",\n" +
                "                \"big_gold_box\": true,\n" +
                "                \"predecessors\": null,\n" +
                "                \"twd_prize_value\": null\n" +
                "            },\n" +
                "            \"finished\": false,\n" +
                "            \"progress\": \"\",\n" +
                "            \"condition_type\": 1,\n" +
                "            \"condition_times\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"today_finished\": false,\n" +
                "    \"treasure_totips\": \"連続 0 日間ログインしました\",\n" +
                "    \"finished_totips\": \"あと 3 日間署名して、大きな宝箱の金貨報酬を受け取りましょう\",\n" +
                "    \"all_finished\": false\n" +
                "}", TaskSeries::class.java
    )
    SigninDialogContent(bean,{},{})
}

@Composable
@Preview(
    "签到列表",
    locale = "ja-rJP",
)
private fun SigninPreview() {
    SignInWidget(
        task = sAppJson.decodeFromString<TaskSeries>(
            "{\n" +
                    "    \"series_id\": 13,\n" +
                    "    \"series_title\": \"签到领金币\",\n" +
                    "    \"series_type\": 3,\n" +
                    "    \"tasks\": [\n" +
                    "        {\n" +
                    "            \"id\": 31,\n" +
                    "            \"title\": \"已领取\",\n" +
                    "            \"desc\": \"完成当日签到\",\n" +
                    "            \"prize\": \"5金币\",\n" +
                    "            \"prize_type\": 5,\n" +
                    "            \"extra\": {\n" +
                    "                \"hint\": \"\",\n" +
                    "                \"task_icon\": \"\",\n" +
                    "                \"prize_icon\": \"https://media.ucoofun.com/opsite/itasks/jp-check-in-12456-coin.webp\",\n" +
                    "                \"big_gold_box\": false,\n" +
                    "                \"predecessors\": null,\n" +
                    "                \"twd_prize_value\": null\n" +
                    "            },\n" +
                    "            \"finished\": true,\n" +
                    "            \"progress\": \"\",\n" +
                    "            \"condition_type\": 1,\n" +
                    "            \"condition_times\": 1\n" +
                    "        },\n" +
                    "        {\n" +
                    "            \"id\": 32,\n" +
                    "            \"title\": \"已领取\",\n" +
                    "            \"desc\": \"完成当日签到\",\n" +
                    "            \"prize\": \"10金币\",\n" +
                    "            \"prize_type\": 5,\n" +
                    "            \"extra\": {\n" +
                    "                \"hint\": \"\",\n" +
                    "                \"task_icon\": \"\",\n" +
                    "                \"prize_icon\": \"https://media.ucoofun.com/opsite/itasks/jp-check-in-12456-coin.webp\",\n" +
                    "                \"big_gold_box\": false,\n" +
                    "                \"predecessors\": null,\n" +
                    "                \"twd_prize_value\": null\n" +
                    "            },\n" +
                    "            \"finished\": true,\n" +
                    "            \"progress\": \"\",\n" +
                    "            \"condition_type\": 1,\n" +
                    "            \"condition_times\": 1\n" +
                    "        },\n" +
                    "        {\n" +
                    "            \"id\": 33,\n" +
                    "            \"title\": \"第三天\",\n" +
                    "            \"desc\": \"完成当日签到\",\n" +
                    "            \"prize\": \"大额宝箱\\n50金币\",\n" +
                    "            \"prize_type\": 6,\n" +
                    "            \"extra\": {\n" +
                    "                \"hint\": \"\",\n" +
                    "                \"task_icon\": \"\",\n" +
                    "                \"prize_icon\": \"https://media.ucoofun.com/opsite/itasks/jp-check-in-3-coin.webp\",\n" +
                    "                \"big_gold_box\": false,\n" +
                    "                \"predecessors\": null,\n" +
                    "                \"twd_prize_value\": null\n" +
                    "            },\n" +
                    "            \"finished\": false,\n" +
                    "            \"progress\": \"\",\n" +
                    "            \"condition_type\": 1,\n" +
                    "            \"condition_times\": 1\n" +
                    "        },\n" +
                    "        {\n" +
                    "            \"id\": 34,\n" +
                    "            \"title\": \"第四天\",\n" +
                    "            \"desc\": \"完成当日签到\",\n" +
                    "            \"prize\": \"15金币\",\n" +
                    "            \"prize_type\": 5,\n" +
                    "            \"extra\": {\n" +
                    "                \"hint\": \"\",\n" +
                    "                \"task_icon\": \"\",\n" +
                    "                \"prize_icon\": \"https://media.ucoofun.com/opsite/itasks/jp-check-in-12456-coin.webp\",\n" +
                    "                \"big_gold_box\": false,\n" +
                    "                \"predecessors\": null,\n" +
                    "                \"twd_prize_value\": null\n" +
                    "            },\n" +
                    "            \"finished\": false,\n" +
                    "            \"progress\": \"\",\n" +
                    "            \"condition_type\": 1,\n" +
                    "            \"condition_times\": 1\n" +
                    "        },\n" +
                    "        {\n" +
                    "            \"id\": 35,\n" +
                    "            \"title\": \"第五天\",\n" +
                    "            \"desc\": \"完成当日签到\",\n" +
                    "            \"prize\": \"20金币\",\n" +
                    "            \"prize_type\": 5,\n" +
                    "            \"extra\": {\n" +
                    "                \"hint\": \"\",\n" +
                    "                \"task_icon\": \"\",\n" +
                    "                \"prize_icon\": \"https://media.ucoofun.com/opsite/itasks/jp-check-in-12456-coin.webp\",\n" +
                    "                \"big_gold_box\": false,\n" +
                    "                \"predecessors\": null,\n" +
                    "                \"twd_prize_value\": null\n" +
                    "            },\n" +
                    "            \"finished\": false,\n" +
                    "            \"progress\": \"\",\n" +
                    "            \"condition_type\": 1,\n" +
                    "            \"condition_times\": 1\n" +
                    "        },\n" +
                    "        {\n" +
                    "            \"id\": 36,\n" +
                    "            \"title\": \"第六天\",\n" +
                    "            \"desc\": \"完成当日签到\",\n" +
                    "            \"prize\": \"25金币\",\n" +
                    "            \"prize_type\": 5,\n" +
                    "            \"extra\": {\n" +
                    "                \"hint\": \"\",\n" +
                    "                \"task_icon\": \"\",\n" +
                    "                \"prize_icon\": \"https://media.ucoofun.com/opsite/itasks/jp-check-in-12456-coin.webp\",\n" +
                    "                \"big_gold_box\": false,\n" +
                    "                \"predecessors\": null,\n" +
                    "                \"twd_prize_value\": null\n" +
                    "            },\n" +
                    "            \"finished\": false,\n" +
                    "            \"progress\": \"\",\n" +
                    "            \"condition_type\": 1,\n" +
                    "            \"condition_times\": 1\n" +
                    "        },\n" +
                    "        {\n" +
                    "            \"id\": 37,\n" +
                    "            \"title\": \"第七天\",\n" +
                    "            \"desc\": \"完成当日签到\",\n" +
                    "            \"prize\": \"超级惊喜宝箱\\n最高奖励5000金币\",\n" +
                    "            \"prize_type\": 6,\n" +
                    "            \"extra\": {\n" +
                    "                \"hint\": \"\",\n" +
                    "                \"task_icon\": \"\",\n" +
                    "                \"prize_icon\": \"https://media.ucoofun.com/opsite/itasks/jp-check-in-7-coin.webp\",\n" +
                    "                \"big_gold_box\": true,\n" +
                    "                \"predecessors\": null,\n" +
                    "                \"twd_prize_value\": null\n" +
                    "            },\n" +
                    "            \"finished\": false,\n" +
                    "            \"progress\": \"\",\n" +
                    "            \"condition_type\": 1,\n" +
                    "            \"condition_times\": 1\n" +
                    "        }\n" +
                    "    ],\n" +
                    "    \"today_finished\": true,\n" +
                    "    \"treasure_totips\": \"已连续签到2天\",\n" +
                    "    \"finished_totips\": \"再签1天，可领取大额宝箱金币奖励\",\n" +
                    "    \"all_finished\": false\n" +
                    "}"
        )
    ) {

    }
}


@Composable
@Preview(
    "任务列表",
    locale = "ja-rJP",
)
private fun TaskListPreview() {
    val list = sAppJson.decodeFromString<List<TaskSeries>>(
        "[\n" +
                "    {\n" +
                "        \"series_id\": 14,\n" +
                "        \"series_title\": \"做任务领金币\",\n" +
                "        \"multiplier_help\": \"做任务领金币\",\n" +
                "        \"series_type\": 2,\n" +
                "        \"tasks\": [\n" +
                "            {\n" +
                "                \"id\": 38,\n" +
                "                \"title\": \"上传自己的头像\",\n" +
                "                \"desc\": \"上传自己的头像\",\n" +
                "                \"prize\": \"10金币\",\n" +
                "                \"prize_type\": 5,\n" +
                "                \"extra\": {\n" +
                "                    \"hint\": \"\",\n" +
                "                    \"task_icon\": \"\",\n" +
                "                    \"prize_icon\": \"https://media.ucoofun.com/opsite/itasks/jp-check-in-12456-coin.webp\",\n" +
                "                    \"big_gold_box\": false,\n" +
                "                    \"predecessors\": null,\n" +
                "                    \"twd_prize_value\": null\n" +
                "                },\n" +
                "                \"finished\": true,\n" +
                "                \"progress\": \"\",\n" +
                "                \"condition_type\": 14,\n" +
                "                \"condition_times\": 1\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": 39,\n" +
                "                \"title\": \"填写个人简介\",\n" +
                "                \"desc\": \"填写个人简介\",\n" +
                "                \"prize\": \"15金币\",\n" +
                "                \"prize_type\": 5,\n" +
                "                \"extra\": {\n" +
                "                    \"hint\": \"\",\n" +
                "                    \"task_icon\": \"\",\n" +
                "                    \"prize_icon\": \"https://media.ucoofun.com/opsite/itasks/jp-check-in-12456-coin.webp\",\n" +
                "                    \"big_gold_box\": false,\n" +
                "                    \"predecessors\": null,\n" +
                "                    \"twd_prize_value\": null\n" +
                "                },\n" +
                "                \"finished\": true,\n" +
                "                \"progress\": \"\",\n" +
                "                \"condition_type\": 15,\n" +
                "                \"condition_times\": 1\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": 40,\n" +
                "                \"title\": \"填写所有个人基础信息\",\n" +
                "                \"desc\": \"填写所有个人基础信息\",\n" +
                "                \"prize\": \"30金币\",\n" +
                "                \"prize_type\": 5,\n" +
                "                \"extra\": {\n" +
                "                    \"hint\": \"\",\n" +
                "                    \"task_icon\": \"\",\n" +
                "                    \"prize_icon\": \"https://media.ucoofun.com/opsite/itasks/jp-check-in-12456-coin.webp\",\n" +
                "                    \"big_gold_box\": false,\n" +
                "                    \"predecessors\": null,\n" +
                "                    \"twd_prize_value\": null\n" +
                "                },\n" +
                "                \"finished\": true,\n" +
                "                \"progress\": \"\",\n" +
                "                \"condition_type\": 16,\n" +
                "                \"condition_times\": 1\n" +
                "            }\n" +
                "        ],\n" +
                "        \"today_finished\": true,\n" +
                "        \"treasure_totips\": \"下列为新手任务，完成后可解锁每日任务\",\n" +
                "        \"finished_totips\": \"\",\n" +
                "        \"all_finished\": true\n" +
                "    },\n" +
                "    {\n" +
                "        \"series_id\": 17,\n" +
                "        \"series_title\": \"做任务领金币2\",\n" +
                "        \"series_type\": 4,\n" +
                "        \"tasks\": [\n" +
                "            {\n" +
                "                \"id\": 52,\n" +
                "                \"title\": \"完成1次Say Hi\",\n" +
                "                \"desc\": \"完成1次Say Hi\",\n" +
                "                \"prize\": \"10金币\",\n" +
                "                \"prize_type\": 5,\n" +
                "                \"extra\": {\n" +
                "                    \"hint\": \"\",\n" +
                "                    \"task_icon\": \"\",\n" +
                "                    \"prize_icon\": \"\",\n" +
                "                    \"big_gold_box\": false,\n" +
                "                    \"predecessors\": null,\n" +
                "                    \"twd_prize_value\": null\n" +
                "                },\n" +
                "                \"finished\": true,\n" +
                "                \"progress\": \"\",\n" +
                "                \"condition_type\": 17,\n" +
                "                \"condition_times\": 1\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": 53,\n" +
                "                \"title\": \"发送3条消息\",\n" +
                "                \"desc\": \"发送3条消息\",\n" +
                "                \"prize\": \"15金币\",\n" +
                "                \"prize_type\": 5,\n" +
                "                \"extra\": {\n" +
                "                    \"hint\": \"\",\n" +
                "                    \"task_icon\": \"\",\n" +
                "                    \"prize_icon\": \"\",\n" +
                "                    \"big_gold_box\": false,\n" +
                "                    \"predecessors\": null,\n" +
                "                    \"twd_prize_value\": null\n" +
                "                },\n" +
                "                \"finished\": true,\n" +
                "                \"progress\": \"\",\n" +
                "                \"condition_type\": 18,\n" +
                "                \"condition_times\": 3\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": 54,\n" +
                "                \"title\": \"赠送1个礼物\",\n" +
                "                \"desc\": \"赠送1个礼物\",\n" +
                "                \"prize\": \"30金币\",\n" +
                "                \"prize_type\": 5,\n" +
                "                \"extra\": {\n" +
                "                    \"hint\": \"\",\n" +
                "                    \"task_icon\": \"\",\n" +
                "                    \"prize_icon\": \"\",\n" +
                "                    \"big_gold_box\": false,\n" +
                "                    \"predecessors\": null,\n" +
                "                    \"twd_prize_value\": null\n" +
                "                },\n" +
                "                \"finished\": false,\n" +
                "                \"progress\": \"\",\n" +
                "                \"condition_type\": 3,\n" +
                "                \"condition_times\": 1\n" +
                "            }\n" +
                "        ],\n" +
                "        \"today_finished\": false,\n" +
                "        \"treasure_totips\": \"每日任务0点更新\",\n" +
                "        \"finished_totips\": \"\",\n" +
                "        \"all_finished\": false\n" +
                "    }\n" +
                "]"
    )
    PreviewCupidTheme {
        Column {
            list.forEach {
                NewbieTaskWidget(task = it) {

                }
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}


sealed interface TaskAction {

    data class Button(
        val enable: Boolean,
        val text: String,
        val onClick: () -> Unit,
    ) : TaskAction

    data class Icon(val model: Any) : TaskAction
}

@Composable
private fun Item(
    title: String,
    desc: String,
    rewardValue: String,
    rewardIcon: Any,
    action: TaskAction,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(5.dp)
        ) {
            Text(
                text = title,
                modifier = Modifier,
                color = Color(0xFF1D2129),
                fontSize = 14.sp
            )

            Text(
                text = desc,
                modifier = Modifier,
                color = Color(0xFF86909C),
                fontSize = 12.sp
            )

            Row(verticalAlignment = Alignment.CenterVertically) {
                ComposeImage(
                    model = rewardIcon,
                    modifier = Modifier.size(20.dp),
                    contentScale = ContentScale.Fit
                )

                Text(
                    text = rewardValue,
                    modifier = Modifier.padding(start = 5.dp),
                    color = Color(0xFFFF5E8B),
                    fontSize = 16.sp
                )
            }
        }


        when (action) {
            is TaskAction.Button -> {
                ElevatedButton(
                    onClick = composeClick(onClick = action.onClick),
                    modifier = Modifier
                        .padding(start = 8.dp)
                        .widthIn(min = 72.dp)
                        .height(28.dp),
                    contentPadding = PaddingValues(horizontal = 8.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = colorResource(id = R.color.FFFF5E8B),
                        contentColor = Color.White,
                        disabledContainerColor = colorResource(id = R.color.FFFF5E8B).copy(alpha = 0.5f),
                        disabledContentColor = Color.White.copy(alpha = 0.5f)
                    ),
                    enabled = action.enable,
                    elevation = null
                ) {
                    AutoSizeText(
                        text = action.text,
                        fontSize = 14.sp,
                    )
                }
            }

            is TaskAction.Icon -> {
                ComposeImage(
                    model = action.model,
                    modifier = Modifier
                        .padding(start = 48.dp)
                        .size(32.dp),
                    contentScale = ContentScale.Fit
                )
            }
        }
    }
}

@Preview
@Composable
private fun PreviewItem() {
    Column {
        Item(
            "文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案",
            "文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案",
            "+10086",
            R.drawable.ic_cpd_diamond,
            TaskAction.Button(true, stringResource(id = R.string.cpd去完成), {})
        )

        Item(
            "文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案",
            "文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案",
            "+10086",
            R.drawable.ic_cpd_diamond,
            TaskAction.Button(false, stringResource(id = R.string.cpd已完成), {})
        )

        Item(
            "文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案",
            "文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案",
            "+10086",
            R.drawable.ic_cpd_diamond,
            TaskAction.Icon(R.drawable.ic_cpd_diamond)
        )
    }
}

@Preview
@Composable
private fun PreviewItem2() {
    Row(
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .background(Color(0xFFFFE7EE), RoundedCornerShape(6.dp))
            .padding(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {

        Text(
            text = "文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案",
            modifier = Modifier.weight(1f),
            color = Color(0xFFFF5E8B),
            fontSize = 12.sp
        )

        if (true) {
            Box(
                modifier = Modifier
                    .padding(8.dp)
                    .height(24.dp)
                    .background(Color(0xFFFF5E8B), CircleShape)
                    .padding(horizontal = 8.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "desc",
                    color = Color(0xFFFFFFFF),
                    fontSize = 12.sp
                )
            }
        }
    }
}