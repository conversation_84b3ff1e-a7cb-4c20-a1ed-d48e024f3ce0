

package com.qyqy.cupid.ui.coin

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.appendInlineContent
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.Placeholder
import androidx.compose.ui.text.PlaceholderVerticalAlign
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.data.WithdrawList
import com.qyqy.cupid.model.WithdrawViewModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.dialog.DialogButton
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.dialog.TitleAlertDialog
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.composeClick
import com.qyqy.ucoo.R
import com.qyqy.ucoo.accountManager
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.DirectionState
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.http.getStringOrNull
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.utils.LogUtils
import kotlinx.coroutines.launch
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive

/**
 *  @time 9/10/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.coin
 */

private interface WithdrawInfoAction {
    data class UpdateInputValue(val pos: Int, val item: WithdrawInfoItem, val value: String) : WithdrawInfoAction
    data object onAskClicked : WithdrawInfoAction
    data object onInfoNextStepClicked : WithdrawInfoAction
    data object onWithdrawClicked : WithdrawInfoAction
}


@Composable
fun WithdrawInfoPage(withdrawWayJson: String) {
    var currentWay by remember {
        mutableStateOf(sAppJson.decodeFromString<WithdrawList.WithdrawType>(withdrawWayJson))
    }

    val controller = LocalAppNavController.current
    val dialogQueue = LocalDialogQueue.current
    val loadingFlag = LocalContentLoading.current
    val scope = rememberCoroutineScope()
    val viewModel = viewModel(modelClass = WithdrawViewModel::class)
    //信息未填充
    var list by remember {
        mutableStateOf(
            if (currentWay.id == 1) {
                listOf(
                    WithdrawInfoItem("PAYPAY登録電話番号またはID", "PAYPAY登録電話番号またはIDを入力してください。", "account"),
                    WithdrawInfoItem("PAYPAYのアカウント", "PAYPAYのアカウントを入力してください", "name", "情報は正確にご記入ください。万が一、ご記入内容にお間違いがあり損失が発生した場合は、ご本人様のご負担となります！"),
                    WithdrawInfoItem("LineのID", "LineのID を入力してください", "line_id", "お支払い手続き中に問題が発生した場合は、公式運営者よりLINEにてご連絡させていただきます。")
                )
            } else {
                listOf(
                    WithdrawInfoItem("銀行口座番号", "銀行口座番号を入力してください", "account"),
                    WithdrawInfoItem("Zengin Code", "zengin codeを入力してください", "zengin_code"),
                    WithdrawInfoItem("銀行口座名", "銀行口座名を入力してください", "bank_name"),
                    WithdrawInfoItem("支店の名前", "支店の名前を入力してください", "subbank_name"),
                    WithdrawInfoItem("受取人の名前", "受取人の名前を入力してください", "name", "情報は正確にご記入ください。万が一、ご記入内容にお間違いがあり損失が発生した場合は、ご本人様のご負担となります！"),
                    WithdrawInfoItem("都道府県", "都道府県を入力してください", "city"),
                    WithdrawInfoItem("郵便番号", "郵便番号を入力してください", "postcode"),
                )
            }
        )
    }

    LaunchedEffect(key1 = currentWay) {
        list = list.toMutableList().apply {
            forEachIndexed { index, item ->
                try {
                    this[index] = item.copy(value = currentWay.account?.getStringOrNull(item.key) ?: "")
                } catch (e: Exception) {
                    LogUtils.w("xzwzz", index, item, currentWay.account.toString(), e)
                }
            }
        }
    }

    if (currentWay.account == null) {
        WithdrawInfoContent(list = list) { action ->
            when (action) {
                is WithdrawInfoAction.UpdateInputValue -> {
                    list = list.toMutableList().apply {
                        if (action.pos < list.size) {
                            this[action.pos] = action.item.copy(value = action.value)
                        }
                    }
                }

                WithdrawInfoAction.onAskClicked -> {
                    scope.launch {
                        loadingFlag.value = true
                        viewModel.getWithdrawHelper().onSuccess {
                            it.get("jump_link")?.jsonPrimitive?.contentOrNull?.let {
                                controller.navigateByLink(it)
                            }
                            loadingFlag.value = false
                        }.onFailure {
                            loadingFlag.value = false
                        }.toastError()
                    }
                }

                WithdrawInfoAction.onInfoNextStepClicked -> {
                    //请求创建账户信息
                    scope.launch {

                        for (item in list) {
                            if (item.value.isBlank()) {
                                toast(item.hint)
                                return@launch
                            }
                        }
                        loadingFlag.value = true
                        val params = list.map { it.key to it.value }
                        viewModel.createWithdrawAccount(currentWay.type, params).onSuccess {
                            loadingFlag.value = false
                            currentWay = currentWay.copy(account = it.get("account")?.jsonObject)
                        }.onFailure {
                            loadingFlag.value = false
                        }.toastError()
                    }
                }

                else -> {}
            }
        }
    } else {
        WithdrawConfirmContent(currentWay, list) { action ->
            when (action) {
                is WithdrawInfoAction.onWithdrawClicked -> {
                    scope.launch {
                        loadingFlag.value = true
                        viewModel.createWithdrawOrder(currentWay.id).onSuccess {
                            loadingFlag.value = false
                            controller.popBackStack()
//                            it.getStringOrNull("toast")
                            val content = it.getStringOrNull("hint")
                            dialogQueue.push(direction = DirectionState.CENTER) { idialog, actions ->
                                TitleAlertDialog(
                                    modifier = Modifier.width(270.dp),
                                    title = stringResource(id = R.string.cpd_warn_tips),
                                    content = content,
                                    endButton = DialogButton(stringResource(id = R.string.cpd_iknow), { idialog.dismiss() })
                                )
                                accountManager.refreshSelfUserByRemote()
                            }
                        }.onFailure {
                            loadingFlag.value = false
                        }.toastError()
                    }

                }

                else -> {}
            }

        }
    }


}

/**
 * 提现信息item
 *
 * @property title 显示的标题
 * @property hint 输入框中的默认字符
 * @property key 上传时用到的key
 * @property desc 描述, 部分item需要tips
 * @property value 输入的值
 */
data class WithdrawInfoItem(val title: String, val hint: String, val key: String, val desc: String? = null, val value: String = "")

/**
 * 提交提现信息页面
 *
 * @param list 需要提交的字段
 * @param onAction 事件
 */
@Composable
private fun WithdrawInfoContent(
    list: List<WithdrawInfoItem>,
    onAction: (WithdrawInfoAction) -> Unit
) {
    Column {
        CupidAppBar(title = stringResource(id = R.string.cpd_write_withdraw_info))
        LazyColumn(
            modifier = Modifier
                .weight(1f)
                .background(color = CpdColors.FFF5F7F9)
                .imePadding()
        ) {
            itemsIndexed(list) { index, item ->
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                ) {
                    Spacer(modifier = Modifier.height(20.dp))
                    Text(item.title, color = CpdColors.FF1D2129, fontSize = 14.sp)
                    Spacer(modifier = Modifier.height(12.dp))
                    BasicTextField(
                        value = item.value,
                        onValueChange = { onAction(WithdrawInfoAction.UpdateInputValue(index, item, it)) },
                        singleLine = true,
                        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                        keyboardActions = KeyboardActions(onDone = {

                        }),
                        textStyle = MaterialTheme.typography.bodyMedium.copy(
                            lineHeight = 18.sp,
                            fontSize = 14.sp,
                            color = CpdColors.FF1D2129,
                        ),
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color.White, RoundedCornerShape(4.dp))
                            .padding(12.dp),
                        cursorBrush = SolidColor(CpdColors.FFFF5E8B),
                        decorationBox = { fn ->
                            fn.invoke()
                            if (item.value.isNullOrBlank()) {
                                Text(
                                    text = item.hint,
                                    style = MaterialTheme.typography.labelMedium,
                                    color = CpdColors.FF86909C,
                                )
                            }
                        }
                    )
                    if (item.desc != null) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            item.desc,
                            modifier = Modifier.fillMaxWidth(), color = CpdColors.FF86909C, fontSize = 12.sp
                        )
                    }
                }
            }
            item {
                Spacer(modifier = Modifier.height(32.dp))
            }
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(color = Color.White)
                .padding(horizontal = 16.dp, vertical = 12.dp)
                .navigationBarsPadding()
        ) {
            AppButton(
                text = stringResource(id = R.string.cpd_start_ask),
                onClick = composeClick {
                    onAction(WithdrawInfoAction.onAskClicked)
                },
                border = BorderStroke(1.dp, color = CpdColors.FFC9CDD4),
                background = Color.White,
                textStyle = TextStyle(color = CpdColors.FF4E5969, fontSize = 14.sp),
                contentPadding = PaddingValues(horizontal = 12.dp),
                modifier = Modifier
                    .widthIn(72.dp)
                    .height(44.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            AppButton(
                text = stringResource(id = R.string.cpd_next_step),
                onClick = composeClick {
                    onAction(WithdrawInfoAction.onInfoNextStepClicked)
                },
                border = BorderStroke(1.dp, color = CpdColors.FFFF5E8B),
                background = CpdColors.FFFF5E8B,
                textStyle = TextStyle(color = Color.White, fontSize = 14.sp),
                contentPadding = PaddingValues(horizontal = 12.dp),
                modifier = Modifier
                    .weight(1f)
                    .height(44.dp)
            )
        }
    }

}

/**
 * 确认收款信息
 */
@Composable
private fun WithdrawConfirmContent(withdrawType: WithdrawList.WithdrawType, infos: List<WithdrawInfoItem>, onAction: (WithdrawInfoAction) -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(color = CpdColors.FFF5F7F9)
    ) {
        CupidAppBar(title = stringResource(id = R.string.cpd_confirm_withdraw_info))
        LazyColumn(
            modifier = Modifier
                .padding(16.dp)
                .background(color = Color.White, shape = RoundedCornerShape(8.dp))
                .padding(horizontal = 16.dp)
        ) {
            item {
                Row(modifier = Modifier.height(64.dp), verticalAlignment = Alignment.CenterVertically) {
                    Text(text = stringResource(id = R.string.cpd_withdrawa_way), fontSize = 16.sp, color = CpdColors.FF1D2129)
                    val annotatedString = buildAnnotatedString {
                        appendInlineContent("prefixIcon")
                        withStyle(
                            style = SpanStyle(
                                fontSize = 14.sp, color = CpdColors.FF86909C,

                                )
                        ) {
                            append(withdrawType.name)
                        }
                    }
                    val inlineContentMap = mapOf(
                        "prefixIcon" to InlineTextContent(
                            Placeholder(
                                width = 24.sp,
                                height = 24.sp,
                                placeholderVerticalAlign = PlaceholderVerticalAlign.TextCenter
                            )
                        ) {
                            ComposeImage(model = withdrawType.icon, modifier = Modifier.size(20.dp))
                        }
                    )
                    Text(
                        text = annotatedString, inlineContent = inlineContentMap, modifier = Modifier.weight(1f),
                        textAlign = TextAlign.End,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            itemsIndexed(infos) { index, item ->
                HorizontalDivider(thickness = 0.3.dp, color = CpdColors.FFF0F0F0)
                Row(modifier = Modifier.height(64.dp), verticalAlignment = Alignment.CenterVertically) {
                    Text(text = item.title, fontSize = 16.sp, color = CpdColors.FF1D2129)
                    Text(
                        text = item.value, fontSize = 14.sp, color = CpdColors.FF86909C, modifier = Modifier.weight(1f),
                        textAlign = TextAlign.End,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                }
            }
            item {
                Row(modifier = Modifier.height(64.dp), verticalAlignment = Alignment.CenterVertically) {
                    Text(text = stringResource(id = R.string.cpd_withdrawa_request_money), fontSize = 16.sp, color = CpdColors.FF1D2129)
                    Text(
                        text = "${sUser.cash}${stringResource(id = R.string.cpd_cash_unit_en)}",
                        fontSize = 14.sp, color = CpdColors.FF86909C, modifier = Modifier.weight(1f),
                        textAlign = TextAlign.End,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                }
            }
        }

        Spacer(modifier = Modifier.weight(1f))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(color = Color.White)
                .padding(horizontal = 16.dp, vertical = 12.dp)
                .navigationBarsPadding()
        ) {
            AppButton(
                text = stringResource(id = R.string.cpd_start_withdraw),
                onClick = composeClick {
                    onAction(WithdrawInfoAction.onWithdrawClicked)
                },
                border = BorderStroke(1.dp, color = CpdColors.FFFF5E8B),
                background = CpdColors.FFFF5E8B,
                textStyle = TextStyle(color = Color.White, fontSize = 14.sp),
                contentPadding = PaddingValues(horizontal = 12.dp),
                modifier = Modifier
                    .weight(1f)
                    .height(44.dp)
            )
        }
    }
}

//region 预览

@Composable
@Preview(showBackground = true)
private fun WithdrawInfoPagePreview() {
    val list = remember {
        listOf(
            WithdrawInfoItem("PayPay注册电话或ID", "请输入PAYPAY注册电话或ID", "paypay_id", null),
            WithdrawInfoItem("PAYPAY账户名称", "请输入PAYPAY账户名称", "paypay_id", "请确保填写正确的账户信息，如果由于信息填写错误导致的损失，需要由您本人承担。"),
            WithdrawInfoItem("LINE ID", "数据", "paypay_id", "如果付款过程中遇到问题，官方运营将通过LINE联系您")
        )
    }
    WithdrawInfoContent(list = list) {

    }
}

@Composable
@Preview(showBackground = true)
private fun WithdrawBankCardPagePreview() {
    val list = remember {
        listOf(
            WithdrawInfoItem("銀行口座番号", "请输入銀行口座番号", "band_card"),
            WithdrawInfoItem("Zengin Code", "请输入Zengin Code", "paypay_id"),
            WithdrawInfoItem("銀行口座名", "请输入銀行口座名", "paypay_id"),
            WithdrawInfoItem("支店の名前", "请输入支店の名前", "paypay_id"),
            WithdrawInfoItem("受取人の名前", "请输入受取人の名前", "paypay_id", "请确保填写正确的账户信息，如果由于信息填写错误导致的损失，需要由您本人承担。"),
            WithdrawInfoItem("都道府県", "请输入都道府県", "paypay_id"),
            WithdrawInfoItem("郵便番号", "请输入郵便番号", "paypay_id"),
        )
    }

    WithdrawInfoContent(list = list) {

    }
}

@Composable
@Preview(showBackground = true)
private fun WithdrawConfirmPagePreview() {
    WithdrawConfirmContent(WithdrawList.WithdrawType(), listOf()) {

    }
}

//endregion