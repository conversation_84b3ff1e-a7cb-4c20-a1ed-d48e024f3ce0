package com.qyqy.cupid.ui.coin

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.cupid.apis.WithdrawApi
import com.qyqy.cupid.data.WithdrawRecordBean
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.state.CupidStateListView
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.state.StateViewModelWithIntPage
import com.qyqy.ucoo.http.ApiException
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.toast
import kotlinx.serialization.json.decodeFromJsonElement
import java.text.SimpleDateFormat

/**
 *  @time 9/10/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.coin
 */

private class WithdrawListViewModel : StateViewModelWithIntPage<WithdrawRecordBean>() {
    private val api by lazy { createApi<WithdrawApi>() }

    override suspend fun loadData(pageNum: Int): Result<List<WithdrawRecordBean>> {
        val id = if (pageNum == firstPage) 0 else (listFlow.value.lastOrNull()?.id ?: 0)
        val result = runApiCatching { api.getWithdrawRecords(id) }

        return if (result.isSuccess) {
            val list = result.getOrNull()?.get("withdraws")?.let {
                sAppJson.decodeFromJsonElement<List<WithdrawRecordBean>>(it)
            } ?: listOf()

            Result.success(list)
        } else {
            Result.failure<List<WithdrawRecordBean>>(result.exceptionOrNull() ?: ApiException(msg = "error")).also {
                toast(it.exceptionOrNull()?.message ?: "")
            }
        }
    }
}


@Composable
fun WithdrawRecordPage() {
    val viewModel = viewModel(modelClass = WithdrawListViewModel::class, factory = viewModelFactory {
        initializer {
            WithdrawListViewModel()
        }
    })
    LaunchedEffect(key1 = Unit) {
        viewModel.refresh()
    }
    Column {
        CupidAppBar(title = stringResource(id = R.string.cpd_withdrawa_records))
        HorizontalDivider(thickness = 0.3.dp, color = Color(0xffF0F0F0))
        CupidStateListView(viewModel = viewModel,
            keyProvider = { _, item -> item.id }) { item, pos, _, _ ->
            Item(item)
            HorizontalDivider(thickness = 0.3.dp, color = Color(0xffF0F0F0), modifier = Modifier.padding(horizontal = 16.dp))
        }
    }
}

@Composable
private fun Item(bean: WithdrawRecordBean) {
    val colors = remember {
        if (bean.status < 1) {
            Color(0xFF00B42A) to Color(0xFFE8FFEA)
        } else if (bean.status < 3) {
            Color(0xFF4E5969) to Color(0xFFF2F3F5)
        } else {
            Color(0xFFF53F3F) to Color(0xFFFFECE8)
        }
    }
    val formater = remember {
        SimpleDateFormat("MM-dd HH:mm")
    }
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(72.dp)
            .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f, true)) {
            Text(
                "${stringResource(id = R.string.cpd_withdrawa_money, bean.amount)}${stringResource(id = R.string.cpd_cash_unit)}",
                color = CpdColors.FF1D2129,
                fontSize = 12.sp,
                lineHeight = 12.sp
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                formater.format(bean.createTime * 1000L),
                color = CpdColors.FF86909C,
                fontSize = 12.sp,
                lineHeight = 12.sp
            )
        }

        Text(
            when (bean.status) {
                0 -> stringResource(id = R.string.cpd_order_doing)
                1 -> stringResource(id = R.string.cpd_order_done)
                2 -> stringResource(id = R.string.cpd_order_failed)
                3 -> stringResource(id = R.string.cpd_order_decline)
                else -> ""
            }, modifier =
            Modifier
                .background(color = colors.second, shape = RoundedCornerShape(4.dp))
                .padding(6.dp),
            fontSize = 12.sp, lineHeight = 12.sp, color = colors.first
        )
    }
}

@Composable
@Preview()
private fun WithdrawRecordPagePreview() {
    Item(
        WithdrawRecordBean(status = 0, createTime = 1726026287)
    )
}