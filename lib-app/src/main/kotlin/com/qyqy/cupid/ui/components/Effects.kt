package com.qyqy.cupid.ui.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.qyqy.ucoo.utils.LogUtils
import com.yy.yyeva.EvaAnimConfig
import com.yy.yyeva.inter.IEvaAnimListener
import com.yy.yyeva.util.EvaConstant
import com.yy.yyeva.view.EvaAnimViewV3
import kotlinx.coroutines.launch
import java.io.File

@Composable
fun EffectView(
    url: String,
    effectFileLoader: suspend (String) -> File?,
    modifier: Modifier = Modifier,
    mute: Boolean = false,
    loop: Boolean = true,
) {
    var file by remember(url) {
        mutableStateOf<File?>(null)
    }
    val scope = rememberCoroutineScope()
    DisposableEffect(key1 = url) {
        scope.launch {
            file = effectFileLoader.invoke(url)
        }
        onDispose {
            file = null
        }
    }

    val f = file
    Box(modifier = modifier) {
        if (f != null) {
            AndroidView(
                factory = {
                    EvaAnimViewV3(it).apply {
                        setVideoMode(EvaConstant.VIDEO_MODE_SPLIT_HORIZONTAL)
                        setMute(mute)
                        setLoop(loop)
                    }
                },
                modifier = Modifier.fillMaxSize(),
                onReset = {
                    it.stopPlay()
                    it.setAnimListener(null)
                },
                onRelease = {
                    it.stopPlay()
                    it.setAnimListener(null)
                }
            ) {
//                it.setAnimListener(null)
//                it.stopPlay()
                it.setAnimListener(object : IEvaAnimListener {
                    override fun onFailed(errorType: Int, errorMsg: String?) {
                        LogUtils.e("effect", "$url\n$errorMsg,$errorType")
                    }

                    override fun onVideoComplete(lastFrame: Boolean) {
//                        it.startPlay(f)
                    }

                    override fun onVideoDestroy() = Unit

                    override fun onVideoRender(frameIndex: Int, config: EvaAnimConfig?) = Unit

                    override fun onVideoRestart() = Unit

                    override fun onVideoStart(isRestart: Boolean) = Unit
                })
                it.startPlay(f)
            }
        } else {
            CircularProgressIndicator(
                color = Color.White,
                modifier = Modifier
                    .size(24.dp)
                    .align(Alignment.Center)
            )
        }
    }
}