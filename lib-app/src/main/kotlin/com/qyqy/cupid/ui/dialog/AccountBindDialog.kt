

package com.qyqy.cupid.ui.dialog

import android.os.Parcelable
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.linecorp.linesdk.LineApiResponseCode
import com.qyqy.cupid.ui.login.GoogleAuthLoginDelegate
import com.qyqy.cupid.ui.login.LineLoginContract
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.BuildConfig
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AccountBindInfo
import com.qyqy.ucoo.accountManager
import com.qyqy.ucoo.app
import com.qyqy.ucoo.asFragmentActivity
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.ui.AnyPopDialogProperties
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.DirectionState
import com.qyqy.ucoo.login.LoginChannel
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.utils.LogUtil
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize

/**
 *  @time 8/20/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.dialog
 */
@Parcelize
data class AccountBindDialog(val bindInfo: AccountBindInfo) : SimpleAnimatedDialog(), Parcelable {

    override val properties: AnyPopDialogProperties
        get() = AnyPopDialogProperties(true, true, direction = DirectionState.CENTER)

    @Composable
    override fun Content(dialog: IDialog) {
        AccountBindContent(bindInfo = bindInfo) {
            dialog.dismiss()
        }
    }
}

@Composable
fun AccountBindContent(
    bindInfo: AccountBindInfo,
    onSucceed: ((channel: Int, token: String) -> Unit)? = null,
    onDismiss: () -> Unit = {}
) {
    val activity = LocalContext.current.asFragmentActivity
    val scope = rememberCoroutineScope()

    Column(
        modifier = Modifier
            .background(Color.White, RoundedCornerShape(8.dp))
            .padding(horizontal = 16.dp, vertical = 20.dp)
            .widthIn(240.dp, 270.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            bindInfo.unboundOutsideAccountTips,
            color = Color(0xFF4E5969),
            fontSize = 15.sp
        )
        Spacer(modifier = Modifier.height(20.dp))
        Row(
            horizontalArrangement = Arrangement.spacedBy(32.dp)
        ) {
            bindInfo.canBindOutsideAccount.forEach { channelType ->
                when (channelType) {
                    LoginChannel.LINE -> {
                        val lineLauncher = rememberLauncherForActivityResult(contract = LineLoginContract()) { result ->
                            when (result.responseCode) {
                                LineApiResponseCode.SUCCESS -> {
                                    scope.launch {
                                        val credential = result.lineCredential
                                        val token = credential!!.accessToken.tokenString
                                        if (onSucceed != null) {
                                            onSucceed.invoke(LoginChannel.LINE, token)
                                        } else {
                                            app.accountManager
                                                .bindThirdAccount(LoginChannel.LINE, token)
                                                .onSuccess {
                                                    accountManager.refreshBindState()
                                                    onDismiss()
                                                }
                                                .toastError()
                                        }
                                    }
                                }

                                LineApiResponseCode.CANCEL -> {
                                    toastRes(R.string.cpd_login_cancel)
                                }

                                else -> {
                                    toastRes(R.string.cpd_login_cancel)
                                    LogUtil.w("bind failed. code =  ${result.responseCode}", "LineAuthLoginDelegate")
                                }
                            }
                        }
                        ComposeImage(
                            model = R.drawable.ic_cpd_line_bind,
                            modifier = Modifier
                                .size(48.dp)
                                .click {
                                    lineLauncher.launch(Unit)
                                }
                        )
                    }

                    LoginChannel.GOOGLE -> {
                        ComposeImage(
                            model = R.drawable.ic_cpd_google_bind,
                            modifier = Modifier
                                .size(48.dp)
                                .click {
                                    scope.launch {
                                        try {
                                            GoogleAuthLoginDelegate(activity!!, BuildConfig.GOOGLE_WEB_CLIENT_ID)
                                                .startAuth()
                                                ?.let { token ->
                                                    if (onSucceed != null) {
                                                        onSucceed.invoke(LoginChannel.GOOGLE, token)
                                                    } else {
                                                        app.accountManager
                                                            .bindThirdAccount(LoginChannel.GOOGLE, token)
                                                            .onSuccess {
                                                                accountManager.refreshBindState()
                                                                onDismiss()
                                                            }
                                                            .toastError()
                                                    }
                                                }
                                        } catch (e: Exception) {
                                            e.printStackTrace()
                                            e.toast()
                                        }
                                    }
                                }
                        )
                    }
                }
            }
        }
    }
}

@Composable
@Preview(showBackground = true)
fun AccountBindPreview() {
    AccountBindContent(
        bindInfo = AccountBindInfo(
            unboundOutsideAccountTips = "为保障您的账号信息安全，给您提供更好的服务，请绑定您的第三方平台账号",
            needBindOutsideAccount = true,
            showBindAccountButton = false,
            canBindOutsideAccount = listOf(2, 4, 8)
        )
    )
}
