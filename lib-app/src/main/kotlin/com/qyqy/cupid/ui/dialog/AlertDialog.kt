package com.qyqy.cupid.ui.dialog

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.presentation.room.RichText
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.im.message.entity.RichItem
import com.qyqy.ucoo.utils.OnClick

@Composable
fun AlertDialogContent(
    icon: (@Composable () -> Unit)?,
    title: AnnotatedString?,
    content: AnnotatedString?,
    startButtonText: String?,
    startButtonClick: OnClick?,
    endButtonText: String?,
    endButtonClick: OnClick?,
    modifier: Modifier = Modifier,
    iconSetTop: Boolean = true,
) {
    check(title != null || content != null)
    Column(
        modifier = Modifier
            .background(Color(0xFFFFFFFF), RoundedCornerShape(8.dp))
            .padding(horizontal = 16.dp, vertical = 20.dp)
            .then(modifier),
        verticalArrangement = Arrangement.spacedBy(12.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (iconSetTop) {
            if (icon != null) {
                icon()
            }

            if (title != null) {
                Text(text = title, color = Color(0xFF1D2129), fontSize = 16.sp, textAlign = TextAlign.Center)
            }

            if (content != null) {
                Text(text = content, color = Color(0xFF86909C), fontSize = 15.sp, textAlign = TextAlign.Center)
            }
        } else {
            if (title != null) {
                Text(text = title, color = Color(0xFF1D2129), fontSize = 16.sp, textAlign = TextAlign.Center)

                if (icon != null) {
                    icon()
                }

                if (content != null) {
                    Text(text = content, color = Color(0xFF86909C), fontSize = 15.sp, textAlign = TextAlign.Center)
                }
            } else {
                if (content != null) {
                    Text(text = content, color = Color(0xFF86909C), fontSize = 15.sp, textAlign = TextAlign.Center)
                }

                if (icon != null) {
                    icon()
                }
            }
        }

        if (startButtonText != null || endButtonText != null) {

            Spacer(modifier = Modifier.height(8.dp))

            Row(horizontalArrangement = Arrangement.spacedBy(12.dp)) {
                if (startButtonText != null) {
                    AppButton(
                        text = startButtonText,
                        background = Color(0xFFF1F2F3),
                        color = Color(0xFF86909C),
                        fontSize = 16.sp,
                        modifier = Modifier
                            .widthIn(min = 113.dp, max = 200.dp)
                            .height(36.dp)
                            .weight(1f, false),
                        contentPadding = PaddingValues(horizontal = 6.dp)
                    ) {
                        startButtonClick?.invoke()
                    }
                }

                if (endButtonText != null) {
                    AppButton(
                        text = endButtonText,
                        background = Color(0xFFFF5E8B),
                        color = Color(0xFFFFFFFF),
                        fontSize = 16.sp,
                        modifier = Modifier
                            .widthIn(min = 113.dp, max = 200.dp)
                            .height(36.dp)
                            .weight(1f, false),
                        contentPadding = PaddingValues(horizontal = 6.dp)
                    ) {
                        endButtonClick?.invoke()
                    }
                }
            }
        }
    }
}

data class DialogButton(
    val text: String,
    val onClick: OnClick,
)


@Composable
fun TitleAlertDialog(
    title: String,
    content: String? = null,
    startButton: DialogButton? = null,
    endButton: DialogButton? = null,
    modifier: Modifier = Modifier,
) {
    AlertDialogContent(
        null,
        title.let {
            buildAnnotatedString {
                append(it)
            }
        },
        content?.let {
            buildAnnotatedString {
                append(it)
            }
        },
        startButton?.text,
        startButton?.onClick,
        endButton?.text,
        endButton?.onClick,
        modifier
    )
}

@Composable
fun ContentAlertDialog(
    content: String,
    title: String? = null,
    startButton: DialogButton? = null,
    endButton: DialogButton? = null,
    modifier: Modifier = Modifier,
) {
    AlertDialogContent(
        null,
        title?.let {
            buildAnnotatedString {
                append(it)
            }
        },
        content.let {
            buildAnnotatedString {
                append(it)
            }
        },
        startButton?.text,
        startButton?.onClick,
        endButton?.text,
        endButton?.onClick,
        modifier = modifier
    )
}

@Composable
fun IconAlertDialog(
    icon: @Composable () -> Unit,
    content: String,
    title: String? = null,
    startButton: DialogButton? = null,
    endButton: DialogButton? = null,
) {
    AlertDialogContent(
        icon,
        title?.let {
            buildAnnotatedString {
                append(it)
            }
        },
        content.let {
            buildAnnotatedString {
                append(it)
            }
        },
        startButton?.text,
        startButton?.onClick,
        endButton?.text,
        endButton?.onClick
    )
}

@Composable
fun RichTextAlertDialog(
    title: String? = null,
    contents: List<RichItem>,
    startButton: DialogButton? = null,
    endButton: DialogButton? = null,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .background(Color(0xFFFFFFFF), RoundedCornerShape(8.dp))
            .padding(horizontal = 16.dp, vertical = 20.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (title != null) {
            Text(text = title, color = Color(0xFF1D2129), fontSize = 16.sp, textAlign = TextAlign.Center)


        }
        if (contents.isNotEmpty()) {
            RichText(
                rich = contents,
                color = CpdColors.FF86909C,
                fontSize = 15.sp,
                lineHeight = 22.sp
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        Row(horizontalArrangement = Arrangement.spacedBy(12.dp)) {
            if (startButton != null) {
                AppButton(
                    text = startButton.text,
                    background = Color(0xFFF1F2F3),
                    color = Color(0xFF86909C),
                    fontSize = 16.sp,
                    modifier = Modifier
                        .widthIn(min = 113.dp, max = 200.dp)
                        .height(36.dp)
                        .weight(1f, false),
                    contentPadding = PaddingValues(horizontal = 6.dp)
                ) {
                    startButton.onClick.invoke()
                }
            }

            if (endButton != null) {
                AppButton(
                    text = endButton.text,
                    background = Color(0xFFFF5E8B),
                    color = Color(0xFFFFFFFF),
                    fontSize = 16.sp,
                    modifier = Modifier
                        .widthIn(min = 113.dp, max = 200.dp)
                        .height(36.dp)
                        .weight(1f, false),
                    contentPadding = PaddingValues(horizontal = 6.dp)
                ) {
                    endButton.onClick.invoke()
                }
            }
        }
    }
}

@Composable
@Preview
private fun AlertDialogContentPreView() {
    AlertDialogContent(icon = null, title = AnnotatedString("preview title"), content = AnnotatedString("preview content"),
        startButtonText = "取消", startButtonClick = { }, endButtonText = "确定", endButtonClick = { })
}

@Composable
@Preview
private fun TitleAlertDialogPreView() {
    TitleAlertDialog(title = "preview title", content = "preview content", endButton = DialogButton("确定", {}))
}

@Composable
@Preview
private fun ContentAlertDialogPreView() {
    ContentAlertDialog(content = "preview content", endButton = DialogButton("确定", {}))
}

@Composable
@Preview
private fun IconAlertDialogPreView() {
    IconAlertDialog(icon = {
        Image(
            painter = painterResource(id = R.drawable.ic_cpd_line_app),
            contentDescription = null,
            modifier = Modifier.size(48.dp)
        )
    }, content = "preview content", title = "preview title", endButton = DialogButton("确定", {}))
}


@Composable
fun AlertDialogContent2(
    icon: (@Composable () -> Unit)?,
    title: AnnotatedString?,
    content: AnnotatedString?,
    topButtonText: String?,
    topButtonClick: OnClick?,
    bottomButtonText: String?,
    bottomButtonClick: OnClick?,
    modifier: Modifier = Modifier,
) {
    check(title != null || content != null)
    Column(
        modifier = Modifier
            .background(Color(0xFFFFFFFF), RoundedCornerShape(8.dp))
            .padding(horizontal = 16.dp, vertical = 20.dp)
            .then(modifier),
        verticalArrangement = Arrangement.spacedBy(12.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (icon != null) {
            icon()
        }

        if (title != null) {
            Text(text = title, color = Color(0xFF1D2129), fontSize = 16.sp, textAlign = TextAlign.Center)
        }

        if (content != null) {
            Text(text = content, color = Color(0xFF86909C), fontSize = 15.sp, textAlign = TextAlign.Center)
        }

        if (topButtonText != null || bottomButtonText != null) {

            Spacer(modifier = Modifier.height(8.dp))

            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                if (topButtonText != null) {
                    AppButton(
                        text = topButtonText,
                        background = Color(0xFFFF5E8B),
                        color = Color(0xFFFFFFFF),
                        fontSize = 16.sp,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(36.dp),
                        contentPadding = PaddingValues(horizontal = 6.dp)
                    ) {
                        topButtonClick?.invoke()
                    }
                }

                if (bottomButtonText != null) {
                    AppButton(
                        text = bottomButtonText,
                        background = Color.Transparent,
                        color = Color(0xFF86909C),
                        fontSize = 16.sp,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(36.dp),
                        contentPadding = PaddingValues(horizontal = 6.dp)
                    ) {
                        bottomButtonClick?.invoke()
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewAlertDialogContent2() {
    AlertDialogContent2(
        icon = null,
        title = AnnotatedString("preview title"),
        content = AnnotatedString("preview content"),
        topButtonText = stringResource(id = R.string.cpd_cancel),
        topButtonClick = { },
        bottomButtonText = stringResource(id = R.string.cpd_confirm),
        bottomButtonClick = { }
    )
}

@Composable
fun TitleAlertDialog2(
    title: String,
    content: String? = null,
    topButton: DialogButton? = null,
    bottomButton: DialogButton? = null,
) {
    AlertDialogContent2(
        null,
        title.let {
            buildAnnotatedString {
                append(it)
            }
        },
        content?.let {
            buildAnnotatedString {
                append(it)
            }
        },
        topButton?.text,
        topButton?.onClick,
        bottomButton?.text,
        bottomButton?.onClick
    )
}

@Composable
fun ContentAlertDialog2(
    content: String,
    title: String? = null,
    topButton: DialogButton? = null,
    bottomButton: DialogButton? = null,
) {
    AlertDialogContent2(
        null,
        title?.let {
            buildAnnotatedString {
                append(it)
            }
        },
        content.let {
            buildAnnotatedString {
                append(it)
            }
        },
        topButton?.text,
        topButton?.onClick,
        bottomButton?.text,
        bottomButton?.onClick
    )
}