package com.qyqy.cupid.ui.dialog

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Parcelable
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ElevatedButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.IUserMenuAction
import com.qyqy.cupid.ui.home.message.C2CChatConst
import com.qyqy.cupid.ui.profile.RechargeDialog
import com.qyqy.cupid.ui.profile.RechargePageContent
import com.qyqy.cupid.widgets.composeClick
import com.qyqy.ucoo.DataTrace
import com.qyqy.ucoo.R
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.utils.OnClick
import kotlinx.parcelize.Parcelize

///////////////////////////////////////////////////////////////////////////
// 更多菜单
///////////////////////////////////////////////////////////////////////////
@Parcelize
data object MoreActionDialog : AnimatedDialog<IUserMenuAction>(), Parcelable {

    @Composable
    override fun Content(dialog: IDialog, onAction: IUserMenuAction?) {
        MoreActionContent({
            dialog.dismiss()
            onAction?.onShowReport()
        }) {
            dialog.dismiss()
            onAction?.onShowBlack()
        }
    }
}

@Composable
private fun MoreActionContent(onReport: OnClick = {}, onBlack: OnClick = {}) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = Color(0xFFFAFAFA),
                shape = RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
            )
            .navigationBarsPadding()
            .padding(horizontal = 24.dp, vertical = 20.dp)
    ) {
        Text(
            text = stringResource(id = R.string.cpd更多),
            modifier = Modifier.align(Alignment.CenterHorizontally),
            color = Color(0xFF1D2129),
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium
        )
        Row(
            modifier = Modifier
                .padding(top = 24.dp)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(22.dp)
        ) {
            Column(modifier = Modifier.noEffectClickable {
                onReport()
            }, horizontalAlignment = Alignment.CenterHorizontally) {
                Image(
                    painter = painterResource(id = R.drawable.ic_user_report),
                    contentDescription = null,
                    modifier = Modifier.size(48.dp)
                )
                Text(
                    text = stringResource(id = R.string.cpd举报),
                    color = Color(0xFF1D2129),
                    fontSize = 12.sp,
                )
            }

            Column(
                modifier = Modifier.noEffectClickable {
                    onBlack()
                },
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_user_black),
                    contentDescription = null,
                    modifier = Modifier.size(48.dp)
                )
                Text(
                    text = stringResource(id = R.string.cpd拉黑),
                    color = Color(0xFF1D2129),
                    fontSize = 12.sp,
                )
            }
        }

        Spacer(modifier = Modifier.height(20.dp))
    }
}

///////////////////////////////////////////////////////////////////////////
// 更多菜单
///////////////////////////////////////////////////////////////////////////


///////////////////////////////////////////////////////////////////////////
// 拉黑确认
///////////////////////////////////////////////////////////////////////////
@Parcelize
data object BlackAlertDialog : NormalDialog<IUserMenuAction>(), Parcelable {

    @Composable
    override fun Content(dialog: IDialog, onAction: IUserMenuAction?) {
        BlackAlertContent({
            dialog.dismiss()
        }) {
            onAction?.onBlack(dialog) ?: dialog.dismiss()
        }
    }
}

@Composable
private fun BlackAlertContent(onClose: OnClick = {}, onConfirm: OnClick = {}) {
    ContentAlertDialog(
        content = stringResource(id = R.string.cpd拉黑后将不会收到对方的消息哦),
        startButton = DialogButton(stringResource(id = R.string.cpd取消)) {
            onClose()
        },
        endButton = DialogButton(stringResource(id = R.string.cpd确定)) {
            onConfirm()
        }
    )
}
///////////////////////////////////////////////////////////////////////////
// 拉黑确认
///////////////////////////////////////////////////////////////////////////


///////////////////////////////////////////////////////////////////////////
// 通用二次确认弹窗
///////////////////////////////////////////////////////////////////////////

data class CommonAlertDialog(
    val content: String,
    val onConfirm: OnClick,
) : SimpleDialog() {

    @Composable
    override fun Content(dialog: IDialog) {
        ContentAlertDialog(
            content = content,
            startButton = DialogButton(stringResource(id = R.string.cpd取消)) {
                dialog.dismiss()
            },
            endButton = DialogButton(stringResource(id = R.string.cpd确定)) {
                dialog.dismiss()
                onConfirm()
            }
        )
    }
}

///////////////////////////////////////////////////////////////////////////
// 通用二次确认弹窗
///////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////
// 发起通话亲密度不足
///////////////////////////////////////////////////////////////////////////
@Parcelize
data class InsufficientIntimacyToStartCall(private val userId: String, private val content: String) : SimpleDialog(), Parcelable {

    @Composable
    override fun Content(dialog: IDialog) {
        ContentAlertDialog2(
            content = content,
            topButton = DialogButton(stringResource(id = R.string.赠送礼物)) {
                AppLinkManager.controller?.navigateByLink("ucoo://page/c2c?userId=$userId&action=${C2CChatConst.ACTION_SHOW_GIFT}")
                dialog.dismiss()
            },
            bottomButton = DialogButton(stringResource(id = R.string.继续聊天)) {
                dialog.dismiss()
            },
        )
    }
}
///////////////////////////////////////////////////////////////////////////
// 发起通话亲密度不足
///////////////////////////////////////////////////////////////////////////


///////////////////////////////////////////////////////////////////////////
// 发起通话余额不足
///////////////////////////////////////////////////////////////////////////
@Parcelize
data class BalanceNotEnoughToStartCall(private val content: String) : SimpleDialog(), Parcelable {

    @Composable
    override fun Content(dialog: IDialog) {
        val dialogQueue = LocalDialogQueue.current
        ContentAlertDialog2(
            content = content,
            topButton = DialogButton(stringResource(id = R.string.cpd立即充值)) {
                dialogQueue.push(RechargeDialog, true)
                dialog.dismiss()
            },
            bottomButton = DialogButton(stringResource(id = R.string.cpd以后再说)) {
                dialog.dismiss()
            },
        )
    }
}
///////////////////////////////////////////////////////////////////////////
// 发起通话余额不足
///////////////////////////////////////////////////////////////////////////


///////////////////////////////////////////////////////////////////////////
// 发起通话对方余额不足
///////////////////////////////////////////////////////////////////////////
@Parcelize
data class TargetBalanceNotEnoughToStartCall(private val content: String) : SimpleDialog(), Parcelable {

    @Composable
    override fun Content(dialog: IDialog) {
        ContentAlertDialog(
            content = content,
            endButton = DialogButton(stringResource(id = R.string.cpd我知道了)) {
                dialog.dismiss()
            },
        )
    }
}
///////////////////////////////////////////////////////////////////////////
// 发起通话对方余额不足
///////////////////////////////////////////////////////////////////////////

@Composable
fun LineNumCopyDialogContent(
    title: String = "公式コミュニティーに参加するメリット",
    content: String = "1.デイリータスクのダイヤ報酬が2倍になります\n" +
            "2.公式限定メダル、アバターフレーム、着せ替え特典を獲得できます。\n" +
            "3.UCOOで、より多くのダイヤを獲得し、より効率的に現金を稼ぐ方法を、公式運営から指導と支援を受けることができます。\n" +
            "4.アプリの使用の問題やフィードバックは、優先に答えられます。",
    hintTitle: String = "LINE IDをコピーして\n公式lineを追加してください",
    lineId: String = "haruka07091102",
    onClose: OnClick = {},
) {
    Column(modifier = Modifier.fillMaxWidth()) {

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 40.dp, bottom = 20.dp)
                .background(Color(0xFFFEF5FD), Shapes.corner12)
                .padding(horizontal = 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            Image(
                painter = painterResource(id = R.drawable.ic_cpd_line_chat),
                contentDescription = null,
                modifier = Modifier
                    .offset(y = (-40).dp)
                    .size(112.dp, 80.dp),
            )

            Text(
                text = title,
                color = Color(0xFF1D2129),
                modifier = Modifier.padding(horizontal = 10.dp),
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(10.dp))

            Text(
                text = content,
                color = Color(0xFF4E5969),
                fontSize = 12.sp,
            )

            Spacer(modifier = Modifier.height(10.dp))

            Text(
                text = hintTitle,
                color = Color(0xFF1D2129),
                modifier = Modifier.padding(horizontal = 10.dp),
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(10.dp))

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp)
                    .background(Color(0xFFFFE7ED), Shapes.corner12)
                    .padding(horizontal = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_cpd_line_app),
                    contentDescription = null,
                    modifier = Modifier.size(32.dp),
                )

                Text(
                    text = "LINE ID:\n$lineId",
                    modifier = Modifier
                        .padding(horizontal = 4.dp)
                        .weight(1f),
                    lineHeight = 16.sp
                )

                val context = LocalContext.current

                AppButton(
                    text = stringResource(id = R.string.cpd复制),
                    modifier = Modifier
                        .size(72.dp, 28.dp)
                        .fillMaxWidth(),
                    background = Color(0xFFFF5E8B),
                    color = Color(0xFFFFFFFF),
                ) {
                    val clipboard = context.applicationContext.getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager
                    val clip = ClipData.newPlainText("lineId", lineId)
                    clipboard?.setPrimaryClip(clip)
                    toastRes(R.string.cpd已复制)
                }
            }

            Spacer(modifier = Modifier.height(20.dp))

        }

        Image(
            painter = painterResource(id = R.drawable.ic_close_circle),
            contentDescription = null,
            modifier = Modifier
                .align(Alignment.CenterHorizontally)
                .size(30.dp)
                .noEffectClickable(onClick = onClose),
            colorFilter = ColorFilter.tint(Color.White)
        )
    }
}

@Preview
@Composable
private fun PreviewLineNumCopyDialogContent() {
    Box(
        modifier = Modifier
            .background(Color.Black.copy(0.5f))
            .padding(30.dp)
    ) {
        LineNumCopyDialogContent()
    }
}


@Composable
fun TaskRewardDialogContent(
    rewardStr: String,
    rewardTips: String,
    rewardIcon: Any = R.drawable.ic_cpd_task_diamond,
    button: DialogButton,
) {
    Column(
        modifier = Modifier
            .background(
                brush = Brush.verticalGradient(
                    listOf(
                        Color(0xFFFF90AF),
                        Color(0xFFFF5E8B)
                    )
                ),
                shape = RoundedCornerShape(12.dp)
            )
            .widthIn(max = 270.dp)
            .padding(vertical = 20.dp, horizontal = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            rewardTips, textAlign = TextAlign.Center,
            style = MaterialTheme.typography.titleSmall.copy(color = Color.White, fontSize = 12.sp)
        )
        Spacer(modifier = Modifier.height(10.dp))
        ComposeImage(model = rewardIcon, modifier = Modifier.size(96.dp))
        Spacer(modifier = Modifier.height(8.dp))
        Text(rewardStr, style = MaterialTheme.typography.titleMedium.copy(color = colorResource(id = R.color.FFFFE666)))
        Spacer(modifier = Modifier.height(10.dp))
        ElevatedButton(
            onClick = composeClick {
                button.onClick()
            }, modifier = Modifier
                .width(206.dp)
                .height(36.dp), contentPadding = PaddingValues(horizontal = 8.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = Color.White,
                contentColor = colorResource(id = R.color.FFFF5E8B)
            ),
            elevation = null
        ) {
            Text(button.text, style = MaterialTheme.typography.titleSmall)
        }
    }
}

@Composable
@Preview
fun PreviewTaskRewardDialogContent() {
    TaskRewardDialogContent(
        "50金币",
        "完成\"填写所有个人基础信息\"任务,获得金币奖励完成\"填写所有个人基础信息\"任务,获得金币奖励完成\"填写所有个人基础信息\"任务,获得金币奖励",
        R.drawable.ic_cpd_task_diamond,
        DialogButton("立即兑换现金") {

        }
    )
}