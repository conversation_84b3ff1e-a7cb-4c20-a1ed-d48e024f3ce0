package com.qyqy.cupid.ui.dialog

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.data.CoupleInfo
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.widgets.AlignHorizontalContainer
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.utils.OnClick


@Composable
fun JoinToCoupleRoom(user: User, onClick: OnClick = {}) {
    Box(modifier = Modifier.fillMaxWidth()) {
        Column(
            modifier = Modifier
                .align(Alignment.TopCenter)
                .padding(top = 20.dp)
                .width(270.dp)
                .paint(painterResource(id = R.drawable.bg_joined_private_room))
                .padding(start = 16.dp, top = 82.dp, end = 16.dp, bottom = 22.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = stringResource(id = R.string.cpd进入了情侣小屋, user.nickname),
                fontSize = 15.sp,
                color = Color(0xFFB40020),
                textAlign = TextAlign.Center,
                lineHeight = 22.sp
            )

            Column(
                modifier = Modifier
                    .padding(top = 20.dp)
                    .widthIn(min = 148.dp)
                    .height(36.dp)
                    .background(
                        brush = Brush.horizontalGradient(listOf(Color(0xFFFF8ECB), Color(0xFFFF4794))),
                        shape = CircleShape
                    )
                    .noEffectClickable(onClick = onClick)
                    .padding(horizontal = 12.dp),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(text = stringResource(id = R.string.cpd立即进入), color = Color(0xFFFFF7FB), fontSize = 16.sp)
            }
        }


        Box(modifier = Modifier.align(Alignment.TopCenter)) {
            CircleComposeImage(
                model = user.avatarUrl,
                modifier = Modifier.size(80.dp),
                borderStroke = BorderStroke(2.dp, Color.White)
            )
        }

    }
}

@Preview
@Composable
private fun PreviewJoinToCoupleRoom() {
    JoinToCoupleRoom(userForPreview)
}


@Composable
fun CouplePendantFloat(coupleInfo: CoupleInfo, modifier: Modifier = Modifier, onClick: OnClick = {}) {
    AlignHorizontalContainer(visible = coupleInfo.visible, modifier = modifier, tag = "CouplePendantFloat") {
        Column(
            modifier = Modifier
                .size(80.dp, 90.dp)
                .paint(painterResource(id = R.drawable.bg_joined_private_room_float))
                .noEffectClickable(onClick = onClick),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(modifier = Modifier.padding(top = 10.dp)) {
                Row(horizontalArrangement = Arrangement.spacedBy((-8).dp)) {
                    CircleComposeImage(
                        model = coupleInfo.selfUserInfo.avatarUrl,
                        modifier = Modifier.size(36.dp),
                        borderStroke = BorderStroke(1.dp, Color.White)
                    )

                    CircleComposeImage(
                        model = coupleInfo.targetUserInfo.avatarUrl,
                        modifier = Modifier.size(36.dp),
                        borderStroke = BorderStroke(1.dp, Color.White)
                    )
                }

                Image(
                    painter = painterResource(id = R.drawable.ic_cpd_cp_heart),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .size(24.dp)
                )
            }

            Spacer(modifier = Modifier.weight(1f))

            if (coupleInfo.visible) {
                AutoSizeText(
                    text = if (coupleInfo.isCp) {
                        stringResource(id = R.string.cpd情侣小屋)
                    } else {
                        stringResource(id = R.string.cpd和TA成为情侣)
                    },
                    modifier = Modifier
                        .padding(horizontal = 4.dp)
                        .heightIn(max = 28.dp),
                    fontSize = 14.sp,
                    color = Color(0xFFB40020),
                    maxLines = 2,
                    alignment = Alignment.Center
                )
            }

            Spacer(modifier = Modifier.weight(2f))
        }
    }
}

@Preview
@Composable
private fun Preview1() {
    PreviewCupidTheme {
        CouplePendantFloat(CoupleInfo(isCp = true))
    }
}