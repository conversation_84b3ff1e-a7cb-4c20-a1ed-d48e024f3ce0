package com.qyqy.cupid.ui.dialog

import androidx.compose.animation.core.AnimationConstants.DefaultDurationMillis
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.Stable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.LocalSaveableStateRegistry
import androidx.compose.runtime.saveable.SaveableStateHolder
import androidx.compose.runtime.saveable.SaveableStateRegistry
import androidx.compose.runtime.saveable.listSaver
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.saveable.rememberSaveableStateHolder
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.ui.window.SecureFlagPolicy
import androidx.core.util.Predicate
import androidx.navigation.compose.currentBackStackEntryAsState
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.ucoo.compose.isEditOnCompose
import com.qyqy.ucoo.compose.ui.AnimatedDialog
import com.qyqy.ucoo.compose.ui.AnyPopDialogProperties
import com.qyqy.ucoo.compose.ui.DirectionState
import com.qyqy.ucoo.compose.ui.FullDialog
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.flow.collectLatest
import kotlinx.parcelize.RawValue
import java.util.UUID


fun interface IDialog {

    fun dismiss()

}

interface IDialogAction

sealed interface IDialogContent<in T : IDialogAction> : IDialog {

    val restoreState: Boolean
        get() = false

    @Composable
    fun Content(
        saveableStateHolder: SaveableStateHolder,
        key: Any,
        onDismiss: OnClick,
        onAction: T?
    )

}


abstract class NormalDialog<T : IDialogAction> : IDialogContent<T> {

    open val isFull: Boolean
        get() = false

    open val properties: DialogProperties
        get() = DialogProperties()

    @Composable
    abstract fun Content(dialog: IDialog, onAction: T?)

    private var isActiveClose by mutableStateOf(false)

    override fun dismiss() {
        isActiveClose = true
    }

    /**
     * test方法 dismiss回调
     * 因为在LaunchedEffect 会导致多调用一次, 有时间谁来调一下吧
     *
     * @param isInitiative true主动调用dismiss关闭,false为点击区域外或back按键
     */
    protected open fun onDismiss(isInitiative: Boolean) = Unit

    @Composable
    final override fun Content(
        saveableStateHolder: SaveableStateHolder,
        key: Any,
        onDismiss: OnClick,
        onAction: T?
    ) {
        if (isActiveClose) {
            LaunchedEffect(Unit) {
                onDismiss(isActiveClose)
                isActiveClose = false
                onDismiss()
            }
        }
        if (isFull) {
            FullDialog(
                onDismissRequest = {
                    onDismiss(isActiveClose)
                    onDismiss()
                },
                properties = properties,
            ) {
                saveableStateHolder.SaveableStateProvider(key = key) {
                    Content(this, onAction)
                }
            }
        } else {
            Dialog(
                onDismissRequest = {
                    onDismiss(isActiveClose)
                    onDismiss()
                },
                properties = properties,
            ) {
                saveableStateHolder.SaveableStateProvider(key = key) {
                    Content(this, onAction)
                }
            }
        }
    }
}


abstract class SimpleDialog : NormalDialog<IDialogAction>() {

    @Composable
    abstract fun Content(dialog: IDialog)

    @Composable
    final override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        Content(dialog)
    }
}


abstract class AnimatedDialog<T : IDialogAction> : IDialogContent<T> {

    open val properties: AnyPopDialogProperties
        get() = AnyPopDialogProperties(direction = DirectionState.BOTTOM)


    @Composable
    abstract fun Content(dialog: IDialog, onAction: T?)

    protected var isActiveClose by mutableStateOf(false)

    override fun dismiss() {
        isActiveClose = true
    }

    /**
     * dismiss回调
     *
     * @param isInitiative true主动调用dismiss关闭,false为点击区域外或back按键
     */
    protected open fun onDismiss(isInitiative: Boolean) = Unit

    @Composable
    final override fun Content(
        saveableStateHolder: SaveableStateHolder,
        key: Any,
        onDismiss: OnClick,
        onAction: T?
    ) {
        AnimatedDialog(
            isActiveClose = isActiveClose,
            onDismiss = {
                onDismiss(isActiveClose)
                isActiveClose = false
                onDismiss()
            },
            properties = properties,
        ) {
            saveableStateHolder.SaveableStateProvider(key = key) {
                Content(this, onAction)
            }
        }
    }
}

abstract class SimpleAnimatedDialog : AnimatedDialog<IDialogAction>() {

    @Composable
    abstract fun Content(dialog: IDialog)

    @Composable
    final override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        Content(dialog)
    }
}

data class QueueItem<T : IDialogAction>(
    val dialog: IDialogContent<T>,
    val showing: Boolean = false,
    val key: String = UUID.randomUUID().toString(),
)

@Composable
fun rememberSimpleDialogQueue(
    initialEntries: List<QueueItem<IDialogAction>> = emptyList(),
    autoEnable: Boolean = true,
) = rememberDialogQueue(initialEntries, autoEnable)

@Composable
fun <T : IDialogAction> rememberDialogQueue(
    initialEntries: List<QueueItem<T>> = emptyList(),
    autoEnable: Boolean = true,
): DialogQueue<T> = run {
    val registry = LocalSaveableStateRegistry.current
    rememberSaveable(saver = listSaver(save = { queue ->
        DialogQueue.run {
            queue.save(registry)
        }
    }, restore = {
        DialogQueue.restore(it)
    })) {
        DialogQueue(initialEntries)
    }.also { queue ->
        if (autoEnable && !isEditOnCompose) {
            val navController = LocalAppNavController.current.composeNav
            val id = rememberSaveable {
                navController.currentDestination?.id ?: -1
            }
            if (id != -1) {
                val currentEntry by navController.currentBackStackEntryAsState()
                LaunchedEffect(key1 = Unit) {
                    snapshotFlow {
                        currentEntry?.destination?.id == id
                    }.collectLatest {
                        queue.enable = it
                    }
                }
            }
        }
    }
}

@Stable
class DialogQueue<T : IDialogAction> internal constructor(initialEntries: List<QueueItem<T>> = emptyList()) {

    companion object {
        fun <T : IDialogAction> DialogQueue<T>.save(registry: SaveableStateRegistry?): List<Any> {
            return buildList {
                dialogStack.forEach {
                    if (it.dialog.restoreState && registry?.canBeSaved(it.dialog) == true) {
                        add(it.dialog)
                        add(it.showing)
                        add(it.key)
                    }
                }
            }
        }

        fun <T : IDialogAction> restore(list: List<Any>): DialogQueue<T> {
            if (list.isEmpty() || list.size.rem(3) != 0) {
                return DialogQueue(emptyList())
            }
            return DialogQueue(List(list.size.div(3)) { index ->
                val i = index.times(3)
                QueueItem(
                    dialog = list[i] as IDialogContent<T>,
                    showing = list[i + 1] as Boolean,
                    key = list[i + 2] as String,
                )
            })
        }
    }

    private var dialogAction: MutableState<T?> = mutableStateOf(null)

    private val dialogStack = mutableStateListOf<QueueItem<T>>().apply {
        addAll(initialEntries)
    }

    private val showingDialogList by derivedStateOf {
        buildList {
            for (item in dialogStack) {
                if (item.showing) {
                    add(item)
                }
            }

            if (isEmpty()) {
                dialogStack.takeIf {
                    it.isNotEmpty()
                }?.also {
                    val item = it.last()
                    it[it.lastIndex] = item.copy(showing = true)
                    add(item)
                }
            }
        }
    }

    var enable by mutableStateOf(true)

    @Composable
    fun DialogContent(onAction: T? = null) {
        val saveableStateHolder = rememberSaveableStateHolder()
        if (!enable) {
            return
        }
        dialogAction.value = onAction
        CompositionLocalProvider(LocalDialogQueue provides this) {
            for (item in showingDialogList) {
                val dialog = item.dialog
                val key = item.key
                key(key) {
                    dialog.Content(
                        saveableStateHolder = saveableStateHolder,
                        key = key,
                        onDismiss = {
                            saveableStateHolder.removeState(key)
                            internalPopDialog(key)
                        },
                        onAction = dialogAction.value
                    )
                }
            }
        }
    }

    fun push(dialog: IDialogContent<T>, immediatelyShow: Boolean = false) {
        dialogStack.add(QueueItem(dialog, immediatelyShow || showingDialogList.isEmpty()))
    }

    fun pushWithKey(
        dialog: IDialogContent<T>,
        key: String = UUID.randomUUID().toString(),
        immediatelyShow: Boolean = false
    ) {
        dialogStack.add(
            QueueItem(
                dialog,
                immediatelyShow || showingDialogList.isEmpty(),
                key = key
            )
        )
    }

    fun push(
        immediatelyShow: Boolean = false,
        dismissOnBackPress: Boolean = true,
        dismissOnClickOutside: Boolean = true,
        isAppearanceLightNavigationBars: Boolean = true,
        direction: DirectionState = DirectionState.BOTTOM,
        backgroundDimEnabled: Boolean = true,
        statusBarColor: @RawValue Color = Color.Transparent,
        navBarColor: @RawValue Color = Color.Transparent,
        durationMillis: Int = DefaultDurationMillis,
        securePolicy: SecureFlagPolicy = SecureFlagPolicy.Inherit,
        content: @Composable (dialog: IDialog, onAction: IDialogAction?) -> Unit,
    ) {
        push(object : AnimatedDialog<IDialogAction>() {
            override val properties: AnyPopDialogProperties = AnyPopDialogProperties(
                dismissOnBackPress = dismissOnBackPress,
                dismissOnClickOutside = dismissOnClickOutside,
                isAppearanceLightNavigationBars = isAppearanceLightNavigationBars,
                direction = direction,
                backgroundDimEnabled = backgroundDimEnabled,
                statusBarColor = statusBarColor,
                navBarColor = navBarColor,
                durationMillis = durationMillis,
                securePolicy = securePolicy,
            )

            @Composable
            override fun Content(dialog: IDialog, onAction: IDialogAction?) {
                content(dialog, onAction)
            }
        }, immediatelyShow)
    }

    fun pushCenterDialog(
        immediatelyShow: Boolean = false,
        isFull: Boolean = false,
        properties: DialogProperties = DialogProperties(),
        content: @Composable (dialog: IDialog, onAction: IDialogAction?) -> Unit,
    ) {
        push(object : NormalDialog<IDialogAction>() {

            override val isFull: Boolean = isFull

            override val properties: DialogProperties = properties

            @Composable
            override fun Content(dialog: IDialog, onAction: IDialogAction?) {
                content(dialog, onAction)
            }
        }, immediatelyShow)
    }

    fun pop() {
        if (showingDialogList.isNotEmpty()) {
            showingDialogList.last().dialog.dismiss()
        }
    }

    private fun internalPopDialog(key: String) {
        dialogStack.find { it.key == key }?.also {
            // 是pop出去的时候dismiss一下
            dialogStack.remove(it)
        }
        if (showingDialogList.isEmpty() && dialogStack.isNotEmpty()) {
            dialogStack[dialogStack.lastIndex] =
                dialogStack[dialogStack.lastIndex].copy(showing = true)
        }
    }

    fun clear() {
        dialogStack.clear()
    }

    fun dismiss(predicate: Predicate<QueueItem<*>>) {
        val listToRemove = dialogStack.filter { predicate.test(it) }
        dialogStack.removeAll(listToRemove)
    }
}


val LocalDialogQueue = staticCompositionLocalOf<DialogQueue<*>> {
    error("LocalDialogQueue is null")
}