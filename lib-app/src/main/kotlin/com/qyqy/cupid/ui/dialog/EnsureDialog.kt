package com.qyqy.cupid.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.utils.OnClick

data class DialogUIStyle(
    val bgColor: Color,
    val titleColor: Color,
    val contentColor: Color,
    val accentColor: Color,
    val strokeColor: Color
) {
    companion object {
        val cupid = DialogUIStyle(Color.White, Color(0xFF1D2129), Color(0xFF86909C), Color(0xFFFF5E8B), Color(0xFFC9CDD4))
        val ucoo = DialogUIStyle(Color(0xFF222222), Color.White, Color(0x80FFFFFF), Color(0xFF945EFF), Color(0x80FFFFFF))
    }
}

@Composable
fun EnsureContent(
    title: String,
    content: String,
    leftButtonText: String,
    rightButtonText: String,
    onLeftClick: OnClick,
    onRightClick: OnClick,
    style: DialogUIStyle = DialogUIStyle.cupid
) {
    Column(
        Modifier
            .width(270.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(style.bgColor)
            .padding(16.dp, 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(text = title, color = style.titleColor, fontSize = 17.sp, fontWeight = FontWeight.Bold)
        Spacer(modifier = Modifier.height(4.dp))
        Text(text = content, color = style.contentColor, fontSize = 15.sp)
        Spacer(modifier = Modifier.height(20.dp))
        Row(modifier = Modifier.fillMaxWidth()) {
            Text(
                text = leftButtonText, fontSize = 16.sp, lineHeight = 36.sp, modifier = Modifier
                    .weight(1f)
                    .clip(Shapes.chip)
                    .border(1.dp, style.strokeColor, Shapes.chip)
                    .click(onClick = onLeftClick),
                color = style.contentColor,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.width(12.dp))
            Text(
                text = rightButtonText, fontSize = 16.sp, lineHeight = 36.sp, modifier = Modifier
                    .weight(1f)
                    .clip(Shapes.chip)
                    .click(onClick = onRightClick)
                    .background(style.accentColor),
                color = Color.White,
                textAlign = TextAlign.Center
            )
        }

    }

}

@Preview
@Composable
private fun Preview() {
    Column(
        modifier = Modifier
            .background(Color.Gray)
            .fillMaxWidth()
            .padding(vertical = 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        EnsureContent(
            title = "注销账号",
            content = "注销账号后我们将删除您所有相关的数据，为了防止您意外删除账户，我们提供了30天的反悔期，30天内如果重新登录原账号，我们将保留您账号的相关数据。如果30天内未登录账号，则届时账号将被自动删除",
            leftButtonText = "确认注销",
            rightButtonText = "取消",
            onLeftClick = { },
            onRightClick = { })

        Spacer(modifier = Modifier.height(20.dp))

        EnsureContent(
            title = "注销账号",
            content = "注销账号后我们将删除您所有相关的数据，为了防止您意外删除账户，我们提供了30天的反悔期，30天内如果重新登录原账号，我们将保留您账号的相关数据。如果30天内未登录账号，则届时账号将被自动删除",
            leftButtonText = "确认注销",
            rightButtonText = "取消",
            onLeftClick = { },
            onRightClick = { }, style = DialogUIStyle.ucoo
        )
    }
}