package com.qyqy.cupid.ui.dialog

import androidx.compose.foundation.layout.widthIn
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.AnyPopDialogProperties
import com.qyqy.ucoo.compose.ui.DirectionState

/**
 *  @time 8/27/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.dialog
 */
/**
 * 家族不可用时的弹窗
 * @property reasonInt 1 自己退出 2 被踢了 3家族解散了
 */
data class FamilyBanDialog(val reasonInt: Int) : SimpleAnimatedDialog() {
    override val properties: AnyPopDialogProperties = AnyPopDialogProperties(false, false, direction = DirectionState.CENTER)

    @Composable
    override fun Content(dialog: IDialog) {
        FamilyBanContent(reasonInt = reasonInt, { dialog.dismiss() })
    }
}

@Composable
fun FamilyBanContent(reasonInt: Int, onDismiss: () -> Unit) {
    val controller = LocalAppNavController.current
    TitleAlertDialog(
        title = stringResource(
            when (reasonInt) {
                2 -> R.string.cpd_family_has_kicked
                3 -> R.string.cpd_family_has_disband
                4 -> R.string.cpd_family_has_disband_owner
                else -> R.string.cpd_family_has_exit
            }
        ),
        endButton = DialogButton(stringResource(id = R.string.cpd_iknow)) {
            onDismiss()
            //1.先看栈里有没有家族主页
            var hasFamilyPage = controller.contains(CupidRouters.FAMILY_HOME)
            if (hasFamilyPage) {
                controller.popBackStack(CupidRouters.FAMILY_HOME, true)
                return@DialogButton
            }
            //2.再看有没有详情页
            hasFamilyPage = controller.contains(CupidRouters.FAMILY_DETAIL)
            if (hasFamilyPage) {
                controller.popBackStack(CupidRouters.FAMILY_DETAIL, true)
            }
        }, modifier = Modifier.widthIn(270.dp)
    )
}