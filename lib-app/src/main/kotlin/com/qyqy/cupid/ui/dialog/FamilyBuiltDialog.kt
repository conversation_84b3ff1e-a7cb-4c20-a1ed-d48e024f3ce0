package com.qyqy.cupid.ui.dialog

import android.os.Parcelable
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.cupid.model.FamilyViewModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.ui.home.mine.edit.EditorTextField
import com.qyqy.cupid.utils.CupidFamilyManager
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.AnyPopDialogProperties
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.DirectionState
import com.qyqy.ucoo.tribe.bean.TribeInfo
import com.qyqy.ucoo.tribe.bean.tribeId
import kotlinx.parcelize.Parcelize

/**
 *  @time 8/26/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.dialog
 */
@Parcelize
data object FamilyBuiltDialog : SimpleAnimatedDialog(), Parcelable {
    
    override val properties: AnyPopDialogProperties
        get() = AnyPopDialogProperties(direction = DirectionState.CENTER)

    @Composable
    override fun Content(dialog: IDialog) {
        val viewModel = viewModel(FamilyViewModel::class.java, factory = viewModelFactory {
            initializer {
                FamilyViewModel(CupidFamilyManager.myTribe?.tribeId ?: "0")
            }
        })
        val tribeInfo = viewModel.familyFlow.collectAsStateWithLifecycle()
        FamilyBuiltContent(tribeInfo.value, {
            viewModel.updateFamilyInfo(familyIntro = it) {
                dialog.dismiss()
            }
        }) {
            dialog.dismiss()
        }
    }

}

@Composable
private fun FamilyBuiltContent(tribeInfo: TribeInfo?, onSaved: (newBuiltStr: String) -> Unit, onDismiss: () -> Unit) {

    var displayText by remember {
        mutableStateOf(tribeInfo?.bulletin ?: "")
    }
    Column(
        modifier = Modifier
            .background(Color.White, RoundedCornerShape(12.dp))
            .width(270.dp)
            .padding(horizontal = 16.dp)
            .padding(bottom = 10.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(44.dp)
        ) {
            Text(
                text = stringResource(id = R.string.cpd_family_built),
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.align(Alignment.Center)
            )
        }
        EditorTextField(
            displayText,
            {
                displayText = it.take(500)
            },
            hint = stringResource(id = R.string.cpd_family_built_hint),
            maxLength = 500,
            modifier = Modifier
                .padding(top = 20.dp, bottom = 20.dp)
                .heightIn(200.dp, 270.dp)
        )
        Row {
            AppButton(
                text = stringResource(id = R.string.cpd_cancel),
                background = CpdColors.FFF1F2F3,
                color = CpdColors.FF86909C,
                fontSize = 16.sp,
                modifier = Modifier
                    .weight(1f)
                    .height(36.dp)

            ) {
                onDismiss()
            }
            Spacer(modifier = Modifier.width(12.dp))
            AppButton(
                text = stringResource(id = R.string.cpd_save),
                background = CpdColors.FFFF5E8B,
                color = Color.White,
                fontSize = 16.sp,
                modifier = Modifier
                    .weight(1f)
                    .height(36.dp)
            ) {
                onSaved(displayText)
            }

        }
    }
}

@Composable
@Preview
private fun FamilyBuiltPreview() {
    FamilyBuiltContent(null, {}, {})
}