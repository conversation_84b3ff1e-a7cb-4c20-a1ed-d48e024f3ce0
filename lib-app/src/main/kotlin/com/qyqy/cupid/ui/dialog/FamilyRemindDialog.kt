

package com.qyqy.cupid.ui.dialog

import android.text.Html
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.qyqy.cupid.data.FamilyEncourageInfo
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.NavListener
import com.qyqy.cupid.ui.relations.family.FamilySquareAction
import com.qyqy.cupid.ui.relations.family.familyCreator
import com.qyqy.cupid.utils.CupidFamilyManager
import com.qyqy.cupid.utils.eventbus.LocalEventBus
import com.qyqy.cupid.widgets.composeClick
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.AnyPopDialogProperties
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.DirectionState
import com.qyqy.ucoo.compose.ui.toAnnotatedString

/**
 *  @time 9/9/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.dialog
 */
object FamilyRemindDialog : SimpleAnimatedDialog() {
    override val properties: AnyPopDialogProperties = AnyPopDialogProperties(
        direction = DirectionState.CENTER
    )

    @Composable
    override fun Content(dialog: IDialog) {
        val controller = LocalAppNavController.current
        val info by CupidFamilyManager.familyEncourageInfo.collectAsStateWithLifecycle()
        if (info.bonusType == 0) {
            //无奖励
            dialog.dismiss()
            return
        }
        FamilyRemindDialogContent(dialog, info, {
            controller.navigateByLink(it)
        })
    }
}


@Composable
private fun FamilyRemindDialogContent(dialog: IDialog, info: FamilyEncourageInfo, onAction: (String) -> Unit) {

    Column(
        modifier = Modifier
            .fillMaxWidth(0.72f)
            .paint(
                painter = painterResource(
                    id =
                    if (info.bonusType == 1)
                        R.drawable.ic_cpd_family_coin_bg
                    else R.drawable.ic_cpd_family_diamond_bg
                ),
                contentScale = ContentScale.FillBounds
            )
            .padding(horizontal = 16.dp, vertical = 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        ComposeImage(
            model = if (info.bonusType == 1) R.drawable.ic_cpd_coin else R.drawable.ic_cpd_diamond,
            modifier = Modifier.size(64.dp)
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            "+${info.bonusAmount}${
                stringResource(id = if (info.bonusType == 1) R.string.cpd_coin else R.string.cpd_diamond)
            }", fontSize = 20.sp, color = CpdColors.FF3D241F
        )


        //没看懂这个digest字段干嘛的
//        info.digest.forEach {
//            Text(
//                it.richText,
//                fontSize = 12.sp, color = CpdColors.FF86909C
//            )
//        }

        Spacer(modifier = Modifier.height(12.dp))

        info.popupMsg.forEach {
            Text(
                Html.fromHtml(it.richText, Html.FROM_HTML_MODE_LEGACY).toAnnotatedString(),
                fontSize = 12.sp, color = CpdColors.FF86909C,
                textAlign = TextAlign.Center
            )
        }
        val eventBus = LocalEventBus.current
        info.actions.forEach {
            Spacer(modifier = Modifier.height(20.dp))
            AppButton(
                text = it.actionText,
                onClick = composeClick {
                    if (it.actionType == 1) {
                        eventBus.addStickyEvent(NavListener.NavKey, "${NavListener.HOME_SUB_NAV}showFamilyCreator", true)
                    }
                    dialog.dismiss()
                },
                border = BorderStroke(1.dp, color = CpdColors.FFFF5E8B),
                background = CpdColors.FFFF5E8B,
                textStyle = TextStyle(color = Color.White, fontSize = 14.sp),
                contentPadding = PaddingValues(horizontal = 12.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .height(32.dp)
            )
        }


        //创建家族
//        Spacer(modifier = Modifier.height(20.dp))
//        AppButton(
//            text = stringResource(id = R.string.cupid_create), onClick = composeClick { dialog.dismiss() },
//            border = BorderStroke(1.dp, color = CpdColors.FFFF5E8B),
//            background = CpdColors.FFFF5E8B,
//            textStyle = TextStyle(color = Color.White, fontSize = 14.sp),
//            contentPadding = PaddingValues(horizontal = 12.dp),
//            modifier = Modifier
//                .fillMaxWidth()
//                .padding(horizontal = 16.dp)
//                .height(32.dp)
//        )

        //加入家族
//        Spacer(modifier = Modifier.height(20.dp))
//
//        AppButton(
//            text = stringResource(id = R.string.cpd_join_family), onClick = composeClick { dialog.dismiss() },
//            border = BorderStroke(1.dp, color = CpdColors.FFFF5E8B),
//            background = CpdColors.FFFF5E8B,
//            textStyle = TextStyle(color = Color.White, fontSize = 14.sp),
//            contentPadding = PaddingValues(horizontal = 12.dp),
//            modifier = Modifier
//                .fillMaxWidth()
//                .padding(horizontal = 16.dp)
//                .height(32.dp)
//        )
    }
}

@Composable
@Preview(showBackground = true, widthDp = 375)
private fun FamilyRemindDialogPreview() {
    FamilyRemindDialogContent(
        object : IDialog {
            override fun dismiss() {

            }
        }, FamilyEncourageInfo(
            bonusType = 1,
            bonusAmount = 100,
            digest = listOf(
                FamilyEncourageInfo.Digest(
                    "",
                    "<font color='#86909C'>加入家族可获得1000金币奖励</font>",
                    1,
                )
            ),
            popupMsg = listOf(
                FamilyEncourageInfo.PopupMsg(
                    richText = "<font color='#86909C'>成功加入某个家族后，可获得1000金币奖励<br />快加入一个你喜欢的家族吧！</font>",
                )
            ),
            actions = listOf(
                FamilyEncourageInfo.EncourageAction(
                    1, "创建家族"
                )
            )
        ), {}
    )
}