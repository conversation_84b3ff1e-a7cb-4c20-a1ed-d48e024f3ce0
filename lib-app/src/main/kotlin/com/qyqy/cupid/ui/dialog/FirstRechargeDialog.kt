package com.qyqy.cupid.ui.dialog

import androidx.annotation.Keep
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.common.io.Files.append
import com.ishumei.smantifraud.SmAntiFraud.option
import com.qyqy.cupid.data.FirstRechargeRewardModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.ui.profile.RechargeDialog
import com.qyqy.cupid.ui.profile.RechargePageContent
import com.qyqy.cupid.ui.profile.RechargePageWithoutListScaffold
import com.qyqy.cupid.widgets.AlignHorizontalContainer
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.DataPoint
import com.qyqy.ucoo.DataTrace
import com.qyqy.ucoo.R
import com.qyqy.ucoo.ReportExposureCompose
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.app
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.ui.AnimatedComposeImage
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.IconLoading
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.user.AppPurchaseHelper
import com.qyqy.ucoo.user.ORDER_TYPE_GOLD
import com.qyqy.ucoo.user.Purchase
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.launch
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

//region 首页首充挂件相应的dialog


data class FirstRechargeRewardDialog(val model: FirstRechargeRewardModel) : SimpleAnimatedDialog() {

    @Composable
    override fun Content(dialog: IDialog) {
        LaunchedEffect(key1 = Unit) {
            UIConfig.refreshFirstRechargeReward()
        }
        val dialogQueue = LocalDialogQueue.current
        ReportExposureCompose(exposureName = "jp_first_charge_expose")
        FirstRechargeRewardContent(model, {
            Analytics.appReportEvent(DataPoint.clickBody("jp_first_charge_click"))
            dialog.dismiss()
            dialogQueue.push(RechargeDialog)
        }) {
            dialog.dismiss()
        }
    }

}

@Composable
private fun FirstRechargeRewardContent(model: FirstRechargeRewardModel, onRecharge: OnClick, onClose: OnClick) {
    val context = LocalContext.current
    Box {
        Column {
            ComposeImage(
                model = R.drawable.cpd_ic_first_recharge_top,
                modifier = Modifier.fillMaxWidth(),
                contentScale = ContentScale.FillBounds
            )
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        brush = Brush.verticalGradient(
                            listOf(
                                Color(0xffff5631),
                                Color(0xffff75a7)
                            )
                        )
                    ),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {

                if (model.version == 2) {
                    Text(text = buildAnnotatedString {
                        withStyle(
                            SpanStyle(
                                fontWeight = FontWeight.SemiBold,
                                fontSize = 16.sp,
                                color = Color(0xfffffdc9)
                            )
                        ) {
                            append(model.displayCostJapanCoinCnt.toString())
                        }
                        append(context.getString(R.string.cpd充值金额))
                        withStyle(
                            SpanStyle(
                                fontWeight = FontWeight.SemiBold,
                                fontSize = 16.sp,
                                color = Color(0xfffffdc9)
                            )
                        ) {
                            append(model.displayValueJapanCoinCnt.toString())
                        }
                        append(context.getString(R.string.cpd赠送金额))
                    }, fontSize = 12.sp, color = Color.White, lineHeight = 12.sp)
                    FirstRechargeContentVersion2(model = model)
                } else {
                    Text(text = model.title, fontSize = 14.sp, color = Color.White, lineHeight = 14.sp)
                    Row(
                        modifier = Modifier
                            .padding(top = 16.dp)
                            .horizontalScroll(rememberScrollState()),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Spacer(modifier = Modifier.width(20.dp))
                        for (item in model.rights) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Box(
                                    modifier = Modifier
                                        .size(101.dp)
                                        .background(Color(0xFFFFF0E7), RoundedCornerShape(8.dp))
                                        .border(width = 3.dp, color = Color(0xFFFEC18A), shape = RoundedCornerShape(8.dp))
                                        .padding(10.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    ComposeImage(
                                        model = item.icon,
                                        modifier = Modifier.sizeIn(minWidth = 30.dp, minHeight = 30.dp),
                                        contentScale = ContentScale.Fit
                                    )
                                }

                                Spacer(modifier = Modifier.height(8.dp))

                                Text(
                                    text = item.text,
                                    modifier = Modifier
                                        .widthIn(max = 101.dp)
                                        .heightIn(min = 30.dp),
                                    fontSize = 12.sp,
                                    color = Color.White,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                        Spacer(modifier = Modifier.width(20.dp))
                    }

                    Image(
                        painter = painterResource(id = R.drawable.btn_first_recharge),
                        contentDescription = null,
                        modifier = Modifier
                            .padding(top = 16.dp)
                            .noEffectClickable(onClick = onRecharge)
                    )
                }
            }
        }
        Image(
            painter = painterResource(id = R.drawable.ic_cpd_close_small),
            contentDescription = null,
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(top = 36.dp, end = 8.dp)
                .size(20.dp)
                .noEffectClickable(onClick = onClose)
        )
    }
}

@Composable
private fun FirstRechargeContentVersion2(model: FirstRechargeRewardModel) {
    RechargePageWithoutListScaffold { onConfirm ->
        var selectChannelIdx by remember {
            mutableStateOf(model.payWays.firstOrNull()?.fkChannel ?: -1)
        }

        val selectPayway by remember {
            derivedStateOf {
                model.payWays.find {
                    it.fkChannel == selectChannelIdx
                }
            }
        }

        // 当前选中的付款方式对应的商品
        val selectedMemberOption by remember {
            derivedStateOf {
                selectPayway?.chargeItems?.firstOrNull()
            }
        }
        val context = LocalContext.current

        selectedMemberOption?.apply {
            LazyVerticalGrid(
                columns = GridCells.Fixed(4),
                modifier = Modifier.padding(vertical = 8.dp, horizontal = 12.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.CenterHorizontally)
            ) {
                if (topRights != null) {
                    item {
                        GoodItem(
                            modifier = Modifier
                                .fillMaxWidth()
                                .aspectRatio(1f), topRights
                        )
                    }
                }
                items(subRights) {
                    GoodItem(
                        modifier = Modifier
                            .fillMaxWidth()
                            .aspectRatio(1f), it
                    )
                }
            }
        }

        // 1 Google IAP、2 APPLE IAP、3 WXPAY、4 ALIPAY
        if (model.payWays.size > 1) {
            Column(
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth()
                    .background(color = Color.White, shape = RoundedCornerShape(8.dp))
                    .padding(vertical = 12.dp, horizontal = 16.dp)
                    .heightIn(max = 360.dp)
            ) {
                Text(
                    stringResource(id = R.string.cpd请选择支付方式), fontSize = 14.sp, lineHeight = 14.sp,
                    color = Color(0xff1d2129),
                    fontWeight = FontWeight.Medium, modifier = Modifier.padding(bottom = 8.dp)
                )
                LazyColumn {
                    itemsIndexed(model.payWays) { idx, payway ->
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .padding(vertical = 8.dp)
                                .click(noEffect = true) {
                                    selectChannelIdx = payway.fkChannel
                                }
                        ) {
                            ComposeImage(model = payway.icon, modifier = Modifier.size(28.dp), contentScale = ContentScale.FillBounds)
                            Text(
                                payway.name, modifier = Modifier
                                    .padding(start = 8.dp),
                                maxLines = 1, overflow = TextOverflow.Ellipsis,
                                color = Color(0xFF1D2129),
                                fontSize = 14.sp, lineHeight = 14.sp
                            )
                            payway.discount?.let {
                                Text(
                                    it.name, modifier = Modifier
                                        .padding(start = 8.dp)
                                        .background(
                                            color = Color(0xFFF53F3F),
                                            shape = RoundedCornerShape(4.dp)
                                        )
                                        .padding(horizontal = 4.dp),
                                    fontSize = 10.sp, lineHeight = 18.sp, textAlign = TextAlign.Center,
                                    color = Color.White
                                )
                            }
                            Spacer(modifier = Modifier.weight(1f))
                            ComposeImage(
                                model = if (selectChannelIdx == payway.fkChannel) R.drawable.ic_cpd_checked_2 else R.drawable.ic_cpd_unchecked_2,
                                modifier = Modifier.size(18.dp)
                            )
                        }
                        if (idx != model.payWays.lastIndex) {
                            HorizontalDivider(thickness = 0.5.dp, color = Color(0xFFEAEAEA))
                        }
                    }
                }
            }
        }

        Box(
            modifier = Modifier
                .padding(top = 16.dp, bottom = 8.dp)
                .paint(painter = painterResource(id = R.drawable.ic_cpd_first_recharge))
                .click {
                    val fkType = selectPayway?.callType ?: return@click
                    val fkLink = selectedMemberOption?.fkLink ?: ""
                    val orderType = model.orderType
                    val productId = selectedMemberOption?.productId ?: ""
//                    Analytics.appReportEvent(DataPoint.clickBody("jp_first_charge_click"))
                    Analytics.appReportEvent(DataPoint.clickBody(TracePoints.JP_NB_PKG_202502_BUY_BTN_CLICK, useShushu = true))
                    onConfirm(fkType, productId, fkLink, orderType)
                }
        ) {
            Text(
                buildAnnotatedString {
                    append(context.getString(R.string.cpd立即购入))
                    withStyle(
                        SpanStyle(
                            fontSize = 12.sp
                        )
                    ) {
                        append("（${selectedMemberOption?.transformPrice?.replace(" ", "")}）")
                    }
                }, modifier = Modifier
                    .align(Alignment.Center)
                    .padding(bottom = 4.dp), color = Color.White, fontSize = 18.sp, lineHeight = 18.sp
            )
        }
    }
}

@Composable
private fun GoodItem(modifier: Modifier = Modifier, item: FirstRechargeRewardModel.RewardItem) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(1f)
                .background(Color(0xFFFFF0E7), RoundedCornerShape(8.dp))
                .border(width = 3.dp, color = Color(0xFFFEC18A), shape = RoundedCornerShape(8.dp))
                .padding(10.dp),
            contentAlignment = Alignment.Center
        ) {
            ComposeImage(
                model = item.icon,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillWidth,
                forceThird = true
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = item.text,
            modifier = Modifier
                .widthIn(max = 101.dp)
                .heightIn(min = 30.dp),
            fontSize = 12.sp,
            color = Color.White,
            textAlign = TextAlign.Center
        )
    }
}

//endregion

//region 带用户信息的首充dialog

@Keep
@Serializable
data class FirstRechargeUserInfo(
    @SerialName("user")
    val user: AppUser,
    @SerialName("target_user")
    val target: AppUser,
    @SerialName("title")
    val title: String,
    @SerialName("hint")
    val hint: String
)

class FirstRechargeWithUserInfoDialog(val json: String) : SimpleAnimatedDialog() {

    @Composable
    override fun Content(dialog: IDialog) {
        var model by remember {
            mutableStateOf(UIConfig.firstRechargeRewardState.value)
        }
//        LaunchedEffect(key1 = Unit) {
//            UIConfig.refreshFirstRechargeReward()
//        }
        LaunchedEffect(key1 = Unit) {
            UIConfig.refreshFirstRechargeReward {
                model = it
            }
        }

        if (model != null) {
            val bean = sAppJson.decodeFromString<FirstRechargeUserInfo>(json)
            FirstRechargeWithUserInfoContent(model!!, bean.title, bean.hint, bean.target.avatarUrl, bean.user.avatarUrl)
        } else {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(108.dp), contentAlignment = Alignment.Center
            ) {
                IconLoading()
            }
        }
    }
}

@Composable
private fun FirstRechargeWithUserInfoContent(
    model: FirstRechargeRewardModel,
    title: String = stringResource(id = R.string.充值金币继续和她聊天),
    hint: String = stringResource(R.string.你的免费聊天次数已用完),
    leftAvatar: String = "",
    rightAvatar: String = "",
) {
    val density = LocalDensity.current
    val context = LocalContext.current
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 16.dp)
            .background(Color.White, RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp))
            .paint(
                painterResource(id = R.drawable.bg_top_recharge_coin_to_chat),
                contentScale = ContentScale.FillWidth,
                alignment = Alignment.TopCenter
            )
            .padding(bottom = with(density) {
                WindowInsets.navigationBars
                    .getBottom(density)
                    .toDp()
            }.coerceAtLeast(15.dp))
    ) {

        Row(
            modifier = Modifier
                .align(Alignment.TopCenter)
                .offset(y = (-16).dp),
            horizontalArrangement = Arrangement.spacedBy((-16).dp)
        ) {
            CircleComposeImage(
                model = leftAvatar, modifier = Modifier
                    .size(80.dp)
                    .border(0.75.dp, Color.White, CircleShape)
            )
            CircleComposeImage(
                model = rightAvatar, modifier = Modifier
                    .size(80.dp)
                    .border(0.75.dp, Color.White, CircleShape)
            )
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 80.dp, start = 16.dp, end = 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            Text(
                text = title,
                color = Color(0xFF1D2129),
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(10.dp))

            Text(
                text = hint,
                color = Color(0xFF4E5969),
                fontSize = 12.sp,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(20.dp))

            Text(
                text = stringResource(id = R.string.cpd限时首充奖励),
                fontSize = 14.sp,
                lineHeight = 14.sp,
                color = Color.Black,
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(10.dp))

            RechargePageWithoutListScaffold { onConfirm ->
                var selectChannelIdx by remember {
                    mutableStateOf(model.payWays.firstOrNull()?.fkChannel ?: -1)
                }

                val selectPayway by remember {
                    derivedStateOf {
                        model.payWays.find {
                            it.fkChannel == selectChannelIdx
                        }
                    }
                }

                // 当前选中的付款方式对应的商品
                val selectedMemberOption by remember {
                    derivedStateOf {
                        selectPayway?.chargeItems?.firstOrNull()
                    }
                }

                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    selectedMemberOption?.apply {
                        LazyVerticalGrid(
                            columns = GridCells.Fixed(4),
                            horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.CenterHorizontally)
                        ) {
                            if (topRights != null) {
                                item {
                                    GoodItemV2(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .aspectRatio(1f), topRights
                                    )
                                }
                            }
                            items(subRights) {
                                GoodItemV2(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .aspectRatio(1f), it
                                )
                            }
                        }
                    }

                    if (model.payWays.size > 1) {
                        Text(
                            stringResource(id = R.string.cpd请选择支付方式), fontSize = 14.sp, lineHeight = 14.sp,
                            color = Color(0xff1d2129),
                            fontWeight = FontWeight.Medium, modifier = Modifier
                                .align(Alignment.Start)
                                .padding(bottom = 8.dp)
                        )

                        LazyColumn(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(color = Color.White, shape = RoundedCornerShape(8.dp))
                                .heightIn(max = 360.dp)
                        ) {
                            itemsIndexed(model.payWays) { idx, payway ->
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier
                                        .background(color = Color.White, shape = RoundedCornerShape(8.dp))
                                        .border(0.5.dp, color = Color(0xFFEAEAEA), shape = RoundedCornerShape(8.dp))
                                        .padding(12.dp)
                                        .click(noEffect = true) {
                                            selectChannelIdx = payway.fkChannel
                                        }
                                ) {
                                    ComposeImage(model = payway.icon, modifier = Modifier.size(28.dp), contentScale = ContentScale.FillBounds)
                                    Text(
                                        payway.name, modifier = Modifier
                                            .padding(start = 8.dp),
                                        maxLines = 1, overflow = TextOverflow.Ellipsis,
                                        color = Color(0xFF1D2129),
                                        fontSize = 14.sp, lineHeight = 14.sp
                                    )
                                    payway.discount?.let {
                                        Text(
                                            it.name, modifier = Modifier
                                                .padding(start = 8.dp)
                                                .background(
                                                    color = Color(0xFFF53F3F),
                                                    shape = RoundedCornerShape(4.dp)
                                                )
                                                .padding(horizontal = 4.dp),
                                            fontSize = 10.sp, lineHeight = 18.sp, textAlign = TextAlign.Center,
                                            color = Color.White
                                        )
                                    }
                                    Spacer(modifier = Modifier.weight(1f))
                                    ComposeImage(
                                        model = if (selectChannelIdx == payway.fkChannel) R.drawable.ic_cpd_checked_2 else R.drawable.ic_cpd_unchecked_2,
                                        modifier = Modifier.size(18.dp)
                                    )
                                }
                                if (idx != model.payWays.size - 1) {
                                    Spacer(modifier = Modifier.height(4.dp))
                                }
                            }
                        }
                    }


                    AppButton(
                        text = buildAnnotatedString {
                            append(context.getString(R.string.cpd立即购入))
                            withStyle(
                                SpanStyle(
                                    fontSize = 12.sp
                                )
                            ) {
                                append("（${selectedMemberOption?.transformPrice?.replace(" ", "")}）")
                            }
                        }, modifier = Modifier
                            .padding(horizontal = 8.dp)
                            .padding(top = 16.dp, bottom = 8.dp)
                            .fillMaxWidth()
                            .height(44.dp),
                        colors = ButtonColors(
                            containerColor = CpdColors.FFFF5E8B,
                            contentColor = Color.White,
                            disabledContainerColor = CpdColors.FFFF5E8B,
                            disabledContentColor = Color.White
                        )
                    ) {
                        val fkType = selectPayway?.callType ?: return@AppButton
                        val fkLink = selectedMemberOption?.fkLink ?: ""
                        val orderType = model.orderType
                        val productId = selectedMemberOption?.productId ?: ""
                        onConfirm(fkType, productId, fkLink, orderType)
                    }
                }
            }
        }
    }
}

@Composable
private fun GoodItemV2(modifier: Modifier = Modifier, item: FirstRechargeRewardModel.RewardItem) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(80.dp)
                .background(Color(0xFFFFE7EE), RoundedCornerShape(8.dp))
                .padding(10.dp),
            contentAlignment = Alignment.Center
        ) {
            ComposeImage(
                model = item.icon,
                modifier = Modifier.sizeIn(minWidth = 30.dp, minHeight = 30.dp),
                contentScale = ContentScale.Fit
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = item.text,
            modifier = Modifier
                .widthIn(max = 101.dp)
                .heightIn(min = 30.dp),
            fontSize = 12.sp,
            color = CpdColors.FF1D2129,
            textAlign = TextAlign.Center
        )
    }
}

//endregion

@Preview
@Composable
private fun PreviewFirstRechargeContent() {
    FirstRechargeRewardContent(
        FirstRechargeRewardModel(
        isEnable = true,
        version = 2,
        title = "但是了肯德基是",
        rights = listOf(FirstRechargeRewardModel.RewardItem("", "hhh"), FirstRechargeRewardModel.RewardItem("", "hhh")),
        payWays = listOf(
            FirstRechargeRewardModel.PayWay(
                name = "测试支付方式啦", desc = "这是描述", icon = "",
                discount = FirstRechargeRewardModel.Discount("30%割引"),
                chargeItems = listOf(
                    FirstRechargeRewardModel.PayWay.ChargeItem(
                        "", "", "1", "",
                        subRights = listOf(
                            FirstRechargeRewardModel.RewardItem("", "1"),
                            FirstRechargeRewardModel.RewardItem("", "2"),
                            FirstRechargeRewardModel.RewardItem("", "3"),
                            FirstRechargeRewardModel.RewardItem("", "4"),
                            FirstRechargeRewardModel.RewardItem("", "5"),
                            FirstRechargeRewardModel.RewardItem("", "6")
                        ),
                        topRights = FirstRechargeRewardModel.RewardItem("", "topItem"),
                    )
                )
            )
        )
    ), {}, {})
}

@Preview
@Composable
private fun RechargeCoinToChatPanelPreview() {
    FirstRechargeWithUserInfoContent(
        FirstRechargeRewardModel(
            isEnable = true,
            version = 2,
            title = "但是了肯德基是",
            rights = listOf(FirstRechargeRewardModel.RewardItem("", "hhh"), FirstRechargeRewardModel.RewardItem("", "hhh")),
            payWays = listOf(
                FirstRechargeRewardModel.PayWay(
                    name = "测试支付方式啦", desc = "这是描述", icon = "",
                    discount = FirstRechargeRewardModel.Discount("30%割引"),
                    chargeItems = listOf(
                        FirstRechargeRewardModel.PayWay.ChargeItem(
                            "", "", "1", "",
                            subRights = listOf(
                                FirstRechargeRewardModel.RewardItem("", "1"),
                                FirstRechargeRewardModel.RewardItem("", "2"),
                                FirstRechargeRewardModel.RewardItem("", "3"),
                                FirstRechargeRewardModel.RewardItem("", "4"),
                                FirstRechargeRewardModel.RewardItem("", "5"),
                                FirstRechargeRewardModel.RewardItem("", "6")
                            ),
                            topRights = FirstRechargeRewardModel.RewardItem("", "topItem"),
                        )
                    )
                ),
                FirstRechargeRewardModel.PayWay(
                    name = "测试支付方式啦", desc = "这是描述", icon = "",
                    discount = FirstRechargeRewardModel.Discount("30%割引"),
                    chargeItems = listOf(
                        FirstRechargeRewardModel.PayWay.ChargeItem(
                            "", "", "1", "",
                            subRights = listOf(
                                FirstRechargeRewardModel.RewardItem("", "1"),
                                FirstRechargeRewardModel.RewardItem("", "2"),
                                FirstRechargeRewardModel.RewardItem("", "3"),
                                FirstRechargeRewardModel.RewardItem("", "4"),
                                FirstRechargeRewardModel.RewardItem("", "5"),
                                FirstRechargeRewardModel.RewardItem("", "6")
                            ),
                            topRights = FirstRechargeRewardModel.RewardItem("", "topItem"),
                        )
                    )
                )
            )
        ),
        title = "title",
        hint = "hint",
        leftAvatar = "",
        rightAvatar = "",
    )
}

@Composable
fun FirstRechargeRewardFloat(modifier: Modifier = Modifier) {

    val model = UIConfig.firstRechargeRewardState.value

    val dialogQueue = LocalDialogQueue.current

    val visible = model?.isEnable == true
    AlignHorizontalContainer(
        visible = visible,
        modifier = modifier,
        tag = "FirstRechargeRewardFloat"
    ) {
        ReportExposureCompose(exposureName = TracePoints.JP_NB_PKG_202502_POPUP_SHOW, useShushu = true) {
            Box(modifier = Modifier.noEffectClickable(enabled = visible) {
                dialogQueue.push(FirstRechargeRewardDialog(model!!))
            }) {

                AnimatedComposeImage(
                    model = com.qyqy.ucoo.glide.drawable.Image.assetsToUri("ic_first_recharge_reward.webp"),
                    modifier = Modifier.size(68.dp, 90.dp)
                )

                Image(
                    painter = painterResource(id = R.drawable.ic_cpd_close_small),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(top = 5.dp)
                        .size(15.dp)
                        .noEffectClickable(onClick = {
                            UIConfig.firstRechargeRewardState.value = null
                        })
                )
            }
        }
    }
}