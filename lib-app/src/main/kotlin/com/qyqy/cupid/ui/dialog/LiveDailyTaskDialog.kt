package com.qyqy.cupid.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.data.AudioRoomAudiencePopup
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.ComposeImage


@Composable
fun DailyTaskWidget(modifier: Modifier = Modifier, bean: AudioRoomAudiencePopup, onClick: (AudioRoomAudiencePopup.Button) -> Unit) {
    Box(modifier = modifier.fillMaxWidth()) {
        Column(
            modifier = Modifier
                .padding(top = 24.dp)
                .fillMaxWidth()
                .background(
                    brush = Brush.horizontalGradient(
                        listOf(Color(0xffff859b), Color(0xffff3e74))
                    ), shape = RoundedCornerShape(topStart = 40.dp, topEnd = 40.dp, bottomStart = 24.dp, bottomEnd = 24.dp)
                )
                .padding(top = 40.dp, start = 16.dp, end = 16.dp, bottom = 20.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Row(
                modifier = Modifier
                    .padding(vertical = 10.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(24.dp, Alignment.CenterHorizontally),
            ) {
                bean.bonus.forEach { bonu ->
                    Column(
                        Modifier
                            .size(80.dp)
                            .background(
                                color = Color(0x26ffffff), shape = RoundedCornerShape(8.dp)
                            )
                            .padding(6.dp), verticalArrangement = Arrangement.Center, horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        ComposeImage(
                            model = bonu.icon,
                            modifier = Modifier.weight(1f),
                            contentScale = ContentScale.FillHeight,
                            contentDescription = null
                        )
                        if (bonu.text.isNotBlank()) {
                            Text(
                                bonu.text, modifier = Modifier.padding(top = 5.dp),
                                color = Color(0xfffffb0e),
                                fontSize = 16.sp, fontWeight = FontWeight.Medium,
                                lineHeight = 16.sp
                            )
                        }
                    }
                }
            }
            Text(
                text = bean.desc,
                color = Color.White,
                fontSize = 14.sp,
                lineHeight = 21.sp
            )
            Row(
                modifier = Modifier
                    .padding(top = 24.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.Center
            ) {
                bean.buttons.forEach { button ->
                    AppButton(
                        text = button.label,
                        modifier = Modifier
                            .padding(horizontal = 6.dp)
                            .weight(1f)
                            .height(40.dp),
                        color = CpdColors.FFFF5E8B,
                        background = Color.White,
                        fontSize = 12.sp,
                    ) {
                        onClick(button)
                    }
                }
            }
        }
        Text(
            text = bean.title,
            fontSize = 18.sp,
            lineHeight = 48.sp,
            textAlign = TextAlign.Center,
            color = Color(0xff8b392a),
            modifier = Modifier
                .width(295.dp)
                .height(64.dp)
                .paint(painterResource(id = R.drawable.cpd_live_task_header))
                .align(Alignment.TopCenter)
        )
    }
}

class LiveDailyTaskDialog(val bean: AudioRoomAudiencePopup) : SimpleDialog() {
    @Composable
    override fun Content(dialog: IDialog) {
        val controller = LocalAppNavController.current
        DailyTaskWidget(bean = bean) { button ->
            dialog.dismiss()
            if (button.t != 0) {
                controller.navigateByLink(button.actionLink)
            }
        }
    }

}

@Preview
@Composable
private fun DailyTaskWidgetPreview() {
    PreviewCupidTheme {
        DailyTaskWidget(
            Modifier, AudioRoomAudiencePopup(
                title = "这个是标题",
                bonus = listOf(
                    AudioRoomAudiencePopup.Bonu(
                        icon = "",
                        text = "xxx"
                    )
                ),
                buttons = listOf(
                    AudioRoomAudiencePopup.Button(
                        t = 0, label = "わかった",
                        actionLink = "ucoo://xxx"
                    )
                ),
                desc = "这是一个描述这是一个描述这是一个描述这是一个描述"
            )
        ) {

        }
    }
}

