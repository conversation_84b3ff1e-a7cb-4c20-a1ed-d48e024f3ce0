package com.qyqy.cupid.ui.dialog

import android.os.Parcelable
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.utils.OnClick
import kotlinx.parcelize.Parcelize



@Composable
fun RegisterRewardDialogContent(
    title: String,
    button: String,
    count: String,
    isFemale: Boolean,
    backgroundIMG: String = "",
    onClick: OnClick = {}
) {
    Box {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .padding(top = 40.dp)
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            Color(0xFFFFBA54),
                            Color(0xFFFF5E8B)
                        )
                    ),
                    shape = RoundedCornerShape(12.dp)
                )
                .size(280.dp, 336.dp)
        ) {
            Spacer(modifier = Modifier.height(76.dp))
            Text(
                title,
                style = TextStyle(color = Color.White, fontSize = 14.sp, fontWeight = FontWeight.Medium),
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(horizontal = 12.dp)
            )
            Spacer(modifier = Modifier.weight(1f))
            Column(
                modifier = Modifier
                    .background(
                        brush = Brush.verticalGradient(
                            colors = listOf(
                                Color(0xFFFFFDF1),
                                Color(0xFFFFDFAE)
                            )
                        ),
                        shape = RoundedCornerShape(12.dp)
                    )
                    .border(
                        1.5.dp, brush = Brush.verticalGradient(
                            colors = listOf(
                                Color(0xFFFFEF61),
                                Color(0xFFFF9737)
                            )
                        ), RoundedCornerShape(12.dp)
                    )
                    .size(88.dp),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                ComposeImage(model = if (isFemale) R.drawable.ic_cpd_diamond else R.drawable.ic_cpd_coin, modifier = Modifier.size(40.dp))
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = count, style = MaterialTheme.typography.titleSmall.copy(
                        color = Color(0xFFC54016)
                    )
                )
            }
            Spacer(modifier = Modifier.weight(1f))
            Box(
                modifier = Modifier
                    .size(232.dp, 40.dp)
                    .paint(painterResource(id = R.drawable.ic_cpd_first_login))
                    .clip(CircleShape)
                    .click(onClick = onClick),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    button, style = TextStyle(
                        color = Color(0xFFC54016),
                        fontSize = 14.sp
                    )
                )
            }
            Spacer(modifier = Modifier.height(16.dp))
        }
        ComposeImage(
            model = R.drawable.ic_cpd_first_login_header,
            modifier = Modifier.size(280.dp, 116.dp)
        )
    }
}


@Preview
@Composable
private fun PreviewRegisterRewardDialogContent() {
    PreviewCupidTheme {
        RegisterRewardDialogContent(
            "登録成功、おめでとうございます。300コインのボーナスを獲得しました。お楽しみください！",
            "コインを受け取る",
            "x300", true
        )
    }
}

data class RegisterRewardDialog(
    val title: String,
    val button: String,
    val count: Int,
    val isFemale: Boolean,
    val link: String = "",
    val backgroundIMG: String = ""
) : SimpleDialog() {

    @Composable
    override fun Content(dialog: IDialog) {
        val controller = LocalAppNavController.current
        RegisterRewardDialogContent(
            title = title,
            button = button,
            count = "x$count",
            backgroundIMG = backgroundIMG,
            isFemale = isFemale
        ) {
            if(link.isNotBlank()){
                controller.navigateByLink(link)
            }
            dialog.dismiss()
        }
    }

}