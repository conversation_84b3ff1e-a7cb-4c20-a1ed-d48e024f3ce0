package com.qyqy.cupid.ui.dialog

import android.os.Parcelable
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.ui.coin.SigninDialogContent
import com.qyqy.cupid.ui.home.CupidHomeViewModel
import com.qyqy.ucoo.R
import com.qyqy.ucoo.asComponentActivity
import com.qyqy.ucoo.compose.ui.AnyPopDialogProperties
import com.qyqy.ucoo.compose.ui.DirectionState
import com.qyqy.ucoo.compose.ui.IconLoading
import com.qyqy.ucoo.http.getIntOrNull
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonPrimitive

/**
 *  @time 8/14/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.dialog
 */
@Parcelize
data object SigninDialog : SimpleAnimatedDialog(), Parcelable {
    override val properties: AnyPopDialogProperties
        get() = AnyPopDialogProperties(true, true, direction = DirectionState.CENTER)

    @Composable
    override fun Content(dialog: IDialog) {
        SignInContent {
            dialog.dismiss()
        }
    }
}


@Composable
private fun SignInContent(onClose: OnClick = {}) {
    val owner = LocalContext.current.asComponentActivity
    if (owner == null) {
        onClose()
    }

    //使用activity的全局viewModel
    val viewModel = viewModel(modelClass = CupidHomeViewModel::class.java, viewModelStoreOwner = owner!!)

    val signInfo = viewModel.signInfo.collectAsStateWithLifecycle()

    if (signInfo.value.tasks.isEmpty()) {
        LaunchedEffect(Unit) {
            viewModel.requestSignInfo()
        }
    }

    val loadingFlag = remember {
        mutableStateOf(false)
    }

    val scope = rememberCoroutineScope()

    val dialogQueue = LocalDialogQueue.current

    Box {
        //签到组件
        SigninDialogContent(signInfo.value, onDismiss = { onClose() }) {
            scope.launch {
                loadingFlag.value = true
                viewModel.startSign(signInfo.value.seriesId).onSuccess {
                    onClose()
                    val title = it.get("message")?.jsonPrimitive?.content ?: ""
                    val info = it.get("prize_info")?.jsonPrimitive?.content ?: ""
                    val type = it.getIntOrNull("prize_type") ?: 0
                    dialogQueue.push(
                        CupidTaskRewardDialog(
                            if (type == 5) R.drawable.ic_cpd_task_coin else R.drawable.ic_cpd_task_diamond,
                            info, title
                        )
                    )
                }
                loadingFlag.value = false
            }
        }

        //加载框
        AnimatedVisibility(visible = loadingFlag.value, modifier = Modifier.align(Alignment.Center)) {
            IconLoading()
        }
    }
}