

package com.qyqy.cupid.ui.dialog

import android.os.Parcelable
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ElevatedButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.widgets.composeClick
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.ComposeImage
import kotlinx.parcelize.Parcelize

/**
 *  @time 8/2/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.dialog
 */
@Composable
fun TaskFinishDialogContent(
    rewardStr: String,
    rewardTips: String,
    rewardIcon: Any = R.drawable.ic_cpd_task_coin,
    onClick: () -> Unit = {}
) {
    Column(
        modifier = Modifier
            .background(
                brush = Brush.verticalGradient(
                    listOf(
                        Color(0xFFFF90AF),
                        Color(0xFFFF5E8B)
                    )
                ),
                shape = RoundedCornerShape(12.dp)
            )
            .widthIn(max = 270.dp)
            .padding(vertical = 20.dp, horizontal = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        ComposeImage(model = rewardIcon, modifier = Modifier.size(96.dp))
        Spacer(modifier = Modifier.height(8.dp))
        Text(rewardStr, style = MaterialTheme.typography.titleMedium.copy(color = colorResource(id = R.color.FFFFE666)))
        Spacer(modifier = Modifier.height(10.dp))
        Text(
            rewardTips, textAlign = TextAlign.Center,
            style = MaterialTheme.typography.titleSmall.copy(color = Color.White, fontSize = 12.sp)
        )
        Spacer(modifier = Modifier.height(10.dp))
        ElevatedButton(
            onClick = composeClick {
                onClick()
            }, modifier = Modifier
                .width(206.dp)
                .height(36.dp), contentPadding = PaddingValues(horizontal = 8.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = Color.White,
                contentColor = colorResource(id = R.color.FFFF5E8B)
            ),
            elevation = null
        ) {
            Text(stringResource(id = R.string.cpd确认), style = MaterialTheme.typography.titleSmall)
        }
    }
}

@Composable
@Preview
fun TaskFinishDialogPreview() {
    TaskFinishDialogContent(
        "50金币",
        "完成\"填写所有个人基础信息\"任务,获得金币奖励完成\"填写所有个人基础信息\"任务,获得金币奖励完成\"填写所有个人基础信息\"任务,获得金币奖励",
        R.drawable.ic_cpd_task_diamond
    )
}

@Parcelize
data class CupidTaskRewardDialog(
    val rewardImage: Int,
    val rewardValue: String,
    val rewardDesc: String,
) : SimpleDialog(), Parcelable {

    @Composable
    override fun Content(dialog: IDialog) {
        TaskFinishDialogContent(rewardStr = rewardValue, rewardTips = rewardDesc, rewardImage) {
            dialog.dismiss()
        }
    }
}