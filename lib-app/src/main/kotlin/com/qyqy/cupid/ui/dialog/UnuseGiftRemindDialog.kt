package com.qyqy.cupid.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.ui.GiftExtra
import com.qyqy.cupid.ui.IC2CAction
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.bean.PrivilegedGiftRemindBean
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.ui.AnyPopDialogProperties
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.DirectionState
import com.qyqy.ucoo.im.bean.CPGift
import com.qyqy.ucoo.utils.LogUtils

data class UnuseGiftRemindDialog(
    private val bean: PrivilegedGiftRemindBean,
    val onDismiss: () -> Unit
) : AnimatedDialog<IC2CAction>() {

    override val properties: AnyPopDialogProperties
        get() = AnyPopDialogProperties(direction = DirectionState.CENTER)

    @Composable
    override fun Content(dialog: IDialog, onAction: IC2CAction?) {
        UnuseGiftRemindContent(bean = bean) {
            onAction?.onSendGift(CPGift(id = bean.gift.id, icon = bean.gift.icon, name = bean.gift.name, price = 0), 1, GiftExtra(fromPacket = true))
            dialog.dismiss()
        }
    }

    override fun onDismiss(isInitiative: Boolean) {
        if (!isInitiative) {
            onDismiss()
        }
    }
}

@Composable
private fun UnuseGiftRemindContent(bean: PrivilegedGiftRemindBean, clickOK: () -> Unit = {}) {
    Column(
        modifier = Modifier
            .fillMaxWidth(0.72f)
            .background(
                color = Color.White,
                shape = RoundedCornerShape(8.dp)
            )
            .padding(20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = bean.desc,
            lineHeight = 21.sp,
            fontSize = 14.sp,
            color = CpdColors.FF1D2129,
            modifier = Modifier.padding(horizontal = 16.dp),
            textAlign = TextAlign.Center,
        )
        ComposeImage(
            model = bean.gift.icon,
            modifier = Modifier
                .padding(top = 16.dp)
                .size(80.dp)
                .background(
                    color = Color(0x26ff5e8b),
                    shape = RoundedCornerShape(8.dp)
                )
                .padding(10.dp),
            contentScale = ContentScale.FillWidth
        )
        Text(
            text = bean.gift.name,
            fontSize = 12.sp,
            color = CpdColors.FF86909C,
            textAlign = TextAlign.Center,
            lineHeight = 12.sp,
            modifier = Modifier.padding(top = 8.dp, bottom = 20.dp)
        )
        Box(
            modifier = Modifier
                .sizeIn(174.dp, 36.dp, 210.dp, 36.dp)
                .background(
                    color = CpdColors.FFFF5E8B,
                    shape = CircleShape
                )
                .click(noEffect = true) { clickOK() }
        ) {
            Text(
                text = bean.btnTxt,
                modifier = Modifier.align(Alignment.Center),
                color = Color.White,
                fontSize = 12.sp,
                lineHeight = 12.sp
            )
        }
    }
}

@Composable
@Preview(showBackground = true, widthDp = 375)
private fun FreeGiftRemindDialogPreview() {
    Box(modifier = Modifier.padding(10.dp)) {
        UnuseGiftRemindContent(PrivilegedGiftRemindBean(userForPreview, "点击赠送", "这是描述"))
    }
}