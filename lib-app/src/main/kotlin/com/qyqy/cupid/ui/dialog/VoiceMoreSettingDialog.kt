package com.qyqy.cupid.ui.dialog

import androidx.compose.runtime.Composable
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.cupid.ui.live.VoiceLiveChatRoom
import com.qyqy.cupid.ui.live.settings.VoiceRoomAction
import com.qyqy.cupid.ui.live.settings.VoiceRoomInfoSetting
import com.qyqy.cupid.ui.live.settings.VoiceRoomSettingPanel
import com.qyqy.cupid.ui.live.settings.VoiceRoomSettingPanelItem

/**
 *  @time 8/29/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.dialog
 */
data class VoiceMoreSettingDialog(val settings: VoiceLiveChatRoom) : AnimatedDialog<IVoiceLiveAction>() {
    @Composable
    override fun Content(dialog: IDialog, onAction: IVoiceLiveAction?) {
        VoiceRoomSettingPanel(settings) { item ->
            when (item) {
                is VoiceRoomSettingPanelItem.RoomAdmin -> {
                    dialog.dismiss()
                    onAction?.showRoomInfoManagePanel()
                }

                is VoiceRoomSettingPanelItem.RoomMode -> {
                    dialog.dismiss()
                    onAction?.showRoomModeManagePanel()
                }

                is VoiceRoomSettingPanelItem.ReportRoom -> {
                    dialog.dismiss()
                    onAction?.reportRoom()
                }

                is VoiceRoomSettingPanelItem.BlackRoom -> {
                    dialog.dismiss()
                    onAction?.showBlackRoomDialog()
                }

                is VoiceRoomSettingPanelItem.LockRoom -> {
                    dialog.dismiss()
                    onAction?.showLockRoomDialog()
                }

                is VoiceRoomSettingPanelItem.UnLockRoom -> {
                    onAction?.toggleRoomLock(dialog, false)
                }

                is VoiceRoomSettingPanelItem.CollapseRoom -> {
                    dialog.dismiss()
                    onAction?.collapseRoom()
                }

                is VoiceRoomSettingPanelItem.ExitRoom -> {
                    dialog.dismiss()
                    onAction?.exitRoom()
                }
            }
        }
    }
}

data class VoiceInfoManageDialog(val settings: VoiceLiveChatRoom) : AnimatedDialog<IVoiceLiveAction>() {
    @Composable
    override fun Content(dialog: IDialog, onAction: IVoiceLiveAction?) {
        VoiceRoomInfoSetting(settings) { action ->
            when (action) {
                VoiceRoomAction.NavigateAdministrator -> {
                    onAction?.navigateToAdministrator()
                    dialog.dismiss()
                }

                VoiceRoomAction.NavigateBlackList -> {
                    onAction?.navigateToBlackList()
                    dialog.dismiss()
                }

                is VoiceRoomAction.UpdateRoomName -> {
                    onAction?.onRoomNameUpdate(action.newTitle, dialog)
                }

                is VoiceRoomAction.EditNotice -> {
                    onAction?.onEditNotice()
                }

                else -> {}
            }
        }
    }
}