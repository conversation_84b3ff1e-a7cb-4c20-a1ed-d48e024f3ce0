

package com.qyqy.cupid.ui.dialog

import android.Manifest
import android.content.Context
import android.os.Parcelable
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ElevatedButton
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.overseas.common.utils.AppPermissionResultLauncher
import com.overseas.common.utils.PermissionRequest
import com.qyqy.cupid.FaceKt
import com.qyqy.cupid.data.WithdrawList
import com.qyqy.cupid.model.WithdrawViewModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.widgets.click
import com.qyqy.cupid.widgets.composeClick
import com.qyqy.ucoo.R
import com.qyqy.ucoo.asComponentActivity
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.IconLoading
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.utils.OnClick
import com.qyqy.ucoo.utils.toast
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.parcelize.Parcelize

@Parcelize
data object WithdrawDialog : AnimatedDialog<IDialogAction>(), Parcelable {

    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        val viewModel = viewModel<WithdrawViewModel>()
        val withdrawWays = viewModel.withdrawWays.collectAsStateWithLifecycle()
        val dialogQueue = LocalDialogQueue.current

        val activity = LocalContext.current.asComponentActivity

        LaunchedEffect(key1 = Unit) {
            viewModel.withdrawEvent.onEach {
                when (it) {
                    is WithdrawViewModel.Event.RealAuth -> {
                        dialog.dismiss()
                        dialogQueue.pushCenterDialog { dialog, _ ->
                            val onStart = composeClick {
                                dialog.dismiss()
                                if (FaceKt.isDeviceX86()) {
                                    authTip(dialogQueue)
                                } else if (activity != null) {
                                    AppPermissionResultLauncher(activity).onlyOnceLaunch(PermissionRequest(Manifest.permission.CAMERA)) { allow ->
                                        if (allow) {
                                            startAuthTip(dialogQueue, activity, viewModel)
                                        } else {
                                            toastRes(R.string.cpd缺少相机权限)
                                        }
                                    }
                                }
                            }
                            ContentAlertDialog(
                                content = it.content,
                                startButton = DialogButton(it.cancelButton) {
                                    dialog.dismiss()
                                },
                                endButton = DialogButton(it.confirmButton, onStart)
                            )
                        }
                    }

                    is WithdrawViewModel.Event.Error -> {
                        toast(it.msg)
                        dialog.dismiss()
                    }
                }
            }.launchIn(this)

            viewModel.precheckWithdraw()
        }
        WithdrawContent(withdrawWays) {
            dialog.dismiss()
        }
    }

    private fun authTip(dialogQueue: DialogQueue<*>) {
        dialogQueue.pushCenterDialog { dialog, _ ->
            ContentAlertDialog(
                content = stringResource(id = R.string.cpd请使用手机app进行认证),
                endButton = DialogButton(stringResource(id = R.string.cpd我知道了)) {
                    dialog.dismiss()
                }
            )
        }
    }

    private fun startAuthTip(dialogQueue: DialogQueue<*>, context: Context, viewModel: WithdrawViewModel) {
        dialogQueue.pushCenterDialog { dialog, _ ->
            IconLoading()
            LaunchedEffect(key1 = Unit) {
                viewModel.startRealAuth(context, dialog)
            }
        }
    }
}

@Composable
private fun WithdrawContent(withdrawWays: State<WithdrawList>, onClose: OnClick = {}) {
    val controller = LocalAppNavController.current
    if (withdrawWays.value.data.isEmpty()) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(200.dp)
                .background(
                    color = Color.White,
                    shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)
                ),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            IconLoading()
        }
    } else {

        WithdrawWidget(withdrawWays.value,
            {
                onClose()
            },
            { action ->
                controller.navigateByLink(action.jumpLink)
                onClose()
            }
        )
    }
}

@Composable
private fun WithdrawWidget(
    typeBean: WithdrawList,
    onClose: () -> Unit = {},
    onConfirm: (WithdrawList.WithdrawType) -> Unit = {},
) {
    var selectedIdx by remember {
        mutableStateOf(0)
    }

    Column(
        modifier = Modifier
            .background(
                color = Color.White,
                shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)
            )
    ) {
        Box(modifier = Modifier.height(44.dp)) {
            Text(
                stringResource(id = R.string.cupid_income_request_withdraw),
                style = MaterialTheme.typography.titleMedium.copy(fontSize = 18.sp, color = colorResource(id = R.color.FF1D2129)),
                modifier = Modifier.align(Alignment.Center)
            )
            HorizontalDivider(color = colorResource(id = R.color.FFF0F0F0), modifier = Modifier.align(Alignment.BottomCenter))
            ComposeImage(
                model = R.drawable.ic_cpd_close_grey, modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 16.dp)
                    .size(20.dp)
                    .click {
                        onClose()
                    }
            )
        }
        typeBean.data.forEachIndexed { index, option ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .click {
                        selectedIdx = index
                    }
                    .height(64.dp)
                    .padding(horizontal = 16.dp), verticalAlignment = Alignment.CenterVertically
            ) {
                ComposeImage(model = option.icon, modifier = Modifier.size(40.dp))
                Spacer(modifier = Modifier.width(8.dp))
                Text(option.desc, modifier = Modifier.weight(1f))
                if (selectedIdx == index) {
                    ComposeImage(model = R.drawable.ic_cpd_checked)
                }
            }
            HorizontalDivider(modifier = Modifier.padding(horizontal = 16.dp), color = colorResource(id = R.color.FFF0F0F0))
        }
        Spacer(modifier = Modifier.height(20.dp))
        Text(
            typeBean.hint,
            modifier = Modifier.padding(horizontal = 16.dp), color = CpdColors.FF86909C,
            fontSize = 12.sp
        )
        Spacer(modifier = Modifier.height(20.dp))
        ElevatedButton(
            onClick = composeClick { onConfirm(typeBean.data[selectedIdx]) }, modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth()
                .height(44.dp),
            contentPadding = PaddingValues(horizontal = 8.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = colorResource(id = R.color.FFFF5E8B),
                contentColor = Color.White
            ),
            elevation = null
        ) {
            Text(stringResource(id = R.string.cpd_next_step), style = MaterialTheme.typography.titleSmall)
        }
    }
}

@Composable
@Preview(name = "申请提现dialog", showBackground = true)
private fun RequestWithdrawalWidgetPreview() {
    PreviewCupidTheme {
        WithdrawContent(
            remember {
                mutableStateOf(WithdrawList(listOf(), ""))
            }
        )
    }
}


