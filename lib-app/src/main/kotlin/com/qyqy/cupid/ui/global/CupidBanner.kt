

package com.qyqy.cupid.ui.global

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavBackStackEntry
import com.hjq.language.MultiLanguages
import com.overseas.common.utils.isPreviewOnCompose
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.home.HomeSubPage
import com.qyqy.cupid.widgets.AlignHorizontalContainer
import com.qyqy.cupid.widgets.click
import com.qyqy.cupid.widgets.webview.WebComposeView
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.CircleIndicator
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.LoopAdjustEffect
import com.qyqy.ucoo.compose.ui.LoopViewPagerEffect
import com.qyqy.ucoo.compose.ui.fixCurrentPage
import com.qyqy.ucoo.compose.ui.mapLoopViewPagerPage
import com.qyqy.ucoo.compose.ui.rememberViewPagerState
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.map.BannerInfo
import com.qyqy.ucoo.setting.ActivityPendant
import com.qyqy.ucoo.utils.LogUtil
import com.qyqy.ucoo.utils.OnClick
import com.qyqy.ucoo.utils.web.putValueByUrl
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

enum class ScreenLocation(val position: Int = 0) {
    HOME(102),//首页
    GOLD(103),//金币页
    INCOME(104),//收益页
    FAMILY_SQUARE(105),//家族广场
    MESSAGE_LIST(106),//消息列表
    CHAT_PAGE(107),//聊天页
    ROOM(108),//语音房
    FAMILY(109),//家族大厅
    ROOM_LIST(110),//语音房列表
    OTHER(0)
}

//<editor-fold desc="Banner">
@Composable
fun CupidBanner(
    modifier: Modifier = Modifier,
    list: List<BannerInfo>,
    clickCallback: OnClick = {}
) {
    val scope = rememberCoroutineScope()
    val visible = list.isNotEmpty()
    if (visible) {
        Box(
            modifier = modifier
                .fillMaxWidth(1f)
                .aspectRatio(343f / 76)
        ) {
            val realPageCount = list.size
            val state = rememberViewPagerState(initialPage = 0, pageCount = realPageCount)
            val cur by remember {
                derivedStateOf { state.fixCurrentPage }
            }
            LoopViewPagerEffect(pagerState = state, realCount = realPageCount)
            LoopAdjustEffect(realPageCount, state.pageCount, cur) {
                scope.launch {
                    state.scrollToPage(it)
                }
            }
            val navController = LocalAppNavController.current
            HorizontalPager(modifier = Modifier.fillMaxSize(1f), state = state) {
                val model = list.getOrNull(mapLoopViewPagerPage(it, realPageCount))
                    ?: return@HorizontalPager
                ComposeImage(model = model.picUrl, modifier = Modifier
                    .fillMaxSize(1f)
                    .clip(RoundedCornerShape(12.dp))
                    .clickable {
                        navController.navigateByLink(model.jumpLink, model.container)
                        clickCallback.invoke()
                    })
            }
            CircleIndicator(
                count = realPageCount,
                currentIndex = mapLoopViewPagerPage(cur, realPageCount),
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 8.dp)
            )
        }
    }
}

@Composable
fun BannerView(
    location: ScreenLocation,
    modifier: Modifier = Modifier,
    clickCallback: OnClick = {}
) {
    val banners = if (isPreviewOnCompose) {
        listOf<BannerInfo>()
    } else {
        val list by UIConfig.flowBannerList.collectAsStateWithLifecycle(initialValue = UIConfig.banners)
        val bannerList = remember {
            derivedStateOf { list.filter { it.support(location.position) } }
        }
        bannerList.value
    }
    CupidBanner(list = banners, modifier = modifier, clickCallback = clickCallback)
}
//</editor-fold>

//<editor-fold desc="Pendant">
fun NavBackStackEntry?.getLocation(): ScreenLocation {
    val router = this?.destination?.route
    LogUtil.d("current router: $router")
    if (router != null) {
        when {
            router.startsWith(CupidRouters.C2CChat) -> return ScreenLocation.CHAT_PAGE
            router.startsWith(CupidRouters.VOICE_LIVE_ROOM) -> return ScreenLocation.ROOM
            router.startsWith(CupidRouters.FAMILY_HOME) -> return ScreenLocation.FAMILY
            else -> {}
        }
    }
    return ScreenLocation.OTHER
}

fun Int.getHomePosition(): ScreenLocation {
    return when (this) {
        HomeSubPage.Main.t -> ScreenLocation.HOME
        HomeSubPage.Gold.t -> ScreenLocation.GOLD
        HomeSubPage.Income.t -> ScreenLocation.INCOME
        HomeSubPage.Message.t -> ScreenLocation.MESSAGE_LIST
        HomeSubPage.Family.t -> ScreenLocation.ROOM_LIST//家族大厅第一个tab变成语音房列表了
        else -> ScreenLocation.OTHER
    }
}

@Composable
fun CupidPendant(location: ScreenLocation, modifier: Modifier = Modifier) {
    val list by UIConfig.flowPendantList.collectAsStateWithLifecycle(initialValue = emptyList())
    val pendants by remember(location) {
        derivedStateOf {
            list.filter {
                it.name != "audioroom_operator_daily_task" &&
                        ((it.visiblePoi.contains(101) && location != ScreenLocation.OTHER) || it.visiblePoi.contains(
                            location.position
                        ))
            }.sortedBy { it.orderId }
        }
    }
    var visible by rememberSaveable {
        mutableStateOf(true)
    }

    val navController = LocalAppNavController.current

    AlignHorizontalContainer(
        visible = pendants.isNotEmpty() && visible,
        modifier = modifier,
        tag = "CupidPendant"
    ) {
        if (pendants.size == 1) {
            val item = pendants.getOrNull(0) ?: return@AlignHorizontalContainer
            CupidPendentItem(item = item, onClick = {
                navController.navigateByLink(item.jumpLink, item.container)
            }) {
                visible = false
            }
        } else {
            key(pendants) {
                val pagerState = rememberPagerState {
                    pendants.size
                }
                LaunchedEffect(key1 = pendants, pagerState.settledPage) {
                    delay(5000L)
                    if (pagerState.pageCount <= 0) return@LaunchedEffect
                    val nextPage = pagerState.settledPage.plus(1).rem(pagerState.pageCount)
                    if (nextPage == 0) {
                        pagerState.scrollToPage(0)
                    } else {
                        pagerState.animateScrollToPage(nextPage)
                    }
                }
                HorizontalPager(
                    state = pagerState,
                    modifier = Modifier.size(68.dp, 90.dp),
                    beyondViewportPageCount = 2
                ) {
                    val item = pendants[it]
                    CupidPendentItem(item = item, onClick = {
                        navController.navigateByLink(item.jumpLink, item.container)
                    }) {
                        visible = false
                    }
                }
            }
        }
    }
}

@Composable
fun CupidPendentItem(
    item: ActivityPendant,
    showClose: Boolean = true,
    onClick: OnClick,
    onClose: OnClick
) {
    when (item.type) {
        2 -> {
            //webview类型 icon为网页链接
            val url = remember(item) {
                item.icon.putValueByUrl(
                    "app-language",
                    MultiLanguages.getAppLanguage().toLanguageTag()
                )
            }
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(12.dp))
            ) {
                WebComposeView(
                    url = url, modifier = Modifier.fillMaxSize()
                )
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .noEffectClickable(onClick = onClick)
                )
                if (showClose) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_cpd_close_small),
                        contentDescription = "close",
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .padding(4.dp)
                            .size(16.dp)
                            .click(onClick = onClose)
                    )
                }
            }
        }

        else -> {
            Box(
                modifier = Modifier
                    .size(68.dp, 90.dp)
                    .clip(Shapes.corner12)
                    .click(onClick = onClick)
            ) {
                ComposeImage(
                    model = item.icon,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.FillHeight
                )
                if (showClose) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_cpd_close_small),
                        contentDescription = "close",
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .padding(4.dp)
                            .size(16.dp)
                            .click(onClick = onClose)
                    )
                }
            }
        }
    }
}
//</editor-fold>