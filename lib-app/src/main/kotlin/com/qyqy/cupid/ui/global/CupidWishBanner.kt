

package com.qyqy.cupid.ui.global

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.im.panel.gift.GiftPosition
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.IC2CAction
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.cupid.ui.dialog.DialogQueue
import com.qyqy.cupid.ui.profile.wish.RoomUserWishListModel
import com.qyqy.cupid.ui.profile.wish.RoomWishViewModel
import com.qyqy.cupid.ui.profile.wish.WishEntry
import com.qyqy.cupid.ui.profile.wish.WishListTableDialog
import com.qyqy.cupid.ui.profile.wish.WishPage
import com.qyqy.cupid.ui.profile.wish.WishRoomHostPage
import com.qyqy.cupid.ui.profile.wish.rememberC2CWishViewModel
import com.qyqy.cupid.ui.profile.wish.rememberRoomWishViewModel
import com.qyqy.cupid.widgets.AlignHorizontalContainer
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.compose.LaunchOnceEffect
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.sAppKV
import com.qyqy.ucoo.sUser
import kotlinx.coroutines.delay

@Composable
fun CupidC2CWishFloatWidget(
    targetId: String,
    dialogQueue: DialogQueue<IC2CAction>,
    modifier: Modifier = Modifier,
) {
    val wishViewModel = rememberC2CWishViewModel(targetId = targetId)
    val isBoy = sUser.isBoy
    val firstEntity = (if (isBoy) wishViewModel.targetWishEntries else wishViewModel.myWishEntries)
    val secondEntity = (if (isBoy) wishViewModel.myWishEntries else wishViewModel.targetWishEntries)
    C2CWishFloatWidgetContent(
        wishEntryLists = listOf(firstEntity, secondEntity),
        modifier = modifier,
        onAction = { isMine: Boolean, _ ->
            // 设置到指定位置
            wishViewModel.resetState(isMine)
            // 打开心愿单弹窗
            dialogQueue.push(WishListTableDialog(wishViewModel))
        }
    )
}

@Composable
private fun C2CWishFloatWidgetContent(
    wishEntryLists: List<List<WishEntry>>,
    modifier: Modifier = Modifier,
    onAction: (isMine: Boolean, user: WishEntry?) -> Unit = { _, _ -> },
) {
    var visible by rememberSaveable {
        mutableStateOf(true)
    }

    AlignHorizontalContainer(visible = visible, modifier = modifier, tag = "WishFloatWidgetContent") {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            val pagerState = rememberPagerState {
                wishEntryLists.size
            }

            Box(
                modifier = Modifier
                    .padding(5.dp)
                    .size(90.dp, 130.dp)
            ) {
                val isBoy = sUser.isBoy

                HorizontalPager(
                    state = pagerState,
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(Shapes.corner12)
                ) {
                    val wishEntry = wishEntryLists[it]
                    WishWidgetItem(
                        items = wishEntry,
                        isMe = (!isBoy && it == 0) || (isBoy && it != 0)
                    ) { isMe, wishEntry ->
                        onAction(isMe, wishEntry)
                    }
                }
                Image(
                    painter = painterResource(id = R.drawable.ic_cpd_close_small),
                    contentDescription = "close",
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .size(16.dp)
                        .offset(4.dp, -4.dp)
                        .click(onClick = { visible = false })
                )
            }
            Row(horizontalArrangement = Arrangement.spacedBy(4.dp)) {
                for (i in 0 until pagerState.pageCount) {
                    Box(
                        modifier = Modifier
                            .size(6.dp)
                            .background(
                                color = if (i == pagerState.settledPage) CpdColors.FFFF5E8B
                                else Color(0x4D000000), shape = CircleShape
                            )
                    )
                }
            }
            Spacer(modifier = Modifier.height(4.dp))
        }
    }
}

@Composable
private fun WishWidgetItem(
    items: List<WishEntry>,
    isMe: Boolean,
    click: (isMe: Boolean, wishEntry: WishEntry?) -> Unit,
) {
    val isEmpty = items.isEmpty()
    val pagerState = rememberPagerState {
        items.size
    }
    if (pagerState.pageCount > 1) {
        LaunchedEffect(key1 = items, pagerState.settledPage) {
            delay(5000L)
            val nextPage = pagerState.settledPage.plus(1).rem(pagerState.pageCount)
            if (nextPage == 0) {
                pagerState.scrollToPage(0)
            } else {
                pagerState.animateScrollToPage(nextPage)
            }
        }
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painter = painterResource(id = R.drawable.ic_cpd_wish_background),
                contentScale = ContentScale.FillWidth
            )
            .click {
                val wishEntry = if (pagerState.pageCount == 0) null else items[pagerState.settledPage]
                click(isMe, wishEntry)
            },
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.height(8.dp))
        ComposeImage(
            model = if (isMe) {
                R.drawable.ic_cpd_mine_wish
            } else {
                R.drawable.ic_cpd_oppo_wish
            },
            modifier = Modifier
                .padding(horizontal = 3.dp)
                .size(72.dp, 32.dp),
            contentScale = ContentScale.FillWidth,
        )

        if (isEmpty) {
            Text(
                text = stringResource(id = if (isMe) R.string.cpd_wish_mine_placeholder else R.string.cpd_wish_oppo_placeholder),
                modifier = Modifier.padding(horizontal = 4.dp, vertical = 12.dp),
                color = Color(0xFFF4EEFF),
                fontSize = 10.5.sp,
                lineHeight = 12.sp,
                fontWeight = FontWeight.SemiBold,
                textAlign = TextAlign.Center
            )
        } else {
            HorizontalPager(state = pagerState, userScrollEnabled = false) { index ->
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    val wishEntry = items[index]
                    Text(
                        wishEntry.gift.name,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp, horizontal = 8.dp)
                            .basicMarquee(),
                        color = Color(0xFFE3D4F8),
                        fontSize = 10.sp,
                        lineHeight = 10.sp,
                        textAlign = TextAlign.Center
                    )
                    Column(
                        modifier = Modifier
                            .padding(horizontal = 12.dp)
                            .fillMaxWidth()
                            .background(
                                brush = Brush.verticalGradient(
                                    listOf(Color(0xFFFF9CE9), Color.White)
                                ),
                                shape = RoundedCornerShape(4.dp)
                            )
                            .padding(horizontal = 4.dp, vertical = 2.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        ComposeImage(model = wishEntry.gift.icon, modifier = Modifier.size(40.dp))
                        AutoSizeText(
                            "${wishEntry.process}/${wishEntry.count}",
                            color = Color(0xFFFF2AD0),
                            maxLines = 1,
                            fontSize = 10.sp,
                            lineHeight = 10.sp,
                            modifier = Modifier.padding(top = 2.dp)
                        )
                    }
                }
            }
        }
    }
    Spacer(modifier = Modifier.height(8.dp))
}

@Composable
@Preview
private fun CupidC2CWishFloatWidgetPreview() {
    val wishJsons =
        "[{\"id\":23,\"gift\":{\"t\":4,\"id\":433,\"name\":\"ししざ\",\"price_type\":3,\"price\":3000,\"icon\":\"https://media.ucoofun.com/opsite/gift/icon/%E6%98%9F%E5%BA%A7%E7%8B%AE%E5%AD%90_RoWfQMv_O5b87kM_qrMJGh9.png\",\"effect_file\":\"https://media.ucoofun.com/opsite/gift/effect/%E6%98%9F%E5%BA%A7%E7%8B%AE%E5%AD%90_U4BRBdj_bxFj3Ai_MfelBdJ.mp4\",\"native_region\":1},\"count\":99,\"thanks\":\"唱一首歌\",\"process\":0},{\"id\":24,\"gift\":{\"t\":4,\"id\":429,\"name\":\"おひつじざ\",\"price_type\":3,\"price\":3000,\"icon\":\"https://media.ucoofun.com/opsite/gift/icon/%E6%98%9F%E5%BA%A7%E7%99%BD%E7%BE%8A_znCDrzd_rhgzn1H_BY7FcHn.png\",\"effect_file\":\"https://media.ucoofun.com/opsite/gift/effect/%E6%98%9F%E5%BA%A7%E7%99%BD%E7%BE%8A_PKQtcZo_mKp4ddZ_s9egvgi.mp4\",\"native_region\":1},\"count\":520,\"thanks\":\"发一张可爱表情照片\",\"process\":0},{\"id\":25,\"gift\":{\"t\":4,\"id\":429,\"name\":\"おひつじざ\",\"price_type\":3,\"price\":3000,\"icon\":\"https://media.ucoofun.com/opsite/gift/icon/%E6%98%9F%E5%BA%A7%E7%99%BD%E7%BE%8A_znCDrzd_rhgzn1H_BY7FcHn.png\",\"effect_file\":\"https://media.ucoofun.com/opsite/gift/effect/%E6%98%9F%E5%BA%A7%E7%99%BD%E7%BE%8A_PKQtcZo_mKp4ddZ_s9egvgi.mp4\",\"native_region\":1},\"count\":520,\"thanks\":\"发一张生活照\",\"process\":0}]"
    val wishList = sAppJson.decodeFromString<List<WishEntry>>(wishJsons)
    PreviewCupidTheme {
        C2CWishFloatWidgetContent(listOf(listOf(), wishList))
    }
}

@Composable
fun CupidRoomWishFloatWidget(
    roomId: Int,
    dialogQueue: DialogQueue<IVoiceLiveAction>,
    modifier: Modifier = Modifier,
) {
    val wishViewModel = rememberRoomWishViewModel(roomId = roomId)
    val items by remember {
        derivedStateOf {
            wishViewModel.userWishEntries.filter { it.list.isNullOrEmpty().not() }
        }
    }

    var showGuide by remember {
        mutableStateOf(sAppKV.getBoolean("show_room_wish_guide", true))
    }

    var visible by rememberSaveable {
        mutableStateOf(true)
    }

    AlignHorizontalContainer(visible = visible, modifier = modifier, tag = "RoomWishFloatWidgetContent") {
        Box {
            RoomWishFloatWidgetContent(items) {
                // 打开心愿单弹窗
                showGuide = false
                wishViewModel.navToPage(WishPage.Home)
                dialogQueue.push { _, onAction ->
                    WishRoomHostPage(
                        wishViewModel = wishViewModel,
                        dialogQueue = dialogQueue,
                        onSendGift = { user, gift ->
                            (onAction as? IVoiceLiveAction)?.showUserInfoPanel(user, GiftPosition(gift.rawGiftId))
                        }
                    ) {
                        (onAction as? IVoiceLiveAction)?.showUserInfoPanel(it)
                    }
                }
            }

            if (showGuide) {
                val transition = rememberInfiniteTransition(label = "active_anim")
                val fraction by transition.animateFloat(
                    initialValue = 0f, targetValue = 30f, animationSpec = infiniteRepeatable(
                        tween(200, easing = LinearEasing), repeatMode = RepeatMode.Reverse
                    ), label = "background"
                )

                LaunchOnceEffect {
                    sAppKV.putBoolean("show_room_wish_guide", false)
                }

                Box(
                    modifier = Modifier
                        .matchParentSize()
                        .background(Color(0x66000000), Shapes.corner12)
                ) {
                    ComposeImage(
                        model = R.drawable.cpd_winner_raise_handle,
                        modifier = Modifier
                            .padding(start = 32.dp, top = 28.dp)
                            .rotate(fraction)
                            .size(28.dp)
                    )

                    Image(
                        painter = painterResource(id = R.drawable.ic_cpd_room_wish_guide_text),
                        contentDescription = null,
                        modifier = Modifier
                            .padding(start = 4.dp, top = 60.dp)
                            .size(60.dp, 20.dp)
                    )
                }
            } else {
                Image(
                    painter = painterResource(id = R.drawable.ic_cpd_close_small),
                    contentDescription = "close",
                    modifier = Modifier
                        .padding(top = 2.dp, end = 2.dp)
                        .align(Alignment.TopEnd)
                        .size(15.dp)
                        .click(onClick = {
                            visible = false
                        })
                )
            }
        }
    }
}


@Composable
private fun RoomWishFloatWidgetContent(
    items: List<RoomUserWishListModel>,
    onClick: (model: RoomUserWishListModel?) -> Unit = { _ -> },
) {
    val isEmpty = items.isEmpty()
    val pagerState = rememberPagerState {
        items.size
    }
    if (pagerState.pageCount > 1) {
        LaunchedEffect(key1 = items.size, pagerState.settledPage) {
            delay(5000L)
            val nextPage = pagerState.settledPage.plus(1).rem(pagerState.pageCount)
            if (nextPage == 0) {
                pagerState.scrollToPage(0)
            } else {
                pagerState.animateScrollToPage(nextPage)
            }
        }
    }
    Column(
        modifier = Modifier
            .size(68.dp, 90.dp)
            .paint(
                painter = painterResource(id = R.drawable.ic_cpd_room_wish_background),
                contentScale = ContentScale.FillBounds
            )
            .click {
                onClick(items.getOrNull(pagerState.settledPage))
            },
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (isEmpty) {
            Text(
                text = stringResource(id = R.string.cpd暂无心愿),
                modifier = Modifier.padding(top = 45.dp, start = 3.dp, end = 3.dp),
                color = Color(0xFFF4EEFF),
                fontSize = 12.sp,
                fontWeight = FontWeight.SemiBold,
                textAlign = TextAlign.Center
            )
        } else {
            HorizontalPager(state = pagerState) { index ->
                val user = items[index].user!!
                val wishEntry = items[index].list!!.first()
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 34.dp),
                    contentAlignment = Alignment.TopCenter
                ) {
                    Column(
                        modifier = Modifier
                            .padding(top = 4.dp)
                            .size(48.dp)
                            .background(
                                brush = Brush.verticalGradient(listOf(Color(0xFFFF9CE9), Color.White)),
                                shape = RoundedCornerShape(4.dp)
                            )
                            .border(0.5.dp, colorResource(id = R.color.white_alpha_50), RoundedCornerShape(4.dp))
                            .padding(top = 4.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        ComposeImage(model = wishEntry.gift.icon, modifier = Modifier.size(28.dp))
                        AutoSizeText(
                            "${wishEntry.process}/${wishEntry.count}",
                            color = Color(0xFFFF2AD0),
                            maxLines = 1,
                            fontSize = 10.sp,
                            lineHeight = 10.sp,
                            modifier = Modifier.padding(top = 2.dp)
                        )
                    }

                    CircleComposeImage(
                        model = user.avatarUrl,
                        modifier = Modifier
                            .padding(start = 6.dp)
                            .align(Alignment.TopStart)
                            .size(18.dp)
                    )
                }
            }
        }
    }
}

@Composable
@Preview
private fun RoomWishFloatWidgetContentPreview() {
    PreviewCupidTheme {
        RoomWishFloatWidgetContent(RoomWishViewModel.Preview.userWishEntries.filter { it.list.isNullOrEmpty().not() })
    }
}