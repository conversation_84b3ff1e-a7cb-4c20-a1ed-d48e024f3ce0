package com.qyqy.cupid.ui.global

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.ucoo.asComponentActivity

@Stable
data class GlobalPendant(val key: String, val content: @Composable () -> Unit)

class GlobalPendantViewModel : ViewModel() {

    private val statePendants = mutableStateListOf<GlobalPendant>()

    val pendants: List<GlobalPendant>
        get() = statePendants

    fun add(p: GlobalPendant) {
        remove(p.key)
        statePendants.add(p)
    }

    fun remove(key: String) {
        statePendants.removeIf { it.key == key }
    }

}

@Composable
fun CurrentScreenPendant(key: String, content: @Composable () -> Unit) {
    val activity = LocalContext.current.asComponentActivity!!
    val vm = viewModel<GlobalPendantViewModel>(viewModelStoreOwner = activity)
    DisposableEffect(key1 = key) {
        vm.add(GlobalPendant(key, content))
        onDispose {
            vm.remove(key)
        }
    }
}