package com.qyqy.cupid.ui.home

import com.qyqy.cupid.data.users.AlbumHolder
import com.qyqy.cupid.data.users.IAlbumHolder
import com.qyqy.cupid.data.users.UserObject
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getOrNull
import com.qyqy.ucoo.http.parseValue
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement
import retrofit2.http.GET
import retrofit2.http.Query

interface CupidHomeApi {
    /**
     * 获取首页推荐列表
     * @param sameCity 0全部 1同城
     * @return
     */
    @GET("api/ucuser/v1/japan/index/user/recom")
    suspend fun getMainRecommendList(@Query("same_city") sameCity: Int = 0): ApiResponse<JsonObject>

    @GET("api/privatechat/v1/japan/audiochat/recommend")
    suspend fun getAudioChatList(): ApiResponse<JsonObject>
}

interface JsonObjectHolder {
    val jsonObject: JsonObject
}

class CupidHomeUser(override val jsonObject: JsonObject) :
    UserObject(
        jsonObject
    ),
    IAlbumHolder by AlbumHolder(jsonObject)

val CupidHomeUser.onlineStatus: Int
    get() = jsonObject.parseValue("online_status", 0)

val CupidHomeUser.career: String
    get() = jsonObject.parseValue("career", "")

val CupidHomeUser.locationLabel: String
    get() = jsonObject.parseValue("location_label", "")

class CupidHomeApiRepo(val api: CupidHomeApi) {
    suspend fun getMainRecommendList(sameCity: Int): Result<List<AppUser>> = runApiCatching(globalErrorHandleIntercepted = { true }) {
        api.getMainRecommendList(sameCity)
    }.map { value: JsonObject ->
        value["users"]?.let { sAppJson.decodeFromJsonElement<List<AppUser>>(it) }.orEmpty()
//        buildList {
//            // 1. 加入所有用户
//            addAll(value["users"]?.let { sAppJson.decodeFromJsonElement<List<AppUser>>(it) }.orEmpty())
//
//            // 2. 获取指定插入的位置
//            val positions = value["audioroom_position"]?.let { sAppJson.decodeFromJsonElement<List<Int>>(it) }.orEmpty().toMutableList()
//
//            // 3. 获取所有语音房推荐
//            val audioRooms = value["audioroom_users"]?.let { sAppJson.decodeFromJsonElement<List<AppUser>>(it) }.orEmpty()
//
//            // 4. 数据处理
//            if (positions.isEmpty()) {
//                addAll(0, audioRooms)
//            } else {
//                audioRooms.forEach { room ->
//                    val pos = Math.min((positions.firstOrNull() ?: (this.size - 1)), this.size - 1)
//                    add(pos, room)
//                    if (positions.isNotEmpty()) {
//                        positions.removeAt(0)
//                    }
//                }
//            }
//        }
    }

    suspend fun getAudioChatRecommendList(): Result<List<AppUser>> = runApiCatching {
        api.getAudioChatList()
    }.map { obj ->
        obj.getOrNull("users")?.let { sAppJson.decodeFromJsonElement<List<AppUser>>(it) }.orEmpty()
    }
}

val cupidHomeApi = CupidHomeApiRepo(createApi())
