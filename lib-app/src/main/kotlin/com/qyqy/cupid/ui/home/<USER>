package com.qyqy.cupid.ui.home

import android.content.Intent
import android.net.Uri
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.VectorConverter
import androidx.compose.animation.core.animateValue
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.profile.guard.GuardListViewModel
import com.qyqy.cupid.utils.ZipUtil
import com.qyqy.cupid.widgets.CpdButton
import com.qyqy.cupid.widgets.webview.WebComposeView
import com.qyqy.cupid.widgets.webview.WebDialog
import com.qyqy.ucoo.UCOOEnvironment

import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.setting.DebugActivity
import com.qyqy.ucoo.utils.LogUtils
import com.qyqy.ucoo.utils.download.Downloader
import com.qyqy.ucoo.utils.download.Request
import kotlinx.coroutines.launch
import java.io.File


@Composable
fun TestPage(modifier: Modifier = Modifier) {
    val nav = LocalAppNavController.current
    val context = LocalContext.current

    val tran = rememberInfiniteTransition(label = "color")
    var end by remember {
        mutableStateOf(Offset.Zero)
    }
    val v by tran.animateValue(
        initialValue = Offset.Zero,
        targetValue = end,
        typeConverter = Offset.VectorConverter,
        animationSpec = infiniteRepeatable(TweenSpec(durationMillis = 2000)),
        label = ""
    )
    val endV by remember {
        derivedStateOf {
            v.plus(Offset(12.sp.value, 24.sp.value))
        }
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .systemBarsPadding()
            .padding(16.dp)
    ) {
        Text(
            text = "幼儿园班花",
            modifier = Modifier.onGloballyPositioned {
                end = Offset(it.size.width.toFloat(), 0f)
            },
            style = TextStyle(
                fontSize = 24.sp,
                brush = Brush.linearGradient(
                    start = v,
                    end = endV,
                    colors = listOf(Color(0xFFDD7617), Color(0xFFFFF09F),Color(0xFFDD7617))
                )
            )
        )
        val scope = rememberCoroutineScope()
        CpdButton(text = "Download dist") {
            scope.launch {
                var dir = File(context.filesDir, "games")
                dir.deleteRecursively()
                dir.mkdirs()
                LogUtils.d("test", dir.absolutePath)
                val url = "http://***********:5500/out/dist.zip"
                val fileName = Uri.parse(url).lastPathSegment ?: "dist.zip"
                val destFile = File(dir, fileName)
                if (destFile.exists()) {
                    destFile.delete()
                }
                destFile.createNewFile()
                val result = Downloader.create(dir.absolutePath)
                    .execute(
                        Request.Builder(url)
                            .apply {
                                fileName(fileName)
                            }.build()
                    )
                if (result.isSuccess) {
                    val f = result.getOrNull()
                    LogUtils.e("test", "download success=>${f?.absolutePath}}")
                    //解压f
//                    ZipUtil.upZipFile(f, "${dir.absolutePath}${File.separator}")
                    ZipUtil.upZipFile(f, context.filesDir.absolutePath)
                } else {
                    LogUtils.e("test", "download error:${result.exceptionOrNull()?.message.orEmpty()}")
                }
            }
        }
        CpdButton(text = "查看用户信息") {
            context.startActivity(Intent(context, DebugActivity::class.java))
        }
        CpdButton(text = "unzip") {
            scope.launch {
                val file = File(context.filesDir, "dist.zip")
                ZipUtil.upZipFile(file, context.filesDir.absolutePath)
            }
        }

        var visible by remember {
            mutableStateOf(false)
        }
        if (visible) {

            Dialog(onDismissRequest = { visible = false }) {
                WebComposeView(url = "${UCOOEnvironment.getDebugEnv()?.API_HOST}/h5/japan/gold_racing")
//                WebComposeView(
//                    url = "https://ucoogame.com/racing/dist/src/packages/racing/index.html?_pkg=cupid",
//                )
            }
        }
        val dq = LocalDialogQueue.current
        CpdButton(text = "Open Dist index.html") {
//            val act = ActivityLifecycle.startTopActivity
//            if (act is BaseActivity) {
//                val f = JsBridgeWebFragment.newInstance("https://${Const.DOMAIN_UCOO_GAME}/src/packages/racing/index.html?_pkg=cupid")
//                act.showFragment(f)
//            }
//            val url = "https://${Const.DOMAIN_UCOO_GAME}/racing/index.html"
            val url = "${UCOOEnvironment.getDebugEnv()?.API_HOST}/h5/japan/gold_racing"
            dq.push(
                WebDialog(
                    url,
                    modifier = Modifier.fillMaxSize()
                )
            )
//            nav.navigateByLink("http://app.ucoofun.com/dist/index.html")
//            val f = File(context.filesDir, "games/dist/index.html")
//            nav.navigateByLink("file://${f.absolutePath}")
        }
        CpdButton(text = "开通会员") {
            nav.navigate(CupidRouters.MEMBERSHIP_ACTIVE_PAGE)
        }

        CpdButton(
            text = "Dress Up"
        ) {
            nav.navigate(CupidRouters.DRESS_UP)
        }

        CpdButton(
            text = "My Guard"
        ) {
            nav.navigate(CupidRouters.MY_GUARD, mapOf("type" to GuardListViewModel.GuardType.OUT))
        }

        CpdButton(
            text = "Family Rank"
        ) {
            nav.navigate(CupidRouters.FAMILY_RANK)
        }

        CpdButton(text = "等级说明") {
            nav.navigateWeb("${Const.Url.baseURL}/h5/ja/level")
        }

        CpdButton(text = "语音匹配") {
            val url =
                "ucoo://page/web_frame?info={\"target_url\":\"https%3A%2F%2Fapi.test.ucoofun.com%2Fh5%2Fjapan%2Faudio_match_doc\",\"gravity\":\"center\",\"width\":270,\"height\":290,\"radius\":{\"left_top\":8,\"right_top\":8,\"right_bottom\":8,\"left_bottom\":8},\"cancelable\":true}"
            nav.navigateByLink(url)
        }

        val link = "https://api.test.ucoofun.com/h5/japan/gold_racing"

        CpdButton(text = "赛车") {
            nav.navigateWeb(link)
        }

        CpdButton(text = "Bank") {
            nav.navigateByLink("${Const.Url.baseURL}/h5/japan/withdraw/create_account?type=1")
        }

        CpdButton(text = "Paypay") {
            nav.navigateByLink("${Const.Url.baseURL}/h5/japan/withdraw/create_account?type=2")
        }
    }
}