@file:OptIn(
    ExperimentalMaterial3Api::class,
    
    ExperimentalSharedTransitionApi::class
)

package com.qyqy.cupid.ui.home

import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.apis.PhrasesApi
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.IHomeAction
import com.qyqy.cupid.ui.NavListener
import com.qyqy.cupid.ui.global.BannerView
import com.qyqy.cupid.ui.global.ScreenLocation
import com.qyqy.cupid.ui.home.audiochat.AudioChatListPage
import com.qyqy.cupid.ui.home.message.C2CChatConst
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.cupid.ui.relations.family.AudioWaves
import com.qyqy.cupid.utils.eventbus.EventBusListener
import com.qyqy.cupid.utils.getVoiceLiveHelper
import com.qyqy.cupid.widgets.SimpleTabLayout
import com.qyqy.cupid.widgets.click
import com.qyqy.cupid.widgets.composeClick
import com.qyqy.ucoo.DataPoint
import com.qyqy.ucoo.DataTrace
import com.qyqy.ucoo.R
import com.qyqy.ucoo.ReportExposureCompose
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.account.Album
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.getSampleImageUrl
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.state.EmptyView
import com.qyqy.ucoo.compose.state.ErrorView
import com.qyqy.ucoo.compose.state.PullRefreshBox
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AgeGender
import com.qyqy.ucoo.compose.ui.AnimatedDialog
import com.qyqy.ucoo.compose.ui.AnyPopDialogProperties
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.DirectionState
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.fixCurrentPage
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.http.ApiException
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getBoolOrNull
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.self
import com.qyqy.ucoo.utils.ComposeContent
import com.qyqy.ucoo.utils.OnClick
import eu.bambooapps.material3.pullrefresh.PullRefreshIndicatorDefaults
import kotlinx.coroutines.launch


class MainViewModel : ViewModel() {
    private val _isRefreshing: MutableState<Boolean> = mutableStateOf(false)
    val isRefreshing: State<Boolean> = _isRefreshing
    private val _state: MutableState<Result<List<AppUser>>> =
        mutableStateOf(Result.success(emptyList()))
    val state: State<Result<List<AppUser>>> = _state

    var onlyShowSameCity = 0
        private set

    fun toggleRegionType(type: Int) {
        onlyShowSameCity = type
        loadData()
    }

    init {
        loadData()
    }

    fun loadData() {
        viewModelScope.launch {
            _isRefreshing.value = true
            cupidHomeApi.getMainRecommendList(onlyShowSameCity)
                .onSuccess {
                    _state.value = Result.success(it)
                }
                .onFailure {
                    (it as? ApiException)?.let {

                        if (onlyShowSameCity == 1 && it.code == -12) {
                            AppLinkManager.controller?.navigate(
                                CupidRouters.MEMBERSHIP_ACTIVE_PAGE,
                                mapOf("from" to "jp_same_city_need_vip")
                            )
                        }

                        if (onlyShowSameCity == 1 && it.code < 0) {
                            // 使用同城用户功能请求时失败, 且code被解析, 说明不是网络错误, 此时不该消除数据. 直接重置类型即可
                            onlyShowSameCity = 0
                        } else {
                            _state.value = Result.failure(it)
                        }
                    }
                }
                .toastError()
            _isRefreshing.value = false
        }
    }

    private val api by lazy {
        createApi<PhrasesApi>()
    }

    fun sendPhrases(id: Int, onResult: (Boolean?) -> Unit) {
        viewModelScope.launch {
            runApiCatching {
                api.sendPhrases(mapOf("target_userid" to id))
            }.onSuccess {
                onResult(it.getBoolOrNull("has_sent_prologue"))
            }.onFailure {
                onResult(null)
            }
        }
    }
}


@Composable
fun MainPage(onAction: IHomeAction, modifier: Modifier = Modifier) {
    val vm: MainViewModel = viewModel()

    var showFilterBox by remember {
        mutableStateOf(false)
    }
    val config by UIConfig.configFlow.collectAsStateWithLifecycle()
    val tabs by remember {
        derivedStateOf { config.getOrNull()?.indexTabs.orEmpty() }
    }
    val scope = rememberCoroutineScope()
    val pagerState = rememberPagerState {
        tabs.size
    }
    val titles = remember(tabs) {
        return@remember tabs.map { it.name }
    }

    val listener = remember(tabs, scope) {
        object : NavListener(HOME_SUB_NAV) {
            override fun handleNav(route: String): Boolean {
                val index = tabs.indexOfFirst { it.t.toString() == route }
                if (index != -1) {
                    scope.launch {
                        pagerState.scrollToPage(index)
                    }
                    return true
                }
                return false
            }
        }
    }
    EventBusListener(listener = listener)

    Box(modifier = Modifier.fillMaxWidth()) {
        Column(
            modifier = modifier
                .fillMaxSize()
                .paint(
                    painter = painterResource(id = R.drawable.cupid_header_home),
                    contentScale = ContentScale.FillWidth,
                    alignment = Alignment.TopCenter
                )
                .statusBarsPadding()
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 20.dp, bottom = 10.dp, start = 16.dp, end = 16.dp)
                    .heightIn(min = 30.dp),
                contentAlignment = Alignment.BottomStart
            ) {
                SimpleTabLayout(
                    pagerState = pagerState,
                    titles = titles,
                    divider = { Spacer(modifier = Modifier.width(24.dp)) },
                    indicator = {
                        if (tabs.size > 1) {
                            Box(
                                modifier = Modifier
                                    .size(16.dp, 2.dp)
                                    .align(Alignment.BottomCenter)
                                    .background(color = Color(0xFF1D2129), shape = Shapes.chip)
                            )
                        }
                    }, onTabClick = {
                        scope.launch {
                            pagerState.animateScrollToPage(it)
                        }
                    }) { text, selected ->
                    Text(
                        text = text, fontSize = 18.sp, style =
                        if (!selected) TextStyle(color = Color(0xFF86909C))
                        else TextStyle(color = Color(0xFF1D2129), fontWeight = FontWeight.Bold),
                        modifier = Modifier.padding(vertical = 6.dp),
                        textAlign = TextAlign.Start
                    )
                }

                if (tabs.isNotEmpty() && tabs[pagerState.fixCurrentPage].t == 101) {
                    ComposeImage(
                        model = R.drawable.ic_cpd_filter,
                        modifier = Modifier
                            .align(Alignment.BottomEnd)
                            .size(24.dp)
                            .click {
                                showFilterBox = true
                            }
                    )
                }
            }

            HorizontalPager(state = pagerState, modifier = Modifier.fillMaxSize()) { pageIndex ->
                val tab = tabs[pageIndex]
                when (tab.t) {
                    101 -> ReportExposureCompose(exposureName = TracePoints.VISIT_HOME_RECOMMEND_LIST) {
                        RecommendUsers(onAction = onAction, vm = vm)
                    }

                    102 -> ReportExposureCompose(exposureName = TracePoints.VISIT_HOME_VOICE_CALL) {
                        AudioChatListPage()
                    }

                    else -> {}
                }
            }
        }

        if (showFilterBox) {
            var isShowFilterBoxActiveClose by remember { mutableStateOf(false) }
            AnimatedDialog(
                isActiveClose = isShowFilterBoxActiveClose,
                onDismiss = { showFilterBox = false },
                properties = AnyPopDialogProperties(direction = DirectionState.TOP)
            ) {
                FilterItemWidget(vm.onlyShowSameCity) {
                    isShowFilterBoxActiveClose = true
                    vm.toggleRegionType(it)
                }
            }
        }

        ReportExposureCompose(
            exposureName = DataTrace.Exposure.SubHome.首页访问,
            reExposureMode = false
        )
    }
}

@Composable
fun RecommendUsers(onAction: IHomeAction, vm: MainViewModel) {
    val refreshing by vm.isRefreshing
    val result by vm.state
    val voiceHelper = getVoiceLiveHelper()

    val loadingWidget = LocalContentLoading.current
    val scope = rememberCoroutineScope()

    Column(modifier = Modifier.fillMaxSize()) {
        BannerView(
            location = ScreenLocation.HOME,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .padding(bottom = 12.dp),
            clickCallback = {
                Analytics.reportClickEvent(TracePoints.CLICK_HOME_RECOMMEND_BANNER)
            }
        )

        PullRefreshBox(
            isRefreshing = refreshing, onRefresh = { vm.loadData() },
            colors = PullRefreshIndicatorDefaults.colors(
                contentColor = MaterialTheme.colorScheme.primary,
                containerColor = Color.White
            )
        ) {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp),
            ) {
                if (result.isSuccess) {
                    itemsIndexed(
                        result.getOrThrow(),
                        key = { index, item -> "RecommendUsers-${index}-${item.id}" }) { index, item ->
                        MainPageItem(
                            user = item,
                            modifier = Modifier.padding(12.dp),
                            onAvatarClick = {
                                onAction.navigateToProfile(item.id)
                            },
                            onCellClick = {
                                Analytics.appReportEvent(DataPoint.clickBody(DataTrace.Click.SubHome.首页CELL点击))
                                onAction.onNavigateTo(
                                    CupidRouters.C2CChat,
                                    arguments = mapOf("user" to item)
                                )
                            },
                            onHi = {
                                Analytics.appReportEvent(DataPoint.clickBody(DataTrace.Click.SubHome.首页SAY_HI点击))
                                //todo 如果用户已设置了开场白,则随机发送一条开场白,不然就直接进私聊
                                if (!self.isBoy) {
                                    loadingWidget.value = true
                                    vm.sendPhrases(item.userId) {
                                        loadingWidget.value = false
//                                    if (it != null && !it) {
//                                    }
                                        onAction.onNavigateTo(
                                            CupidRouters.C2CChat,
                                            arguments = mapOf("user" to item)
                                        )
                                    }
                                } else {
                                    val map = buildMap {
                                        put("user", item)
                                        if (self.isBoy) {
                                            put("action", C2CChatConst.ACTION_SAY_HI.toString())
                                        }
                                    }
                                    onAction.onNavigateTo(CupidRouters.C2CChat, arguments = map)
                                }
                            },
                            onAudioRoomClick = {
                                val roomId = item.room?.roomId
                                if (roomId != null) {
                                    voiceHelper.joinVoiceLiveRoom(
                                        roomId,
                                        from = "jp_ar_intro_click"
                                    )
                                }
                            })
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                    item { Spacer(modifier = Modifier.height(50.dp)) }
                }
            }
            if (result.isFailure) {
                ErrorView(result.exceptionOrNull(), Modifier.fillMaxSize())
            } else if (result.getOrNull()
                    .isNullOrEmpty() && !vm.isRefreshing.value && vm.onlyShowSameCity == 1
            ) {
                EmptyView(
                    modifier = Modifier.fillMaxSize(),
                    textRes = R.string.cpd_home_local_empty,
                    iconRes = R.drawable.ic_cpd_empty
                )
            }
        }
    }
}

@Composable
fun MainPageItem(
    user: User,
    modifier: Modifier = Modifier,
    onAvatarClick: OnClick = {},
    onCellClick: OnClick = {},
    onAudioRoomClick: OnClick = {},
    onHi: () -> Unit = {},
    action: ComposeContent = {
        if ((user as? AppUser)?.room != null) {
            ReportExposureCompose(exposureName = "jp_ar_intro_expose")
            Row(
                modifier = Modifier
                    .clickable(onClick = onAudioRoomClick)
                    .padding(10.dp, 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                AudioWaves()
                Spacer(modifier = Modifier.width(2.dp))
                Text(
                    stringResource(id = R.string.cpd_home_in_voice),
                    color = CpdColors.FFFF5E8B,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        } else {
            Row(
                modifier = Modifier
                    .clip(RoundedCornerShape(50))
                    .click { onHi() }
                    .border(1.dp, MaterialTheme.colorScheme.primary, RoundedCornerShape(50))
                    .padding(10.dp, 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
//                if (self.isBoy) {
                Image(
                    painter = painterResource(id = R.drawable.cupid_ic_heart),
                    contentDescription = "",
                    modifier = Modifier.size(12.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = "Hi",
                    style = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.Bold),
                    fontStyle = FontStyle.Italic,
                    color = MaterialTheme.colorScheme.primary,
                )
//                } else {
//                    Text(
//                        stringResource(R.string.cupid_main_hi),
//                        fontSize = 14.sp,
//                        color = MaterialTheme.colorScheme.primary,
//                    )
//                }
            }
        }
    },
) {
    Surface(shape = Shapes.corner12, modifier = Modifier.clickable(onClick = onCellClick)) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .click {
                    val u = user as? AppUser
                    if (u?.room != null) {
                        onAudioRoomClick()
                    } else {
                        onCellClick()
                    }
                }
                .then(modifier)
        ) {
            Box(modifier = Modifier.size(48.dp)) {
                ComposeImage(
                    model = user.avatarUrl, modifier = Modifier
                        .fillMaxSize()
                        .clip(CircleShape)
                        .clickable(onClick = onAvatarClick)
                )

                if (user.onlineStatus == 0) {
                    Spacer(
                        modifier = Modifier
                            .size(10.dp)
                            .clip(CircleShape)
                            .background(Color.White)
                            .padding(2.dp)
                            .clip(CircleShape)
                            .background(Color(0xFF18E046))
                            .align(Alignment.BottomEnd)
                    )
                }
            }
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 8.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Start
                        ) {
                            var over by remember {
                                mutableStateOf(true)
                            }
                            Text(
                                modifier = if (over) Modifier.weight(1f) else Modifier,
                                text = user.nickname,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium,
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis,
                                onTextLayout = { la ->
                                    over = la.hasVisualOverflow
                                }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            AgeGender(age = user.age, isBoy = user.isBoy)
                        }
                        Spacer(modifier = Modifier.height(4.dp))
                        //标签
                        val tags = remember(user.locationLabel, user.height, user.career) {
                            listOf(
                                user.locationLabel,
                                if (user.height > 0) "${user.height}cm" else "",
                                user.career
                            ).filter { it.isNotEmpty() }
                                .joinToString(separator = " | ")
                        }
                        if (tags.isNotEmpty()) {
                            Text(
                                text = tags,
                                fontSize = 12.sp,
                                lineHeight = 14.sp,
                                color = Color(0xFF4E5969),
                            )
                        }
                    }
                    Spacer(modifier = Modifier.width(4.dp))

                    action()
                }

                Spacer(modifier = Modifier.height(2.dp))
                //简介
                if (user.shortIntro.isNotEmpty()) {
                    Text(
                        modifier = Modifier
                            .fillMaxWidth(1f)
                            .padding(end = 48.dp),
                        text = user.shortIntro,
                        fontSize = 12.sp,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        color = Color(0xFF86909C)
                    )
                }
                val images = remember(user.albumList) {
                    user.albumList.reversed().take(3)
                }
                if (images.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(10.dp))
                    Row(modifier = Modifier.fillMaxWidth()) {
                        images.forEach { item ->
                            ComposeImage(
                                model = item.url, modifier = Modifier
                                    .size(72.dp)
                                    .clip(RoundedCornerShape(12.dp))
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                        }
                    }
                }
            }


        }
    }
}

@Composable
fun FilterItemWidget(selectIdx: Int = 0, onSelected: (selectIdx: Int) -> Unit = {}) {
    var current by remember {
        mutableStateOf(selectIdx)
    }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White, RoundedCornerShape(bottomStart = 12.dp, bottomEnd = 12.dp))
            .padding(horizontal = 16.dp)
            .padding(bottom = 16.dp)
            .statusBarsPadding()
    ) {
        Text(
            stringResource(id = R.string.cpd_home_filter_title),
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(44.dp),
            textAlign = TextAlign.Center,
            color = CpdColors.FF1D2129,
            fontSize = 16.sp,
            lineHeight = 44.sp,
            fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.height(20.dp))
        Text(
            stringResource(id = R.string.cpd_home_filter_subtitle),
            color = CpdColors.FF1D2129,
            fontSize = 14.sp,
            lineHeight = 14.sp
        )
        Spacer(modifier = Modifier.height(16.dp))
        Row(verticalAlignment = Alignment.Bottom) {
            Text(
                stringResource(id = R.string.cpd_home_filter_all_city),
                modifier = Modifier
                    .weight(1f)
                    .background(
                        color = if (current == 0) CpdColors.FFFFCFDC else CpdColors.FFF1F2F3,
                        shape = CircleShape
                    )
                    .clip(CircleShape)
                    .height(32.dp)
                    .click { current = 0 },
                textAlign = TextAlign.Center,
                color = if (current == 0) CpdColors.FFFF5E8B else CpdColors.FF86909C,
                fontSize = 12.sp,
                lineHeight = 32.sp
            )
            Spacer(modifier = Modifier.width(4.dp))
            Box(
                modifier = Modifier
                    .weight(1f)
                    .height(32.dp)
                    .click { current = 1 }
            ) {
                Text(
                    stringResource(id = R.string.cpd_home_filter_local_city),
                    modifier = Modifier
                        .background(
                            color = if (current == 1) CpdColors.FFFFCFDC else CpdColors.FFF1F2F3,
                            shape = CircleShape
                        )
                        .fillMaxWidth()
                        .height(32.dp),
                    textAlign = TextAlign.Center,
                    color = if (current == 1) CpdColors.FFFF5E8B else CpdColors.FF86909C,
                    fontSize = 12.sp,
                    lineHeight = 32.sp
                )
                ComposeImage(
                    model = R.drawable.ic_cpd_vip, modifier = Modifier
                        .align(Alignment.TopEnd)
                        .offset(y = -16.dp)
                )
            }
        }
        AppButton(text = stringResource(id = R.string.cpd_home_filter_confirm),
            color = Color.White,
            background = CpdColors.FFFF5E8B,
            contentPadding = PaddingValues(horizontal = 12.dp),
            modifier = Modifier
                .padding(top = 24.dp)
                .fillMaxWidth()
                .height(48.dp),
            onClick = composeClick {
                onSelected(current)
            })
    }
}

@Preview
@Composable
fun MainItemPreview(modifier: Modifier = Modifier) {
    PreviewCupidTheme {
        val albums = listOf(
            Album(url = getSampleImageUrl(random = 1)),
            Album(url = getSampleImageUrl(random = 2)),
            Album(url = getSampleImageUrl(random = 3))
        )
        MainPageItem(
            user = userForPreview.copy(
                nickname = "阿哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈啊",
                albumList = albums,
                shortIntro = "hello intro阿哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈啊hello intro阿哈哈哈哈哈哈",
                height = 180,

                )
        )
    }
}

@Preview(showBackground = true)
@Composable
fun FilterItemPreview() {
    FilterItemWidget()
}