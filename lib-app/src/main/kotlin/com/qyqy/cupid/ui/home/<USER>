package com.qyqy.cupid.ui.home

import android.os.SystemClock
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.ui.CupidMainActivity
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.CupidViewModel
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.compose.enableClick
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.ui.AppBasicTextField
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.home.search.RoomSearched
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.tribe.bean.BriefUser
import com.qyqy.ucoo.user.UserManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.buffer
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive

private data class SearchContent(
    val text: String,
    val time: Long = SystemClock.elapsedRealtime(),
)

@OptIn(FlowPreview::class,  ExperimentalCoroutinesApi::class)
@Composable
fun SearchPage() {
    val activity = LocalContext.current as CupidMainActivity
    val viewModel = viewModel<CupidViewModel>(viewModelStoreOwner = activity)
    val voiceLiveHelper = viewModel.voiceLiveHelper
    val navController = LocalAppNavController.current

    val searchFlow = remember {
        MutableStateFlow(SearchContent("", 0))
    }

    val searchEventFlow = remember {
        MutableStateFlow(SearchContent("", 0))
    }

    val searchText by searchFlow.collectAsStateWithLifecycle()

    var list by remember {
        mutableStateOf<List<Any>>(emptyList())
    }

    val scope = rememberCoroutineScope()

    LaunchedEffect(key1 = searchFlow) {
        combine(searchFlow.debounce(500), searchEventFlow) { auto, event ->
            if (auto.time >= event.time) {
                auto
            } else {
                event
            }
        }.distinctUntilChanged()
            .mapLatest {
                val text = it.text
                if (text.isEmpty()) {
                    list = emptyList()
                } else {
                    runApiCatching<JsonObject> {
                        UserManager.userRepository.userApi.searchUserAndRoom(text)
                    }.onSuccess { json ->
                        list = parseResult(json["search_info_list"]?.jsonArray)
                        if (list.isEmpty()) {
                            toast(activity.getString(R.string.cpd_id_not_exist))
                        }
                    }.onFailure {
                        it.toast()
                        list = emptyList()
                    }
                }
            }
            .flowOn(Dispatchers.Default)
            .buffer(0)
            .collect()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .enableClick()
            .background(Color.White)
            .statusBarsPadding()
            .navigationBarsPadding()
            .imePadding()
            .padding(16.dp)
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Row(
                modifier = Modifier
                    .padding(end = 8.dp)
                    .weight(1f)
                    .height(36.dp)
                    .background(Color(0xFFF1F2F3), CircleShape),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Spacer(modifier = Modifier.width(8.dp))
                Icon(
                    painter = painterResource(id = R.drawable.ic_search),
                    contentDescription = null,
                    tint = Color(0xFF86909C)
                )
                Spacer(modifier = Modifier.width(8.dp))
                val pattern = remember { Regex("^\\d+\$") }
                val focusRequester = remember { FocusRequester() } //焦点

                LaunchedEffect(Unit) {
                    delay(150)
                    focusRequester.requestFocus()
                }

                AppBasicTextField(
                    value = searchText.text,
                    onValueChange = {
                        if (it.isEmpty() || it.matches(pattern)) {
                            searchFlow.value = SearchContent(it)
                        }
                    },
                    modifier = Modifier.focusRequester(focusRequester),
                    hintValue = stringResource(id = R.string.cpd请输入搜索内容),
                    hintStyle = TextStyle(color = Color(0xFF86909C), fontSize = 12.sp),
                    textStyle = TextStyle(color = Color(0xFF1D2129), fontSize = 14.sp),
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number, imeAction = ImeAction.Search),
                    keyboardActions = KeyboardActions(
                        onSearch = {
                            scope.launch {
                                searchEventFlow.emit(searchText.copy(time = SystemClock.elapsedRealtime()))
                            }
                        }
                    )
                )
                Spacer(modifier = Modifier.width(8.dp))
            }
            Text(
                text = stringResource(id = R.string.cpd_cancel),
                modifier = Modifier.clickable {
                    navController.popBackStack()
                },
                fontSize = 14.sp,
                color = Color(0xFF86909C)
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        LazyColumn(verticalArrangement = Arrangement.spacedBy(16.dp)) {
            items(list) {
                val title: String
                val avatar: String
                val id: String
                val desc: String
                if (it is BriefUser) {
                    title = it.nickname
                    avatar = it.avatarUrl
                    id = stringResource(R.string.cpd_search_id, it.publicId)
                    desc = stringResource(R.string.cpd用户搜索结果)
                } else {
                    it as RoomSearched
                    title = it.title
                    avatar = it.avatarUrl
                    id = stringResource(R.string.cpd_search_id, it.publicId)
                    desc = stringResource(R.string.cpd房间搜索结果)
                }
                Row(
                    modifier = Modifier.noEffectClickable {
                        if (it is BriefUser) {
                            if (it.isSelf) {
                                return@noEffectClickable
                            }
                            navController.popBackStack()
                            navController.navigate(CupidRouters.C2CChat, mapOf("user" to it.toAppUser()))
                        } else {
                            it as RoomSearched
                            navController.popBackStack()
                            voiceLiveHelper.joinVoiceLiveRoom(it.id)
                        }
                    },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    CircleComposeImage(model = avatar, modifier = Modifier.size(56.dp))
                    Spacer(modifier = Modifier.width(8.dp))
                    Column {
                        Text(text = title, fontSize = 14.sp, color = Color(0xFF1D2129), fontWeight = FontWeight.Medium)
                        Text(text = id, fontSize = 12.sp, color = Color(0xFF86909C))
                    }
                    Spacer(modifier = Modifier.weight(1f))
                    Text(text = desc, fontSize = 12.sp, color = Color(0xFFFF5E8B))
                }
            }
        }
    }
}

private fun parseResult(array: JsonArray?): List<Any> {
    val list = mutableListOf<Any>()
    array ?: return list
    for (obj in array) {
        val jsonObject = obj.jsonObject
        val type = jsonObject["type"]?.jsonPrimitive?.content
        if (type == null) {
            continue
        } else {
            when (type) {
                "user" ->
                    jsonObject["user"]?.let {
                        sAppJson.decodeFromJsonElement<BriefUser>(it)
                    }?.also {
                        list.add(it)
                    }

                "room" ->
                    jsonObject["room"]?.let {
                        sAppJson.decodeFromJsonElement<RoomSearched>(it)
                    }?.also {
                        list.add(it)
                    }
            }
        }
    }
    return list
}

@Preview
@Composable
private fun PreviewSearchPage() {
    SearchPage()
}