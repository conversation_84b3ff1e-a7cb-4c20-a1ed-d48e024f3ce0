package com.qyqy.cupid.ui.home

import androidx.lifecycle.ViewModel
import com.qyqy.ucoo.AppInfo
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.presentation.sign_tasks.SignTaskApi
import com.qyqy.ucoo.compose.presentation.sign_tasks.TaskSeries
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.sUserKV
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement
import java.util.Calendar
import java.util.Date
import kotlin.time.DurationUnit
import kotlin.time.toDuration

/**
 *  @time 8/14/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.home
 */
class CupidHomeViewModel : ViewModel() {
    private val api = createApi<SignTaskApi>()

    //签到任务
    private val _signInfo: MutableStateFlow<TaskSeries> = MutableStateFlow(TaskSeries())
    val signInfo = _signInfo.asStateFlow()

    suspend fun requestSignInfo(callback: (TaskSeries) -> Unit = {}) {
        val lastTime = sUserKV.getLong("key_sign_dialog_show")
//        val lastTime = 0L
        innerRequestSignInfo { value ->
            if (!isToday(lastTime) && !value.todayFinished && value.tasks.isNotEmpty()) {
                callback(value)
                sUserKV.putLong("key_sign_dialog_show", System.currentTimeMillis())
            }
        }
    }

    suspend fun startSign(seriesId: Int): Result<JsonObject> {
        return runApiCatching {
            val params = mutableMapOf("series_id" to seriesId.toString())
            val smId = AppInfo.smBoxId
            if (!smId.isNullOrEmpty()) {
                params["ism_device_id"] = smId
            }
            api.doSign(params)
        }.onSuccess { obj ->
            innerRequestSignInfo()
        }.toastError()
    }

    private suspend fun innerRequestSignInfo(callback: suspend (TaskSeries) -> Unit = {}) {
        runApiCatching {
            api.getSignInfo()
        }.onSuccess { obj ->
            obj.takeIf {
                it.containsKey("series_id") && it.containsKey("today_finished")
            }?.let {
                val value = sAppJson.decodeFromJsonElement<TaskSeries>(it)
                _signInfo.value = value
                callback(value)
            }
        }
    }

    private fun isToday(timestamp: Long): Boolean {
        val calendar = Calendar.getInstance()

        // 获取当前时间
        val now = Date()
        calendar.time = now
        val currentYear = calendar.get(Calendar.YEAR)
        val currentMonth = calendar.get(Calendar.MONTH)
        val currentDay = calendar.get(Calendar.DAY_OF_MONTH)

        // 获取时间戳对应的日期
        val timestampDate = Date(timestamp)
        calendar.time = timestampDate
        val timestampYear = calendar.get(Calendar.YEAR)
        val timestampMonth = calendar.get(Calendar.MONTH)
        val timestampDay = calendar.get(Calendar.DAY_OF_MONTH)

        // 比较年份、月份和日期
        return currentYear == timestampYear && currentMonth == timestampMonth && currentDay == timestampDay
    }
}