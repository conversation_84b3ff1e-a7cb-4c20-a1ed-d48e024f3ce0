package com.qyqy.cupid.ui.home

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Badge
import androidx.compose.material3.BadgedBox
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.saveable.rememberSaveableStateHolder
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.IHomeAction
import com.qyqy.cupid.ui.ISubRouteNav
import com.qyqy.cupid.ui.NavListener
import com.qyqy.cupid.ui.SubRouteNavImpl
import com.qyqy.cupid.ui.coin.CoinModuleTab
import com.qyqy.cupid.ui.coin.CoinPage
import com.qyqy.cupid.ui.coin.IncomePage
import com.qyqy.cupid.ui.dialog.FirstRechargeRewardFloat
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.dialog.SigninDialog
import com.qyqy.cupid.ui.home.message.MessagePage
import com.qyqy.cupid.ui.home.message.formatCount
import com.qyqy.cupid.ui.home.mine.CpdMinePage
import com.qyqy.cupid.ui.home.mine.visitors.VisitorViewModel
import com.qyqy.cupid.ui.relations.family.FamilySquareTab
import com.qyqy.cupid.utils.eventbus.LocalEventBus
import com.qyqy.ucoo.R
import com.qyqy.ucoo.ReportExposureCompose
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.app
import com.qyqy.ucoo.asComponentActivity
import com.qyqy.ucoo.compose.LaunchOnceEffect
import com.qyqy.ucoo.compose.isEditOnCompose
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.state.EmptyView
import com.qyqy.ucoo.compose.ui.AppearanceStatusBars
import com.qyqy.ucoo.config.AnonymousConfig
import com.qyqy.ucoo.config.BottomTab
import com.qyqy.ucoo.config.IPageConf
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.im.compat.conversation.AppConversationManger
import com.qyqy.ucoo.utils.EntityCallback

interface SubTab {
    @get:StringRes
    val titleRes: Int

    @get:DrawableRes
    val iconNormal: Int

    @get:DrawableRes
    val iconSelected: Int
}

interface SubPage<T> {
    var remoteTabText: String?
    val content: @Composable (IHomeAction) -> Unit
}

open class BasicSubPage<T>(
    override val titleRes: Int,
    override val iconNormal: Int,
    override val iconSelected: Int,
    override var remoteTabText: String? = null,
    override val content: @Composable (IHomeAction) -> Unit,
) : SubPage<T>, SubTab

sealed interface HomeSubPage : SubPage<IHomeAction>, SubTab {
    /**
     * 定位数字 [IPageConf]
     */
    val t: Int

    fun getTabName(): String {
        return remoteTabText?.takeIf { it.isNotEmpty() } ?: app.getString(titleRes)
    }

    object Main : BasicSubPage<IHomeAction>(titleRes = R.string.cupid_home,
        iconNormal = R.drawable.ic_cpd_home_unselect,
        iconSelected = R.drawable.ic_cpd_home_selected,
        content = { it -> MainPage(it) }), HomeSubPage {
        override val t: Int = 11
    }

    //2.33版本开始金币和收益合并
    object NewCoin : BasicSubPage<IHomeAction>(titleRes = R.string.cupid_income,
        iconNormal = R.drawable.ic_cpd_income_unselect,
        iconSelected = R.drawable.ic_cpd_income_selected,
        content = {
            CoinModuleTab()
        }), HomeSubPage {
        override val t: Int = 17
    }

    object Gold : BasicSubPage<IHomeAction>(titleRes = R.string.cupid_gold,
        iconNormal = R.drawable.ic_cpd_gold_unselect,
        iconSelected = R.drawable.ic_cpd_gold_selected,
        content = { _ ->
            CoinPage(
                modifier = Modifier
                    .background(colorResource(id = R.color.FFF5F7F9))
                    .paint(
                        painter = painterResource(id = R.drawable.cupid_header_home),
                        contentScale = ContentScale.FillWidth,
                        alignment = Alignment.TopCenter
                    )
                    .statusBarsPadding()
                    .padding(top = 20.dp)
            )
        }), HomeSubPage {
        override val t: Int = 12
    }

    object Income : BasicSubPage<IHomeAction>(titleRes = R.string.cupid_income,
        iconNormal = R.drawable.ic_cpd_income_unselect,
        iconSelected = R.drawable.ic_cpd_income_selected,
        content = { _ ->
            IncomePage(
                modifier = Modifier
                    .background(colorResource(id = R.color.FFF5F7F9))
                    .paint(
                        painter = painterResource(id = R.drawable.cupid_header_home),
                        contentScale = ContentScale.FillWidth,
                        alignment = Alignment.TopCenter
                    )
                    .statusBarsPadding()
                    .padding(top = 20.dp)
            )
        }), HomeSubPage {
        override val t: Int = 13
    }

    object Message : BasicSubPage<IHomeAction>(titleRes = R.string.cupid_message,
        iconNormal = R.drawable.ic_cupid_msg_unselect,
        iconSelected = R.drawable.ic_cupid_msg_select,
        content = {
            MessagePage(Message, it)
        }), HomeSubPage {
        override val t: Int = 14
    }

    object Mine : BasicSubPage<IHomeAction>(titleRes = R.string.cupid_mine,
        iconNormal = R.drawable.ic_cpd_mine_unselect,
        iconSelected = R.drawable.ic_cpd_mine_sel,
        content = {
            ReportExposureCompose(exposureName = TracePoints.MY_PAGE) {
                CpdMinePage(it)
            }
        }), HomeSubPage {
        override val t: Int = 15
    }

    object Family : BasicSubPage<IHomeAction>(
        R.string.cpd_family,
        R.drawable.ic_family_unselect,
        R.drawable.ic_family_selected,
        content = { _ ->
            ReportExposureCompose(exposureName = TracePoints.VISIT_LIVE_PAGE) {
                FamilySquareTab()
            }
        }), HomeSubPage {
        override val t: Int = 16
    }
}

@Composable
fun TabItem(
    subTab: SubTab,
    isSelected: Boolean,
    modifier: Modifier = Modifier,
    coverText: String? = null
) {
    val title = coverText ?: stringResource(id = subTab.titleRes)
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Bottom
    ) {
        if (subTab is HomeSubPage.Message) {
            val unreadConversationCount by AppConversationManger.unReadCountFlow.collectAsStateWithLifecycle()
            val unreadVisitorCount by VisitorViewModel.visitorCount.collectAsStateWithLifecycle()
            val badgeNumber = if (isEditOnCompose) {
                0
            } else {
                unreadConversationCount + unreadVisitorCount
            }
            BadgedBox(badge = {
                Badge(
                    modifier = Modifier.alpha(if (badgeNumber > 0) 1f else 0f),
                    containerColor = Color(0xFFF76560),
                    contentColor = Color.White,
                ) {
                    Text(text = badgeNumber.formatCount(99))
                }
            }) {
                Image(
                    modifier = Modifier.height(20.dp),
                    painter = painterResource(id = if (isSelected) subTab.iconSelected else subTab.iconNormal),
                    contentDescription = title
                )
            }
        } else {
            Image(
                modifier = Modifier.height(20.dp),
                painter = painterResource(id = if (isSelected) subTab.iconSelected else subTab.iconNormal),
                contentDescription = title
            )
        }
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = title,
            fontSize = 10.sp,
            lineHeight = 10.sp,
            color = if (isSelected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

private val transferBottomTabToHomePage: (tab: BottomTab) -> HomeSubPage? = { tab ->
    when (tab.t) {
        HomeSubPage.Main.t -> HomeSubPage.Main
        HomeSubPage.Gold.t -> HomeSubPage.Gold
        HomeSubPage.Income.t -> HomeSubPage.Income
        HomeSubPage.NewCoin.t -> HomeSubPage.NewCoin
        HomeSubPage.Message.t -> HomeSubPage.Message
        HomeSubPage.Mine.t -> HomeSubPage.Mine
        HomeSubPage.Family.t -> HomeSubPage.Family
        else -> null
    }.also { it?.remoteTabText = tab.name }
}

@Composable
fun homeScreen(
    onAction: IHomeAction = IHomeAction.Empty,
    onEntityCallback: EntityCallback<Int> = {}
): ISubRouteNav {
    val pageConfig by UIConfig.CupIdHomeTabFlow.collectAsStateWithLifecycle()

    AppearanceStatusBars(isLight = true)

    val subPages: List<HomeSubPage> by remember {
        derivedStateOf {
            pageConfig?.run {
                bottomTabs.distinctBy { it.t }.mapNotNull { tab ->
                    transferBottomTabToHomePage.invoke(tab)
                }
            }.orEmpty().ifEmpty {
                listOf(HomeSubPage.Mine)
            }
        }
    }
    val dialogQueue = LocalDialogQueue.current

    LocalContext.current.asComponentActivity?.let { owner ->
        val viewModel = viewModel(CupidHomeViewModel::class.java, viewModelStoreOwner = owner)
        LaunchOnceEffect(Unit) {
            viewModel.requestSignInfo {
                dialogQueue.push(SigninDialog)
            }
        }
    }

    if (pageConfig == null) {
        // 已经不会为null了
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Text(text = "Loading...", color = Color.Gray.copy(alpha = 0.1f))
        }
        return SubRouteNavImpl.Empty
    } else {
        return if (subPages.isNotEmpty()) {
            val currentState = rememberSaveable {
                mutableIntStateOf(
                    subPages.find { it.t == pageConfig?.landingPage?.page }?.t ?: subPages.first().t
                )
            }
            var currentT by currentState
            LaunchedEffect(key1 = currentT) {
                onEntityCallback.invoke(currentT)
            }
            val currentTab: HomeSubPage =
                subPages.firstOrNull { it.t == currentT } ?: subPages.first()
                    .also { currentT = it.t }

            val vmVisitor = viewModel<VisitorViewModel>()
            LaunchOnceEffect {
                vmVisitor.refreshVisitors()
            }

            val eventBus = LocalEventBus.current


            HomeTabPage(currentTab, onAction, subPages, {
                currentT = it.t
                if (it.t == HomeSubPage.Family.t) {
                    eventBus.addStickyEvent(
                        NavListener.NavKey,
                        "${NavListener.HOME_SUB_NAV}homeSwitchTab",
                        true
                    )
//                    // 每天第一次进入, 判断是否要弹窗
//                    isTodayHasRan("key_family_dialog_show") {
//                        dialogQueue.push(FamilyRemindDialog)
//                        return@isTodayHasRan true
//                    }
                }
                if (it.t == HomeSubPage.Message.t) {
                    AnonymousConfig.refreshConversationConfig()
                }
            })
            remember(subPages) {
                SubRouteNavImpl.Home(subPages, currentState, eventBus)
            }

        } else {
            EmptyView(iconRes = R.drawable.cupid_empty, modifier = Modifier.fillMaxSize())
            SubRouteNavImpl.Empty
        }
    }
}

@Composable
private fun HomeTabPage(
    currentTab: HomeSubPage,
    onAction: IHomeAction,
    subPages: List<HomeSubPage>,
    onTabSelected: (HomeSubPage) -> Unit,
    modifier: Modifier = Modifier,
) {
    val density = LocalDensity.current
    val tabModifier = Modifier.height(46.dp)

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background),
        verticalArrangement = Arrangement.spacedBy((-12).dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .weight(1f)
        ) {
            val saveableStateHolder = rememberSaveableStateHolder()
            saveableStateHolder.SaveableStateProvider(key = currentTab.titleRes) {
                currentTab.content(onAction)
            }

            FirstRechargeRewardFloat(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(bottom = 10.dp)
            )
        }
        Row(
            modifier = Modifier
                .background(Color.Transparent)
                .graphicsLayer {
                    shadowElevation = with(density) {
                        4.dp.toPx()
                    }
                    shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)
                }
                .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                .background(Color.White)
                .padding(bottom = with(density) {
                    WindowInsets.navigationBars
                        .getBottom(this)
                        .toDp()
                        .coerceAtLeast(10.dp)
                }),
        ) {
            subPages.forEach { tab ->
                TabItem(
                    modifier = tabModifier
                        .weight(1f)
                        .noEffectClickable {
                            onTabSelected(tab)
                        },
                    subTab = tab,
                    isSelected = tab == currentTab,
                    coverText = tab.getTabName()
                )
            }
        }
    }
}

@Preview(showSystemUi = true)
@Composable
fun Preview() {
    PreviewCupidTheme {
        homeScreen()
    }
}
