

package com.qyqy.cupid.ui.home.mine

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.utils.OnClick


@Composable
fun <T> AlbumRow(albumList: List<T>, converter: (T) -> String, modifier: Modifier = Modifier, onAdd: OnClick = {}) {
    val list = remember(albumList) {
        albumList.map(converter)
    }
    CupidBasicAlbumRow(
        albumList = list, modifier = modifier
            .fillMaxWidth()
            .background(Color(0xFFFAFAFA), Shapes.corner12)
            .border(0.5.dp, Color(0xFFF1F2F3), Shapes.corner12)
            .padding(start = 12.dp)
            .padding(vertical = 12.dp),
        onAdd = onAdd
    )
}

@Composable
fun CupidBasicAlbumRow(albumList: List<String>, modifier: Modifier = Modifier, onAdd: OnClick = {}) {
    Row(
        modifier = modifier
    ) {
        Box(
            modifier = Modifier
                .size(56.dp)
                .clip(Shapes.small)
                .clickable(onClick = onAdd)
                .background(Color.White, Shapes.small)
                .border(0.5.dp, MaterialTheme.colorScheme.primary, Shapes.small),
            contentAlignment = Alignment.Center
        ) {
            Text(text = "+", fontSize = 28.sp, color = MaterialTheme.colorScheme.primary)
        }
        Spacer(modifier = Modifier.width(8.dp))
        LazyRow(
            modifier = Modifier
                .weight(1f)
                .height(56.dp)
        ) {
            items(albumList) { item ->
                ComposeImage(
                    model = item, modifier = Modifier
                        .fillMaxHeight()
                        .aspectRatio(1f)
                        .clip(RoundedCornerShape(8.dp))
                )
                Spacer(modifier = Modifier.width(8.dp))
            }
        }
    }
}

@Preview
@Composable
private fun AlbumRowPreview() {
    PreviewCupidTheme {
        AlbumRow(albumList = emptyList<String>(),{it})
    }
}