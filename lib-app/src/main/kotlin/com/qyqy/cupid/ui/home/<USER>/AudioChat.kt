@file:OptIn(ExperimentalMaterial3Api::class)

package com.qyqy.cupid.ui.home.audiochat

import android.Manifest
import com.qyqy.ucoo.widget.Switch
import com.qyqy.ucoo.widget.SyncSwitchButton
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.LifecycleResumeEffect
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.CupidViewModel
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.home.MainPageItem
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.cupid.widgets.click
import com.qyqy.cupid.widgets.state.CupidPullRefreshBox
import com.qyqy.ucoo.R
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.asComponentActivity
import com.qyqy.ucoo.compose.LaunchOnceEffect
import com.qyqy.ucoo.compose.presentation.room.RichText
import com.qyqy.ucoo.compose.state.EmptyView
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.home.main.MatchSwitchViewModel
import com.qyqy.ucoo.im.message.entity.RichItem
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.utils.LogUtils
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AudioChatListPage(vm: AudioChatViewModel = viewModel()) {
    val nav = LocalAppNavController.current
    val list by vm.dataState
    val vmSwitch = viewModel<MatchSwitchViewModel>()
    val scope = rememberCoroutineScope()
    LaunchOnceEffect {
        vmSwitch.refreshMatchState()
    }
    LifecycleResumeEffect(key1 = "video-call") {
        val job = scope.launch {
            delay(3000)
            LogUtils.d("audio_chat", "start request video call")
            vm.requestVideoCall()
        }
        onPauseOrDispose {
            job.cancel()
        }
    }
    val setting by vmSwitch.matchStateFlow.collectAsStateWithLifecycle()
    val showSwitch = setting.audioChat.atiBtnShow
    val richTextList = setting.audioChat.richText
    val desc = setting.audioChat.atiBtnHint
    val st = vmSwitch.audioChatState
    val activity = LocalContext.current.asComponentActivity!!
    val mainViewModel = viewModel<CupidViewModel>(viewModelStoreOwner = activity)
    val c2cCallingHelper = mainViewModel.c2cCallingHelper
    val callUserState = remember {
        mutableStateOf<User?>(null)
    }
    val dq = LocalDialogQueue.current
    val launcher = c2cCallingHelper.rememberCallingLauncher(targetUserState = callUserState, fromRecommend = true)
    CupidPullRefreshBox(
        isRefreshing = vm.isRefreshing,
        onRefresh = { vm.refresh() },
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp)
    ) {
        LazyColumn(modifier = Modifier.fillMaxSize(), verticalArrangement = Arrangement.spacedBy(8.dp)) {
            if (showSwitch) {
                item {
                    MatchItem(st, richTextList, desc) {
                        val stateLast = st.value
                        val result = vmSwitch.switchAudioState()
                        if (result) {
                            if (stateLast == Switch.ON) {
                                toastRes(R.string.cpd_toast_audio_switch_off)
                            } else {
                                nav.navigateByLink(setting.audioChat.atiNoteLink)
                            }
                        }
                        result
                    }
                }
            }
            if (vm.isLoaded && list.isEmpty()) {
                item {
                    EmptyView(
                        modifier = Modifier
                            .fillMaxWidth()
                            .aspectRatio(0.75f)
                    )
                }
            }
            items(list) { u ->
                ItemView(u) {
                    Analytics.reportClickEvent(TracePoints.CLICK_HOME_VOICE_CALL_BUTTON)
                    callUserState.value = u
                    launcher.launch(arrayOf(Manifest.permission.RECORD_AUDIO))
                }
            }
        }
    }
}

@Preview
@Composable
private fun Preview() {
    PreviewCupidTheme {
        val st = remember {
            mutableStateOf(Switch.ON)
        }
        Column {
            MatchItem(st)
        }
    }
}

@Composable
fun MatchItem(
    switchState: MutableState<Switch>,
    richTextList: List<RichItem> = emptyList(),
    desc: String = "",
    onSync: suspend (Switch) -> Boolean = { false },
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White, Shapes.corner12)
            .padding(12.dp)
    ) {
        Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = stringResource(R.string.cpd_audio_switch),
                color = Color(0xFF1D2129),
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.weight(1f)
            )
            SyncSwitchButton(
                switchState,
                onSync = onSync,
                width = 40.dp,
                height = 20.dp,
                uncheckedTrackColor = Color(0xFFCCCCCC),
                checkedTrackColor = Color(0xFFFF5E8B),
            )
        }
        Spacer(modifier = Modifier.height(8.dp))
        if (richTextList.isNotEmpty()) {
            RichText(
                rich = richTextList,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                fontSize = 12.sp,
                lineHeight = 16.sp
            )
        } else {
            Text(text = desc, color = MaterialTheme.colorScheme.onSurfaceVariant, fontSize = 12.sp)
        }
    }

}

@Composable
private fun ItemView(user: AppUser, onCall: OnClick = {}) {
    val nav = LocalAppNavController.current
    MainPageItem(user = user, modifier = Modifier.padding(12.dp), onCellClick = {
        nav.navigate(CupidRouters.C2CChat, mapOf("user" to user))
        Analytics.reportClickEvent(TracePoints.CLICK_HOME_VOICE_CALL_CELL)
    }, onAvatarClick = {
        Analytics.reportClickEvent(TracePoints.CLICK_HOME_VOICE_CALL_CELL)
        nav.navigateToProfile(user.id)
    }, action = {
        Row(
            modifier = Modifier
                .heightIn(min = 24.dp)
                .clip(Shapes.chip)
                .border(1.dp, MaterialTheme.colorScheme.primary, Shapes.chip)
                .click(onClick = onCall)
                .padding(horizontal = 9.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(id = R.drawable.cpd_phone_call),
                contentDescription = "call",
                modifier = Modifier.size(12.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            //免费
            Text(
                text = stringResource(if (user.canFreeChat) R.string.cpd_免费发起通话 else R.string.cpd发起通话),
                color = Color(0xFFFF5E8B),
                fontSize = 12.sp
            )
        }
    })
}