package com.qyqy.cupid.ui.home.audiochat

import androidx.lifecycle.viewModelScope
import com.qyqy.cupid.ui.home.cupidHomeApi
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.compose.state.LiStateViewModel
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.user.video.VideoCallApi
import kotlinx.coroutines.launch

class AudioChatViewModel : LiStateViewModel<AppUser>() {

    init {
        viewModelScope.launch {
            refresh()
        }
    }

    private val videoApi = createApi<VideoCallApi>()

    override suspend fun fetch(): List<AppUser> {
        return cupidHomeApi.getAudioChatRecommendList().getOrNull().orEmpty()
    }


    suspend fun requestVideoCall() = runApiCatching {
        videoApi.requestVideoCall()
    }
}