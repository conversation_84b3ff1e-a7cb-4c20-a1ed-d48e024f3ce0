package com.qyqy.cupid.ui.home.mine

import android.os.Bundle
import androidx.collection.mutableScatterMapOf
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.IHomeAction
import com.qyqy.cupid.ui.NavArgument
import com.qyqy.cupid.ui.dialog.ContentAlertDialog
import com.qyqy.cupid.ui.dialog.DialogButton
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.UserInfo
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.presentation.ScreenNavigator
import com.qyqy.ucoo.compose.presentation.ff.UserItem
import com.qyqy.ucoo.compose.state.EmptyView
import com.qyqy.ucoo.compose.state.PullRefreshBox
import com.qyqy.ucoo.compose.state.StateLayoutDefaults
import com.qyqy.ucoo.compose.ui.AppearanceStatusBars
import com.qyqy.ucoo.setting.UserBlackListContract
import com.qyqy.ucoo.setting.UserBlackListViewModel
import com.qyqy.ucoo.utils.OnClick
import eu.bambooapps.material3.pullrefresh.PullRefreshIndicatorDefaults

object BlackListNavigator : ScreenNavigator {

    @Composable
    override fun ContentScreen(activity: BaseActivity, bundle: Bundle) {
        val rootArguments = mutableScatterMapOf<String, NavArgument>()
        AppearanceStatusBars(isLight = true)
//        CupidNavigationGraph(CupidRouters.BLACK_LIST, rootArguments = rootArguments)
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BlackListScreen(onAction: IHomeAction) {
    val vm: UserBlackListViewModel = viewModel()
    val state by vm.uiState.collectAsStateWithLifecycle()
    val list = state.listState.getOrNull().orEmpty()
    val isRefreshing by remember {
        derivedStateOf { state.refreshState.isLoading }
    }
    val hasMore by remember {
        derivedStateOf { !state.loadMoreState.isDone }
    }
    val loaded by vm.loaded
    LaunchedEffect(key1 = vm) {
        vm.uiState
        vm.sendEvent(UserBlackListContract.Event.Refresh)
    }
    var userCurrent by remember {
        mutableStateOf<UserInfo?>(null)
    }
    if (userCurrent != null) {
        Dialog(onDismissRequest = { userCurrent = null }) {
            ContentAlertDialog(
                content = stringResource(id = R.string.cpd确定要取消拉黑吗),
                startButton = DialogButton(stringResource(id = R.string.cpd取消)) {
                    userCurrent = null
                },
                endButton = DialogButton(stringResource(id = R.string.cpd确定)) {
                    vm.sendEvent(UserBlackListContract.Event.OnBlackClicked(userCurrent!!.id, false))
                    userCurrent = null
                }
            )
        }
    }

    Scaffold(topBar = { CupidAppBar(stringResource(id = R.string.cpd_black_list)) }) { pv ->
        PullRefreshBox(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.surface)
                .padding(pv),
            colors = PullRefreshIndicatorDefaults.colors(
                containerColor = MaterialTheme.colorScheme.surface,
                MaterialTheme.colorScheme.primary
            ),
            isRefreshing = isRefreshing,
            onRefresh = {
                vm.sendEvent(UserBlackListContract.Event.Refresh)
            }
        ) {
            LazyColumn(modifier = Modifier.fillMaxSize(1f)) {
                items(list) { item ->
                    BlackListItem(user = item, onAvatarClick = {
                        onAction.navigateToProfile(item.id)
                    }, onCancelBlack = {
                        userCurrent = item
                    })
                }
                if (loaded && !isRefreshing && list.isNotEmpty() && hasMore) {
                    item {
                        Box(modifier = Modifier.fillMaxWidth()) {
                            LaunchedEffect(true) {
                                vm.sendEvent(UserBlackListContract.Event.LoadMore)
                            }
                            StateLayoutDefaults.BottomLoadBar(hasMore = hasMore)
                        }
                    }
                }
            }
            if (loaded && !isRefreshing && list.isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(0.75f)
                        .padding(16.dp), contentAlignment = Alignment.Center
                ) {
                    EmptyView(textRes = R.string.cpd暂无黑名单数据)
                }
            }
        }
    }
}

@Composable
fun BlackListItem(user: UserInfo, onAvatarClick: (UserInfo) -> Unit = {}, onCancelBlack: OnClick = {}) {
    UserItem(user = user, nameColor = MaterialTheme.colorScheme.onSurface, onAvatarClicked = onAvatarClick, endCompose = {
        OutlinedButton(
            onClick = onCancelBlack,
            colors = ButtonDefaults.outlinedButtonColors(contentColor = MaterialTheme.colorScheme.primary),
            border = BorderStroke(1.dp, MaterialTheme.colorScheme.primary),
            contentPadding = PaddingValues(12.dp, 8.dp)
        ) {
            Text(text = stringResource(id = R.string.cpd_cancel_add_backlist), fontSize = 14.sp)
        }
    })
}

@Preview
@Composable
private fun BlackListItemPreview() {
    PreviewCupidTheme {
        BlackListItem(user = UserInfo(nickname = "Hello"))
    }
}