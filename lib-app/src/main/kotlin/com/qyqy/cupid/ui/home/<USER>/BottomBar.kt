package com.qyqy.cupid.ui.home.message

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.data.CallMode
import com.qyqy.cupid.ui.IC2CAction
import com.qyqy.cupid.ui.home.message.icons.ActionIcons
import com.qyqy.cupid.ui.home.message.icons.Audio
import com.qyqy.cupid.ui.home.message.icons.CurrencyGift
import com.qyqy.cupid.ui.home.message.icons.Emoji
import com.qyqy.cupid.ui.home.message.icons.Gift
import com.qyqy.cupid.ui.home.message.icons.Keyboard
import com.qyqy.cupid.ui.home.message.icons.Line
import com.qyqy.cupid.ui.home.message.icons.Photo
import com.qyqy.cupid.ui.home.message.icons.VideoCall
import com.qyqy.cupid.ui.home.message.icons.VoiceCall
import com.qyqy.cupid.widgets.composeClick
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.presentation.room.AudioPanel
import com.qyqy.ucoo.compose.presentation.room.EmojiPanel
import com.qyqy.ucoo.compose.presentation.room.KeyboardPanelState
import com.qyqy.ucoo.compose.presentation.room.withInputField
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.config.UserConf
import com.qyqy.ucoo.im.compat.MessageBundle
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.utils.rememberRequestAlbumPermissionHelper


@Composable
fun ColumnScope.C2CBottomBar(
    panelState: KeyboardPanelState,
    textFieldValue: MutableState<TextFieldValue>,
    hiddenModules: List<String>?,
    onAction: IC2CAction,
) {
    val audioPanel = panelState[AudioPanel]
    val emojiPanel = panelState[EmojiPanel]

    val showSendBtn by remember {
        derivedStateOf {
            textFieldValue.value.text.isNotEmpty() && !panelState.isShowing(audioPanel) && !panelState.isShowing(emojiPanel)
        }
    }

    Spacer(modifier = Modifier.height(12.dp))

    Row(
        modifier = Modifier.padding(horizontal = 16.dp),
        verticalAlignment = Alignment.Bottom
    ) {

        Box(
            modifier = Modifier
                .clip(RoundedCornerShape(20.dp))
                .weight(1f)
                .heightIn(min = 40.dp)
                .background(Color(0xFFF6F7FB)),
            contentAlignment = Alignment.CenterStart
        ) {

            // 不能隐藏会抛异常
            BasicTextField(
                value = textFieldValue.value,
                onValueChange = {
                    textFieldValue.value = it
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 10.dp)
                    .withInputField(panelState),
                textStyle = TextStyle.Default.copy(
                    color = Color(0xFF1D2129), fontSize = 14.sp
                ),
                maxLines = 6,
                cursorBrush = SolidColor(MaterialTheme.colorScheme.primary),
                decorationBox = { innerTextField ->
                    innerTextField()
                    if (textFieldValue.value.text.isEmpty()) {
                        AppText(text = stringResource(id = R.string.输入新消息), fontSize = 14.sp, color = Color(0xFF86909C))
                    }
                })
        }

        Row(
            modifier = Modifier.height(40.dp), verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(
                onClick = composeClick {
                    panelState.switchPanel(emojiPanel)
                },
                modifier = Modifier
                    .padding(start = 6.dp)
                    .size(36.dp)
            ) {
                Image(
                    imageVector = if (panelState.isShowing(emojiPanel)) ActionIcons.Keyboard else ActionIcons.Emoji,
                    contentDescription = "",
                )
            }

            AnimatedVisibility(visible = showSendBtn) {
                AppButton(
                    text = stringResource(id = R.string.cpd发送),
                    modifier = Modifier
                        .padding(start = 6.dp)
                        .size(60.dp, 28.dp),
                    fontSize = 14.sp,
                    textStyle = LocalTextStyle.current.copy(fontWeight = FontWeight.Medium)
                ) {
                    val text = textFieldValue.value.text
                    if (text.isNotBlank()) {
                        onAction.onSendMessage(MessageBundle.Text.create(text))
                        textFieldValue.value = TextFieldValue()
                    } else {
                        toastRes(R.string.不能发送空白消息)
                    }
                }
            }
        }

    }

    Row(
        modifier = Modifier
            .padding(vertical = 6.dp, horizontal = 26.dp)
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {

        IconButton(onClick = composeClick {
            panelState.switchPanel(audioPanel)
        }, modifier = Modifier.size(36.dp)) {
            Icon(
                imageVector = if (panelState.isShowing(audioPanel)) ActionIcons.Keyboard else ActionIcons.Audio,
                contentDescription = ""
            )
        }

        val coinGiftEnable = remember(hiddenModules) {
            hiddenModules?.contains(UserConf.SHOW_COIN_GIFT) == true
        }

        val diamondGiftEnable = remember(hiddenModules) {
            hiddenModules?.contains(UserConf.SHOW_DIAMOND_GIFT) == true
        }

        if (coinGiftEnable || diamondGiftEnable) {
            IconButton(onClick = composeClick {
                onAction.onCurrencyGift(coinGiftEnable, diamondGiftEnable)
            }, modifier = Modifier.size(36.dp)) {
                Icon(imageVector = ActionIcons.CurrencyGift, contentDescription = "")
            }
        }

        val hideLineBtn = remember(hiddenModules) {
            hiddenModules?.contains(UserConf.HIDE_LINE) != false
        }

        if (!hideLineBtn) {
            IconButton(onClick = composeClick {
                onAction.onShowExchangeLineRequestDialog()
            }, modifier = Modifier.size(36.dp)) {
                Icon(
                    imageVector = ActionIcons.Line,
                    contentDescription = ""
                )
            }
        }

        val uploadImageHelper = rememberRequestAlbumPermissionHelper(context = LocalContext.current, needCrop = false) {
            onAction.onSendMultipleMessage(it.list.map { media ->
                MessageBundle.Image.create(media.uri, media.path, media.width, media.height, it.originalFlag)
            })
        }

        IconButton(onClick = composeClick {
            uploadImageHelper.start()
        }, modifier = Modifier.size(36.dp)) {
            Icon(imageVector = ActionIcons.Photo, contentDescription = "")
        }

        val hideGiftBtn = remember(hiddenModules) {
            hiddenModules?.contains(UserConf.HIDE_GIFT) != false
        }

        if (!hideGiftBtn) {
            IconButton(onClick = composeClick {
                onAction.onShowGiftPanel()
            }, modifier = Modifier.size(36.dp)) {
                Icon(
                    imageVector = ActionIcons.Gift,
                    contentDescription = "",
                    tint = Color(0xFFFF5E8B)
                )
            }
        }

        val hideVoiceBtn = remember(hiddenModules) {
            hiddenModules?.contains(UserConf.HIDE_VOICE_CHAT) != false
        }

        if (!hideVoiceBtn) {
            IconButton(onClick = composeClick {
                onAction.onStartC2CCall(CallMode.OnlyVoice)
            }, modifier = Modifier.size(36.dp)) {
                Icon(imageVector = ActionIcons.VoiceCall, contentDescription = "")
            }
        }

        val hideVideoBtn = remember(hiddenModules) {
            hiddenModules?.contains(UserConf.HIDE_VIDEO_CHAT) != false
        }
        if (!hideVideoBtn) {
            IconButton(onClick = composeClick {
                onAction.onStartC2CCall(CallMode.OnlyVideo)
            }, modifier = Modifier.size(36.dp)) {
                Icon(imageVector = ActionIcons.VideoCall, contentDescription = "")
            }
        }
    }
}