package com.qyqy.cupid.ui.home.message

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import com.qyqy.cupid.im.panel.audio.AudioRecordPanel
import com.qyqy.cupid.im.panel.emoji.EmojiPanel
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.ucoo.compose.presentation.room.AudioPanel
import com.qyqy.ucoo.compose.presentation.room.EmojiPanel
import com.qyqy.ucoo.compose.presentation.room.KeyboardPanelState
import com.qyqy.ucoo.compose.presentation.room.withPanelSize


@Composable
fun C2CBottomPanel(
    state: KeyboardPanelState,
    textFieldValue: MutableState<TextFieldValue>,
    onAction: IIMAction,
) {
    val current = state.targetVisiblePanel
    if (current != null && !current.isKeyboard && current.autoHideEnable) {
        BackHandler {
            state.hidePanel(current)
        }
    }

    state.panels.onEach { panel ->
        val alpha by remember {
            derivedStateOf {
                if (state.isAllowPanelShowing(panel)) 1f else 0f
            }
        }

        val zIndex = if (state.targetVisiblePanel == panel) 1f else 0f

        val modifier = Modifier
            .height(200.dp)
            .alpha(alpha)
            .zIndex(zIndex)
            .withPanelSize(state, panel)
            .navigationBarsPadding()

        when (panel.key) {
            AudioPanel -> {
                AudioRecordPanel(
                    modifier = modifier,
                    onAction = onAction
                )
            }

            EmojiPanel -> {
                EmojiPanel(
                    panelState = state,
                    textFieldValue = textFieldValue,
                    modifier = modifier,
                    onAction = onAction
                )
            }
        }
    }
}