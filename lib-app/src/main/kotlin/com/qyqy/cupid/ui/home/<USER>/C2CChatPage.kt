package com.qyqy.cupid.ui.home.message

import android.Manifest
import android.os.Parcelable
import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.spring
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.foundation.text.appendInlineContent
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.Placeholder
import androidx.compose.ui.text.PlaceholderVerticalAlign
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.data.CallMode
import com.qyqy.cupid.data.PhrasesSettingBean
import com.qyqy.cupid.data.users.introEmptyRes
import com.qyqy.cupid.im.panel.gift.C2CBottomGiftPanelDialog
import com.qyqy.cupid.im.panel.gift.GiftPosition
import com.qyqy.cupid.im.panel.gift.GiftViewModel
import com.qyqy.cupid.model.PhrasesViewModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.CupidViewModel
import com.qyqy.cupid.ui.DestinationRoute
import com.qyqy.cupid.ui.GiftExtra
import com.qyqy.cupid.ui.IC2CAction
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.dialog.BlackAlertDialog
import com.qyqy.cupid.ui.dialog.ContentAlertDialog
import com.qyqy.cupid.ui.dialog.CouplePendantFloat
import com.qyqy.cupid.ui.dialog.DialogButton
import com.qyqy.cupid.ui.dialog.DialogQueue
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IconAlertDialog
import com.qyqy.cupid.ui.dialog.MoreActionDialog
import com.qyqy.cupid.ui.dialog.NormalDialog
import com.qyqy.cupid.ui.dialog.SimpleAnimatedDialog
import com.qyqy.cupid.ui.dialog.TitleAlertDialog
import com.qyqy.cupid.ui.dialog.UnuseGiftRemindDialog
import com.qyqy.cupid.ui.dialog.rememberDialogQueue
import com.qyqy.cupid.ui.global.CupidC2CWishFloatWidget
import com.qyqy.cupid.ui.home.chat.QuickReplyBoard
import com.qyqy.cupid.ui.home.chat.QuickReplyViewModel
import com.qyqy.cupid.ui.home.message.icons.ActionIcons
import com.qyqy.cupid.ui.home.message.icons.More
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.cupid.ui.profile.AudioStateBar
import com.qyqy.cupid.ui.profile.RechargeDialog
import com.qyqy.cupid.ui.profile.RechargePageContent
import com.qyqy.cupid.ui.profile.RechargePageScaffold
import com.qyqy.cupid.ui.profileDestination
import com.qyqy.cupid.utils.CupidFamilyManager
import com.qyqy.cupid.widgets.AlignHorizontalContainer
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.DataPoint
import com.qyqy.ucoo.DataTrace
import com.qyqy.ucoo.R
import com.qyqy.ucoo.ReportExposureCompose
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.currentRoomId
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.privateRoomId
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.asComponentActivity
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.bean.PrivilegedGiftRemindBean
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.presentation.room.AudioPanel
import com.qyqy.ucoo.compose.presentation.room.ChatRoomScaffold
import com.qyqy.ucoo.compose.presentation.room.EmojiPanel
import com.qyqy.ucoo.compose.presentation.room.IMPanel
import com.qyqy.ucoo.compose.presentation.room.KeyboardPanelState
import com.qyqy.ucoo.compose.presentation.room.LocalMsgAudioPlayer
import com.qyqy.ucoo.compose.presentation.room.Panel
import com.qyqy.ucoo.compose.presentation.room.rememberMsgAudioPlayer
import com.qyqy.ucoo.compose.presentation.room.rememberPanelState
import com.qyqy.ucoo.compose.presentation.room.withAutoHidePanel
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AgeGender
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppPhotoPreviewer
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AppearanceStatusBars
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.PaginateState
import com.qyqy.ucoo.compose.ui.overScrollVertical
import com.qyqy.ucoo.compose.ui.photo.LocalPhotoPreviewState
import com.qyqy.ucoo.compose.ui.photo.rememberPhotoGestureState
import com.qyqy.ucoo.compose.ui.photo.rememberPhotoPreviewState
import com.qyqy.ucoo.compose.ui.rememberOverscrollFlingBehavior
import com.qyqy.ucoo.compose.ui.rememberPaginateState
import com.qyqy.ucoo.compose.ui.runWithLoading
import com.qyqy.ucoo.compose.ui.topFadingEdge
import com.qyqy.ucoo.config.UserConf
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.im.bean.CPGift
import com.qyqy.ucoo.im.bean.MsgSendCondition
import com.qyqy.ucoo.im.compat.ConversationType
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.IMCompatListener
import com.qyqy.ucoo.im.compat.MessageBundle
import com.qyqy.ucoo.im.compat.MsgFilter
import com.qyqy.ucoo.im.compat.SendParams
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.UCInstanceMessage
import com.qyqy.ucoo.im.compat.WatchMessageEventEffect
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.loadMsgListLayout
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.sUserFlow
import com.qyqy.ucoo.setting.ReportActivity
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.user.AppPurchaseHelper
import com.qyqy.ucoo.user.Purchase
import com.qyqy.ucoo.user.WalletViewModel
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import kotlin.math.abs

object C2CChatConst {
    const val ACTION_SAY_HI = 1
    const val ACTION_SHOW_GIFT = 2
    const val ACTION_SHOW_IME = 3
    const val ACTION_CALL_VOICE = 4
    const val ACTION_CALL_VIDEO = 5
}

@Composable
fun C2CChatScreen(user: AppUser, effects: Flow<Any>) {

    val viewModel = viewModel<C2CChatViewModel>(initializer = {
        C2CChatViewModel(user)
    })

    val giftViewModel = viewModel<GiftViewModel>(initializer = {
        GiftViewModel(user.id, ConversationType.C2C)
    })

    val listState = rememberLazyListState()

    val panelState = rememberPanelState(listState, viewModel.panels, 12)

    val gestureState = with(LocalDensity.current) {
        rememberPhotoGestureState(
            extraHorizontalOffset = 80.dp.toPx(),
            extraVerticalOffset = 100.dp.toPx(),
        )
    }

    val previewState = rememberPhotoPreviewState(
        gestureState = gestureState,
        onGestureClose = { _, offsetY, velocityY ->
            abs(velocityY) > 1000 || abs(offsetY) > 60.dp.toPx()
        }
    ) {
        viewModel.getPreviewImageList(true, null).second
    }

    val scope = rememberCoroutineScope()

    val targetUser by viewModel.targetUserFlow.collectAsStateWithLifecycle()

    val selfUser by sUserFlow.collectAsStateWithLifecycle()

    val dialogQueue = rememberDialogQueue<IC2CAction>()

    val navController = LocalAppNavController.current

    val loading = LocalContentLoading.current

    WatchMessageEventEffect(listener = object : IMCompatListener {

        override val filter: MsgFilter = MsgFilter(targetUser.id)

        override fun onMessageCheckFail(condition: MsgSendCondition?, throwable: Throwable?) {
            if (condition != null) {
                toast(condition.toastMsg)
                val title = condition.title
                val hint = condition.hint
                if (title.isNotEmpty() && hint.isNotEmpty()) {
                    dialogQueue.push(
                        RechargeCoinToChatPanelDialog(
                            title,
                            hint,
                            targetUser.avatarUrl,
                            selfUser.avatarUrl
                        )
                    )
                }
            } else throwable?.toast()
        }

        override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
            message.parseDataJson<PrivilegedGiftRemindBean>()?.also {
                dialogQueue.push(UnuseGiftRemindDialog(it) {
                    scope.launch {
                        delay(500)
                        dialogQueue.push(
                            C2CBottomGiftPanelDialog(
                                null,
                                giftViewModel.giftListModelState
                            )
                        )
                    }
                })
            }
        }
    })

    WatchMessageEventEffect(listener = object : IMCompatListener {
        override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
            super.onRecvNewCustomMessage(message, offline)
            when (message.cmd) {
                MsgEventCmd.GIFT_UNUSED_POPUP -> {
                    message.parseDataJson<PrivilegedGiftRemindBean>()?.let { bean ->
                        dialogQueue.push(UnuseGiftRemindDialog(bean) {
                            scope.launch {
                                delay(300)
                                //可能已经弹出, 要关闭掉
                                dialogQueue.dismiss {
                                    it.dialog is C2CBottomGiftPanelDialog
                                }

                                toastRes(R.string.cpd之后你可以在这里赠送背包礼物)
                                dialogQueue.push(
                                    C2CBottomGiftPanelDialog(
                                        GiftPosition(bean.gift.id),
                                        giftViewModel.giftListModelState
                                    )
                                )
                            }
                        }, true)
                    }

                }
            }
        }
    })

    val hiddenModules by remember {
        viewModel.getHiddenModulesFlow()
    }.collectAsStateWithLifecycle()

    val activity = LocalContext.current.asComponentActivity!!
    val mainViewModel = viewModel<CupidViewModel>(viewModelStoreOwner = activity)
    val c2cCallingHelper = mainViewModel.c2cCallingHelper
    val voiceLiveHelper = mainViewModel.voiceLiveHelper

    AppearanceStatusBars(isLight = true)

    Box(modifier = Modifier.fillMaxSize()) {
        CompositionLocalProvider(LocalPhotoPreviewState provides previewState) {

            val launcher = c2cCallingHelper.rememberCallingLauncher(targetUser)

            LaunchedEffect(key1 = effects) {
                effects.onEach {
                    when (it) {
                        C2CChatConst.ACTION_SHOW_GIFT -> {
                            dialogQueue.push(
                                C2CBottomGiftPanelDialog(
                                    null,
                                    giftViewModel.giftListModelState
                                )
                            )
                        }

                        C2CChatConst.ACTION_SHOW_IME -> {
                            panelState.showPanel(Panel.Keyboard)
                        }

                        C2CChatConst.ACTION_CALL_VOICE -> {
                            launcher.launch(arrayOf(Manifest.permission.RECORD_AUDIO))
                        }

                        C2CChatConst.ACTION_CALL_VIDEO -> {
                            launcher.launch(
                                arrayOf(
                                    Manifest.permission.RECORD_AUDIO,
                                    Manifest.permission.CAMERA
                                )
                            )
                        }

                        is GiftPosition -> {
                            dialogQueue.push(
                                C2CBottomGiftPanelDialog(
                                    it,
                                    giftViewModel.giftListModelState
                                )
                            )
                        }

                        is Int -> viewModel.handleAction(it)
                    }
                }.launchIn(this)
            }

            ReportExposureCompose(
                exposureName = DataTrace.Exposure.Message.私聊页面访问,
                reExposureMode = false
            )

            val paginateState = viewModel.bindListState(listState)

            C2CChatPage(
                targetUser = targetUser,
                selfUser = selfUser,
                messageList = viewModel.messageList,
                paginateState = paginateState,
                listState = listState,
                panelState = panelState,
                dialogQueue = dialogQueue,
                hiddenModules = hiddenModules,
                overlayContent = {
                    AppPhotoPreviewer(previewState = previewState)
                },
                safeContent = {
                    val hideWishList = rememberSaveable(hiddenModules) {
                        hiddenModules?.contains(UserConf.HIDE_WISHLIST) != false
                    }
                    if (!hideWishList) {
                        CupidC2CWishFloatWidget(
                            targetId = targetUser.id,
                            dialogQueue = dialogQueue,
                            modifier = Modifier
                                .align(Alignment.BottomEnd)
                                .padding(bottom = 10.dp)
                        )
                    }

                    if (!selfUser.isBoy) {
                        ChatPhrasesFloatWidget(
                            modifier = Modifier
                                .align(Alignment.BottomEnd)
                                .padding(bottom = 10.dp),
                            onClick = {
                                navController.navigate(CupidRouters.CHAT_PHRASES)
                            }
                        )
                    }

                    CouplePendantFloat(
                        coupleInfo = viewModel.coupleInfo,
                        modifier = Modifier
                            .align(Alignment.BottomEnd)
                            .padding(bottom = 10.dp)
                    ) {
                        if (viewModel.coupleInfo.isCp) {
                            val privateRoomId =
                                viewModel.coupleInfo.privateRoomId ?: targetUser.privateRoomId
                                ?: return@CouplePendantFloat
                            voiceLiveHelper.joinVoiceLiveRoom(privateRoomId, isPrivate = true)
                        } else {
                            scope.launch {
                                loading.runWithLoading {
                                    viewModel.getCPRightPageInfo()
                                }?.also {
                                    navController.navigate(
                                        CupidRouters.BECOME_CP,
                                        mapOf(Const.KEY_DATA to it)
                                    )
                                }
                            }
                        }
                    }
                },
                onAction = object : IC2CAction {
                    override fun onShowExchangeLineRequestDialog() {
                        scope.launch {
                            loading.runWithLoading {
                                val content =
                                    viewModel.getExchangeLineConfig() ?: return@runWithLoading
                                dialogQueue.push(LineExchangeDialog(content))
                            }
                        }
                    }

                    override fun onRequestExchangeLine() {
                        scope.launch {
                            loading.runWithLoading {
                                val (suc, tip) = viewModel.requestExchangeLine()
                                if (!suc) {
                                    dialogQueue.push(InsufficientIntimacyToLineExchangeDialog(tip))
                                }
                            }
                        }
                    }

                    override fun onShowIntimacyRuleDialog() {
                        val tableState = mutableStateOf(IntimacyLevelTable())
                        val tasks = mutableStateListOf<Task>()
                        scope.launch {
                            viewModel.fetchIntimacyLevelTable(targetUser.id).onSuccess {
                                tableState.value = it
                            }
                            viewModel.fetchIntimacyTaskList(targetUser.id).onSuccess {
                                tasks.addAll(it.tasks)
                            }
                        }
                        dialogQueue.push(
                            IntimacyPagePanelDialog(
                                targetUser,
                                selfUser,
                                tableState,
                                tasks
                            )
                        )
                    }

                    override fun onRequestJoinFamily(familyId: Int) {
                        scope.launch {
                            loading.value = true
                            CupidFamilyManager.joinTribe(familyId)
                                .onSuccess {
                                    loading.value = false
                                }.onFailure {
                                    loading.value = false
                                }.toastError()
                        }
                    }

                    override fun onRequestJoinVoiceRoom(roomId: Int) {
                        voiceLiveHelper.joinVoiceLiveRoom(roomId, true, from = "jp_pc_room_click")
                    }

                    override fun onChatPhrasesClicked(phrases: PhrasesSettingBean.MyPrologue) {
                        onSendMessage(MessageBundle.Text.create(phrases.content))
                    }

                    override fun onCurrencyGift(
                        coinGiftEnable: Boolean,
                        diamondGiftEnable: Boolean,
                    ) {
                        scope.launch {
                            loading.value = true
                            viewModel.getUserAccount()
                                .onSuccess {
                                    loading.value = false
                                    CurrencyGift.show(
                                        coinGiftEnable,
                                        diamondGiftEnable,
                                        it,
                                        dialogQueue,
                                        viewModel
                                    )
                                }.onFailure {
                                    loading.value = false
                                }.toastError()
                        }
                    }

                    override fun onConfirmCurrencyGift(orderId: String) {
                        scope.launch {
                            loading.value = true
                            viewModel.confirmCurrencyGiftOrder(orderId)
                                .onSuccess { toastRes(R.string.cpd赠送成功) }.toastError()
                            loading.value = false
                        }
                    }

                    override fun onAgreeCpInvite(code: String) {
                        scope.launch {
                            loading.runWithLoading {
                                viewModel.agreeHaveCp(code)
                            }
                        }
                    }

                    override fun goBecomeCouplePage() {
                        scope.launch {
                            loading.runWithLoading {
                                viewModel.getCPRightPageInfo()
                            }?.also {
                                navController.navigate(
                                    CupidRouters.BECOME_CP,
                                    mapOf(Const.KEY_DATA to it)
                                )
                            }
                        }
                    }

                    override fun onShowGiftPanel(position: GiftPosition?) {
                        giftViewModel.fetchGift()
                        dialogQueue.push(
                            C2CBottomGiftPanelDialog(
                                position,
                                giftViewModel.giftListModelState
                            ), true
                        )
                    }

                    override fun onSendGift(gift: CPGift, count: Int, extra: GiftExtra) {
                        giftViewModel.sendGift(gift, count, extra)
                    }

                    override fun onShowRechargePanel() {
                        dialogQueue.push(RechargeDialog, true)
                    }

                    override fun onShowReport() {
                        navController.navigate(
                            CupidRouters.REPORT_START,
                            mapOf(
                                "type" to ReportActivity.TYPE_USER,
                                "id" to targetUser.id
                            )
                        )
                    }

                    override fun onShowBlack() {
                        dialogQueue.push(BlackAlertDialog)
                    }

                    override fun onBlack(dialog: IDialog) {
                        dialog.dismiss()
                        scope.launch {
                            loading.runWithLoading {
                                viewModel.addToBlackList()
                            }
                        }
                    }

                    override fun onSendMessage(message: MessageBundle) {
                        IMCompatCore.sendC2CMessage(user.id, message)
                    }

                    override fun onSendMultipleMessage(messages: List<MessageBundle>) {
                        if (messages.isNotEmpty()) {
                            IMCompatCore.sendMessages(SendParams(user.id, ConversationType.C2C), messages)
                        } else {
                            toastRes(R.string.消息发送失败)
                        }
                    }

                    override fun onResendMessage(message: UCInstanceMessage) {
                        IMCompatCore.sendMessage(SendParams(user.id, ConversationType.C2C), message.base)
                    }

                    override fun onPreview(message: UCInstanceMessage) {
                        previewState.startPreview(previewKey = message.id)
                    }

                    override fun onNavigateTo(destination: DestinationRoute) {
                        navController.navigate(destination)
                    }

                    override fun onStartC2CCall(mode: CallMode) {
                        launcher.launch(
                            if (mode == CallMode.OnlyVoice) {
                                arrayOf(Manifest.permission.RECORD_AUDIO)
                            } else {
                                arrayOf(
                                    Manifest.permission.RECORD_AUDIO,
                                    Manifest.permission.CAMERA
                                )
                            }
                        )
                    }
                }
            )
        }

        giftViewModel.GiftEffectView()
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun C2CChatPage(
    targetUser: User,
    selfUser: User,
    messageList: List<MessageEntry<*>>,
    paginateState: PaginateState<*>,
    listState: LazyListState,
    panelState: KeyboardPanelState,
    dialogQueue: DialogQueue<IC2CAction>,
    hiddenModules: List<String>? = null,
    overlayContent: @Composable BoxScope.() -> Unit = {},
    safeContent: @Composable BoxScope.() -> Unit = {},
    onAction: IC2CAction = IC2CAction.Empty,
) {

    dialogQueue.DialogContent(onAction)

    val textFieldValue = rememberSaveable(stateSaver = TextFieldValue.Saver) {
        mutableStateOf(TextFieldValue())
    }

    val onBackPressedDispatcher = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher

    ChatRoomScaffold(
        panelState = panelState,
        modifier = Modifier.background(Color.White),
        topBar = {
            Column {
                CenterAlignedTopAppBar(
                    colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.White),
                    title = {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            CircleComposeImage(
                                model = targetUser.avatarUrl,
                                modifier = Modifier
                                    .size(32.dp)
                                    .noEffectClickable {
                                        onAction.navigateToProfile(targetUser.id)
                                    }
                            )
                            val hideIntimate = remember(hiddenModules) {
                                hiddenModules?.contains(UserConf.HIDE_INTIMATE) != false
                            }
                            Column(
                                modifier = Modifier
                                    .padding(horizontal = 6.dp)
                                    .noEffectClickable(enabled = !hideIntimate) {
                                        onAction.onShowIntimacyRuleDialog()
                                    },
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Image(
                                    painter = painterResource(id = R.drawable.ic_cpd_love_intimacy),
                                    contentDescription = null,
                                    modifier = Modifier.height(26.dp),
                                    contentScale = ContentScale.FillHeight
                                )
                                if (!hideIntimate) {
                                    AppText(
                                        text = "${stringResource(id = R.string.亲密度)}${targetUser.intimateScore}",
                                        color = Color(0xFFFF5E8B),
                                        fontSize = 10.sp
                                    )
                                }
                            }
                            CircleComposeImage(
                                model = selfUser.avatarUrl,
                                modifier = Modifier
                                    .size(32.dp)
                                    .noEffectClickable {
                                        onAction.navigateToProfile(selfUser.id)
                                    }
                            )
                        }
                    },
                    navigationIcon = {
                        IconButton(onClick = {
                            onBackPressedDispatcher?.onBackPressed()
                        }) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_cpd_navigation_back),
                                contentDescription = "back",
                                tint = Color(0xFF1D2129),
                            )
                        }
                    },
                    actions = {
                        IconButton(
                            onClick = {
                                dialogQueue.push(MoreActionDialog)
                            },
                        ) {
                            Icon(
                                imageVector = ActionIcons.More,
                                contentDescription = "menu",
                                tint = Color(0xFF1D2129)
                            )
                        }
                    }
                )
                HorizontalDivider(thickness = 0.5.dp, color = Color(0xFFF0F0F0))
            }
        },
        bottomBar = {
            C2CBottomBar(panelState, textFieldValue, hiddenModules, onAction)
        },
        panelContent = {
            C2CBottomPanel(panelState, textFieldValue, onAction)
        },
        overlayContent = overlayContent,
        safeContent = safeContent,
    ) {

        val isLoading by remember {
            derivedStateOf {
                paginateState.nextLoadState.isLoading
            }
        }

        val isEnd by remember {
            derivedStateOf {
                paginateState.nextLoadState.isEnd
            }
        }

        CompositionLocalProvider(LocalMsgAudioPlayer provides rememberMsgAudioPlayer()) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color(0xFFF5F7F9))
            ) {
                if (targetUser.currentRoomId != null) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color.Transparent)
                            .padding(horizontal = 16.dp, 8.dp)
                    ) {
                        AudioStateBar(user = targetUser, onAction::onRequestJoinVoiceRoom)
                    }
                }
                Column(
                    modifier = Modifier.topFadingEdge(
                        color = Color(0x15CCCCCC),
                        width = 20.dp,
                        spec = spring(),
                        isVisible = listState.canScrollBackward,
                    )
                ) {
                    LazyColumn(
                        modifier = Modifier
                            .withAutoHidePanel(panelState)
                            .fillMaxSize()
                            .overScrollVertical(),
                        state = listState,
                        contentPadding = if (targetUser.currentRoomId != null) PaddingValues(bottom = 16.dp) else
                            PaddingValues(vertical = 16.dp),
                        reverseLayout = true,
                        verticalArrangement = Arrangement.spacedBy(16.dp),
                        flingBehavior = rememberOverscrollFlingBehavior { listState }
                    ) {
                        loadMsgListLayout("c2c", messageList, onAction) {
                            C2CChatViewModel.getMsgLayoutContent(it, true)
                        }

                        if (isLoading) {
                            item(key = "isLoading", contentType = "isLoading") {
                                Box(
                                    modifier = Modifier
                                        .padding(top = 10.dp)
                                        .fillMaxWidth(),
                                    contentAlignment = Alignment.Center
                                ) {
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(16.dp),
                                        color = Color(0xFFFF5E8B),
                                        strokeWidth = 1.5.dp
                                    )
                                }
                            }
                        }

                        if (isEnd) {
                            item(key = "userCard", contentType = "userCard") {
                                UserCardItem(
                                    user = targetUser,
                                    modifier = Modifier
                                        .padding(horizontal = 16.dp)
                                        .fillMaxWidth()
                                        .clip(Shapes.corner12)
                                        .background(Color.White),
                                    onCellClick = {
                                        onAction.onNavigateTo(profileDestination(targetUser.id))
                                    }
                                )
                            }
                        }
                    }
                }
            }

            PhrasesMessageBox(
                targetUser,
                modifier = Modifier.align(Alignment.BottomCenter),
                panelState.inputFieldHasFocus
            ) {
                onAction.onChatPhrasesClicked(it)
            }

            //男用户快捷回复
            if (selfUser.isBoy) {
                val replyVM = viewModel<QuickReplyViewModel>()
                val visible by replyVM.visible
                val replys by replyVM.replyList
                val posting by replyVM.posting
                val scope = rememberCoroutineScope()
                LaunchedEffect(key1 = Unit) {
                    replyVM.request(targetUser.userId, false)
                }
                if (visible) {
                    ReportExposureCompose(exposureName = DataTrace.Exposure.QuickReply.VISIT_QUICK_REPLY_JP) {
                        MaterialTheme(
                            colorScheme = MaterialTheme.colorScheme.copy(
                                primary = Color(0xFF1D2129),
                                primaryContainer = Color.White,
                                onPrimaryContainer = Color(0xFF4E5969)
                            )
                        ) {
                            QuickReplyBoard(title = stringResource(R.string.cpd_quick_reply),
                                replyList = replys,
                                modifier = Modifier
                                    .padding(bottom = 12.dp)
                                    .width(343.dp)
                                    .align(Alignment.BottomCenter)
                                    .paint(
                                        painterResource(id = R.drawable.bg_quick_reply_ja),
                                        contentScale = ContentScale.FillBounds
                                    ),
                                onReply = {
                                    DataPoint.clickBody(DataTrace.Click.QuickReply.QUICK_REPLY_CLICK_JP)
                                        .report()
                                    replyVM.trySendMessage(targetUser.id, it)
                                },
                                posting = posting,
                                closeButton = {
                                    Image(
                                        painter = painterResource(id = R.drawable.ic_close_gray),
                                        contentDescription = "close",
                                        modifier = Modifier
                                            .size(14.dp)
                                            .click {
                                                replyVM.close()
                                            }
                                            .align(Alignment.CenterEnd)
                                    )
                                })
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun PhrasesMessageBox(
    targetUser: User,
    modifier: Modifier = Modifier,
    inputFocus: Boolean,
    onPhrasesSend: (PhrasesSettingBean.MyPrologue) -> Unit,
) {
    val vm = viewModel<PhrasesViewModel>()
    val myPhrasesList by vm.phrasesList.collectAsStateWithLifecycle()
    LaunchedEffect(key1 = Unit) {
        vm.refreshPhrasesList(targetUser.userId)
    }

    myPhrasesList?.let { list ->
        var showPopup by rememberSaveable {
            mutableStateOf(true)
        }
        LaunchedEffect(key1 = inputFocus) {
            if (inputFocus && showPopup) {
                showPopup = false
            }
        }
        val shouldShow by remember(inputFocus, list) {
            derivedStateOf {
                list.isNotEmpty() && showPopup && !inputFocus
            }
        }
        if (shouldShow) {
            PhrasesMessageBox(list, onClose = { showPopup = false }, onPhrasesSend = {
                onPhrasesSend(it)
            }, modifier = modifier)
        }
    }
}


@Composable
private fun UserCardItem(
    user: User,
    modifier: Modifier = Modifier,
    onCellClick: OnClick = {},
    onButtonClick: OnClick = { onCellClick() },
) {
    Row(
        modifier = modifier
            .clickable(onClick = onCellClick)
            .padding(12.dp)
    ) {
        Box(modifier = Modifier.size(48.dp)) {
            ComposeImage(
                model = user.avatarUrl, modifier = Modifier
                    .fillMaxSize()
                    .clip(CircleShape)
            )

            if (user.onlineStatus == 0) {
                Spacer(
                    modifier = Modifier
                        .size(10.dp)
                        .clip(CircleShape)
                        .background(Color(0xFF18E046))
                        .border(1.dp, Color.White, CircleShape)
                        .align(Alignment.BottomEnd)
                )
            }
        }
        Column(
            modifier = Modifier
                .animateContentSize()
                .fillMaxWidth()
                .padding(start = 8.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier
                        .animateContentSize()
                        .weight(1f),
                    verticalArrangement = Arrangement.spacedBy(2.dp),
                ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Text(
                            modifier = Modifier.weight(1f, false),
                            text = user.nickname,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                        Spacer(modifier = Modifier.width(2.dp))
                        AgeGender(age = user.age, isBoy = user.isBoy)
                    }

                    val tags = remember(user.locationLabel, user.height, user.career) {
                        listOf(
                            user.locationLabel.ifEmpty {
                                user.nativeProfile?.cityCode?.name
                            },
                            if (user.height > 0) "${user.height}cm" else "",
                            user.career.ifEmpty {
                                user.nativeProfile?.job?.name
                            }
                        ).filter { !it.isNullOrBlank() }
                            .joinToString(separator = " | ")
                    }
                    if (tags.isNotEmpty()) {
                        //标签
                        Text(text = tags, fontSize = 12.sp, color = Color(0xFF4E5969))
                    }
                }

                AppButton(
                    text = stringResource(id = R.string.cpd主页),
                    modifier = Modifier
                        .padding(start = 4.dp, top = 4.dp)
                        .widthIn(min = 56.dp)
                        .height(24.dp),
                    background = Color.Transparent,
                    color = Color(0xFFFF5E8B),
                    fontSize = 12.sp,
                    border = BorderStroke(0.5.dp, Color(0xFFFF5E8B)),
                    contentPadding = PaddingValues(horizontal = 10.dp),
                    onClick = onButtonClick
                )
            }

            Spacer(modifier = Modifier.height(4.dp))
            //简介
            Text(
                text = user.shortIntro.ifEmpty { stringResource(id = user.introEmptyRes) },
                fontSize = 12.sp,
                color = Color(0xFF86909C),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )

            val images = remember {
                user.albumList.asReversed().take(4)
            }
            if (images.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    images.forEach { item ->
                        ComposeImage(
                            model = item.url, modifier = Modifier
                                .size(60.dp)
                                .clip(RoundedCornerShape(8.dp))
                        )
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewUserCardItem() {
    PreviewCupidTheme {
        UserCardItem(user = userForPreview)
    }
}

@Preview
@Composable
private fun PreviewChatRoomPage() {
    val listState = rememberLazyListState()
    C2CChatPage(
        targetUser = userForPreview,
        selfUser = userForPreview,
        messageList = emptyList(),
        paginateState = rememberPaginateState(""),
        listState = listState,
        dialogQueue = remember {
            DialogQueue()
        },
        panelState = rememberPanelState(
            listState,
            arrayOf(
                IMPanel(AudioPanel, autoHideEnable = false, applyNavigationBarsPadding = true),
                IMPanel(EmojiPanel)
            )
        )
    )
}

@Parcelize
data class RechargeCoinToChatPanelDialog(
    val title: String,
    val hint: String,
    val leftAvatar: String,
    val rightAvatar: String,
) : SimpleAnimatedDialog(), Parcelable {

    @Composable
    override fun Content(dialog: IDialog) {
        RechargeCoinToChatPanel(title, hint, leftAvatar, rightAvatar) {
            dismiss()
        }
    }
}

@Parcelize
data class LineExchangeDialog(private val content: String) : NormalDialog<IC2CAction>(),
    Parcelable {

    @Composable
    override fun Content(dialog: IDialog, onAction: IC2CAction?) {
        LineExchangeContent(content) {
            onAction?.onRequestExchangeLine()
            dialog.dismiss()
        }
    }
}

@Parcelize
data class InsufficientIntimacyToLineExchangeDialog(private val content: String) :
    NormalDialog<IC2CAction>(), Parcelable {

    @Composable
    override fun Content(dialog: IDialog, onAction: IC2CAction?) {
        InsufficientIntimacyToLineExchangeContent(content.ifEmpty {
            stringResource(id = R.string.亲密度不足)
        }, {
            dialog.dismiss()
        }) {
            onAction?.onShowGiftPanel()
            dialog.dismiss()
        }
    }
}

@Parcelize
data class IntimacyRuleDialog(private val title: String?, private val content: String?) :
    NormalDialog<IC2CAction>(), Parcelable {

    @Composable
    override fun Content(dialog: IDialog, onAction: IC2CAction?) {
        IntimacyRuleContent(title.orEmpty().ifEmpty {
            stringResource(id = R.string.cpd亲密度规则)
        }, content.orEmpty()) {
            dialog.dismiss()
        }
    }
}


@Composable
private fun RechargeCoinToChatPanel(
    title: String,
    hint: String,
    leftAvatar: String = "",
    rightAvatar: String = "",
    onDismiss: OnClick = {}
) {
    val vm = viewModel<WalletViewModel>()
    LaunchedEffect(key1 = vm) {
        vm.actionFlow.onEach {
            when (it) {
                WalletViewModel.ACTION_OPEN_AGENT_CHAT -> {
                    onDismiss()
                }

                else -> {}
            }
        }.launchIn(this)
    }
    RechargePageScaffold(viewModel = vm) { purchaseHelper, flow, onConfirm ->
        RechargeCoinToChatPanel(
            purchaseHelper = purchaseHelper,
            flow = flow,
            title = title,
            hint = hint,
            leftAvatar = leftAvatar,
            rightAvatar = rightAvatar,
            onConfirm = onConfirm
        )
    }
}


@Composable
private fun RechargeCoinToChatPanel(
    purchaseHelper: AppPurchaseHelper,
    flow: Flow<List<Purchase>>,
    title: String = stringResource(id = R.string.充值金币继续和她聊天),
    hint: String = stringResource(R.string.你的免费聊天次数已用完),
    leftAvatar: String = "",
    rightAvatar: String = "",
    onConfirm: () -> Unit = {},
) {
    val density = LocalDensity.current
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 16.dp)
            .background(Color.White, RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp))
            .paint(
                painterResource(id = R.drawable.bg_top_recharge_coin_to_chat),
                contentScale = ContentScale.FillWidth,
                alignment = Alignment.TopCenter
            )
            .padding(bottom = with(density) {
                WindowInsets.navigationBars
                    .getBottom(density)
                    .toDp()
            }.coerceAtLeast(15.dp))
    ) {

        Row(
            modifier = Modifier
                .align(Alignment.TopCenter)
                .offset(y = (-16).dp),
            horizontalArrangement = Arrangement.spacedBy((-16).dp)
        ) {
            CircleComposeImage(
                model = leftAvatar, modifier = Modifier
                    .size(80.dp)
                    .border(0.75.dp, Color.White, CircleShape)
            )
            CircleComposeImage(
                model = rightAvatar, modifier = Modifier
                    .size(80.dp)
                    .border(0.75.dp, Color.White, CircleShape)
            )
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 80.dp, start = 16.dp, end = 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            Text(
                text = title,
                color = Color(0xFF1D2129),
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(10.dp))

            Text(
                text = hint,
                color = Color(0xFF4E5969),
                fontSize = 12.sp,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(15.dp))

            RechargePageContent(
                purchaseHelper = purchaseHelper,
                flow = flow,
                exposureName = DataTrace.Exposure.Recharge.私聊消息拦截_充值页面,
                clickName = DataTrace.Click.Recharge.私聊消息拦截_充值页面_充值按钮点击,
                onConfirm = onConfirm
            )
        }
    }
}

@Composable
private fun LineExchangeContent(content: String, onConfirm: OnClick = {}) {
    IconAlertDialog(
        icon = {
            Image(
                painter = painterResource(id = R.drawable.ic_cpd_line_app),
                contentDescription = null,
                modifier = Modifier.size(48.dp)
            )
        },
        content = content,
        endButton = DialogButton(stringResource(id = R.string.申请交换)) {
            onConfirm()
        }
    )
}

@Composable
private fun InsufficientIntimacyToLineExchangeContent(
    content: String,
    onClose: OnClick = {},
    onShowGiftPanel: OnClick = {},
) {
    ContentAlertDialog(
        content = content,
        startButton = DialogButton(stringResource(id = R.string.继续聊天)) {
            onClose()
        },
        endButton = DialogButton(stringResource(id = R.string.赠送礼物)) {
            onShowGiftPanel()
        }
    )
}

@Composable
private fun IntimacyRuleContent(
    title: String = stringResource(id = R.string.cpd亲密度规则),
    content: String = stringResource(id = R.string.亲密度不足),
    onClose: OnClick = {},
) {
    TitleAlertDialog(
        title = title,
        content = content,
        endButton = DialogButton(stringResource(id = R.string.cpd我知道了)) {
            onClose()
        }
    )
}

@Composable
private fun ChatPhrasesFloatWidget(
    modifier: Modifier = Modifier,
    onClick: OnClick = {},
) {
    val vm = viewModel<PhrasesViewModel>()
    val floatWidgetSetting by vm.floatWidget.collectAsStateWithLifecycle()
    LaunchedEffect(key1 = Unit) {
        if (floatWidgetSetting == null) {
            vm.refreshFloatWidgetSetting()
        }
    }
    floatWidgetSetting?.let { setting ->
        AlignHorizontalContainer(
            visible = setting.isShow,
            modifier = modifier,
            tag = "ChatPhrasesFloatWidget"
        ) {
            Column(
                modifier = Modifier
                    .width(68.dp)
                    .heightIn(90.dp)
                    .background(color = CpdColors.FFFFEAF0, shape = RoundedCornerShape(12.dp))
                    .border(
                        1.dp,
                        brush = Brush.verticalGradient(
                            listOf(
                                Color(0xFFFFD4E0),
                                Color(0xFFFFA9C2)
                            )
                        ),
                        shape = RoundedCornerShape(12.dp)
                    )
                    .click {
                        onClick()
                    }
                    .padding(6.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_cpd_phrases_float),
                    contentDescription = "",
                    modifier = Modifier.size(56.dp, 32.dp)
                )
                Text(
                    text = stringResource(id = R.string.cpd问候语设置),
                    fontSize = 12.sp,
                    color = CpdColors.FF1D2129,
                    lineHeight = 14.sp,
                    modifier = Modifier.padding(top = 4.dp, bottom = 2.dp)
                )
                val annotatedString = buildAnnotatedString {
                    append("+${setting.reward}")
                    appendInlineContent(id = "diamond")
                }
                val inlineMap = mapOf("diamond" to InlineTextContent(
                    Placeholder(12.sp, 12.sp, PlaceholderVerticalAlign.TextCenter)
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_cpd_diamond),
                        contentDescription = "",
                        modifier = Modifier
                            .padding(start = 2.dp)
                            .fillMaxSize()
                    )
                })
                Text(
                    annotatedString,
                    inlineContent = inlineMap,
                    fontSize = 12.sp,
                    color = CpdColors.FFFF5E8B,
                    fontWeight = FontWeight.SemiBold
                )
            }
        }
    }
}


@Preview
@Composable
private fun PreviewRechargeCoinToChatPanel() {
    RechargeCoinToChatPanel(purchaseHelper = AppPurchaseHelper(), flow = emptyFlow())
}

@Preview
@Composable
private fun PreviewLineExchange() {
    LineExchangeContent(stringResource(id = R.string.为了保持社交礼仪))
}

@Preview
@Composable
private fun PreviewInsufficientIntimacyToLineExchange() {
    InsufficientIntimacyToLineExchangeContent(stringResource(id = R.string.亲密度不足))
}


@Preview
@Composable
private fun PreviewIntimacyRuleContent() {
    IntimacyRuleContent()
}
