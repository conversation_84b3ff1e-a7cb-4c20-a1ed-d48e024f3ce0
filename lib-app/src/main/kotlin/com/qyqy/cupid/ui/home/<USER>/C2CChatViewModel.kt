package com.qyqy.cupid.ui.home.message

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewModelScope
import com.overseas.common.utils.TaskRunMode
import com.qyqy.cupid.data.CoupleInfo
import com.qyqy.cupid.event.IMEvent
import com.qyqy.cupid.im.messages.CallMessageContent
import com.qyqy.cupid.im.messages.CurrencyGiftMessageContent
import com.qyqy.cupid.im.messages.ExchangeLineMessageContent
import com.qyqy.cupid.im.messages.GiftGuideMessageContent
import com.qyqy.cupid.im.messages.GiftMessageContent
import com.qyqy.cupid.im.messages.GuardMessageContent
import com.qyqy.cupid.im.messages.ImageMessageContent
import com.qyqy.cupid.im.messages.InviteToCoupleMessage
import com.qyqy.cupid.im.messages.MsgLayoutContent
import com.qyqy.cupid.im.messages.RecallMessageContent
import com.qyqy.cupid.im.messages.ReceiveInviteToCronyMessage
import com.qyqy.cupid.im.messages.RechargeRewardMessageContent
import com.qyqy.cupid.im.messages.SendInviteToCronyMessage
import com.qyqy.cupid.im.messages.ShareFamilyMessageContent
import com.qyqy.cupid.im.messages.ShareVoiceRoomMessageContent
import com.qyqy.cupid.im.messages.StartCallTipMessageContent
import com.qyqy.cupid.im.messages.SystemTipMessageContent
import com.qyqy.cupid.im.messages.TextMessageContent
import com.qyqy.cupid.im.messages.ToCronyResultTipMessage
import com.qyqy.cupid.im.messages.TxTImgContent
import com.qyqy.cupid.im.messages.VoiceMessageContent
import com.qyqy.cupid.im.messages.WithdrawalMessageContent
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AccountRepository
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.app
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.presentation.room.AudioPanel
import com.qyqy.ucoo.compose.presentation.room.EmojiPanel
import com.qyqy.ucoo.compose.presentation.room.IMPanel
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.http.ApiException
import com.qyqy.ucoo.http.getStringOrNull
import com.qyqy.ucoo.im.bean.CPRightPageInfo
import com.qyqy.ucoo.im.bean.Scene
import com.qyqy.ucoo.im.chat.ChatRepository
import com.qyqy.ucoo.im.compat.ConversationType
import com.qyqy.ucoo.im.compat.SendParams
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.UCGiftMessage
import com.qyqy.ucoo.im.compat.UCImageMessage
import com.qyqy.ucoo.im.compat.UCInstanceMessage
import com.qyqy.ucoo.im.compat.UCTextMessage
import com.qyqy.ucoo.im.compat.UCVideoCallMessage
import com.qyqy.ucoo.im.compat.UCVoiceMessage
import com.qyqy.ucoo.im.compat.chat.IMMessageConfig
import com.qyqy.ucoo.im.compat.chat.ListStateMessageViewModel
import com.qyqy.ucoo.im.compat.isRevoked
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.sUserFlow
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.user.UserManager
import com.qyqy.ucoo.user.UserRepository
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class C2CChatViewModel(
    private val user: AppUser,
) : ListStateMessageViewModel(
    SendParams(receiver = user.id, type = ConversationType.C2C),
    IMMessageConfig(autoPlayNoPlayedGiftEffect = true, autoMarkReadReceipt = true)
) {

    companion object {

        fun getMsgLayoutContent(message: UCInstanceMessage, includeRevoked: Boolean): MsgLayoutContent? {
            if (includeRevoked && message.isRevoked) {
                return RecallMessageContent
            }
            return when (message) {
                is UCTextMessage -> {
                    TextMessageContent
                }

                is UCImageMessage -> {
                    ImageMessageContent
                }

                is UCVoiceMessage -> {
                    VoiceMessageContent
                }

                is UCGiftMessage -> {
                    GiftMessageContent
                }

                is UCVideoCallMessage.Other -> {
                    if (message.showInMsgList) {
                        CallMessageContent
                    } else {
                        null
                    }
                }


                is UCCustomMessage -> {
                    getMsgLayoutContentByCustomMsg(message)
                }

                else -> null
            }
        }

        private fun getMsgLayoutContentByCustomMsg(message: UCCustomMessage): MsgLayoutContent? {
            return when (message.cmd) {

                IMEvent.EXCHANGE_CONTACT -> {
                    ExchangeLineMessageContent
                }

                MsgEventCmd.PRIVATE_SYSTEM_HINT, MsgEventCmd.PRIVATE_SYSTEM_HINT_V2 -> {
                    SystemTipMessageContent
                }

                MsgEventCmd.INVITE_TO_TRIBE -> {
                    ShareFamilyMessageContent
                }

                MsgEventCmd.INVITE_TO_ROOM -> {
                    ShareVoiceRoomMessageContent
                }

                MsgEventCmd.GUIDE_VIDEO_CHAT -> {
                    StartCallTipMessageContent
                }

                MsgEventCmd.GUARD_CHANGE_EVENT -> {
                    GuardMessageContent
                }

                IMEvent.JAPAN_FIRST_CHARGE_AWARD -> {
                    RechargeRewardMessageContent
                }

                IMEvent.JAPAN_WITHDRAW_COUPON_CODE -> {
                    WithdrawalMessageContent
                }

                IMEvent.PRIVATECHAT_JP_INTIMATE_GIFT_MSG -> {
                    GiftGuideMessageContent
                }

                IMEvent.PRIVATECHAT_JP_TRANSFER_RECORD -> {
                    CurrencyGiftMessageContent
                }

                MsgEventCmd.INVITE_TO_JOIN_RELATIVE_GROUP -> {
                    val sendUser = message.getJsonValue<AppUser>("inviter")
                    if (sendUser?.isSelf == true) {
                        SendInviteToCronyMessage
                    } else {
                        ReceiveInviteToCronyMessage
                    }
                }

                MsgEventCmd.ACCEPT_TO_JOIN_RELATIVE_GROUP, MsgEventCmd.RELATIVE_GROUP_BROKEN -> {
                    ToCronyResultTipMessage
                }

                MsgEventCmd.CONFIRM_CP, MsgEventCmd.GIVE_CONFESSION_GIFT -> {
                    InviteToCoupleMessage
                }

                MsgEventCmd.PRIVATECHAT_OFFICIAL_H5_LINK -> {
                    TxTImgContent
                }

                else -> null
            }
        }
    }

    val panels: Array<IMPanel> = arrayOf(
        IMPanel(AudioPanel, forceClearFocus = true), // 录音面板
        IMPanel(EmojiPanel, forceRequestFocus = true) // 表情面板
    )

    private val chatRepo = ChatRepository()

    private val userRepo = UserRepository()

    private val accountRepo = AccountRepository()

    private val selfUserFlow = sUserFlow

    val targetUserFlow = UserManager.getUserFlowById(id = user.id).stateIn(viewModelScope, SharingStarted.Eagerly, user)

    var coupleInfo: CoupleInfo by mutableStateOf(CoupleInfo.empty)
        private set

    init {
        UserManager.fetchUserById(id = user.id, mode = TaskRunMode.Remote)
        refreshCoupleInfo()
    }

    override fun getUser(id: String): User? {
        return when (id) {
            selfUserFlow.value.id -> selfUserFlow.value
            targetUserFlow.value.id -> targetUserFlow.value
            else -> super.getUser(id)
        }
    }

    override fun filterShownMessage(message: UCInstanceMessage): Boolean {
        return getMsgLayoutContent(message, false) != null
    }

    override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
        when (message.cmd) {
            MsgEventCmd.CONFIRM_CP -> {
                refreshCoupleInfo()
            }
        }
    }

    fun handleAction(action: Int) {
        when (action) {
            C2CChatConst.ACTION_SAY_HI -> sayHi()
            else -> {}
        }
    }

    suspend fun addToBlackList() = userRepo.updateBlackShip(user.id, true).onSuccess {
        toastRes(R.string.cupid_result_success)
    }.toastError()

    suspend fun getExchangeLineConfig() = chatRepo.getExchangeLineConfig()
        .map {
            it.getStringOrNull("msg") ?: app.getString(R.string.为了保持社交礼仪)
        }.toastError().getOrNull()

    suspend fun requestExchangeLine(): Pair<Boolean, String> {
        chatRepo.requestExchangeLine(user.id).onFailure {
            if (it is ApiException && it.code == -20) {
                return false to it.msg
            }
        }.toastError()
        return true to ""
    }

    suspend fun getIntimacyRule(): Pair<String?, String?>? {
        return chatRepo.fetchIntimacyRule().toastError().map {
            it.getStringOrNull("title") to it.getStringOrNull("content")
        }.getOrNull()
    }

    suspend fun fetchIntimacyLevelTable(userId: String) = chatRepo.fetchIntimacyLevelTable(userId)

    suspend fun fetchIntimacyTaskList(userId: String) = chatRepo.fetchIntimacyTaskList(userId)

    fun getHiddenModulesFlow() = UIConfig.configFlow.map {
        it.getOrNull()?.hideJapanEntries
    }.combine(flow {
        emit(chatRepo.getCurrencyGiftConfig(user.id).getOrNull()?.map { it.toString() })
    }) { hide: List<String>?, show: List<String>? ->
        if (hide == null && show == null) {
            null
        } else {
            buildList<String> {
                if (hide != null) {
                    addAll(hide)
                }
                if (show != null) {
                    addAll(show)
                }
            }
        }
    }.stateIn(viewModelScope, SharingStarted.Eagerly, UIConfig.userConf.hideJapanEntries)

    suspend fun getUserAccount() = accountRepo.fetchUserAccount().map {
        it.accountInfo
    }

    suspend fun createCurrencyGiftOrder(count: Int, type: Int) = accountRepo.createCurrencyGiftOrder(user.id, count, type)

    suspend fun confirmCurrencyGiftOrder(orderId: String) = accountRepo.confirmCurrencyGiftOrder(orderId)

    private fun sayHi() {
        viewModelScope.launch {
            chatRepo.sayHiJapan(user.id)
        }
    }

    private fun refreshCoupleInfo() {
        if (user.gender != sUser.gender) {
            viewModelScope.launch {
                userRepo.getCoupleInfoById(user.userId).onSuccess {
                    coupleInfo = it
                }
            }
        }
    }

    suspend fun getCPRightPageInfo(): CPRightPageInfo? {
        return userRepo.getHaveCpInfo(user.id).onFailure { refreshCoupleInfo() }.toastError().getOrNull()
    }

    suspend fun agreeHaveCp(code: String) {
        userRepo.agreeHaveCp(code, Scene.NONE, 0)
            .toastError()
    }
}
