package com.qyqy.cupid.ui.home.message

import android.annotation.SuppressLint
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.im.compat.MessageBundle
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.utils.ComposeContent
import com.qyqy.ucoo.utils.OnClick

@Composable
fun MenuItemButton(
    imageVector: ImageVector,
    onClick: OnClick,
    modifier: Modifier = Modifier,
    tint: Color = LocalContentColor.current
) {
    IconButton(onClick = onClick, modifier = modifier.size(36.dp)) {
        Icon(imageVector = imageVector, contentDescription = "", tint = tint)
    }
}

@Composable
fun ChatBottomBar(
    textFieldValue: MutableState<TextFieldValue>,
    sendButtonVisible: Boolean,
    emojiMenuItem: ComposeContent,
    menuItems: ComposeContent,
    @SuppressLint("ModifierParameter")
    textFieldModifier: Modifier = Modifier,
    onAction: IIMAction,
) {
    Spacer(modifier = Modifier.height(12.dp))

    Row(
        modifier = Modifier.padding(horizontal = 16.dp),
        verticalAlignment = Alignment.Bottom
    ) {

        Box(
            modifier = Modifier
                .clip(RoundedCornerShape(20.dp))
                .weight(1f)
                .heightIn(min = 40.dp)
                .background(Color(0xFFF6F7FB)),
            contentAlignment = Alignment.CenterStart
        ) {

            // 不能隐藏会抛异常
            BasicTextField(
                value = textFieldValue.value,
                onValueChange = {
                    textFieldValue.value = it
                },
                modifier = textFieldModifier,
                textStyle = TextStyle.Default.copy(
                    color = Color(0xFF1D2129), fontSize = 14.sp
                ),
                maxLines = 6,
                cursorBrush = SolidColor(MaterialTheme.colorScheme.primary),
                decorationBox = { innerTextField ->
                    innerTextField()
                    if (textFieldValue.value.text.isEmpty()) {
                        AppText(text = stringResource(id = R.string.输入新消息), fontSize = 14.sp, color = Color(0xFF86909C))
                    }
                })
        }

        Row(
            modifier = Modifier.height(40.dp), verticalAlignment = Alignment.CenterVertically
        ) {
            Spacer(modifier = Modifier.width(6.dp))
            emojiMenuItem()
            AnimatedVisibility(visible = sendButtonVisible) {
                AppButton(
                    text = stringResource(id = R.string.cpd发送),
                    modifier = Modifier
                        .padding(start = 6.dp)
                        .size(60.dp, 28.dp),
                    fontSize = 14.sp,
                    textStyle = LocalTextStyle.current.copy(fontWeight = FontWeight.Medium)
                ) {
                    val text = textFieldValue.value.text
                    if (text.isNotBlank()) {
                        onAction.onSendMessage(MessageBundle.Text.create(text))
                        textFieldValue.value = TextFieldValue()
                    } else {
                        toastRes(R.string.不能发送空白消息)
                    }
                }
            }
        }
    }
    menuItems()
}