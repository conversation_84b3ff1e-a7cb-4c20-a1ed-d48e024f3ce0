

package com.qyqy.cupid.ui.home.mine

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.cupid.data.users.introEmptyRes
import com.qyqy.cupid.model.GiftWallViewModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.IHomeAction
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.NavListener
import com.qyqy.cupid.ui.home.mine.crony.CpZonePage
import com.qyqy.cupid.ui.home.mine.crony.CronyHomePage
import com.qyqy.cupid.ui.home.mine.crony.CronyViewModel
import com.qyqy.cupid.ui.home.mine.edit.ChangeableProperty
import com.qyqy.cupid.ui.home.mine.edit.CupidEditViewModel
import com.qyqy.cupid.ui.home.mine.edit.editorController
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.cupid.ui.navigateToRelationPage
import com.qyqy.cupid.ui.navigatorToProfileEdit
import com.qyqy.cupid.ui.profile.ProfileInformation
import com.qyqy.cupid.ui.profile.ProfileIntro
import com.qyqy.cupid.ui.profile.getDisplayInfoList
import com.qyqy.cupid.ui.relations.Relations
import com.qyqy.cupid.ui.relations.family.icons.IconFamily
import com.qyqy.cupid.utils.eventbus.EventBusListener
import com.qyqy.cupid.widgets.CpdAppScrollableTabRow
import com.qyqy.cupid.widgets.CupidGiftWallPage
import com.qyqy.cupid.widgets.GiftWallContainerStyle
import com.qyqy.cupid.widgets.LevelComposeView
import com.qyqy.cupid.widgets.TextLabel
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.LocalTracePage
import com.qyqy.ucoo.R
import com.qyqy.ucoo.TraceConst
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.isInvalid
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.accountManager
import com.qyqy.ucoo.compose.ComposeLifecycleObserve
import com.qyqy.ucoo.compose.data.CategoryGiftWall
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.D_DIN
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AgeGender
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.sUserFlow
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.launch

private data class MineOption(@DrawableRes val icon: Int, val titleRes: Int, val onClick: OnClick)

private interface MinePageAction {
    data object GoCoinPage : MinePageAction // 去我的金币页
    data object GoLevelPage : MinePageAction // 去我的等级页面
    data object GoSuitPage : MinePageAction // 去我的等级页面
    data object GoDefendPage : MinePageAction // 去我的守护页

    // 常用功能
    data object GoVipPage : MinePageAction // 去会员特典
    data object GoVisitorPage : MinePageAction // 去访问记录
    data object GoCustomerServicePage : MinePageAction // 访问客服
    data object GoSettings : MinePageAction // 去设置
}

@Composable
fun CpdMinePage(uiActionCallback: IHomeAction) {

    val user by sUserFlow.collectAsStateWithLifecycle()

    val giftWallViewModel = viewModel(
        modelClass = GiftWallViewModel::class.java,
        factory = viewModelFactory {
            initializer {
                GiftWallViewModel(user.id)
            }
        }
    )
    val giftWallDataState = giftWallViewModel.giftWallData.collectAsStateWithLifecycle()

    LaunchedEffect(key1 = Unit) {
        giftWallViewModel.refresh()
    }

    val context = LocalContext.current

    val viewModel = viewModel<CronyViewModel> {
        CronyViewModel(user.id)
    }

    LaunchedEffect(Unit) {
        viewModel.fetchCronyTable()
    }

    if (user.publicCP != null) {
        ComposeLifecycleObserve { event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                viewModel.fetchCpZone()
            }
        }
    }

    val tabList by remember(viewModel) {
        derivedStateOf {
            buildList {
                if (viewModel.cpZone != null) {
                    add(CpdMineTab.CpZone(context.getString(R.string.cpd情侣空间)))
                }
                add(CpdMineTab.Crony(context.getString(R.string.cpd亲密关系)))
                add(CpdMineTab.Info(context.getString(R.string.cpd_mine_profile)))
                giftWallDataState.value.forEach {
                    add(CpdMineTab.Gift(it))
                }
            }
        }
    }

    val controller = LocalAppNavController.current

    LaunchedEffect(key1 = user.id) {
        if (!user.isInvalid()) {
            accountManager.refreshSelfUserByRemote()
        }
    }
    MineContent(
        user = user,
        tabList = tabList,
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painter = painterResource(id = R.drawable.cupid_header_home),
                contentScale = ContentScale.FillWidth,
                alignment = Alignment.TopStart
            )
            .padding(horizontal = 16.dp)
            .statusBarsPadding(),
        uiActionCallback,
        remember {
            { action ->
                when (action) {
                    MinePageAction.GoCoinPage -> {
                        controller.navigate(CupidRouters.MyCoin)
                        Analytics.reportClickEvent(TracePoints.MY_PAGE_WALLET)
                    }

                    MinePageAction.GoVipPage -> {
                        Analytics.reportClickEvent(TracePoints.MY_PAGE_MEMBER_ENTRY)
                        controller.navigate(CupidRouters.MEMBERSHIP_ACTIVE_PAGE)
                    }

                    MinePageAction.GoLevelPage -> {
                        Analytics.reportClickEvent(TracePoints.MY_PAGE_RANGE)
                        controller.navigateWeb("${Const.Url.baseURL}/h5/ja/level")
                    }

                    MinePageAction.GoSuitPage -> {
                        Analytics.reportClickEvent(TracePoints.MY_PAGE_DRESSUP)
                        controller.navigate(CupidRouters.DRESS_UP)
                    }

                    MinePageAction.GoDefendPage -> {
                        Analytics.reportClickEvent(TracePoints.CLICK_MY_PAGE_GUARD)
                        controller.navigate(CupidRouters.MY_GUARD)
                    }

                    MinePageAction.GoVisitorPage -> {
                        Analytics.reportClickEvent(TracePoints.CLICK_MY_PAGE_VISITORS)
                        controller.navigate(CupidRouters.VISITORS)
                    }

                    MinePageAction.GoCustomerServicePage -> {
                        Analytics.reportClickEvent(TracePoints.CLICK_MY_PAGE_SUPPORT)
                        controller.navigate(CupidRouters.FEEDBACK)
                    }

                    MinePageAction.GoSettings -> {
                        uiActionCallback.onNavigateTo(CupidRouters.SETTINGS)
                    }
                }
            }
        }
    )
}

@Composable
private fun MineContent(
    user: User,
    tabList: List<CpdMineTab>,
    modifier: Modifier = Modifier,
    uiActionCallback: IHomeAction = IHomeAction.Empty,
    onAction: (MinePageAction) -> Unit = {},
) {
    Column(modifier = modifier.verticalScroll(rememberScrollState())) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(44.dp),
            horizontalArrangement = Arrangement.End
        ) {
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .aspectRatio(1f)
                    .clickable {
                        uiActionCallback.onNavigateTo(CupidRouters.SETTINGS)
                    },
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_cpd_mine_settings),
                    contentDescription = "setting"
                )
            }
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            CircleComposeImage(
                model = user.avatarUrl,
                modifier = Modifier
                    .size(80.dp)
                    .noEffectClickable {
                        uiActionCallback.navigateToProfile(user.id)
                    },
            )

            MineBriefInfo(
                user = user,
                modifier = Modifier
                    .padding(horizontal = 8.dp)
                    .weight(1f)
                    .click(noEffect = true) {
                        uiActionCallback.navigatorToProfileEdit()
                    }
            )
            Icon(
                painter = painterResource(id = R.drawable.ic_arrow_right),
                contentDescription = "",
                modifier = Modifier.size(12.dp),
                tint = MaterialTheme.typography.labelMedium.color
            )
        }

        //region 好友 关注 粉丝

        // 关注，好友数，
        val labelFocus = stringResource(R.string.cpd_focus)
        val labelFans = stringResource(R.string.label_fans)
        val labelFriend = stringResource(R.string.label_friend)
        val countList = remember(user.followCount, user.fansCount, user.friendCnt) {
            buildList {
                add(labelFocus to user.followCount)
                add(labelFans to user.fansCount)
                add(labelFriend to user.friendCnt)
            }
        }
        Spacer(modifier = Modifier.height(20.dp))
        Row(modifier = Modifier.fillMaxWidth()) {
            countList.forEach { c ->
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .click {
                            uiActionCallback.navigateToRelationPage(
                                when (c.first) {
                                    labelFocus -> Relations.FOCUS
                                    labelFans -> Relations.FANS
                                    else -> Relations.FRIENDS
                                }
                            )
                        },
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(text = c.first, style = MaterialTheme.typography.bodyMedium)
                    Spacer(modifier = Modifier.height(10.dp))
                    Text(
                        text = c.second.toString(),
                        style = MaterialTheme.typography.headlineMedium,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
        Spacer(modifier = Modifier.height(20.dp))

        //endregion

        //region 公共功能模块
        val options = remember {
            listOf(
                MineOption(
                    R.drawable.ic_cpd_mine_coin,
                    R.string.cupid_mine_gold,
                    { onAction(MinePageAction.GoCoinPage) }
                ),
                MineOption(
                    R.drawable.ic_cpd_mine_vip,
                    R.string.cpd_mine_vip,
                    { onAction(MinePageAction.GoVipPage) }
                ),
                MineOption(
                    R.drawable.ic_cpd_mine_level,
                    R.string.cpd_mine_level,
                    { onAction(MinePageAction.GoLevelPage) }
                ),
                MineOption(
                    R.drawable.ic_cpd_mine_defend,
                    R.string.cpd_mine_defend,
                    { onAction(MinePageAction.GoDefendPage) }
                ),
            )
        }

        Row(
            modifier = Modifier
                .background(
                    Color.White,
                    RoundedCornerShape(12.dp),
                ),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            options.forEach {
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(vertical = 10.dp)
                        .click {
                            it.onClick()
                        },
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    ComposeImage(model = it.icon, modifier = Modifier.size(40.dp))
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        stringResource(id = it.titleRes),
                        color = CpdColors.FF1D2129,
                        fontSize = 12.sp
                    )
                }
            }
        }
        //endregion
        val nav = LocalAppNavController.current
        Spacer(modifier = Modifier.height(10.dp))
        //region 常用模块
        val usuallyOptions = remember {
            listOf(
                MineOption(
                    R.drawable.cpd_icon_mine_dress_up,
                    R.string.cpd_mine_suit
                ) { onAction(MinePageAction.GoSuitPage) },
                MineOption(R.drawable.cpd_shopping_bag, R.string.cpd_装扮商城) {
                    nav.navigate(CupidRouters.DRESS_UP_MALL)
                    Analytics.reportClickEvent(TracePoints.CLICK_MY_PAGE_DRESS_MALL)
                },
                MineOption(
                    R.drawable.ic_cpd_mine_visitor_history,
                    R.string.cpd_mine_history
                ) { onAction(MinePageAction.GoVisitorPage) },
                MineOption(
                    R.drawable.ic_cpd_mine_customer,
                    R.string.cpd_mine_customer
                ) { onAction(MinePageAction.GoCustomerServicePage) },
            )
        }
        Column(
            modifier = Modifier
                .background(Color.White, RoundedCornerShape(12.dp)),
        ) {
            Text(
                stringResource(id = R.string.cpd_usually_capability),
                color = CpdColors.FF1D2129,
                fontSize = 14.sp,
                modifier = Modifier.padding(top = 16.dp, start = 12.dp),
                fontWeight = FontWeight.SemiBold
            )
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                usuallyOptions.forEach {
                    Column(
                        modifier = Modifier
                            .weight(1f)
                            .padding(vertical = 10.dp)
                            .click {
                                it.onClick()
                            },
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        ComposeImage(model = it.icon, modifier = Modifier.size(28.dp))
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            stringResource(id = it.titleRes),
                            color = CpdColors.FF1D2129,
                            fontSize = 12.sp
                        )
                    }
                }
            }
        }
        //endregion

        Spacer(modifier = Modifier.height(20.dp))

        val pagerState = rememberPagerState { tabList.size }

        CpdAppScrollableTabRow(
            tabs = tabList,
            pagerState = pagerState,
            spacePadding = 28.dp,
            edgePadding = 0.dp
        )

        val scope = rememberCoroutineScope()
        val listener = remember(scope) {
            object : NavListener(HOME_SUB_NAV) {
                override fun handleNav(route: String): Boolean {
                    when (route) {
                        "cp" -> {
                            val pos = tabList.indexOfFirst { it is CpdMineTab.CpZone }
                            if (pos != -1) {
                                scope.launch {
                                    pagerState.scrollToPage(pos)
                                }
                            }
                            return true
                        }

                        "closefriend" -> {
                            val pos = tabList.indexOfFirst { it is CpdMineTab.Crony }
                            if (pos != -1) {
                                scope.launch {
                                    pagerState.scrollToPage(pos)
                                }
                            }
                            return true
                        }

                        else -> {}
                    }
                    return false
                }
            }
        }
        EventBusListener(listener = listener)

        HorizontalPager(
            modifier = Modifier.fillMaxSize(),
            verticalAlignment = Alignment.Top,
            state = pagerState
        ) { page ->
            when (val tab = tabList[page]) {
                is CpdMineTab.Info -> {
                    MineProfile(user = user, uiActionCallback = uiActionCallback)
                }

                is CpdMineTab.Gift -> {
                    GiftWall(tab.model)
                }

                is CpdMineTab.Crony -> {
                    CompositionLocalProvider(LocalTracePage provides TraceConst.MINE_PAGE) {
                        CronyHomePage(false)
                    }
                }

                is CpdMineTab.CpZone -> {
                    CpZonePage(false)
                }
            }
        }
    }
}

@Composable
private fun MineBriefInfo(user: User, modifier: Modifier = Modifier) {
    Column(modifier = modifier, verticalArrangement = Arrangement.Center) {
        Text(
            text = user.nickname,
            style = MaterialTheme.typography.headlineMedium,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
        Spacer(modifier = Modifier.height(8.dp))
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            AgeGender(age = user.age, isBoy = user.isBoy)

            LevelComposeView(user = user)

            if (user.isVip) {
                ComposeImage(
                    model = R.drawable.ic_cpd_vip_brife,
                    modifier = Modifier.size(40.dp, 16.dp)
                )
            }
        }
        Spacer(modifier = Modifier.height(10.dp))
        Row(verticalAlignment = Alignment.CenterVertically) {
            val textStyle = remember {
                TextStyle(
                    color = Color(0xFF4E5969),
                    fontSize = 12.sp,
                )
            }
            val mod = remember {
                Modifier
                    .height(20.dp)
                    .background(Color(0xFFF1F2F3), RoundedCornerShape(50))
                    .padding(horizontal = 8.dp)
            }
            val city = user.nativeProfile?.cityCode?.name
            if (!city.isNullOrBlank()) {
                TextLabel(text = city, textStyle = textStyle, modifier = mod)
                Spacer(modifier = Modifier.width(4.dp))
            }
            TextLabel(text = "ID ${user.publicId}", textStyle = textStyle, modifier = mod)
        }
    }
}

@Composable
@Preview
private fun MineBriefPagePreview() {
    MineBriefInfo(user = userForPreview)
}

@Preview
@Composable
private fun Preview() {
    PreviewCupidTheme {
        MineContent(
            modifier = Modifier.padding(horizontal = 16.dp),
            user = userForPreview,
            tabList = listOf()
        )
    }
}

//region 资料卡

@Composable
private fun MineProfile(
    user: User,
    uiActionCallback: IHomeAction = IHomeAction.Empty,
) {
    val context = LocalContext.current
    val vm = viewModel<CupidEditViewModel>()
    val editorController = editorController(ChangeableProperty.NONE, vm)

    Column {
        Spacer(modifier = Modifier.height(20.dp))
        ProfileIntro(
            intro = user.shortIntro,
            user.introEmptyRes,
            editEnable = user.isSelf,
            onEditIntro = {
                editorController.invoke(ChangeableProperty.INTRO, user.shortIntro)
            })
        Spacer(modifier = Modifier.height(40.dp))
        Text(
            text = stringResource(id = R.string.cpd_person_album),
            style = MaterialTheme.typography.headlineMedium
        )
        Spacer(modifier = Modifier.height(20.dp))
        val albums = remember(user.albumList) {
            user.albumList.reversed()
        }
        AlbumRow(albumList = albums, { it.url }, onAdd = {
            uiActionCallback.onNavigateTo(CupidRouters.ALBUM_EDIT)
        })
        Spacer(modifier = Modifier.height(40.dp))
        val baseInfoList = remember(user) {
            user.getDisplayInfoList(context)
        }
        ProfileInformation(list = baseInfoList, editEnable = user.isSelf, onEditBaseInfo = {
            uiActionCallback.navigatorToProfileEdit()
        })
        Spacer(modifier = Modifier.height(40.dp))
    }
}

//endregion

@Composable
private fun GiftWall(
    giftWallDataState: CategoryGiftWall?,
) {
    giftWallDataState?.let {
        Box(
            modifier = Modifier
                .height(600.dp)
                .padding(top = 20.dp)
        ) {
            CupidGiftWallPage(
                1,
                it.items,
                Modifier.fillMaxWidth(),
                GiftWallContainerStyle(
                    categoryTitleColor = Color(0xFFB18D23),
                    backgroundColor = Color.White,
                    leftArrowImgRes = R.drawable.ic_cpd_mine_gift_left,
                    rightArrowImgRes = R.drawable.ic_cpd_mine_gift_right
                )
            )
        }
    }
}

//region 我的 卡片


@Composable
private fun CardRow(
    goldCount: Int,
    family: Pair<String, String>?,
    modifier: Modifier = Modifier,
    onClickGold: OnClick = {},
    onClickEmptyFamily: OnClick = {},
    onClickFamily: OnClick = {},
) {
    Row(modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Center) {
        Row(
            modifier = Modifier
                .weight(1f)
                .height(80.dp)
                .click(onClick = onClickGold)
                .background(Color(0xFFFFF8E8), Shapes.corner12)
                .padding(horizontal = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(id = R.drawable.cupid_gold_icon),
                contentDescription = "coin",
                contentScale = ContentScale.FillBounds,
                modifier = Modifier.size(32.dp)
            )
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 8.dp),
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = stringResource(id = R.string.cupid_mine_gold),
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(10.dp))
                Text(
                    text = goldCount.toString(),
                    style = TextStyle(Color(0xFFFFB71A), 24.sp),
                    fontFamily = D_DIN
                )
            }
        }
        Spacer(modifier = Modifier.width(12.dp))
        if (family == null) {
            Row(
                modifier = Modifier
                    .weight(1f)
                    .height(80.dp)
                    .clickable(onClick = onClickEmptyFamily)
                    .background(Color(0xFFFFEAF0), Shapes.corner12)
                    .padding(horizontal = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    IconFamily,
                    contentDescription = "family",
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier.size(32.dp)
                )
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 8.dp),
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = stringResource(R.string.cpd_def_title),
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(10.dp))
                    Text(
                        text = stringResource(R.string.cpd_def_desc),
                        style = TextStyle(Color(0xFF86909C), 12.sp)
                    )
                }
            }
        } else {
            Row(
                modifier = Modifier
                    .weight(1f)
                    .height(80.dp)
                    .click(onClick = onClickFamily)
                    .background(Color(0xFFFFEAF0), Shapes.corner12)
                    .padding(horizontal = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                CircleComposeImage(
                    family.first,
                    contentDescription = "family",
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier.size(32.dp)
                )
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 8.dp),
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = stringResource(id = R.string.cpd家庭),
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(10.dp))
                    Text(
                        text = family.second,
                        style = TextStyle(MaterialTheme.colorScheme.primary, 12.sp)
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun CardsPreview() {
    CardRow(goldCount = 999, null)
}

@Preview
@Composable
private fun CardsPreview2() {
    CardRow(goldCount = 999, "" to "我的家族")
}

//endregion
