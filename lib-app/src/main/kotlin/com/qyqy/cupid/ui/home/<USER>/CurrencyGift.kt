package com.qyqy.cupid.ui.home.message

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.IC2CAction
import com.qyqy.cupid.ui.dialog.DialogQueue
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.UCOOAccount
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.UserAccount
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.ui.AppBasicTextField
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable

import kotlinx.serialization.SerialName


///////////////////////////////////////////////////////////////////////////
// 选择赠送货币
///////////////////////////////////////////////////////////////////////////
@Serializable
data class CurrencyGiftOrder(
    @SerialName("before_transfer_balance")
    val beforeTransferBalance: Int = 0, // 7788
    @SerialName("order_no")
    val orderNo: String = "", // xxxxxx
    @SerialName("to_user")
    val toUser: AppUser = AppUser(),
    @SerialName("total_cost_cnt")
    val totalCostCnt: Int = 0, // 3034
    @SerialName("transfer_cnt")
    val transferCnt: Int = 0, // 3000
    @SerialName("transfer_fee")
    val transferFee: Int = 0, // 34
)


object CurrencyGift {

    fun show(
        coinGiftEnable: Boolean,
        diamondGiftEnable: Boolean,
        account: UCOOAccount,
        dialogQueue: DialogQueue<IC2CAction>,
        viewModel: C2CChatViewModel,
    ) {
        when {
            coinGiftEnable && diamondGiftEnable -> {
                dialogQueue.push { dialog, _ ->
                    SelectCurrencyGiftContent({
                        dialog.dismiss()
                        showEditCurrencyGift(false, account, dialogQueue, viewModel)
                    }, {
                        dialog.dismiss()
                        showEditCurrencyGift(true, account, dialogQueue, viewModel)
                    }) {
                        dialog.dismiss()
                    }
                }
            }

            coinGiftEnable -> {
                showEditCurrencyGift(false, account, dialogQueue, viewModel)
            }

            diamondGiftEnable -> {
                showEditCurrencyGift(true, account, dialogQueue, viewModel)
            }

            else -> Unit
        }

    }

    private fun showEditCurrencyGift(
        isDiamond: Boolean,
        account: UCOOAccount,
        dialogQueue: DialogQueue<IC2CAction>,
        viewModel: C2CChatViewModel,
    ) {
        dialogQueue.push { dialog, _ ->
            var loading by remember {
                mutableStateOf(false)
            }
            val scope = rememberCoroutineScope()
            CurrencyGiftContent(
                loading = loading,
                title = if (isDiamond) stringResource(id = R.string.cpd赠送钻石) else stringResource(id = R.string.cpd赠送金币),
                subTitle = if (isDiamond) stringResource(id = R.string.cpd请输入赠送钻石的数量) else stringResource(id = R.string.cpd请输入赠送金币的数量),
                costTip = if (isDiamond) {
                    stringResource(id = R.string.cpd每次赠送钻石将收取手续费, "${account.diamondFee}%")
                } else {
                    if (account.coinFee > 0) {
                        stringResource(id = R.string.cpd每次赠送金币将收取手续费, "${account.coinFee}%")
                    } else {
                        ""
                    }
                },
                currencyBalance = if (isDiamond) {
                    stringResource(id = R.string.cpd我的钻石余额, account.diamond)
                } else {
                    stringResource(id = R.string.cpd我的金币余额, account.balance)
                },
                currencyIcon = if (isDiamond) R.drawable.ic_cpd_diamond else R.drawable.ic_cpd_coin,
                currencyColor = if (isDiamond) Color(0xFFFF5E8B) else Color(color = 0xFFFFB71A),
                onConfirm = {
                    scope.launch {
                        loading = true
                        viewModel.createCurrencyGiftOrder(it, if (!isDiamond) 21 else 22).onSuccess {
                            dialog.dismiss()
                            confirmCurrencyGift(isDiamond, it, dialogQueue)
                        }.toastError()
                        loading = false
                    }
                }
            ) {
                dialog.dismiss()
            }
        }
    }


    private fun confirmCurrencyGift(
        isDiamond: Boolean,
        order: CurrencyGiftOrder,
        dialogQueue: DialogQueue<IC2CAction>,
    ) {
        dialogQueue.pushCenterDialog { dialog, onAction ->
            ConfirmCurrencyGiftContent(
                user = order.toUser,
                value = order.transferCnt,
                fee = order.transferFee,
                currencyBalance = if (isDiamond) {
                    stringResource(id = R.string.cpd我的钻石余额, order.beforeTransferBalance)
                } else {
                    stringResource(id = R.string.cpd我的金币余额, order.beforeTransferBalance)
                },
                currencyIcon = if (isDiamond) R.drawable.ic_cpd_diamond else R.drawable.ic_cpd_coin,
                currencyColor = if (isDiamond) Color(0xFFFF5E8B) else Color(color = 0xFFFFB71A),
                onConfirm = {
                    dialog.dismiss()
                    (onAction as? IC2CAction)?.onConfirmCurrencyGift(order.orderNo)
                }
            ) {
                dialog.dismiss()
            }
        }
    }
}

///////////////////////////////////////////////////////////////////////////
// 选择赠送货币
///////////////////////////////////////////////////////////////////////////

@Composable
private fun SelectCurrencyGiftContent(onCoin: OnClick = {}, onDiamond: OnClick = {}, onClose: OnClick = {}) {

    val density = LocalDensity.current
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .padding(bottom = with(density) {
                androidx.compose.foundation.layout.WindowInsets.navigationBars
                    .getBottom(density)
                    .toDp()
            }.coerceAtLeast(66.dp)), horizontalAlignment = Alignment.CenterHorizontally
    ) {

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(44.dp)
        ) {
            Text(
                text = stringResource(id = R.string.cpd_txt_give),
                modifier = Modifier.align(Alignment.Center),
                color = Color(0xFF1D2129),
                fontSize = 15.sp,
                fontWeight = FontWeight.Medium
            )

            IconButton(onClick = onClose, modifier = Modifier.align(Alignment.CenterEnd)) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_cpd_close), contentDescription = "close", tint = Color(0xFFC9CDD4)
                )
            }
        }


        Row(
            modifier = Modifier
                .padding(top = 32.dp, start = 16.dp, end = 16.dp)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Row(
                modifier = Modifier
                    .weight(1f)
                    .height(56.dp)
                    .background(Color(0xFFF5F7F9), RoundedCornerShape(8.dp))
                    .noEffectClickable {
                        onCoin()
                    },
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_cpd_currency_coin),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(8.dp)
                        .size(40.dp)
                )
                Text(
                    text = stringResource(id = R.string.cpd赠送金币),
                    color = Color(0xFF1D2129),
                    fontSize = 16.sp,
                )
            }

            Row(
                modifier = Modifier
                    .weight(1f)
                    .height(56.dp)
                    .background(Color(0xFFF5F7F9), RoundedCornerShape(8.dp))
                    .noEffectClickable {
                        onDiamond()
                    },
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_cpd_currency_diamond),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(8.dp)
                        .size(40.dp)
                )
                Text(
                    text = stringResource(id = R.string.cpd赠送钻石),
                    color = Color(0xFF1D2129),
                    fontSize = 16.sp,
                )
            }
        }
    }
}

@Preview
@Composable
private fun PreviewSelectCurrencyGiftContent() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF626364)),
        contentAlignment = Alignment.BottomCenter
    ) {
        SelectCurrencyGiftContent()
    }
}

///////////////////////////////////////////////////////////////////////////
// 选择赠送货币
///////////////////////////////////////////////////////////////////////////


///////////////////////////////////////////////////////////////////////////
// 确定赠送货币的数量
///////////////////////////////////////////////////////////////////////////

@Composable
private fun CurrencyGiftContent(
    loading: Boolean,
    title: String,
    subTitle: String,
    costTip: String,
    currencyBalance: String,
    @DrawableRes currencyIcon: Int,
    currencyColor: Color,
    onConfirm: (Int) -> Unit = {},
    onClose: OnClick = {},
) {
    val density = LocalDensity.current
    var inputValue by rememberSaveable {
        mutableStateOf("")
    }

    val inputNumber by remember {
        derivedStateOf {
            inputValue.toIntOrNull() ?: 0
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .padding(bottom = with(density) {
                androidx.compose.foundation.layout.WindowInsets.navigationBars
                    .getBottom(density)
                    .toDp()
            }.coerceAtLeast(20.dp))
            .imePadding(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(44.dp)
        ) {
            Text(
                text = title,
                modifier = Modifier.align(Alignment.Center),
                color = Color(0xFF1D2129),
                fontSize = 15.sp,
                fontWeight = FontWeight.Medium
            )

            IconButton(onClick = onClose, modifier = Modifier.align(Alignment.CenterEnd)) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_cpd_close), contentDescription = "close", tint = Color(0xFFC9CDD4)
                )
            }
        }

        Column(
            modifier = Modifier
                .padding(horizontal = 32.dp)
                .fillMaxWidth()
        ) {

            Text(
                text = subTitle,
                modifier = Modifier
                    .padding(top = 16.dp)
                    .align(Alignment.CenterHorizontally),
                color = Color(0xFF1D2129),
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )

            val pattern = remember { Regex("^\\d+\$") }
            val focusRequester = remember { FocusRequester() } //焦点

            LaunchedEffect(Unit) {
                delay(150)
                focusRequester.requestFocus()
            }

            Box(
                modifier = Modifier
                    .padding(top = 16.dp)
                    .fillMaxWidth()
                    .height(48.dp)
                    .background(Color(0xFFF5F7F9), RoundedCornerShape(8.dp))
                    .padding(horizontal = 12.dp),
                contentAlignment = Alignment.CenterStart
            ) {
                AppBasicTextField(
                    value = inputValue,
                    onValueChange = {
                        if (loading) {
                            return@AppBasicTextField
                        }
                        if (it.isEmpty()) {
                            inputValue = ""
                        } else if (it.matches(pattern)) { // 保证输入都是数字
                            if (it.firstOrNull() == '0') { // 保证两位及以上的数字必须是非0开头
                                val index = it.substring(1).indexOfFirst { c -> c != '0' }
                                inputValue = if (index == -1) {
                                    "0"
                                } else {
                                    it.substring(1.plus(index))
                                }
                            } else {
                                inputValue = it
                            }
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .focusRequester(focusRequester),
                    textStyle = TextStyle(
                        color = currencyColor,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Medium
                    ),
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.NumberPassword, imeAction = ImeAction.Done),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            if (!loading && inputNumber > 0) {
                                onConfirm(inputNumber)
                            }
                        }
                    )
                )
            }

            if (costTip.isNotBlank()) {
                Text(
                    text = costTip,
                    modifier = Modifier.padding(top = 8.dp),
                    color = Color(0xFF86909C),
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium
                )
            }

            Row(
                modifier = Modifier
                    .padding(top = 20.dp)
                    .align(Alignment.CenterHorizontally),
                verticalAlignment = Alignment.CenterVertically
            ) {

                Text(
                    text = currencyBalance,
                    color = currencyColor,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium
                )

                Image(
                    painter = painterResource(id = currencyIcon),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(start = 3.dp)
                        .size(12.dp)
                )
            }

            AppButton(
                text = if (loading) "" else stringResource(id = R.string.cpd下一步),
                background = Color(0xFFFF5E8B),
                color = Color(0xFFFFFFFF),
                fontSize = 16.sp,
                modifier = Modifier
                    .padding(top = 12.dp)
                    .align(Alignment.CenterHorizontally)
                    .fillMaxWidth()
                    .height(44.dp),
                contentPadding = PaddingValues(horizontal = 6.dp),
                enabled = inputNumber > 0,
                onLayoutContent = { left ->
                    if (loading && !left) {
                        CircularProgressIndicator(modifier = Modifier.requiredSize(20.dp), strokeWidth = 2.dp, color = Color.White)
                    }
                }
            ) {
                if (!loading) {
                    onConfirm(inputNumber)
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewCurrencyGiftContent() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF626364)),
        contentAlignment = Alignment.BottomCenter
    ) {
        CurrencyGiftContent(
            loading = false,
            title = stringResource(id = R.string.cpd赠送金币),
            subTitle = stringResource(id = R.string.cpd请输入赠送金币的数量),
            costTip = stringResource(id = R.string.cpd每次赠送金币将收取手续费, "10%"),
            currencyBalance = stringResource(id = R.string.cpd我的金币余额, 10000),
            currencyIcon = R.drawable.ic_cpd_coin,
            currencyColor = Color(color = 0xFFFFB71A),
        )
    }
}

///////////////////////////////////////////////////////////////////////////
// 确定赠送货币的数量
///////////////////////////////////////////////////////////////////////////


///////////////////////////////////////////////////////////////////////////
// 确认赠送货币
///////////////////////////////////////////////////////////////////////////


@Composable
private fun ConfirmCurrencyGiftContent(
    user: User,
    value: Int,
    fee: Int,
    currencyBalance: String,
    @DrawableRes currencyIcon: Int,
    currencyColor: Color,
    onConfirm: () -> Unit = {},
    onClose: OnClick = {},
) {
    Column(
        modifier = Modifier
            .width(270.dp)
            .drawWithCache {
                val px8 = 8.dp.toPx()
                val px24 = 24.dp.toPx()
                val endY = 135.dp.toPx()
                onDrawBehind {
                    drawRoundRect(
                        brush = Brush.verticalGradient(
                            colors = listOf(Color(0xFFFFCDDB), Color(0xFFFFFFFF)),
                            endY = endY,
                        ),
                        topLeft = Offset(0f, px24),
                        cornerRadius = CornerRadius(px8),
                    )
                }
            },
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(modifier = Modifier.fillMaxWidth()) {
            CircleComposeImage(
                model = user.avatarUrl,
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .size(72.dp)
                    .border(1.5.dp, Color.White, CircleShape)
            )

            IconButton(
                onClick = onClose,
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(top = 24.dp)
                    .size(32.dp)
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_cpd_close),
                    contentDescription = "close",
                    modifier = Modifier.size(20.dp),
                    tint = Color(0xFFC9CDD4)
                )
            }
        }

        Text(
            text = user.nickname,
            modifier = Modifier.padding(top = 5.dp),
            color = Color(0xFF1D2129),
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium
        )

        Text(
            text = "ID: ${user.publicId}",
            modifier = Modifier.padding(top = 8.dp),
            color = Color(0xFF1D2129),
            fontSize = 12.sp,
        )

        Row(
            modifier = Modifier
                .padding(top = 16.dp)
                .align(Alignment.CenterHorizontally),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "${stringResource(id = R.string.cpd赠送内容)}：",
                color = Color(0xFF1D2129),
                fontSize = 14.sp,
            )
            Text(
                text = value.toString(),
                color = currencyColor,
                fontSize = 14.sp,
            )
            Image(
                painter = painterResource(id = currencyIcon),
                contentDescription = null,
                modifier = Modifier
                    .padding(start = 3.dp)
                    .size(14.dp)
            )
        }

        Row(
            modifier = Modifier
                .padding(top = 5.dp)
                .align(Alignment.CenterHorizontally),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "${stringResource(id = R.string.cpd手续费)}：",
                color = Color(0xFF1D2129),
                fontSize = 14.sp,
            )
            Text(
                text = fee.toString(),
                color = currencyColor,
                fontSize = 14.sp,
            )
            Image(
                painter = painterResource(id = currencyIcon),
                contentDescription = null,
                modifier = Modifier
                    .padding(start = 3.dp)
                    .size(14.dp)
            )
        }

        AppButton(
            text = stringResource(id = R.string.cpd确认赠送),
            background = Color(0xFFFF5E8B),
            color = Color(0xFFFFFFFF),
            fontSize = 16.sp,
            modifier = Modifier
                .padding(top = 16.dp)
                .align(Alignment.CenterHorizontally)
                .size(148.dp, 36.dp),
            contentPadding = PaddingValues(horizontal = 6.dp),
            onClick = onConfirm,
        )

        Text(
            text = currencyBalance,
            modifier = Modifier.padding(top = 5.dp, bottom = 16.dp),
            color = Color(0xFF86909C),
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

@Preview
@Composable
private fun PreviewConfirmCurrencyGiftContent() {
    Box(
        modifier = Modifier
            .size(300.dp)
            .background(Color(0xFF626364)),
        contentAlignment = Alignment.Center
    ) {
        ConfirmCurrencyGiftContent(
            user = userForPreview,
            value = 1000,
            fee = 10,
            currencyBalance = stringResource(id = R.string.cpd我的金币余额, 10000),
            currencyIcon = R.drawable.ic_cpd_coin,
            currencyColor = Color(color = 0xFFFFB71A),
        )
    }
}
///////////////////////////////////////////////////////////////////////////
// 确认赠送货币
///////////////////////////////////////////////////////////////////////////