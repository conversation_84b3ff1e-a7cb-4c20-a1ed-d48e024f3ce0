package com.qyqy.cupid.ui.home.message

import android.os.Bundle
import androidx.activity.OnBackPressedCallback
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.dialog.NormalDialog
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.presentation.ScreenNavigator
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.im.conversation.NotificationPermissionHelper
import com.qyqy.ucoo.utils.OnClick

@Stable
data class NotiAlertData(
    @DrawableRes val titleResInt: Int,
    @DrawableRes val contentResId: Int,
    @StringRes val openTextResId: Int,
    @StringRes val skipTextResId: Int,
    val backgroundColor: Color,
    val buttonColor: Color,
)

object FullScreenNotificationAlert : ScreenNavigator {
    @Composable
    override fun ContentScreen(activity: BaseActivity, bundle: Bundle) {
        val data = NotiAlertData(
            R.drawable.img_noti_title,
            R.drawable.img_noti_content,
            R.string.打开通知,
            R.string.skip,
            Color(0xFF1C1E1F),
            Color(0xFF8B56FC)
        )
        activity.onBackPressedDispatcher.addCallback(object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                //拦截🔙按钮
            }
        })
        FullScreenNotiAlert(data = data, onSkip = { activity.finish() }, onOpen = {
            activity.run {
                startActivity(NotificationPermissionHelper.getNotificationIntent(this))
                finish()
            }
        })
    }

}

class FullScreenNotificationDialog : NormalDialog<IDialogAction>() {
    override val isFull: Boolean = true

    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        val data = remember {
            NotiAlertData(
                R.drawable.cpd_img_noti_title,
                R.drawable.cpd_img_noti_content,
                R.string.cpd打开通知,
                R.string.cpd_skip,
                Color.White,
                Color(0xFFFF5E8B)
            )
        }
        Box(modifier = Modifier.fillMaxSize()) {
            val context = LocalContext.current
            FullScreenNotiAlert(data = data, onSkip = { dialog.dismiss() }, onOpen = {
                dialog.dismiss()
                context.startActivity(NotificationPermissionHelper.getNotificationIntent(context))
            })
        }
    }

}

@Composable
fun FullScreenNotiAlert(data: NotiAlertData, onSkip: OnClick, onOpen: OnClick, modifier: Modifier = Modifier) {
    Column(
        modifier = modifier
            .background(data.backgroundColor)
            .systemBarsPadding()
            .padding(horizontal = 24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.height(60.dp))
        Image(
            painter = painterResource(id = data.titleResInt),
            contentDescription = "",
            modifier = Modifier.height(62.dp),
            contentScale = ContentScale.FillHeight
        )
        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        )
        Image(
            painter = painterResource(id = data.contentResId),
            contentDescription = "c",
            modifier = Modifier.fillMaxWidth(),
            contentScale = ContentScale.Crop
        )
        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .weight(2f)
        )
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(44.dp)
                .clip(Shapes.chip)
                .background(data.buttonColor)
                .click(onClick = onOpen),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = stringResource(id = data.openTextResId),
                style = TextStyle(color = Color.White, textAlign = TextAlign.Center, fontSize = 16.sp)
            )
        }

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(44.dp)
                .click(onClick = onSkip),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = stringResource(id = data.skipTextResId),
                style = TextStyle(color = Color(0xFF86909C), textAlign = TextAlign.Center, fontSize = 16.sp)
            )
        }

    }
}

@Preview
@Composable
private fun Preview() {
    PreviewCupidTheme {
        val data = NotiAlertData(
            R.drawable.cpd_img_noti_title,
            R.drawable.cpd_img_noti_content,
            R.string.cpd打开通知,
            R.string.cpd_skip,
            Color.White,
            MaterialTheme.colorScheme.primary
        )
        FullScreenNotiAlert(data = data, onSkip = { /*TODO*/ }, onOpen = { /*TODO*/ })
    }
}


@Preview
@Composable
private fun UCOOPreview() {
    PreviewCupidTheme {
        val data = NotiAlertData(
            R.drawable.img_noti_content,
            R.drawable.img_noti_title,
            R.string.打开通知,
            R.string.skip,
            Color(0xFF1C1E1F),
            Color(0xFF8B56FC)
        )
        FullScreenNotiAlert(data = data, onSkip = { /*TODO*/ }, onOpen = { /*TODO*/ })
    }
}