package com.qyqy.cupid.ui.home.message

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.paint
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.SimpleAnimatedDialog
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.presentation.room.color
import com.qyqy.ucoo.compose.theme.D_DIN
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.LoadingWidget
import com.qyqy.ucoo.compose.ui.overScrollHorizontal
import com.qyqy.ucoo.compose.ui.overScrollVertical
import com.qyqy.ucoo.compose.ui.rememberOverscrollFlingBehavior
import kotlinx.coroutines.delay
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class IntimacyLevelTable(
    val level: Int = 0,
    val levels: List<Level> = listOf(),
    val score: Int = 0,
)

@Serializable
data class Level(
    val level: Int = 0,
    val name: String = "",
    @SerialName("right_desc")
    val rightDesc: String = "",
    @SerialName("right_type")
    val rightType: Int = 0,
    val score: Int = 0,
)

@Serializable
data class IntimacyTasks(
    val tasks: List<Task> = listOf(),
)

@Serializable
data class Task(
    @SerialName("base_score")
    val baseScore: Int = 0,
    val desc: String = "",
    val id: Int = 0,
    @SerialName("jump_link")
    val jumpLink: String = "",
    @SerialName("limit_desc")
    val limitDesc: String = "",
    @SerialName("limit_times")
    val limitTimes: Int = 0,
    @SerialName("limit_type")
    val limitType: Int = 0,
    val name: String = "",
    val process: Int = 0,
    @SerialName("task_name")
    val taskName: String = "",
    @SerialName("task_type")
    val taskType: Int = 0,
)

data class IntimacyPagePanelDialog(
    val targetUser: User,
    val selfUser: User,
    val tableState: State<IntimacyLevelTable>,
    val tasks: List<Task>,
) : SimpleAnimatedDialog() {

    @Composable
    override fun Content(dialog: IDialog) {
        IntimacyPage(dialog, targetUser, selfUser, tableState.value, tasks)
    }
}


@Composable
private fun IntimacyPage(
    dialog: IDialog,
    targetUser: User,
    selfUser: User,
    table: IntimacyLevelTable,
    tasks: List<Task>,
) {
    val isPreviewLevel = table.levels.isEmpty()
    val isPreviewTask = tasks.isEmpty()
    val density = LocalDensity.current
    val navigationBars = WindowInsets.navigationBars
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color(0xFFFEE3F0), RoundedCornerShape(12.dp))
            .paint(
                painter = painterResource(id = R.drawable.bg_cpd_intimacy_zone),
                contentScale = ContentScale.FillWidth,
                alignment = Alignment.TopCenter
            )
            .padding(horizontal = 16.dp), horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)) {

            Row(
                modifier = Modifier,
                horizontalArrangement = Arrangement.spacedBy((-8).dp)
            ) {
                CircleComposeImage(
                    model = targetUser.avatarUrl, modifier = Modifier
                        .size(64.dp)
                        .border(1.5.dp, Color.White, CircleShape)
                )
                CircleComposeImage(
                    model = selfUser.avatarUrl, modifier = Modifier
                        .size(64.dp)
                        .border(1.5.dp, Color.White, CircleShape)
                )
            }

            Image(
                painter = painterResource(id = R.drawable.ic_cpd_intimacy_love),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 12.dp)
                    .size(24.dp)
            )
        }

        if (isPreviewLevel) {
            LoadingWidget(modifier = Modifier.height(20.dp), color = Color(0x80FF5FC1))
        } else {
            Row {
                Text(text = buildAnnotatedString {
                    color(Color(0xFF3A1920)) {
                        append(stringResource(id = R.string.cpd亲密度))
                        append("：")
                    }
                    withStyle(SpanStyle(color = Color(0xFFFF5E8B), fontFamily = D_DIN)) {
                        append(table.score.toString())
                    }
                }, fontSize = 12.sp)
                Spacer(modifier = Modifier.width(16.dp))
                Text(text = buildAnnotatedString {
                    color(Color(0xFF3A1920)) {
                        append(stringResource(id = R.string.cpd亲密度等级))
                        append("：")
                    }
                    withStyle(SpanStyle(color = Color(0xFFFF5E8B), fontFamily = D_DIN)) {
                        append(table.level.toString())
                    }
                }, fontSize = 12.sp)
            }
        }

        val rowState: LazyListState = if (isPreviewLevel) {
            rememberLazyListState()
        } else {
            rememberLazyListState(table.levels.indexOfFirst { it.level == table.level }.coerceAtLeast(0))
        }
        LazyRow(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp, bottom = 12.dp)
                .overScrollHorizontal(),
            state = rowState,
            flingBehavior = rememberOverscrollFlingBehavior { rowState }
        ) {
            items(if (isPreviewLevel) 6 else table.levels.size, key = {
                if (isPreviewLevel) {
                    it
                } else {
                    "level_${table.levels[it].level}"
                }
            }) { index ->

                if (index != 0) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_cpd_intimacy_level_arrow),
                        contentDescription = null,
                        modifier = Modifier
                            .padding(top = 24.dp, start = 15.dp, end = 15.dp)
                            .size(10.dp, 12.dp)
                    )
                }

                Column(
                    modifier = Modifier
                        .width(60.dp)
                        .alpha(if (isPreviewLevel || table.level < table.levels[index].level) 0.5f else 1f),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Box(modifier = Modifier.size(60.dp)) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_cpd_intimacy_level_love),
                            contentDescription = null,
                            modifier = Modifier.fillMaxSize()
                        )

                        if (!isPreviewLevel) {
                            val item = table.levels[index]
                            Column(
                                modifier = Modifier
                                    .align(Alignment.Center)
                                    .padding(bottom = 2.dp),
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Text(
                                    text = item.score.toString(),
                                    color = Color.White,
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.SemiBold,
                                    fontFamily = D_DIN,
                                    lineHeight = 14.sp
                                )

                                Text(
                                    text = stringResource(id = R.string.cpd亲密度),
                                    color = Color.White,
                                    fontSize = 10.sp,
                                    lineHeight = 14.sp
                                )
                            }
                        }

                    }

                    if (!isPreviewLevel) {
                        val item = table.levels[index]
                        Text(
                            text = item.name,
                            modifier = Modifier.wrapContentWidth(unbounded = true),
                            color = Color(0xFF3A1920),
                            fontSize = 12.sp,
                            maxLines = 1,
                            lineHeight = 14.sp
                        )
                        AppText(
                            text = item.rightDesc,
                            modifier = Modifier.wrapContentWidth(unbounded = true),
                            color = Color(0x803A1920),
                            fontSize = 10.sp,
                            maxLines = 1,
                            lineHeight = 12.sp
                        )
                    } else {
                        Spacer(
                            modifier = Modifier
                                .size(80.dp, 16.dp)
                                .clip(RoundedCornerShape(2.dp))
                                .background(Color(0xFFFFF6FB)),
                        )

                        Spacer(modifier = Modifier.height(5.dp))

                        Spacer(
                            modifier = Modifier
                                .size(120.dp, 12.dp)
                                .clip(RoundedCornerShape(2.dp))
                                .background(Color(0xFFFFF6FB)),
                        )
                    }
                }
            }
        }

        val columnState: LazyListState = rememberLazyListState()
        LazyColumn(
            modifier = Modifier
                .animateContentSize()
                .fillMaxWidth()
                .heightIn(max = 350.dp)
                .background(Color(0xFFFFF6FB), RoundedCornerShape(8.dp))
                .padding(horizontal = 12.dp, vertical = 16.dp)
                .overScrollVertical(),
            state = columnState,
            verticalArrangement = Arrangement.spacedBy(14.dp),
            flingBehavior = rememberOverscrollFlingBehavior { columnState }
        ) {
            item(contentType = 0) {
                Text(
                    text = stringResource(id = R.string.cpd亲密度任务),
                    modifier = Modifier
                        .animateItem()
                        .drawBehind {
                            drawRect(
                                brush = Brush.horizontalGradient(listOf(Color(0xFFFF5E8B), Color(0x00FF5E8B))),
                                topLeft = Offset(0f, 10.dp.toPx())
                            )
                        }
                        .padding(end = 10.dp),
                    color = Color(0xFF3A1920),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold
                )
            }

            if (isPreviewTask) {
                items(2, key = { "preview_$it" }, contentType = { 1 }) {
                    Spacer(
                        modifier = Modifier
                            .animateItem()
                            .fillMaxWidth()
                            .height(60.dp)
                            .clip(RoundedCornerShape(4.dp))
                            .background(Color(0x80FEE3F0)),
                    )
                }
            } else {
                items(tasks, key = { "task_${it.id}" }, contentType = { 2 }) {
                    Row(
                        modifier = Modifier
                            .animateItem()
                            .fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {

                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = it.name,
                                modifier = Modifier.basicMarquee(Int.MAX_VALUE),
                                color = Color(0xFF3A1920),
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium
                            )
                            Spacer(modifier = Modifier.height(2.dp))
                            Text(
                                text = buildAnnotatedString {
                                    color(Color(0xFFFF5E8B)) {
                                        append(it.desc)
                                    }
                                    append("  ")
                                    color(Color(0xFF86909C)) {
                                        append(it.limitDesc)
                                    }
                                },
                                modifier = Modifier.basicMarquee(Int.MAX_VALUE),
                                fontSize = 12.sp,
                            )
                        }

                        Spacer(modifier = Modifier.width(8.dp))

                        val nav = LocalAppNavController.current
                        AppButton(
                            text = it.taskName,
                            modifier = Modifier.size(88.dp, 32.dp),
                            background = Color(0xFFFF5E8B),
                            color = Color.White,
                            contentPadding = PaddingValues(horizontal = 4.dp),
                            enabled = true,
                            onClick = {
                                dialog.dismiss()
                                nav.navigateByLink(it.jumpLink)
                            },
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(with(density) {
            navigationBars.getBottom(this).toDp()
        }.coerceAtLeast(40.dp)))
    }
}

@Preview
@Composable
private fun PreviewIntimacyPage() {
    IntimacyPage({ }, userForPreview, userForPreview, IntimacyLevelTable(), emptyList())
}