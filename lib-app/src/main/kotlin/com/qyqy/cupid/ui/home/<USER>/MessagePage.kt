package com.qyqy.cupid.ui.home.message

import android.os.SystemClock
import android.text.Html
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Badge
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.ui.AppNavController
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.CupidViewModel
import com.qyqy.cupid.ui.DestinationRoute
import com.qyqy.cupid.ui.DurationTaskFloat
import com.qyqy.cupid.ui.IHomeAction
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.global.BannerView
import com.qyqy.cupid.ui.global.ScreenLocation
import com.qyqy.cupid.ui.home.HomeSubPage
import com.qyqy.cupid.ui.home.mine.visitors.VisitorViewModel
import com.qyqy.cupid.ui.relations.family.CreateRoomContent
import com.qyqy.cupid.ui.relations.family.FamilySquareAction
import com.qyqy.cupid.ui.relations.family.icons.IconFamily
import com.qyqy.cupid.utils.CupidFamilyManager
import com.qyqy.cupid.utils.getVoiceLiveHelper
import com.qyqy.cupid.utils.navigateToFamilyHome
import com.qyqy.cupid.widgets.AvatarComposeView
import com.qyqy.cupid.widgets.SimpleTabLayout
import com.qyqy.cupid.widgets.click
import com.qyqy.cupid.widgets.composeClick
import com.qyqy.ucoo.DataPoint
import com.qyqy.ucoo.DataTrace
import com.qyqy.ucoo.R
import com.qyqy.ucoo.ReportExposureCompose
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.UserPartition
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.app
import com.qyqy.ucoo.asComponentActivity
import com.qyqy.ucoo.compose.isEditOnCompose
import com.qyqy.ucoo.compose.presentation.room.RichText
import com.qyqy.ucoo.compose.presentation.room.SimpleItem
import com.qyqy.ucoo.compose.presentation.room.color
import com.qyqy.ucoo.compose.state.EmptyView
import com.qyqy.ucoo.compose.ui.AnimatedDialog
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.fixCurrentPage
import com.qyqy.ucoo.compose.ui.overScrollVertical
import com.qyqy.ucoo.compose.ui.rememberOverscrollFlingBehavior
import com.qyqy.ucoo.compose.ui.toAnnotatedString
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.config.UserConf
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.chat.utils.UIMessageUtils
import com.qyqy.ucoo.im.compat.conversation.AppConversationManger
import com.qyqy.ucoo.im.compat.conversation.C2CConversation
import com.qyqy.ucoo.im.compat.conversation.ConvLabel
import com.qyqy.ucoo.im.compat.conversation.IUIConversation
import com.qyqy.ucoo.im.compat.conversation.TribeConversation
import com.qyqy.ucoo.im.compat.conversation.VisitorHistoryConversation
import com.qyqy.ucoo.im.compat.conversation.VoiceConversation
import com.qyqy.ucoo.multilingual.currentLocale
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.sUserFlow
import com.qyqy.ucoo.utils.blur.BlurTransformation
import com.qyqy.ucoo.widget.orElse
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

fun Int.formatCount(max: Int = 999): String {
    return if (this > max) {
        "$max+"
    } else {
        this.toString()
    }
}

@Composable
fun MessagePage(homeSubPage: HomeSubPage, onAction: IHomeAction, modifier: Modifier = Modifier) {
    Box {

        val titles by remember {
            mutableStateOf(listOf(homeSubPage.getTabName(), app.getString(R.string.cpd亲密度)))
        }
        val pagerState = rememberPagerState {
            titles.size
        }

        val scope = rememberCoroutineScope()

        Column(
            modifier = modifier
                .fillMaxSize()
                .paint(
                    painter = painterResource(id = R.drawable.cupid_header_home),
                    contentScale = ContentScale.FillWidth,
                    alignment = Alignment.TopCenter
                )
                .statusBarsPadding(), horizontalAlignment = Alignment.CenterHorizontally
        ) {

            Box(
                modifier = Modifier
                    .padding(start = 12.dp, top = 12.dp, bottom = 10.dp, end = 12.dp)
                    .fillMaxWidth(),
                contentAlignment = Alignment.CenterStart
            ) {
                SimpleTabLayout(
                    pagerState = pagerState,
                    titles = titles,
                    divider = { Spacer(modifier = Modifier.width(24.dp)) },
                    onTabClick = {
                        scope.launch {
                            pagerState.animateScrollToPage(it)
                        }
                    }
                ) { text, selected ->
                    Text(
                        text = text, style =
                        if (!selected) TextStyle(color = Color(0xFF86909C), fontSize = 18.sp)
                        else TextStyle(
                            color = Color(0xFF1D2129),
                            fontSize = 24.sp,
                            fontWeight = FontWeight.SemiBold
                        ),
                        modifier = Modifier.padding(vertical = 6.dp),
                        textAlign = TextAlign.Start
                    )
                }
                IconButton(
                    onClick = { onAction.onNavigateTo(CupidRouters.Search) },
                    modifier = Modifier.align(Alignment.CenterEnd)
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_search),
                        contentDescription = null,
                        modifier = Modifier.size(24.dp),
                        tint = Color(0xFF1D2129)
                    )
                }
            }

            HorizontalPager(state = pagerState, beyondViewportPageCount = 0) { pageIndex ->
                when (pageIndex) {
                    0 -> {
                        ReportExposureCompose(exposureName = TracePoints.MESSAGE_LIST) {
                            MessageListWidget(onAction = onAction)
                        }
                    }

                    1 -> {
                        ReportExposureCompose(exposureName = TracePoints.VISIT_INTIMACY_PAGE) {
                            MessageIntimacyWidget {
                                onAction.onNavigateTo(it)
                            }
                        }
                    }

                    else -> Unit
                }
            }
        }

        val activity = LocalContext.current.asComponentActivity!!
        val viewModel = viewModel<CupidViewModel>(viewModelStoreOwner = activity)

        LaunchedEffect(key1 = Unit) {
            viewModel.requestCupidDurationTasks()
        }

        DurationTaskFloat(
            taskInfo = viewModel.taskInfo,
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(bottom = 160.dp)
        )
    }

}

/**
 * 使用亲密度排行的消息页
 * 出现些问题, 如果是新登录的, IM里没有这个用户对应的conversation, 这个时候需要显示这个用户吗?
 * 如果显示的话, 数据从哪儿来
 */
@Composable
fun MessageIntimacyWidget(onNav: (DestinationRoute) -> Unit = {}) {
    val isEdit = isEditOnCompose

    val conversationList by remember {
        if (isEdit) {
            MutableStateFlow(emptyList())
        } else {
            AppConversationManger.conversationsFlow.map { list ->
                list.mapNotNull {
                    if (it is C2CConversation && it.user.intimateScore > 0) {
                        it
                    } else {
                        null
                    }
                }.sortedByDescending { it.user.intimateScore }
            }
        }
    }.collectAsStateWithLifecycle(emptyList())

    if (conversationList.isNotEmpty()) {
        val listState = rememberLazyListState()

        LaunchedEffect(key1 = listState) {
            snapshotFlow {
                if (!listState.isScrollInProgress) {
                    val firstVisibleItemIndex =
                        listState.layoutInfo.visibleItemsInfo.firstOrNull()?.index.orElse(0)
                    val lastVisibleItemIndex =
                        listState.layoutInfo.visibleItemsInfo.lastOrNull()?.index.orElse(0).plus(1)
                    Triple(
                        first = true,
                        second = firstVisibleItemIndex.minus(2).coerceAtLeast(0),
                        third = lastVisibleItemIndex.plus(2).coerceIn(0, conversationList.lastIndex)
                    )
                } else {
                    Triple(false, 0, 0)
                }
            }.filter {
                it.first && it.second < it.third
            }.onEach {
                val currentTime = SystemClock.elapsedRealtime()
                val checkStatusList = conversationList.subList(it.second, it.third).filter { item ->
                    currentTime.minus(AppConversationManger.getLastUpdateInfoTimestamp(item.id)) > 120_000
                }.joinToString { item ->
                    AppConversationManger.setLastUpdateInfoTimestamp((item as C2CConversation).id, currentTime)
                    item.id
                }
                AppConversationManger.updateUsersOnlineStatus(checkStatusList)
            }.collect()
        }

        val hideIntimate by remember {
            UIConfig.configFlow.map {
                it.getOrNull()?.hideJapanEntries
            }.filterNotNull().map {
                it.contains(UserConf.HIDE_INTIMATE)
            }
        }.collectAsStateWithLifecycle(initialValue = true)

        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .overScrollVertical(),
            state = listState,
            contentPadding = PaddingValues(top = 5.dp, bottom = 20.dp),
            flingBehavior = rememberOverscrollFlingBehavior { listState }
        ) {
            items(conversationList, key = { item ->
                item.stableId
            }) { item ->
                C2CConversationItem(item, hideIntimate, isIntimacy = true, onNav)
            }
        }
    } else {
        EmptyView(
            modifier = Modifier.fillMaxSize(),
            textRes = R.string.快去找朋友聊天吧,
            iconRes = R.drawable.cupid_empty,
        )
    }
}

/**
 * 消息主页
 *
 * @param onAction
 */
@Composable
fun MessageListWidget(onAction: IHomeAction) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        NotificationAlertBar()

        BannerView(
            location = ScreenLocation.MESSAGE_LIST, modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .padding(bottom = 8.dp),
            clickCallback = {
                Analytics.reportClickEvent(TracePoints.CLICK_MESSAGE_LIST_BANNER)
            }
        )

        ReportExposureCompose(
            exposureName = DataTrace.Exposure.Message.消息列表页访问,
            reExposureMode = false
        )

        MessageListContent(onEmptyFamilyClick = { nav ->
            if (!nav.navigateByLink("ucoo://page/home?sub_route_page=${CupidRouters.HOME_SUBPAGE_FAMILY_ID}&action=${FamilySquareAction.SHOW_REMIND_DIALOG}")) {
                nav.navigate(
                    CupidRouters.FAMILY_SQUARE,
                    mapOf("action" to FamilySquareAction.SHOW_REMIND_DIALOG)
                )
            }
        }) {
            onAction.onNavigateTo(it)
        }
    }
}


/**
 *
 * @param showAll 是否显示家族 房间 访问记录等等
 * @param onEmptyFamilyClick 跳转至创建家族
 * @param onNav 跳转
 */
@Composable
fun MessageListContent(
    onEmptyFamilyClick: (AppNavController) -> Unit = { },
    onNav: (DestinationRoute) -> Unit = {},
) {
    val nav = LocalAppNavController.current

    val isEdit = isEditOnCompose

    val conversationList by remember {
        if (isEdit) {
            MutableStateFlow(emptyList())
        } else {
            AppConversationManger.conversationsFlow
        }
    }.collectAsStateWithLifecycle()

    val vm = viewModel<VisitorViewModel>()

    //request per minute
    LaunchedEffect(key1 = vm) {
        vm.refreshVisitors()
    }
    if (conversationList.isNotEmpty()) {
        val listState = rememberLazyListState()

        LaunchedEffect(key1 = listState) {
            snapshotFlow {
                if (conversationList.isNotEmpty() && !listState.isScrollInProgress) {
                    val firstVisibleItemIndex =
                        listState.layoutInfo.visibleItemsInfo.firstOrNull()?.index.orElse(0)
                    val lastVisibleItemIndex =
                        listState.layoutInfo.visibleItemsInfo.lastOrNull()?.index.orElse(0).plus(1)
                    Triple(
                        first = true,
                        second = firstVisibleItemIndex.minus(2).coerceAtLeast(0),
                        third = lastVisibleItemIndex.plus(2).coerceIn(0, conversationList.lastIndex)
                    )
                } else {
                    Triple(false, 0, 0)
                }
            }.filter {
                it.first && it.second < it.third
            }.onEach {
                val currentTime = SystemClock.elapsedRealtime()

                //fixed 2.40.0 subList从second到third会遗漏掉最后一个
                val checkStatusList = conversationList.subList(it.second, Math.min(it.third + 1, conversationList.size)).filter { item ->
                    item is C2CConversation && currentTime.minus(
                        AppConversationManger.getLastUpdateInfoTimestamp(
                            item.id
                        )
                    ) > 120_000
                }.joinToString { item ->
                    AppConversationManger.setLastUpdateInfoTimestamp(
                        (item as C2CConversation).id,
                        currentTime
                    )
                    item.id
                }
                AppConversationManger.updateUsersOnlineStatus(checkStatusList)
            }.collect()
        }

        val hideIntimate by remember {
            UIConfig.configFlow.map {
                it.getOrNull()?.hideJapanEntries
            }.filterNotNull().map {
                it.contains(UserConf.HIDE_INTIMATE)
            }
        }.collectAsStateWithLifecycle(initialValue = true)

        val voiceLiveHelper = getVoiceLiveHelper()

        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .overScrollVertical(),
            state = listState,
            contentPadding = PaddingValues(top = 5.dp, bottom = 20.dp),
            flingBehavior = rememberOverscrollFlingBehavior { listState }
        ) {
            items(conversationList, key = { item ->
                item.stableId
            }, contentType = { item ->
                when (item) {
                    is C2CConversation -> 1
                    is TribeConversation.Instance -> 2
                    is TribeConversation.Empty -> 3
                    is VoiceConversation -> 4
                    is VisitorHistoryConversation -> 5
                    else -> 0
                }
            }) { item ->
                when (item) {
                    is C2CConversation -> {
                        C2CConversationItem(item, hideIntimate, isIntimacy = false, onNav)
                    }

                    is TribeConversation.Instance -> {
                        FConversationItem(item)
                    }

                    is TribeConversation.Empty -> {
                        EmptyFamilyItem(modifier = Modifier.click {
                            Analytics.reportClickEvent(TracePoints.CLICK_MESSAGE_LIST_FAMILY_ENTRY)
                            onEmptyFamilyClick(nav)
                        })
                    }

                    is VoiceConversation -> {
                        ConversationVoiceRoomItem {
                            voiceLiveHelper.joinVoiceLiveRoom(it, from = "jp_pc_list_my_room_click")
                        }
                    }

                    is VisitorHistoryConversation -> {
                        ConversationVisitorItem(
                            vm,
                            modifier = Modifier.click {
                                Analytics.reportClickEvent(TracePoints.CLICK_MESSAGE_LIST_VISITOR_ENTRY)
                                nav.navigate(CupidRouters.VISITORS)
                            },
                        )
                    }

                    else -> {}
                }
            }
        }
    } else {
        EmptyView(
            modifier = Modifier.fillMaxSize(),
            textRes = R.string.快去找朋友聊天吧,
            iconRes = R.drawable.cupid_empty,
        )
    }
}

@Composable
fun FConversationItem(conversation: TribeConversation.Instance) {
    val nav = LocalAppNavController.current

    FamilyConvItem(
        title = conversation.name,
        avatar = conversation.iconUrl,
        conversation = conversation,
        modifier = Modifier.click {
            Analytics.reportClickEvent(TracePoints.CLICK_MESSAGE_LIST_FAMILY_ENTRY)
            nav.navigateToFamilyHome()
        })
}

@Composable
fun EmptyFamilyItem(modifier: Modifier = Modifier) {
    SimpleItem(modifier = modifier.padding(16.dp, 12.dp), startContent = {
        Box(
            modifier = Modifier
                .size(56.dp)
                .background(Color(0xFFFFE4EB), CircleShape)
                .padding(10.dp)
        ) {
            Image(
                imageVector = IconFamily, contentDescription = "", modifier = Modifier
                    .fillMaxSize()
                    .clip(CircleShape)
            )
        }
        Spacer(modifier = Modifier.width(8.dp))
    }, centerContent = {
        Text(
            text = stringResource(R.string.cpd_def_title),
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold
        )
        Spacer(modifier = Modifier.height(10.dp))
        val encourageInfo = CupidFamilyManager.familyEncourageInfo.collectAsStateWithLifecycle()
        val digest = encourageInfo.value.digest.firstOrNull()
        Text(text = if (digest == null) buildAnnotatedString { append(stringResource(R.string.cpd_def_desc)) }
        else Html.fromHtml(digest.richText, Html.FROM_HTML_MODE_LEGACY).toAnnotatedString(),
            style = TextStyle(Color(0xFF86909C), 14.sp))
    }) {
        ComposeImage(
            model = if (sUser.isBoy) R.drawable.ic_cpd_coin
            else R.drawable.ic_cpd_diamond, modifier = Modifier.size(36.dp)
        )

    }
}

@Composable
fun FamilyConvItem(
    title: String,
    avatar: String,
    conversation: TribeConversation.Instance,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier.padding(horizontal = 16.dp, vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box {
            ComposeImage(
                model = avatar, modifier = Modifier
                    .size(56.dp)
                    .clip(CircleShape)
            )

            val badgeNumber = conversation.unreadCount
            Badge(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .offset(0.dp, (-2).dp)
                    .alpha(if (badgeNumber > 0) 1f else 0f)
                    .border(0.25.dp, Color(0xFFFFFFFF), CircleShape),
                containerColor = Color(0xFFF76560),
                contentColor = Color.White,
            ) {
                Text(text = badgeNumber.formatCount(99))
            }
        }

        Column(
            modifier = Modifier
                .padding(horizontal = 10.dp)
                .weight(1f)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = title,
                    color = Color(0xFF1D2129),
                    fontSize = 16.sp,
                    maxLines = 1,
                    modifier = Modifier.weight(1f, false),
                    overflow = TextOverflow.Ellipsis,
                    fontWeight = FontWeight.Medium,
                )
                Spacer(modifier = Modifier.height(3.dp))
                Text(
                    text = stringResource(id = R.string.cpd_family_mytribe),
                    fontSize = 10.sp,
                    color = MaterialTheme.colorScheme.surface,
                    lineHeight = 18.sp,
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .widthIn(min = 56.dp)
                        .background(
                            Brush.horizontalGradient(listOf(Color(0xFFFF4A7D), Color(0xFFB961FF))),
                            RoundedCornerShape(50)
                        )
                        .padding(horizontal = 2.dp)
                )
            }

            val hintExtra = conversation.hintExtra
            if (hintExtra != null && hintExtra.hintVisible) {
                val richList = hintExtra.hintRichList
                if (richList != null) {
                    RichText(
                        rich = richList,
                        color = Color(0xFFFF5E8B),
                        fontSize = 14.sp,
                        maxLines = 1, overflow = TextOverflow.Ellipsis
                    )
                } else {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(2.dp)
                    ) {
                        if (!hintExtra.hintIcon.isNullOrEmpty()) {
                            ComposeImage(
                                model = hintExtra.hintIcon,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                        }

                        Text(
                            text = hintExtra.hintText.orEmpty(),
                            color = colorResource(id = if (hintExtra.isSelf) R.color.color_points else R.color.color_reward),
                            fontSize = 14.sp,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                }
            } else {
                val context = LocalContext.current
                val redColor = MaterialTheme.colorScheme.primary
                val summaryText = remember(context, conversation) {
                    buildAnnotatedString {
                        if ((conversation.atInfoList?.atMeCount ?: 0) > 0) {
                            color(redColor) {
                                append(context.getString(R.string.cpd_someone_mentioned_me))
                            }
                            append(" ")
                        }
                        color(Color(0xFF86909C)) {
                            append(conversation.getDisplaySummary(context).toAnnotatedString())
                        }
                    }
                }
                Text(
                    text = summaryText,
                    fontSize = 14.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
        Column(
            modifier = Modifier.height(56.dp),
            horizontalAlignment = Alignment.End,
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            val time = rememberConversationTime(conversation = conversation)
            Text(text = time, fontSize = 12.sp, color = Color(0xFF86909C))
            Spacer(modifier = Modifier.height(6.dp))
        }
    }
}

/**
 * 语音房item
 */
@Composable
fun ConversationVoiceRoomItem(modifier: Modifier = Modifier, onJoinVoiceRoom: (Int) -> Unit = {}) {
    val myself by sUserFlow.collectAsStateWithLifecycle()
    val hasRoom by remember {
        derivedStateOf {
            (myself as AppUser).room != null
        }
    }

    //region 创建语音房弹窗
    var showCreateRoomDialog by rememberSaveable {
        mutableStateOf(false)
    }
    if (showCreateRoomDialog) {
        AnimatedDialog(onDismiss = { showCreateRoomDialog = false }) {
            CreateRoomContent(onCreateSuccess = {
                onJoinVoiceRoom(it.roomId)
                showCreateRoomDialog = false
                // update self
                app.accountManager.refreshSelfUserByRemote()
            })
        }
    }
    //endregion

    SimpleItem(modifier = modifier
        .click {
            val myRoom = (myself as AppUser).room
            if (myRoom == null) {
                showCreateRoomDialog = true
            } else {
                onJoinVoiceRoom(myRoom.roomId)
            }
        }
        .padding(16.dp, 12.dp), startContent = {
        Image(
            painter = painterResource(id = R.drawable.ic_cpd_message_voice),
            contentDescription = "",
            modifier = Modifier.size(56.dp)
        )
        Spacer(modifier = Modifier.width(8.dp))
    }, centerContent = {
        Text(
            text = stringResource(id = R.string.cpd_my_room),
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold
        )
        Spacer(modifier = Modifier.height(10.dp))
        Text(
            text = stringResource(id = R.string.cpd_voice_room_placeholder),
            style = TextStyle(Color(0xFF86909C), 14.sp)
        )
    }) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(
                if (hasRoom) stringResource(id = R.string.cpd_enter_room)
                else stringResource(id = R.string.cpd_create_room),
                color = CpdColors.FFFF5E8B,
                fontSize = 12.sp
            )
            Spacer(modifier = Modifier.width(4.dp))
            ComposeImage(model = R.drawable.ic_cpd_message_arrow_right)
        }
    }
}

/**
 * 访问记录item
 */
@Composable
fun ConversationVisitorItem(vm: VisitorViewModel, modifier: Modifier = Modifier) {
    val badgeNumber by VisitorViewModel.visitorCount.collectAsStateWithLifecycle()
    val list by vm.dataState
    val isMember by vm.isMember
    SimpleItem(modifier = modifier.padding(16.dp, 12.dp), startContent = {
        Box {
            Box(modifier = Modifier.size(56.dp)) {
                key("visitor-count-${list.size}") {
                    val pagerState = rememberPagerState {
                        list.size
                    }
                    if (pagerState.pageCount > 1) {
                        LaunchedEffect(key1 = pagerState) {
                            while (true) {
                                delay(5000L)
                                pagerState.animateScrollToPage(
                                    pagerState.fixCurrentPage.plus(1).rem(pagerState.pageCount)
                                )
                            }
                        }
                    }
                    HorizontalPager(
                        state = pagerState,
                        modifier = Modifier.fillMaxSize()
                    ) { index ->
                        val item = list[index]
                        CircleComposeImage(
                            model = item.second.avatarUrl,
                            modifier = Modifier.fillMaxSize(),
                            requestBuilderTransform = {
                                if (!isMember) {
                                    it.transform(BlurTransformation(8, 4))
                                } else {
                                    it
                                }
                            }
                        )
                    }
                }
            }
            Badge(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .offset(0.dp, (-2).dp)
                    .alpha(if (badgeNumber > 0) 1f else 0f)
                    .border(0.25.dp, Color(0xFFFFFFFF), CircleShape),
                containerColor = Color(0xFFF76560),
                contentColor = Color.White,
            ) {
                Text(text = badgeNumber.formatCount(99))
            }
        }
        Spacer(modifier = Modifier.width(8.dp))
    }, centerContent = {
        Text(
            text = stringResource(id = R.string.cpd_visitor_history),
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold
        )
        Spacer(modifier = Modifier.height(10.dp))
        Text(text = buildAnnotatedString {
            if (badgeNumber > 0) {
                val str = stringResource(id = R.string.cpd_visitor_subtitle, badgeNumber)
                append(str)
                "\\d+人".toRegex().find(str)?.also {
                    addStyle(
                        SpanStyle(color = CpdColors.FFFF5E8B),
                        it.range.first,
                        it.range.last.plus(1)
                    )
                }
            } else {
                append(stringResource(id = R.string.cpd_default_visitor))
            }
        }, style = TextStyle(Color(0xFF86909C), 14.sp))
    }) {
        Box {}
    }
}

@Composable
private fun C2CConversationItem(
    conversation: C2CConversation,
    hideIntimate: Boolean,
    isIntimacy: Boolean = false,
    onNav: (destination: DestinationRoute) -> Unit = {},
) {

    var expanded by remember {
        mutableStateOf(false)
    }

    var offset by remember {
        mutableStateOf(DpOffset.Zero)
    }

    val density = LocalDensity.current

    Box {
        val user = conversation.user
        Row(
            modifier = Modifier
                .background(color = if (conversation.isPinned) Color.White else Color.Transparent)
                .pointerInput(Unit) {
                    detectTapGestures(
                        onLongPress = {
                            offset = density.run {
                                DpOffset(it.x.toDp(), 0.dp)
                            }
                            expanded = true
                        },
                        onTap = {
                            val event =
                                if (isIntimacy) TracePoints.CLICK_INTIMACY_CELL else DataTrace.Click.Message.消息列表CELL点击
                            Analytics.appReportEvent(DataPoint.clickBody(event))
                            onNav(DestinationRoute(CupidRouters.C2CChat, mapOf("user" to user)))
                        }
                    )
                }
                .padding(horizontal = 16.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box {
                AvatarComposeView(user = user, modifier = Modifier
                    .size(56.dp)
                    .clip(CircleShape), onClick = composeClick {
                    onNav(DestinationRoute(CupidRouters.PROFILE, mapOf("userId" to user.id)))
                })
                val badgeNumber = conversation.unreadCount
                Badge(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .offset(0.dp, (-2).dp)
                        .alpha(if (badgeNumber > 0) 1f else 0f)
                        .border(0.25.dp, Color(0xFFFFFFFF), CircleShape),
                    containerColor = Color(0xFFF76560),
                    contentColor = Color.White,
                ) {
                    Text(text = badgeNumber.formatCount(99))
                }

                if (conversation.user.onlineStatus == 0) {
                    Spacer(
                        modifier = Modifier
                            .align(Alignment.BottomEnd)
                            .offset((-1).dp, (-1).dp)
                            .clip(CircleShape)
                            .size(10.dp)
                            .background(Color(0xFF18E046))
                            .border(1.dp, Color(0xFFFFFFFF), CircleShape),
                    )
                }
            }

            Column(
                modifier = Modifier
                    .padding(horizontal = 10.dp)
                    .weight(1f)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = buildAnnotatedString {
                            append(user.nickname)
                            if (user.cityName.isNotEmpty()) {
                                withStyle(
                                    SpanStyle(
                                        color = if (user.isSameCity) CpdColors.FFFF5E8B else CpdColors.FF86909C,
                                        fontSize = 12.sp
                                    )
                                ) {
                                    append("[${user.cityName}]")
                                }
                            }

                        },
                        color = Color(0xFF1D2129),
                        fontSize = 16.sp,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.weight(1f, false)
                    )
                    conversation.labels?.forEach {
                        if (it is ConvLabel.ComposeUILabel) {
                            Spacer(modifier = Modifier.width(4.dp))
                            it.LabelContent()
                        }
                    }
                    if (user.isCoinTrade) {
                        Text(
                            stringResource(id = R.string.cpd官方代充), fontSize = 10.sp, lineHeight = 18.sp,
                            fontWeight = FontWeight.Medium, color = Color.White,
                            modifier = Modifier
                                .padding(start = 4.dp)
                                .height(18.dp)
                                .background(
                                    color = Color(0xFF0D94F6),
                                    shape = CircleShape
                                )
                                .padding(horizontal = 10.dp),
                            textAlign = TextAlign.Center
                        )
                    }
                }
                Spacer(modifier = Modifier.height(2.5.dp))

                val hintExtra = conversation.hintExtra
                if (hintExtra != null && hintExtra.hintVisible) {
                    val richList = hintExtra.hintRichList
                    if (richList != null) {
                        RichText(
                            rich = richList,
                            color = Color(0xFFFF5E8B),
                            fontSize = 14.sp,
                            maxLines = 1, overflow = TextOverflow.Ellipsis
                        )
                    } else {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(2.dp)
                        ) {
                            if (!hintExtra.hintIcon.isNullOrEmpty()) {
                                ComposeImage(
                                    model = hintExtra.hintIcon,
                                    contentDescription = null,
                                    modifier = Modifier.size(16.dp)
                                )
                            }

                            Text(
                                text = hintExtra.hintText.orEmpty(),
                                color = colorResource(id = if (hintExtra.isSelf) R.color.color_points else R.color.color_reward),
                                fontSize = 14.sp,
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis
                            )
                        }
                    }
                } else {
                    val context = LocalContext.current
                    val summaryText = remember(context, conversation) {
                        conversation.getDisplaySummary(context).toAnnotatedString()
                    }
                    Text(
                        text = summaryText,
                        color = Color(0xFF86909C),
                        fontSize = 14.sp,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            Column(
                modifier = Modifier.height(56.dp),
                horizontalAlignment = Alignment.End,
                verticalArrangement = Arrangement.SpaceBetween
            ) {
                val time = rememberConversationTime(conversation = conversation)
                Text(text = time, fontSize = 12.sp, color = Color(0xFF86909C))
                Spacer(modifier = Modifier.height(6.dp))
                if (!hideIntimate && user.intimateScore > 0) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Text(
                            text = user.intimateScore.toString(),
                            color = Color(0xFFFF5E8B),
                            fontSize = 12.sp
                        )
                        Image(
                            painter = painterResource(id = R.drawable.ic_cpd_intimacy),
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
                Spacer(modifier = Modifier.height(3.dp))
            }
        }

        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false },
            offset = offset
        ) {
            DropdownMenuItem(text = {
                Text(text = stringResource(id = R.string.cpd_delete))
            }, onClick = {
                IMCompatCore.deleteConversation(conversation.imConversation)
                expanded = false
            })

            DropdownMenuItem(text = {
                Text(
                    text = if (!conversation.isPinned) stringResource(id = R.string.cpd置顶) else stringResource(
                        id = R.string.cpd取消置顶
                    )
                )
            }, onClick = {
                IMCompatCore.setPinnedConversation(conversation.imConversation, !conversation.isPinned)
                expanded = false
            })
        }
    }
}

@Composable
fun rememberConversationTime(conversation: IUIConversation) = run {
    val sentTime = conversation.timestamp
    if (sentTime <= 0) {
        ""
    } else {
        val configuration = LocalConfiguration.current
        val context = LocalContext.current
        remember(configuration, context, sentTime, UserPartition.Cupid) {
            UIMessageUtils.getMessageTimeFormatText(
                context,
                sentTime,
                configuration.currentLocale,
                UserPartition.Cupid
            )
        }
    }
}

//@Preview
//@Composable
//private fun EmptyFamilyPreview() {
//    CupidTheme {
//        EmptyFamilyItem(modifier = Modifier.fillMaxWidth())
//    }
//}