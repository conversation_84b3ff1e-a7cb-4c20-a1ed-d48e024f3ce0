package com.qyqy.cupid.ui.home.mine

import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.google.accompanist.drawablepainter.rememberDrawablePainter
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.app
import com.qyqy.ucoo.compose.data.CategoryGiftWall
import com.qyqy.ucoo.compose.data.LabelItem
import com.qyqy.ucoo.compose.data.Relationship
import com.qyqy.ucoo.compose.ifEmpty
import com.qyqy.ucoo.compose.orDefault
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.ui.ITab
import com.qyqy.ucoo.widget.LoadingDrawable

sealed interface CpdMineTab : ITab {

    data class Info(override val name: String) : CpdMineTab

    data class Gift(val model: CategoryGiftWall) : CpdMineTab {

        override val name: String
            get() = model.category
    }

    data class Crony(override val name: String) : CpdMineTab

    data class CpZone(override val name: String) : CpdMineTab
}

sealed class CpCrony(
    open val self: User,
    open val target: User?,
    open val cpValue: String?,
    open val cpLevel: Int,
    open val cpDurationDay: String?,
    open val cpLogoUrl: String,
    open val gradientTopColor: Color = Color(0xFFFFEFF5),
    open val gradientBottomColor: Color = Color(0xFFFF659C),
    open val cardBgUrl: String?,
) {

    abstract val name1: String?

    abstract val name2: String?

    abstract val avatar1: Any?

    abstract val avatar2: Any?

    fun hasCp() = this is Simple

    companion object {
        fun previewSimple1(): CpCrony {
            return Simple(userForPreview, userForPreview, "857395", 0, "在一起34天", "", Color(0xFFFFEFF5), Color(0xFFFF659C), null)
        }

        fun previewSimple2(): CpCrony {
            return None(userForPreview, "")
        }
    }

    data class None(override val self: User, override val cardBgUrl: String? = self.cpCardBackground) :
        CpCrony(self, null, null, 0, null, "", cardBgUrl = cardBgUrl) {

        override val name1: String?
            get() = null

        override val name2: String?
            get() = null

        override val avatar1: Any
            get() = self.avatarUrl

        override val avatar2: Any?
            get() = null
    }

    data class Simple(
        override val self: User,
        override val target: User,
        override val cpValue: String,
        override val cpLevel: Int,
        override val cpDurationDay: String,
        override val cpLogoUrl: String,
        override val gradientTopColor: Color,
        override val gradientBottomColor: Color,
        override val cardBgUrl: String?,
    ) : CpCrony(self, target, cpValue, cpLevel, cpDurationDay, cpLogoUrl, cardBgUrl = cardBgUrl) {

        override val name1: String
            get() = self.nickname

        override val name2: String
            get() = target.nickname

        override val avatar1: Any
            get() = self.avatarUrl

        override val avatar2: Any
            get() = target.avatarUrl
    }
}

sealed class CronyLabel(
    open val label: LabelItem?,
    open val user: User?,
    open val labelColor: Color?,
    open val textColor: Color,
    open val gradientTopColor: Color?,
    open val gradientBottomColor: Color?,
) {

    @get:Composable
    @get:ReadOnlyComposable
    abstract val name: String

    @get:Composable
    abstract val avatar: Any

    companion object {
        fun previewSimple(): Simple {
            return Simple(
                LabelItem(name = "大哥", intimacyValue = "亲密度9999999999"),
                AppUser(nickname = "幼儿园"),
                Color(0xFF9CD6FF),
                Color(0xFF549ACD),
                Color(0xFFD8F1FF),
                Color(0xFFAFDDFF)
            )
        }
    }


    data object Placeholder : CronyLabel(null, null, null, Color(0xFF9184A3), null, null) {

        override val name: String
            @Composable
            @ReadOnlyComposable
            get() = "loading"

        override val avatar: Painter
            @Composable
            get() = rememberDrawablePainter(LoadingDrawable(LocalDensity.current.run {
                3.dp.toPx()
            }))
    }

    data object None : CronyLabel(null, null, null, Color(0xFF9184A3), null, null) {
        override val name: String
            @Composable
            @ReadOnlyComposable
            get() = stringResource(id = R.string.cpd邀请密友)

        override val avatar: Painter
            @Composable
            get() = painterResource(id = R.drawable.ic_user_relationship_add)
    }

    data class Simple(
        override val label: LabelItem,
        override val user: User,
        override val labelColor: Color,
        override val textColor: Color,
        override val gradientTopColor: Color,
        override val gradientBottomColor: Color,
    ) : CronyLabel(label, user, labelColor, textColor, gradientTopColor, gradientBottomColor) {

        override val name: String
            @Composable
            @ReadOnlyComposable
            get() = user.nickname

        override val avatar: Any
            @Composable
            get() = user.avatarUrl
    }
}

fun Relationship.Label.Normal.toSimpleCronyLabel() = CronyLabel.Simple(
    label = label,
    user = user,
    labelColor = labelColor,
    textColor = textColor,
    gradientTopColor = gradientTopColor,
    gradientBottomColor = gradientBottomColor
)


data class CronyTable(
    val isVisible: Boolean = false,
    val emptyCount: Int = 6,
    val totalCount: Int = emptyCount,
    val maxCount: Int = Int.MAX_VALUE,
    val pricePerLabel: Int = 5000,
    val labelList: List<CronyLabel.Simple> = emptyList(),
    val labelTagList: List<LabelItem> = emptyList(),
    val ruleLink: String = "",
) {
    val hasLabel = totalCount > emptyCount || labelList.isNotEmpty()

    val placeholderCount = (totalCount - labelList.size - emptyCount).coerceAtLeast(0)

}