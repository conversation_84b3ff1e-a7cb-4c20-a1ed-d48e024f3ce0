package com.qyqy.cupid.ui.home.mine

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.qyqy.cupid.theme.navigationPadding
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.profile.RechargePageContent
import com.qyqy.cupid.ui.profile.RechargePageScaffold
import com.qyqy.ucoo.R
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.theme.D_DIN
import com.qyqy.ucoo.sUserFlow

@Composable
fun MyCoinPage() {
    val user by sUserFlow.collectAsStateWithLifecycle()
    MyCoinPage(user.balance.toString())
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MyCoinPage(balance: String) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        val onBackPressedDispatcher = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher

        CenterAlignedTopAppBar(
            colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.White),
            title = {
                Text(
                    text = stringResource(id = R.string.cupid_mine_gold),
                    style = MaterialTheme.typography.titleLarge
                )
            },
            navigationIcon = {
                IconButton(onClick = {
                    onBackPressedDispatcher?.onBackPressed()
                }) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_cpd_navigation_back),
                        contentDescription = "back",
                        tint = Color(0xFF1D2129),
                    )
                }
            },
            actions = {
                Text(
                    text = stringResource(id = R.string.cupid_income_exchange_history),
                    color = colorResource(id = R.color.FF86909C),
                    fontSize = 14.sp,
                    modifier = Modifier
                        .padding(end = 16.dp)
                        .clickable {
                            AppLinkManager.controller?.navigate(CupidRouters.TOPUP_HISTORY)
                        }
                )
            }
        )

        HorizontalDivider(thickness = 0.5.dp, color = Color(0xFFF0F0F0))

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp)
                .navigationPadding(15.dp)
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_cpd_coin),
                contentDescription = null,
                modifier = Modifier
                    .padding(top = 24.dp)
                    .align(Alignment.CenterHorizontally)
                    .size(72.dp)
            )

            Text(
                text = stringResource(id = R.string.cpd所持金币),
                modifier = Modifier
                    .padding(top = 5.dp)
                    .align(Alignment.CenterHorizontally),
                color = Color(0xFF1D2129),
                fontSize = 14.sp
            )

            Text(
                text = balance,
                modifier = Modifier
                    .padding(top = 5.dp)
                    .align(Alignment.CenterHorizontally),
                color = Color(0xFF1D2129),
                fontSize = 32.sp,
                fontFamily = D_DIN,
            )

            Text(
                text = stringResource(id = R.string.cpd选择充值金额),
                modifier = Modifier.padding(top = 40.dp, bottom = 10.dp),
                color = Color(0xFF1D2129),
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )

            RechargePageScaffold { purchaseHelper, flow, onConfirm ->
                RechargePageContent(purchaseHelper = purchaseHelper, flow = flow, modifier = Modifier.weight(1f), onConfirm = onConfirm)
            }
        }
    }
}