

package com.qyqy.cupid.ui.home.message

import android.app.Activity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.app.ActivityCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LifecycleStartEffect
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.repeatOnLifecycle
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.dialog.NormalDialog
import com.qyqy.cupid.ui.home.message.icons.ActionIcons
import com.qyqy.cupid.ui.home.message.icons.IconBell
import com.qyqy.cupid.widgets.CpdButton
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.compose.rememberDateState
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.im.conversation.NotificationPermissionHelper
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.launch

@Composable
fun NotificationAlertBar() {
    val dq = LocalDialogQueue.current
    val scope = rememberCoroutineScope()
    var showNfTip by rememberSaveable {
        mutableStateOf(true)
    }
    val context = LocalContext.current
    var lackNfPermission by remember {
        mutableStateOf(!NotificationPermissionHelper.hasNotificationPermission(context))
    }
    val refresh = remember {
        {
            lackNfPermission = !NotificationPermissionHelper.hasNotificationPermission(context)
        }
    }

    LifecycleStartEffect(Unit) {
        refresh()
        onStopOrDispose { }
    }

    if (lackNfPermission) {
        val launcher = rememberLauncherForActivityResult(
            contract =
            ActivityResultContracts.StartActivityForResult()
        ) {
            refresh()
        }
        val permissionLauncher = rememberLauncherForActivityResult(
            contract = ActivityResultContracts.RequestPermission()
        ) {
            refresh()
        }
        val requestPermission = remember {
            {
                if (!NotificationPermissionHelper.hasNotificationPermission(context)) {
                    if (NotificationPermissionHelper.isOverAndroid13) {
                        val rationale: Boolean = try {
                            val act = context as Activity
                            ActivityCompat.shouldShowRequestPermissionRationale(
                                act,
                                NotificationPermissionHelper.NOTIFICATION_PERMISSION
                            )
                        } catch (e: Exception) {
                            e.printStackTrace()
                            false
                        }
                        if (rationale) {
                            launcher.launch(NotificationPermissionHelper.getNotificationIntent(context))
                        } else {
                            permissionLauncher.launch(NotificationPermissionHelper.NOTIFICATION_PERMISSION)
                        }
                    } else {
                        launcher.launch(NotificationPermissionHelper.getNotificationIntent(context))
                    }
                } else {
                    showNfTip = false
                }
                refresh()
            }
        }

//        val alertConversation by remember {
//            derivedStateOf {
//                conversationList.filter { it is UIConversation.Single && it.unReadCount > 0 }
//                    .maxByOrNull { (it as UIConversation.Single).user.intimateScore } as? UIConversation.Single
//            }
//        }
        //弹窗
        val date by rememberDateState()
        val lifecycleOwner = LocalLifecycleOwner.current
        LaunchedEffect(key1 = date) {
            //v2 2.36.0
            scope.launch {
                lifecycleOwner.repeatOnLifecycle(Lifecycle.State.RESUMED) {
                    if (!NotificationPermissionHelper.hasNotificationPermission(context) && NotificationPermissionHelper.showFullScreenAlert()) {
                        dq.push(FullScreenNotificationDialog())
                    }
                }
            }
            //v1
//            val lastDate = AppUsageUtil.nfLastShowDate
//            if (lastDate.isEmpty()) {
//                dq.push(NotificationTioDialog {
//                    requestPermission()
//                })
//                AppUsageUtil.nfLastShowDate = date
//            }
        }
        //通知条
        if (showNfTip) {
            NotificationAlertContent(modifier = Modifier
                .padding(bottom = 8.dp)
                .padding(horizontal = 16.dp), onClose = {
                showNfTip = false
            }, onClick = {
                requestPermission()
            })
        }
    }
}

@Composable
fun NotificationAlertContent(modifier: Modifier = Modifier, onClose: OnClick = {}, onClick: OnClick = {}) {
    Row(
        modifier = modifier
            .background(MaterialTheme.colorScheme.surface, Shapes.small)
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = ActionIcons.IconBell,
            contentDescription = "bell",
            modifier = Modifier.size(18.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        Spacer(modifier = Modifier.width(4.dp))
        Text(
            text = stringResource(R.string.cpd_noti_status_message),
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.weight(1f),
            fontSize = 12.sp
        )
        Box(
            modifier = Modifier
                .clip(Shapes.chip)
                .click(onClick = onClick)
                .background(MaterialTheme.colorScheme.primary, Shapes.chip)
                .heightIn(min = 24.dp)
                .widthIn(min = 56.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = stringResource(R.string.cpd_noti_status_btn),
                fontSize = 10.sp,
                color = MaterialTheme.colorScheme.surface,
            )
        }
        Spacer(modifier = Modifier.width(8.dp))
        Icon(
            painter = painterResource(id = R.drawable.ic_close_dialog),
            contentDescription = "bell",
            modifier = Modifier
                .size(20.dp)
                .click(onClick = onClose),
            tint = Color(0xFFC9CDD4)
        )
    }
}

@Preview
@Composable
fun NotificationStatusBarPreview(modifier: Modifier = Modifier) {
    PreviewCupidTheme {
        NotificationAlertContent()
    }
}

@Composable
fun OverBackgroundLayer(
    title: String,
    message: String,
    topUI: @Composable BoxScope.() -> Unit,
    modifier: Modifier = Modifier,
    onClick: OnClick = {},
    onClose: OnClick = {},
) {
    Box(modifier = modifier) {
        Column(modifier = Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
            Spacer(modifier = Modifier.height(24.dp))
            Column(
                modifier = Modifier
                    .background(Brush.verticalGradient(listOf(Color(0xFFFFCDDB), Color.White, Color.White)), Shapes.small)
                    .padding(horizontal = 16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.height(58.dp))
                Text(
                    text = title,
                    color = Color.Black,
                    fontSize = 16.sp,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
                Spacer(modifier = Modifier.height(20.dp))
                Text(
                    text = message,
                    color = Color(0xFF606161),
                    fontSize = 14.sp,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
                Spacer(modifier = Modifier.height(20.dp))
                CpdButton(
                    text = stringResource(R.string.cpd_btn_open),
                    onClick = onClick,
                    modifier = Modifier.widthIn(min = 148.dp)
                )
                Spacer(modifier = Modifier.height(20.dp))
            }
            Spacer(modifier = Modifier.height(20.dp))
            Image(
                painter = painterResource(id = R.drawable.ic_close_circle),
                contentDescription = "",
                modifier = Modifier
                    .size(30.dp)
                    .click(onClick = onClose)
            )
        }
        topUI()
    }
}

class NotificationTioDialog(val onClick: OnClick) : NormalDialog<IDialogAction>() {
    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        OverBackgroundLayer(
            modifier = Modifier.width(270.dp),
            title = stringResource(R.string.cpd_title_noti_alert),
            message = stringResource(R.string.cpd_noti_message),
            topUI = {
                Image(
                    painter = painterResource(id = R.drawable.ic_cpd_bell), "",
                    modifier = Modifier
                        .size(72.dp)
                        .align(Alignment.TopCenter)
                )
            },
            onClick = {
                dialog.dismiss()
                onClick.invoke()
            },
            onClose = {
                dialog.dismiss()
            }
        )
    }
}

class NFUserDialog(val user: User, val onClick: OnClick) : NormalDialog<IDialogAction>() {
    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        OverBackgroundLayer(
            modifier = Modifier.width(270.dp),
            title = stringResource(R.string.cpd_title_noti_alert),
            message = stringResource(R.string.cpd_noti_open_for_user, user.nickname),
            topUI = {
                ComposeImage(
                    model = user.avatarUrl,
                    modifier = Modifier
                        .size(72.dp)
                        .border(1.5.dp, Color.White, CircleShape)
                        .clip(CircleShape)
                        .align(Alignment.TopCenter)
                )
            },
            onClick = {
                dialog.dismiss()
                onClick.invoke()
            },
            onClose = {
                dialog.dismiss()
            }
        )
    }
}

@Preview
@Composable
private fun UserAlertPreview() {
    PreviewCupidTheme {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(0.75f)
                .background(Color.Gray), contentAlignment = Alignment.Center
        ) {
            OverBackgroundLayer(
                modifier = Modifier.width(270.dp),
                title = stringResource(R.string.cpd_title_noti_alert),
                message = stringResource(R.string.cpd_noti_message),
                topUI = {
                    Image(
                        painter = painterResource(id = R.drawable.ic_cpd_bell), "",
                        modifier = Modifier
                            .size(72.dp)
                            .clip(CircleShape)
                            .align(Alignment.TopCenter)
                    )
                }
            )
        }
    }
}