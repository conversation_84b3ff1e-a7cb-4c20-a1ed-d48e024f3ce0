@file:OptIn( ExperimentalFoundationApi::class)

package com.qyqy.cupid.ui.home.message

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.contentColorFor
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.data.PhrasesSettingBean
import com.qyqy.cupid.model.PhrasesViewModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.theme.CupidTheme
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.widgets.CpdAppTabRow
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.click
import com.qyqy.cupid.widgets.composeClick
import com.qyqy.cupid.widgets.state.CupidStateList
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppTab
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.IconLoading
import com.qyqy.ucoo.compose.ui.LoadingLayout
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.overScrollVertical
import com.qyqy.ucoo.compose.ui.verticalScrollWithScrollbar
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.launch

private interface PhrasesAction {
    data object AddPhrases : PhrasesAction
    data object AboutPhrases : PhrasesAction
    data class DelPhrases(val target: PhrasesSettingBean.MyPrologue) : PhrasesAction
    data object StartSetting : PhrasesAction
}

@Composable
fun PhrasesPage() {
    val controller = LocalAppNavController.current
    val scope = rememberCoroutineScope()
    val loadingFlag = LocalContentLoading.current

    val vm: PhrasesViewModel = viewModel()
    LaunchedEffect(key1 = Unit) {
        vm.refresh()
    }

    CupidTheme {
        PhrasesPageContent(vm) { action ->
            when (action) {
                PhrasesAction.AddPhrases -> {
                    controller.navigate(CupidRouters.CHAT_PHRASES_ADD)
                }

                is PhrasesAction.DelPhrases -> {
                    scope.launch {
                        loadingFlag.value = true
                        vm.deletePhrases(action.target.id).onSuccess {
                            loadingFlag.value = false
                            vm.refresh()
                        }.onFailure {
                            loadingFlag.value = false
                        }.toastError()
                    }
                }
            }
        }
    }
}

@Composable
private fun PhrasesPageContent(vm: PhrasesViewModel = viewModel(), onAction: (PhrasesAction) -> Unit = {}) {
    val tabTitles = listOf(
        AppTab(stringResource(id = R.string.cpd_phrases_tab_mine)),
        AppTab(stringResource(id = R.string.cpd_phrases_tab_about))
    )

    val setting by vm.phrasesSetting.collectAsStateWithLifecycle()
    var showPhrasesAboutDialog by remember {
        mutableStateOf(false)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(color = CpdColors.FFF5F7F9),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        CupidAppBar(title = stringResource(id = R.string.cpd_phrases_title))
        if (setting != null) {
//            if (setting?.myPrologue?.isEmpty() == true) 1 else 0
            val pagerState = rememberPagerState(0) { tabTitles.size }
            CpdAppTabRow(
                tabs = tabTitles,
                pagerState = pagerState,
                indicatorColor = CpdColors.FFFF5E8B
            )
            HorizontalPager(state = pagerState) { page ->
                when (page) {
                    0 -> {
                        PhrasesListWidget(vm) {
                            when (it) {
                                PhrasesAction.AboutPhrases -> {
                                    showPhrasesAboutDialog = true
                                }

                                else -> {
                                    onAction(it)
                                }
                            }
                        }
                    }

                    1 -> {
                        val scope = rememberCoroutineScope()
                        PhrasesAboutWidget(setting) { action ->
                            when (action) {
                                PhrasesAction.StartSetting -> {
                                    scope.launch {
                                        pagerState.scrollToPage(0)
                                    }
                                }

                                else -> {
                                    onAction(action)
                                }
                            }
                        }
                    }

                    else -> {}
                }
            }
        } else {
            Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                IconLoading()
            }
        }
    }

    if (showPhrasesAboutDialog) {
        Dialog(onDismissRequest = { showPhrasesAboutDialog = false }) {
            val descScrollState = rememberScrollState()
            Column(
                modifier = Modifier
                    .fillMaxWidth(0.9f)
                    .height(320.dp)
                    .background(color = Color.White, shape = RoundedCornerShape(8.dp)),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    "アドバイス",
                    modifier = Modifier.padding(top = 20.dp),
                    fontSize = 17.sp,
                    fontWeight = FontWeight.Medium
                )
                PhrasesEmptyWidget(
                    setting,
                    false,
                    modifier = Modifier
                        .weight(1f)
                        .verticalScrollWithScrollbar(descScrollState),
                    isDialog = true
                )
                AppButton(
                    text = stringResource(id = R.string.cpd_iknow),
                    onClick = composeClick {
                        showPhrasesAboutDialog = false
                    },
                    background = CpdColors.FFFF5E8B,
                    textStyle = TextStyle(color = Color.White, fontSize = 14.sp),
                    contentPadding = PaddingValues(horizontal = 12.dp),
                    modifier = Modifier
                        .padding(vertical = 20.dp)
                        .fillMaxWidth(0.5f)
                        .height(36.dp)
                )
            }
        }
    }
}

//region 组件

@Composable
private fun PhrasesListWidget(viewModel: PhrasesViewModel = viewModel(), onAction: (PhrasesAction) -> Unit = {}) {
    val listValue by viewModel.listFlow.collectAsStateWithLifecycle()
    val setting by viewModel.phrasesSetting.collectAsStateWithLifecycle()
    val isEmpty by remember {
        derivedStateOf {
            listValue.isEmpty()
        }
    }
    Box(modifier = Modifier.fillMaxSize()) {
        CupidStateList(
            viewModel = viewModel,
            modifier = Modifier.fillMaxSize(),
            empty = { PhrasesEmptyWidget(setting) }
        ) {
            itemsIndexed(it) { index, item ->
                Column(
                    modifier = Modifier
                        .padding(horizontal = 16.dp)
                        .padding(top = 8.dp)
                        .fillMaxWidth()
                        .background(
                            color = Color.White,
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(12.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(24.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        ComposeImage(model = R.drawable.ic_cpd_phrases_message)
                        Text(
                            "${stringResource(id = R.string.cpd_phrases_startword)}${index + 1}",
                            modifier = Modifier
                                .weight(1f)
                                .padding(horizontal = 4.dp),
                            fontSize = 14.sp,
                            lineHeight = 14.sp,
                            color = CpdColors.FF1D2129,
                            fontWeight = FontWeight.Medium
                        )
                        AppButton(
                            text = stringResource(id = R.string.cpd_delete),
                            onClick = composeClick {
                                onAction(PhrasesAction.DelPhrases(item))
                            },
                            border = BorderStroke(0.5.dp, CpdColors.FFFF5E8B),
                            background = Color.White,
                            textStyle = TextStyle(color = CpdColors.FFFF5E8B, fontSize = 10.sp),
                            contentPadding = PaddingValues(horizontal = 12.dp),
                            modifier = Modifier.sizeIn(56.dp, 24.dp)
                        )
                    }

                    Text(
                        item.content,
                        fontSize = 14.sp,
                        lineHeight = 14.sp,
                        color = CpdColors.FF86909C,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(vertical = 12.dp)
                    )

                    HorizontalDivider(
                        color = CpdColors.FFF0F0F0,
                        thickness = 0.5.dp,
                        modifier = Modifier.padding(bottom = 12.dp)
                    )

                    Row {
                        Text(
                            stringResource(
                                id = R.string.cpd_phrases_item_desc,
                                item.sendCnt, item.replyCnt, item.replyRatio
                            ),
                            modifier = Modifier.weight(1f),
                            fontSize = 12.sp, color = CpdColors.FF1D2129
                        )
                        Text(
                            text = item.label,
                            fontSize = 12.sp,
                            color = CpdColors.FFFF5E8B
                        )
                    }
                }
            }

            item {
                Spacer(modifier = Modifier.height(if(isEmpty) 108.dp else 180.dp))
            }
        }
        Column(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .background(color = Color.White)
                .padding(horizontal = 32.dp, vertical = 16.dp)
                .navigationBarsPadding()
        ) {
            AppButton(
                text = stringResource(id = R.string.cpd_phrases_new),
                onClick = composeClick {
                    onAction(PhrasesAction.AddPhrases)
                },
                background = CpdColors.FFFF5E8B,
                textStyle = TextStyle(color = Color.White, fontSize = 14.sp),
                contentPadding = PaddingValues(horizontal = 12.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(44.dp)
            )
            if (!isEmpty) {
                Spacer(modifier = Modifier.height(8.dp))
                AppButton(
                    text = stringResource(id = R.string.cpd_phrases_suggestion),
                    onClick = composeClick {
                        onAction(PhrasesAction.AboutPhrases)
                    },
                    border = BorderStroke(0.5.dp, CpdColors.FFC9CDD4),
                    background = Color.White,
                    textStyle = TextStyle(color = CpdColors.FF4E5969, fontSize = 14.sp),
                    contentPadding = PaddingValues(horizontal = 12.dp),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(44.dp)
                )
            }
        }
    }
}

/**
 * todo 这个组件后面应该是要去掉, 这个模板由服务器下发
 *
 * @param showPic
 * @param modifier
 * @param isDialog
 */
@Composable
private fun PhrasesEmptyWidget(
    setting: PhrasesSettingBean? = null,
    showPic: Boolean = true,
    modifier: Modifier = Modifier,
    isDialog: Boolean = false
) {
    if (setting == null) {
        return
    }
    val curSetting = setting
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (showPic) {
            Spacer(modifier = Modifier.height(20.dp))
            ComposeImage(model = R.drawable.ic_cpd_phrases_empty, modifier = Modifier.size(72.dp, 96.dp))
            Spacer(modifier = Modifier.height(20.dp))
            Text(
//            stringResource(id = R.string.cpd_phrases_sug_title),
                setting.defaultPrologue.title,
                fontSize = 14.sp,
                lineHeight = 21.sp,
                color = CpdColors.FF4E5969
            )
        }

        Spacer(modifier = Modifier.height(24.dp))
        Text(
//            stringResource(id = R.string.cpd_phrases_sug_1),
            setting.defaultPrologue.advice1,
            fontSize = 14.sp,
            lineHeight = 21.sp,
            color = CpdColors.FF4E5969
        )
        Spacer(modifier = Modifier.height(12.dp))
        Column(modifier = Modifier.fillMaxWidth()) {
            val fontSize by remember {
                derivedStateOf {
                    if (isDialog) 12.sp else 14.sp
                }
            }
            val verticalPd by remember {
                derivedStateOf {
                    if (isDialog) 6.dp else 10.dp
                }
            }
            val horizontalPd by remember {
                derivedStateOf {
                    if (isDialog) 10.dp else 16.dp
                }
            }
            curSetting.defaultPrologue.templates.forEachIndexed { index, item ->
                if (item.isNotBlank()) {
                    Text(
//                    stringResource(id = R.string.cpd_phrases_ph_1),
                        item,
                        fontSize = fontSize,
                        lineHeight = 21.sp,
                        color = Color.White,
                        modifier = Modifier
                            .background(color = CpdColors.FFFF5E8B, shape = RoundedCornerShape(12.dp, 0.dp, 12.dp, 12.dp))
                            .padding(vertical = verticalPd, horizontal = horizontalPd)
                    )
                    if (index < curSetting.defaultPrologue.templates.size - 1) {
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                }
            }
//            Text(
//                stringResource(id = R.string.cpd_phrases_ph_1),
//                fontSize = fontSize,
//                lineHeight = 21.sp,
//                color = Color.White,
//                modifier = Modifier
//                    .background(color = CpdColors.FFFF5E8B, shape = RoundedCornerShape(12.dp, 0.dp, 12.dp, 12.dp))
//                    .padding(vertical = verticalPd, horizontal = horizontalPd)
//            )
//            Spacer(modifier = Modifier.height(8.dp))
//            Text(
//                stringResource(id = R.string.cpd_phrases_ph_2),
//                fontSize = fontSize,
//                lineHeight = 21.sp,
//                color = Color.White,
//                modifier = Modifier
//                    .background(color = CpdColors.FFFF5E8B, shape = RoundedCornerShape(12.dp, 0.dp, 12.dp, 12.dp))
//                    .padding(vertical = verticalPd, horizontal = horizontalPd)
//            )
//            Spacer(modifier = Modifier.height(8.dp))
//            Text(
//                stringResource(id = R.string.cpd_phrases_ph_3),
//                fontSize = fontSize,
//                lineHeight = 21.sp,
//                color = Color.White,
//                modifier = Modifier
//                    .background(color = CpdColors.FFFF5E8B, shape = RoundedCornerShape(12.dp, 0.dp, 12.dp, 12.dp))
//                    .padding(vertical = verticalPd, horizontal = horizontalPd)
//            )
        }
        Spacer(modifier = Modifier.height(24.dp))
        Text(
//            stringResource(id = R.string.cpd_phrases_sug_2),
            setting.defaultPrologue.advice2,
            fontSize = 14.sp,
            lineHeight = 21.sp,
            color = CpdColors.FF4E5969
        )
        Spacer(modifier = Modifier.height(24.dp))
        Text(
//            stringResource(id = R.string.cpd_phrases_sug_3),
            setting.defaultPrologue.advice3,
            fontSize = 14.sp,
            lineHeight = 21.sp,
            color = CpdColors.FF4E5969
        )
        Spacer(modifier = Modifier
            .height(56.dp)
            .background(color = Color.Red))
    }
}

@Composable
private fun PhrasesAboutWidget(setting: PhrasesSettingBean?, onAction: (PhrasesAction) -> Unit = {}) {
    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .padding(12.dp)
                .fillMaxWidth()
                .background(color = Color.White, shape = RoundedCornerShape(8.dp))
                .padding(vertical = 16.dp, horizontal = 12.dp)
                .heightIn(48.dp)
                .overScrollVertical()
        ) {
            AnimatedVisibility(visible = setting != null && setting.usageUrl.isNotBlank()) {
                ComposeImage(
                    model = setting!!.usageUrl,
                    contentScale = ContentScale.FillWidth
                )
            }
            Spacer(modifier = Modifier.height(108.dp))
//            Text(
//                stringResource(id = R.string.cpd_phrases_about1),
//                fontSize = 14.sp,
//                lineHeight = 14.sp,
//                color = CpdColors.FF4E5969
//            )
//            ComposeImage(
//                model = R.drawable.ic_cpd_phrases_about1,
//                contentScale = ContentScale.FillWidth,
//                modifier = Modifier.padding(vertical = 12.dp)
//            )
//            Text(
//                stringResource(id = R.string.cpd_phrases_about2),
//                fontSize = 14.sp,
//                lineHeight = 14.sp,
//                color = CpdColors.FF4E5969
//            )
//            ComposeImage(
//                model = R.drawable.ic_cpd_phrases_about2,
//                contentScale = ContentScale.FillWidth,
//                modifier = Modifier.padding(vertical = 12.dp)
//            )
//            Text(
//                stringResource(id = R.string.cpd_phrases_about3),
//                fontSize = 14.sp,
//                lineHeight = 14.sp,
//                color = CpdColors.FF4E5969
//            )
//            ComposeImage(
//                model = R.drawable.ic_cpd_phrases_about3,
//                contentScale = ContentScale.FillWidth,
//                modifier = Modifier.padding(top = 12.dp, bottom = 48.dp)
//            )
        }
        Column(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .background(color = Color.White)
                .padding(horizontal = 32.dp, vertical = 16.dp)
                .navigationBarsPadding()
        ) {
            AppButton(
                text = buildAnnotatedString {
                    append(stringResource(id = R.string.cpd_phrases_start_setting))
                    withStyle(
                        SpanStyle(
                            fontSize = 12.sp
                        )
                    ) {
                        if (setting != null && !setting.isFinishTask) {
                            append(stringResource(id = R.string.cpd_phrases_reward, setting.prologueReward))
                        }
                    }
                },
                onClick = composeClick {
                    onAction(PhrasesAction.StartSetting)
                },
                textStyle = TextStyle(color = Color.White, fontSize = 14.sp),
                contentPadding = PaddingValues(horizontal = 12.dp),
                colors = ButtonColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    contentColor = MaterialTheme.colorScheme.contentColorFor(MaterialTheme.colorScheme.primary),
                    disabledContainerColor = MaterialTheme.colorScheme.primary,
                    disabledContentColor = MaterialTheme.colorScheme.contentColorFor(MaterialTheme.colorScheme.primary),
                ),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(44.dp)
            )
        }
    }
}

@Composable
fun PhrasesMessageBox(
    list: List<PhrasesSettingBean.MyPrologue> = listOf(),
    modifier: Modifier = Modifier,
    onClose: OnClick = {},
    onPhrasesSend: (PhrasesSettingBean.MyPrologue) -> Unit = {}
) {
    LazyColumn(
        modifier = modifier
            .padding(horizontal = 16.dp, vertical = 8.dp)
            .background(
                brush = Brush.horizontalGradient(listOf(Color(0xFFFFE7ED), Color(0xFFFFFCD6))),
                shape = RoundedCornerShape(8.dp)
            )
            .heightIn(min = 48.dp, max = 164.dp)
    ) {
        stickyHeader {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        brush = Brush.horizontalGradient(listOf(Color(0xFFFFE7ED), Color(0xFFFFFCD6))),
                        shape = RoundedCornerShape(8.dp)
                    )
            ) {
                Text(
                    "自動あいさつをタップして即レス", modifier = Modifier.align(Alignment.Center),
                    fontSize = 12.sp,
                    lineHeight = 12.sp, color = CpdColors.FF1D2129
                )
                ComposeImage(model = R.drawable.ic_cpd_close_grey, modifier = Modifier
                    .size(28.dp)
                    .click {
                        onClose()
                    }
                    .padding(8.dp)
                    .align(Alignment.CenterEnd))
            }
        }

        items(list) {
            Text(
                text = it.content, modifier = Modifier
                    .padding(horizontal = 12.dp)
                    .padding(bottom = 8.dp)
                    .fillMaxWidth()
                    .background(Color.White, shape = RoundedCornerShape(4.dp))
                    .click { onPhrasesSend(it) }
                    .padding(8.dp)
                ,
                fontSize = 12.sp, lineHeight = 12.sp, color = CpdColors.FF4E5969,
                maxLines = 1, overflow = TextOverflow.Ellipsis
            )
        }
    }
}


//endregion

//region 预览
@Preview
@Composable
private fun PhrasesMessagePreview(){
    PhrasesMessageBox(
        listOf(PhrasesSettingBean.MyPrologue(content = "你好，我是cupid，欢迎使用。"),
            PhrasesSettingBean.MyPrologue(content = "你好，我是cupid，欢迎使用。"),
            PhrasesSettingBean.MyPrologue(content = "你好，我是cupid，欢迎使用。"),
            PhrasesSettingBean.MyPrologue(content = "你好，我是cupid，欢迎使用。"))
    )
}


@Preview
@Composable
private fun PhrasesPagePreview() {
    PhrasesPageContent()
}

@Preview
@Composable
private fun PhrasesEmptyWidgetPreview() {
    PhrasesEmptyWidget()
}

@Preview
@Composable
private fun PhrasesAboutWidgetPreview() {
    CupidTheme {
        Box(modifier = Modifier.background(color = CpdColors.FFF0F0F0)) {
            PhrasesAboutWidget(null)
        }
    }
}

//endregion
