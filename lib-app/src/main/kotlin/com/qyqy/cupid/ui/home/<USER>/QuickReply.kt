package com.qyqy.cupid.ui.home.chat

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.hjq.language.MultiLanguages
import com.overseas.common.utils.isPreviewOnCompose
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.isTraditionalChinese
import com.qyqy.ucoo.utils.ShushuUtils.userAdd
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

/**
 * 男用户快捷回复
 */
@Composable
fun QuickReplyBoard(
    title: String,
    replyList: List<QuickReplyData.ReplyMsg>,
    modifier: Modifier,
    posting: Boolean = false,
    closeButton: @Composable BoxScope.() -> Unit = {},
    onReply: (QuickReplyData.ReplyMsg) -> Unit = {},
) {
    val textColor = MaterialTheme.colorScheme.onPrimaryContainer
    Column(
        modifier = modifier
            .padding(12.dp)
    ) {
        Box(modifier = Modifier.fillMaxWidth()) {
            if (posting) {
                CircularProgressIndicator(
                    modifier = Modifier
                        .size(16.dp)
                        .align(Alignment.CenterStart),
                    color = textColor
                )
            }
            Text(
                text = title,
                color = MaterialTheme.colorScheme.primary,
                fontSize = 14.sp,
                modifier = Modifier.align(Alignment.Center),
                fontWeight = FontWeight.Medium
            )
            closeButton()
        }
        Spacer(modifier = Modifier.height(12.dp))
        LazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(max = 140.dp),
            verticalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            items(replyList) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .click {
                            onReply(it)
                        }
                        .background(
                            MaterialTheme.colorScheme.primaryContainer,
                            RoundedCornerShape(6.dp)
                        )
                        .padding(8.dp, vertical = 6.dp),
                    contentAlignment = Alignment.CenterStart
                ) {
                    Text(
                        text = if (isPreviewOnCompose) it.text else it.displayText,
                        fontSize = 12.sp,
                        color = textColor
                    )
                }
            }
        }
    }
}


@Preview
@Composable
private fun UCOOReplyPreview() {
    val replys = listOf(
        QuickReplyData.ReplyMsg("你好呀！希望你一天都顺顺利利！\uD83C\uDF1F"),
        QuickReplyData.ReplyMsg("嗨！今天的你遇到什么好事了吗？\uD83D\uDE0A"),
        QuickReplyData.ReplyMsg("嗨，初次见面，感觉我们会聊得来！\uD83E\uDD1D")
    )
    MaterialTheme(
        colorScheme = MaterialTheme.colorScheme.copy(
            primary = Color.White,
            primaryContainer = Color(0x0DFFFFFF),
            onPrimaryContainer = Color.White
        )
    ) {
        QuickReplyBoard(title = "快捷回复", replyList = replys, modifier = Modifier
            .width(343.dp)
            .paint(
                painterResource(id = R.drawable.bg_quick_reply),
                contentScale = ContentScale.FillBounds
            ), posting = true, closeButton = {
            Image(
                painter = painterResource(id = R.drawable.ic_close_gray),
                contentDescription = "close",
                modifier = Modifier
                    .size(14.dp)
                    .align(Alignment.CenterEnd)
            )
        })
    }
}

@Preview
@Composable
private fun CupidReplyPreview() {
    val replys = listOf(
        QuickReplyData.ReplyMsg("你好呀！希望你一天都顺顺利利！\uD83C\uDF1F"),
        QuickReplyData.ReplyMsg("嗨！今天的你遇到什么好事了吗？\uD83D\uDE0A"),
        QuickReplyData.ReplyMsg("嗨，初次见面，感觉我们会聊得来！\uD83E\uDD1D")
    )
    MaterialTheme(
        colorScheme = MaterialTheme.colorScheme.copy(
            primary = Color(0xFF1D2129),
            primaryContainer = Color.White,
            onPrimaryContainer = Color(0xFF4E5969)
        )
    ) {
        QuickReplyBoard(title = "快捷回复", replyList = replys, modifier = Modifier
            .width(343.dp)
            .paint(
                painterResource(id = R.drawable.bg_quick_reply_ja),
                contentScale = ContentScale.FillBounds
            ), posting = true, closeButton = {
            Image(
                painter = painterResource(id = R.drawable.ic_close_gray),
                contentDescription = "close",
                modifier = Modifier
                    .size(14.dp)
                    .align(Alignment.CenterEnd)
            )
        })
    }
}

interface QuickReplyApi {

    @GET("api/privatechat/v1/chat/fast_reply_msg")
    suspend fun getReplyList(@Query("target_userid") uid: Int): ApiResponse<QuickReplyData>

    /**
     * ```
     * 参数  target_userid   int
     * 参数  message_id   string
     * 参数  text_type   int 文本类型  1  text   2  traditional_text
     * ```
     */
    @POST("api/privatechat/v1/chat/send_fast_reply_msg")
    suspend fun sendQuickReply(@Body map: Map<String, String>): ApiResponse<JsonObject>
}

class QuickReplyViewModel : ViewModel() {

    private val api = createApi<QuickReplyApi>()

    private val _posting: MutableState<Boolean> = mutableStateOf(false)
    val posting: State<Boolean> = _posting

    private val _replyList: MutableState<List<QuickReplyData.ReplyMsg>> =
        mutableStateOf(emptyList())
    val replyList: State<List<QuickReplyData.ReplyMsg>> = _replyList

    private val _visible: MutableState<Boolean> = mutableStateOf(false)
    val visible: State<Boolean> = _visible

    fun close() {
        _visible.value = false
    }

    fun request(userId: Int, isCN: Boolean = true) {
        viewModelScope.launch {
            runApiCatching { api.getReplyList(userId) }
                .onSuccess {
                    val list = it.replyMsg
                    _replyList.value = list
                    _visible.value = list.isNotEmpty()
                }.toastError()
        }
    }

    fun trySendMessage(userId: String, msg: QuickReplyData.ReplyMsg) {
        viewModelScope.launch {
            if (IMCompatCore.checkCanSendMessageByC2C(userId)?.canChat != true) {
                return@launch
            }
            if (_posting.value) return@launch
            _posting.value = true
            runApiCatching {
                api.sendQuickReply(
                    mapOf(
                        "target_userid" to userId,
                        "message_id" to msg.id,
                        "text_type" to if (MultiLanguages.getAppLanguage().isTraditionalChinese) "2" else "1"
                    )
                )
            }.onSuccess {
                userAdd("manually_send_message_count" to 1)
                close()
            }.toastError()
            _posting.value = false
        }
    }
}
