package com.qyqy.cupid.ui.home.chat


import com.hjq.language.MultiLanguages
import com.qyqy.ucoo.isTraditionalChinese
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class QuickReplyData(
    @SerialName("reply_msg")
    val replyMsg: List<ReplyMsg> = listOf()
) {
    @Serializable
    data class ReplyMsg(
        @SerialName("text")
        val text: String = "",
        val id: String = "",
        @SerialName("traditional_text")
        val traditionalText: String = ""
    ) {
        val displayText: String
            get() = if (MultiLanguages.getAppLanguage().isTraditionalChinese)
                traditionalText
            else text
    }
}