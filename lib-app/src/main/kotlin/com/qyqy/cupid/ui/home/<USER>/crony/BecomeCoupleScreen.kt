package com.qyqy.cupid.ui.home.mine.crony

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.theme.navigationPadding
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppearanceStatusBars
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.runWithLoading
import com.qyqy.ucoo.im.bean.CPRightPageInfo
import com.qyqy.ucoo.im.bean.Gift
import com.qyqy.ucoo.im.bean.Scene
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.user.UserRepository
import kotlinx.coroutines.launch


@Composable
fun BecomeToCoupleScreen(user: User, info: CPRightPageInfo) {
    val coinValue = if (info.gift.crossOutPrice > 0) info.gift.crossOutPrice else info.gift.price
    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(0.81522f)
                    .paint(
                        painter = painterResource(id = R.drawable.bg_become_couple_head),
                        contentScale = ContentScale.FillBounds
                    )
            ) {
                Spacer(modifier = Modifier.fillMaxHeight(0.587f))

                Row(modifier = Modifier.fillMaxWidth()) {
                    Spacer(modifier = Modifier.weight(115f))
                    UserItem(user = user)
                    Spacer(modifier = Modifier.weight(252f))
                    UserItem(user = info.targetUser)
                    Spacer(modifier = Modifier.weight(115f))
                }
            }

            Column(
                modifier = Modifier
                    .background(Color(0xFFFE8CB7))
                    .padding(start = 6.dp, end = 6.dp, bottom = 185.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(1.72038f)
                        .paint(painter = painterResource(id = R.drawable.bg_become_couple_frame), contentScale = ContentScale.FillBounds),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Spacer(modifier = Modifier.fillMaxHeight(0.21f))

                    Text(
                        text = stringResource(id = R.string.cpd如何成为情侣, info.intimateScore, info.gift.name),
                        modifier = Modifier.padding(horizontal = 20.dp),
                        color = Color(0xFFFF4788),
                        fontSize = 14.sp,
                        lineHeight = 18.sp,
                        textAlign = TextAlign.Center,
                        fontWeight = FontWeight.SemiBold
                    )

                    Spacer(modifier = Modifier.weight(1f))

                    Column(
                        modifier = Modifier
                            .size(88.dp, 110.dp)
                            .background(Color(0xFFFFEEEF), Shapes.extraSmall),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        ComposeImage(
                            model = info.gift.icon,
                            modifier = Modifier.size(80.dp)
                        )

                        if (!sUser.isBoy) {
                            Text(
                                text = stringResource(id = R.string.cpd免费),
                                color = Color(0xFFFFB71A),
                                fontSize = 13.sp,
                                fontWeight = FontWeight.Bold
                            )
                        } else {
                            Row(verticalAlignment = Alignment.CenterVertically) {
                                Text(
                                    text = "$coinValue",
                                    color = Color(0xFFFFB71A),
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.Bold
                                )
                                Image(
                                    painter = painterResource(id = R.drawable.ic_cpd_coin),
                                    contentDescription = null,
                                    modifier = Modifier
                                        .padding(start = 3.dp)
                                        .size(14.dp)
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.weight(2f))
                }

                Spacer(modifier = Modifier.height(16.dp))

                ComposeImage(
                    model = info.rightImage,
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(0.6f),
                    contentScale = ContentScale.FillBounds
                )

            }

        }

        AppearanceStatusBars(isLight = false)

        Box {
            Image(
                painter = painterResource(id = R.drawable.bg_become_couple_head),
                contentDescription = null,
                modifier = Modifier.matchParentSize(),
                contentScale = ContentScale.Crop,
                alignment = Alignment.TopCenter
            )
            CupidAppBar(
                title = stringResource(id = R.string.cpd成为在线情侣),
                containerColor = Color.Transparent,
                navigationIconContentColor = Color.White,
            )
        }

        val userRepository = remember {
            UserRepository()
        }

        val scope = rememberCoroutineScope()

        val loadingState = LocalContentLoading.current

        val nav = LocalAppNavController.current

        Column(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .background(Brush.verticalGradient(listOf(Color(0x00FF5696), Color(0xFFFF5696), Color(0xFFFF5796))))
                .navigationPadding(minimumPadding = 20.dp)
                .padding(bottom = 10.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy((-8).dp)
        ) {
            Column(
                modifier = Modifier
                    .size(311.dp, 96.dp)
                    .paint(painterResource(id = R.drawable.frame_button), contentScale = ContentScale.FillBounds)
                    .noEffectClickable {
                        scope.launch {
                            loadingState.runWithLoading {
                                val inviteCode = info.inviteCode
                                if (inviteCode.isNullOrEmpty()) {
                                    userRepository
                                        .inviteHaveCp(info.targetUser.id, Scene.NONE, 0)
                                        .onSuccess {
                                            nav.popBackStack(CupidRouters.C2CChat)
                                        }
                                        .toastError()
                                } else {
                                    userRepository
                                        .agreeHaveCp(inviteCode, Scene.NONE, 0)
                                        .onSuccess {
                                            nav.popBackStack(CupidRouters.C2CChat)
                                        }
                                        .toastError()
                                }
                            }
                        }
                    },
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = stringResource(id = R.string.cpd送给她),
                    fontSize = 18.sp,
                    color = Color(0xFFFFEFF7),
                    fontWeight = FontWeight.SemiBold,
                    lineHeight = 20.sp
                )

                Text(
                    text = if (!sUser.isBoy) {
                        stringResource(id = R.string.cpd女性用户免费)
                    } else {
                        stringResource(id = R.string.cpd_format_金币, coinValue)
                    },
                    fontSize = 12.sp,
                    color = Color(0xFFFFEFF7),
                    lineHeight = 14.sp
                )
            }

            Text(
                text = stringResource(id = R.string.cpd成为情侣温馨提示),
                modifier = Modifier.padding(horizontal = 16.dp),
                color = Color(0x80FFFFFF),
                fontSize = 10.sp,
                lineHeight = 12.sp
            )
        }
    }
}


@Composable
private fun UserItem(user: User) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        CircleComposeImage(
            model = user.avatarUrl, modifier = Modifier
                .size(67.dp)
                .border(1.5.dp, Color(0xFFFF9BC5), CircleShape)
        )
        Spacer(modifier = Modifier.height(1.dp))
        Box(
            modifier = Modifier
                .height(24.dp)
                .requiredWidthIn(min = 80.dp, max = 150.dp)
                .padding(horizontal = 5.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = user.nickname,
                fontSize = 12.sp,
                color = Color.White,
                lineHeight = 24.sp,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}

@Preview
@Composable
private fun PreviewBecomeToCoupleScreen() {
    PreviewCupidTheme {
        BecomeToCoupleScreen(userForPreview, CPRightPageInfo(targetUser = userForPreview, gift = Gift()))
    }
}
