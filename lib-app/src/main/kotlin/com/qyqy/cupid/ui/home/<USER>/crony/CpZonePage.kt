package com.qyqy.cupid.ui.home.mine.crony

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.overseas.common.sntp.SNTPManager
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.dialog.ContentAlertDialog
import com.qyqy.cupid.ui.dialog.ContentAlertDialog2
import com.qyqy.cupid.ui.dialog.DialogButton
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.dialog.TitleAlertDialog
import com.qyqy.cupid.ui.dialog.TitleAlertDialog2
import com.qyqy.cupid.ui.home.mine.CpCrony
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.formatTimeWithHours
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.D_DIN
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.im.bean.CpTask
import com.qyqy.ucoo.im.bean.CpTaskItem
import com.qyqy.ucoo.im.bean.CpZone
import com.qyqy.ucoo.utils.takeIsNotEmpty
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive

@Composable
fun CpZonePage(fromProfile: Boolean) {
    val viewModel = viewModel<CronyViewModel>()

    val cpCrony by viewModel.cpCronyFlow.collectAsStateWithLifecycle()

    val cpZone = viewModel.cpZone

    Column(
        modifier = Modifier
            .padding(top = 20.dp)
            .fillMaxSize()
    ) {
        CpCronyCard(
            cpCrony = cpCrony,
            onWhichCp = {
                val cpRuleUrl: String = (cpCrony.self as AppUser).cpRuleUrl.takeIsNotEmpty() ?: return@CpCronyCard
                AppLinkManager.controller?.navigateWeb(cpRuleUrl)
            },
            onGoUserProfile = {
                if (!fromProfile || viewModel.userId != it.id) {
                    AppLinkManager.controller?.navigateToProfile(it.id)
                }
            }
        )

        Spacer(modifier = Modifier.height(10.dp))

        if (cpZone != null) {
            val dialogQueue = LocalDialogQueue.current

            CpZoneContent(cpZone = cpZone) { uiAction ->
                when (uiAction) {
                    is UIAction.OnRefresh -> {
                        viewModel.fetchCpZone()
                    }

                    is UIAction.OnCpHeatRule -> {
                        dialogQueue.pushCenterDialog { dialog, _ ->
                            TitleAlertDialog(
                                title = stringResource(id = R.string.cpd恋爱热度规则),
                                content = cpZone.cpRuleDesc.orEmpty(),
                                endButton = DialogButton(stringResource(id = R.string.cpd我知道了)) {
                                    dialog.dismiss()
                                }
                            )
                        }
                    }

                    is UIAction.OnDoTask -> {
                        viewModel.doCpTask(uiAction.taskId)
                    }
                }
            }

            Spacer(modifier = Modifier.height(20.dp))

            AppButton(
                text = stringResource(id = R.string.cpd解除CP),
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
                    .height(36.dp)
                    .widthIn(151.dp),
                border = BorderStroke(1.dp, color = Color(0xFFFF87B2)),
                background = Color.White,
                textStyle = TextStyle(color = Color(0xFFFF87B2), fontSize = 14.sp),
                contentPadding = PaddingValues(horizontal = 16.dp),
                onLayoutContent = {
                    if (it) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_cpd_dissolve_cp),
                            contentDescription = null,
                            modifier = Modifier
                                .align(Alignment.CenterVertically)
                                .padding(end = 3.dp)
                                .size(16.dp)
                        )
                    }
                }
            ) {
                dialogQueue.pushCenterDialog { dialog, _ ->
                    ContentAlertDialog2(
                        content = stringResource(id = R.string.cpd_tip_delete_cp),
                        topButton = DialogButton(stringResource(id = R.string.cpd_wait_think)) {
                            dialog.dismiss()
                        },
                        bottomButton = DialogButton(stringResource(id = R.string.cpd_delete_confirm)) {
                            dialog.dismiss()
                            viewModel.dissolveCp()
                        }
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(30.dp))
    }
}


private sealed interface UIAction {

    object OnCpHeatRule : UIAction

    object OnRefresh : UIAction

    data class OnDoTask(val taskId: String) : UIAction
}

@Composable
private fun CpZoneContent(
    cpZone: CpZone,
    onAction: (UIAction) -> Unit = {},
) {
    val cpTask = cpZone.cpTask

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(Shapes.corner12)
            .background(Color(0xFFFFE1EC))
            .border(1.dp, Color(0x4DFFFFFF), Shapes.corner12)
            .padding(horizontal = 16.dp)
    ) {
        CpHeatItem(
            cpZone = cpZone,
            modifier = Modifier
                .padding(top = 12.dp)
                .fillMaxWidth(),
            onAction = onAction
        )

        if (cpTask != null) {

            TaskTitle(cpTask, modifier = Modifier.padding(top = 2.dp))

            cpTask.taskList?.forEach { item ->
                HorizontalDivider(modifier = Modifier.fillMaxWidth(), thickness = 0.5.dp, color = Color(0xFFFFB5CF))
                TaskItem(
                    taskDesc = item.taskName,
                    taskProgress = "${item.taskProgress}%% ${item.taskBonus}",
                    button = item.taskBtnLabel,
                    isDone = item.taskIsFinished,
                ) {
                    onAction(UIAction.OnDoTask(item.taskId))
                }
            } ?: run {
                Spacer(modifier = Modifier.height(5.dp))
            }
        }
    }
}

@Composable
private fun CpHeatItem(cpZone: CpZone, modifier: Modifier, onAction: (UIAction) -> Unit) {
    Column(
        modifier = modifier
            .clip(Shapes.small)
            .background(Color(0x4DFFFFFF))
            .padding(start = 12.dp, top = 18.dp, end = 12.dp, bottom = 14.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            AppText(
                text = buildAnnotatedString {
                    append(stringResource(id = R.string.cpd七日恋爱热度))
                    append(": ")
                    withStyle(SpanStyle(fontFamily = D_DIN, fontSize = 18.sp, color = Color(0xFFFE0466))) {
                        append("${cpZone.hotDegree}℃")
                    }
                },
                color = Color(0xFF590D27),
                fontSize = 16.sp,
                lineHeight = 18.sp,
                fontWeight = FontWeight.SemiBold,
            )
            Spacer(modifier = Modifier.width(4.dp))
            Image(
                painter = painterResource(id = R.drawable.ic_cpd_cp_zone_qa),
                contentDescription = null,
                modifier = Modifier
                    .size(18.dp)
                    .noEffectClickable {
                        onAction(UIAction.OnCpHeatRule)
                    },
            )
            Spacer(modifier = Modifier.weight(1f))
            if (cpZone.hotDegree < cpZone.hotDegreeGoal) {
                CountDownTimerBar(cpZone.hotDegreeDeadTime * 1000, Color(0xFFFF249A)) {
                    onAction(UIAction.OnRefresh)
                }
            }
        }
        Spacer(modifier = Modifier.height(9.dp))
        AppText(
            text = cpZone.heatTip,
            fontSize = 11.sp,
            color = Color(0xFFFE0466),
            fontWeight = FontWeight.Medium
        )
        BoxWithConstraints(
            modifier = Modifier
                .padding(vertical = 10.dp)
                .fillMaxWidth()
                .height(16.dp)
                .background(Color(0xFFFFB1CC), Shapes.small)
                .clip(Shapes.small)
        ) {
            val progress = cpZone.hotDegree.toFloat().div(cpZone.hotDegreeGoal).coerceAtMost(1f)
            val width by animateDpAsState(
                targetValue = maxWidth.times(progress),
                label = "cp_heat_progress"
            )
            Box(
                modifier = Modifier
                    .width(width)
                    .fillMaxHeight()
                    .background(Brush.horizontalGradient(listOf(Color(0xFFFFA9BE), Color(0xFFFF5B94))), Shapes.small)
            )
        }
        Box(modifier = Modifier.fillMaxWidth()) {
            Text(
                text = "0 ℃",
                modifier = Modifier.align(Alignment.CenterStart),
                color = Color(0xFF590D27),
                fontSize = 14.sp,
                fontFamily = D_DIN,
                lineHeight = 14.sp
            )
            Text(
                text = "${cpZone.hotDegreeGoal} ℃",
                modifier = Modifier.align(Alignment.CenterEnd),
                color = Color(0xFF590D27),
                fontSize = 14.sp,
                fontFamily = D_DIN,
                lineHeight = 14.sp
            )
        }
    }
}


@Composable
private fun TaskTitle(cpTask: CpTask, modifier: Modifier) {
    val deadline = cpTask.endTimestamp.times(1000)

    var countDownTime by remember {
        mutableStateOf(emptyList<String>())
    }

    LaunchedEffect(key1 = cpTask.version, key2 = cpTask.dailyTaskFinished, key3 = cpTask.ongoing) {
        if (cpTask.version != 2 || cpTask.dailyTaskFinished || cpTask.ongoing == null) {
            countDownTime = emptyList()
            return@LaunchedEffect
        }
        while (isActive) {
            delay(1000)
            val now = SNTPManager.now()
            if (now >= deadline) {
                countDownTime = emptyList()
                break
            } else {
                countDownTime = formatTimeWithHours(deadline.minus(now)).split(":")
            }
        }
    }

    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = 8.dp, bottom = 16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            AppText(
                text = buildAnnotatedString {
                    append(cpTask.dailyTaskTitle.ifEmpty {
                        stringResource(id = R.string.cpd热度值奖励任务)
                    })
                },
                color = Color(0xFF590D27),
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
            )
        }

        AutoSizeText(
            text = cpTask.dailyTaskNote,
            color = Color(0xFF804458),
            fontSize = 12.sp,
            maxLines = 3,
        )
    }
}


@Composable
private fun TaskItem(
    taskDesc: String,
    taskProgress: String,
    button: String,
    isDone: Boolean,
    action: () -> Unit = {},
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 18.dp),
    ) {
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(10.dp)
        ) {
            AppText(
                text = taskDesc,
                color = Color(0xFF590D27),
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                lineSpacingRatio = 0.2f,
            )

            AppText(
                text = buildAnnotatedString {
                    val list = taskProgress.split("%%")
                    list.forEachIndexed { index, text ->
                        if (index > 0 && index == list.lastIndex) {
                            withStyle(SpanStyle(color = Color(0xFFFE0466))) {
                                append(text)
                            }
                        } else {
                            append(text)
                        }
                    }
                },
                color = Color(0xFF804458),
                fontSize = 12.sp,
                lineSpacingRatio = 0.2f,
            )
        }
        Spacer(modifier = Modifier.width(5.dp))
        Button(
            onClick = action,
            modifier = Modifier
                .padding(8.dp)
                .size(68.dp, 28.dp),
            enabled = !isDone,
            contentPadding = PaddingValues(horizontal = 5.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFFFF3A80),
                disabledContainerColor = Color(0xFFFF8EB5),
            )
        ) {
            AutoSizeText(text = button, color = Color.White, fontSize = 13.sp)
        }
    }
}


@Composable
private fun CountDownTimerBar(hotDegreeDeadTime: Long, textColor: Color = Color(0xFFFE0466), onTickComplete: () -> Unit) {
    var countDownTime by remember {
        mutableStateOf(emptyList<String>())
    }

    LaunchedEffect(key1 = hotDegreeDeadTime) {
        if (hotDegreeDeadTime.minus(SNTPManager.now()) > 0) {
            countDownTime = formatTimeWithHours(hotDegreeDeadTime.minus(SNTPManager.now())).split(":")
        }
        while (isActive) {
            delay(1000)
            val now = SNTPManager.now()
            if (now >= hotDegreeDeadTime) {
                countDownTime = emptyList()
                onTickComplete.invoke()
                break
            } else {
                countDownTime = formatTimeWithHours(hotDegreeDeadTime.minus(now)).split(":")
            }
        }
    }
    if (countDownTime.size == 3) {
        val cellModifier = remember {
            Modifier
                .height(20.dp)
                .requiredWidthIn(22.dp)
                .clip(RoundedCornerShape(3.dp))
                .background(Color(0xFFFFB1CC))
                .padding(horizontal = 2.dp)
                .wrapContentHeight()
        }
        val style = remember(key1 = "style") {
            TextStyle(fontSize = 14.sp, color = textColor, fontFamily = D_DIN)
        }
        Text(
            text = countDownTime[0],
            modifier = cellModifier,
            style = style,
            textAlign = TextAlign.Center,
        )

        Text(text = ":", color = Color(0xFFFE0466), fontSize = 14.sp, fontWeight = FontWeight.Bold)

        Text(
            text = countDownTime[1],
            modifier = cellModifier,
            style = style,
            textAlign = TextAlign.Center,
        )

        Text(text = ":", color = Color(0xFFFE0466), fontSize = 14.sp, fontWeight = FontWeight.Bold)

        Text(
            text = countDownTime[2],
            modifier = cellModifier,
            style = style,
            textAlign = TextAlign.Center,
        )
    }
}


@Preview(showBackground = true, backgroundColor = 0xFF111111)
@Composable
private fun PreviewCpZonePage2() {
    PreviewCupidTheme {
        Column(
            modifier = Modifier
                .padding(top = 20.dp)
                .fillMaxSize()
        ) {
            CpCronyCard(
                cpCrony = CpCrony.previewSimple2(),
                onWhichCp = { },
                onGoUserProfile = {})

            Spacer(modifier = Modifier.height(10.dp))

            CpZoneContent(
                cpZone = CpZone(
                    60,
                    100,
                    233,
                    userForPreview,
                    userForPreview,
                    CpTask(version = 2, taskList = listOf(CpTaskItem("abc1", "xyz1", "0", true, "任务1", "进度1"))),
                    heatTip = "7日間の熱度がXXXX℃に下回る場合恋人関係が自動的に解消される",
                    hotDegreeDeadTime = 1735299820,
                ).also {
                    it.hasNextLevel = true
                    it.isPublishCp = false
                }
            )

            Spacer(modifier = Modifier.height(30.dp))
        }
    }
}
