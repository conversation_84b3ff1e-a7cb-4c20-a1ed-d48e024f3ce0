package com.qyqy.cupid.ui.home.mine.crony

import android.graphics.drawable.ColorDrawable
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.GenericShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.bumptech.glide.integration.compose.placeholder
import com.qyqy.cupid.config.CupidConst
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.home.mine.CpCrony
import com.qyqy.cupid.ui.home.mine.CronyLabel
import com.qyqy.cupid.ui.home.mine.CronyTable
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.LocalTracePage
import com.qyqy.ucoo.R
import com.qyqy.ucoo.TraceConst
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.getApproximateColor
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.theme.D_DIN
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.VerticalGrid
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.utils.takeIsNotEmpty

@Composable
fun CronyHomePage(fromProfile: Boolean) {
    val viewModel = viewModel<CronyViewModel>()
    val userId = viewModel.userId

    val dialogQueue = LocalDialogQueue.current
    val appNavController = LocalAppNavController.current
    Column(
        modifier = Modifier
            .padding(top = 20.dp)
            .fillMaxSize()
    ) {

        if (!fromProfile || sUser.id == userId || viewModel.cpCrony.hasCp()) {
            CpCronyCard(
                cpCrony = viewModel.cpCrony,
                onWhichCp = {
                    val cpRuleUrl: String =
                        (viewModel.cpCrony.self as AppUser).cpRuleUrl.takeIsNotEmpty()
                            ?: return@CpCronyCard
                    AppLinkManager.controller?.navigateWeb(cpRuleUrl)
                    if (!fromProfile) {
                        Analytics.reportClickEvent(TracePoints.JP_MY_PAGE_MAKE_CP)
                    }
                },
                onGoUserProfile = {
                    if (!fromProfile || userId != it.id) {
                        AppLinkManager.controller?.navigateToProfile(it.id)
                    }
                }
            )
        }


        Spacer(modifier = Modifier.height(10.dp))

        viewModel.cronyTable?.apply {
            CronyUserListCard(
                showEmptySeat = sUser.id == userId,
                cronyTable = this,
                onGoManagePage = {
                    appNavController.navigate(CupidRouters.CRONY_MANAGE)
                },
                onClickUserLabel = {
                    if (it is CronyLabel.Simple) {
                        Analytics.reportClickEvent(TracePoints.MY_PAGE_RELATIVE_FRIEND_CELL_CLICK_PROFILE)
                        appNavController.navigateToProfile(it.user.id)
                    } else {
                        Analytics.reportClickEvent(TracePoints.MY_PAGE_RELATIVE_FRIEND_CELL_CLICK_INVITE)
                        dialogQueue.pushCenterDialog { dialog, _ ->
                            SelectRelationshipLabelDialog(labelTagList) { label ->
                                dialog.dismiss()
                                appNavController.navigate(
                                    CupidRouters.INVITE_TO_CRONY,
                                    mapOf(Const.KEY_ID to label.id)
                                )
                            }
                        }
                    }
                }
            )
        }

        Spacer(modifier = Modifier.height(30.dp))
    }
}

@Composable
@Preview
private fun PreviewCronyHomePage() {
    PreviewCupidTheme {
        Column(
            modifier = Modifier
                .padding(top = 20.dp)
                .fillMaxSize()
        ) {
            CpCronyCard(
                cpCrony = CpCrony.previewSimple2(),
                onWhichCp = { },
                onGoUserProfile = {})

            Spacer(modifier = Modifier.height(10.dp))

            CronyUserListCard(
                showEmptySeat = true,
                CronyTable(
                    emptyCount = 2,
                    totalCount = 6,
                    labelList = listOf(CronyLabel.previewSimple(), CronyLabel.previewSimple()),
                ), {}, {})

            Spacer(modifier = Modifier.height(30.dp))
        }
    }
}


@Composable
fun CpCronyCard(cpCrony: CpCrony, onWhichCp: () -> Unit, onGoUserProfile: (User) -> Unit) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(2.25658f),
        shape = Shapes.corner12,
    ) {
        //卡片背景
        ComposeImage(
            model = cpCrony.cardBgUrl.orEmpty(),
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.verticalGradient(
                        listOf(
                            cpCrony.gradientTopColor,
                            cpCrony.gradientBottomColor
                        )
                    ),
                )
                .clip(Shapes.corner12),
            loading = placeholder(ColorDrawable(android.graphics.Color.TRANSPARENT)),
            failure = placeholder(ColorDrawable(android.graphics.Color.TRANSPARENT))
        )
        val isSimple = cpCrony is CpCrony.Simple
        Column(verticalArrangement = Arrangement.Center) {
            if (isSimple) {
                Spacer(modifier = Modifier.height(12.dp))
            }
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically
            ) {
                UserAvatarWithNameItem(cpCrony.avatar1, cpCrony.name1, Modifier.noEffectClickable {
                    onGoUserProfile(cpCrony.self)
                })
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Spacer(modifier = Modifier.height(if (isSimple) 10.dp else 32.dp))
                    Column(
                        modifier = Modifier
                            .align(Alignment.CenterHorizontally)
                            .height(66.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {

                        val cpValue = cpCrony.cpValue.orEmpty()
                        LocalDensity.current.apply {
                            Text(
                                text = stringResource(id = R.string.cpd爱意值).takeIf { cpValue.isNotEmpty() }
                                    ?: "",
                                color = Color.White,
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Bold,
                                style = TextStyle(
                                    shadow = Shadow(
                                        color = Color.White.getApproximateColor(),
                                        offset = Offset(0.15.dp.toPx(), 0.3.dp.toPx()),
                                        blurRadius = 0.2.dp.toPx()
                                    )
                                )
                            )
                        }
                        AutoSizeText(
                            text = cpValue,
                            modifier = Modifier.padding(horizontal = 3.dp),
                            color = Color(0xFFFF035E),
                            fontSize = 15.sp,
                            fontFamily = D_DIN,
                            maxLines = 1,
                        )
                    }
                    if (isSimple) {
                        AppText(
                            text = cpCrony.cpDurationDay.orEmpty(),
                            color = Color.White,
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            lineHeight = 20.sp,
                            modifier = Modifier
                                .background(Color(0x59030303), Shapes.large)
                                .padding(horizontal = 6.dp, vertical = 2.dp)
                        )
                    } else {
                        Button(
                            modifier = Modifier.size(96.dp, 32.dp),
                            colors = ButtonDefaults.buttonColors(Color.Transparent),
                            contentPadding = PaddingValues(),
                            elevation = ButtonDefaults.buttonElevation(2.dp),
                            onClick = { onWhichCp() },
                        ) {
                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .background(
                                        brush = Brush.verticalGradient(
                                            listOf(
                                                Color(0xFFFFD1DF), Color(0xFFFF6694)
                                            )
                                        ),
                                        shape = CircleShape
                                    )
                                    .border(
                                        width = 0.8.dp,
                                        brush = Brush.verticalGradient(
                                            listOf(
                                                Color(0xA6FFFFFF),
                                                Color(0xD9DD2566)
                                            )
                                        ),
                                        shape = CircleShape
                                    )
                                    .padding(PaddingValues(horizontal = 5.dp))
                            ) {
                                AutoSizeText(
                                    modifier = Modifier.align(Alignment.Center),
                                    text = stringResource(R.string.cpd去组情侣),
                                    color = Color(0xFFFFFEFE),
                                    fontSize = 14.sp,
                                )
                            }
                        }
                    }
                }
                UserAvatarWithNameItem(cpCrony.avatar2, cpCrony.name2, Modifier.noEffectClickable {
                    if (cpCrony.target != null) {
                        onGoUserProfile(cpCrony.target!!)
                    }
                })
            }
        }
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(8.dp)
        ) {
            val nav = LocalAppNavController.current
            Image(
                painter = painterResource(id = R.drawable.cpd_ic_cp_rank),
                contentDescription = "rank",
                modifier = Modifier
                    .size(22.dp)
                    .align(Alignment.TopEnd)
                    .click {
                        nav.navigateByLink(CupidConst.URL.CP_RANK)
                    }
            )
        }
    }
}



@Composable
private fun UserAvatarWithNameItem(avatar: Any?, name: String?, modifier: Modifier = Modifier) {
    Column(
        modifier = Modifier.width(72.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        if (avatar != null) {
            CircleComposeImage(
                model = avatar,
                modifier = Modifier
                    .border(1.dp, Color.White, CircleShape)
                    .size(72.dp)
                    .then(modifier),
            )
        } else {
            Box(
                modifier = Modifier
                    .size(72.dp)
                    .clip(CircleShape)
                    .background(
                        Brush.radialGradient(
                            listOf(
                                Color.White,
                                Color(0xFFFFEDF1),
                                Color(0xFFFFD3DB)
                            )
                        )
                    )
                    .padding(4.dp),
                contentAlignment = Alignment.Center
            ) {
                AutoSizeText(
                    text = stringResource(id = R.string.cpd暂无情侣),
                    color = Color(0xFFF07D94),
                    fontSize = 12.sp,
                    alignment = Alignment.Center,
                    maxLines = 2
                )
            }
        }

        if (name != null) {
            Text(
                text = name,
                modifier = Modifier
                    .padding(top = 4.dp)
                    .wrapContentWidth(Alignment.CenterHorizontally),
                color = Color.White,
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp,
                overflow = TextOverflow.Ellipsis,
                maxLines = 1,
            )
        }
    }
}


@Composable
@Preview
private fun CpCardPreview() {
    PreviewCupidTheme {
        CpCronyCard(
            cpCrony = CpCrony.previewSimple1(),
            onWhichCp = { },
            onGoUserProfile = {})
    }
}

@Composable
@Preview
private fun CpCardPreview2() {
    PreviewCupidTheme {
        CpCronyCard(
            cpCrony = CpCrony.previewSimple2(),
            onWhichCp = { },
            onGoUserProfile = {})
    }
}

@Composable
private fun CronyUserListCard(
    showEmptySeat: Boolean,
    cronyTable: CronyTable,
    onGoManagePage: () -> Unit,
    onClickUserLabel: (CronyLabel) -> Unit,
) {
    val labelShape = remember {
        GenericShape { size, _ ->
            val topRadius = size.height.times(0.4f)
            val bottomRadius = size.height.times(0.3f)
            arcTo(
                Rect(0f, 0f, topRadius.times(2), topRadius.times(2)),
                165f, 105f, false
            )
            lineTo(size.width.minus(topRadius), 0f)
            arcTo(
                Rect(size.width.minus(topRadius.times(2)), 0f, size.width, topRadius.times(2)),
                -90f, 105f, false
            )
            arcTo(
                Rect(
                    size.width.minus(bottomRadius.times(2)).minus(3),
                    size.height.minus(bottomRadius.times(2)),
                    size.width.minus(3),
                    size.height
                ),
                15f, 75f, false
            )
            arcTo(
                Rect(
                    3f,
                    size.height.minus(bottomRadius.times(2)),
                    3f.plus(bottomRadius.times(2)),
                    size.height
                ),
                90f, 75f, false
            )
        }
    }

    Surface(
        modifier = Modifier.fillMaxWidth(),
        shape = Shapes.corner12,
        border = BorderStroke(1.5.dp, Color.White)
    ) {
        Column(
            modifier = Modifier
                .heightIn(min = 0.dp)
                .background(
                    brush = Brush.verticalGradient(listOf(Color(0xFFDFC9FF), Color(0xFFC09AF0))),
                )
                .padding(bottom = 8.dp)
        ) {
            Box(
                modifier = Modifier.fillMaxWidth(),
            ) {
                Image(
                    modifier = Modifier
                        .height(36.dp)
                        .align(Alignment.Center),
                    painter = painterResource(id = R.drawable.ic_cpd_crony_top_tag),
                    contentDescription = null
                )
            }


            val modifier = Modifier.fillMaxWidth()

            VerticalGrid(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp),
                columns = 3,
                horizontalSpace = 12.dp,
                verticalSpace = 16.dp,
            ) {
                for (label in cronyTable.labelList) {
                    RelationShipItem(
                        Modifier
                            .fillMaxWidth()
                            .clickable {
                                onClickUserLabel(label)
                            },
                        labelShape,
                        label
                    )
                }

                repeat(cronyTable.placeholderCount) {
                    RelationShipItem(
                        Modifier.fillMaxWidth(),
                        labelShape,
                        CronyLabel.Placeholder
                    )
                }

                if (showEmptySeat) {
                    repeat(cronyTable.emptyCount) {
                        RelationShipItem(
                            modifier.clickable {
                                onClickUserLabel(CronyLabel.None)
                            },
                            labelShape,
                            CronyLabel.None
                        )
                    }
                }
            }

            if (showEmptySeat && cronyTable.hasLabel) {
                Box(
                    modifier = Modifier
                        .padding(top = 8.dp, bottom = 12.dp)
                        .align(Alignment.CenterHorizontally)
                        .height(36.dp)
                        .background(
                            Brush.verticalGradient(
                                listOf(
                                    Color(0xFFE8DCFF),
                                    Color(0xFF914EFF)
                                )
                            ), CircleShape
                        )
                        .border(1.dp, Color(0xA6FFFFFF), CircleShape)
                        .clip(CircleShape)
                        .clickable(onClick = onGoManagePage)
                        .padding(vertical = 5.dp, horizontal = 15.dp),
                    contentAlignment = Alignment.Center,
                ) {
                    AutoSizeText(
                        text = stringResource(id = R.string.cpd管理密友关系),
                        color = Color(0xFFFFFFFF),
                        fontSize = 14.sp,
                        maxLines = 1,
                        alignment = Alignment.Center
                    )
                }
            }
        }
    }
}


@Composable
private fun RelationShipItem(
    modifier: Modifier,
    shape: Shape,
    label: CronyLabel,
) {
    Box {
        Column(
            modifier = Modifier
                .aspectRatio(0.816667f)
                .run {
                    if (label is CronyLabel.Simple) {
                        background(
                            Brush.verticalGradient(
                                listOf(
                                    label.gradientTopColor,
                                    label.gradientBottomColor
                                )
                            ),
                            Shapes.small
                        )
                    } else {
                        background(Color(0xFFE3D0FC), Shapes.small)
                    }
                }
                .border(1.dp, Color(0x80FFFFFF), Shapes.small)
                .clip(Shapes.small)
                .then(modifier),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Spacer(modifier = Modifier.fillMaxHeight(0.212121f))
            val imageModifier = Modifier
                .fillMaxWidth(0.57143f)
                .aspectRatio(1f)
            if (label.avatar is Painter) {
                Image(
                    modifier = imageModifier.clip(CircleShape),
                    painter = label.avatar as Painter,
                    contentDescription = null,
                )
            } else {
                val avatarUrl = label.avatar
                CircleComposeImage(
                    model = avatarUrl,
                    modifier = imageModifier,
                )
            }
            AppText(
                modifier = Modifier.padding(top = 10.dp, start = 6.dp, end = 6.dp),
                text = label.name,
                color = label.textColor,
                fontSize = 12.sp,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
        }

        if (label is CronyLabel.Simple) {
            LocalDensity.current.apply {
                Surface(
                    modifier = Modifier
                        .align(Alignment.TopCenter)
                        .widthIn(min = 56.dp)
                        .height(20.dp)
                        .offset(y = (-4).dp),
                    shape = shape,
                    border = BorderStroke(1.dp, Color.White),
                    color = label.labelColor,
                    contentColor = Color.White
                ) {
                    Box(
                        modifier = Modifier.padding(horizontal = 8.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        AutoSizeText(
                            text = label.label.name,
                            fontSize = 12.sp,
                            style = LocalTextStyle.current.merge(
                                TextStyle(
                                    shadow = Shadow(
                                        color = Color.Green.getApproximateColor(),
                                        offset = Offset(0.15.dp.toPx(), 0.3.dp.toPx()),
                                        blurRadius = 0.2.dp.toPx()
                                    ),
                                )
                            ),
                        )
                    }
                }
            }
        }
    }
}

@Composable
@Preview
private fun PreviewCronyUserListCard() {
    PreviewCupidTheme {
        CronyUserListCard(
            showEmptySeat = true,
            CronyTable(
                emptyCount = 2,
                totalCount = 6,
                labelList = listOf(CronyLabel.previewSimple(), CronyLabel.previewSimple()),
            ), {}, {})
    }
}
