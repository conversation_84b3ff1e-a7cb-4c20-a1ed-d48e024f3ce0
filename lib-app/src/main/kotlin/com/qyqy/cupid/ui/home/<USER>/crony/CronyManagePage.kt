package com.qyqy.cupid.ui.home.mine.crony

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.dialog.ContentAlertDialog
import com.qyqy.cupid.ui.dialog.DialogButton
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.cupid.ui.relations.TabLayout
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.state.CupidStateListView
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.UserInfo
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.data.LabelItem
import com.qyqy.ucoo.compose.presentation.ff.FamilyListViewModel
import com.qyqy.ucoo.compose.presentation.ff.InviteViewModel
import com.qyqy.ucoo.compose.presentation.ff.LatestViewModel
import com.qyqy.ucoo.compose.presentation.ff.MyFriendsViewModel
import com.qyqy.ucoo.compose.presentation.ff.UserItem
import com.qyqy.ucoo.compose.state.PageResult
import com.qyqy.ucoo.compose.state.StateViewModel
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AppearanceStatusBars
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.bean.FamilyRelation
import com.qyqy.ucoo.im.bean.colorSmallFont
import com.qyqy.ucoo.im.bean.colorSmallFontBg
import com.qyqy.ucoo.im.chat.share.ShareApi
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.user.RelationshipApi
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.int
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive


@Composable
fun SelectRelationshipLabelDialog(
    labelList: List<LabelItem>,
    onConfirm: (LabelItem) -> Unit = {},
) {
    var selectedIndex by remember(labelList) {
        mutableIntStateOf(-1)
    }
    Column(
        modifier = Modifier
            .width(290.dp)
            .background(Color(0xFFFFFFFF), Shapes.small)
            .padding(vertical = 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        AutoSizeText(
            modifier = Modifier
                .height(24.dp)
                .padding(horizontal = 10.dp),
            text = stringResource(id = R.string.cpd请选择密友关系标签),
            fontSize = 17.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF1D2129),
            maxLines = 2,
            alignment = Alignment.Center
        )
        LazyVerticalGrid(
            columns = GridCells.Fixed(3),
            modifier = Modifier
                .padding(top = 10.dp)
                .sizeIn(maxHeight = 280.dp)
                .selectableGroup(),
            contentPadding = PaddingValues(horizontal = 16.dp, vertical = 10.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            itemsIndexed(labelList, key = { _, item ->
                item
            }) { index, item ->
                val selected = selectedIndex == index
                Surface(
                    selected = selected,
                    onClick = {
                        selectedIndex = index
                    },
                    modifier = Modifier.height(36.dp),
                    shape = Shapes.extraSmall,
                    color = if (selected) Color(0xFFFF5E8B) else Color(0xFFF1F2F3),
                    contentColor = if (selected) Color.White else Color(0xFF86909C),
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(5.dp), contentAlignment = Alignment.Center
                    ) {
                        AutoSizeText(text = item.name, fontSize = 15.sp)
                    }
                }
            }
        }
        Button(
            onClick = {
                onConfirm(labelList[selectedIndex])
            },
            modifier = Modifier
                .padding(top = 22.dp)
                .size(238.dp, 36.dp),
            enabled = selectedIndex > -1,
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFFFF5E8B),
                disabledContainerColor = Color(0x80FF5E8B)
            )
        ) {
            Text(
                text = stringResource(id = R.string.cpd下一步),
                fontSize = 16.sp,
                color = if (selectedIndex > -1) Color.White else Color(0x80FFFFFF)
            )
        }
    }
}

@Preview(widthDp = 375)
@Composable
private fun PreviewSelectRelationshipLabelDialog() {
    SelectRelationshipLabelDialog(
        listOf(
            LabelItem(name = "你大爷"),
            LabelItem(name = "你二爷"),
            LabelItem(name = "你三爷"),
            LabelItem(name = "你四爷"),
            LabelItem(name = "你五爷"),
            LabelItem(name = "你六爷"),
            LabelItem(name = "你七爷"),
        )
    )
}


@Composable
fun InviteToCronyPage(
    tagId: Int = 0,
    latestViewModel: LatestViewModel = viewModel(factory = viewModelFactory {
        initializer {
            LatestViewModel(tagId)
        }
    }),
    friendsViewModel: MyFriendsViewModel = viewModel(factory = viewModelFactory {
        initializer {
            MyFriendsViewModel(tagId)
        }
    }),
) {
    AppearanceStatusBars(isLight = true)

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        CupidAppBar(title = stringResource(id = R.string.cpd_send_family_invite), actions = {
            Box(modifier = Modifier.size(40.dp))
        })

        val pagerState = rememberPagerState(0) {
            2
        }

        val titles = listOf(stringResource(id = R.string.cpd最近聊天), stringResource(id = R.string.cpd我的好友))
        TabLayout(modifier = Modifier.padding(horizontal = 48.dp), pagerState = pagerState, titles = titles)
        HorizontalPager(state = pagerState) {
            when (it) {
                0 -> InviteUserList(viewModel = latestViewModel)
                else -> InviteUserList(viewModel = friendsViewModel)
            }
        }

    }
}


@Composable
private fun InviteUserList(viewModel: InviteViewModel) {
    CupidStateListView(viewModel = viewModel) { item, _, coroutineScope, loadingState ->
        InviteToCronyUserItem(user = item) {
            coroutineScope.launch {
                loadingState.value = true
                viewModel.inviteUser(item.userId)
                loadingState.value = false
            }
        }
    }
}

/**
 * 邀请加入亲友团
 */
@Composable
private fun InviteToCronyUserItem(
    user: UserInfo,
    onInvite: () -> Unit = {},
) {
    UserItem(user = user, nameColor = MaterialTheme.colorScheme.onSurface, onAvatarClicked = {
        AppLinkManager.controller?.navigateToProfile(it.id)
    }) {
        AppButton(
            text = stringResource(id = if (user.hasInvited) R.string.cpd_has_invited else R.string.cpd_invite),
            onClick = onInvite,
            border = BorderStroke(1.dp, color = if (user.hasInvited) Color(0xFFE5E6EB) else CpdColors.FFFF5E8B),
            background = Color.White,
            textStyle = TextStyle(color = if (user.hasInvited) Color(0xFFE5E6EB) else CpdColors.FFFF5E8B, fontSize = 14.sp),
            enabled = !user.hasInvited,
            contentPadding = PaddingValues(horizontal = 12.dp),
            modifier = Modifier
                .height(32.dp)
                .widthIn(88.dp)
        )
    }
}

@Preview
@Composable
private fun InviteToCronyPagePreviewer() {
    PreviewCupidTheme {
        InviteToCronyPage()
    }
}

@Composable
fun CronyManagePage(familyListViewModel: FamilyListViewModel = viewModel()) {
    val dialogQueue = LocalDialogQueue.current
    val loadingState = LocalContentLoading.current
    val scope = rememberCoroutineScope()

    AppearanceStatusBars(isLight = true)
    Column(modifier = Modifier.fillMaxSize()) {
        CupidAppBar(title = stringResource(id = R.string.cpd_title_family_setting), actions = {
            Box(modifier = Modifier.size(40.dp))
        })
        CronyList(onRemoved = { brokeId ->
            dialogQueue.pushCenterDialog { dialog, onAction ->
                RemoveCronyConfirmDialogContent({
                    dialog.dismiss()
                }) {
                    dialog.dismiss()
                    scope.launch {
                        loadingState.value = true
                        familyListViewModel.giveupReleationship(brokeId).onSuccess {
                            familyListViewModel.filterItems { f -> f.id != brokeId }
                        }
                        loadingState.value = false
                    }
                }
            }
        }, familyListViewModel)
    }
}

@Preview
@Composable
private fun PreviewCronyManagePage() {
    PreviewCupidTheme {
        CronyManagePage()
    }
}


@Composable
private fun CronyList(onRemoved: (id: Int) -> Unit, familyListViewModel: FamilyListViewModel) {
    CupidStateListView(
        viewModel = familyListViewModel,
        keyProvider = { _, familyRelation -> familyRelation.id },
        modifier = Modifier.fillMaxSize()
    ) { item: FamilyRelation, _: Int, _: CoroutineScope, _: MutableState<Boolean> ->
        RemoveCronyItem(user = item) {
            onRemoved(item.id)
        }
    }
}

/**
 * 解除亲友团
 */
@Composable
private fun RemoveCronyItem(
    user: FamilyRelation,
    onRemove: () -> Unit = {},
) {
    UserItem(user = user.user, nameColor = MaterialTheme.colorScheme.onSurface, onAvatarClicked = {
        AppLinkManager.controller?.navigateToProfile(it.id)
    }, afterNickName = {
        Spacer(modifier = Modifier.width(4.dp))
        AppText(
            text = user.label.name,
            color = user.label.colorSmallFont,
            textAlign = TextAlign.Center,
            fontSize = 11.sp,
            lineHeight = 18.sp,
            modifier = Modifier
                .defaultMinSize(34.dp, 18.dp)
                .background(user.label.colorSmallFontBg, shape = RoundedCornerShape(4.dp))
                .padding(horizontal = 6.dp)
        )
    }) {
        AppButton(
            text = stringResource(id = R.string.cpd_remote_relation),
            onClick = onRemove,
            border = BorderStroke(1.dp, color = CpdColors.FFFF5E8B),
            background = Color.White,
            textStyle = TextStyle(color = CpdColors.FFFF5E8B, fontSize = 14.sp),
            contentPadding = PaddingValues(horizontal = 12.dp),
            modifier = Modifier
                .height(32.dp)
                .widthIn(88.dp)
        )
    }
}

@Composable
private fun RemoveCronyConfirmDialogContent(onDismiss: () -> Unit, onRemove: () -> Unit) {
    ContentAlertDialog(
        content = stringResource(id = R.string.cpd_confirm_broke_family),
        startButton = DialogButton(stringResource(id = R.string.cpd取消), onDismiss),
        endButton = DialogButton(stringResource(id = R.string.cpd_confirm), onRemove)
    )
}

@Preview
@Composable
private fun PreviewRemoveCronyConfirmDialogContent() {
    RemoveCronyConfirmDialogContent({}, {})
}

@Composable
fun InviteMyCronyPage(roomId: String) {
    AppearanceStatusBars(isLight = true)

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        CupidAppBar(title = stringResource(id = R.string.cpd邀请密友), actions = {
            Box(modifier = Modifier.size(44.dp))
        })

        val viewModel = viewModel<InviteMyCronyViewModel> {
            InviteMyCronyViewModel(sUser.id)
        }

        CupidStateListView(viewModel = viewModel) { user, _, coroutineScope, loadingState ->
            UserItem(user = user, nameColor = MaterialTheme.colorScheme.onSurface, onAvatarClicked = {
                AppLinkManager.controller?.navigateToProfile(it.id)
            }) {
                AppButton(
                    text = stringResource(id = if (user.hasInvited) R.string.cpd_has_invited else R.string.cpd_invite),
                    onClick = {
                        coroutineScope.launch {
                            loadingState.value = true
                            viewModel.shareAudioRoom(roomId, user)
                            loadingState.value = false
                        }
                    },
                    border = BorderStroke(1.dp, color = if (user.hasInvited) Color(0xFFE5E6EB) else CpdColors.FFFF5E8B),
                    background = Color.White,
                    textStyle = TextStyle(color = if (user.hasInvited) Color(0xFFE5E6EB) else CpdColors.FFFF5E8B, fontSize = 14.sp),
                    enabled = !user.hasInvited,
                    contentPadding = PaddingValues(horizontal = 12.dp),
                    modifier = Modifier
                        .height(32.dp)
                        .widthIn(88.dp)
                )
            }
        }
    }
}

class InviteMyCronyViewModel(private val userId: String) : StateViewModel<Int, UserInfo>() {

    private val relationshipApi = createApi(RelationshipApi::class.java)

    private val shareApi by lazy {
        createApi(ShareApi::class.java)
    }

    init {
        refresh()
    }

    override suspend fun loadPageData(id: Int?): Result<PageResult<Int, UserInfo>> {
        return withContext(Dispatchers.Default) {
            runApiCatching {
                relationshipApi.getMyRelationshipLabelList(userId, id ?: 0)
            }.mapCatching {
                it["relatives"]?.jsonArray?.let { array ->
                    array.map { element ->
                        val json = element.jsonObject
                        json.getValue("id").jsonPrimitive.int to sAppJson.decodeFromJsonElement<UserInfo>(json.getValue("relative_info"))
                    }
                }
            }.map {
                if (it.isNullOrEmpty()) {
                    PageResult.NoNext(emptyList())
                } else {
                    PageResult.HasNext(it.map { it.second }, it.last().first)
                }
            }
        }
    }

    fun shareAudioRoom(room_id: String, userInfo: UserInfo) {
        viewModelScope.launch {
            runApiCatching {
                shareApi.shareAudioRoom(buildMap {
                    put("share_type", "2")
                    put("room_id", room_id)
                    put("target_user_id", userInfo.id)
                })
            }.onSuccess {
                updateItem({ it.id == userInfo.id }) {
                    it.copy(hasInvited = true)
                }
            }.toastError()
        }
    }

}