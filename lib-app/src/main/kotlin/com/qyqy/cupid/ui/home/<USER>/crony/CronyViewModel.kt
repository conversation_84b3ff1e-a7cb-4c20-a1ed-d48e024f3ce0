package com.qyqy.cupid.ui.home.mine.crony

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.Color
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.cupid.ui.home.mine.CpCrony
import com.qyqy.cupid.ui.home.mine.CronyTable
import com.qyqy.cupid.ui.home.mine.toSimpleCronyLabel
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.app
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.ifEmpty
import com.qyqy.ucoo.compose.map
import com.qyqy.ucoo.compose.orDefault
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.bean.CpZone
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.sUserFlow
import com.qyqy.ucoo.user.RelationshipRepository
import com.qyqy.ucoo.user.UserManager
import com.qyqy.ucoo.utils.toast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement

class CronyViewModel constructor(val userId: String) : ViewModel() {

    private val userFlow: StateFlow<User> = if (userId == sUser.id) {
        sUserFlow
    } else {
        UserManager.getUserFlowById(userId).stateIn(
            viewModelScope,
            SharingStarted.WhileSubscribed(5000),
            UserManager.getMemoryUserCacheById(userId) ?: AppUser(userId)
        )
    }

    private val relationshipRepository: RelationshipRepository = RelationshipRepository()

    var cpZone by mutableStateOf<CpZone?>(null)
        private set

    val cpCronyFlow = userFlow.map(viewModelScope) {
        (it as AppUser).toCpCrony().also { cpCrony ->
            if (it.isSelf && cpCrony is CpCrony.None) {
                cpZone = null
            }
        }
    }

    val cpCrony get() = cpCronyFlow.value

    private val cronyTableState = mutableStateOf(CronyTable())

    val cronyTable: CronyTable?
        get() = cronyTableState.value.takeIf {
            it.isVisible
        }

    fun fetchCronyTable() {
        viewModelScope.launch {
            val ret = relationshipRepository.getRelationshipSettings(userId) ?: return@launch
            val configs = ret.first
            val cronyCount = configs[0]
            val seatsTotalCount = configs[1]

            val emptyCount = seatsTotalCount - cronyCount
            val isVisible = sUser.id == userId || cronyCount > 0

            cronyTableState.update {
                copy(
                    isVisible = isVisible,
                    emptyCount = emptyCount,
                    totalCount = seatsTotalCount,
                    maxCount = configs[2],
                    pricePerLabel = configs[3],
                    ruleLink = ret.second,
                )
            }

            if (!isVisible) {
                return@launch
            }

            launch {
                val tagList = relationshipRepository.getAllLabelList(1)
                if (tagList != null) {
                    cronyTableState.update {
                        copy(labelTagList = tagList)
                    }
                }
            }

            if (cronyCount != 0) { // 有设置过密友，则分页拉取
                val labelList = relationshipRepository.getMyRelationshipLabelList(userId, 0)
                if (!labelList.isNullOrEmpty()) {
                    cronyTableState.update {
                        copy(labelList = labelList.map { it.toSimpleCronyLabel() })
                    }

                    var relativeId: Int = labelList.last().label.relativeId
                    val moreList = buildList {
                        while (currentCoroutineContext().isActive) {
                            val list = relationshipRepository.getMyRelationshipLabelList(userId, relativeId)
                            if (list.isNullOrEmpty()) {
                                break
                            }
                            relativeId = list.last().label.relativeId
                            addAll(list)
                        }
                    }
                    if (moreList.isNotEmpty()) {
                        cronyTableState.update {
                            copy(labelList = buildList {
                                addAll(<EMAIL>)
                                moreList.forEach {
                                    add(it.toSimpleCronyLabel())
                                }
                            })
                        }
                    }
                }
            } else {
                cronyTableState.update {
                    copy(labelList = emptyList())
                }
            }
        }
    }

    fun fetchCpZone() {
        viewModelScope.launch(Dispatchers.IO) {
            UserManager.userRepository.getUserCpZone(userId).mapCatching { json ->
                json["cp_zone"]?.let {
                    sAppJson.decodeFromJsonElement<CpZone?>(it)
                }
            }.onSuccess {
                cpZone = it
            }
        }
    }

    fun doCpTask(taskId: String) {
        viewModelScope.launch {
            UserManager.userRepository.routerLinkByTaskId(taskId).onSuccess {
                if (it.action == 0) {
                    toast(it.toast)
                    fetchCpZone()
                } else {
                    if (it.taskIsFinished) {
                        fetchCpZone()
                    }
                    AppLinkManager.controller?.navigateByLink(it.clientJumpLink)
                }
            }
        }
    }

    fun dissolveCp() {
        viewModelScope.launch {
            UserManager.userRepository.dissolveCp().toastError()
        }
    }
}

private fun <T> MutableState<T>.update(function: T.() -> T) {
    value = function(value)
}

private fun AppUser.toCpCrony(): CpCrony {
    val user = this
    val cpExtraInfo = user.cpExtraInfo
    val cp = user.publicCP ?: user.cp
    return if (cp != null) {
        CpCrony.Simple(
            user,
            cp,
            cpExtraInfo?.cpValue.orDefault(0).toString(),
            cpExtraInfo?.levelInfo?.level.orDefault(1),
            cpExtraInfo?.togatherDays.ifEmpty(app.getString(R.string.在一起N天, 1)),
            cpExtraInfo?.levelInfo?.headerImgUrl.orEmpty(),
            Color(0xFFF6E7EA),
            Color(0xFFFFCCD6),
            user.cpCardBackground
        )
    } else {
        CpCrony.None(user, user.cpCardBackground)
    }
}