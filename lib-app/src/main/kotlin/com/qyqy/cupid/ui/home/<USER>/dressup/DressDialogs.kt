package com.qyqy.cupid.ui.home.mine.dressup

import androidx.compose.runtime.Composable
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.dialog.NormalDialog
import com.qyqy.cupid.ui.home.mine.dressup.mall.BroughtSuccessContent
import com.qyqy.ucoo.utils.OnClick

class BroughtSuccessDialog(val name: String, val icon: String, private val onCheck: OnClick) : NormalDialog<IDialogAction>() {
    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        BroughtSuccessContent(icon = icon, name = name, onEnsure = { dialog.dismiss() }) {
            dialog.dismiss()
            onCheck.invoke()
        }
    }
}