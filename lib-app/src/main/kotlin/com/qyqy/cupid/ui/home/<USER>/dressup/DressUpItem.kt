

package com.qyqy.cupid.ui.home.mine.dressup

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.clickWithShape
import com.qyqy.ucoo.compose.ui.AnimatedComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.utils.OnClick

@Composable
fun DressUpItem(icon: String, name: String, expireText: String, active: Boolean, onClick: OnClick) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(min = 170.dp)
            .background(color = Color(0xFFFFFFFF), shape = RoundedCornerShape(8.dp))
            .padding(10.dp, 8.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        AnimatedComposeImage(
            model = icon,
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(1f),
            contentScale = ContentScale.Inside
        )

        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = name,
            color = Color(0xFF1D2129),
            fontSize = 13.sp,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth(),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(text = expireText, color = Color(0xFF86909C), fontSize = 11.sp, textAlign = TextAlign.Center)
        Spacer(modifier = Modifier.height(8.dp))
        Box(
            modifier = Modifier
                .clickWithShape(onClick = onClick)
                .heightIn(min = 26.dp)
                .widthIn(min = 80.dp)
                .background(if (!active) MaterialTheme.colorScheme.primary else Color(0xFFF1F2F3)),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = if (active) stringResource(id = R.string.cpd_取消佩戴) else stringResource(id = R.string.cpd_佩戴),
                color = if (!active) Color.White else Color(0xFF86909C),
                fontSize = 12.sp,
            )
        }
    }
}

@Preview
@Composable
private fun DressUpItemPreview() {
    PreviewCupidTheme {
        Column(modifier = Modifier.width(120.dp)) {
            DressUpItem(icon = "", name = "头像", expireText = "2024-8-9过期", active = false) {

            }
            DressUpItem(icon = "", name = "头像", expireText = "2024-8-9过期", active = true) {

            }
        }
    }
}