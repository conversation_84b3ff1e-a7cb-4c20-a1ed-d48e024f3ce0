package com.qyqy.cupid.ui.home.mine.dressup

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.dialog.AnimatedDialog
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.dialog.NormalDialog
import com.qyqy.cupid.ui.dialog.rememberDialogQueue
import com.qyqy.cupid.ui.home.mine.dressup.mall.BroughtSuccessContent
import com.qyqy.cupid.ui.home.mine.dressup.mall.DUItem
import com.qyqy.cupid.ui.home.mine.dressup.mall.DressUpContent
import com.qyqy.cupid.ui.home.mine.dressup.mall.DressUpGoods
import com.qyqy.cupid.ui.home.mine.dressup.mall.DressUpGoodsBuyContent
import com.qyqy.cupid.ui.home.mine.dressup.mall.DressUpGoodsListResult
import com.qyqy.cupid.ui.home.mine.dressup.mall.DressUpMallItemList
import com.qyqy.cupid.ui.home.mine.dressup.mall.cost
import com.qyqy.cupid.utils.launchSingTask
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.state.CupidPullRefreshBox
import com.qyqy.ucoo.accountManager
import com.qyqy.ucoo.compose.state.LiStateViewModel
import com.qyqy.ucoo.compose.state.itemLoadMore
import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.parseValue
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.sUserFlow
import com.qyqy.ucoo.utils.GiftEffectHelper
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query
import kotlin.time.Duration.Companion.seconds

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DressUpMallScreen(vm: DressUpMallViewModel = viewModel()) {
    val nav = LocalAppNavController.current
    val dq = rememberDialogQueue<IDialogAction>()
    dq.DialogContent()
    val data by vm.data
    DressUpContent(tabList = data.tabs, onClickItem = { title, goods ->
        dq.push(GoodsDetailDialog(title, goods, onCheckMyGold = {
            nav.navigate(CupidRouters.MyCoin)
        }) {
            dq.push(BuySuccessDialog(goods), true)
        })
    }, onClickMore = { title, propType ->
        nav.launchSingTask(CupidRouters.DRESS_UP_LIST, mapOf("title" to title, "propType" to propType))
    }) {
        nav.launchSingTask(CupidRouters.DRESS_UP)
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DressUpListScreen(
    title: String, propType: Int, viewModel: PropsListViewModel = viewModel {
        PropsListViewModel(propType)
    }
) {
    val dataList by viewModel.dataState
    val nav = LocalAppNavController.current
    val dq = rememberDialogQueue<IDialogAction>()
    dq.DialogContent()
    Scaffold(modifier = Modifier.fillMaxSize(), topBar = { CupidAppBar(title = title) }) { pv ->
        CupidPullRefreshBox(
            modifier = Modifier.padding(pv),
            isRefreshing = viewModel.isRefreshing,
            onRefresh = { viewModel.refresh() }) {
            LazyVerticalGrid(
                columns = GridCells.Fixed(3),
                modifier = Modifier.padding(16.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(dataList) { goods ->
                    DUItem(icon = goods.propInfo.icon, name = goods.propInfo.name, cost = goods.cost) {
                        dq.push(GoodsDetailDialog(title, goods, onCheckMyGold = {
                            nav.navigate(CupidRouters.MyCoin)
                        }) {
                            dq.push(BuySuccessDialog(goods), true)
                        })
                    }
                }
                itemLoadMore(viewModel.allowLoad, viewModel.isLoadingNow, viewModel.hasMore, span = {
                    GridItemSpan(3)
                }) {
                    viewModel.loadMore()
                }
            }
        }
    }
}

interface DressUpMallApi {
    @GET("api/dressup/v1/prop/japan/store/list")
    suspend fun getStoreList(): ApiResponse<DressUpGoodsListResult>

    @POST("api/dressup/v1/prop/japan/store/buy")
    suspend fun buy(@Body map: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>

    @GET("api/dressup/v1/prop/japan/store/props")
    suspend fun getPropList(
        @Query("prop_type") propType: Int,
        @Query("page") page: Int,
        @Query("page_size") pageSize: Int
    ): ApiResponse<DressUpMallItemList>
}

private val api = createApi<DressUpMallApi>()


class PropsListViewModel(private val propType: Int) : LiStateViewModel<DressUpGoods>() {
    private var page = 0
    private val pageSize = 30

    override suspend fun fetch(): List<DressUpGoods> {
        return runApiCatching { api.getPropList(propType, page, pageSize) }
            .getOrNull()?.list.orEmpty()
    }

    override fun onStartRequest(isRefresh: Boolean) {
        super.onStartRequest(isRefresh)
        if (isRefresh) {
            page = 1
        } else {
            page += 1
        }
    }

}

class DressUpMallViewModel : ViewModel() {
    private val _refreshing: MutableState<Boolean> = mutableStateOf(false)
    val refreshing: State<Boolean> = _refreshing

    private val _data: MutableState<DressUpGoodsListResult> = mutableStateOf(DressUpGoodsListResult())
    val data: State<DressUpGoodsListResult> = _data


    init {
        refresh()
    }

    fun refresh() {
        viewModelScope.launch {
            _refreshing.value = true
            runApiCatching { api.getStoreList() }
                .onSuccess {
                    _data.value = it
                }
            _refreshing.value = false
        }
    }
}

/**
 * 购买成功弹窗
 */
private class BuySuccessDialog(val goods: DressUpGoods) : NormalDialog<IDialogAction>() {
    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        val nav = LocalAppNavController.current
        BroughtSuccessContent(icon = goods.propInfo.icon, name = goods.propInfo.name, onEnsure = {
            dialog.dismiss()
        }, onLook = {
            dialog.dismiss()
            nav.launchSingTask(
                CupidRouters.DRESS_UP,
                mapOf("propType" to goods.propInfo.propType, "propId" to goods.propInfo.id, "time" to System.currentTimeMillis())
            )
        })
    }
}

/**
 * 购买详情弹窗
 */
private class GoodsDetailDialog(
    val title: String,
    val data: DressUpGoods,
    private val onCheckMyGold: () -> Unit,
    private val onSuccess: () -> Unit,
) :
    AnimatedDialog<IDialogAction>() {
    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        val u by sUserFlow.collectAsStateWithLifecycle()
        var progressing by remember {
            mutableStateOf(false)
        }
        val scope = rememberCoroutineScope()
        DressUpGoodsBuyContent(
            title = title,
            goods = data,
            balance = u.balance,
            buttonEnable = !progressing,
            selfAvatar = if (data.propInfo.propType == 1) u.avatarUrl else "",
            effectFileLoader = {
                GiftEffectHelper.getGiftEffectFile(it, timeout = 15.seconds)
            },
            onCheckMyGold = onCheckMyGold
        ) { days ->
            scope.launch {
                progressing = true
                runApiCatching {
                    api.buy(mapOf("prop_type" to data.propInfo.propType, "prop_id" to data.propInfo.id, "days" to days))
                }.onSuccess {
                    val b = it.parseValue<Int>("my_balance", u.balance)
                    accountManager.updateSelfUser {
                        this.balance = b
                    }
                    onSuccess.invoke()
                    dialog.dismiss()
                }
                progressing = false
            }
        }
    }
}