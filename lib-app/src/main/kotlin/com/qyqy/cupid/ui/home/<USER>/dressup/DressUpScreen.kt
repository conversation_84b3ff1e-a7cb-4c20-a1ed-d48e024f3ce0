@file:OptIn(ExperimentalMaterial3Api::class)

package com.qyqy.cupid.ui.home.mine.dressup

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyGridItemScope
import androidx.compose.foundation.lazy.grid.LazyGridState
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.theme.CupidTheme
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.home.mine.dressup.mall.DressUpMallButton
import com.qyqy.cupid.ui.relations.TabLayout
import com.qyqy.cupid.utils.launchSingTask
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.click
import com.qyqy.cupid.widgets.state.CupidPullRefreshBox
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.state.LoadMoreView
import com.qyqy.ucoo.compose.state.OnLoadMore
import com.qyqy.ucoo.compose.ui.AppScrollableTabRow
import com.qyqy.ucoo.compose.ui.AppTab
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.fixCurrentPage
import com.qyqy.ucoo.mine.dressup.DressUpEntity
import com.qyqy.ucoo.mine.dressup.DressUpProp
import com.qyqy.ucoo.mine.dressup.Prop
import com.qyqy.ucoo.utils.EntityCallback
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@Composable
fun DressUpScreen(propType: Int = -1, propId: Int = -1, serial: Long) {
    val nav = LocalAppNavController.current
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        containerColor = Color(0xFFF5F7F9),
        topBar = {
            CupidAppBar(
                title = stringResource(R.string.cpd_mine_suit),
                modifier = Modifier.background(MaterialTheme.colorScheme.surface),
                actions = {
                    DressUpMallButton(modifier = Modifier.click {
                        nav.launchSingTask(CupidRouters.DRESS_UP_MALL)
                    })
                }
            )
        },
    ) {
        val tabList by produceDressUpTabList(serial)
        val tabs = remember(tabList) {
            tabList.map { AppTab(it.second) }
        }

        key(tabList, propType, propId, serial) {
            val pagerState =
                rememberPagerState(initialPage = tabList.indexOfFirst { it.first == propType }.takeIf { it != -1 } ?: 0) {
                    tabList.size
                }
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(it)
            ) {
                if (tabList.size < 5) {
                    TabLayout(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(MaterialTheme.colorScheme.surface)
                            .padding(horizontal = 32.dp),
                        indicatorHeight = 4.dp,
                        pagerState = pagerState,
                        titles = tabList.map { it.second },
                        itemContent = { title, selected ->
                            Text(
                                text = title,
                                color = Color(if (selected) 0xFF1D2129 else 0xFF86909C),
                                fontSize = 14.sp,
                                fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis,
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                        })
                } else {
                    AppScrollableTabRow(
                        tabs = tabs,
                        modifier = Modifier.padding(8.dp),
                        pagerState = pagerState,
                        indicatorColor = MaterialTheme.colorScheme.primary
                    ) { index, tab ->
                        val selected = index == pagerState.fixCurrentPage
                        Text(
                            text = tab.name,
                            color = Color(if (selected) 0xFF1D2129 else 0xFF86909C),
                            fontSize = 14.sp,
                            fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
                            maxLines = 1,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    }
                }

                HorizontalPager(
                    modifier = Modifier.fillMaxSize(),
                    state = pagerState,
                ) {
                    val item = tabList[it]
                    DressUpPage(t = item.first, previewPropId = if (propType == item.first) propId else -1, serial = serial)
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DressUpPage(
    t: Int,
    vm: DressUpViewModel = viewModel(key = "dress-up-$t") {
        DressUpViewModel(propType = t)
    },
    previewPropId: Int = -1,
    serial: Long = 0L,
) {
    val list by vm.dataList
    val isPosting by vm.isPosting
    LocalContentLoading.current.value = isPosting
    val scope = rememberCoroutineScope()
    var scrolled by rememberSaveable {
        mutableStateOf(false)
    }
    LaunchedEffect(key1 = serial) {
        vm.refresh()
    }
    val state = rememberLazyGridState()
    if (previewPropId != -1 && list.isNotEmpty() && !scrolled) {
        LaunchedEffect(key1 = previewPropId) {
            scope.launch {
                val index = list.indexOfFirst { it.propId == previewPropId }
                if (index > -1) {
                    delay(700)
                    state.animateScrollToItem(index)
                    scrolled = true
                }
            }
        }
    }
    val nav = LocalAppNavController.current
    CupidPullRefreshBox(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp, 20.dp),
        isRefreshing = vm.isRefreshing,
        onRefresh = { vm.refresh() }) {
        DressUpGrid(items = list, state = state, loadMore = vm.allowLoad, entityCallback = {
            vm.toggle(it)
        }, onLoadMore = { vm.loadMore() }, onEmpty = {
            if (vm.isLoaded) {
                EmptyDressUp(text = stringResource(id = R.string.cupid_no_data)) {
                    nav.launchSingTask(CupidRouters.DRESS_UP_MALL)
                }
            }
        })
    }
}

@Composable
fun DressUpGrid(
    items: List<DressUpEntity>,
    loadMore: Boolean,
    entityCallback: EntityCallback<DressUpEntity>,
    state: LazyGridState = rememberLazyGridState(),
    onEmpty: @Composable LazyGridItemScope.() -> Unit = {},
    onLoadMore: OnLoadMore = {}
) {
    val forever = stringResource(R.string.cpd_forever)
    LazyVerticalGrid(
        columns = GridCells.Fixed(3),
        state = state,
        modifier = Modifier.fillMaxSize(),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        if (items.isEmpty()) {
            item(span = { GridItemSpan(3) }) {
                onEmpty()
            }
        }
        items(items) {
            val expireTime = remember(it.expireStampV2) {
                it.expireStampV2?.let { t ->
                    SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date(t * 1000))
                } ?: forever
            }
            DressUpItem(icon = it.url, name = it.title, expireText = expireTime, active = it.isUsing) {
                entityCallback(it)
            }
        }
        if (loadMore) {
            item(span = { GridItemSpan(3) }) {
                LoadMoreView(onLoadMore = onLoadMore)
            }
        }
    }
}

@Preview
@Composable
private fun DressGridPreview() {
    CupidTheme {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFFF5F7F9))
                .padding(16.dp, 20.dp)
        ) {
            DressUpGrid(items = buildList {
                repeat(10) {
                    add(DressUpProp(1, Prop(1, "Prop:$it", "", 1), false, System.currentTimeMillis()))
                }
            }, loadMore = false, entityCallback = {})
        }
    }
}
