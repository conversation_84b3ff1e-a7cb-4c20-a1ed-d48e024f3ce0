package com.qyqy.cupid.ui.home.mine.dressup

import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.produceState
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.accountManager
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.state.LiState
import com.qyqy.ucoo.compose.state.LiStateInfo
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getOrNull
import com.qyqy.ucoo.http.parseValue
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.mine.dressup.DressUpApi
import com.qyqy.ucoo.mine.dressup.DressUpEntity
import com.qyqy.ucoo.mine.dressup.DressUpProp
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.utils.ComposeState
import kotlinx.coroutines.launch
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonObject

private val api = createApi<DressUpApi>()

@Composable
fun produceDressUpTabList(serial: Long) = run {
    produceState(initialValue = emptyList(), serial) {
        value = runApiCatching { api.getDressUpTabList() }
            .map { obj ->
                obj.getOrNull("tab_list")?.let { array ->
                    try {
                        array.jsonArray.map {
                            it.jsonObject.parseValue<Int>("t", 1) to
                                    it.jsonObject.parseValue<String>("name", "")
                        }
                    } catch (e: Exception) {
                        emptyList()
                    }
                }
            }.getOrNull().orEmpty()
    }
}

class DressUpViewModel(private val propType: Int, val stateableInfo: LiStateInfo = LiStateInfo()) : ViewModel(),
    LiState by stateableInfo {

    private val _dataList: MutableState<List<DressUpEntity>> = mutableStateOf(emptyList())
    val dataList: ComposeState<List<DressUpEntity>> = _dataList

    fun refresh() {
        getData(0)
    }


    fun loadMore() {
        getData(_dataList.value.lastOrNull()?.id ?: 0)
    }

    private fun getData(lastId: Int) {
        viewModelScope.launch {
            if (stateableInfo.isProgressing) {
                return@launch
            }
            if (lastId == 0) {
                stateableInfo.onStartRefresh()
            } else {
                stateableInfo.onStartLoading()
            }
            runApiCatching { api.getPropList(propType, lastId) }
                .onSuccess {
                    if (lastId == 0) {
                        _dataList.value = it.props
                    } else {
                        _dataList.value += it.props
                    }
                    stateableInfo.onLoadComplete(it.props.isNotEmpty())
                }.onFailure {
                    stateableInfo.onLoadError()
                    it.toast()
                }
        }
    }

    private val _isPosting: MutableState<Boolean> = mutableStateOf(false)
    val isPosting: ComposeState<Boolean> = _isPosting

    fun toggle(props: DressUpEntity) {
        viewModelScope.launch {
            _isPosting.value = true
            runApiCatching {
                api.setPropUseState(props.propType, props.propId, props.isUsing.not())
            }
                .onSuccess {
                    val list = _dataList.value
                    val index = list.indexOfFirst { it.propId == props.propId }
                    if (index > -1) {
                        val entity = list[index] as DressUpProp
                        _dataList.value = list.toMutableList().also { it[index] = entity.copy(isUsing = props.isUsing.not()) }
                    }
                    accountManager.refreshSelfUserByRemote()
                }
                .toastError()
            _isPosting.value = false
        }
    }

}