package com.qyqy.cupid.ui.home.mine.dressup

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.widgets.CpdButton
import com.qyqy.ucoo.R

@Composable
fun EmptyDressUp(text: String, onGoto: () -> Unit = {}) {
    Column(modifier = Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
        Spacer(modifier = Modifier.height(170.dp))
        Image(
            painter = painterResource(id = R.drawable.cpd_ic_empty_dress),
            contentDescription = "",
            modifier = Modifier.size(120.dp)
        )
        Box(modifier = Modifier.height(56.dp), contentAlignment = Alignment.Center) {
            Text(text = text, color = Color(0xFF86909C), fontSize = 12.sp)
        }
        CpdButton(
            text = stringResource(R.string.cpd_to_dress_mall),
            onClick = onGoto,
            modifier = Modifier.padding(horizontal = 24.dp)
        )
    }
}