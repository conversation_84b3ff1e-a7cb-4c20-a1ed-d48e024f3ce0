package com.qyqy.cupid.ui.home.mine.dressup.mall

import androidx.compose.ui.unit.Dp

sealed class DressUpData(val spanCount: Int, open val index: Int) {
    data class Title(val propType: Int, val text: String, override val index: Int) : DressUpData(3, index)
    data class Item(
        val id: Int,
        val name: String,
        val icon: String,
        val cost: Int,
        val data: DressUpGoods,
        override val index: Int
    ) : DressUpData(1, index)
    data class SpaceItem(val space:Dp):DressUpData(3,0)
}