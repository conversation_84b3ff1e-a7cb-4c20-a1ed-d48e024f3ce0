package com.qyqy.cupid.ui.home.mine.dressup.mall

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.MultiTabs
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AnimatedComposeImage
import kotlinx.coroutines.launch


@Composable
fun DressUpContent(
    tabList: List<DressUpCategory> = emptyList(),
    onClickMore: (String, Int) -> Unit = { _, _ -> },
    onClickItem: (String, DressUpGoods) -> Unit = { _, _ -> },
    toDressUpPage: () -> Unit = {},
) {
    var lastHeight by remember {
        mutableFloatStateOf(0f)
    }
    var spaceHeight by remember {
        mutableStateOf(1.dp)
    }
    val d = LocalDensity.current
    val items = remember(tabList) {
        buildList {
            tabList.forEachIndexed { index, c ->
                add(DressUpData.Title(c.propType, c.tabName, index))
                addAll(c.list.map {
                    DressUpData.Item(
                        it.id,
                        it.propInfo.name,
                        it.propInfo.icon,
                        if (it.propInfo.gainType == 1) {
                            -1
                        } else {
                            it.priceConf.firstOrNull()?.coin ?: 0
                        },
                        it.copy(typeName = c.tabName),
                        index
                    )
                })
                if (index == tabList.lastIndex) {
                    add(DressUpData.SpaceItem(1.dp))
                    val line = (c.list.size / 3) + (if (c.list.size % 3 == 0) 0 else 1)
                    lastHeight = with(d) {
                        (line * 140 + (line - 1).coerceAtLeast(0) * 8).dp.toPx()
                    }
                }
            }
        }
    }
    val state = rememberLazyGridState()

    val selectedIndex by remember(items) {
        derivedStateOf {
            val index = state.firstVisibleItemIndex
            items.getOrNull(index)?.index ?: 0
        }
    }
    val scope = rememberCoroutineScope()
    Scaffold(modifier = Modifier.fillMaxSize(), topBar = {
        CupidAppBar(title = stringResource(id = R.string.cpd_装扮商城), actions = {
            Text(
                text = stringResource(id = R.string.cpd_mine_dress),
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier
                    .clickable(onClick = toDressUpPage)
                    .padding(16.dp, 4.dp)
            )
        })
    }) { pd ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(pd)
        ) {

            MultiTabs(
                tabs = tabList,
                selectedIndex = selectedIndex,
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
                    .height(32.dp)
            ) { item, index, isSelected ->
                Box(
                    modifier = Modifier
                        .height(28.dp)
                        .widthIn(min = 72.dp, max = 88.dp)
                        .clickable {
                            val idx = items.indexOfFirst { it is DressUpData.Title && it.text == item.tabName }
                            if (idx != -1) {
                                scope.launch {
                                    state.animateScrollToItem(idx)
                                }
                            }
                        }
                ) {
                    val sel = index == selectedIndex
                    Text(
                        text = item.tabName,
                        color = Color(if (sel) 0xFF1D2129 else 0xFF86909C),
                        fontSize = 14.sp,
                        fontWeight = if (sel) FontWeight.Medium else FontWeight.Normal,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.align(Alignment.Center)
                    )
                    if (sel) {
                        Spacer(
                            modifier = Modifier
                                .size(10.dp, 4.dp)
                                .align(Alignment.BottomCenter)
                                .background(
                                    MaterialTheme.colorScheme.primary,
                                    RoundedCornerShape(2.dp)
                                )
                        )
                    }
                }
            }
            LazyVerticalGrid(
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .onGloballyPositioned {
                        with(d) {
                            spaceHeight = ((it.size.height - lastHeight) / d.density).dp
                        }
                    },
                state = state,
                columns = GridCells.Fixed(3),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(items, span = {
                    GridItemSpan(it.spanCount)
                }) { item: DressUpData ->
                    when (item) {
                        is DressUpData.Title -> DUTitle(title = item.text) {
                            onClickMore(item.text, item.propType)
                        }

                        is DressUpData.Item -> DUItem(icon = item.icon, name = item.name, cost = item.cost) {
                            onClickItem.invoke(item.data.typeName, item.data)
                        }

                        is DressUpData.SpaceItem -> Spacer(modifier = Modifier.height(spaceHeight))
                        else -> {}
                    }
                }
            }
        }
    }
}

@Composable
fun DUItem(icon: String, name: String, cost: Int, onClick: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(min = 140.dp)
            .clip(Shapes.small)
            .clickable(onClick = onClick)
            .background(color = Color(0xFFFFFFFF), shape = Shapes.small)
            .padding(horizontal = 10.dp)
            .padding(top = 8.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        AnimatedComposeImage(
            model = icon,
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(1f),
            contentScale = ContentScale.Inside
        )

        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = name,
            color = Color(0xFF1D2129),
            fontSize = 13.sp,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth(),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(min = 30.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            if (cost > 0) {
                Image(
                    painter = painterResource(id = R.drawable.cupid_gold_icon),
                    contentDescription = "gold",
                    modifier = Modifier.size(14.dp)
                )
                Spacer(modifier = Modifier.width(2.dp))
                Text(text = cost.toString(), color = Color(0xFFFFB71A), fontSize = 12.sp, fontWeight = FontWeight.Medium)
            } else {
                Text(
                    text = stringResource(R.string.cpd_活动获得),
                    fontSize = 10.sp,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier
                        .border(1.dp, MaterialTheme.colorScheme.primary, RoundedCornerShape(4.dp))
                        .padding(6.dp, 1.dp)
                )
            }
        }
        Spacer(modifier = Modifier.height(5.dp))
    }
}

@Composable
fun DUTitle(title: String, onClickMore: () -> Unit) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .heightIn(min = 30.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(text = title, color = Color(0xFF1D2129), fontSize = 14.sp, fontWeight = FontWeight.Medium)
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .clip(Shapes.chip)
                .clickable(onClick = onClickMore)
                .padding(vertical = 4.dp, horizontal = 2.dp)
        ) {
            Text(text = stringResource(R.string.cpd_查看更多), color = Color(0xFF86909C), fontSize = 12.sp)
            Spacer(modifier = Modifier.width(2.dp))
            Image(
                painter = painterResource(id = R.drawable.ic_cpd_arrow_right),
                contentDescription = "arrow",
                modifier = Modifier.size(10.dp)
            )
        }
    }
}

@Preview
@Composable
private fun Preview() {
    PreviewCupidTheme {
        Column(
            Modifier
                .fillMaxWidth()
                .background(Color(0xFFEEEEEE))
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Column(modifier = Modifier.width(110.dp)) {
                DUItem(icon = "", name = "头像框", cost = 200) {

                }
                Spacer(modifier = Modifier.height(4.dp))
                DUItem(icon = "", name = "头像框", cost = -1) {

                }
            }
            DUTitle(title = "头像框") {

            }
        }
    }
}

@Composable
fun DressUpMallButton(modifier: Modifier = Modifier) {
    Text(
        text = stringResource(id = R.string.cpd_装扮商城),
        color = Color(0xFFFF5E8B),
        fontSize = 14.sp,
        modifier = modifier.padding(16.dp, 10.dp)
    )
}

@Composable
fun DressUpCategoryContent(title: String) {
//    Scaffold(modifier = Modifier.fillMaxSize()) { pv ->
//        CupidAppBar(title = )
//    }
}