package com.qyqy.cupid.ui.home.mine.dressup.mall


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class DressUpGoodsListResult(
    val tabs: List<DressUpCategory> = emptyList(),
    @SerialName("my_balance")
    val myBalance: Int = 0
)

@Serializable
data class DressUpMallItemList(
    val list: List<DressUpGoods>,
    @SerialName("my_balance")
    val myBalance: Int = 0
)

@Serializable
data class DressUpCategory(
    @SerialName("tab_name")
    val tabName: String,
    val list: List<DressUpGoods> = emptyList(),
    @SerialName("prop_type")
    val propType: Int = 1,// 装扮类型，1头像框；2勋章；3进场特效；5聊天气泡
)

@Serializable
data class DressUpGoods(
    @SerialName("id")
    val id: Int = 0,
    @SerialName("native_region")
    val nativeRegion: Int = 0,
    @SerialName("preview_url")
    val previewUrl: String = "",//// 预览url，进场特效时如果是拍好的视频可能用到
    @SerialName("price_conf")
    val priceConf: List<PriceConf> = listOf(),
    @SerialName("prop_info")
    val propInfo: PropInfo = PropInfo(),

    val typeName: String = "",
)

val DressUpGoods.cost: Int
    get() = if (propInfo.gainType == 1) {
        -1
    } else {
        priceConf.firstOrNull()?.coin ?: 0
    }

@Serializable
data class PriceConf(
    @SerialName("coin")
    val coin: Int = 0,
    @SerialName("day")
    val day: Int = 0
)

@Serializable
data class PropInfo(
    @SerialName("gain_type")
    val gainType: Int = 0,//// 获取途径，1活动获取；2商城购买
    @SerialName("icon")
    val icon: String = "",
    @SerialName("id")
    val id: Int = 0,
    @SerialName("name")
    val name: String = "",
    @SerialName("prop_desc")
    val propDesc: String = "",
    @SerialName("prop_type")
    val propType: Int = 0,/// 装扮类型，1头像框；2勋章；3进场特效；5聊天气泡
    @SerialName("effect_file")
    val effectFile: String = "",
)

