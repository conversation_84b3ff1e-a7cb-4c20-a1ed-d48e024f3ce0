package com.qyqy.cupid.ui.home.mine.dressup.mall

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.components.EffectView
import com.qyqy.cupid.widgets.CpdButton
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AnimatedComposeImage
import java.io.File

@Composable
fun BroughtSuccessContent(icon: String, name: String, onEnsure: () -> Unit, onLook: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White, Shapes.small)
            .padding(16.dp, 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(R.string.cpd_购买成功),
            color = Color(0xFF1D2129),
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.height(24.dp))
        Box(
            modifier = Modifier
                .size(80.dp)
                .background(Color(0xFFF1F2F3), RoundedCornerShape(4.dp))
                .padding(6.dp)
        ) {
            AnimatedComposeImage(
                model = icon,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Inside
            )
        }
        Spacer(modifier = Modifier.height(8.dp))
        Text(text = name, color = Color(0xFF1D2129), fontSize = 14.sp, fontWeight = FontWeight.Medium)
        Spacer(modifier = Modifier.height(16.dp))
        Text(text = stringResource(R.string.cpd_put_success), color = Color(0xFF86909C), fontSize = 12.sp)
        Spacer(modifier = Modifier.height(24.dp))
        Row(modifier = Modifier.fillMaxWidth()) {
            Box(
                modifier = Modifier
                    .weight(1f)
                    .height(32.dp)
                    .clickable(onClick = onEnsure)
                    .background(Color(0xFFE5E6EB), Shapes.chip),
                contentAlignment = Alignment.Center
            ) {
                Text(text = stringResource(id = R.string.cpd_btn_known), color = Color(0xFF1D2129), fontSize = 14.sp)
            }
            Spacer(modifier = Modifier.width(12.dp))
            Box(
                modifier = Modifier
                    .weight(1f)
                    .height(32.dp)
                    .clickable(onClick = onLook)
                    .background(MaterialTheme.colorScheme.primary, Shapes.chip),
                contentAlignment = Alignment.Center
            ) {
                Text(text = stringResource(R.string.cpd_goto_look), color = Color.White, fontSize = 14.sp)
            }
        }
    }
}


@Preview
@Composable
private fun Preview() {
    PreviewCupidTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.Gray)
                .padding(50.dp)
        ) {
            BroughtSuccessContent(icon = "", name = "头像框名称", onEnsure = { }) {

            }
        }
    }
}

@Composable
private fun Header(
    textCategory: String,
    icon: String? = null,
    selfAvatar: String = "",
    effectUrl: String? = null,
    effectFileLoader: suspend (String) -> File? = { null },
    size: DpSize = DpSize(width = 116.dp, height = 116.dp)
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color(0xFFFFD3DF), RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
    ) {
        Box(
            modifier = Modifier
                .padding(8.dp)
                .background(Color(0x33000000), Shapes.chip)
                .height(24.dp)
                .padding(horizontal = 8.dp)
        ) {
            Text(text = textCategory, color = Color(0xFFFFFFFF), fontSize = 12.sp, fontWeight = FontWeight.Medium)
        }
        if (icon != null) {
            Box(
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(vertical = 20.dp)
                    .size(size), contentAlignment = Alignment.Center
            ) {
                if (selfAvatar.isNotEmpty()) {
                    AnimatedComposeImage(
                        model = selfAvatar, modifier = Modifier
                            .size(80.dp)
                            .clip(CircleShape)
                    )
                }
                AnimatedComposeImage(
                    model = icon, modifier = Modifier
                        .size(size)
                        .align(Alignment.Center)
                )
            }
        }
        if (effectUrl != null) {
            Box(
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(bottom = 20.dp, top = 24.dp)
                    .size(173.dp, 360.dp)
                    .paint(painterResource(id = R.drawable.cpd_bg_rom))
                    .padding(6.dp),
                contentAlignment = Alignment.Center
            ) {
                EffectView(
                    effectUrl, effectFileLoader, modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(8.dp))
                )
            }
        }
    }
}

@Composable
private fun ContentLimited(name: String, desc: String) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White)
            .navigationBarsPadding()
            .padding(16.dp, 20.dp)
    ) {
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(text = name, color = Color(0xFF1D2129), fontSize = 18.sp, fontWeight = FontWeight.Medium)
            Text(text = stringResource(R.string.cpd_活动专属奖励), color = Color(0xFFFF5E8B), fontSize = 12.sp)
        }
        Spacer(modifier = Modifier.height(20.dp))
        Text(text = stringResource(R.string.cpd_商品描述), color = Color(0xFF1D2129), fontSize = 14.sp)
        Spacer(modifier = Modifier.height(8.dp))
        Text(text = desc, color = Color(0xFF86909C), fontSize = 12.sp, modifier = Modifier.fillMaxWidth())
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = stringResource(R.string.cpd_限时活动获得_无法购买),
            color = Color(0xFFC9CDD4),
            fontSize = 14.sp,
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.Center
        )
    }
}

@Preview
@Composable
private fun PreviewLimit() {
    PreviewCupidTheme {
        Column(modifier = Modifier.fillMaxWidth()) {
            Header(textCategory = "头像框", icon = "", effectFileLoader = { null })
            ContentLimited(
                name = "头像框名称",
                desc = "描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述"
            )
        }
    }
}


@Composable
private fun ContentNoLimited(
    name: String,
    desc: String,
    balance: Int,
    costs: List<PriceConf>,
    buttonEnable: Boolean,
    onCheckMyGold: () -> Unit = {},
    onBuy: (days: Int) -> Unit = {}
) {
    var current by remember {
        mutableIntStateOf(costs.first().day)
    }
    val coin by remember {
        derivedStateOf { costs.first { it.day == current }.coin }
    }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White)
            .padding(horizontal = 16.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(min = 56.dp), verticalAlignment = Alignment.CenterVertically
        ) {
            Text(text = name, color = Color(0xFF1D2129), fontSize = 18.sp, fontWeight = FontWeight.Medium)
            Spacer(modifier = Modifier.weight(1f))
            Image(
                painter = painterResource(id = R.drawable.cupid_gold_icon),
                contentDescription = "gold",
                modifier = Modifier.size(24.dp)
            )
            Text(text = coin.toString(), color = Color(0xFFFF673B), fontSize = 16.sp)
        }
        Text(text = stringResource(R.string.cpd_选择购买时长), color = Color(0xFF1D2129), fontSize = 14.sp)
        Spacer(modifier = Modifier.height(12.dp))
        DurationSelector(duration = current) {
            current = it
        }
        Spacer(modifier = Modifier.height(16.dp))
        Text(text = stringResource(R.string.cpd_商品描述), color = Color(0xFF1D2129), fontSize = 14.sp)
        Spacer(modifier = Modifier.height(8.dp))
        Text(text = desc, color = Color(0xFF86909C), fontSize = 12.sp, modifier = Modifier.fillMaxWidth())
        Spacer(modifier = Modifier.height(16.dp))
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                modifier = Modifier
                    .heightIn(min = 28.dp)
                    .background(Color(0xFFF1F2F3), Shapes.chip)
                    .clickable(onClick = onCheckMyGold)
                    .padding(horizontal = 3.dp), verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(id = R.drawable.cupid_gold_icon),
                    contentDescription = "gold1",
                    modifier = Modifier.size(22.dp)
                )
                Spacer(modifier = Modifier.width(10.dp))
                Text(text = balance.toString(), color = Color(0xFF1D2129), fontSize = 14.sp)
                Spacer(modifier = Modifier.width(3.dp))
            }
            if (!buttonEnable) {
                CircularProgressIndicator(modifier = Modifier.size(24.dp), color = MaterialTheme.colorScheme.primary)
            }
            CpdButton(
                text = stringResource(R.string.cpd_立即购买),
                enabled = buttonEnable,
                modifier = Modifier.widthIn(min = 128.dp),
                onClick = {
                    onBuy.invoke(current)
                })
        }
        Spacer(modifier = Modifier.height(16.dp))
    }
}


@Composable
private fun DurationSelector(duration: Int, options: List<Int> = listOf(3, 7, 30), onDurationChange: (Int) -> Unit) {
    Row {
        options.forEach { item ->
            val isSelected = item == duration
            Box(
                modifier = Modifier
                    .widthIn(min = 96.dp)
                    .height(40.dp)
                    .clickable {
                        onDurationChange(item)
                    }
                    .background(Color(if (isSelected) 0xFFFFEFF3 else 0xFFF1F2F3), Shapes.small)
                    .then(if (isSelected) Modifier.border(1.dp, MaterialTheme.colorScheme.primary, Shapes.small) else Modifier),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = stringResource(id = R.string.cpd_format_day, item),
                    fontSize = 14.sp,
                    color = if (isSelected) MaterialTheme.colorScheme.primary else Color(0xFF4E5969)
                )
            }
            Spacer(modifier = Modifier.width(12.dp))
        }
    }
}

@Preview
@Composable
private fun ContentNoLimitedPreview() {
    PreviewCupidTheme {
        Column(modifier = Modifier.fillMaxWidth()) {
            Header(textCategory = "头像框", icon = "")
            ContentNoLimited(
                name = "头像框名称",
                desc = "描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述描述",
                balance = 350,
                buttonEnable = true,
                costs = emptyList()
            )
        }
    }
}

@Composable
fun DressUpGoodsBuyContent(
    title: String,
    goods: DressUpGoods,
    balance: Int,
    selfAvatar: String,
    buttonEnable: Boolean = true,
    effectFileLoader: suspend (String) -> File? = { null },
    onCheckMyGold: () -> Unit = {},
    onBuy: (days: Int) -> Unit
) {
    Column(modifier = Modifier.fillMaxWidth()) {
        val propInfo = goods.propInfo
        val isEffect = propInfo.propType == 3
        Header(
            textCategory = title,
            selfAvatar = selfAvatar,
            effectFileLoader = effectFileLoader,
            icon = propInfo.icon.takeIf { !isEffect },
            effectUrl = goods.propInfo.effectFile.takeIf { isEffect })
        if (propInfo.gainType == 1) {
            ContentLimited(name = propInfo.name, desc = propInfo.propDesc)
        } else {
            ContentNoLimited(
                name = propInfo.name,
                desc = propInfo.propDesc,
                balance = balance,
                costs = goods.priceConf,
                buttonEnable = buttonEnable,
                onCheckMyGold = onCheckMyGold,
                onBuy = onBuy
            )
        }
    }
}

