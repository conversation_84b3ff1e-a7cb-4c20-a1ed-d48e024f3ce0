package com.qyqy.cupid.ui.home.mine.edit

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.theme.CupidTheme
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.mine.UploadImageContract
import com.qyqy.ucoo.mine.UploadImageViewModel
import com.qyqy.ucoo.sUserFlow
import com.qyqy.ucoo.utils.EntityCallback
import com.qyqy.ucoo.utils.OnClick
import com.qyqy.ucoo.utils.SelectMedia
import com.qyqy.ucoo.utils.rememberRequestAlbumPermissionHelper

@Composable
fun AlbumEditScreen() {
    val vm: UploadImageViewModel = viewModel()
    val user by sUserFlow.collectAsStateWithLifecycle()
    val list by remember {
        derivedStateOf {
            val l = user.albumList.reversed().map { it.url }
            buildList {
                if (l.size < 9) {
                    add("add")
                }
                addAll(l)
            }
        }
    }
    val context = LocalContext.current
    val uistate by vm.uiState.collectAsStateWithLifecycle()
    val loadingState = LocalContentLoading.current
    loadingState.value = uistate.loadingState.isLoading

    val launcher =
        rememberRequestAlbumPermissionHelper(
            context = context,
            needCrop = false,
            selectedLimit = 9 - user.albumList.size
        ) { data: SelectMedia ->
            vm.sendEvent(UploadImageContract.Event.UploadAlbum(data.list))
        }
    AlbumEditContent(albums = list, onDelete = {
        vm.sendEvent(UploadImageContract.Event.DeleteAlbum(it))
    }, onAdd = {
        launcher.start()
    })
}

@Composable
fun AlbumEditContent(albums: List<String>, onDelete: EntityCallback<String> = {}, onAdd: OnClick = {}) {
    Scaffold(modifier = Modifier.fillMaxSize(), topBar = {
        CupidAppBar(title = stringResource(id = R.string.cpd_person_album))
    }) { pv ->
        Column(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.surface)
                .fillMaxSize()
                .padding(pv)
                .padding(16.dp, 20.dp)
        ) {

            LazyVerticalGrid(
                columns = GridCells.Fixed(3),
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.spacedBy(8.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(albums) { item ->
                    if ("add" == item) {
                        AddItem(onClick = onAdd)
                    } else {
                        AlbumItem(url = item, onDel = {
                            onDelete(item)
                        })
                    }
                }
            }

        }
    }
}

@Composable
fun AddItem(onClick: OnClick) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(1f)
            .clip(Shapes.small)
            .click(onClick = onClick)
            .background(Color(0xFFF1F2F3)),
        contentAlignment = Alignment.Center
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_cpd_add_tribe_avator_cross),
            contentDescription = "",
            modifier = Modifier.size(24.dp)
        )
    }
}


@Composable
fun AlbumItem(url: String, modifier: Modifier = Modifier, onDel: () -> Unit = {}) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .aspectRatio(1f)
            .clip(Shapes.small)
    ) {
        ComposeImage(model = url, modifier = Modifier.fillMaxSize())
        Box(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(4.dp)
                .size(20.dp)
                .clip(CircleShape)
                .click(onClick = onDel)
                .background(Color(0x80000000))
                .padding(4.dp)
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_cpd_close),
                contentDescription = "",
                modifier = Modifier.fillMaxSize()
            )
        }
    }
}

@Composable
@Preview
fun AlbumPreview(modifier: Modifier = Modifier) {
    CupidTheme {
        AlbumEditContent(listOf("add", ""))
    }
}