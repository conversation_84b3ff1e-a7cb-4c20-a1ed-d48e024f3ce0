package com.qyqy.cupid.ui.home.mine.edit

import androidx.annotation.StringRes
import androidx.compose.runtime.Stable
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User

@Stable
enum class ChangeableProperty(val postKey: String, val titleModify: Int, val spanCount: Int = 1) {
    AVATAR("avatar_url", R.string.cupid_avatar),
    NICKNAME("nickname", R.string.cupid_modify_nickname),
    BIRTHDAY("birthday", R.string.cupid_modify_birthday),
    INTRO("short_intro", R.string.cupid_modify_intro),
    LIVE_ADDRESS("city_code", R.string.cupid_modify_live_address),
    BORN_ADDRESS("birth_city_code", R.string.cupid_modify_born_address),
    ACADEMY("educational_history", R.string.cupid_modify_academy),
    JOB("job", R.string.cupid_modify_job),
    HEIGHT("height", R.string.cupid_modify_height),
    BODY_SIZE("body_type", R.string.cupid_modify_body_size),
    MARRY_HISTORY("marital_history", R.string.cupid_modify_marry_history),
    SMOKE("tobacco", R.string.cupid_modify_smoke),
    MARRY_INTENT("marriage_intention", R.string.cupid_modify_marry_intent, 2),
    DATE_INTENT("dating_hope", R.string.cupid_modify_date_intent, 2),
    NONE("", 0)

}

fun getChangeablePropertyType(postKey: String) = when (postKey) {
    "avatar_url" -> ChangeableProperty.AVATAR
    "birthday" -> ChangeableProperty.BIRTHDAY
    "nickname" -> ChangeableProperty.NICKNAME
    "short_intro" -> ChangeableProperty.INTRO
    "city_code" -> ChangeableProperty.LIVE_ADDRESS
    "birth_city_code" -> ChangeableProperty.BORN_ADDRESS
    "educational_history" -> ChangeableProperty.ACADEMY
    "job" -> ChangeableProperty.JOB
    "height" -> ChangeableProperty.HEIGHT
    "body_type" -> ChangeableProperty.BODY_SIZE
    "marital_history" -> ChangeableProperty.MARRY_HISTORY
    "tobacco" -> ChangeableProperty.SMOKE
    "marriage_intention" -> ChangeableProperty.MARRY_INTENT
    "dating_hope" -> ChangeableProperty.DATE_INTENT
    else -> ChangeableProperty.NONE
}

fun User.getBaseInfo(property: ChangeableProperty): String {
    val profile = nativeProfile

    if (profile != null) {
        return when (property) {
            ChangeableProperty.NICKNAME -> nickname
            ChangeableProperty.BIRTHDAY -> birthday
            ChangeableProperty.INTRO -> shortIntro
            ChangeableProperty.LIVE_ADDRESS -> profile.cityCode.name
            ChangeableProperty.BORN_ADDRESS -> profile.birthCityCode.name
            ChangeableProperty.ACADEMY -> profile.educationalHistory.name
            ChangeableProperty.JOB -> profile.job.name
            ChangeableProperty.HEIGHT -> if (height == 0) "" else "${height}cm"
            ChangeableProperty.BODY_SIZE -> profile.bodyType.name
            ChangeableProperty.MARRY_HISTORY -> profile.maritalHistory.name
            ChangeableProperty.SMOKE -> profile.tobacco.name
            ChangeableProperty.MARRY_INTENT -> profile.marriageIntention.name
            ChangeableProperty.DATE_INTENT -> profile.datingHope.name
            ChangeableProperty.AVATAR -> avatarUrl
            ChangeableProperty.NONE -> ""
        }
    } else {
        return ""
    }
}

@get:StringRes
val ChangeableProperty.textRes: Int
    get() = when (this) {
        ChangeableProperty.NICKNAME -> R.string.cpd_nickname
        ChangeableProperty.INTRO -> R.string.cpd_description
        ChangeableProperty.BIRTHDAY -> R.string.cpd_birthday
        ChangeableProperty.LIVE_ADDRESS -> R.string.cpd_location_address
        ChangeableProperty.BORN_ADDRESS -> R.string.cpd_born_address
        ChangeableProperty.ACADEMY -> R.string.cpd_academic
        ChangeableProperty.JOB -> R.string.cpd_job
        ChangeableProperty.HEIGHT -> R.string.cpd_height
        ChangeableProperty.BODY_SIZE -> R.string.cpd_body_size
        ChangeableProperty.MARRY_HISTORY -> R.string.cpd_marry_history
        ChangeableProperty.SMOKE -> R.string.cpd_smoke
        ChangeableProperty.MARRY_INTENT -> R.string.cpd_marry_intent
        ChangeableProperty.DATE_INTENT -> R.string.cpd_date_intent
        ChangeableProperty.AVATAR -> R.string.avatar
        ChangeableProperty.NONE -> R.string.cpd_job
    }