package com.qyqy.cupid.ui.home.mine.edit


import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.cupid.data.CityData
import com.qyqy.cupid.data.EnumEntity
import com.qyqy.cupid.data.ProfileEnums
import com.qyqy.cupid.ui.login.loadJapanCityData
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.accountManager
import com.qyqy.ucoo.app
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.core.oss.Uploader
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.mine.MediaInfo
import com.qyqy.ucoo.mine.url
import com.qyqy.ucoo.user.UserApi
import com.qyqy.ucoo.utils.ComposeState
import io.github.album.MediaData
import kotlinx.coroutines.launch

class CupidEditViewModel : ViewModel() {
    private val api = createApi<UserApi>()

    private val _posting: MutableState<Boolean> = mutableStateOf(false)
    val posting: ComposeState<Boolean> = _posting

    private val _cityList: MutableState<List<CityData>> = mutableStateOf(emptyList())
    val cityList: ComposeState<List<CityData>> = _cityList

    private val _enums: MutableState<ProfileEnums> = mutableStateOf(ProfileEnums())

    @Composable
    fun rememberPropertyValues(property: ChangeableProperty): List<EnumEntity> = remember {
        val em = _enums.value
        when (property) {
            ChangeableProperty.ACADEMY -> em.educationalHistory
            ChangeableProperty.JOB -> em.job
            ChangeableProperty.BODY_SIZE -> em.bodyType
            ChangeableProperty.MARRY_HISTORY -> em.maritalHistory
            ChangeableProperty.SMOKE -> em.tobacco
            ChangeableProperty.MARRY_INTENT -> em.marriageIntention
            ChangeableProperty.DATE_INTENT -> em.datingHope
            else -> emptyList()
        }
    }


    val heightOptions by lazy {
        buildList {
            for (i in 140 until 201) {
                add(StringWheelData(i.toString()))
            }
        }
    }

    suspend fun initOptions(context: Context) {
        _cityList.value = loadJapanCityData(context)
        initEnums()
    }

    private suspend fun initEnums() {
        runApiCatching { api.getProfileEnums() }
            .onSuccess {
                _enums.value = it
            }
    }

    suspend fun update(editors: ChangeableProperty, info: Any): Boolean {
        _posting.value = true
        val value = when (info) {
            is EnumEntity -> info.code
            is String -> info
            else -> ""
        }
        val entity = when (info) {
            is EnumEntity -> info
            else -> EnumEntity(name = value)
        }
        val result = runApiCatching {
            api.updateUser(mapOf(editors.postKey to value))
        }.onSuccess { u: AppUser ->
            app.accountManager.updateSelfUser {
                when (editors) {
                    ChangeableProperty.NICKNAME -> this.nickname = info.toString()
                    ChangeableProperty.BIRTHDAY -> this.birthday = info.toString()
                    ChangeableProperty.INTRO -> this.shortIntro = info.toString()
                    ChangeableProperty.LIVE_ADDRESS -> {
                        this.nativeProfile = this.nativeProfile?.copy(cityCode = entity)
                    }

                    ChangeableProperty.BORN_ADDRESS -> {
                        this.nativeProfile = this.nativeProfile?.copy(birthCityCode = entity)
                    }

                    ChangeableProperty.ACADEMY -> {
                        this.nativeProfile = this.nativeProfile?.copy(educationalHistory = entity)
                    }

                    ChangeableProperty.JOB -> {
                        this.nativeProfile = this.nativeProfile?.copy(job = entity)
                    }

                    ChangeableProperty.HEIGHT -> this.height = info.toString().toInt()
                    ChangeableProperty.BODY_SIZE -> {
                        this.nativeProfile = this.nativeProfile?.copy(bodyType = entity)
                    }

                    ChangeableProperty.MARRY_HISTORY -> {
                        this.nativeProfile = this.nativeProfile?.copy(maritalHistory = entity)
                    }

                    ChangeableProperty.SMOKE -> {
                        this.nativeProfile = this.nativeProfile?.copy(tobacco = entity)
                    }

                    ChangeableProperty.MARRY_INTENT -> {
                        this.nativeProfile = this.nativeProfile?.copy(marriageIntention = entity)
                    }

                    ChangeableProperty.DATE_INTENT -> {
                        this.nativeProfile = this.nativeProfile?.copy(datingHope = entity)
                    }
                    else -> {}
                }
            }
        }.toastError()
        _posting.value = false
        return result.isSuccess
    }

    fun updateAvatar(mediaData: MediaData) {
        viewModelScope.launch {
            _posting.value = true
            val result = Uploader.uploadMediaData(mediaData, "avatar")
            result?.also { mf: MediaInfo ->
                runApiCatching {
                    api.updateUser(mapOf("avatar_url" to mf.url))
                }.onSuccess {
                    accountManager.updateSelfUser {
                        avatarUrl = mf.url
                    }
                }
            }
            _posting.value = false
        }
    }
}