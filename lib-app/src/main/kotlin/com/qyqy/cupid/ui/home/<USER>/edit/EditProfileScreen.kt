package com.qyqy.cupid.ui.home.mine.edit

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.data.users.completeCountRateStr
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.IHomeAction
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.accountManager
import com.qyqy.ucoo.compose.LaunchOnceEffect
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.sUserFlow
import com.qyqy.ucoo.utils.OnClick
import com.qyqy.ucoo.utils.SelectMedia
import com.qyqy.ucoo.utils.rememberRequestAlbumPermissionHelper
import kotlinx.coroutines.delay


@Composable
fun CupidEditProfilePage(onAction: IHomeAction, showEditor: ChangeableProperty = ChangeableProperty.NONE) {
    val vm = viewModel<CupidEditViewModel>()
    val context = LocalContext.current
    val launcher = rememberRequestAlbumPermissionHelper(context) { m: SelectMedia ->
        vm.updateAvatar(m.list[0])
    }
    val user by sUserFlow.collectAsStateWithLifecycle()

    LaunchOnceEffect {
        accountManager.refreshSelfUserByRemote()
        if (showEditor == ChangeableProperty.AVATAR) {
            delay(300)
            launcher.start()
        }
    }

    val editorController = editorController(showEditor, vm)
    var displayed by rememberSaveable {
        mutableStateOf(false)
    }
    if (!displayed) {
        LaunchedEffect(key1 = showEditor) {
            when (showEditor) {
                ChangeableProperty.NONE -> {}
                else -> editorController.invoke(showEditor, user.getBaseInfo(showEditor))
            }
        }
        displayed = true
    }
    EditProfileContent(user, editorController, onAvatarClick = {
        launcher.start()
    }, onGotoAlbumEdit = {
        onAction.onNavigateTo(CupidRouters.ALBUM_EDIT)
    })
}

@Composable
private fun EditProfileContent(
    user: User,
    editorController: EditorController,
    onAvatarClick: OnClick = {},
    onGotoAlbumEdit: OnClick = {},
) {
    val context = LocalContext.current
    val albums = remember(user.albumList) {
        user.albumList.reversed().map { it.url }
    }

    Scaffold(topBar = { CupidAppBar(title = stringResource(id = R.string.cpd_edit_profile)) }) { paddingValues ->
        Column(
            modifier = Modifier
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
        ) {
            val noValue = stringResource(id = R.string.cupid_no_value)
            val itemModifier = Modifier
            Divider(true)
            Surface {
                Column(modifier = Modifier.fillMaxWidth()) {
                    CupidAvatarEditItem(avatar = user.avatarUrl, modifier = itemModifier.clickable(onClick = onAvatarClick))
                    Divider()
                    CupidEditTextItem(
                        title = stringResource(id = R.string.cpd_nickname),
                        content = user.nickname,
                        modifier = itemModifier.clickable {
                            editorController.invoke(ChangeableProperty.NICKNAME, user.nickname)
                        })
                    Divider()
                    CupidEditTextItem(
                        title = stringResource(id = R.string.cpd_birthday),
                        content = user.birthday,
                        modifier = itemModifier.clickable {
                            editorController.invoke(ChangeableProperty.BIRTHDAY, user.birthday)
                        })
                    Divider()
                    CupidEditTextItem(
                        title = stringResource(id = R.string.cpd_description),
                        content = user.shortIntro,
                        modifier = itemModifier.clickable {
                            editorController.invoke(ChangeableProperty.INTRO, user.shortIntro)
                        })
                    Divider()
                    CupidAlbumEditItem(
                        albumList = albums,
                        modifier = itemModifier.click(onClick = onGotoAlbumEdit),
                        onAdd = onGotoAlbumEdit
                    )
                }
            }
            Spacer(modifier = Modifier.height(8.dp))
            Surface {
                Column(modifier = Modifier.fillMaxWidth()) {
                    CupidSectionTitle(title = stringResource(id = R.string.cupid_base_info), content = user.completeCountRateStr)
                    val items = remember {
                        listOf(
                            ChangeableProperty.LIVE_ADDRESS,
                            ChangeableProperty.BORN_ADDRESS,
                            ChangeableProperty.ACADEMY,
                            ChangeableProperty.JOB,
                            ChangeableProperty.HEIGHT,
                            ChangeableProperty.BODY_SIZE,
                            ChangeableProperty.MARRY_HISTORY,
                            ChangeableProperty.SMOKE,
                            ChangeableProperty.MARRY_INTENT,
                            ChangeableProperty.DATE_INTENT,
                        )
                    }
                    items.forEach {
                        Divider()
                        CupidEditTextItem(
                            title = stringResource(id = it.textRes),
                            content = user.getBaseInfo(it).takeIf { txt -> txt.isNotEmpty() } ?: noValue,
                            modifier = itemModifier.click {
                                editorController.invoke(it, user.getBaseInfo(it))
                            })
                    }
                    Spacer(modifier = Modifier.height(40.dp))
                }
            }

        }
    }
}

@Composable
private fun Divider(fullWidth: Boolean = false) {
    HorizontalDivider(
        thickness = 0.5.dp,
        modifier = Modifier.padding(horizontal = if (fullWidth) 0.dp else 16.dp),
        color = Color(0xFFF0F0F0)
    )
}

@Preview
@Composable
private fun EditProfilePreview() {
    PreviewCupidTheme {
        EditProfileContent(user = userForPreview, editorController = { _, _ -> })
    }
}