package com.qyqy.cupid.ui.home.mine.edit

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import com.qyqy.cupid.data.CityData
import com.qyqy.cupid.data.EnumEntity
import com.qyqy.cupid.ui.profile.propertyList
import com.qyqy.cupid.widgets.wheel.common.core.WheelData
import com.qyqy.ucoo.compose.ui.AnimatedDialog
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.utils.EntityCallback
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.launch


typealias EditorController = (ChangeableProperty, String) -> Unit

data class StringWheelData(override val uniqueId: String) : WheelData

@Composable
fun editorController(shoEditors: ChangeableProperty, vm: CupidEditViewModel): EditorController {
    val scope = rememberCoroutineScope()
    val context = LocalContext.current
    var currentProperty by rememberSaveable {
        mutableStateOf(shoEditors)
    }
    var data by rememberSaveable {
        mutableStateOf("")
    }
    val loadingState = LocalContentLoading.current
    val loading by vm.posting
    loadingState.value = loading

    val onSave: EntityCallback<Any> = remember {
        { content: Any ->
            scope.launch {
                if (vm.update(currentProperty, content)) {
                    currentProperty = ChangeableProperty.NONE
                }
            }
        }
    }
    var isActiveClose by remember {
        mutableStateOf(false)
    }
    val onDismiss: OnClick = { currentProperty = ChangeableProperty.NONE }
    val onClose: OnClick = { isActiveClose = true }
    LaunchedEffect(key1 = Unit) {
        scope.launch {
            vm.initOptions(context)
        }
    }

    when (currentProperty) {
        ChangeableProperty.NICKNAME -> AnimatedDialog(
            isActiveClose = isActiveClose,
            onDismiss = onDismiss,
        ) {
            EditorPanelNickName(data, onSave, onClose)
        }

        ChangeableProperty.BIRTHDAY -> AnimatedDialog(
            onDismiss = onDismiss,
            isActiveClose = isActiveClose
        ) {
            EditorBirthday(data, onSave, onClose)
        }

        ChangeableProperty.INTRO -> AnimatedDialog(
            isActiveClose = isActiveClose,
            onDismiss = onDismiss,
        ) {
            EditorPanelIntro(data, onSave, onClose)
        }

        ChangeableProperty.LIVE_ADDRESS -> AnimatedDialog(isActiveClose = isActiveClose, onDismiss = onDismiss) {
            val cityList by vm.cityList
            val cityName = data.split("·").getOrNull(0).orEmpty()
            val cityIndex = cityList.indexOfFirst { cityName == it.name }.takeIf { it != -1 } ?: 0
            val areaList = cityList[cityIndex].city
            val areaName = data.split("·").getOrNull(1).orEmpty()
            val areaIndex = areaList.indexOfFirst { areaName == it.areaName }.takeIf { it != -1 } ?: 0
            EditorAreaSelect(
                stringResource(id = currentProperty.titleModify), cityList,
                { pair: Pair<CityData?, CityData.Area?> ->
                    val entity = EnumEntity(
                        pair.second?.areaCode.orEmpty(),
                        "${pair.first?.name.orEmpty()}·${pair.second?.areaName.orEmpty()}"
                    )
                    onSave(entity)
                },
                onClose,
                previewIndexArray = arrayOf(cityIndex, areaIndex)
            )
        }

        ChangeableProperty.BORN_ADDRESS -> AnimatedDialog(isActiveClose = isActiveClose, onDismiss = onDismiss) {
            val cityList by vm.cityList
            EditorAreaSelect(
                stringResource(id = currentProperty.titleModify), cityList,
                { pair: Pair<CityData?, CityData.Area?> ->
                    val entity = EnumEntity(
                        pair.first?.id.orEmpty(),
                        pair.first?.name.orEmpty()
                    )
                    onSave(entity)
                },
                previewIndexArray = arrayOf(cityList.indexOfFirst { it.name == data }.takeIf { it != -1 } ?: 0),
                onClose = onClose,
                columnCount = 1
            )
        }

        ChangeableProperty.HEIGHT -> AnimatedDialog(isActiveClose = isActiveClose, onDismiss = onDismiss) {
            CupidOptionsSelector(
                title = stringResource(id = currentProperty.titleModify),
                list = vm.heightOptions,
                onSave = { d: StringWheelData ->
                    onSave(d.uniqueId)
                },
                onClose = onClose,
                selectIndex = (data.replace("cm", "").toIntOrNull() ?: 170) - 140
            )
        }

        else -> {
            if (propertyList.contains(currentProperty)) {
                val list = vm.rememberPropertyValues(property = currentProperty)
                ShowOptionEditorPanel(currentProperty, list, data, isActiveClose, onDismiss, onClose, onSave)
            }
        }
    }


    val c: EditorController = remember {
        { editor, content ->
            isActiveClose = false
            currentProperty = editor
            data = content
        }
    }
    return c
}

@Composable
fun ShowOptionEditorPanel(
    property: ChangeableProperty,
    list: List<EnumEntity>,
    data: String,
    isActiveClose: Boolean,
    onDismiss: OnClick,
    onClose: OnClick,
    onSave: EntityCallback<Any>
) {
    val index = list.indexOfFirst { it.name == data.replace("cm", "") }.takeIf { it != -1 } ?: 0
    AnimatedDialog(onDismiss = onDismiss, isActiveClose = isActiveClose) {
        CupidOptionsSelector(
            title = stringResource(id = property.titleModify),
            list = list,
            onSave = { d: EnumEntity ->
                onSave(d)
            },
            onClose = onClose,
            selectIndex = index
        )
    }
}