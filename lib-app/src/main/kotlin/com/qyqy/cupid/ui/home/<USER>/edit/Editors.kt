package com.qyqy.cupid.ui.home.mine.edit

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.data.CityData
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.widgets.click
import com.qyqy.cupid.widgets.wheel.common.DefaultWheelPicker
import com.qyqy.cupid.widgets.wheel.common.WheelPickerDefaults
import com.qyqy.cupid.widgets.wheel.common.core.WheelData
import com.qyqy.cupid.widgets.wheel.datetime.WheelDatePickerComponent
import com.qyqy.cupid.widgets.wheel.datetime.now
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.utils.ComposeContent
import com.qyqy.ucoo.utils.EntityCallback
import com.qyqy.ucoo.utils.LogUtils
import com.qyqy.ucoo.utils.OnClick
import kotlinx.datetime.LocalDate

@Composable
fun EditorPanel(
    modifier: Modifier = Modifier,
    title: String,
    composeContent: ComposeContent,
    onClose: OnClick = {},
    onSave: OnClick = {}
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(Color.White, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .padding(horizontal = 16.dp)
            .imePadding()
            .padding(bottom = 10.dp)
            .navigationBarsPadding()
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(44.dp)
        ) {
            Text(text = title, style = MaterialTheme.typography.headlineMedium, modifier = Modifier.align(Alignment.Center))
            Image(
                painter = painterResource(id = R.drawable.ic_close_gray),
                contentDescription = "",
                modifier = Modifier
                    .size(20.dp)
                    .align(Alignment.CenterEnd)
                    .click(onClick = onClose)
            )
        }
        composeContent()
        AppButton(
            text = stringResource(id = R.string.cpd_save),
            fontSize = 16.sp,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .clip(RoundedCornerShape(50))
                .background(MaterialTheme.colorScheme.primary),
            paddingValues = PaddingValues(14.dp),
            onClick = onSave
        )
    }
}

@Composable
fun EditorTextField(
    value: String,
    onValueChange: (String) -> Unit,
    hint: String,
    modifier: Modifier = Modifier,
    maxLength: Int = 999
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .background(Color(0xFFFAFAFA), Shapes.corner12)
            .padding(12.dp)
    ) {
        BasicTextField(
            value = value,
            onValueChange = onValueChange,
            modifier = Modifier.fillMaxSize(),
            textStyle = MaterialTheme.typography.bodyMedium.copy(lineHeight = 18.sp, fontSize = 14.sp),
            decorationBox = { fn ->
                fn.invoke()
                if (value.isEmpty()) {
                    Text(text = hint, style = MaterialTheme.typography.labelMedium)
                }
            }
        )
        Text(
            text = "${value.length}/$maxLength",
            style = MaterialTheme.typography.labelMedium,
            modifier = Modifier.align(Alignment.BottomEnd)
        )
    }
}

//region 生日日期选择

@Composable
fun EditorBirthday(
    initialValue: String,
    onSave: EntityCallback<String>,
    onClose: OnClick,
    modifier: Modifier = Modifier
) {
    var selectDate by remember {
        mutableStateOf(LocalDate.parse(initialValue))
    }

    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(Color.White, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .padding(horizontal = 16.dp)
            .imePadding()
            .padding(bottom = 10.dp)
            .navigationBarsPadding()
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(44.dp)
        ) {
            Text(
                text = stringResource(id = R.string.cupid_modify_birthday_hint),
                style = MaterialTheme.typography.headlineMedium, modifier = Modifier.align(Alignment.Center)
            )
            Text(
                text = stringResource(id = R.string.cpd_save),
                fontSize = 15.sp,
                color = CpdColors.FFFF5E8B,
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .click(onClick = {
                        onSave(selectDate.toString())
                    })
            )
        }
        WheelDatePickerComponent.WheelDatePicker(
            modifier = Modifier
                .fillMaxWidth(),
            startDate = LocalDate.parse(initialValue),
            dateTextColor = Color(0xFF1D2129),
            selectorProperties = com.qyqy.cupid.widgets.wheel.datetime.WheelPickerDefaults.selectorProperties(
                borderColor = Color(0xFFE5E6EB),
            ),
            yearsRange = IntRange(1945, LocalDate.now().year),
            hideHeaderDivider = true,
            hideHeader = true,
            rowCount = 5,
            height = 224.dp,
            onDateChangeListener = {
                selectDate = it
            },
        )
    }
}

//endregion

//<editor-fold desc="昵称">
@Composable
fun EditorPanelNickName(nickname: String, onSave: EntityCallback<String>, onClose: OnClick) {
    var displayText by remember {
        mutableStateOf(nickname)
    }
    EditorPanel(
        title = stringResource(R.string.cupid_modify_nickname),
        composeContent = {
            EditorTextField(
                displayText,
                {
                    displayText = it.replace("\n", "").take(20)
                },
                hint = stringResource(id = R.string.cpd_please_input_your_nick_name),
                maxLength = 20,
                modifier = Modifier
                    .padding(top = 20.dp, bottom = 20.dp)
                    .height(100.dp)
            )
        }, onSave = { onSave.invoke(displayText) }, onClose = onClose
    )
}
//</editor-fold>

@Composable
fun EditorPanelIntro(intro: String, onSave: EntityCallback<String>, onClose: OnClick) {
    var displayText by remember {
        mutableStateOf(intro)
    }
    EditorPanel(
        title = stringResource(ChangeableProperty.INTRO.titleModify),
        composeContent = {
            EditorTextField(
                displayText,
                {
                    displayText = it.take(200)
                },
                hint = stringResource(id = R.string.cpd_input_profile_to_youself),
                maxLength = 200,
                modifier = Modifier
                    .padding(top = 20.dp, bottom = 20.dp)
                    .height(168.dp)
            )
        }, onSave = { onSave.invoke(displayText) }, onClose = onClose
    )
}

@Composable
fun EditorText(title: String, content: String, maxLength: Int, onSave: EntityCallback<String>, onClose: OnClick) {
    var displayText by remember {
        mutableStateOf(content)
    }
    EditorPanel(
        title = title,
        composeContent = {
            EditorTextField(
                displayText,
                {
                    displayText = it.take(maxLength)
                },
                hint = stringResource(id = R.string.cpd_input_profile_to_youself),
                maxLength = maxLength,
                modifier = Modifier
                    .padding(top = 20.dp, bottom = 20.dp)
                    .height(168.dp)
            )
        }, onSave = { onSave.invoke(displayText) }, onClose = onClose
    )
}

//<editor-fold desc="地区">
@Composable
fun EditorAreaSelect(
    title: String,
    cityList: List<CityData>,
    onSave: EntityCallback<Pair<CityData?, CityData.Area?>>,
    onClose: OnClick,
    modifier: Modifier = Modifier,
    previewIndexArray: Array<Int>,
    rowCount: Int = 5,
    columnCount: Int = 2,
    height: Dp = (rowCount * 48).dp,
) {
    var indexList by remember {
        mutableStateOf(List(columnCount) { 0 })
    }
    EditorPanel(modifier = modifier.heightIn(min = 200.dp), title = title, onSave = {
        try {
            val city = cityList[indexList[0]]
            val area = if (indexList.size > 1) city.city[indexList[1]] else null
            onSave.invoke(city to area)
        } catch (e: Exception) {
            LogUtils.e("EditorAreaSelector",e)
        }
    }, onClose = onClose, composeContent = {
        if (cityList.isNotEmpty()) {
            DefaultWheelPicker(
                cityList,
                wheelIndexs = indexList,
                modifier = modifier,
                height = height,
                startIndexs = previewIndexArray,
                rowCount = rowCount,
                selectorProperties = WheelPickerDefaults.selectorProperties(borderColor = Color(0xFFE5E6EB)),
                onSnapped = { columnIndex, selectIndex ->
                    if (columnIndex < columnCount) {
                        indexList = indexList.toMutableList().also { it[columnIndex] = selectIndex }
                    }
                }
            )
        }
    })
}
//</editor-fold>

@Composable
fun <T : WheelData> CupidOptionsSelector(
    title: String,
    list: List<T>,
    onSave: EntityCallback<T>,
    onClose: OnClick,
    selectIndex: Int = 0,
    rowCount: Int = 5,
    height: Dp = (rowCount * 48).dp,
) {
    val selectList = remember(selectIndex, list) {
        mutableStateOf(List(1) { selectIndex.coerceIn(0, list.lastIndex) })
    }
    EditorPanel(modifier = Modifier.heightIn(min = 200.dp), title = title,
        onClose = onClose, onSave = {
            val idx = selectList.value[0]
            if (idx in list.indices) {
                onSave(list[idx])
            }
        },
        composeContent = {
            if (list.isNotEmpty()) {
                DefaultWheelPicker(
                    list,
                    selectList.value,
                    textColor = LocalContentColor.current,
                    selectorProperties = WheelPickerDefaults.selectorProperties(borderColor = Color(0xFFE5E6EB)),
                    rowCount = rowCount,
                    height = height,
                    startIndexs = arrayOf(selectIndex),
                    modifier = Modifier.padding(top = 14.dp, bottom = 14.dp),
                    textStyle = MaterialTheme.typography.titleMedium,
                    onSnapped = { rowIdx, seletedIdx ->
                        selectList.value = selectList.value.toMutableList().apply {
                            set(rowIdx, seletedIdx)
                        }
                    }
                )
            }
        })
}