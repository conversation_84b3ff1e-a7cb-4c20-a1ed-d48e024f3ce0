package com.qyqy.cupid.ui.home.mine.edit

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.qyqy.cupid.ui.home.mine.CupidBasicAlbumRow
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.utils.OnClick

@Composable
fun CupidBasicEditItem(modifier: Modifier = Modifier, title: String, actions: @Composable RowScope.() -> Unit) {
    Row(
        modifier = modifier.padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(text = title, style = MaterialTheme.typography.bodyMedium)
        Spacer(modifier = Modifier.weight(1f))
        actions()
    }
}


@Composable
fun CupidAvatarEditItem(modifier: Modifier = Modifier, avatar: String) {
    CupidBasicEditItem(modifier = modifier, title = stringResource(R.string.cupid_avatar)) {
        ComposeImage(model = avatar, modifier = Modifier.size(48.dp).clip(CircleShape))
        Spacer(modifier = Modifier.width(8.dp))
        CupidArrowRight()
    }
}

@Composable
fun CupidSectionTitle(modifier: Modifier = Modifier, title: String, content: String) {
    CupidBasicEditItem(modifier = modifier, title = title) {
        Text(text = content, style = MaterialTheme.typography.bodyMedium)
    }
}

@Composable
fun CupidEditTextItem(modifier: Modifier = Modifier, title: String, content: String) {
    CupidBasicEditItem(modifier = modifier, title = title) {
        Text(
            text = content,
            style = MaterialTheme.typography.labelMedium,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.widthIn(max = 184.dp)
        )
        Spacer(modifier = Modifier.width(8.dp))
        CupidArrowRight()
    }
}

@Composable
fun CupidAlbumEditItem(modifier: Modifier = Modifier, albumList: List<String>,onAdd:OnClick={}) {
    CupidBasicEditItem(modifier = modifier, title = stringResource(R.string.cupid_album)) {
        CupidBasicAlbumRow(albumList = albumList, modifier = Modifier.size(248.dp, 56.dp), onAdd = onAdd)
        Spacer(modifier = Modifier.width(8.dp))
        CupidArrowRight()
    }
}

@Composable
private fun CupidArrowRight() {
    Icon(
        painter = painterResource(id = R.drawable.ic_arrow_right),
        contentDescription = "",
        tint = MaterialTheme.colorScheme.onSurfaceVariant,
        modifier = Modifier.size(14.dp)
    )
}