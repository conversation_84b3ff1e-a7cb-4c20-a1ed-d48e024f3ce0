package com.qyqy.cupid.ui.home.message.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import kotlin.LazyThreadSafetyMode

val CupidIconHome: ImageVector by lazy(LazyThreadSafetyMode.NONE) {
    ImageVector.Builder(
        name = "CupidIconHome",
        defaultWidth = 24.dp,
        defaultHeight = 24.dp,
        viewportWidth = 24f,
        viewportHeight = 24f
    ).apply {
        path(
            stroke = SolidColor(Color(0xFF1D2129)),
            strokeLineWidth = 1.8f,
            strokeLineCap = StrokeCap.Round,
            strokeLineJoin = StrokeJoin.Round
        ) {
            moveTo(10.303f, 3.925f)
            lineTo(4.205f, 8.809f)
            curveTo(3.518f, 9.355f, 3.078f, 10.508f, 3.228f, 11.37f)
            lineTo(4.398f, 18.375f)
            curveTo(4.609f, 19.625f, 5.806f, 20.637f, 7.073f, 20.637f)
            horizontalLineTo(16.93f)
            curveTo(18.188f, 20.637f, 19.394f, 19.616f, 19.605f, 18.375f)
            lineTo(20.775f, 11.37f)
            curveTo(20.916f, 10.508f, 20.476f, 9.355f, 19.798f, 8.809f)
            lineTo(13.7f, 3.934f)
            curveTo(12.758f, 3.177f, 11.236f, 3.177f, 10.303f, 3.925f)
            close()
        }
        path(
            stroke = SolidColor(Color(0xFF000000)),
            strokeLineWidth = 1.8f
        ) {
            moveTo(14.666f, 10.903f)
            curveTo(15.526f, 11.404f, 16.131f, 12.421f, 16.104f, 13.608f)
            curveTo(16.047f, 16.114f, 12.617f, 17.961f, 12.001f, 17.961f)
            curveTo(11.386f, 17.961f, 7.955f, 16.114f, 7.899f, 13.608f)
            curveTo(7.872f, 12.421f, 8.477f, 11.404f, 9.336f, 10.903f)
            curveTo(10.141f, 10.435f, 11.151f, 10.432f, 12.001f, 11.123f)
            curveTo(12.852f, 10.432f, 13.862f, 10.434f, 14.666f, 10.903f)
            close()
        }
    }.build()
}
