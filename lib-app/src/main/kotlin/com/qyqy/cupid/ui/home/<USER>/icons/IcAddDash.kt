package com.qyqy.cupid.ui.home.message.icons


import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import kotlin.LazyThreadSafetyMode


val ActionIcons.IconAddDash: ImageVector by lazy(LazyThreadSafetyMode.NONE) {
    ImageVector.Builder(
        name = "Icon",
        defaultWidth = 24.dp,
        defaultHeight = 24.dp,
        viewportWidth = 24f,
        viewportHeight = 24f
    ).apply {
        path(
            stroke = SolidColor(Color(0xFFFF5E8B)),
            strokeLineWidth = 1f,
            strokeLineCap = StrokeCap.Round,
            strokeLineJoin = StrokeJoin.Round
        ) {
            moveTo(12f, 12f)
            moveToRelative(-11f, 0f)
            arcToRelative(11f, 11f, 0f, isMoreThanHalf = true, isPositiveArc = true, 22f, 0f)
            arcToRelative(11f, 11f, 0f, isMoreThanHalf = true, isPositiveArc = true, -22f, 0f)
        }
        path(
            fill = SolidColor(Color(0xFFFF5E8B)),
            pathFillType = PathFillType.EvenOdd
        ) {
            moveTo(12.75f, 6.75f)
            curveTo(12.75f, 6.336f, 12.414f, 6f, 12f, 6f)
            curveTo(11.586f, 6f, 11.25f, 6.336f, 11.25f, 6.75f)
            verticalLineTo(11.25f)
            horizontalLineTo(6.75f)
            curveTo(6.336f, 11.25f, 6f, 11.586f, 6f, 12f)
            curveTo(6f, 12.414f, 6.336f, 12.75f, 6.75f, 12.75f)
            horizontalLineTo(11.25f)
            verticalLineTo(17.25f)
            curveTo(11.25f, 17.664f, 11.586f, 18f, 12f, 18f)
            curveTo(12.414f, 18f, 12.75f, 17.664f, 12.75f, 17.25f)
            verticalLineTo(12.75f)
            horizontalLineTo(17.25f)
            curveTo(17.664f, 12.75f, 18f, 12.414f, 18f, 12f)
            curveTo(18f, 11.586f, 17.664f, 11.25f, 17.25f, 11.25f)
            horizontalLineTo(12.75f)
            verticalLineTo(6.75f)
            close()
        }
    }.build()
}

