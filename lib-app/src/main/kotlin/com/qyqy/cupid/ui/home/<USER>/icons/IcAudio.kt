package com.qyqy.cupid.ui.home.message.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

public val ActionIcons.Audio: ImageVector
    get() {
        if (_audio != null) {
            return _audio!!
        }
        _audio = Builder(name = "Audio", defaultWidth = 25.0.dp, defaultHeight = 24.0.dp,
            viewportWidth = 25.0f, viewportHeight = 24.0f).apply {
            group {
                path(fill = SolidColor(Color(0xFF1D2129)), stroke = null, strokeLineWidth = 0.0f,
                    strokeLineCap = StrokeCap.Butt, strokeLineJoin = StrokeJoin.Miter, strokeLineMiter = 4.0f,
                    pathFillType = PathFillType.EvenOdd
                ) {
                    moveTo(19.57f, 12.01f)
                    curveTo(19.7002f, 12.0284f, 19.8255f, 12.0723f, 19.9387f, 12.1392f)
                    curveTo(20.0519f, 12.206f, 20.1508f, 12.2946f, 20.2298f, 12.3997f)
                    curveTo(20.3087f, 12.5049f, 20.3662f, 12.6245f, 20.3988f, 12.7519f)
                    curveTo(20.4314f, 12.8793f, 20.4386f, 13.0118f, 20.42f, 13.142f)
                    curveTo(20.1693f, 14.876f, 19.3567f, 16.4799f, 18.1066f, 17.7075f)
                    curveTo(16.8566f, 18.9352f, 15.2383f, 19.7187f, 13.5f, 19.938f)
                    verticalLineTo(21.0f)
                    curveTo(13.5f, 21.2652f, 13.3946f, 21.5196f, 13.2071f, 21.7071f)
                    curveTo(13.0196f, 21.8946f, 12.7652f, 22.0f, 12.5f, 22.0f)
                    curveTo(12.2348f, 22.0f, 11.9804f, 21.8946f, 11.7929f, 21.7071f)
                    curveTo(11.6054f, 21.5196f, 11.5f, 21.2652f, 11.5f, 21.0f)
                    verticalLineTo(19.938f)
                    curveTo(9.762f, 19.7184f, 8.144f, 18.9347f, 6.8942f, 17.7071f)
                    curveTo(5.6444f, 16.4795f, 4.8318f, 14.8758f, 4.581f, 13.142f)
                    curveTo(4.5433f, 12.8794f, 4.6115f, 12.6127f, 4.7705f, 12.4004f)
                    curveTo(4.9296f, 12.1881f, 5.1664f, 12.0477f, 5.429f, 12.01f)
                    curveTo(5.6916f, 11.9723f, 5.9583f, 12.0405f, 6.1706f, 12.1996f)
                    curveTo(6.3829f, 12.3586f, 6.5233f, 12.5954f, 6.561f, 12.858f)
                    curveTo(6.7693f, 14.2844f, 7.4841f, 15.5882f, 8.5745f, 16.531f)
                    curveTo(9.665f, 17.4738f, 11.0584f, 17.9927f, 12.5f, 17.9927f)
                    curveTo(13.9415f, 17.9927f, 15.3349f, 17.4738f, 16.4254f, 16.531f)
                    curveTo(17.5159f, 15.5882f, 18.2307f, 14.2844f, 18.439f, 12.858f)
                    curveTo(18.4576f, 12.728f, 18.5017f, 12.6029f, 18.5687f, 12.4899f)
                    curveTo(18.6356f, 12.3769f, 18.7242f, 12.2783f, 18.8293f, 12.1995f)
                    curveTo(18.9345f, 12.1208f, 19.0541f, 12.0635f, 19.1813f, 12.031f)
                    curveTo(19.3086f, 11.9985f, 19.44f, 11.9913f, 19.57f, 12.01f)
                    close()
                    moveTo(12.5f, 2.0f)
                    curveTo(13.8261f, 2.0f, 15.0979f, 2.5268f, 16.0355f, 3.4645f)
                    curveTo(16.9732f, 4.4022f, 17.5f, 5.6739f, 17.5f, 7.0f)
                    verticalLineTo(12.0f)
                    curveTo(17.5f, 13.3261f, 16.9732f, 14.5979f, 16.0355f, 15.5355f)
                    curveTo(15.0979f, 16.4732f, 13.8261f, 17.0f, 12.5f, 17.0f)
                    curveTo(11.1739f, 17.0f, 9.9022f, 16.4732f, 8.9645f, 15.5355f)
                    curveTo(8.0268f, 14.5979f, 7.5f, 13.3261f, 7.5f, 12.0f)
                    verticalLineTo(7.0f)
                    curveTo(7.5f, 5.6739f, 8.0268f, 4.4022f, 8.9645f, 3.4645f)
                    curveTo(9.9022f, 2.5268f, 11.1739f, 2.0f, 12.5f, 2.0f)
                    close()
                    moveTo(12.5f, 4.0f)
                    curveTo(11.7043f, 4.0f, 10.9413f, 4.3161f, 10.3787f, 4.8787f)
                    curveTo(9.8161f, 5.4413f, 9.5f, 6.2043f, 9.5f, 7.0f)
                    verticalLineTo(12.0f)
                    curveTo(9.5f, 12.7956f, 9.8161f, 13.5587f, 10.3787f, 14.1213f)
                    curveTo(10.9413f, 14.6839f, 11.7043f, 15.0f, 12.5f, 15.0f)
                    curveTo(13.2956f, 15.0f, 14.0587f, 14.6839f, 14.6213f, 14.1213f)
                    curveTo(15.1839f, 13.5587f, 15.5f, 12.7956f, 15.5f, 12.0f)
                    verticalLineTo(7.0f)
                    curveTo(15.5f, 6.2043f, 15.1839f, 5.4413f, 14.6213f, 4.8787f)
                    curveTo(14.0587f, 4.3161f, 13.2956f, 4.0f, 12.5f, 4.0f)
                    close()
                }
            }
        }
            .build()
        return _audio!!
    }

private var _audio: ImageVector? = null
