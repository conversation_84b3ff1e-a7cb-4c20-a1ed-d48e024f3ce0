package com.qyqy.cupid.ui.home.message.icons


import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import kotlin.LazyThreadSafetyMode

val ActionIcons.IconBell: ImageVector by lazy(LazyThreadSafetyMode.NONE) {
    ImageVector.Builder(
        name = "IconBell",
        defaultWidth = 18.dp,
        defaultHeight = 18.dp,
        viewportWidth = 18f,
        viewportHeight = 18f
    ).apply {
        path(fill = SolidColor(Color(0xFFFF5E8B))) {
            moveTo(12.857f, 10.103f)
            lineTo(11.846f, 9.129f)
            curveTo(11.63f, 8.909f, 11.366f, 8.455f, 11.289f, 8.168f)
            lineTo(10.815f, 6.398f)
            curveTo(10.374f, 4.754f, 9.028f, 3.592f, 7.458f, 3.301f)
            curveTo(6.921f, 2.755f, 6.143f, 2.536f, 5.373f, 2.742f)
            curveTo(4.61f, 2.946f, 4.035f, 3.543f, 3.846f, 4.291f)
            curveTo(2.663f, 5.335f, 2.096f, 6.995f, 2.531f, 8.618f)
            lineTo(3.005f, 10.388f)
            curveTo(3.082f, 10.675f, 3.08f, 11.2f, 3.001f, 11.492f)
            lineTo(2.607f, 12.849f)
            curveTo(2.453f, 13.393f, 2.529f, 13.928f, 2.831f, 14.357f)
            curveTo(3.124f, 14.781f, 3.621f, 15.038f, 4.185f, 15.074f)
            curveTo(5.667f, 15.172f, 7.154f, 15.013f, 8.581f, 14.631f)
            curveTo(10.009f, 14.248f, 11.376f, 13.642f, 12.611f, 12.823f)
            curveTo(13.058f, 12.531f, 13.337f, 12.059f, 13.389f, 11.528f)
            curveTo(13.442f, 10.996f, 13.25f, 10.478f, 12.857f, 10.103f)
            close()
        }
        path(fill = SolidColor(Color(0xFFFF5E8B))) {
            moveTo(10.736f, 14.81f)
            curveTo(10.66f, 15.7f, 10.039f, 16.489f, 9.129f, 16.733f)
            curveTo(8.577f, 16.881f, 7.971f, 16.803f, 7.479f, 16.507f)
            curveTo(7.199f, 16.358f, 6.956f, 16.123f, 6.781f, 15.862f)
            curveTo(6.876f, 15.852f, 6.969f, 15.834f, 7.07f, 15.822f)
            curveTo(7.237f, 15.8f, 7.411f, 15.776f, 7.582f, 15.745f)
            curveTo(7.99f, 15.673f, 8.402f, 15.585f, 8.808f, 15.477f)
            curveTo(9.207f, 15.37f, 9.6f, 15.242f, 9.982f, 15.102f)
            curveTo(10.125f, 15.049f, 10.27f, 15.002f, 10.405f, 14.944f)
            curveTo(10.513f, 14.9f, 10.621f, 14.856f, 10.736f, 14.81f)
            close()
        }
        path(
            stroke = SolidColor(Color(0xFFFF5E8B)),
            strokeLineWidth = 1.25f,
            strokeLineCap = StrokeCap.Round
        ) {
            moveTo(11.109f, 3.374f)
            lineTo(11.109f, 0.926f)
        }
        path(
            stroke = SolidColor(Color(0xFFFF5E8B)),
            strokeLineWidth = 1.25f,
            strokeLineCap = StrokeCap.Round
        ) {
            moveTo(13.606f, 6.772f)
            horizontalLineTo(16.156f)
        }
        path(
            stroke = SolidColor(Color(0xFFFF5E8B)),
            strokeLineWidth = 1.25f,
            strokeLineCap = StrokeCap.Round
        ) {
            moveTo(12.986f, 4.542f)
            lineTo(15.321f, 2.207f)
        }
    }.build()
}
