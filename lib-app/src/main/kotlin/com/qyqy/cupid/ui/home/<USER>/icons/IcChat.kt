package com.qyqy.cupid.ui.home.message.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import kotlin.LazyThreadSafetyMode

val ActionIcons.Chat: ImageVector by lazy(LazyThreadSafetyMode.NONE) {
    ImageVector.Builder(
        name = "Chat",
        defaultWidth = 16.dp,
        defaultHeight = 16.dp,
        viewportWidth = 16f,
        viewportHeight = 16f
    ).apply {
        path(
            fill = SolidColor(Color(0xFF4E5969)),
            pathFillType = PathFillType.EvenOdd
        ) {
            moveTo(15.2f, 7.999f)
            curveTo(15.2f, 11.975f, 11.977f, 15.199f, 8f, 15.199f)
            curveTo(6.826f, 15.199f, 5.718f, 14.918f, 4.739f, 14.42f)
            lineTo(2.286f, 15.033f)
            curveTo(1.74f, 15.169f, 1.228f, 14.715f, 1.298f, 14.158f)
            lineTo(1.644f, 11.385f)
            curveTo(1.106f, 10.375f, 0.8f, 9.223f, 0.8f, 7.999f)
            curveTo(0.8f, 4.022f, 4.024f, 0.799f, 8f, 0.799f)
            curveTo(11.977f, 0.799f, 15.2f, 4.022f, 15.2f, 7.999f)
            close()
            moveTo(8f, 5.923f)
            curveTo(8.314f, 5.503f, 8.818f, 5.229f, 9.382f, 5.229f)
            curveTo(10.337f, 5.229f, 11.112f, 6.007f, 11.112f, 6.969f)
            curveTo(11.112f, 7.339f, 11.053f, 7.682f, 10.95f, 7.999f)
            curveTo(10.458f, 9.555f, 8.943f, 10.485f, 8.193f, 10.741f)
            curveTo(8.087f, 10.778f, 7.913f, 10.778f, 7.807f, 10.741f)
            curveTo(7.057f, 10.485f, 5.542f, 9.555f, 5.05f, 7.999f)
            curveTo(4.947f, 7.682f, 4.888f, 7.339f, 4.888f, 6.969f)
            curveTo(4.888f, 6.007f, 5.663f, 5.229f, 6.618f, 5.229f)
            curveTo(7.181f, 5.229f, 7.686f, 5.503f, 8f, 5.923f)
            close()
        }
    }.build()
}
