package com.qyqy.cupid.ui.home.message.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val ActionIcons.CurrencyGift: ImageVector
    get() {
        if (_GiftCardLine != null) {
            return _GiftCardLine!!
        }
        _GiftCardLine = ImageVector.Builder(
            name = "GiftCardLine",
            defaultWidth = 25.dp,
            defaultHeight = 24.dp,
            viewportWidth = 25f,
            viewportHeight = 24f
        ).apply {
            group {
                path(
                    fill = SolidColor(Color(0xFF1D2129)),
                    pathFillType = PathFillType.EvenOdd
                ) {
                    moveTo(19.5f, 4f)
                    curveTo(20.265f, 4f, 21.001f, 4.292f, 21.558f, 4.817f)
                    curveTo(22.115f, 5.342f, 22.45f, 6.06f, 22.495f, 6.824f)
                    lineTo(22.5f, 7f)
                    verticalLineTo(17f)
                    curveTo(22.5f, 17.765f, 22.208f, 18.501f, 21.683f, 19.058f)
                    curveTo(21.158f, 19.615f, 20.44f, 19.95f, 19.676f, 19.995f)
                    lineTo(19.5f, 20f)
                    horizontalLineTo(5.5f)
                    curveTo(4.735f, 20f, 3.998f, 19.708f, 3.442f, 19.183f)
                    curveTo(2.885f, 18.658f, 2.55f, 17.94f, 2.505f, 17.176f)
                    lineTo(2.5f, 17f)
                    verticalLineTo(7f)
                    curveTo(2.5f, 6.235f, 2.792f, 5.498f, 3.317f, 4.942f)
                    curveTo(3.842f, 4.385f, 4.56f, 4.05f, 5.324f, 4.005f)
                    lineTo(5.5f, 4f)
                    horizontalLineTo(19.5f)
                    close()
                    moveTo(19.5f, 6f)
                    horizontalLineTo(5.5f)
                    curveTo(5.255f, 6f, 5.019f, 6.09f, 4.836f, 6.253f)
                    curveTo(4.653f, 6.415f, 4.536f, 6.64f, 4.507f, 6.883f)
                    lineTo(4.5f, 7f)
                    verticalLineTo(17f)
                    curveTo(4.5f, 17.245f, 4.59f, 17.481f, 4.753f, 17.664f)
                    curveTo(4.915f, 17.847f, 5.14f, 17.964f, 5.383f, 17.993f)
                    lineTo(5.5f, 18f)
                    horizontalLineTo(19.5f)
                    curveTo(19.745f, 18f, 19.981f, 17.91f, 20.164f, 17.747f)
                    curveTo(20.347f, 17.584f, 20.464f, 17.36f, 20.493f, 17.117f)
                    lineTo(20.5f, 17f)
                    verticalLineTo(7f)
                    curveTo(20.5f, 6.755f, 20.41f, 6.519f, 20.247f, 6.336f)
                    curveTo(20.084f, 6.153f, 19.86f, 6.036f, 19.617f, 6.007f)
                    lineTo(19.5f, 6f)
                    close()
                    moveTo(12.854f, 8.992f)
                    curveTo(13.184f, 8.421f, 13.719f, 7.996f, 14.35f, 7.804f)
                    curveTo(14.981f, 7.612f, 15.662f, 7.668f, 16.254f, 7.959f)
                    lineTo(16.406f, 8.039f)
                    lineTo(16.999f, 8.383f)
                    curveTo(17.308f, 8.561f, 17.579f, 8.798f, 17.795f, 9.081f)
                    curveTo(18.013f, 9.364f, 18.172f, 9.687f, 18.264f, 10.031f)
                    curveTo(18.357f, 10.375f, 18.38f, 10.735f, 18.333f, 11.088f)
                    curveTo(18.287f, 11.441f, 18.171f, 11.782f, 17.993f, 12.091f)
                    curveTo(17.699f, 12.6f, 17.274f, 13.021f, 16.762f, 13.31f)
                    curveTo(16.25f, 13.598f, 15.671f, 13.745f, 15.083f, 13.733f)
                    lineTo(14.878f, 13.723f)
                    lineTo(15.846f, 15.4f)
                    curveTo(15.975f, 15.621f, 16.014f, 15.883f, 15.955f, 16.132f)
                    curveTo(15.897f, 16.381f, 15.745f, 16.598f, 15.531f, 16.738f)
                    curveTo(15.318f, 16.879f, 15.059f, 16.932f, 14.807f, 16.888f)
                    curveTo(14.555f, 16.843f, 14.33f, 16.704f, 14.178f, 16.498f)
                    lineTo(14.114f, 16.4f)
                    lineTo(12.5f, 13.605f)
                    lineTo(10.886f, 16.4f)
                    curveTo(10.758f, 16.619f, 10.551f, 16.782f, 10.307f, 16.854f)
                    curveTo(10.063f, 16.926f, 9.801f, 16.903f, 9.574f, 16.789f)
                    curveTo(9.347f, 16.675f, 9.172f, 16.479f, 9.084f, 16.24f)
                    curveTo(8.996f, 16.002f, 9.003f, 15.739f, 9.102f, 15.505f)
                    lineTo(9.154f, 15.4f)
                    lineTo(10.122f, 13.723f)
                    curveTo(9.502f, 13.774f, 8.879f, 13.648f, 8.328f, 13.359f)
                    curveTo(7.776f, 13.07f, 7.318f, 12.63f, 7.007f, 12.091f)
                    curveTo(6.662f, 11.494f, 6.557f, 10.789f, 6.714f, 10.118f)
                    curveTo(6.87f, 9.446f, 7.276f, 8.86f, 7.849f, 8.477f)
                    lineTo(8.001f, 8.383f)
                    lineTo(8.595f, 8.04f)
                    curveTo(9.166f, 7.71f, 9.842f, 7.61f, 10.484f, 7.76f)
                    curveTo(11.127f, 7.91f, 11.688f, 8.298f, 12.055f, 8.846f)
                    lineTo(12.146f, 8.992f)
                    lineTo(12.5f, 9.604f)
                    lineTo(12.854f, 8.992f)
                    close()
                    moveTo(14.586f, 9.992f)
                    lineTo(13.886f, 11.204f)
                    lineTo(14.505f, 11.561f)
                    curveTo(14.8f, 11.731f, 15.151f, 11.778f, 15.48f, 11.689f)
                    curveTo(15.809f, 11.601f, 16.09f, 11.386f, 16.261f, 11.091f)
                    curveTo(16.308f, 11.01f, 16.338f, 10.92f, 16.351f, 10.827f)
                    curveTo(16.363f, 10.734f, 16.357f, 10.639f, 16.332f, 10.549f)
                    curveTo(16.308f, 10.458f, 16.266f, 10.373f, 16.209f, 10.299f)
                    curveTo(16.152f, 10.224f, 16.08f, 10.162f, 15.999f, 10.115f)
                    lineTo(15.405f, 9.772f)
                    curveTo(15.337f, 9.733f, 15.261f, 9.707f, 15.183f, 9.697f)
                    curveTo(15.105f, 9.686f, 15.026f, 9.692f, 14.949f, 9.712f)
                    curveTo(14.873f, 9.732f, 14.802f, 9.768f, 14.739f, 9.816f)
                    curveTo(14.677f, 9.864f, 14.624f, 9.924f, 14.585f, 9.992f)
                    moveTo(10.413f, 9.992f)
                    curveTo(10.342f, 9.869f, 10.229f, 9.775f, 10.096f, 9.727f)
                    curveTo(9.962f, 9.679f, 9.815f, 9.68f, 9.682f, 9.73f)
                    lineTo(9.594f, 9.772f)
                    lineTo(9f, 10.115f)
                    curveTo(8.919f, 10.162f, 8.847f, 10.224f, 8.79f, 10.299f)
                    curveTo(8.733f, 10.373f, 8.691f, 10.458f, 8.667f, 10.549f)
                    curveTo(8.642f, 10.639f, 8.636f, 10.734f, 8.648f, 10.827f)
                    curveTo(8.661f, 10.92f, 8.691f, 11.01f, 8.738f, 11.091f)
                    curveTo(8.897f, 11.367f, 9.153f, 11.573f, 9.456f, 11.671f)
                    curveTo(9.759f, 11.769f, 10.087f, 11.751f, 10.378f, 11.621f)
                    lineTo(10.495f, 11.561f)
                    lineTo(11.113f, 11.204f)
                    lineTo(10.413f, 9.992f)
                    close()
                }
            }
        }.build()

        return _GiftCardLine!!
    }

@Suppress("ObjectPropertyName")
private var _GiftCardLine: ImageVector? = null