package com.qyqy.cupid.ui.home.message.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.EvenOdd
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

public val ActionIcons.Gift: ImageVector
    get() {
        if (_gift != null) {
            return _gift!!
        }
        _gift = Builder(name = "Gift", defaultWidth = 25.0.dp, defaultHeight = 24.0.dp,
                viewportWidth = 25.0f, viewportHeight = 24.0f).apply {
            group {
                path(fill = SolidColor(Color(0xFFFF5E8B)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = EvenOdd) {
                    moveTo(21.5f, 14.0f)
                    verticalLineTo(18.0f)
                    curveTo(21.5f, 18.7652f, 21.2077f, 19.5015f, 20.6827f, 20.0583f)
                    curveTo(20.1578f, 20.615f, 19.4399f, 20.9501f, 18.676f, 20.995f)
                    lineTo(18.5f, 21.0f)
                    horizontalLineTo(13.5f)
                    verticalLineTo(14.0f)
                    horizontalLineTo(21.5f)
                    close()
                    moveTo(11.5f, 14.0f)
                    verticalLineTo(21.0f)
                    horizontalLineTo(6.5f)
                    curveTo(5.7348f, 21.0f, 4.9985f, 20.7077f, 4.4417f, 20.1827f)
                    curveTo(3.885f, 19.6578f, 3.5499f, 18.9399f, 3.505f, 18.176f)
                    lineTo(3.5f, 18.0f)
                    verticalLineTo(14.0f)
                    horizontalLineTo(11.5f)
                    close()
                    moveTo(9.167f, 2.0f)
                    curveTo(10.507f, 2.0f, 11.705f, 2.608f, 12.5f, 3.564f)
                    curveTo(12.8795f, 3.1065f, 13.3492f, 2.7321f, 13.8798f, 2.4642f)
                    curveTo(14.4104f, 2.1963f, 14.9905f, 2.0407f, 15.584f, 2.007f)
                    lineTo(15.834f, 2.0f)
                    horizontalLineTo(15.9f)
                    curveTo(16.5896f, 2.0f, 17.2509f, 2.2739f, 17.7385f, 2.7615f)
                    curveTo(18.2261f, 3.2491f, 18.5f, 3.9104f, 18.5f, 4.6f)
                    curveTo(18.5f, 5.016f, 18.425f, 5.414f, 18.289f, 5.783f)
                    lineTo(18.199f, 6.0f)
                    horizontalLineTo(18.5f)
                    curveTo(19.2652f, 6.0f, 20.0015f, 6.2923f, 20.5583f, 6.8173f)
                    curveTo(21.115f, 7.3422f, 21.4501f, 8.0601f, 21.495f, 8.824f)
                    lineTo(21.5f, 9.0f)
                    verticalLineTo(12.0f)
                    horizontalLineTo(13.5f)
                    verticalLineTo(6.0f)
                    horizontalLineTo(11.5f)
                    verticalLineTo(12.0f)
                    horizontalLineTo(3.5f)
                    verticalLineTo(9.0f)
                    curveTo(3.5f, 8.2348f, 3.7923f, 7.4985f, 4.3173f, 6.9417f)
                    curveTo(4.8422f, 6.385f, 5.5601f, 6.0499f, 6.324f, 6.005f)
                    lineTo(6.5f, 6.0f)
                    horizontalLineTo(6.8f)
                    curveTo(6.608f, 5.573f, 6.5f, 5.1f, 6.5f, 4.6f)
                    curveTo(6.4999f, 3.9388f, 6.7518f, 3.3024f, 7.2044f, 2.8203f)
                    curveTo(7.6569f, 2.3383f, 8.2761f, 2.0467f, 8.936f, 2.005f)
                    lineTo(9.1f, 2.0f)
                    horizontalLineTo(9.167f)
                    close()
                    moveTo(9.167f, 4.0f)
                    horizontalLineTo(9.1f)
                    curveTo(8.9409f, 4.0f, 8.7883f, 4.0632f, 8.6757f, 4.1757f)
                    curveTo(8.5632f, 4.2883f, 8.5f, 4.4409f, 8.5f, 4.6f)
                    curveTo(8.5f, 4.9713f, 8.6475f, 5.3274f, 8.9101f, 5.59f)
                    curveTo(9.1726f, 5.8525f, 9.5287f, 6.0f, 9.9f, 6.0f)
                    horizontalLineTo(11.476f)
                    curveTo(11.3957f, 5.4446f, 11.118f, 4.9368f, 10.6938f, 4.5695f)
                    curveTo(10.2695f, 4.2022f, 9.7282f, 4.0f, 9.167f, 4.0f)
                    close()
                    moveTo(15.9f, 4.0f)
                    horizontalLineTo(15.833f)
                    curveTo(14.713f, 4.0f, 13.778f, 4.788f, 13.553f, 5.84f)
                    lineTo(13.523f, 6.0f)
                    horizontalLineTo(15.1f)
                    curveTo(15.4713f, 6.0f, 15.8274f, 5.8525f, 16.0899f, 5.59f)
                    curveTo(16.3525f, 5.3274f, 16.5f, 4.9713f, 16.5f, 4.6f)
                    curveTo(16.5f, 4.4577f, 16.4494f, 4.32f, 16.3572f, 4.2116f)
                    curveTo(16.2651f, 4.1032f, 16.1374f, 4.031f, 15.997f, 4.008f)
                    lineTo(15.9f, 4.0f)
                    close()
                }
            }
        }
        .build()
        return _gift!!
    }

private var _gift: ImageVector? = null
