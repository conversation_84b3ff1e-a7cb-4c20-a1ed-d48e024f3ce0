package com.qyqy.cupid.ui.home.message.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import kotlin.LazyThreadSafetyMode

val ActionIcons.IconGroupPeople: ImageVector by lazy(LazyThreadSafetyMode.NONE) {
    ImageVector.Builder(
        name = "IconGroupPeople",
        defaultWidth = 12.dp,
        defaultHeight = 12.dp,
        viewportWidth = 12f,
        viewportHeight = 12f
    ).apply {
        path(fill = SolidColor(Color(0xFFF53F3F))) {
            moveTo(4.5f, 1f)
            curveTo(3.19f, 1f, 2.125f, 2.065f, 2.125f, 3.375f)
            curveTo(2.125f, 4.66f, 3.13f, 5.7f, 4.44f, 5.745f)
            curveTo(4.48f, 5.74f, 4.52f, 5.74f, 4.55f, 5.745f)
            curveTo(4.56f, 5.745f, 4.565f, 5.745f, 4.575f, 5.745f)
            curveTo(4.58f, 5.745f, 4.58f, 5.745f, 4.585f, 5.745f)
            curveTo(5.865f, 5.7f, 6.87f, 4.66f, 6.875f, 3.375f)
            curveTo(6.875f, 2.065f, 5.81f, 1f, 4.5f, 1f)
            close()
        }
        path(fill = SolidColor(Color(0xFFF53F3F))) {
            moveTo(7.04f, 7.074f)
            curveTo(5.645f, 6.144f, 3.37f, 6.144f, 1.965f, 7.074f)
            curveTo(1.33f, 7.499f, 0.98f, 8.074f, 0.98f, 8.689f)
            curveTo(0.98f, 9.304f, 1.33f, 9.874f, 1.96f, 10.295f)
            curveTo(2.66f, 10.764f, 3.58f, 11f, 4.5f, 11f)
            curveTo(5.42f, 11f, 6.34f, 10.764f, 7.04f, 10.295f)
            curveTo(7.67f, 9.869f, 8.02f, 9.299f, 8.02f, 8.679f)
            curveTo(8.015f, 8.064f, 7.67f, 7.494f, 7.04f, 7.074f)
            close()
        }
        path(fill = SolidColor(Color(0xFFF53F3F))) {
            moveTo(9.995f, 3.669f)
            curveTo(10.075f, 4.639f, 9.385f, 5.489f, 8.43f, 5.604f)
            curveTo(8.425f, 5.604f, 8.425f, 5.604f, 8.42f, 5.604f)
            horizontalLineTo(8.405f)
            curveTo(8.375f, 5.604f, 8.345f, 5.604f, 8.32f, 5.614f)
            curveTo(7.835f, 5.639f, 7.39f, 5.484f, 7.055f, 5.199f)
            curveTo(7.57f, 4.739f, 7.865f, 4.049f, 7.805f, 3.299f)
            curveTo(7.77f, 2.894f, 7.63f, 2.524f, 7.42f, 2.209f)
            curveTo(7.61f, 2.114f, 7.83f, 2.054f, 8.055f, 2.034f)
            curveTo(9.035f, 1.949f, 9.91f, 2.679f, 9.995f, 3.669f)
            close()
        }
        path(fill = SolidColor(Color(0xFFF53F3F))) {
            moveTo(10.995f, 8.295f)
            curveTo(10.955f, 8.78f, 10.645f, 9.2f, 10.125f, 9.485f)
            curveTo(9.625f, 9.76f, 8.995f, 9.89f, 8.37f, 9.875f)
            curveTo(8.73f, 9.55f, 8.94f, 9.145f, 8.98f, 8.715f)
            curveTo(9.03f, 8.095f, 8.735f, 7.5f, 8.145f, 7.025f)
            curveTo(7.81f, 6.76f, 7.42f, 6.55f, 6.995f, 6.395f)
            curveTo(8.1f, 6.075f, 9.49f, 6.29f, 10.345f, 6.98f)
            curveTo(10.805f, 7.35f, 11.04f, 7.815f, 10.995f, 8.295f)
            close()
        }
    }.build()
}
