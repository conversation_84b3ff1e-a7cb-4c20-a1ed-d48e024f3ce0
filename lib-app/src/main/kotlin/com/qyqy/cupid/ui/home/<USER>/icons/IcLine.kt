package com.qyqy.cupid.ui.home.message.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.EvenOdd
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

public val ActionIcons.Line: ImageVector
    get() {
        if (_line != null) {
            return _line!!
        }
        _line = Builder(name = "Line", defaultWidth = 25.0.dp, defaultHeight = 24.0.dp,
                viewportWidth = 25.0f, viewportHeight = 24.0f).apply {
            group {
                path(fill = SolidColor(Color(0xFF1D2129)), stroke = null, strokeLineWidth = 0.0f,
                        strokeLineCap = Butt, strokeLineJoin = Miter, strokeLineMiter = 4.0f,
                        pathFillType = EvenOdd) {
                    moveTo(4.5f, 11.5f)
                    curveTo(4.5f, 8.073f, 7.903f, 5.0f, 12.5f, 5.0f)
                    curveTo(17.097f, 5.0f, 20.5f, 8.073f, 20.5f, 11.5f)
                    curveTo(20.5f, 13.185f, 19.586f, 14.813f, 17.949f, 16.317f)
                    curveTo(16.579f, 17.575f, 14.768f, 18.681f, 12.785f, 19.579f)
                    curveTo(12.7275f, 19.1486f, 12.5707f, 18.7374f, 12.327f, 18.378f)
                    curveTo(11.964f, 17.863f, 11.407f, 17.511f, 10.672f, 17.382f)
                    curveTo(6.935f, 16.73f, 4.5f, 14.4f, 4.5f, 11.5f)
                    close()
                    moveTo(12.5f, 3.0f)
                    curveTo(7.156f, 3.0f, 2.5f, 6.643f, 2.5f, 11.5f)
                    curveTo(2.5f, 15.74f, 6.049f, 18.606f, 10.328f, 19.352f)
                    curveTo(10.695f, 19.416f, 10.787f, 19.638f, 10.821f, 19.987f)
                    curveTo(10.868f, 20.453f, 10.766f, 20.951f, 10.974f, 21.387f)
                    curveTo(11.223f, 21.909f, 11.837f, 22.142f, 12.371f, 21.928f)
                    curveTo(14.942f, 20.9f, 17.434f, 19.505f, 19.301f, 17.79f)
                    curveTo(21.164f, 16.08f, 22.5f, 13.957f, 22.5f, 11.5f)
                    curveTo(22.5f, 6.643f, 17.844f, 3.0f, 12.5f, 3.0f)
                    close()
                    moveTo(12.768f, 9.36f)
                    curveTo(12.6364f, 9.2022f, 12.4594f, 9.0888f, 12.2611f, 9.0351f)
                    curveTo(12.0628f, 8.9815f, 11.8528f, 8.9902f, 11.6596f, 9.0602f)
                    curveTo(11.4664f, 9.1301f, 11.2995f, 9.2578f, 11.1815f, 9.426f)
                    curveTo(11.0635f, 9.5941f, 11.0001f, 9.7946f, 11.0f, 10.0f)
                    verticalLineTo(13.0f)
                    curveTo(11.0f, 13.2652f, 11.1054f, 13.5196f, 11.2929f, 13.7071f)
                    curveTo(11.4804f, 13.8946f, 11.7348f, 14.0f, 12.0f, 14.0f)
                    curveTo(12.2652f, 14.0f, 12.5196f, 13.8946f, 12.7071f, 13.7071f)
                    curveTo(12.8946f, 13.5196f, 13.0f, 13.2652f, 13.0f, 13.0f)
                    verticalLineTo(12.762f)
                    lineTo(13.732f, 13.64f)
                    curveTo(13.8636f, 13.7978f, 14.0406f, 13.9112f, 14.2389f, 13.9649f)
                    curveTo(14.4372f, 14.0185f, 14.6472f, 14.0098f, 14.8404f, 13.9398f)
                    curveTo(15.0336f, 13.8699f, 15.2005f, 13.7422f, 15.3185f, 13.574f)
                    curveTo(15.4365f, 13.4059f, 15.4999f, 13.2054f, 15.5f, 13.0f)
                    verticalLineTo(10.0f)
                    curveTo(15.5f, 9.7348f, 15.3946f, 9.4804f, 15.2071f, 9.2929f)
                    curveTo(15.0196f, 9.1054f, 14.7652f, 9.0f, 14.5f, 9.0f)
                    curveTo(14.2348f, 9.0f, 13.9804f, 9.1054f, 13.7929f, 9.2929f)
                    curveTo(13.6054f, 9.4804f, 13.5f, 9.7348f, 13.5f, 10.0f)
                    verticalLineTo(10.238f)
                    lineTo(12.768f, 9.36f)
                    close()
                    moveTo(7.5f, 10.0f)
                    curveTo(7.5f, 9.7348f, 7.3946f, 9.4804f, 7.2071f, 9.2929f)
                    curveTo(7.0196f, 9.1054f, 6.7652f, 9.0f, 6.5f, 9.0f)
                    curveTo(6.2348f, 9.0f, 5.9804f, 9.1054f, 5.7929f, 9.2929f)
                    curveTo(5.6054f, 9.4804f, 5.5f, 9.7348f, 5.5f, 10.0f)
                    verticalLineTo(13.0f)
                    curveTo(5.5f, 13.2652f, 5.6054f, 13.5196f, 5.7929f, 13.7071f)
                    curveTo(5.9804f, 13.8946f, 6.2348f, 14.0f, 6.5f, 14.0f)
                    horizontalLineTo(7.5f)
                    curveTo(7.7652f, 14.0f, 8.0196f, 13.8946f, 8.2071f, 13.7071f)
                    curveTo(8.3946f, 13.5196f, 8.5f, 13.2652f, 8.5f, 13.0f)
                    curveTo(8.5f, 12.7348f, 8.3946f, 12.4804f, 8.2071f, 12.2929f)
                    curveTo(8.0196f, 12.1054f, 7.7652f, 12.0f, 7.5f, 12.0f)
                    verticalLineTo(10.0f)
                    close()
                    moveTo(9.5f, 9.0f)
                    curveTo(9.7652f, 9.0f, 10.0196f, 9.1054f, 10.2071f, 9.2929f)
                    curveTo(10.3946f, 9.4804f, 10.5f, 9.7348f, 10.5f, 10.0f)
                    verticalLineTo(13.0f)
                    curveTo(10.5f, 13.2652f, 10.3946f, 13.5196f, 10.2071f, 13.7071f)
                    curveTo(10.0196f, 13.8946f, 9.7652f, 14.0f, 9.5f, 14.0f)
                    curveTo(9.2348f, 14.0f, 8.9804f, 13.8946f, 8.7929f, 13.7071f)
                    curveTo(8.6054f, 13.5196f, 8.5f, 13.2652f, 8.5f, 13.0f)
                    verticalLineTo(10.0f)
                    curveTo(8.5f, 9.7348f, 8.6054f, 9.4804f, 8.7929f, 9.2929f)
                    curveTo(8.9804f, 9.1054f, 9.2348f, 9.0f, 9.5f, 9.0f)
                    close()
                    moveTo(18.5f, 11.0f)
                    horizontalLineTo(18.366f)
                    curveTo(18.4543f, 11.1519f, 18.5008f, 11.3244f, 18.5008f, 11.5f)
                    curveTo(18.5008f, 11.6756f, 18.4543f, 11.8481f, 18.366f, 12.0f)
                    horizontalLineTo(18.5f)
                    curveTo(18.7652f, 12.0f, 19.0196f, 12.1054f, 19.2071f, 12.2929f)
                    curveTo(19.3946f, 12.4804f, 19.5f, 12.7348f, 19.5f, 13.0f)
                    curveTo(19.5f, 13.2652f, 19.3946f, 13.5196f, 19.2071f, 13.7071f)
                    curveTo(19.0196f, 13.8946f, 18.7652f, 14.0f, 18.5f, 14.0f)
                    horizontalLineTo(17.0f)
                    curveTo(16.7348f, 14.0f, 16.4804f, 13.8946f, 16.2929f, 13.7071f)
                    curveTo(16.1054f, 13.5196f, 16.0f, 13.2652f, 16.0f, 13.0f)
                    verticalLineTo(10.0f)
                    curveTo(16.0f, 9.7348f, 16.1054f, 9.4804f, 16.2929f, 9.2929f)
                    curveTo(16.4804f, 9.1054f, 16.7348f, 9.0f, 17.0f, 9.0f)
                    horizontalLineTo(18.5f)
                    curveTo(18.7652f, 9.0f, 19.0196f, 9.1054f, 19.2071f, 9.2929f)
                    curveTo(19.3946f, 9.4804f, 19.5f, 9.7348f, 19.5f, 10.0f)
                    curveTo(19.5f, 10.2652f, 19.3946f, 10.5196f, 19.2071f, 10.7071f)
                    curveTo(19.0196f, 10.8946f, 18.7652f, 11.0f, 18.5f, 11.0f)
                    close()
                }
            }
        }
        .build()
        return _line!!
    }

private var _line: ImageVector? = null
