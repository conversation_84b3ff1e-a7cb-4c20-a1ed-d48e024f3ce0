package com.qyqy.cupid.ui.home.message.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

public val ActionIcons.Photo: ImageVector
    get() {
        if (_photo != null) {
            return _photo!!
        }
        _photo = Builder(name = "Photo", defaultWidth = 25.0.dp, defaultHeight = 24.0.dp,
            viewportWidth = 25.0f, viewportHeight = 24.0f).apply {
            group {
                path(fill = SolidColor(Color(0xFF1D2129)), stroke = null, strokeLineWidth = 0.0f,
                    strokeLineCap = StrokeCap.Butt, strokeLineJoin = StrokeJoin.Miter, strokeLineMiter = 4.0f,
                    pathFillType = NonZero) {
                    moveTo(20.5f, 3.0f)
                    curveTo(21.0046f, 2.9998f, 21.4906f, 3.1904f, 21.8605f, 3.5335f)
                    curveTo(22.2305f, 3.8766f, 22.4572f, 4.3468f, 22.495f, 4.85f)
                    lineTo(22.5f, 5.0f)
                    verticalLineTo(19.0f)
                    curveTo(22.5002f, 19.5046f, 22.3096f, 19.9906f, 21.9665f, 20.3605f)
                    curveTo(21.6234f, 20.7305f, 21.1532f, 20.9572f, 20.65f, 20.995f)
                    lineTo(20.5f, 21.0f)
                    horizontalLineTo(4.5f)
                    curveTo(3.9954f, 21.0002f, 3.5094f, 20.8096f, 3.1395f, 20.4665f)
                    curveTo(2.7695f, 20.1234f, 2.5428f, 19.6532f, 2.505f, 19.15f)
                    lineTo(2.5f, 19.0f)
                    verticalLineTo(5.0f)
                    curveTo(2.4998f, 4.4954f, 2.6904f, 4.0094f, 3.0335f, 3.6395f)
                    curveTo(3.3766f, 3.2695f, 3.8468f, 3.0428f, 4.35f, 3.005f)
                    lineTo(4.5f, 3.0f)
                    horizontalLineTo(20.5f)
                    close()
                    moveTo(10.379f, 12.05f)
                    lineTo(4.722f, 17.707f)
                    curveTo(4.6562f, 17.7731f, 4.5815f, 17.8296f, 4.5f, 17.875f)
                    verticalLineTo(19.0f)
                    horizontalLineTo(20.5f)
                    verticalLineTo(17.875f)
                    curveTo(20.4185f, 17.8296f, 20.3438f, 17.7731f, 20.278f, 17.707f)
                    lineTo(17.45f, 14.88f)
                    lineTo(16.743f, 15.587f)
                    lineTo(16.95f, 15.794f)
                    curveTo(17.0428f, 15.8869f, 17.1165f, 15.9972f, 17.1667f, 16.1186f)
                    curveTo(17.2169f, 16.2399f, 17.2427f, 16.37f, 17.2427f, 16.5014f)
                    curveTo(17.2427f, 16.6327f, 17.2167f, 16.7628f, 17.1664f, 16.8841f)
                    curveTo(17.1161f, 17.0054f, 17.0424f, 17.1157f, 16.9495f, 17.2085f)
                    curveTo(16.8566f, 17.3013f, 16.7463f, 17.375f, 16.6249f, 17.4252f)
                    curveTo(16.5036f, 17.4754f, 16.3735f, 17.5012f, 16.2421f, 17.5012f)
                    curveTo(16.1108f, 17.5012f, 15.9807f, 17.4752f, 15.8594f, 17.4249f)
                    curveTo(15.7381f, 17.3746f, 15.6278f, 17.3009f, 15.535f, 17.208f)
                    lineTo(10.38f, 12.05f)
                    horizontalLineTo(10.379f)
                    close()
                    moveTo(20.5f, 5.0f)
                    horizontalLineTo(4.5f)
                    verticalLineTo(15.1f)
                    lineTo(9.495f, 10.106f)
                    curveTo(9.7127f, 9.8883f, 10.0032f, 9.7586f, 10.3106f, 9.7419f)
                    curveTo(10.618f, 9.7251f, 10.9208f, 9.8223f, 11.161f, 10.015f)
                    lineTo(11.262f, 10.105f)
                    lineTo(15.328f, 14.172f)
                    lineTo(16.566f, 12.934f)
                    curveTo(16.7837f, 12.7163f, 17.0742f, 12.5866f, 17.3816f, 12.5699f)
                    curveTo(17.689f, 12.5531f, 17.9918f, 12.6503f, 18.232f, 12.843f)
                    lineTo(18.334f, 12.934f)
                    lineTo(20.5f, 15.101f)
                    verticalLineTo(5.0f)
                    close()
                    moveTo(16.0f, 7.0f)
                    curveTo(16.3978f, 7.0f, 16.7794f, 7.158f, 17.0607f, 7.4393f)
                    curveTo(17.342f, 7.7206f, 17.5f, 8.1022f, 17.5f, 8.5f)
                    curveTo(17.5f, 8.8978f, 17.342f, 9.2794f, 17.0607f, 9.5607f)
                    curveTo(16.7794f, 9.842f, 16.3978f, 10.0f, 16.0f, 10.0f)
                    curveTo(15.6022f, 10.0f, 15.2206f, 9.842f, 14.9393f, 9.5607f)
                    curveTo(14.658f, 9.2794f, 14.5f, 8.8978f, 14.5f, 8.5f)
                    curveTo(14.5f, 8.1022f, 14.658f, 7.7206f, 14.9393f, 7.4393f)
                    curveTo(15.2206f, 7.158f, 15.6022f, 7.0f, 16.0f, 7.0f)
                    close()
                }
            }
        }
            .build()
        return _photo!!
    }

private var _photo: ImageVector? = null
