package com.qyqy.cupid.ui.home.message.icons


import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import kotlin.LazyThreadSafetyMode

val ActionIcons.RbSelected: ImageVector by lazy(LazyThreadSafetyMode.NONE) {
    ImageVector.Builder(
        name = "RbSelected",
        defaultWidth = 20.dp,
        defaultHeight = 20.dp,
        viewportWidth = 20f,
        viewportHeight = 20f
    ).apply {
        path(
            stroke = SolidColor(Color(0xFFFF5E8B)),
            strokeLineWidth = 1f
        ) {
            moveTo(18.5f, 10f)
            curveTo(18.5f, 14.694f, 14.694f, 18.5f, 10f, 18.5f)
            curveTo(5.306f, 18.5f, 1.5f, 14.694f, 1.5f, 10f)
            curveTo(1.5f, 5.306f, 5.306f, 1.5f, 10f, 1.5f)
            curveTo(14.694f, 1.5f, 18.5f, 5.306f, 18.5f, 10f)
            close()
        }
        path(
            fill = SolidColor(Color(0xFFFF5E8B)),
            pathFillType = PathFillType.EvenOdd
        ) {
            moveTo(10f, 15f)
            curveTo(12.761f, 15f, 15f, 12.761f, 15f, 10f)
            curveTo(15f, 7.239f, 12.761f, 5f, 10f, 5f)
            curveTo(7.239f, 5f, 5f, 7.239f, 5f, 10f)
            curveTo(5f, 12.761f, 7.239f, 15f, 10f, 15f)
            close()
        }
    }.build()
}



val ActionIcons.RbUnselected: ImageVector by lazy(LazyThreadSafetyMode.NONE) {
    ImageVector.Builder(
        name = "RbUnselected",
        defaultWidth = 20.dp,
        defaultHeight = 20.dp,
        viewportWidth = 20f,
        viewportHeight = 20f
    ).apply {
        path(
            stroke = SolidColor(Color(0xFFDADADA)),
            strokeLineWidth = 1f
        ) {
            moveTo(18.5f, 10f)
            curveTo(18.5f, 14.694f, 14.694f, 18.5f, 10f, 18.5f)
            curveTo(5.306f, 18.5f, 1.5f, 14.694f, 1.5f, 10f)
            curveTo(1.5f, 5.306f, 5.306f, 1.5f, 10f, 1.5f)
            curveTo(14.694f, 1.5f, 18.5f, 5.306f, 18.5f, 10f)
            close()
        }
    }.build()
}

