package com.qyqy.cupid.ui.home.message.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import kotlin.Suppress

val ActionIcons.VideoCall: ImageVector
    get() {
        if (_VideoCall != null) {
            return _VideoCall!!
        }
        _VideoCall = ImageVector.Builder(
            name = "VideoCall",
            defaultWidth = 25.dp,
            defaultHeight = 24.dp,
            viewportWidth = 25f,
            viewportHeight = 24f
        ).apply {
            group {
                path(fill = SolidColor(Color(0xFF1D2129))) {
                    moveTo(16.5f, 4f)
                    curveTo(17.005f, 4f, 17.491f, 4.19f, 17.861f, 4.534f)
                    curveTo(18.23f, 4.877f, 18.457f, 5.347f, 18.495f, 5.85f)
                    lineTo(18.5f, 6f)
                    verticalLineTo(8.21f)
                    lineTo(20.594f, 6.92f)
                    curveTo(20.775f, 6.809f, 20.981f, 6.745f, 21.193f, 6.735f)
                    curveTo(21.405f, 6.726f, 21.615f, 6.77f, 21.806f, 6.864f)
                    curveTo(21.996f, 6.959f, 22.159f, 7.1f, 22.279f, 7.275f)
                    curveTo(22.399f, 7.449f, 22.473f, 7.652f, 22.494f, 7.863f)
                    lineTo(22.5f, 7.983f)
                    verticalLineTo(16.017f)
                    curveTo(22.5f, 16.229f, 22.446f, 16.438f, 22.343f, 16.623f)
                    curveTo(22.24f, 16.809f, 22.091f, 16.965f, 21.911f, 17.077f)
                    curveTo(21.731f, 17.19f, 21.525f, 17.254f, 21.314f, 17.265f)
                    curveTo(21.102f, 17.276f, 20.89f, 17.232f, 20.7f, 17.139f)
                    lineTo(20.594f, 17.081f)
                    lineTo(18.5f, 15.79f)
                    verticalLineTo(18f)
                    curveTo(18.5f, 18.505f, 18.31f, 18.991f, 17.966f, 19.361f)
                    curveTo(17.623f, 19.73f, 17.153f, 19.957f, 16.65f, 19.995f)
                    lineTo(16.5f, 20f)
                    horizontalLineTo(4.5f)
                    curveTo(3.995f, 20f, 3.509f, 19.81f, 3.139f, 19.466f)
                    curveTo(2.769f, 19.123f, 2.543f, 18.653f, 2.505f, 18.15f)
                    lineTo(2.5f, 18f)
                    verticalLineTo(6f)
                    curveTo(2.5f, 5.495f, 2.69f, 5.009f, 3.033f, 4.639f)
                    curveTo(3.377f, 4.269f, 3.847f, 4.043f, 4.35f, 4.005f)
                    lineTo(4.5f, 4f)
                    horizontalLineTo(16.5f)
                    close()
                    moveTo(16.5f, 6f)
                    horizontalLineTo(4.5f)
                    verticalLineTo(18f)
                    horizontalLineTo(16.5f)
                    verticalLineTo(6f)
                    close()
                    moveTo(20.5f, 9.327f)
                    lineTo(18.5f, 10.559f)
                    verticalLineTo(13.442f)
                    lineTo(20.5f, 14.674f)
                    verticalLineTo(9.327f)
                    close()
                }
            }
        }.build()

        return _VideoCall!!
    }

@Suppress("ObjectPropertyName")
private var _VideoCall: ImageVector? = null
