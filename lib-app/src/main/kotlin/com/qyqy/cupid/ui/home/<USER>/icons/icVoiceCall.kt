package com.qyqy.cupid.ui.home.message.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import kotlin.Suppress

val ActionIcons.VoiceCall: ImageVector
    get() {
        if (_VoiceCall != null) {
            return _VoiceCall!!
        }
        _VoiceCall = ImageVector.Builder(
            name = "VoiceCall",
            defaultWidth = 25.dp,
            defaultHeight = 24.dp,
            viewportWidth = 25f,
            viewportHeight = 24f
        ).apply {
            group {
                path(
                    fill = SolidColor(Color(0xFF1D2129)),
                    pathFillType = PathFillType.EvenOdd
                ) {
                    moveTo(8.672f, 15.83f)
                    curveTo(12.517f, 19.674f, 16.08f, 20.096f, 17.126f, 20.135f)
                    curveTo(18.39f, 20.181f, 19.68f, 19.149f, 20.238f, 18.091f)
                    curveTo(19.348f, 17.048f, 18.189f, 16.237f, 16.92f, 15.359f)
                    curveTo(16.171f, 16.108f, 15.248f, 17.497f, 14.019f, 17f)
                    curveTo(13.32f, 16.719f, 11.594f, 15.924f, 10.086f, 14.415f)
                    curveTo(8.577f, 12.906f, 7.783f, 11.181f, 7.5f, 10.483f)
                    curveTo(7.002f, 9.251f, 8.396f, 8.326f, 9.145f, 7.577f)
                    curveTo(8.267f, 6.287f, 7.471f, 5.098f, 6.429f, 4.253f)
                    curveTo(5.357f, 4.813f, 4.319f, 6.093f, 4.366f, 7.374f)
                    curveTo(4.405f, 8.42f, 4.826f, 11.983f, 8.672f, 15.83f)
                    close()
                    moveTo(17.052f, 22.133f)
                    curveTo(15.612f, 22.08f, 11.531f, 21.517f, 7.257f, 17.243f)
                    curveTo(2.984f, 12.969f, 2.421f, 8.889f, 2.367f, 7.448f)
                    curveTo(2.287f, 5.252f, 3.969f, 3.119f, 5.912f, 2.286f)
                    curveTo(6.146f, 2.185f, 6.402f, 2.147f, 6.656f, 2.175f)
                    curveTo(6.909f, 2.203f, 7.151f, 2.296f, 7.357f, 2.445f)
                    curveTo(8.965f, 3.618f, 10.074f, 5.395f, 11.027f, 6.787f)
                    curveTo(11.225f, 7.077f, 11.316f, 7.427f, 11.284f, 7.776f)
                    curveTo(11.251f, 8.125f, 11.098f, 8.452f, 10.85f, 8.7f)
                    lineTo(9.494f, 10.057f)
                    curveTo(9.809f, 10.752f, 10.45f, 11.95f, 11.5f, 13f)
                    curveTo(12.55f, 14.05f, 13.748f, 14.691f, 14.444f, 15.007f)
                    lineTo(15.799f, 13.651f)
                    curveTo(16.048f, 13.402f, 16.376f, 13.249f, 16.727f, 13.217f)
                    curveTo(17.077f, 13.186f, 17.428f, 13.279f, 17.717f, 13.479f)
                    curveTo(19.137f, 14.464f, 20.805f, 15.557f, 22.021f, 17.114f)
                    curveTo(22.183f, 17.321f, 22.285f, 17.569f, 22.319f, 17.83f)
                    curveTo(22.352f, 18.091f, 22.314f, 18.357f, 22.21f, 18.598f)
                    curveTo(21.373f, 20.552f, 19.255f, 22.215f, 17.052f, 22.133f)
                    close()
                }
            }
        }.build()

        return _VoiceCall!!
    }

@Suppress("ObjectPropertyName")
private var _VoiceCall: ImageVector? = null



val ActionIcons.VoiceHangup: ImageVector
    get() {
        if (_VoiceHangup != null) {
            return _VoiceHangup!!
        }
        _VoiceHangup = ImageVector.Builder(
            name = "PhoneLine",
            defaultWidth = 20.dp,
            defaultHeight = 20.dp,
            viewportWidth = 20f,
            viewportHeight = 20f
        ).apply {
            group {
                path(
                    fill = SolidColor(Color(0xFFFFFFFF)),
                    pathFillType = PathFillType.EvenOdd
                ) {
                    moveTo(10f, 6.453f)
                    curveTo(5.468f, 6.453f, 3.121f, 8.304f, 2.481f, 8.898f)
                    curveTo(1.709f, 9.615f, 1.557f, 10.983f, 1.851f, 11.935f)
                    curveTo(2.991f, 12.026f, 4.151f, 11.82f, 5.416f, 11.59f)
                    curveTo(5.417f, 10.708f, 5.142f, 9.345f, 6.159f, 8.914f)
                    curveTo(6.737f, 8.668f, 8.222f, 8.119f, 10f, 8.12f)
                    curveTo(11.778f, 8.119f, 13.263f, 8.668f, 13.841f, 8.913f)
                    curveTo(14.86f, 9.345f, 14.583f, 10.711f, 14.583f, 11.594f)
                    curveTo(15.861f, 11.837f, 17.031f, 12.068f, 18.142f, 11.952f)
                    curveTo(18.444f, 10.991f, 18.302f, 9.625f, 17.519f, 8.898f)
                    curveTo(16.88f, 8.304f, 14.532f, 6.453f, 10f, 6.453f)
                    close()
                    moveTo(1.347f, 7.676f)
                    curveTo(2.227f, 6.859f, 4.964f, 4.786f, 10f, 4.786f)
                    curveTo(15.036f, 4.786f, 17.772f, 6.859f, 18.653f, 7.676f)
                    curveTo(19.994f, 8.923f, 20.26f, 11.171f, 19.606f, 12.807f)
                    curveTo(19.528f, 13.004f, 19.399f, 13.178f, 19.234f, 13.311f)
                    curveTo(19.068f, 13.443f, 18.871f, 13.531f, 18.661f, 13.564f)
                    curveTo(17.022f, 13.821f, 15.322f, 13.427f, 13.94f, 13.168f)
                    curveTo(13.653f, 13.115f, 13.393f, 12.962f, 13.206f, 12.737f)
                    curveTo(13.019f, 12.512f, 12.917f, 12.229f, 12.917f, 11.937f)
                    lineTo(12.916f, 10.338f)
                    curveTo(12.321f, 10.114f, 11.238f, 9.786f, 10f, 9.786f)
                    curveTo(8.763f, 9.786f, 7.679f, 10.114f, 7.083f, 10.339f)
                    lineTo(7.084f, 11.936f)
                    curveTo(7.084f, 12.23f, 6.98f, 12.513f, 6.792f, 12.738f)
                    curveTo(6.604f, 12.963f, 6.343f, 13.115f, 6.055f, 13.167f)
                    curveTo(4.638f, 13.424f, 3.011f, 13.763f, 1.377f, 13.562f)
                    curveTo(1.159f, 13.535f, 0.953f, 13.45f, 0.779f, 13.315f)
                    curveTo(0.606f, 13.181f, 0.472f, 13.002f, 0.391f, 12.798f)
                    curveTo(-0.267f, 11.154f, 0.001f, 8.926f, 1.347f, 7.676f)
                    close()
                }
            }
        }.build()

        return _VoiceHangup!!
    }

@Suppress("ObjectPropertyName")
private var _VoiceHangup: ImageVector? = null
