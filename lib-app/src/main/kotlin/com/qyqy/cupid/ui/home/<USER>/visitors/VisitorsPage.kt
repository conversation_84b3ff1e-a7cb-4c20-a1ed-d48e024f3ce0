package com.qyqy.cupid.ui.home.mine.visitors

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveableStateHolder
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.dialog.AnimatedDialog
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.dialog.rememberDialogQueue
import com.qyqy.cupid.ui.membership.MembershipGuide2Card
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.isOnline
import com.qyqy.ucoo.app
import com.qyqy.ucoo.compose.LaunchOnceEffect
import com.qyqy.ucoo.compose.state.EmptyView
import com.qyqy.ucoo.compose.state.LiStateViewModel
import com.qyqy.ucoo.compose.state.itemLoadMore
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AnimatedComposeImage
import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getOrNull
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.compat.chat.utils.UIMessageUtils
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.user.WalletContract
import com.qyqy.ucoo.user.WalletViewModel
import com.qyqy.ucoo.utils.ComposeState
import com.qyqy.ucoo.utils.EntityCallback
import com.qyqy.ucoo.utils.OnClick
import com.qyqy.ucoo.utils.blur.BlurTransformation
import jp.wasabeef.glide.transformations.RoundedCornersTransformation
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.booleanOrNull
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import kotlinx.serialization.json.longOrNull
import retrofit2.http.GET
import retrofit2.http.Query


@Composable
fun VisitorsPage() {
    val vm = viewModel<VisitorViewModel>()
    val isMember by vm.isMember
    val nav = LocalAppNavController.current
    val saveableStateHolder = rememberSaveableStateHolder()
    val dq = rememberDialogQueue<IDialogAction>()
    LaunchOnceEffect {
        if (!isMember) {
            dq.push(JoinMemberDialog(), true)
        }
    }
    saveableStateHolder.SaveableStateProvider(key = "key-visitor") {
        Scaffold(modifier = Modifier.fillMaxSize(), topBar = {
            CupidAppBar(title = stringResource(R.string.cpd_visitor_record))
        }) { pd ->
            val context = LocalContext.current
            Box(modifier = Modifier.fillMaxSize()) {
                LazyVerticalGrid(
                    columns = GridCells.Fixed(2), modifier = Modifier
                        .fillMaxSize()
                        .padding(pd)
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    val l by vm.dataState
                    if (vm.isLoaded && l.isEmpty()) {
                        item(span = { GridItemSpan(2) }) {
                            EmptyView(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .aspectRatio(0.75f),
                                iconRes = R.drawable.cpd_empty_visitor,
                                textRes = R.string.cpd_empty_visitor
                            )
                        }
                    }
                    items(l) { item: Pair<String, AppUser> ->
                        VisitorItem(
                            avatar = item.second.avatarUrl,
                            time = item.first,
                            location = item.second.locationLabel,
                            isOnline = item.second.isOnline,
                            blur = !isMember
                        ) {
                            //click
                            if (isMember) {
                                nav.navigateToProfile(item.second.id)
                            } else {
                                dq.push(JoinMemberDialog(), true)
                            }
                        }
                    }
                    itemLoadMore(vm.allowLoad, vm.isLoadingNow, vm.hasMore, span = { GridItemSpan(2) }) {
                        vm.loadMore()
                    }
                }
                dq.DialogContent()
            }
        }
    }
}

class VisitorViewModel : LiStateViewModel<Pair<String, AppUser>>() {

    companion object {
        private val _visitorCount = MutableStateFlow(0)
        val visitorCount = _visitorCount.asStateFlow()

        private val _visitorVisibleState = MutableStateFlow(false)
        val visitorVisibleState = _visitorVisibleState.asStateFlow()
    }

    private val _isMember: MutableState<Boolean> = mutableStateOf(sUser.isVip)
    val isMember: ComposeState<Boolean> = _isMember


    private var currentPage = 0
    override fun onStartRequest(isRefresh: Boolean) {
        super.onStartRequest(isRefresh)
        if (isRefresh) {
            currentPage = 1
        } else {
            currentPage += 1
        }
    }

    /**
     * 刷新会话中的访客记录
     */
    fun refreshVisitors() {
        viewModelScope.launch {
            var lastRecords: List<Pair<String, AppUser>>? = null
            val list = getVisitors(0, 1, 100) {
                extracted(it)
                lastRecords = it.getOrNull("last_visit_record")?.let { item ->
                    sAppJson.decodeFromJsonElement<List<AppUser>>(item).map { u ->
                        "" to u
                    }
                }
                _visitorCount.value = it.getOrNull("total")?.jsonPrimitive?.intOrNull ?: 0
            }

            _visitorVisibleState.value = list.isNotEmpty() || lastRecords.isNullOrEmpty().not()
            _dataState.value = list.ifEmpty { lastRecords.orEmpty() }
        }
    }

    override suspend fun fetch(): List<Pair<String, AppUser>> {
        val flag = if (sUser.isVip) 1 else 0
        return getVisitors(flag, page = currentPage, pageSize = 20) { obj ->
            extracted(obj)
        }
    }

    private fun extracted(obj: JsonObject) {
        _isMember.value = obj.getOrNull("is_member")?.jsonPrimitive?.booleanOrNull ?: false

    }

    private suspend fun getVisitors(flag: Int = 0, page: Int = 1, pageSize: Int = 100, parser: EntityCallback<JsonObject> = {}) =
        runApiCatching { api.getVisitors(flag, page = page, pageSize = pageSize) }
            .getOrNull()?.let { obj ->
                parser.invoke(obj)
                val list = obj["list"]?.jsonArray?.let { array ->
                    array.map {
                        val u = sAppJson.decodeFromJsonElement<AppUser>(it)
                        val ts =
                            it.jsonObject.getOrNull("last_visit_at_ts")?.jsonPrimitive?.longOrNull
                                ?: System.currentTimeMillis()
                        val time = UIMessageUtils.getMessageTimeFormatText(app, ts * 1000)
                        time to u
                    }
                }
                list
            }.orEmpty()

}



@Composable
fun VisitorItem(avatar: String, time: String, location: String, isOnline: Boolean, blur: Boolean, onClick: OnClick = {}) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(165f / 220)
            .clip(RoundedCornerShape(8.dp))
            .click(onClick = onClick)
    ) {
        val density = LocalDensity.current
        val radius = with(density) {
            8.dp.toPx().toInt()
        }
        AnimatedComposeImage(
            model = avatar,
            modifier = Modifier.fillMaxSize(),
            requestBuilderTransform = {
                if (blur) {
                    it.transform(CenterCrop(), RoundedCornersTransformation(radius, radius), BlurTransformation(16, 4))
                } else {
                    it.transform(CenterCrop())
                }
            },
        )
        val colorGreen = Color(0xFF18E046)

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(8.dp)
        ) {
            //online
            if (isOnline) {
                Row(
                    modifier = Modifier
                        .background(Color(0x80000000), Shapes.chip)
                        .padding(horizontal = 6.dp)
                        .heightIn(min = 18.dp)
                        .align(Alignment.End), verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(6.dp)
                            .background(colorGreen, CircleShape)
                    )
                    Spacer(modifier = Modifier.width(2.dp))
                    Text(text = stringResource(R.string.cpd_online), fontSize = 10.sp, color = colorGreen)
                }
            }
            Spacer(modifier = Modifier.weight(1f))
            val style = with(LocalDensity.current) {
                remember {
                    TextStyle(shadow = Shadow(Color.Gray, offset = Offset(1.dp.toPx(), 1.dp.toPx()), blurRadius = 2.dp.toPx()))
                }
            }
            //location
            Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                Image(
                    painter = painterResource(id = R.drawable.ic_location_white),
                    contentDescription = "location",
                    modifier = Modifier.size(12.dp),
                    contentScale = ContentScale.Inside
                )
                Spacer(modifier = Modifier.width(1.5.dp))
                Text(
                    text = location,
                    fontSize = 14.sp,
                    color = Color.White,
                    modifier = Modifier.weight(1f),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    style = style
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            //time
            Text(
                text = time, fontSize = 12.sp, color = Color.White,
                style = style
            )
        }
    }
}


interface VisitorsApi {
    @GET("api/ucmember/v1/japan/visitors")
    suspend fun getVisitors(
        @Query("refresh") refresh: Int = 0,
        @Query("page") page: Int = 1,
        @Query("page_size")
        pageSize: Int = 100,
    ): ApiResponse<JsonObject>
}

private val api = createApi<VisitorsApi>()

@Preview
@Composable
private fun Preview() {
    PreviewCupidTheme {
        Column {
            JoinDialogContent()
            EmptyView(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(0.75f),
                iconRes = R.drawable.cpd_empty_visitor,
                textRes = R.string.cpd_empty_visitor
            )
        }
    }
}


class JoinMemberDialog : AnimatedDialog<IDialogAction>() {
    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        val vm = viewModel<WalletViewModel>()
        val st = vm.vipInfoFlow.collectAsStateWithLifecycle()
        LaunchedEffect(key1 = vm) {
            vm.sendEvent(WalletContract.Event.FetchCupidVipConfig)
        }
        val nav = LocalAppNavController.current
        JoinDialogContent(memberCount = st.value.getOrNull()?.membership ?: 0) {
            nav.navigate(CupidRouters.MEMBERSHIP_ACTIVE_PAGE, mapOf("from" to "jp_visit_record_nedd_vip"))
            dialog.dismiss()
        }
    }
}

@Composable
fun JoinDialogContent(memberCount: Int = 999, onClick: OnClick = {}) {

    val d = LocalDensity.current
    val topLeft = remember {
        with(d) {
            Offset(0f, 50.dp.toPx())
        }
    }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .drawBehind {
                drawRect(Color(0xFF1C1D1E), topLeft = topLeft)
            }
            .navigationBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        MembershipGuide2Card(memberCount = memberCount)
        Spacer(modifier = Modifier.height(44.dp))
        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(text = stringResource(R.string.cpd_tip_visitor_membership), color = Color(0xFFFFE9CE), fontSize = 16.sp)
            Image(
                painter = painterResource(id = R.drawable.cpd_icon_heart),
                contentDescription = "",
                modifier = Modifier
                    .size(16.dp)
                    .graphicsLayer {
                        rotationY = 30f
                    }
            )
        }
        Spacer(modifier = Modifier.height(44.dp))
        Box(
            modifier = Modifier
                .height(44.dp)
                .widthIn(min = 260.dp, max = 345.dp)
                .click(onClick = onClick)
                .background(Brush.horizontalGradient(listOf(Color(0xFFF2DBB5), Color(0xFFEDB664))), Shapes.chip),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = stringResource(R.string.cpd_active_membership_check_visitor),
                color = Color(0xFF593A0C),
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
        }
        Spacer(modifier = Modifier.height(12.dp))
    }
}