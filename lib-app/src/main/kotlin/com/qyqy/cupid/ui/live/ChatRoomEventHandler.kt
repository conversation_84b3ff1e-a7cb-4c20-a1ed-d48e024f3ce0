package com.qyqy.cupid.ui.live

import androidx.compose.runtime.Composable
import com.qyqy.cupid.event.IMEvent
import com.qyqy.cupid.im.panel.pk.PKResultDialog
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.cupid.ui.dialog.DialogButton
import com.qyqy.cupid.ui.dialog.DialogQueue
import com.qyqy.cupid.ui.dialog.TitleAlertDialog
import com.qyqy.cupid.ui.live.panels.rememberInviteUserPanelState
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.im.bean.PKEvent
import com.qyqy.ucoo.im.compat.WatchRecvNewCustomMessage
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.self

@Composable
fun ChatRoomEventHandler(voiceLiveHelper: VoiceLiveHelper, dialogQueue: DialogQueue<IVoiceLiveAction>) {
    val roomValue = voiceLiveHelper.voiceLiveValue ?: return

    val inviteState = rememberInviteUserPanelState(voiceLiveHelper = voiceLiveHelper)

    WatchRecvNewCustomMessage { message ->
        if (message.targetId == roomValue.imId) {
            if (message.cmd == IMEvent.JAPAN_AUDIOROOM_OWNER_GET_MIC_POPUP_SHARE) {
                val title = message.getJsonString("title").orEmpty()
                val content = message.getJsonString("content").orEmpty()
                val text = message.getJsonString("btn_text").orEmpty()
                dialogQueue.pushCenterDialog { dialog, onAction ->
                    TitleAlertDialog(
                        title = title,
                        content = content,
                        endButton = DialogButton(text) {
                            (onAction as? IVoiceLiveAction)?.shareToFamily()
                            dialog.dismiss()
                        }
                    )
                }
            }
        } else {
            when (message.cmd) {
                MsgEventCmd.INVITE_MIC -> {
                    if (message.getJsonValue<AppUser>("invited_user")?.id == self.id) {
                        message.getJsonValue<AppUser>("admin_user")?.also {
                            inviteState.value = it
                        }
                    }
                }

                MsgEventCmd.TEAM_PK_EVENT -> { // pk模式结果
                    val pkEvent = message.parseDataJson<PKEvent>()
                    if (roomValue.isPkRoom && pkEvent?.action == "end" && pkEvent.winnerInfo != null) {
                        pkEvent.run {
                            winnerInfo ?: return@run null
                            PKResult(
                                when (winnerSide) {
                                    1 -> -1
                                    2 -> 1
                                    else -> 0
                                },
                                winnerInfo.heartUserInfo.user,
                                winnerInfo.heartUserInfo.value,
                                winnerInfo.cheerUserInfo.user,
                                winnerInfo.cheerUserInfo.value
                            )
                        }?.takeIf {
                            it.win != 0
                        }?.also {
                            dialogQueue.push(PKResultDialog(it), true)
                        }
                    }
                }

                else -> {}
            }
        }
    }
}