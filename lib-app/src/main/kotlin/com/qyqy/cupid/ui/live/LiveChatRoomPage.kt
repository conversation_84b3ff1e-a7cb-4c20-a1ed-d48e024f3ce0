package com.qyqy.cupid.ui.live

import android.os.SystemClock
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.compose.currentBackStackEntryAsState
import com.qyqy.cupid.data.AudioRoomAudienceBean
import com.qyqy.cupid.model.AudioRoomDailyViewModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.CupidViewModel
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.call.CallTimer
import com.qyqy.cupid.ui.call.formatMillisecondsToTimeString
import com.qyqy.cupid.ui.dialog.ContentAlertDialog
import com.qyqy.cupid.ui.dialog.DialogButton
import com.qyqy.cupid.ui.dialog.DialogQueue
import com.qyqy.cupid.ui.global.CupidPendentItem
import com.qyqy.cupid.ui.global.ScreenLocation
import com.qyqy.cupid.ui.toCupidRoute
import com.qyqy.cupid.widgets.AlignHorizontalContainer
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.component.WebFrameInfo
import com.qyqy.ucoo.compose.LaunchOnceEffect
import com.qyqy.ucoo.compose.keepLastNonNullState
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.multilingual.currentLocale
import com.qyqy.ucoo.setting.ActivityPendant
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest


@Composable
fun LiveChatRoomScaffold(
    modifier: Modifier = Modifier,
    background: @Composable BoxScope.() -> Unit = {},
    topBar: @Composable ColumnScope.() -> Unit = {},
    bottomBar: @Composable ColumnScope.() -> Unit = {},
    panelContent: @Composable BoxScope.() -> Unit = {},
    overlayContent: @Composable BoxScope.() -> Unit = {},
    safeContent: @Composable BoxScope.() -> Unit = {},
    centerContent: @Composable ColumnScope.() -> Unit = {},
) {
    Box(modifier = modifier) {
        background()
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            topBar()
            Box(
                modifier = Modifier
                    .zIndex(2f)
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                Column(
                    modifier = Modifier.fillMaxSize()
                ) {
                    centerContent()
                }
                safeContent()
            }
            bottomBar()
        }
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            panelContent()
        }
        overlayContent()
    }
}


@Composable
fun VoiceLiveChatRoomFloat(
    voiceLiveChatRoom: VoiceLiveChatRoom,
    onClose: () -> Unit,
    onExpand: () -> Unit,
) {
    OutlinedCard(
        modifier = Modifier
            .size(68.dp, 90.dp)
            .noEffectClickable(enabled = voiceLiveChatRoom.collapsed) {
                onExpand()
            },
        shape = Shapes.corner12,
        colors = CardDefaults.elevatedCardColors(
            containerColor = Color.Black
        ),
        border = BorderStroke(2.dp, Color(0xFFFF5E8B)),
        elevation = CardDefaults.elevatedCardElevation(
            defaultElevation = 3.dp
        ),
    ) {
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            ComposeImage(
                model = voiceLiveChatRoom.extraInfo.cpRoomUser?.avatarUrl ?: voiceLiveChatRoom.basicInfo.owner.avatarUrl,
                modifier = Modifier.fillMaxSize()
            )
            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .size(24.dp)
                    .noEffectClickable(onClick = onClose)
                    .padding(4.dp)
                    .background(Color(0x3B000000), CircleShape)
                    .padding(2.dp),
                contentAlignment = Alignment.Center
            ) {
                Icon(painter = painterResource(id = R.drawable.ic_black_close), contentDescription = null, tint = Color.White)
            }

            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
                    .height(20.dp)
                    .background(Brush.verticalGradient(listOf(Color(0x00FF5E8B), Color(0xFFFF5E8B)))),
                contentAlignment = Alignment.Center
            ) {
                VoiceWaves(count = 11, modifier = Modifier.size(47.dp, 8.dp), minHeight = 3.5.dp)
            }
        }
    }
}

@Preview
@Composable
private fun PreviewVoiceLiveChatRoomFloat() {
    PreviewCupidTheme {
        VoiceLiveChatRoomFloat(VoiceLiveChatRoom.preview, {}) {}
    }
}

@Composable
fun RegisterVoiceLiveChatRoomFloat(viewModel: CupidViewModel, modifier: Modifier) {
    val voiceLiveHelper = viewModel.voiceLiveHelper

    val newValue = voiceLiveHelper.voiceLiveValue

    val keepVale = keepLastNonNullState(newState = newValue)

    val state = newValue ?: keepVale

    if (state == null) {
        return
    }

    val currentRoom by state.currentVoiceLiveRoomAsState()

    val backStackEntry = viewModel.appNavController.composeNav.currentBackStackEntryAsState()

    LaunchedEffect(key1 = backStackEntry) {
        snapshotFlow {
            backStackEntry.value?.destination?.route?.toCupidRoute() == CupidRouters.VOICE_LIVE_ROOM
        }.collectLatest {
            if (!it) {
                voiceLiveHelper.collapseRoom(true)
            } else {
                voiceLiveHelper.expandRoom()
            }
        }
    }

    AlignHorizontalContainer(
        visible = newValue != null && currentRoom.collapsed,
        modifier = modifier,
        tag = "RegisterVoiceLiveChatRoomFloat",
        timeMillis = 500
    ) {
        VoiceLiveChatRoomFloat(currentRoom, {
            voiceLiveHelper.exitCurrentRoom()
        }) {
            voiceLiveHelper.expandRoom()
        }
    }
}

@Composable
fun VoiceLiveRoomFloat(
    room: VoiceLiveChatRoom,
    dialogQueue: DialogQueue<IVoiceLiveAction>,
    modifier: Modifier = Modifier,
) {
    val micTask = room.extraInfo.micTask

    val viewModel = viewModel<AudioRoomDailyViewModel> {
        AudioRoomDailyViewModel(room.basicInfo.audioRoomAudienceBean)
//        AudioRoomDailyViewModel(null)
    }

    LaunchOnceEffect {
        viewModel.refreshAudienceInfo()
    }

    val dailyTask by viewModel.dailyTask.collectAsStateWithLifecycle()

    val config by UIConfig.flowPendantList.collectAsStateWithLifecycle(initialValue = emptyList())

    val list by remember(micTask) {
        derivedStateOf {
            buildList {
                if (micTask != null) {
                    add(micTask)
                }
                if ((dailyTask?.interactTask?.interactStatus ?: -1) != -1) {
                    add(dailyTask)
                }

                val operatorTask =
                    config.find { it.name == "audioroom_operator_daily_task" }?.takeIf { it.visiblePoi.contains(ScreenLocation.ROOM.position) }
                if (operatorTask != null) {
                    add(operatorTask)
                }
            }
        }
    }

    val pagerState = rememberPagerState {
        list.size
    }

    if (list.size > 1) {
        LaunchedEffect(key1 = list.size, pagerState.settledPage) {
            delay(5000L)
            val nextPage = pagerState.settledPage.plus(1).rem(pagerState.pageCount)
            if (nextPage == 0) {
                pagerState.scrollToPage(0)
            } else {
                pagerState.animateScrollToPage(nextPage)
            }
        }
    }

    AlignHorizontalContainer(
        visible = list.isNotEmpty(),
        modifier = modifier,
        tag = "VoiceLiveRoomFloat"
    ) {
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.size(68.dp, 90.dp),
            beyondViewportPageCount = 2,
        ) {
            val item = list[it]
            if (item is MicTask) {
                FamilyMicTaskContent(micTask, Modifier.noEffectClickable {
                    dialogQueue.pushCenterDialog { dialog, _ ->
                        ContentAlertDialog(
                            content = micTask?.taskContent?.tipDesc.orEmpty(),
                            endButton = DialogButton(stringResource(id = R.string.cpd我知道了)) {
                                dialog.dismiss()
                            }
                        )
                    }
                })
            } else if (item is AudioRoomAudienceBean) {
                //语音房任务
                val controller = LocalAppNavController.current
                //语音房任务
                DailyBannerWidget(Modifier.click {
                    dialogQueue.push(
                        controller.webDialog(
                            WebFrameInfo(
                                targetUrl = "${Const.Url.baseURL}/h5/japan/room_task?room_id=${room.roomId}",
                                height = 620,
                                gravity = "bottom"
                            )
                        )
                    )
                }, item)
            } else if (item is ActivityPendant) {
                val navController = LocalAppNavController.current
                CupidPendentItem(item = item, showClose = false, onClick = {
                    navController.navigateByLink(item.jumpLink, item.container)
                }, {})
            }
        }
    }
}

@Composable
fun FamilyMicTaskFloat(
    dialogQueue: DialogQueue<IVoiceLiveAction>,
    micTask: MicTask?,
    modifier: Modifier,
) {

    val visible = micTask?.isVisible == true
    AlignHorizontalContainer(
        visible = micTask?.isVisible == true,
        modifier = modifier,
        tag = "FamilyMicTaskFloat"
    ) {
        FamilyMicTaskContent(micTask, Modifier.noEffectClickable(enabled = visible) {
            dialogQueue.pushCenterDialog { dialog, onAction ->
                ContentAlertDialog(
                    content = micTask?.taskContent?.tipDesc.orEmpty(),
                    endButton = DialogButton(stringResource(id = R.string.cpd我知道了)) {
                        dialog.dismiss()
                    }
                )
            }
        })
    }
}


@Composable
fun FamilyMicTaskContent(micTask: MicTask?, modifier: Modifier = Modifier) {
    val task = micTask?.taskContent ?: return
    OutlinedCard(
        modifier = Modifier
            .size(68.dp, 90.dp)
            .then(modifier),
        shape = Shapes.corner12,
        colors = CardDefaults.elevatedCardColors(
            containerColor = Color(0x4DFF5E8B)
        ),
        border = BorderStroke(1.dp, Color(0xFFFF5E8B)),
    ) {
        Icon(
            painter = painterResource(id = R.drawable.user_heart_fill),
            contentDescription = null,
            modifier = Modifier
                .padding(top = 6.dp)
                .align(Alignment.CenterHorizontally),
            tint = Color(0xFFFF5E8B)
        )
        Box(
            modifier = Modifier
                .align(Alignment.CenterHorizontally)
                .weight(1f)
                .padding(horizontal = 2.dp),
            contentAlignment = Alignment.Center
        ) {
            AutoSizeText(
                text = task.label,
                fontSize = 10.sp,
                color = Color.White,
                maxLines = 3,
                alignment = Alignment.Center,
            )
        }

        val locale = LocalConfiguration.current.currentLocale

        if (task is MicTask.Paused) {
            Text(
                text = task.duration.formatMillisecondsToTimeString(locale),
                fontSize = 14.sp,
                color = Color.White,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier
                    .padding(bottom = 6.dp)
                    .align(Alignment.CenterHorizontally)
            )
        } else {
            task as MicTask.Progress
            CallTimer(isCountdown = false, elapsedRealtime = task.elapsedRealtime) {
                Text(
                    text = it.formatMillisecondsToTimeString(locale),
                    fontSize = 14.sp,
                    color = Color.White,
                    fontWeight = FontWeight.SemiBold,
                    modifier = Modifier
                        .padding(bottom = 6.dp)
                        .align(Alignment.CenterHorizontally)
                )
            }
        }
    }
}


@Preview
@Composable
private fun PreviewFamilyMicTaskContent() {
    PreviewCupidTheme {
        FamilyMicTaskContent(MicTask.Progress("大幅度发", "", SystemClock.elapsedRealtime()))
    }
}

//region 语音房任务
@Composable
fun DailyBannerWidget(modifier: Modifier = Modifier, dailyTask: AudioRoomAudienceBean) {
    Column(
        modifier = modifier
            .size(68.dp, 90.dp)
            .paint(
                painter = painterResource(id = R.drawable.ic_daily_task_banner_bg)
            )
    ) {
        Spacer(modifier = Modifier.height(42.dp))
        val locale = LocalConfiguration.current.currentLocale
        Row(
            modifier = Modifier
                .padding(horizontal = 6.dp)
                .fillMaxWidth()
                .background(color = Color.White, shape = RoundedCornerShape(2.dp))
                .padding(horizontal = 2.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            val startTime = remember(dailyTask) {
                SystemClock.elapsedRealtime().minus(dailyTask.interactTask.interactSeconds.times(1000))
            }
            ComposeImage(model = R.drawable.ic_daily_alarm)
            if (dailyTask.interactTask.interactStatus == 1) {
                CallTimer(isCountdown = false, elapsedRealtime = startTime) {
                    Text(
                        text = it.formatMillisecondsToTimeString(locale),
                        color = CpdColors.FFFF5E8B,
                        fontSize = 12.sp, lineHeight = 12.sp, fontWeight = FontWeight.Bold
                    )
                }
            } else {
                Text(
                    "${dailyTask.interactTask.interactSeconds.times(1000).formatMillisecondsToTimeString(locale)}",
                    color = CpdColors.FFFF5E8B, fontSize = 12.sp, lineHeight = 12.sp, fontWeight = FontWeight.Bold
                )
            }
        }
        Row(
            modifier = Modifier
                .padding(horizontal = 6.dp)
                .padding(top = 6.dp)
                .fillMaxWidth()
                .background(color = Color.White, shape = RoundedCornerShape(2.dp))
                .padding(horizontal = 2.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            ComposeImage(model = R.drawable.ic_daily_gift)
            Text("${dailyTask.giftTask.goldCoinCnt}", color = CpdColors.FFFF5E8B, fontSize = 12.sp, lineHeight = 12.sp, fontWeight = FontWeight.Bold)
        }
    }
}

@Preview
@Composable
private fun DailyBannerWidgetPreview() {
    PreviewCupidTheme {
        DailyBannerWidget(
            dailyTask = AudioRoomAudienceBean(
                giftTask = AudioRoomAudienceBean.GiftTask(5), interactTask = AudioRoomAudienceBean.InteractTask(
                    interactStatus = 1,
                    interactSeconds = 300
                )
            ),
        )
    }
}
//endregion