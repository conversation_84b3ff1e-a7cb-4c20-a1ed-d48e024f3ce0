package com.qyqy.cupid.ui.live

import android.content.res.Configuration
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.repeatOnLifecycle
import com.overseas.common.sntp.SNTPManager
import com.qyqy.cupid.im.panel.pk.PKSupportDialog
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.cupid.ui.dialog.DialogQueue
import com.qyqy.cupid.ui.live.MicSeat.Companion.BasicSeatContent
import com.qyqy.cupid.ui.live.panels.RoomMemberListPanelDialog
import com.qyqy.cupid.ui.live.panels.rememberRankPanelState
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.clearFocusOnKeyboardDismiss
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.theme.D_DIN
import com.qyqy.ucoo.compose.ui.AppBasicTextField
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.LoadingLayout
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.VerticalGrid
import com.qyqy.ucoo.utils.toastRes
import kotlinx.coroutines.delay

@Composable
fun ColumnScope.PKGameContent(
    viewModel: VoiceLiveViewModel,
    dialogQueue: DialogQueue<IVoiceLiveAction>,
    onAction: IVoiceLiveAction,
) {
    val room = viewModel.roomState.value

    val loadingState = LocalContentLoading.current

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        contentAlignment = Alignment.TopCenter
    ) {

        Row(
            modifier = Modifier
                .align(Alignment.TopStart)
        ) {
            RoomNoticeButton(room = room.basicInfo, modifier = Modifier.padding(end = 8.dp))
            Row(
                modifier = Modifier
                    .height(26.dp)
                    .clip(CircleShape)
                    .background(Color(0x4D000000))
                    .clickable {
                        dialogQueue.push(RoomMemberListPanelDialog(viewModel.roomState))
                    }
                    .padding(horizontal = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_room_online),
                    contentDescription = null,
                    modifier = Modifier.size(14.dp)
                )

                Text(
                    text = stringResource(id = R.string.cpd多少人在线, room.visitorsCount),
                    modifier = Modifier.padding(start = 2.dp),
                    fontSize = 12.sp,
                    color = Color.White
                )
            }
        }

        val rankState = rememberRankPanelState(roomId = room.roomId)
        Row(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .height(26.dp)
                .clip(CircleShape)
                .background(Color(0x4D000000))
                .clickable {
                    rankState.value = true
                }
                .padding(horizontal = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_rank_logo),
                contentDescription = null,
                modifier = Modifier.size(14.dp)
            )
            Text(
                text = stringResource(id = R.string.cpd_room_rank),
                modifier = Modifier.padding(start = 2.dp),
                fontSize = 12.sp,
                color = Color.White
            )
        }

        room.micInfo.micList.first().apply {
            BasicSeatContent(modifier = Modifier.heightIn(min = 24.dp), maxWidth = 76.dp, onClick = {
                if (it is MicSeat.Empty) {
                    onAction.upMic(it.index)
                } else {
                    onAction.showUserInfoPanel(it.user)
                }
            })
        }
    }

    val pkState = room.extraInfo.pkState

    Row(modifier = Modifier.padding(horizontal = 16.dp)) {
        Box(
            modifier = Modifier
                .padding(end = 5.dp)
                .weight(1f)
        ) {
            Box(
                modifier = Modifier
                    .height(20.dp)
                    .background(Color(0x40000000), RoundedCornerShape(4.dp))
                    .border(0.5.dp, Color(0xFFE1A989), RoundedCornerShape(4.dp))
                    .basicMarquee()
                    .padding(horizontal = 8.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = if (pkState is PkState.PKContent) {
                        stringResource(R.string.cpdPK惩罚, pkState.data.pkPenalty)
                    } else {
                        stringResource(R.string.cpdPK惩罚, stringResource(id = R.string.cpd等待房管设置))
                    },
                    color = Color.White,
                    fontSize = 11.sp,
                    lineHeight = 11.5.sp,
                    maxLines = 1
                )
            }
        }

        Box(
            modifier = Modifier
                .padding(end = 4.dp)
                .widthIn(min = 44.dp)
                .height(20.dp)
                .clip(RoundedCornerShape(4.dp))
                .background(Color(0x40000000))
                .border(0.5.dp, Color(0xFFE1A989), RoundedCornerShape(4.dp))
                .clickable(enabled = room.basicInfo.isOwner || room.basicInfo.isAdmin) {
                    if (pkState is PkState.Settled) {
                        viewModel.resetPk(loadingState)
                    } else {
                        dialogQueue.push { dialog, _ ->
                            PkSettingsPanel(if (pkState is PkState.PKing) pkState.data else null) { type, title, duration ->
                                dialog.dismiss()
                                viewModel.setPk(loadingState, type, title, duration)
                            }
                        }
                    }
                }
                .padding(horizontal = 3.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = if (pkState is PkState.Settled) {
                    stringResource(id = R.string.cpd重新开始)
                } else {
                    stringResource(id = R.string.cpdPK设置)
                },
                color = Color.White,
                fontSize = 11.sp,
                lineHeight = 11.5.sp,
            )
        }

        Box(
            modifier = Modifier
                .widthIn(min = 44.dp)
                .height(20.dp)
                .background(Color(0x40000000), RoundedCornerShape(4.dp))
                .border(0.5.dp, Color(0xFFE1A989), RoundedCornerShape(4.dp))
                .padding(horizontal = 3.dp),
            contentAlignment = Alignment.Center
        ) {
            val context = LocalContext.current

            var timeline by remember {
                mutableStateOf(context.getString(R.string.cpdPK未开始))
            }

            when (pkState) {

                is PkState.Settled -> {
                    timeline = stringResource(id = R.string.cpdPK已结束)
                }

                is PkState.PKing -> {
                    val lifecycle = LocalLifecycleOwner.current

                    val deadline = pkState.data.deadline
                    LaunchedEffect(deadline) {
                        lifecycle.repeatOnLifecycle(Lifecycle.State.STARTED) {
                            var cur = SNTPManager.now()
                            while (deadline > cur) {
                                val left = deadline.minus(cur)
                                timeline = formatTime(left.plus(500).div(1000).toInt()) // 四舍五入
                                if (left > 1500) {
                                    delay(left.rem(1000))
                                } else {
                                    delay(left)
                                    break
                                }
                                cur = SNTPManager.now()
                            }
                            timeline = context.getString(R.string.cpd正在结算)
                        }
                    }
                }

                else -> {
                    timeline = stringResource(id = R.string.cpdPK未开始)
                }

            }

            Text(text = timeline, color = Color.White, fontSize = 11.sp, lineHeight = 11.5.sp)
        }
    }

    Box {
        Row(
            modifier = Modifier
                .padding(top = 6.dp)
                .fillMaxWidth()
                .paint(painterResource(id = R.drawable.background_pk_mic_seats), contentScale = ContentScale.FillWidth)
        ) {

            GridMicSeatsLayout(
                micInfo = room.micInfo.copy(micList = buildList {
                    addAll(room.micInfo.micList.subList(1, 3))
                    addAll(room.micInfo.micList.subList(5, 7))
                }),
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = 16.dp),
                verticalSpace = 4.dp,
            ) {
                if (it is MicSeat.Empty) {
                    onAction.upMic(it.index)
                } else {
                    onAction.showUserInfoPanel(it.user)
                }
            }

            Spacer(modifier = Modifier.width(8.dp))

            GridMicSeatsLayout(
                micInfo = room.micInfo.copy(micList = buildList {
                    addAll(room.micInfo.micList.subList(3, 5))
                    addAll(room.micInfo.micList.subList(7, 9))
                }),
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = 16.dp),
                verticalSpace = 4.dp,
            ) {
                if (it is MicSeat.Empty) {
                    onAction.upMic(it.index)
                } else {
                    onAction.showUserInfoPanel(it.user)
                }
            }
        }

        Image(
            painter = painterResource(id = R.drawable.ic_voice_room_pk),
            contentDescription = null,
            modifier = Modifier
                .align(Alignment.Center)
                .size(35.dp, 40.dp)
        )
    }


    Box(
        modifier = Modifier
            .padding(top = 3.dp, start = 16.dp, end = 16.dp)
            .fillMaxWidth()
            .height(32.dp),
        contentAlignment = Alignment.CenterStart
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy((-2).dp)
        ) {
            Spacer(
                modifier = Modifier
                    .animateContentSize(animationSpec = spring(stiffness = Spring.StiffnessMediumLow))
                    .fillMaxWidth(if (pkState is PkState.PKContent) {
                        pkState.data.run {
                            val total = blueTeam.score.plus(redTeam.score)
                            if (total == 0) {
                                0.5f
                            } else {
                                blueTeam.score
                                    .toFloat()
                                    .div(blueTeam.score.plus(redTeam.score))
                                    .times(0.8f)
                                    .plus(0.1f)
                            }
                        }
                    } else {
                        0.5f
                    })
                    .height(20.dp)
                    .background(Color(0xFF00B0EF), RoundedCornerShape(topStart = 4.dp, bottomStart = 4.dp))
            )

            Image(
                painter = painterResource(id = R.drawable.ic_pk_score_board_cursor),
                contentDescription = null,
                modifier = Modifier
                    .size(4.dp, 26.dp)
                    .zIndex(1f)
            )

            Spacer(
                modifier = Modifier
                    .animateContentSize(animationSpec = spring(stiffness = Spring.StiffnessMediumLow))
                    .weight(1f)
                    .height(20.dp)
                    .background(Color(0xFFFF2C56), RoundedCornerShape(topEnd = 4.dp, bottomEnd = 4.dp))
            )
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = "${
                    if (pkState is PkState.PKContent) {
                        pkState.data.blueTeam.score
                    } else {
                        0
                    }
                }",
                fontSize = 14.sp,
                color = Color.White,
                fontFamily = D_DIN
            )

            if (pkState is PkState.Settled) {
                Box(modifier = Modifier.weight(1f)) {
                    Image(
                        painter = painterResource(
                            id = if (pkState.result.win == 0) {
                                R.drawable.ic_pk_draw_team
                            } else {
                                R.drawable.ic_pk_win_team
                            }
                        ),
                        contentDescription = null,
                        modifier = Modifier
                            .align(
                                when {
                                    pkState.result.win < 0 -> {
                                        Alignment.CenterStart
                                    }

                                    pkState.result.win == 0 -> {
                                        Alignment.Center
                                    }

                                    else -> {
                                        Alignment.CenterEnd
                                    }
                                }
                            )
                            .size(44.dp, 32.dp)
                    )
                }
            }

            Text(
                text = "${
                    if (pkState is PkState.PKContent) {
                        pkState.data.redTeam.score
                    } else {
                        0
                    }
                }",
                fontSize = 14.sp,
                color = Color.White,
                fontFamily = D_DIN,
                textAlign = TextAlign.End
            )
        }
    }

    Box(
        modifier = Modifier
            .padding(bottom = 10.dp)
            .fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .padding(start = 16.dp)
                .size(157.dp, 36.dp)
                .paint(painterResource(id = R.drawable.background_blue_team_contributor))
                .noEffectClickable {
                    dialogQueue.push(PKSupportDialog(room.roomId, true))
                },
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (pkState is PkState.PKContent) {
                pkState.data.blueTeam.contributeUser.take(3).forEach {
                    CircleComposeImage(
                        model = it.avatarUrl,
                        modifier = Modifier
                            .padding(start = 6.dp)
                            .size(24.dp),
                        borderStroke = BorderStroke(1.dp, Color(0xFF9CBEFF))
                    )
                }
            }

            Text(
                text = if (pkState is PkState.PKContent && pkState.data.blueTeam.contributeCount > 0) {
                    stringResource(id = R.string.cpdPK多少人助威, pkState.data.blueTeam.contributeCount)
                } else {
                    stringResource(id = R.string.cpd助威团虚位以待)
                },
                modifier = Modifier.padding(start = 12.dp),
                fontSize = 12.sp,
                color = Color.White,
                textAlign = TextAlign.Center,
                lineHeight = 13.5.sp
            )
        }

        Row(
            modifier = Modifier
                .padding(end = 16.dp)
                .align(Alignment.TopEnd)
                .size(157.dp, 36.dp)
                .paint(painterResource(id = R.drawable.background_red_team_contributor))
                .noEffectClickable {
                    dialogQueue.push(PKSupportDialog(room.roomId, false))
                },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.End
        ) {
            Text(
                text = if (pkState is PkState.PKContent && pkState.data.redTeam.contributeCount > 0) {
                    stringResource(id = R.string.cpdPK多少人助威, pkState.data.redTeam.contributeCount)
                } else {
                    stringResource(id = R.string.cpd助威团虚位以待)
                },
                modifier = Modifier.padding(end = 12.dp),
                fontSize = 12.sp,
                color = Color.White,
                textAlign = TextAlign.Center,
                lineHeight = 13.5.sp
            )

            if (pkState is PkState.PKContent) {
                pkState.data.redTeam.contributeUser.take(3).forEach {
                    CircleComposeImage(
                        model = it.avatarUrl,
                        modifier = Modifier
                            .padding(end = 6.dp)
                            .size(24.dp),
                        borderStroke = BorderStroke(1.dp, Color(0xFFF5A9FF))
                    )
                }
            }
        }
    }
}


private fun formatTime(time: Int): String {
    val minute = time.div(60)
    val second = time.rem(60)
    return "${if (minute > 9) minute else "0$minute"}:${if (second > 9) second else "0$second"}"
}

@Preview(
    uiMode = Configuration.UI_MODE_NIGHT_YES,
    showBackground = true,
    widthDp = 375,
)
@Composable
fun PreviewGridMicSeatsLayout3() {
    LoadingLayout {
        Column {
            PKGameContent(
                dialogQueue = remember { DialogQueue() },
                viewModel = VoiceLiveViewModel(remember { mutableStateOf(VoiceLiveChatRoom.preview2) }),
                onAction = IVoiceLiveAction.Empty,
            )
        }
    }
}


@Composable
private fun PkSettingsPanel(data: PkData?, onEvent: (Int, String?, Int?) -> Unit = { _, _, _ -> }) {
    Column(
        modifier = Modifier
            .imePadding()
            .fillMaxWidth()
            .background(
                color = Color.White,
                shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)
            )
            .padding(bottom = 20.dp, start = 16.dp, end = 16.dp)
            .navigationBarsPadding(),
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(54.dp),
            contentAlignment = Alignment.CenterStart
        ) {
            Text(
                text = stringResource(id = R.string.cpdPK设置),
                modifier = Modifier.align(Alignment.Center),
                color = Color(0xFF1D2129),
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
        }

        Text(
            text = stringResource(id = R.string.cpd_desc_pk_punishment),
            modifier = Modifier.padding(top = 8.dp),
            color = Color(0xFF1D2129),
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium
        )

        var content by rememberSaveable {
            mutableStateOf(data?.pkPenalty.orEmpty())
        }
        Box(
            modifier = Modifier
                .padding(top = 16.dp)
                .background(Color(0xFFF5F7F9), RoundedCornerShape(4.dp))
                .padding(8.dp),
        ) {
            AppBasicTextField(
                value = content,
                onValueChange = {
                    content = it.take(30)
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(60.dp)
                    .clearFocusOnKeyboardDismiss(),
                enabled = data == null,
                hintValue = stringResource(id = R.string.cpd最多输入多少个字, 30),
                selection = TextRange(content.length),
                textStyle = TextStyle(
                    color = Color(0xFF1D2129),
                    fontSize = 15.sp,
                ),
                hintStyle = TextStyle(
                    color = Color(0xFF86909C),
                    fontSize = 15.sp,
                ),
                cursorBrush = SolidColor(Color(0xFF86909C)),
            )
        }

        Text(
            text = stringResource(id = R.string.cpd_desc_pk_duration),
            modifier = Modifier.padding(top = 16.dp),
            color = Color(0xFF1D2129),
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium
        )

        val timeList = remember {
            if (data == null) {
                arrayOf(5, 15, 30, 60)
            } else {
                arrayOf(data.pkDurationMinute)
            }
        }

        var selectedIndex by rememberSaveable {
            mutableIntStateOf(0)
        }

        VerticalGrid(
            modifier = Modifier.padding(top = 16.dp),
            horizontalSpace = 8.dp,
            verticalSpace = 8.dp,
            columns = 3,
        ) {
            timeList.forEachIndexed { index, it ->
                Text(
                    text = stringResource(id = R.string.cpd_format_second, it),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(40.dp)
                        .clip(CircleShape)
                        .background(if (selectedIndex == index) Color(0xFFFF5E8B) else Color(0xFFF5F7F9))
                        .clickable(enabled = data == null) {
                            selectedIndex = index
                        }
                        .wrapContentHeight(),
                    color = if (selectedIndex == index) Color.White else Color(0xFF86909C),
                    fontSize = 14.sp,
                    textAlign = TextAlign.Center
                )
            }
        }

        Spacer(modifier = Modifier.height(40.dp))

        Row(
            modifier = Modifier
                .align(Alignment.CenterHorizontally)
                .padding(horizontal = 16.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {

            if (data != null) {
                AppButton(
                    text = stringResource(id = R.string.cpd加时5分钟),
                    modifier = Modifier
                        .weight(1f)
                        .widthIn(max = 311.dp)
                        .height(48.dp),
                    fontSize = 16.sp,
                    maxLines = 1,
                    color = Color(0xFFFF5E8B),
                    background = Color.Transparent,
                    border = BorderStroke(1.dp, Color(0xFFFF5E8B))
                ) {
                    onEvent(2, null, 5)
                }
            }

            AppButton(
                text = stringResource(id = if (data != null) R.string.cpd重新开始 else R.string.cpd_start_pk),
                modifier = Modifier
                    .weight(1f)
                    .widthIn(max = 311.dp)
                    .height(48.dp),
                fontSize = 16.sp,
                maxLines = 1,
            ) {
                if (data != null) {
                    onEvent(3, null, null)
                } else {
                    if (content.isEmpty()) {
                        toastRes(R.string.cpd_tip_input_punishment)
                        return@AppButton
                    }
                    onEvent(1, content, timeList[selectedIndex])
                }
            }
        }

    }
}

@Preview(
    uiMode = Configuration.UI_MODE_NIGHT_YES,
    showBackground = true,
    widthDp = 375,
)
@Composable
private fun PreviewPkSettingsPanel() {
    PreviewCupidTheme {
        PkSettingsPanel(null)
    }
}