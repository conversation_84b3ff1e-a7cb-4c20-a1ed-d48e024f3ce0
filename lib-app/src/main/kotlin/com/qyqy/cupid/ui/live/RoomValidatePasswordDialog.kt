package com.qyqy.cupid.ui.live

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.overseas.common.utils.ResultContinuation
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.dialog.NormalDialog
import com.qyqy.cupid.ui.dialog.SimpleDialog
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.D_DIN
import com.qyqy.ucoo.compose.ui.AppButton
import kotlinx.coroutines.delay

data class RoomValidatePasswordDialog(
    val roomId: Int,
    val validateResult: ResultContinuation<String>
) : SimpleDialog() {

    @Composable
    override fun Content(dialog: IDialog) {
        RoomPasswordContent(
            type = 0,
            title = stringResource(id = R.string.cpd当前房间已锁定_输入密码后可进入),
            button = stringResource(id = R.string.cpd确认进入),
        ) { _, pwd ->
            validateResult.resume(pwd)
            dialog.dismiss()
        }

        DisposableEffect(key1 = validateResult) {
            onDispose {
                validateResult.cancel()
            }
        }
    }
}

data object RoomSetLockPasswordDialog : NormalDialog<IDialogAction>() {

    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        val context = LocalContext.current
        var type by remember {
            mutableIntStateOf(1)
        }
        var setPassword by remember {
            mutableStateOf("")
        }
        val button by remember {
            derivedStateOf {
                if (type != 1) {
                    context.getString(R.string.cpd确认密码)
                } else {
                    context.getString(R.string.cpd请设置密码)
                }
            }
        }
        RoomPasswordContent(
            type = type,
            title = stringResource(id = R.string.cpd锁定房间后需要输入密码才能进入),
            button = button,
        ) { t, pwd ->
            if (t == 1) {
                setPassword = pwd
                type++
            } else {
                if (setPassword != pwd) {
                    type++
                } else {
                    (onAction as? IVoiceLiveAction)?.toggleRoomLock(dialog, true, setPassword)
                }
            }
        }
    }
}


@Composable
fun RoomPasswordContent(
    type: Int,
    title: String,
    button: String,
    onConfirm: (Int, String) -> Unit = { _, _ -> }
) {

    Column(
        modifier = Modifier
            .background(Color(0xFFFFFFFF), RoundedCornerShape(8.dp))
            .padding(20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = title,
            color = Color(0xFF1D2129),
            fontSize = 14.sp,
            textAlign = TextAlign.Center,
            fontWeight = FontWeight.Medium
        )

        var password by remember(type) {
            mutableStateOf("")
        }

        Box(modifier = Modifier.padding(top = 24.dp, bottom = 32.dp)) {

            val pattern = remember { Regex("^\\d+\$") }
            val focusRequester = remember { FocusRequester() } //焦点

            LaunchedEffect(Unit) {
                delay(150)
                focusRequester.requestFocus()
            }

            BasicTextField(
                value = password,
                onValueChange = {
                    val input = it.take(4)
                    if (input.isEmpty() || input.matches(pattern)) {
                        password = input
                    }
                },
                modifier = Modifier
                    .matchParentSize()
                    .alpha(0f)
                    .focusRequester(focusRequester),
                singleLine = true,
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.NumberPassword, imeAction = ImeAction.Done),
                keyboardActions = KeyboardActions(
                    onDone = {
                        if (password.length == 4) {
                            onConfirm(type, password)
                        }
                    }
                )
            )

            Row(horizontalArrangement = Arrangement.spacedBy(12.dp)) {
                repeat(4) {
                    Box(
                        modifier = Modifier
                            .size(48.dp)
                            .background(Color(0xFFF1F2F3), RoundedCornerShape(4.dp)),
                        contentAlignment = Alignment.Center,
                    ) {
                        Text(
                            text = password.getOrNull(it)?.digitToIntOrNull()?.toString().orEmpty(),
                            color = Color(0xFF1D2129),
                            fontSize = 20.sp,
                            fontFamily = D_DIN
                        )
                    }
                }
            }
        }

        AppButton(
            text = button,
            background = Color(0xFFFF5E8B),
            color = Color(0xFFFFFFFF),
            fontSize = 16.sp,
            modifier = Modifier
                .widthIn(min = 160.dp)
                .height(36.dp)
                .padding(horizontal = 16.dp),
            enabled = password.length == 4,
        ) {
            onConfirm(type, password)
        }
    }
}

@Preview
@Composable
private fun PreviewRoomPasswordContent() {
    RoomPasswordContent(0, "锁定房间后需要输入密码才能进入", "确定密码")
}
