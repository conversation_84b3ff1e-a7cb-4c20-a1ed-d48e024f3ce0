package com.qyqy.cupid.ui.live

import android.Manifest
import android.view.WindowManager
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.LifecycleStartEffect
import androidx.lifecycle.viewmodel.compose.LocalViewModelStoreOwner
import androidx.lifecycle.viewmodel.compose.viewModel
import com.bumptech.glide.integration.compose.placeholder
import com.overseas.common.utils.PermissionUtil
import com.qyqy.cupid.im.messages.SystemAnnouncementMessage
import com.qyqy.cupid.im.panel.gift.GiftPosition
import com.qyqy.cupid.im.panel.gift.GiftScene
import com.qyqy.cupid.im.panel.gift.GiftViewModel
import com.qyqy.cupid.model.CoinWinnerViewModel
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.theme.navigationPadding
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.CupidViewModel
import com.qyqy.cupid.ui.DestinationRoute
import com.qyqy.cupid.ui.GiftExtra
import com.qyqy.cupid.ui.GiftListener
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.dialog.CommonAlertDialog
import com.qyqy.cupid.ui.dialog.ContentAlertDialog
import com.qyqy.cupid.ui.dialog.DialogButton
import com.qyqy.cupid.ui.dialog.DialogQueue
import com.qyqy.cupid.ui.dialog.FirstRechargeRewardFloat
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.dialog.VoiceInfoManageDialog
import com.qyqy.cupid.ui.dialog.VoiceMoreSettingDialog
import com.qyqy.cupid.ui.dialog.rememberDialogQueue
import com.qyqy.cupid.ui.global.CupidRoomWishFloatWidget
import com.qyqy.cupid.ui.live.panels.MessageListDialog
import com.qyqy.cupid.ui.live.panels.RoomAdminMoreDialog
import com.qyqy.cupid.ui.live.panels.RoomGiftDialog
import com.qyqy.cupid.ui.live.panels.RoomMemberListPanelDialog
import com.qyqy.cupid.ui.live.panels.RoomUserPanelDialog
import com.qyqy.cupid.ui.live.panels.VoiceRoomShareContent
import com.qyqy.cupid.ui.live.panels.rememberRankPanelState
import com.qyqy.cupid.ui.live.redpacket.CpdRPOpenConditionDialog
import com.qyqy.cupid.ui.live.redpacket.CpdRedPackagePendantFloat
import com.qyqy.cupid.ui.live.redpacket.CpdRedPacketDetailDialog
import com.qyqy.cupid.ui.live.redpacket.EditRedPacketDialog
import com.qyqy.cupid.ui.live.settings.RoomModePanel
import com.qyqy.cupid.ui.profile.RechargeDialog
import com.qyqy.cupid.ui.setting.TYPE_AUDIOROOM
import com.qyqy.cupid.ui.share.ShareSource
import com.qyqy.cupid.utils.FlowEventBus.getOrNull
import com.qyqy.cupid.utils.eventbus.EventBusListener
import com.qyqy.cupid.widgets.CoinWinnerGameFloat
import com.qyqy.cupid.widgets.CoinWinnerPendingDialog
import com.qyqy.cupid.widgets.GamaPanelPage
import com.qyqy.cupid.widgets.PermissionLauncher
import com.qyqy.cupid.widgets.rememberPermissionLauncher
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.app
import com.qyqy.ucoo.asComponentActivity
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.component.WebFrameInfo
import com.qyqy.ucoo.compose.LaunchOnceEffect
import com.qyqy.ucoo.compose.isEditOnCompose
import com.qyqy.ucoo.compose.keepLastNonNullState
import com.qyqy.ucoo.compose.presentation.redpackage.RedPackageViewModel
import com.qyqy.ucoo.compose.presentation.room.EditRoomNoticeDialog
import com.qyqy.ucoo.compose.ui.AppearanceStatusBars
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.DirectionState
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.overScrollVertical
import com.qyqy.ucoo.compose.ui.rememberOverscrollFlingBehavior
import com.qyqy.ucoo.compose.ui.runWithLoading
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.im.bean.CPGift
import com.qyqy.ucoo.im.compat.ConversationType
import com.qyqy.ucoo.im.compat.MessageBundle
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.UCInstanceMessage
import com.qyqy.ucoo.im.compat.loadMsgListLayout
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.toastRes
import kotlinx.coroutines.launch
import java.util.UUID


@Composable
fun VoiceLiveChatRoomScreen() {

    val activity = LocalContext.current.asComponentActivity!!
    val mainViewModel = viewModel<CupidViewModel>(viewModelStoreOwner = activity)
    val voiceLiveHelper = mainViewModel.voiceLiveHelper

    val newValue = voiceLiveHelper.voiceLiveValue
    val keepVale = keepLastNonNullState(newState = newValue)

    val appNavController = LocalAppNavController.current

    val roomState = if (newValue == null) { // newValue本身是一个可观察对象, 但他的属性不是可观察的
        LaunchedEffect(key1 = Unit) {
            appNavController.popBackStack()
        }
        if (keepVale == null) {
            return
        }
        keepVale.currentVoiceLiveRoomAsState()
    } else {
        newValue.currentVoiceLiveRoomAsState().apply {
            if (value.collapsedFromUser) { // 收起后，要关掉此页面
                LaunchedEffect(key1 = Unit) {
                    appNavController.popBackStack()
                }
            }
        }
    }

    val room by roomState

    val launcher = rememberPermissionLauncher<Int> { tempIndex, _ ->
        if (tempIndex != null) {
            voiceLiveHelper.upMic(tempIndex)
        }
    }

    if (newValue != null) { // 不是退出动画期间

        BackHandler {
            voiceLiveHelper.collapseRoom()
        }

        LifecycleStartEffect(key1 = voiceLiveHelper) {
            voiceLiveHelper.refreshRoomInfo()
            if (room.selfInMic && !PermissionUtil.hasPermissions(Manifest.permission.RECORD_AUDIO)) {
                launcher.launch(arrayOf(Manifest.permission.RECORD_AUDIO))
            }

            activity.window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            onStopOrDispose {
                activity.window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            }
        }
    }

    when (room.settings.roomMode) {
        RoomMode.COUPLE_MIC_MODE -> {
            VoiceLiveChatRoomPage(
                launcher, voiceLiveHelper, roomState,
                safeContent = { _, dialogQueue, _ ->
                    if (!isEditOnCompose) {
                        // 首充挂件
                        FirstRechargeRewardFloat(
                            modifier = Modifier
                                .align(Alignment.BottomEnd)
                                .padding(bottom = 10.dp)
                        )
                    }
                },
            ) { _, _, onAction ->

                Spacer(modifier = Modifier.height(16.dp))

                GridMicSeatsLayout(
                    micInfo = room.micInfo,
                    modifier = Modifier
                        .padding(horizontal = 28.dp)
                        .fillMaxWidth(),
                    rows = 1,
                    maxWidth = 84.dp,
                    onClick = {
                        if (it is MicSeat.Empty) {
                            onAction.upMic(it.index)
                        } else {
                            onAction.showUserInfoPanel(it.user)
                        }
                    },
                )
            }
        }

        RoomMode.PK_MIC_MODE -> {
            VoiceLiveChatRoomPage(launcher, voiceLiveHelper, roomState) { viewModel, dialogQueue, onAction ->
                PKGameContent(viewModel, dialogQueue, onAction)
            }
        }

        else -> {
            VoiceLiveChatRoomPage(launcher, voiceLiveHelper, roomState)
        }
    }
}


@Composable
private fun VoiceLiveChatRoomPage(
    launcher: PermissionLauncher<Int>,
    voiceLiveHelper: VoiceLiveHelper,
    roomState: State<VoiceLiveChatRoom>,
    background: @Composable BoxScope.(VoiceLiveChatRoom, IVoiceLiveAction) -> Unit = { room, _ ->
        val backgroundUrl = room.settings.background
        if (backgroundUrl.isNullOrEmpty() || !room.settings.roomMode.applyRemoteUrl) {
            Spacer(
                modifier = Modifier
                    .fillMaxSize()
                    .paint(
                        painter = painterResource(id = room.settings.roomMode.backgroundResId),
                        contentScale = ContentScale.Crop
                    )
            )
        } else {
            ComposeImage(
                model = backgroundUrl,
                modifier = Modifier
                    .fillMaxSize(),
                loading = placeholder(room.settings.roomMode.backgroundResId),
            )
        }
    },
    topBar: @Composable ColumnScope.(VoiceLiveChatRoom, IVoiceLiveAction) -> Unit = { voiceLiveChatRoom, onAction ->
        VoiceRoomTitleBar(
            voiceLiveChatRoom = voiceLiveChatRoom,
            modifier = Modifier.fillMaxWidth(),
            onAction = onAction
        )
    },
    bottomBar: @Composable ColumnScope.(VoiceLiveViewModel, DialogQueue<IVoiceLiveAction>, IVoiceLiveAction) -> Unit = { viewModel, dialogQueue, onAction ->
        val buttons by createNormalVoiceRoomButtons(viewModel, onAction)
        VoiceRoomBottomBar(
            buttons = buttons,
            modifier = Modifier
                .navigationPadding(minimumPadding = 15.dp)
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        )
    },
    panelContent: @Composable BoxScope.(VoiceLiveChatRoom, IVoiceLiveAction) -> Unit = { _, _ -> },
    overlayContent: @Composable BoxScope.(VoiceLiveChatRoom, IVoiceLiveAction) -> Unit = { _, _ -> },
    safeContent: @Composable BoxScope.(VoiceLiveViewModel, DialogQueue<IVoiceLiveAction>, IVoiceLiveAction) -> Unit = { viewModel, dialogQueue, onAction ->
        if (!isEditOnCompose) {
            val room = roomState.value

            // 首充挂件
            FirstRechargeRewardFloat(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(bottom = 10.dp)
            )

            CupidRoomWishFloatWidget(
                roomId = room.roomId,
                dialogQueue = dialogQueue,
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(bottom = 10.dp)
            )

            //红包挂件
            CpdRedPackagePendantFloat(viewModel = viewModel, dialogQueue = dialogQueue, onAction = onAction)

            // 转盘游戏挂件
            CoinWinnerGameFloat(room.roomId, dialogQueue = dialogQueue)

            VoiceLiveRoomFloat(
                room = room,
                dialogQueue = dialogQueue,
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(bottom = 10.dp)
            )
        }
    },
    centerContent: @Composable ColumnScope.(VoiceLiveViewModel, DialogQueue<IVoiceLiveAction>, IVoiceLiveAction) -> Unit = { viewModel, dialogQueue, onAction ->
        val room = roomState.value

        val rankState = rememberRankPanelState(roomId = room.roomId)
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {
            Box(modifier = Modifier.align(Alignment.CenterStart)) {
                RoomNoticeButton(room = room.basicInfo, modifier = Modifier)
            }

            VoiceRoomRankButton(
                modifier = Modifier.align(Alignment.CenterEnd)
            ) {
                rankState.value = true
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        GridMicSeatsLayout(
            micInfo = room.micInfo,
            modifier = Modifier
                .padding(horizontal = 8.dp)
                .fillMaxWidth(),
            onClick = {
                if (it is MicSeat.Empty) {
                    onAction.upMic(it.index)
                } else {
                    onAction.showUserInfoPanel(it.user)
                }
            }
        )

        Spacer(modifier = Modifier.height(12.dp))

        RoomUserRowList(
            count = room.basicInfo.roomUserList.size,
            userList = room.audienceList,
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .fillMaxWidth()
                .height(48.dp)
                .background(color = Color(0x4D000000), shape = RoundedCornerShape(4.dp))
                .padding(horizontal = 12.dp),
            onMemberList = {
                dialogQueue.push(RoomMemberListPanelDialog(roomState))
            },
            userCallback = {
                onAction.showUserInfoPanel(it)
            }
        )
    },
) {

    val appNavController = LocalAppNavController.current

    val room = roomState.value

    val viewModelStoreOwner = LocalViewModelStoreOwner.current

    val scope = rememberCoroutineScope()

    val loadingPopup = LocalContentLoading.current

    val dialogQueue = rememberDialogQueue<IVoiceLiveAction>()

    ChatRoomEventHandler(voiceLiveHelper = voiceLiveHelper, dialogQueue)

    val giftViewModel = viewModel<GiftViewModel>(initializer = {
        GiftViewModel(room.roomId.toString(), ConversationType.CHATROOM, room.imId)
    })

    val viewModel = viewModel<VoiceLiveViewModel>(initializer = {
        VoiceLiveViewModel(roomState)
    })

    val onAction = object : IVoiceLiveAction {

        override fun onSendMessage(message: MessageBundle) {
            viewModel.sendMessage(message)
        }

        override fun onSendMultipleMessage(messages: List<MessageBundle>) {
            if (messages.isNotEmpty()) {
                viewModel.sendMessages(messages)
            } else {
                toastRes(R.string.消息发送失败)
            }
        }

        override fun onResendMessage(message: UCInstanceMessage) {
            viewModel.sendMessage(message.base)
        }

        override fun onNavigateTo(destination: DestinationRoute) {
            appNavController.navigate(destination)
        }

        override fun showMessagePanel() {
            dialogQueue.push(MessageListDialog)
        }

        override fun onShowGiftPanel(position: GiftPosition?) {
            giftViewModel.fetchGift()
            dialogQueue.push(RoomGiftDialog(position, giftViewModel.giftListModelState, roomState))
        }

        override fun onSendGiftTo(targets: List<String>, gift: CPGift, count: Int, extra: GiftExtra) {
            giftViewModel.sendGiftAt(if (room.isCpRoom) GiftScene.CP else GiftScene.ROOM, targets, gift, count, extra)
        }

        override fun onSendGideComboFinish() {
            giftViewModel.finishGiftCombo()
        }

        override fun onShowRechargePanel() {
            dialogQueue.push(RechargeDialog, true)
        }

        override fun upMic(index: Int) {
            launcher.launch(index, arrayOf(Manifest.permission.RECORD_AUDIO))
        }

        override fun downMic() {
            voiceLiveHelper.downMic()
        }

        override fun toggleMicrophone() {
            voiceLiveHelper.toggleMicrophone()
        }

        override fun collapseRoom() {
            voiceLiveHelper.collapseRoom()
        }

        override fun exitRoom() {
            voiceLiveHelper.exitCurrentRoom()
        }

        override fun showUserInfoPanel(user: User, giftPosition: GiftPosition?) {
            dialogQueue.push(
                RoomUserPanelDialog(
                    userState = mutableStateOf(user as AppUser),
                    currentRoomState = roomState,
                    giftListModelState = giftViewModel.giftListModelState,
                    giftPosition = giftPosition
                ), true
            )
        }

        override fun showShareRoomPanel() {
            dialogQueue.push { dialog, action ->
                VoiceRoomShareContent(dialog = dialog, onAction = action)
            }
        }

        override fun shareToFamily() {
            scope.launch {
                loadingPopup.value = true
                viewModel.shareToFamily()
                loadingPopup.value = false
            }
        }

        override fun shareToFriend() {
            appNavController.navigate(
                CupidRouters.SHARE_TO_FRIENDS,
                mapOf(
                    "sourceType" to ShareSource.SHARE_VOICE_ROOM,
                    "voiceRoomId" to room.roomId.toString()
                )
            )
        }

        //region 房间管理事件

        override fun showRoomMangePanel() {
            dialogQueue.push(VoiceMoreSettingDialog(room), true)
        }

        override fun showRoomInfoManagePanel() {
            dialogQueue.push(VoiceInfoManageDialog(room), true)
        }

        override fun showRoomModeManagePanel() {
            dialogQueue.push { dialog, _ ->
                RoomModePanel(room, { dialog.dismiss() }) {
                    dialog.dismiss()
                    scope.launch {
                        loadingPopup.runWithLoading {
                            viewModel.updateRoomMode(it.value)
                        }
                    }
                }
            }
        }

        override fun reportRoom() {
            appNavController.navigate(
                CupidRouters.REPORT_START, mapOf(
                    "type" to TYPE_AUDIOROOM,
                    "id" to room.roomId.toString()
                )
            )
        }

        override fun showBlackRoomDialog() {
            dialogQueue.push(direction = DirectionState.CENTER) { dialog: IDialog, action: IDialogAction? ->
                ContentAlertDialog(
                    modifier = Modifier.widthIn(220.dp, 270.dp),
                    content = stringResource(id = R.string.cpd_black_room_tip),
                    endButton = DialogButton(stringResource(id = R.string.cpd_confirm)) {
                        scope.launch {
                            loadingPopup.value = true
                            viewModel.blackRoom().onSuccess {
                                loadingPopup.value = false
                                dialog.dismiss()
                                exitRoom()
                            }.onFailure { loadingPopup.value = false }.toastError()
                        }
                    }, startButton = DialogButton(stringResource(id = R.string.cpd_cancel)) {
                        dialog.dismiss()
                    })
            }
        }

        override fun navigateToAdministrator() {
            appNavController.navigate(CupidRouters.VOICE_ADMIN_LIST, mapOf("id" to room.roomId))
        }

        override fun navigateToBlackList() {
            appNavController.navigate(CupidRouters.VOICE_BLACK_LIST, mapOf("id" to room.roomId))
        }

        override fun showLockRoomDialog() {
            dialogQueue.push(RoomSetLockPasswordDialog)
        }

        override fun toggleRoomLock(dialog: IDialog, setLock: Boolean, password: String?) {

            fun doWork() {
                scope.launch {
                    loadingPopup.value = true
                    viewModel.toggleRoomLock(setLock, password).onSuccess {
                        loadingPopup.value = false
                        dialog.dismiss()
                        toastRes(R.string.cpd_setpwd_succeed)
                    }.onFailure {
                        loadingPopup.value = false
                    }.toastError()
                }
            }

            if (setLock) {
                doWork()
            } else {
                dialogQueue.push(CommonAlertDialog(app.getString(R.string.确定要取消锁定当前房间吗)) {
                    doWork()
                }, true)
            }
        }

        override fun onRoomNameUpdate(newName: String, dialog: IDialog) {
            scope.launch {
                loadingPopup.value = true
                val response = viewModel.updateRoomName(newName)
                loadingPopup.value = false
                response.onSuccess {
                    dialog.dismiss()
                }.toastError()
            }
        }

        override fun onEditNotice() {
            dialogQueue.push(EditRoomNoticeDialog(room.roomId, room.basicInfo.notice), true)
        }

        //endregion

        override fun atUser(user: User) {
            viewModel.setInputTextState(InputTextState.Visible("@${user.nickname} "))
        }

        override fun setInputTextState(inputState: InputTextState) {
            viewModel.setInputTextState(inputState)
        }

        override fun onClickNormalRoomButton(button: RoomButton.Normal) {
            when (button.tag) {
                "红包" -> {
                    val rpViewModel = viewModelStoreOwner?.getOrNull<RedPackageViewModel>() ?: return
                    dialogQueue.push(EditRedPacketDialog(rpViewModel) {
                        dialogQueue.push(CpdRPOpenConditionDialog(rpViewModel), true)
                    })
                }

                "游戏" -> {
                    dialogQueue.push { dialog, _ ->
                        val nav = LocalAppNavController.current
                        GamaPanelPage(room, {
                            dialog.dismiss()
                            onSendMessage(it)
                        }, { url, container ->
                            dialog.dismiss()
                            if (container.heightRatio <= 0) {
                                onNavigateToWeb(url)
                            } else {
                                dialogQueue.push(nav.webDialog(WebFrameInfo(targetUrl = url, height = 620, gravity = "bottom")))
                            }
                        }) {
                            when (it) {
                                //金币大赢家
                                1006 -> {
                                    val coinWinnerViewModel =
                                        viewModelStoreOwner?.getOrNull<CoinWinnerViewModel>() ?: return@GamaPanelPage
                                    if (coinWinnerViewModel.gameDetail.value != null) {
                                        coinWinnerViewModel.isGameShowing = true
                                    } else {
                                        dialogQueue.push(CoinWinnerPendingDialog)
                                    }
                                }

                                else -> {}
                            }
                        }
                    }
                }

                "上麦" -> {
                    upMic()
                }

                "下麦" -> {
                    downMic()
                }

                "麦克风切换" -> {
                    toggleMicrophone()
                }

                "礼物" -> {
                    onShowGiftPanel()
                }
            }
        }

        override fun inviteUpMic(user: User) {
            voiceLiveHelper.inviteUpMic(user.userId)
        }

        override fun kickDownMic(user: User) {
            voiceLiveHelper.kickDownMic(user.userId)
        }

        override fun showManageUserMenu(userState: MutableState<AppUser>) {
            dialogQueue.push(RoomAdminMoreDialog(userState, roomState))
        }

        override fun followUser(userState: MutableState<AppUser>, isFollow: Boolean) {
            scope.launch {
                val ret = voiceLiveHelper.updateFriendShip(userState.value.id, isFollow)
                if (ret) {
                    userState.value = userState.value.copy(followed = isFollow)
                }
            }
        }

        override fun blackUserInRoom(userState: MutableState<AppUser>, isBlack: Boolean) {
            scope.launch {
                val ret = voiceLiveHelper.updateUserBlackState(userState.value.userId, isBlack)
                if (ret) {
                    userState.value = userState.value.copy(inRoomBlackList = isBlack)
                }
            }
        }

        override fun updateRoomAdmin(userId: Int, isAdmin: Boolean) {
            voiceLiveHelper.updateRoomAdmin(userId, isAdmin)
        }

        override fun gotoGiftWallPage(userId: Int) {
            appNavController.navigate(CupidRouters.GIFT_WALL, mapOf("userId" to userId))
        }

        override fun gotoFamilyDetail(familyId: Int) {
            appNavController.navigate(CupidRouters.FAMILY_DETAIL, mapOf("family_id" to familyId.toString()))
        }

        override fun onMessageClicked(message: UCInstanceMessage) {
            if (message is UCCustomMessage) {
                when (message.cmd) {
                    MsgEventCmd.RED_PACKET_CREATED -> {
                        val rpViewModel = viewModelStoreOwner?.getOrNull<RedPackageViewModel>() ?: return
                        val redPacketId = message.getJsonInt("red_packet_id", 0)
                        dialogQueue.pushWithKey(CpdRedPacketDetailDialog(redPacketId, rpViewModel),"redpacket-${UUID.randomUUID()}")
                    }

                    else -> {}
                }
            }
        }

        override fun onCoinWinnerCreate(base_coin: Int) {
            val coinWinnerViewModel = viewModelStoreOwner?.getOrNull<CoinWinnerViewModel>() ?: return
            coinWinnerViewModel.createGame(base_coin)
        }

        override fun onClickCoinPKItem(roundID: Int) {
            val coinWinnerViewModel = viewModelStoreOwner?.getOrNull<CoinWinnerViewModel>() ?: return
            coinWinnerViewModel.clickJoinFromMessage(roundID)
        }

        override fun shouldLuckyDataChange(should: Boolean) {
            if (should) {
                giftViewModel.startRefreshComboGiftInfo()
            } else {
                giftViewModel.stopRefreshComboGift()
            }
        }
    }

    dialogQueue.DialogContent(onAction)

    val updateOnAction by rememberUpdatedState(newValue = onAction)

    val listener = remember {
        object : GiftListener() {
            override fun showGiftPanel(giftPosition: GiftPosition?) {
                updateOnAction.onShowGiftPanel(giftPosition)
            }
        }
    }

    EventBusListener(listener = listener)

    AppearanceStatusBars(isLight = false)

    LaunchOnceEffect {
        if (room.basicInfo.showAudienceTaskPopup) {
//            isTodayHasRan("showAudienceTaskPopup") {
            dialogQueue.push(
                appNavController.webDialog(
                    WebFrameInfo(
                        targetUrl = "${Const.Url.baseURL}/h5/japan/room_task?room_id=${room.roomId}",
                        height = 620,
                        gravity = "bottom"
                    )
                )
            )
//                return@isTodayHasRan true
//            }
        }
    }

    Page(
        dialogQueue = dialogQueue,
        viewModel = viewModel,
        giftViewModel = giftViewModel,
        onAction = onAction,
        background = background,
        topBar = topBar,
        bottomBar = bottomBar,
        panelContent = panelContent,
        safeContent = safeContent,
        overlayContent = overlayContent,
        centerContent = centerContent,
    )
}

@Composable
private fun Page(
    dialogQueue: DialogQueue<IVoiceLiveAction>,
    viewModel: VoiceLiveViewModel,
    giftViewModel: GiftViewModel,
    onAction: IVoiceLiveAction,
    background: @Composable BoxScope.(VoiceLiveChatRoom, IVoiceLiveAction) -> Unit = { room, _ ->
        val backgroundUrl = room.settings.background
        if (backgroundUrl.isNullOrEmpty() || !room.settings.roomMode.applyRemoteUrl) {
            Spacer(
                modifier = Modifier
                    .fillMaxSize()
                    .paint(
                        painter = painterResource(id = room.settings.roomMode.backgroundResId),
                        contentScale = ContentScale.Crop
                    )
            )
        } else {
            ComposeImage(
                model = backgroundUrl,
                modifier = Modifier
                    .fillMaxSize(),
                loading = placeholder(room.settings.roomMode.backgroundResId),
            )
        }
    },
    topBar: @Composable ColumnScope.(VoiceLiveChatRoom, IVoiceLiveAction) -> Unit = { voiceLiveChatRoom, _ ->
        VoiceRoomTitleBar(
            voiceLiveChatRoom = voiceLiveChatRoom,
            modifier = Modifier.fillMaxWidth(),
            onAction = onAction
        )
    },
    bottomBar: @Composable ColumnScope.(VoiceLiveViewModel, DialogQueue<IVoiceLiveAction>, IVoiceLiveAction) -> Unit = { _, _, _ ->
        val buttons by createNormalVoiceRoomButtons(viewModel, onAction)
        VoiceRoomBottomBar(
            buttons = buttons,
            modifier = Modifier
                .navigationPadding(minimumPadding = 15.dp)
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        )
    },
    panelContent: @Composable BoxScope.(VoiceLiveChatRoom, IVoiceLiveAction) -> Unit = { _, _ -> },
    overlayContent: @Composable BoxScope.(VoiceLiveChatRoom, IVoiceLiveAction) -> Unit = { _, _ -> },
    safeContent: @Composable BoxScope.(VoiceLiveViewModel, DialogQueue<IVoiceLiveAction>, IVoiceLiveAction) -> Unit = { _, _, _ ->
        if (!isEditOnCompose) {
            val room = viewModel.roomState.value


            // 首充挂件
            FirstRechargeRewardFloat(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(bottom = 20.dp)
            )

            //红包挂件
            CpdRedPackagePendantFloat(viewModel = viewModel, dialogQueue = dialogQueue, onAction = onAction)

            // 转盘游戏挂件
            CoinWinnerGameFloat(room.roomId, dialogQueue = dialogQueue)

            VoiceLiveRoomFloat(
                room = room,
                dialogQueue = dialogQueue,
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(bottom = 10.dp)
            )
        }
    },
    centerContent: @Composable ColumnScope.(VoiceLiveViewModel, DialogQueue<IVoiceLiveAction>, IVoiceLiveAction) -> Unit = { _, _, _ ->
        val room = viewModel.roomState.value
        val rankState = rememberRankPanelState(roomId = room.roomId)
        VoiceRoomRankButton(
            modifier = Modifier
                .align(Alignment.End)
                .padding(end = 16.dp)
        ) {
            rankState.value = true
        }

        Spacer(modifier = Modifier.height(16.dp))

        GridMicSeatsLayout(
            micInfo = room.micInfo,
            modifier = Modifier
                .padding(horizontal = 8.dp)
                .fillMaxWidth(),
            onClick = {
                if (it is MicSeat.Empty) {
                    onAction.upMic(it.index)
                } else {
                    onAction.showUserInfoPanel(it.user)
                }
            }
        )

        Spacer(modifier = Modifier.height(12.dp))

        RoomUserRowList(
            count = room.basicInfo.roomUserList.size,
            userList = room.audienceList,
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .fillMaxWidth()
                .height(48.dp)
                .background(color = Color(0x4D000000), shape = RoundedCornerShape(4.dp))
                .padding(horizontal = 12.dp),
            onMemberList = {
                dialogQueue.push(RoomMemberListPanelDialog(viewModel.roomState))
            },
            userCallback = {
                onAction.showUserInfoPanel(it)
            }
        )
    },
) {

    val roomState by viewModel.roomState
    LiveChatRoomScaffold(
        modifier = Modifier.fillMaxSize(),
        background = {
            background(roomState, onAction)
        },
        topBar = {
            topBar(roomState, onAction)
        },
        bottomBar = {
            bottomBar(viewModel, dialogQueue, onAction)
        },
        panelContent = {
            panelContent(roomState, onAction)
        },
        overlayContent = {
            overlayContent(roomState, onAction)

            giftViewModel.GiftEffectView()

            VoiceRoomInput(
                inputState = viewModel.inputTextState.value,
                onInputStateChange = {
                    onAction.setInputTextState(it)
                },
                modifier = Modifier
                    .imePadding()
                    .fillMaxWidth()
                    .background(Color.Black)
            ) {
                onAction.onSendMessage(it)
            }
        },
        safeContent = {
            safeContent(viewModel, dialogQueue, onAction)
        },
    ) {

        centerContent(viewModel, dialogQueue, onAction)

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        ) {
            val isLoading by remember {
                derivedStateOf {
                    viewModel.paginateState.nextLoadState.isLoading
                }
            }

            val isEnd by remember {
                derivedStateOf {
                    viewModel.paginateState.nextLoadState.isEnd
                }
            }

            val listState = rememberLazyListState()

            viewModel.bindListState(listState)

            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .overScrollVertical(),
                state = listState,
                contentPadding = PaddingValues(vertical = 16.dp, horizontal = 16.dp),
                reverseLayout = true,
                verticalArrangement = Arrangement.spacedBy(8.dp),
                flingBehavior = rememberOverscrollFlingBehavior { listState }
            ) {
                loadMsgListLayout("voicechat", viewModel.messageList, onAction) {
                    VoiceLiveViewModel.getMsgLayoutContent(it, true)
                }

                if (isLoading) {
                    item(key = "isLoading", contentType = "isLoading") {
                        Box(
                            modifier = Modifier
                                .padding(top = 10.dp)
                                .fillMaxWidth(),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                color = Color(0xFFFF5E8B),
                                strokeWidth = 1.5.dp
                            )
                        }
                    }
                }

                if (isEnd) {
                    item(key = "room_system_announcement", contentType = "room_system_announcement") {
                        SystemAnnouncementMessage()
                    }
                }
            }
        }
    }
}


@Preview
@Composable
fun PreviewVoiceLiveChatRoomPage() {
    PreviewCupidTheme {
        Page(
            dialogQueue = remember { DialogQueue() },
            viewModel = VoiceLiveViewModel(remember { mutableStateOf(VoiceLiveChatRoom.preview) }),
            giftViewModel = GiftViewModel(
                VoiceLiveChatRoom.preview.roomId.toString(),
                ConversationType.CHATROOM,
                VoiceLiveChatRoom.preview.imId
            ),
            onAction = IVoiceLiveAction.Empty,
        )
    }
}