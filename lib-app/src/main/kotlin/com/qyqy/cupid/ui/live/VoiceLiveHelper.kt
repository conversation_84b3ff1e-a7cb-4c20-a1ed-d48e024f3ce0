package com.qyqy.cupid.ui.live

import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.util.fastDistinctBy
import com.overseas.common.utils.ResultContinuation
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.CupidViewModel
import com.qyqy.cupid.ui.FloatStatus
import com.qyqy.cupid.ui.IMutexFeature
import com.qyqy.cupid.ui.MutexFeature
import com.qyqy.cupid.utils.launchSingTask
import com.qyqy.ucoo.DataPoint
import com.qyqy.ucoo.R
import com.qyqy.ucoo.VoiceLiveService
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.app
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.ui.DirectionState
import com.qyqy.ucoo.compose.ui.IconLoading
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.http.getBoolOrNull
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.thenApi
import com.qyqy.ucoo.im.bean.Room
import com.qyqy.ucoo.im.bean.RtcToken
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.IMCompatListener
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.user.UserManager
import com.qyqy.ucoo.utils.rtc.AppRtcManager
import com.qyqy.ucoo.utils.rtc.Mic
import com.qyqy.ucoo.utils.rtc.engine.TencentRtcFactory
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

class VoiceLiveHelper(
    private val viewModelScope: CoroutineScope, private val viewModel: CupidViewModel,
) : IMutexFeature {

    companion object {
        var joinActionArguments: Map<String, Any>? = null
    }

    val roomRepository get() = UserManager.roomRepository

    val userRepository = UserManager.userRepository

    private val _voiceLiveState: MutableState<VoiceLiveState?> = mutableStateOf(null)

    val voiceLiveValue by _voiceLiveState

    private val msgListener = object : IMCompatListener {

        override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
            handleMessageEvent(message)
        }
    }

    private fun handleMessageEvent(message: UCCustomMessage) {
        if (message.isC2CMsg) {
            return
        }
        if (voiceLiveValue?.currentRoom?.imInfo?.imId != message.targetId) {
            return
        }
        val cmd = message.cmd
        when (cmd) {
            MsgEventCmd.USER_EXIT -> { // 离开房间
                val user = message.getJsonValue<AppUser>("user") ?: return
                if (user.isSelf && message.getJsonBoolean("is_forced", false)) {
                    exitCurrentRoom(app.getString(R.string.cpd您已被踢出房间))
                }
            }

            MsgEventCmd.BLACK_USER -> {
                val user = message.getJsonValue<AppUser>("user")
                if (user?.isSelf == true) {
                    exitCurrentRoom(app.getString(R.string.cpd您已被房间拉黑))
                }
            }
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun start() {
        IMCompatCore.addIMListener(msgListener)
        viewModelScope.launch {
            snapshotFlow {
                voiceLiveValue
            }.flatMapLatest { state ->
                state?.roomFlow?.map { it.collapsed } ?: flowOf(null)
            }.distinctUntilChanged().filter {
                it == false
            }.collect {
                if (!viewModel.appNavController.popBackStack(CupidRouters.VOICE_LIVE_ROOM)) {
                    viewModel.appNavController.launchSingTask(CupidRouters.VOICE_LIVE_ROOM, joinActionArguments ?: emptyMap())
                    joinActionArguments = null
                }
            }
        }

        snapshotFlow {
            voiceLiveValue
        }.map { it != null }
            .distinctUntilChanged()
            .onEach {
                if (it) {
                    VoiceLiveService.start(app)
                } else {
                    VoiceLiveService.stop(app)
                }
            }.launchIn(viewModelScope)
    }

    override fun clear() {
        IMCompatCore.removeIMListener(msgListener)
        exitCurrentRoom()
    }

    override fun getCurrentMutexFeature(): MutexFeature? {
        if (voiceLiveValue == null) {
            return null
        }
        return MutexFeature.Live
    }

    fun collapseRoom(adaptive: Boolean = false) {
        voiceLiveValue?.updateVoiceRoomInfo {
            if (adaptive && it.collapsed) {
                it
            } else {
                it.copy(
                    floatStatus = FloatStatus.Collapsed(adaptive),
                    extraInfo = it.extraInfo.copy(hasCollapsedRecord = true)
                )
            }
        }
    }

    fun expandRoom() {
        voiceLiveValue?.updateVoiceRoomInfo {
            it.copy(floatStatus = FloatStatus.Expanded)
        }
    }

    fun exitCurrentRoom(toastText: String? = null) {
        updateVoiceLiveState { state ->
            state?.clear()
            if (toastText != null) {
                toast(toastText)
            }
            null
        }
    }

    fun joinVoiceLiveRoom(roomId: Int, forceExpanded: Boolean = true, isPrivate: Boolean = false, from: String? = null): Boolean {
        if (!from.isNullOrEmpty()) {
            Analytics.appReportEvent(DataPoint.clickBody(from))
        }
        val current = voiceLiveValue
        if (current?.roomId == roomId) {
            if (current.collapsed) {
                if (forceExpanded) { // 如果当前是收起的，强制展开
                    current.updateVoiceRoomInfo {
                        it.copy(floatStatus = FloatStatus.Expanded)
                    }
                }
            } else {
                refreshRoomInfo()
            }
            if (!from.isNullOrEmpty()) {
                Analytics.appReportEvent(DataPoint.eventBody("${from}_and_enter"))
            }
            return true
        }
        val message = viewModel.checkFeature(MutexFeature.Live)
        if (message != null) {
            toast(message)
            return false
        }

        viewModel.dialogQueue.push(immediatelyShow = true, direction = DirectionState.CENTER) { dialog, onAction ->
            IconLoading()
            LaunchedEffect(key1 = Unit) {
                try {
                    roomRepository.run {
                        if (isPrivate) {
                            runApiCatching {
                                roomApi.getRtcTokenForPrivateRoom(buildMap {
                                    put("room_id", roomId.toString())
                                    put("only_get_token", "false")
                                    put("with_confirm", "false")
                                })
                            }.fold({ rtcToken ->
                                runApiCatching {
                                    roomApi.queryPrivateRoom(rtcToken.roomId)
                                }.mapCatching {
                                    it.toRoom(rtcToken.chatroomId) to rtcToken
                                }
                            }) {
                                Result.failure(it)
                            }
                        } else {
                            runApiCatching {
                                roomApi.requiresPasswordForRoomEntry(roomId)
                            }.fold({
                                if (it.getBoolOrNull("need_secret") == true) {
                                    val validateResult = ResultContinuation<String>()
                                    viewModel.dialogQueue.push(RoomValidatePasswordDialog(roomId, validateResult), immediatelyShow = true)
                                    val secret = validateResult.suspendUntil()
                                    if (secret.isNotBlank()) {
                                        runApiCatching {
                                            roomApi.validateRoomEntryPassword(mapOf("room_id" to roomId, "secret" to secret))
                                        }
                                    } else {
                                        throw CancellationException("")
                                    }
                                } else {
                                    Result.success(true)
                                }
                            }, {
                                Result.failure(it)
                            }).thenApi {
                                roomApi.getRtcToken(mapOf("room_id" to roomId))
                            }.fold({ rtcToken ->
                                runApiCatching {
                                    roomApi.getChatRoomInfo(rtcToken.roomId)
                                }.map {
                                    it to rtcToken
                                }
                            }, {
                                Result.failure(it)
                            })
                        }
                    }.onSuccess { ret ->
                        val (room, rtcToken) = ret
                        updateVoiceLiveState { state ->
                            val voiceLiveChatRoom = if (state?.roomId == room.roomId) {
                                room.toVoiceLiveChatRoom(
                                    oldRoom = null,
                                    rtcToken = rtcToken,
                                    floatStatus = if (state.collapsed && forceExpanded) {
                                        FloatStatus.Expanded
                                    } else {
                                        state.floatStatus
                                    }
                                )
                            } else {
                                room.toVoiceLiveChatRoom(null, rtcToken, FloatStatus.Expanded)
                            }

                            if (voiceLiveChatRoom == null) {
                                toastRes(R.string.cpd当前不支持此类型房间_请升级APP)
                                return@LaunchedEffect
                            }
                            if (!from.isNullOrEmpty()) {
                                Analytics.appReportEvent(DataPoint.eventBody("${from}_and_enter"))
                            }
                            if (state?.roomId == room.roomId) {
                                state.updateVoiceRoomInfo {
                                    voiceLiveChatRoom
                                }
                                state
                            } else {
                                state?.clear()
                                createVoiceLiveState(voiceLiveChatRoom, rtcToken).also {
                                    if (isPrivate && !it.currentRoom.selfInMic) {
                                        it.upMic(-1)
                                    }
                                }
                            }
                        }
                    }.toastError()
                } finally {
                    dialog.dismiss()
                }
            }
        }

        return false
    }

    fun refreshRoomInfo() {
        voiceLiveValue?.apply {
            if (!currentRoom.isCpRoom) {
                launch {
                    roomRepository.getAdminList(roomId, 0).onSuccess { list ->
                        updateVoiceRoomInfo {
                            it.copy(basicInfo = it.basicInfo.copy(admins = list.map { item -> item.id }))
                        }
                    }
                }

                launch {
                    runApiCatching {
                        roomRepository.roomApi.getChatRoomInfo(roomId)
                    }.onSuccess { room ->
                        try {
                            updateVoiceRoomInfo {
                                room.toVoiceLiveChatRoom(
                                    oldRoom = it,
                                    floatStatus = it.floatStatus,
                                    providerRtcMic = ::getMicById
                                ) ?: throw IllegalStateException()
                            }
                        } catch (e: IllegalStateException) {
                            toastRes(R.string.cpd当前不支持此类型房间_请升级APP)
                            exitCurrentRoom()
                        }
                    }
                }
            } else {
                launch {
                    runApiCatching {
                        roomRepository.roomApi.queryPrivateRoom(roomId)
                    }.onSuccess { room ->
                        try {
                            updateVoiceRoomInfo {
                                room.toRoom(imId).toVoiceLiveChatRoom(
                                    oldRoom = it,
                                    floatStatus = it.floatStatus,
                                    providerRtcMic = ::getMicById
                                ) ?: throw IllegalStateException()
                            }
                        } catch (e: IllegalStateException) {
                            toastRes(R.string.cpd当前不支持此类型房间_请升级APP)
                            exitCurrentRoom()
                        }
                    }
                }
            }
        }
    }

    fun upMic(index: Int) {
        voiceLiveValue?.upMic(index)
    }

    fun downMic() {
        voiceLiveValue?.downMic()
    }

    fun toggleMicrophone() {
        voiceLiveValue?.toggleMicrophone()
    }

    fun inviteUpMic(userId: Int) {
        voiceLiveValue?.also {
            it.launch {
                roomRepository.inviteUpMic(it.roomId, userId).toastError()
            }
        }
    }

    fun acceptUpMic() {
        voiceLiveValue?.also {
            it.launch {
                roomRepository.acceptUpMic(it.roomId).toastError()
            }
        }
    }

    fun kickDownMic(userId: Int) {
        voiceLiveValue?.also {
            it.launch {
                roomRepository.kickDownMic(it.roomId, userId).toastError()
            }
        }
    }


    fun updateRoomAdmin(userId: Int, state: Boolean) {
        val roomId = voiceLiveValue?.roomId ?: return
        viewModelScope.launch {
            val ret = roomRepository.updateRoomAdmin(roomId, userId, state).toastError().isSuccess
            if (!ret) return@launch
            voiceLiveValue?.updateVoiceRoomInfo {
                val list = it.basicInfo.admins
                if (state) {
                    it.copy(basicInfo = it.basicInfo.copy(admins = buildList {
                        addAll(list)
                        add(userId.toString())
                    }.distinct()))
                } else {
                    it.copy(basicInfo = it.basicInfo.copy(admins = list.filter { item ->
                        item != userId.toString()
                    }))
                }
            }
        }
    }

    suspend fun updateUserBlackState(userId: Int, state: Boolean): Boolean {
        val roomId = voiceLiveValue?.roomId ?: return false
        return viewModelScope.async {
            return@async roomRepository.updateRoomBlackList(roomId, userId, state).toastError().isSuccess
        }.await()
    }

    suspend fun updateFriendShip(userId: String, state: Boolean): Boolean {
        return viewModelScope.async {
            val ret = userRepository.updateFriendShip(userId, state).toastError().isSuccess
            if (!ret) return@async false
            voiceLiveValue?.updateVoiceRoomInfo {
                if (it.basicInfo.isRoomOwner(userId)) {
                    it.copy(basicInfo = it.basicInfo.copy(owner = it.basicInfo.owner.copy(followed = state)))
                } else {
                    it
                }
            }
            true
        }.await()
    }

    private inline fun updateVoiceLiveState(function: (VoiceLiveState?) -> VoiceLiveState?) {
        val prevValue = _voiceLiveState.value
        val nextValue = function(prevValue)
        _voiceLiveState.value = nextValue
    }

    private fun Room.toVoiceLiveChatRoom(
        oldRoom: VoiceLiveChatRoom?,
        rtcToken: RtcToken? = null,
        floatStatus: FloatStatus = FloatStatus.Expanded,
        providerRtcMic: (Int) -> Mic? = { null },
    ): VoiceLiveChatRoom? {

        val roomMode = RoomMode.entries.find { it.value == roomMode }

        if (roomMode == null) {
            return null
        }

        val upMicMode = UpMicMode.entries.find { it.value == upMicMode }

        if (upMicMode == null) {
            return null
        }

        val micList: List<MicSeat> = roomMode.mapMicList(seats, providerRtcMic)

        val basicRoomInfo = BasicRoomInfo(
            id = roomId,
            publishId = publicId,
            title = roomName,
            notice = notice,
            owner = owner,
            admins = oldRoom?.basicInfo?.admins ?: admins?.map { it.id }.orEmpty(),
            roomUserList = buildList {
                add(owner)
                micList.forEach {
                    if (it.hasUser) {
                        add(it.user)
                    }
                }
                addAll(audiences)
                add(sUser as AppUser) // 把自己先加进去
            }.fastDistinctBy { it.id },
            showAudienceTaskPopup = showAudienceTaskPopup,
            audioRoomAudienceBean = audienceData
        )

        val settings = Settings(
            background = roomBackground,
            locked = roomLocked,
            showGiftEffect = true,
            roomMode = roomMode,
            upMicMode = upMicMode,
            showRedPacket = roomMode != RoomMode.COUPLE_MIC_MODE && UIConfig.userConf.showRedContent,
        )

        val micInfo = MicInfo(
            rtcId = roomId.toString(),
            rtcToken = rtcToken,
            micList = micList,
        )

        val imInfo = IMInfo(imId = rongCloudId)

        return VoiceLiveChatRoom(
            basicInfo = basicRoomInfo,
            micInfo = micInfo,
            imInfo = imInfo,
            settings = settings,
            floatStatus = floatStatus,
            extraInfo = ExtraInfo(
                micTask = tribeMicTask?.toMicTask(oldRoom?.extraInfo?.micTask),
                cpRoomUser = if (roomMode != RoomMode.COUPLE_MIC_MODE) null else privateRoomExtra?.privateChatUser,
                pkState = if (roomMode == RoomMode.PK_MIC_MODE) pkInfo.toPkState() else PkState.Idea
            )
        )
    }

    private fun createVoiceLiveState(voiceLiveChatRoom: VoiceLiveChatRoom, rtcToken: RtcToken): VoiceLiveState {
        return VoiceLiveState(
            voiceLiveChatRoom = voiceLiveChatRoom, helper = this, roomRepository = roomRepository, appRtcManager = AppRtcManager(
                voiceLiveChatRoom.micInfo.rtcId,
                { roomRepository.getRoomRtcToken(voiceLiveChatRoom).getOrNull() },
                false,
                TencentRtcFactory(jsonConfig = rtcToken.rtcConfig),
                false
            )
        )
    }


}