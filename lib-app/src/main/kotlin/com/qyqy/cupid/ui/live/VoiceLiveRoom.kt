package com.qyqy.cupid.ui.live

import android.os.SystemClock
import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.overseas.common.utils.isPreviewOnCompose
import com.qyqy.cupid.ui.FloatStatus
import com.qyqy.cupid.widgets.AvatarComposeView
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.isInvalid
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.app
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.orDefault
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.im.bean.MIC_MODE_FREE
import com.qyqy.ucoo.im.bean.PkInfo
import com.qyqy.ucoo.im.bean.PrivateRoomUser
import com.qyqy.ucoo.im.bean.ROOM_MODE_8_MIC
import com.qyqy.ucoo.im.bean.ROOM_MODE_PK
import com.qyqy.ucoo.im.bean.ROOM_MODE_PRIVATE
import com.qyqy.ucoo.im.bean.RtcToken
import com.qyqy.ucoo.im.bean.Seat
import com.qyqy.ucoo.im.bean.TribeMicTask
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.utils.rtc.Mic
import kotlinx.serialization.json.JsonObject

data class BasicRoomInfo(
    val id: Int,
    val publishId: String,
    val title: String,
    val notice: String,
    val owner: AppUser,
    val admins: List<String>,
    val roomUserList: List<AppUser>,
    val showAudienceTaskPopup: Boolean = false,
    val audioRoomAudienceBean: JsonObject? = null,
) {
    companion object {

        val preview
            get() = BasicRoomInfo(
                id = 0,
                publishId = "10086",
                title = "我在这里等你哦",
                notice = "这是公告",
                owner = userForPreview,
                admins = listOf("0", "1", "2"),
                roomUserList = List(5) {
                    userForPreview.apply {
                        this.id = "$it"
                    }
                },
                false
            )
    }

    val isOwner: Boolean
        get() = if (isPreviewOnCompose) {
            true
        } else {
            isRoomOwner(sUser.id)
        }

    val isAdmin: Boolean
        get() = if (isPreviewOnCompose) {
            true
        } else {
            isRoomAdmin(sUser.id)
        }

    val isFollowedOwner: Boolean
        get() = isOwner || owner.followed

    fun isRoomAdmin(userId: String) = admins.contains(userId)

    fun isRoomOwner(userId: String) = owner.id == userId

}


sealed interface MicSeat {

    companion object {

        @Composable
        fun MicSeat.BasicSeatContent(
            modifier: Modifier = Modifier,
            maxWidth: Dp = Dp.Unspecified,
            verticalArrangement: Arrangement.Vertical = Arrangement.Top,
            horizontalAlignment: Alignment.Horizontal = Alignment.CenterHorizontally,
            onClick: (MicSeat) -> Unit,
            content: @Composable ColumnScope.() -> Unit = {},
        ) {
            Column(
                verticalArrangement = verticalArrangement, horizontalAlignment = horizontalAlignment
            ) {

                if (user.isInvalid()) {
                    Box(
                        modifier = Modifier
                            .widthIn(max = maxWidth)
                            .fillMaxWidth()
                            .aspectRatio(1f)
                            .noEffectClickable(onClick = {
                                onClick(this@BasicSeatContent)
                            }), contentAlignment = Alignment.Center
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.cupid_room_mic_add),
                            contentDescription = null,
                            modifier = Modifier.fillMaxSize(0.7f)
                        )
                    }
                } else {
                    AvatarComposeView(
                        user = user,
                        modifier = Modifier
                            .widthIn(max = maxWidth)
                            .fillMaxWidth(),
                        fillAvatarIfFrameIsEmpty = false,
                        onClick = {
                            onClick(this@BasicSeatContent)
                        },
                    ) {
                        if (isSpeaking) {
                            val brush = if (user.isBoy) {
                                Brush.verticalGradient(listOf(Color(0x005AB0FF), Color(0xFF5AB0FF)))
                            } else {
                                Brush.verticalGradient(listOf(Color(0x00FF5E8B), Color(0xFFFF5E8B)))
                            }

                            Box(
                                modifier = Modifier
                                    .align(Alignment.BottomCenter)
                                    .fillMaxWidth()
                                    .height(22.dp)
                                    .background(brush),
                                contentAlignment = Alignment.Center
                            ) {
                                VoiceWaves(count = 8, modifier = Modifier.size(33.5.dp, 8.dp), minHeight = 3.5.dp)
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(2.dp))

                Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
                    AppText(
                        text = user.nickname, color = if (user.isInvalid()) {
                            Color.White.copy(alpha = 0.6f)
                        } else {
                            Color.White
                        }, fontSize = 12.sp, maxLines = 1, overflow = TextOverflow.Ellipsis
                    )

                    content()
                }
            }
        }
    }

    val user: AppUser

    val muted: Boolean

    val isSpeaking: Boolean

    val roomMode: RoomMode

    val hasUser: Boolean
        get() = this !is Empty

    @Composable
    fun SeatContent(maxWidth: Dp, onClick: (MicSeat) -> Unit)

    data class Empty(val index: Int, val name: String, override val roomMode: RoomMode) : MicSeat {

        companion object {
            val preview get() = Empty(index = 0, name = "点击加入", roomMode = RoomMode.EIGHT_MIC_MODE)
        }

        override val user = AppUser(id = "-1", nickname = name)

        override val muted: Boolean
            get() = true

        override val isSpeaking: Boolean
            get() = false

        @Composable
        override fun SeatContent(maxWidth: Dp, onClick: (MicSeat) -> Unit) {
            BasicSeatContent(modifier = Modifier.heightIn(min = 30.dp), maxWidth = maxWidth, onClick = onClick)
        }
    }

    data class User(
        override val user: AppUser,
        override val muted: Boolean,
        override val isSpeaking: Boolean,
        override val roomMode: RoomMode,
    ) : MicSeat {

        companion object {

            val preview get() = User(user = userForPreview, muted = false, isSpeaking = false, roomMode = RoomMode.EIGHT_MIC_MODE)
        }


        @Composable
        override fun SeatContent(maxWidth: Dp, onClick: (MicSeat) -> Unit) {
            BasicSeatContent(modifier = Modifier.heightIn(min = 30.dp), maxWidth = maxWidth, onClick = onClick)
        }
    }

    data class Love(
        private val micSeat: MicSeat.User,
        val loveScore: Int,
    ) : MicSeat by micSeat {

        companion object {

            val preview get() = Love(micSeat = User.preview, loveScore = 100)
        }

        @Composable
        override fun SeatContent(maxWidth: Dp, onClick: (MicSeat) -> Unit) {
            BasicSeatContent(modifier = Modifier.heightIn(min = 30.dp), maxWidth = maxWidth, onClick = onClick) {
                Spacer(modifier = Modifier.height(2.dp))
                Row(
                    modifier = Modifier
                        .background(color = Color(0xFF000000), shape = CircleShape)
                        .padding(horizontal = 4.dp),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_cp_love),
                        contentDescription = null,
                        modifier = Modifier.size(12.dp)
                    )
                    Spacer(modifier = Modifier.width(2.dp))
                    Text(
                        text = "$loveScore",
                        color = Color.White,
                        fontSize = 9.sp,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        lineHeight = 12.sp
                    )
                }
            }
        }
    }

}

fun List<MicSeat>.findMicById(id: String) = find { it.user.id == id }?.takeIf { it.hasUser }

data class MicInfo(
    val rtcId: String,
    val rtcToken: RtcToken?,
    val micList: List<MicSeat>,
) {
    companion object {

        val preview
            get() = MicInfo(
                rtcId = "",
                rtcToken = null,
                micList = listOf(
                    MicSeat.Love.preview,
                    MicSeat.Empty.preview,
                    MicSeat.Love.preview,
                    MicSeat.Love.preview,
                    MicSeat.Love.preview,
                    MicSeat.Empty.preview,
                    MicSeat.Love.preview,
                    MicSeat.Love.preview,
                ),
            )

        val preview2
            get() = MicInfo(
                rtcId = "",
                rtcToken = null,
                micList = listOf(
                    MicSeat.User.preview,
                    MicSeat.Empty.preview,
                ),
            )

        val preview3
            get() = MicInfo(
                rtcId = "",
                rtcToken = null,
                micList = listOf(
                    MicSeat.User.preview,
                    MicSeat.Love.preview,
                    MicSeat.Empty.preview,
                    MicSeat.Love.preview,
                    MicSeat.Love.preview,
                    MicSeat.Love.preview,
                    MicSeat.Empty.preview,
                    MicSeat.Love.preview,
                    MicSeat.Love.preview,
                ),
            )
    }

    val selfMic: MicSeat? by lazy(LazyThreadSafetyMode.NONE) {
        micList.findMicById(sUser.id)
    }

    val selfInMic: Boolean
        get() = selfMic != null

    val microphoneEnable: Boolean
        get() = selfMic?.muted == false

    fun isInMicById(id: String): Boolean = micList.findMicById(id) != null

}

data class IMInfo(
    val imId: String,
)

enum class UpMicMode(val value: Int) {
    Free(MIC_MODE_FREE), // 自由上麦
//    Apply(MIC_MODE_REQ), // 申请上麦
//    Pay(MIC_MODE_PAY); // 付费上麦
}

enum class RoomMode(val value: Int) {


    EIGHT_MIC_MODE(ROOM_MODE_8_MIC) {

        override val roomName: String
            @Composable
            get() = stringResource(id = R.string.cpd闲聊模式)

        override fun mapMicItem(index: Int, seat: Seat?, rtcMic: Mic?): MicSeat {
            return if (seat?.hasUser == true) {
                MicSeat.Love(
                    micSeat = super.mapMicItem(index, seat, rtcMic) as MicSeat.User, loveScore = seat.heartValue ?: 0
                )
            } else {
                super.mapMicItem(index, seat, rtcMic)
            }
        }

        override fun mapMicList(list: List<Seat>, providerRtcMic: (Int) -> Mic?): List<MicSeat> {
            return compatMapMicList(8, list, providerRtcMic)
        }
    },

    COUPLE_MIC_MODE(ROOM_MODE_PRIVATE) {

        override val roomName: String
            @Composable
            get() = stringResource(id = R.string.cpd情侣小屋)

        override val backgroundResId: Int = R.drawable.background_cpd_cp_voice_live

        override fun mapMicList(list: List<Seat>, providerRtcMic: (Int) -> Mic?): List<MicSeat> {
            return compatMapMicList(2, list, providerRtcMic)
        }
    },

    PK_MIC_MODE(ROOM_MODE_PK) {

        override val roomName: String
            @Composable
            get() = stringResource(id = R.string.cpdPK模式)

        override val applyRemoteUrl: Boolean = false

        override val backgroundResId: Int = R.drawable.background_cpd_pk_voice_live

        override fun mapMicList(list: List<Seat>, providerRtcMic: (Int) -> Mic?): List<MicSeat> {
            return compatMapMicList(9, list, providerRtcMic)
        }

        override fun mapMicItem(index: Int, seat: Seat?, rtcMic: Mic?): MicSeat {
            return if (seat?.hasUser == true) {
                MicSeat.Love(
                    micSeat = super.mapMicItem(index, seat, rtcMic) as MicSeat.User, loveScore = seat.heartValue ?: 0
                )
            } else {
                MicSeat.Empty(index = index, name = app.getString(R.string.cpd点击加入), roomMode = this)
            }
        }
    };

    @get:Composable
    abstract val roomName: String

    open val applyRemoteUrl: Boolean = true

    @get:DrawableRes
    open val backgroundResId: Int = R.drawable.background_globa_chat_room

    open fun mapMicItem(index: Int, seat: Seat?, rtcMic: Mic?): MicSeat {
        return if (seat?.hasUser == true) {
            MicSeat.User(
                user = seat.user,
                muted = rtcMic?.mute.orDefault(false),
                isSpeaking = rtcMic?.isSpeaking.orDefault(false),
                roomMode = this
            )
        } else {
            MicSeat.Empty(index = index.plus(1), name = app.getString(R.string.cpd点击加入), roomMode = this)
        }
    }

    abstract fun mapMicList(list: List<Seat>, providerRtcMic: (Int) -> Mic? = { null }): List<MicSeat>

    protected fun compatMapMicList(size: Int, list: List<Seat>, providerRtcMic: (Int) -> Mic?): List<MicSeat> {
        return List(size) { index ->
            val seat = list.getOrNull(index)
            mapMicItem(
                index, seat, if (seat?.hasUser == true) {
                    providerRtcMic(seat.user.userId)
                } else {
                    null
                }
            )
        }
    }
}

data class Settings(
    val background: String? = null,
    val locked: Boolean = false,
    val showGiftEffect: Boolean = true,
    val roomMode: RoomMode = RoomMode.EIGHT_MIC_MODE,
    val upMicMode: UpMicMode = UpMicMode.Free,
    val showRedPacket: Boolean = false,
)

data class VoiceLiveChatRoom constructor(
    val basicInfo: BasicRoomInfo,
    val micInfo: MicInfo,
    val imInfo: IMInfo,
    val settings: Settings,
    val floatStatus: FloatStatus,
    val extraInfo: ExtraInfo,
) {

    companion object {
        val preview
            get() = VoiceLiveChatRoom(
                BasicRoomInfo.preview, MicInfo.preview, IMInfo(""), Settings(), FloatStatus.Expanded, ExtraInfo()
            )

        val preview2
            get() = VoiceLiveChatRoom(
                BasicRoomInfo.preview, MicInfo.preview3, IMInfo(""), Settings(), FloatStatus.Expanded, ExtraInfo()
            )
    }

    val roomId: Int
        get() = basicInfo.id


    val imId: String
        get() = imInfo.imId

    val selfMic: MicSeat?
        get() = micInfo.selfMic

    val selfInMic: Boolean
        get() = micInfo.selfInMic

    val collapsed: Boolean
        get() = floatStatus is FloatStatus.Collapsed

    val collapsedFromUser: Boolean
        get() = (floatStatus as? FloatStatus.Collapsed)?.adaptive == false

    val audienceList = micInfo.micList.map { it.user.id }.toSet().run {
        basicInfo.roomUserList.filter {
            !contains(it.id)
        }
    }

    val isCpRoom
        get() = settings.roomMode == RoomMode.COUPLE_MIC_MODE

    val isPkRoom
        get() = settings.roomMode == RoomMode.PK_MIC_MODE

    // 游客人数
    val visitorsCount = basicInfo.roomUserList.size.minus(micInfo.micList.count { it.hasUser }).coerceAtLeast(0)

}

data class ExtraInfo(
    val micTask: MicTask? = null,
    val cpRoomUser: PrivateRoomUser? = null,
    val pkState: PkState = PkState.Idea,
    val hasCollapsedRecord: Boolean = false,
)


//"label": "xxxx",
//"popup_desc": "和同家族成员在同一个语音房上麦时，将会开始统计时间。累计时间达到10分钟即视为完成任务。可为家族增加xxx活跃值。",
//"seconds": 400,
//"status": 0   // 0 暂停状态，1 计时增加，-1 挂件消失

sealed interface MicTask {

    val taskContent: IVisible

    val isVisible: Boolean
        get() = false

    sealed interface IVisible : MicTask {

        override val isVisible: Boolean
            get() = true

        val label: String

        val tipDesc: String

    }

    data class Progress(
        override val label: String,
        override val tipDesc: String,
        val elapsedRealtime: Long,
    ) : IVisible {

        override val taskContent: Progress
            get() = this

        override val isVisible: Boolean
            get() = true
    }

    data class Paused(
        override val label: String,
        override val tipDesc: String,
        val duration: Long,
    ) : IVisible {

        override val taskContent: IVisible
            get() = this

        override val isVisible: Boolean
            get() = true
    }

    data class Finish(
        val exitingTask: IVisible,
    ) : MicTask {

        override val taskContent: IVisible
            get() = exitingTask
    }

}

fun TribeMicTask?.toMicTask(lastTask: MicTask?) = this?.run {
    if (status == -1) {
        if (lastTask != null) {
            if (lastTask is MicTask.IVisible) {
                MicTask.Finish(lastTask)
            } else {
                lastTask
            }
        } else {
            null
        }
    } else {
        if (status == 0) {
            MicTask.Paused(
                label = label, tipDesc = desc, duration = seconds.times(1000L)
            )
        } else {
            MicTask.Progress(
                label = label, tipDesc = desc, elapsedRealtime = SystemClock.elapsedRealtime().minus(seconds.times(1000))
            )
        }
    }
}

fun PkInfo?.toPkState(): PkState {
    this ?: return PkState.Idea
    return when (pkStatus) {
        2 -> {
            PkState.PKing(
                PkData(
                    Team(blueSideValue, blueSideCheersCnt, blueSideCheers),
                    Team(redSideValue, redSideCheersCnt, redSideCheers),
                    endTime.times(1000L),
                    title,
                    pkDuration.div(60),
                )
            )
        }

        3 -> {
            winnerInfo!!
            PkState.Settled(
                PkData(
                    Team(blueSideValue, blueSideCheersCnt, blueSideCheers),
                    Team(redSideValue, redSideCheersCnt, redSideCheers),
                    endTime.times(1000L),
                    title,
                    pkDuration.div(60),
                ), PKResult(
                    when (winnerSide) {
                        1 -> -1
                        2 -> 1
                        else -> 0
                    },
                    winnerInfo.heartUserInfo.user,
                    winnerInfo.heartUserInfo.value,
                    winnerInfo.cheerUserInfo.user,
                    winnerInfo.cheerUserInfo.value
                )
            )
        }

        else -> {
            PkState.Idea
        }
    }
}

sealed interface PkState {

    /**
     * 未开始状态
     */
    data object Idea : PkState

    interface PKContent : PkState {
        val data: PkData
    }


    /**
     * pk中
     */
    data class PKing(override val data: PkData) : PkState, PKContent

    /**
     * PK结算
     */
    data class Settled(override val data: PkData, val result: PKResult) : PkState, PKContent
}

data class PkData(
    val blueTeam: Team, // 蓝队
    val redTeam: Team, // 红队
    val deadline: Long, // 截止时间
    val pkPenalty: String, // pk的惩罚
    val pkDurationMinute: Int, // pk时间
)

data class Team(
    val score: Int, // 总分数
    val contributeCount: Int, // 贡献人数
    val contributeUser: List<AppUser>, // 贡献前三名
)

data class PKResult(
    val win: Int, // 获胜方
    val pkMvp: AppUser, // pk最佳选手
    val mvpScore: Int, // 最佳选手分数
    val bestContributor: AppUser, // pk最佳贡献者
    val contributionScore: Int,// 贡献分
)
