package com.qyqy.cupid.ui.live

import android.os.SystemClock
import androidx.compose.runtime.Stable
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.util.fastDistinctBy
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.qyqy.cupid.event.IMEvent
import com.qyqy.cupid.ui.FloatStatus
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.orDefault
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.im.bean.PkInfo
import com.qyqy.ucoo.im.bean.RoomSettings
import com.qyqy.ucoo.im.bean.Seat
import com.qyqy.ucoo.im.bean.TribeMicTask
import com.qyqy.ucoo.im.compat.ConversationType
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.IMCompatListener
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.im.room.RoomRepository
import com.qyqy.ucoo.isProd
import com.qyqy.ucoo.isRelease
import com.qyqy.ucoo.loginCoroutineScope
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.utils.rtc.AppRtcManager
import com.qyqy.ucoo.utils.rtc.IRtcUpdateListener
import com.qyqy.ucoo.utils.rtc.Mic
import com.qyqy.ucoo.utils.takeIsNotEmpty
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.sample
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.coroutines.EmptyCoroutineContext
import kotlin.math.abs

@Stable
class VoiceLiveState(
    voiceLiveChatRoom: VoiceLiveChatRoom,
    private val helper: VoiceLiveHelper,
    private val roomRepository: RoomRepository,
    private val appRtcManager: AppRtcManager,
) : CoroutineScope {

    internal val roomFlow = MutableStateFlow(voiceLiveChatRoom)

    private val msgListener = object : IMCompatListener {

        override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
            if (message.targetId == voiceLiveChatRoom.imInfo.imId) {
                handleRoomMessageEvent(message)
            } else {
                when (message.cmd) {
                    IMEvent.FAMILY_MIC_TASK_IN_VOICE_ROOM -> {
                        coroutineScope.launch(Dispatchers.Default) {
                            updateVoiceRoomInfo { value ->
                                val micTask = message.parseDataJson<TribeMicTask>().toMicTask(value.extraInfo.micTask)
                                value.copy(extraInfo = value.extraInfo.copy(micTask = micTask))
                            }
                        }
                    }

                    IMEvent.VOICE_MIC_ABANDONED -> {
                        if (message.getJsonInt("userid")?.toString() == sUser.id) {
                            toast(message.getJsonString("toast"))
                            downMic()
                        }
                    }
                }
            }
        }
    }

    private val updateMicListFlow: MutableSharedFlow<List<Seat>> = MutableSharedFlow()

    internal val roomState = mutableStateOf(roomFlow.value)

    val currentRoom: VoiceLiveChatRoom by roomState

    val roomId: Int
        get() = currentRoom.roomId


    val imId: String
        get() = currentRoom.imId

    val isCpRoom
        get() = currentRoom.isCpRoom

    val isPkRoom
        get() = currentRoom.isPkRoom

    val collapsed: Boolean
        get() = currentRoom.collapsed

    val floatStatus: FloatStatus
        get() = currentRoom.floatStatus

    private val coroutineScope get() = this

    override val coroutineContext = SupervisorJob() + Dispatchers.Main.immediate +  if (isProd && isRelease) {
        CoroutineExceptionHandler { _, throwable ->
            Firebase.crashlytics.recordException(throwable)
        }
    } else {
        EmptyCoroutineContext
    }

    init {
        IMCompatCore.addIMListener(msgListener)
        joinImRoom()
        joinRtcRoom()

        coroutineScope.launch {

            launch {
                roomFlow.collect {
                    roomState.value = it
                }
            }

            launch {
                updateMicListFlow.sample(300)
                    .collectLatest {
                        updateMicListChanged(it)
                    }
            }

            launch {
                roomFlow.map {
                    buildSet {
                        it.micInfo.micList.forEach {
                            if (it.hasUser) {
                                add(it.user.id)
                            }
                        }
                    }
                }.collectLatest { list ->
                    appRtcManager.updateRemoteAudioStream(list)

                    val selfInMic = list.any { it == sUser.id }
                    if (selfInMic) {
                        appRtcManager.upMic(null)
                    } else {
                        appRtcManager.downMic(null)
                    }
                }
            }
        }

    }

    fun currentVoiceLiveRoomAsState(): State<VoiceLiveChatRoom> {
        return roomState
    }

    fun clear() {
        coroutineScope.cancel()
        appRtcManager.setRtcUpdateListener(null)
        appRtcManager.levelChannel()
        IMCompatCore.removeIMListener(msgListener)
        loginCoroutineScope.launch {
            IMCompatCore.quitConversation(imId, ConversationType.CHATROOM,true)
        }
    }

    fun updateVoiceRoomInfo(function: (VoiceLiveChatRoom) -> VoiceLiveChatRoom) {
        roomFlow.update(function)
    }

    fun updateRoomUser(uid: String, updater: (AppUser) -> AppUser) {
        updateVoiceRoomInfo {
            val basicRoomInfo = it.basicInfo
            var userList = basicRoomInfo.roomUserList
            var owner = basicRoomInfo.owner
            var updated = false
            if (owner.id == uid) {
                owner = updater(owner)
                updated = true
            }
            if (userList.find { u -> u.id == uid } != null) {
                userList = userList.map { u -> if (u.id == uid) updater(u) else u }
                updated = true
            }
            if (updated) it.copy(
                basicInfo = basicRoomInfo.copy(
                    owner = owner,
                    roomUserList = userList
                )
            ) else it
        }
    }

    fun toggleMicrophone() {
        currentRoom.selfMic?.also {
            val muted = !it.muted
            appRtcManager.setEnableMuteAudio(muted)
            coroutineScope.launch {
                runApiCatching {
                    roomRepository.roomApi.toggleMicMute(mapOf("room_id" to roomId, "muted" to muted.toString()))
                }
            }
        }
    }

    fun upMic(index: Int) {
        coroutineScope.launch {
            val params = if (index == -1) {
                mapOf("room_id" to roomId)
            } else {
                mapOf("room_id" to roomId, "mic_order" to index)
            }

            runApiCatching {
                if (isCpRoom) {
                    roomRepository.roomApi.upMicForPrivateRoom(params)
                } else {
                    roomRepository.roomApi.upMic(params)
                }
            }.onSuccess {
                if (it.rtcToken.isNotEmpty()) {
                    appRtcManager.upMic(it)
                }
            }.toastError()
        }
    }

    fun downMic() {
        coroutineScope.launch {
            runApiCatching {
                if (isCpRoom) {
                    roomRepository.roomApi.downMicForPrivateRoom(mapOf("room_id" to roomId))
                } else {
                    roomRepository.roomApi.downMic(mapOf("room_id" to roomId))
                }
            }.onSuccess {
                appRtcManager.downMic(it)
            }.toastError()
        }
    }

    fun getMicById(id: Int) = appRtcManager.getMicById(id.toString())

    private fun joinImRoom() {
        coroutineScope.launch {
            try {
                val ret = IMCompatCore.joinConversation(imId, ConversationType.CHATROOM,true)
                if (!ret) {
                    toastRes(R.string.cpd连接聊天室失败_请重新加入)
                }
            } finally {
                if (!coroutineScope.isActive) {
                    withContext(NonCancellable) {
                        IMCompatCore.quitConversation(imId, ConversationType.CHATROOM,true)
                    }
                }
            }
        }
    }

    private fun joinRtcRoom() {
        appRtcManager.joinChannel(currentRoom.micInfo.rtcToken, currentRoom.selfInMic)
        appRtcManager.setRtcUpdateListener(object : IRtcUpdateListener {
            override fun onAudioVolumeUpdate() {
                coroutineScope.launch {
                    updateMicListFlow.emit(emptyList())
                }
            }
        })
    }

    private fun handleRoomMessageEvent(message: UCCustomMessage) {
        val cmd = message.cmd
        val sentTime = message.timestamp
        when (cmd) {
            MsgEventCmd.USER_ENTRANCE -> { // 加入房间
                val user = message.getJsonValue<AppUser>("user") ?: return
                if (!user.isSelf) {
                    roomFlow.update { value ->
                        val list = buildList {
                            addAll(value.basicInfo.roomUserList)
                            add(user)
                        }.fastDistinctBy {
                            it.id
                        }
                        value.copy(basicInfo = value.basicInfo.copy(roomUserList = list))
                    }
                }
            }

            MsgEventCmd.USER_EXIT -> { // 离开房间
                val user = message.getJsonValue<AppUser>("user") ?: return
                if (!user.isSelf) {
                    roomFlow.update { value ->
                        val list = value.basicInfo.roomUserList.filter {
                            it.id != user.id
                        }
                        value.copy(basicInfo = value.basicInfo.copy(roomUserList = list))
                    }
                }
            }

            MsgEventCmd.GRANT_ADMIN -> {
                val user = message.getJsonValue<AppUser>("user")?: return
                roomFlow.update { value ->
                    val list = buildList {
                        addAll(value.basicInfo.admins)
                        add(user.id)
                    }.fastDistinctBy {
                        it
                    }
                    value.copy(basicInfo = value.basicInfo.copy(admins = list))
                }
            }

            MsgEventCmd.REVOKE_ADMIN -> {
                val user = message.getJsonValue<AppUser>("user") ?: return
                roomFlow.update { value ->
                    val list = value.basicInfo.admins.filter {
                        it != user.id
                    }
                    value.copy(basicInfo = value.basicInfo.copy(admins = list))
                }
            }

            MsgEventCmd.ROOM_SETTINGS -> { // 房间设置变更
                val settings = message.parseDataJson<RoomSettings>() ?: return

                when (settings.name) {
                    RoomSettings.KEY_TITLE -> {
                        roomFlow.update { value ->
                            value.copy(basicInfo = value.basicInfo.copy(title = settings.value))
                        }
                    }

                    RoomSettings.KEY_MIC_MODE -> {
                        val mode = settings.value.toIntOrNull() ?: return
                        roomFlow.update { value ->
                            value.copy(settings = value.settings.copy(upMicMode = UpMicMode.entries.find { it.value == mode }
                                ?: UpMicMode.Free))
                        }
                    }

                    RoomSettings.KEY_ROOM_LOCK -> {
                        roomFlow.update { value ->
                            value.copy(settings = value.settings.copy(locked = settings.value.toBoolean()))
                        }
                    }

                    RoomSettings.KEY_ROOM_BACKGROUND -> {
                        roomFlow.update { value ->
                            value.copy(settings = value.settings.copy(background = settings.value))
                        }
                    }

                    RoomSettings.KEY_ROOM_MODE -> {
                        val mode = settings.value.toIntOrNull() ?: return

                        val roomMode = RoomMode.entries.find { it.value == mode } ?: RoomMode.EIGHT_MIC_MODE
                        roomFlow.update { value ->
                            value.copy(
                                settings = value.settings.copy(
                                    background = settings.roomBackground.takeIsNotEmpty() ?: value.settings.background,
                                    roomMode = roomMode,
                                ),
                                micInfo = value.micInfo.copy(
                                    micList = roomMode.mapMicList(emptyList())
                                )
                            )
                        }

                        launch {
                            delay(300)
                            helper.refreshRoomInfo()
                        }
                    }

                    RoomSettings.KEY_NOTICE -> {
                        roomFlow.update { value ->
                            value.copy(basicInfo = value.basicInfo.copy(notice = settings.value))
                        }
                    }
                }
            }

            MsgEventCmd.TAKEAWAY_MIC -> {
                appRtcManager.downMic(null)
            }

            MsgEventCmd.SEATS_CHANGE -> {
                val seats =message.getJsonValue<List<Seat>>("seats")
                if (seats != null) {
                    handleMicListChanged(seats, sentTime)
                }

                updateVoiceRoomInfo { value ->
                    if (value.isPkRoom) {
                        val pkInfo =   message.getJsonValue<PkInfo>("pk_info")
                        if (pkInfo != null) {
                            value.copy(extraInfo = value.extraInfo.copy(pkState = pkInfo.toPkState()))
                        } else {
                            value
                        }
                    } else {
                        value
                    }
                }
            }

        }
    }

    private var handleSeatChangeJob: Job? = null

    private fun handleMicListChanged(seats: List<Seat>, sentTime: Long) {
        if (abs(System.currentTimeMillis().minus(sentTime)) > 5_000) { // 消息信息比较旧不准确，应该立即拉取接口
            refreshMicList()
        } else {
            coroutineScope.launch { // 防止刷新过快
                updateMicListFlow.emit(seats)
            }
            handleSeatChangeJob?.cancel()
            handleSeatChangeJob = coroutineScope.launch { // 防止刷新过快
                delay(2000)
                refreshMicList()
                handleSeatChangeJob = null
            }
        }
    }

    private fun refreshMicList() {
        coroutineScope.launch {
            val startT = SystemClock.elapsedRealtime()
            roomRepository.getSeatsMicList(roomId)
                .onSuccess {
                    if (SystemClock.elapsedRealtime().minus(startT) < 3000) {
                        updateMicListFlow.emit(it.seats)
                    }
                }
        }
    }

    private fun updateMicListChanged(seats: List<Seat>) {
        if (seats.isEmpty()) {
            roomFlow.update { value ->
                value.copy(micInfo = value.micInfo.copy(micList = value.micInfo.micList.map {
                    it.copyMicSeat(appRtcManager.getMicById(it.user.id))
                }))
            }
        } else {
            roomFlow.update { value ->
                val newList = value.settings.roomMode.mapMicList(seats) {
                    appRtcManager.getMicById(it.toString())
                }
                val userMap = seats.associateBy {
                    it.user.id
                }
                val list = value.basicInfo.roomUserList.toMutableList().apply {
                    forEachIndexed { index, user ->
                        val newUser = userMap[user.id]
                        if (newUser != null) {
                            this[index] = newUser.user
                        }
                    }
                }.toList()
                value.copy(
                    basicInfo = value.basicInfo.copy(roomUserList = list), micInfo = value.micInfo.copy(micList = newList)
                )
            }
        }
    }

    private fun MicSeat.copyMicSeat(rtcMic: Mic?): MicSeat {
        return when (this) {
            is MicSeat.Empty -> {
                this
            }

            is MicSeat.User -> {
                copy(muted = rtcMic?.mute.orDefault(true), isSpeaking = rtcMic?.isSpeaking.orDefault(false))
            }

            is MicSeat.Love -> {
                copy(
                    micSeat = MicSeat.User(
                        user = user,
                        muted = rtcMic?.mute.orDefault(true),
                        isSpeaking = rtcMic?.isSpeaking.orDefault(false),
                        roomMode = roomMode
                    )
                )
            }
        }
    }

}