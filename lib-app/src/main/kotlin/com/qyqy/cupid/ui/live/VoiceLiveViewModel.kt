package com.qyqy.cupid.ui.live

import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewModelScope
import com.qyqy.cupid.im.messages.CoinPKMessage
import com.qyqy.cupid.im.messages.CommonPublicMessage
import com.qyqy.cupid.im.messages.CpdRedPacketMessage
import com.qyqy.cupid.im.messages.MsgLayoutContent
import com.qyqy.cupid.im.messages.RecallMessageContent
import com.qyqy.cupid.im.messages.RoomGiftMessage
import com.qyqy.cupid.im.messages.RoomInteractiveEffectMessage
import com.qyqy.cupid.im.messages.RoomInteractiveMessage
import com.qyqy.cupid.im.messages.RoomMemberChange
import com.qyqy.cupid.im.messages.RoomSystemMessageContent
import com.qyqy.cupid.im.messages.RoomTextMessage
import com.qyqy.cupid.utils.CupidFamilyManager
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.ui.runWithLoading
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.im.bean.RoomSettings
import com.qyqy.ucoo.im.chat.share.ShareApi
import com.qyqy.ucoo.im.compat.ConversationType
import com.qyqy.ucoo.im.compat.SendParams
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.UCEmojiMessage
import com.qyqy.ucoo.im.compat.UCGiftMessage
import com.qyqy.ucoo.im.compat.UCInstanceMessage
import com.qyqy.ucoo.im.compat.UCTextMessage
import com.qyqy.ucoo.im.compat.chat.IMMessageConfig
import com.qyqy.ucoo.im.compat.chat.ListStateMessageViewModel
import com.qyqy.ucoo.im.compat.isRevoked
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.im.room.RoomRepository
import com.qyqy.ucoo.toastRes
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject

class VoiceLiveViewModel(
    val roomState: State<VoiceLiveChatRoom>,
) : ListStateMessageViewModel(
    sendParams = SendParams(roomState.value.imId, ConversationType.CHATROOM),
    config = IMMessageConfig(
        loadHistoryEnable = roomState.value.extraInfo.hasCollapsedRecord, // 有过收起记录再展开要查记录
        insetTimeLine = false,
        autoCleanUnreadCount = false,
        autoScrollLatestAndFirstVisibleCount = 4,
        smoothScroll = true
    )
) {

    companion object {

        fun getMsgLayoutContent(message: UCInstanceMessage, includeRevoked: Boolean): MsgLayoutContent? {
            if (includeRevoked && message.isRevoked) {
                return RecallMessageContent
            }
            return when (message) {
                is UCTextMessage -> {
                    RoomTextMessage
                }

                is UCGiftMessage -> {
                    RoomGiftMessage
                }

                is UCEmojiMessage -> {
                    when (message.type) {
                        UCEmojiMessage.TYPE_DICE -> RoomInteractiveMessage
                        in UCEmojiMessage.TYPE_NUMBER_1..UCEmojiMessage.TYPE_NUMBER_5 -> RoomInteractiveEffectMessage
                        UCEmojiMessage.TYPE_GUESSING_FIST -> RoomInteractiveMessage
                        else -> null
                    }
                }

                is UCCustomMessage -> {
                    getMsgLayoutContentByCustomMsg(message)
                }

                else -> null
            }
        }

        private fun getMsgLayoutContentByCustomMsg(message: UCCustomMessage): MsgLayoutContent? {
            return when (message.cmd) {
                MsgEventCmd.USER_ENTRANCE -> {
                    RoomMemberChange
                }

                MsgEventCmd.RED_PACKET_CREATED -> CpdRedPacketMessage


                MsgEventCmd.COIN_PK_CREATE, MsgEventCmd.COIN_PK_WIN, MsgEventCmd.COIN_PK_OWNER_REWARD -> {
                    CoinPKMessage
                }

                MsgEventCmd.TEAM_PK_EVENT -> {
                    RoomSystemMessageContent
                }

                MsgEventCmd.COMMON_CHATROOM_PUBLIC_MESSAGES -> {
                    CommonPublicMessage
                }

                else -> null

            }
        }
    }


    val roomId = roomState.value.roomId

    private val repo by lazy {
        RoomRepository()
    }

    private val shareApi by lazy {
        createApi(ShareApi::class.java)
    }

    private val inputTextStateFlow = MutableStateFlow<InputTextState>(InputTextState.Hidden)

    val inputTextState: State<InputTextState>
        @Composable get() = inputTextStateFlow.collectAsStateWithLifecycle()

    fun setInputTextState(state: InputTextState) {
        inputTextStateFlow.value = state
    }

    override fun filterShownMessage(message: UCInstanceMessage): Boolean {
        return getMsgLayoutContent(message, false) != null
    }

    suspend fun setAdminRole(userId: Int, setup: Boolean) = repo.updateRoomAdmin(roomId, userId, setup)

    suspend fun updateRoomName(newName: String) = repo.updateRoomInfo(roomId, RoomSettings.KEY_TITLE, newName)

    suspend fun updateRoomMode(value: Int) = repo.updateRoomInfo(roomId, RoomSettings.KEY_ROOM_MODE, value)

    suspend fun blackRoom() = repo.blackRoom(roomId)

    suspend fun getShuffleRoomName() = repo.getRandomRoomName()

    suspend fun toggleRoomLock(setLock: Boolean, password: String? = null): Result<JsonObject> {
        val pwd = password.orEmpty()
        if (setLock && pwd.isBlank()) {
            toastRes(R.string.cpd_setpwd_tip)
            return Result.failure(IllegalArgumentException("password is null"))
        }
        return repo.toggleRoomLock(roomId, setLock, pwd)
    }

    suspend fun shareToFamily() {
        val familyInfo = CupidFamilyManager.myTribeInfo
        if (familyInfo == null) {
            toastRes(R.string.cpd_room_share2familly_tip)
        } else {
            runApiCatching {
                shareApi.shareAudioRoom(mapOf("share_type" to "1", "room_id" to roomId))
            }.onSuccess {
                toastRes(R.string.cpd_room_share_succeed)
            }.toastError()
        }
    }

    fun setPk(loadingState: MutableState<Boolean>, type: Int, title: String?, duration: Int?) {
        viewModelScope.launch {
            loadingState.runWithLoading {
                repo.pkSettings(roomId, type, title, duration)
            }
        }
    }

    fun resetPk(loadingState: MutableState<Boolean>) {
        viewModelScope.launch {
            loadingState.runWithLoading {
                repo.pkSettings(roomId, 3)
            }
        }
    }
}