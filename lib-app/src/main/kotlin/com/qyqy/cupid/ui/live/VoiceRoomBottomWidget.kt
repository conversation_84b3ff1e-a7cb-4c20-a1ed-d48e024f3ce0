package com.qyqy.cupid.ui.live

import android.os.Parcelable
import androidx.activity.compose.BackHandler
import androidx.annotation.DrawableRes
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Badge
import androidx.compose.material3.BadgedBox
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.cupid.ui.home.message.formatCount
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.isEditOnCompose
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.room.imeVisible
import com.qyqy.ucoo.compose.rememberSaveableRefWithPrevious
import com.qyqy.ucoo.compose.ui.AppBasicTextField
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.im.compat.MessageBundle
import com.qyqy.ucoo.im.compat.conversation.AppConversationManger
import com.qyqy.ucoo.toastRes
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.parcelize.Parcelize


@Stable
sealed class RoomButton {

    @Composable
    abstract fun RowScope.Content()

    @Stable
    data class Input(val onAction: IVoiceLiveAction) : RoomButton() {

        @Composable
        override fun RowScope.Content() {
            Row(
                modifier = Modifier
                    .weight(1f)
                    .height(40.dp)
                    .background(Color(0x26FFFFFF), CircleShape)
                    .noEffectClickable(onClick = {
                        onAction.setInputTextState(InputTextState.Visible())
                    })
                    .padding(horizontal = 12.dp), verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_cpd_edit_text),
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(2.dp))
                Text(
                    text = stringResource(id = R.string.cpd_room_chat_together),
                    fontSize = 12.sp,
                    color = Color.White,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    }

    @Stable
    data class Message(
        val onAction: IVoiceLiveAction,
    ) : RoomButton() {

        @Composable
        override fun RowScope.Content() {
            val badgeNumber by (if (isEditOnCompose) {
                MutableStateFlow(0)
            } else {
                AppConversationManger.unReadCountFlow
            }).collectAsStateWithLifecycle()

            BadgedBox(badge = {
                Badge(
                    modifier = Modifier
                        .offset(x = (-16).dp)
                        .alpha(if (badgeNumber > 0) 1f else 0f),
                    containerColor = Color(0xFFF76560),
                    contentColor = Color.White,
                ) {
                    Text(text = badgeNumber.formatCount(99))
                }
            }) {
                IconButton(onClick = onAction::showMessagePanel, modifier = Modifier.size(40.dp)) {
                    Image(painter = painterResource(id = R.drawable.ic_cpd_message_room), contentDescription = null)
                }
            }
        }
    }

    @Stable
    data class Normal(
        val tag: String,
        @DrawableRes val icon: Int,
        val onAction: IVoiceLiveAction,
    ) : RoomButton() {

        @Composable
        override fun RowScope.Content() {
            IconButton(onClick = {
                onAction.onClickNormalRoomButton(this@Normal)
            }, modifier = Modifier.size(40.dp)) {
                Image(painter = painterResource(id = icon), contentDescription = null)
            }
        }
    }

}


@Composable
fun VoiceRoomBottomBar(
    buttons: Array<RoomButton>, modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier, horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        for (button in buttons) {
            button.apply {
                Content()
            }
        }
    }
}

@Composable
fun createNormalVoiceRoomButtons(viewModel: VoiceLiveViewModel, onAction: IVoiceLiveAction): State<Array<RoomButton>> {
    return remember(onAction) {
        derivedStateOf {
            val mic = viewModel.roomState.value.selfMic
            buildList {
                add(RoomButton.Input(onAction))
                if (viewModel.roomState.value.settings.showRedPacket) {
                    add(RoomButton.Normal("红包", R.drawable.cpd_icon_rp, onAction))
                }

                add(RoomButton.Normal("游戏", R.drawable.ic_cpd_game, onAction))

                if (mic == null) {
                    add(RoomButton.Normal("上麦", R.drawable.ic_cpd_mic_up, onAction))
                } else {
                    add(
                        RoomButton.Normal(
                            tag = "麦克风切换",
                            icon = if (mic.muted) {
                                R.drawable.ic_cpd_mic_off
                            } else {
                                R.drawable.ic_cpd_mic_on
                            },
                            onAction = onAction
                        )
                    )
                    add(RoomButton.Normal("下麦", R.drawable.ic_cpd_mic_down, onAction))
                }
                add(RoomButton.Message(onAction))

                add(RoomButton.Normal("礼物", R.drawable.ic_cpd_room_gift, onAction))
            }.toTypedArray()
        }
    }
}

sealed interface InputTextState : Parcelable {

    @Parcelize
    data class Visible(val initialText: String = "") : InputTextState

    @Parcelize
    data object Hidden : InputTextState

}

@Composable
fun VoiceRoomInput(
    inputState: InputTextState,
    onInputStateChange: (InputTextState) -> Unit,
    modifier: Modifier = Modifier,
    onSend: (MessageBundle) -> Unit,
) {

    if (inputState !is InputTextState.Visible) {
        return
    }

    var inputText by rememberSaveableRefWithPrevious<String>(key1 = inputState.initialText) {
        mutableStateOf("${it.orEmpty()}${inputState.initialText}")
    }

    var selection by remember {
        mutableStateOf<TextRange?>(TextRange(inputText.length))
    }

    SideEffect { // 必须要有这个
        selection = null
    }

    val focusManager = LocalFocusManager.current

    val focusRequester = remember {
        FocusRequester()
    }

    var hasFocus by remember { mutableStateOf(false) }

    val softwareKeyboardController = LocalSoftwareKeyboardController.current

    fun dismiss() {
        if (hasFocus) {
            focusManager.clearFocus(true)
        } else {
            softwareKeyboardController?.hide()
        }
    }

    Column(modifier = Modifier.fillMaxSize()) {

        val imeVisible by imeVisible(minKeyboardHeight = 10.dp)

        Spacer(modifier = Modifier
            .animateContentSize()
            .run {
                if (imeVisible) {
                    weight(1f)
                } else {
                    fillMaxHeight()
                }
            }
            .fillMaxWidth()
            .noEffectClickable {
                dismiss()
            })

        BackHandler {
            onInputStateChange(InputTextState.Hidden)
        }

        DisposableEffect(key1 = Unit) {
            if (hasFocus) {
                softwareKeyboardController?.show()
            } else {
                focusRequester.requestFocus()
            }

            onDispose {
                inputText = ""
                if (hasFocus) {
                    focusManager.clearFocus(true)
                } else {
                    softwareKeyboardController?.hide()
                }
            }
        }

        LaunchedEffect(key1 = Unit) {
            delay(500)

            if (!imeVisible) {
                onInputStateChange(InputTextState.Hidden)
            } else {
                snapshotFlow {
                    imeVisible
                }.filter {
                    !it
                }.collectLatest {
                    onInputStateChange(InputTextState.Hidden)
                }
            }
        }

        Row(modifier = modifier.padding(horizontal = 16.dp, vertical = 12.dp)) {
            Box(
                modifier = Modifier
                    .clip(RoundedCornerShape(20.dp))
                    .weight(1f)
                    .heightIn(min = 40.dp)
                    .background(Color(0xFFF6F7FB)),
                contentAlignment = Alignment.CenterStart
            ) {
                // 不能隐藏会抛异常
                AppBasicTextField(
                    value = inputText,
                    onValueChange = {
                        inputText = it
                    },
                    selection = selection,
                    modifier = Modifier
                        .fillMaxWidth()
                        .focusRequester(focusRequester)
                        .onFocusChanged {
                            hasFocus = it.isFocused
                        }
                        .padding(horizontal = 16.dp, vertical = 10.dp),
                    textStyle = TextStyle.Default.copy(
                        color = Color(0xFF1D2129), fontSize = 14.sp
                    ),
                    maxLines = 6,
                    cursorBrush = SolidColor(MaterialTheme.colorScheme.primary),
                )
            }


            AppButton(
                text = stringResource(id = R.string.cpd发送),
                modifier = Modifier
                    .padding(start = 6.dp)
                    .size(80.dp, 40.dp),
                fontSize = 14.sp,
                textStyle = LocalTextStyle.current.copy(fontWeight = FontWeight.Medium)
            ) {
                val text = inputText
                if (text.isNotBlank()) {
                    onSend(MessageBundle.Text.create(text))
                    dismiss()
                } else {
                    toastRes(R.string.不能发送空白消息)
                }
            }
        }
    }
}