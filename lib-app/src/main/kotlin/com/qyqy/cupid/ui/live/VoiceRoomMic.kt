package com.qyqy.cupid.ui.live

import android.content.res.Configuration
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.qyqy.ucoo.compose.ui.VerticalGrid


@Composable
fun GridMicSeatsLayout(
    micInfo: MicInfo,
    modifier: Modifier = Modifier,
    rows: Int = 2,
    horizontalSpace: Dp = 8.dp,
    verticalSpace: Dp = 12.dp,
    maxWidth: Dp = Dp.Unspecified,
    onClick: (MicSeat) -> Unit = {},
) {
    VerticalGrid(
        modifier = modifier,
        columns = micInfo.micList.size.div(rows),
        horizontalSpace = horizontalSpace,
        verticalSpace = verticalSpace,
        wrapRowHeight = true,
    ) {
        for (micSeat in micInfo.micList) {
            micSeat.SeatContent(maxWidth = maxWidth, onClick)
        }
    }
}

@Preview(uiMode = Configuration.UI_MODE_NIGHT_YES, showBackground = true)
@Composable
private fun PreviewGridMicSeatsLayout() {
    GridMicSeatsLayout(micInfo = MicInfo.preview, modifier = Modifier.fillMaxWidth())
}

@Preview(uiMode = Configuration.UI_MODE_NIGHT_YES, showBackground = true)
@Composable
private fun PreviewGridMicSeatsLayout2() {
    GridMicSeatsLayout(micInfo = MicInfo.preview2, modifier = Modifier.fillMaxWidth(), rows = 1, maxWidth = 84.dp)
}