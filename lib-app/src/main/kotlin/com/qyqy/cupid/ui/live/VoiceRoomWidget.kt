package com.qyqy.cupid.ui.live

import android.content.res.Configuration
import androidx.compose.animation.core.FastOutLinearInEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.home.message.icons.ActionIcons
import com.qyqy.cupid.ui.home.message.icons.More
import com.qyqy.cupid.utils.UserCallback
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.room.EditRoomNoticeDialog
import com.qyqy.ucoo.compose.presentation.room.NoticePopup
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.utils.OnClick
import kotlin.math.sin
import kotlin.random.Random

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VoiceRoomTitleBar(
    voiceLiveChatRoom: VoiceLiveChatRoom,
    modifier: Modifier = Modifier,
    onAction: IVoiceLiveAction = IVoiceLiveAction.Empty,
) {
    val basicInfo = voiceLiveChatRoom.basicInfo
    TopAppBar(
        title = {
            if (voiceLiveChatRoom.isCpRoom) {
                Text(
                    text = stringResource(id = R.string.cpd情侣小屋),
                    modifier = Modifier.fillMaxWidth(),
                    fontSize = 16.sp,
                    color = Color.White,
                    textAlign = TextAlign.Center
                )
            } else {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(6.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .weight(1f, false)
                            .noEffectClickable {
                                onAction.showUserInfoPanel(basicInfo.owner)
                            }
                    ) {
                        Text(
                            text = basicInfo.title,
                            modifier = Modifier.basicMarquee(Int.MAX_VALUE),
                            color = Color.White,
                            fontSize = 14.sp,
                            maxLines = 1,
                        )
                        AppText(
                            text = "ID ${basicInfo.publishId}",
                            color = Color.White.copy(0.65f),
                            fontSize = 12.sp,
                            maxLines = 1,
                        )
                    }

                    if (!basicInfo.isFollowedOwner) {
                        AppButton(
                            text = stringResource(id = R.string.cpd_focus),
                            modifier = Modifier
                                .height(28.dp)
                                .padding(horizontal = 12.dp),
                            background = Color(0xFFFF5E8B),
                            color = Color.White,
                            fontSize = 12.sp,
                            onClick = {
                                onAction.followUser(mutableStateOf(basicInfo.owner), true)
                            },
                        )
                    }
                }
            }
        },
        modifier = modifier,
        navigationIcon = {
            IconButton(onClick = onAction::collapseRoom) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_cpd_navigation_back),
                    contentDescription = "back",
                )
            }
        },
        actions = {
            if (!voiceLiveChatRoom.isCpRoom) {
                IconButton(onClick = onAction::showShareRoomPanel) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_party_share),
                        contentDescription = "share",
                    )
                }
            }

            IconButton(onClick = onAction::showRoomMangePanel) {
                Icon(
                    imageVector = ActionIcons.More,
                    contentDescription = "menu",
                )
            }
        },
        colors = TopAppBarDefaults.mediumTopAppBarColors(
            containerColor = Color.Transparent,
            navigationIconContentColor = Color.White,
            titleContentColor = Color.White,
            actionIconContentColor = Color.White,
        )
    )
}

@Preview(uiMode = Configuration.UI_MODE_NIGHT_YES, showBackground = true)
@Composable
private fun PreviewVoiceRoomTitleBar() {
    VoiceRoomTitleBar(VoiceLiveChatRoom.preview)
}


@Composable
fun RoomUserRowList(
    count: Int,
    userList: List<AppUser>,
    modifier: Modifier = Modifier,
    onMemberList: OnClick = {},
    userCallback: UserCallback = {},
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        LazyRow(
            modifier = Modifier.weight(1f),
            horizontalArrangement = Arrangement.spacedBy(10.dp)
        ) {
            items(userList, key = { "roomuser-${it.id}" }) {
                CircleComposeImage(
                    model = it.avatarUrl,
                    modifier = Modifier
                        .size(32.dp)
                        .click {
                            userCallback.invoke(it)
                        }
                )
            }
        }
        Spacer(modifier = Modifier.width(8.dp))
        VerticalDivider(
            modifier = Modifier.height(20.dp),
            thickness = 0.5.dp,
            color = Color.White.copy(alpha = 0.15f)
        )
        Spacer(modifier = Modifier.width(10.dp))
        Column(
            modifier = Modifier.noEffectClickable(onClick = onMemberList),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_cpd_room_user),
                contentDescription = null,
                tint = Color.White,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.height(1.5.dp))
            AppText(text = count.toString(), color = Color.White, fontSize = 12.sp)
        }
    }
}

@Preview(uiMode = Configuration.UI_MODE_NIGHT_YES, showBackground = true)
@Composable
private fun PreviewRoomUserRowList() {
    RoomUserRowList(
        count = 15,
        userList = BasicRoomInfo.preview.roomUserList,
        modifier = Modifier
            .fillMaxWidth()
            .height(48.dp)
            .padding(horizontal = 12.dp)
    )
}


@Composable
fun VoiceRoomRankButton(modifier: Modifier = Modifier, onClick: OnClick = {}) {
    Row(
        modifier = modifier
            .height(22.dp)
            .background(Color(0x33FFFFFF), Shapes.chip)
            .padding(horizontal = 6.dp)
            .click(onClick = onClick),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_rank_logo),
            contentDescription = null,
            modifier = Modifier.size(12.dp)
        )
        Spacer(modifier = Modifier.width(1.dp))
        Text(
            text = stringResource(id = R.string.cpd_room_rank),
            color = Color.White,
            fontSize = 12.sp,
        )
    }
}

@Composable
fun RoomNotice(modifier: Modifier = Modifier, onClick: OnClick) {
    Row(
        modifier = modifier
            .background(Color(0x33FFFFFF), Shapes.chip)
            .padding(horizontal = 6.dp)
            .height(22.dp)
            .click(onClick = onClick),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(id = R.drawable.icon_notice),
            contentDescription = "notice",
            modifier = Modifier.size(12.dp),
            contentScale = ContentScale.Crop
        )
        Spacer(modifier = Modifier.width(2.dp))
        AppText(text = stringResource(id = R.string.cpd公告), fontSize = 12.sp, color = Color.White)
    }
}

@Composable
fun RoomNoticeButton(room: BasicRoomInfo, modifier: Modifier = Modifier) {
    if (room.isOwner || room.notice.isNotEmpty()) {
        val empty = stringResource(id = R.string.cpd暂无公告)
        var visible by remember {
            mutableStateOf(false)
        }
        val dq = LocalDialogQueue.current
        Box(modifier = Modifier) {
            RoomNotice(modifier = modifier) {
                if (room.isOwner) {
                    dq.push(EditRoomNoticeDialog(room.id, room.notice))
                } else {
                    visible = true
                }
            }
            if (visible) {
                Popup(Alignment.TopStart, onDismissRequest = {
                    visible = false
                }) {
                    NoticePopup(notice = room.notice, emptyMsg = empty, modifier = Modifier
                        .click {
                            visible = false
                        }
                        .padding(top = 30.dp))
                }
            }
        }

    }
}

@Preview
@Composable
private fun NoticePreview() {
    RoomNotice {

    }
}

@Preview
@Composable
private fun PreviewVoiceRoomRankButton() {
    VoiceRoomRankButton()
}


@Composable
fun VoiceWaves(
    count: Int,
    modifier: Modifier = Modifier,
    color: Color = Color.White,
    itemWidth: Dp = 2.dp,
    itemRadius: Dp = itemWidth,
    spaceWidth: Dp = 2.5.dp,
    minHeight: Dp = 2.dp,
) {
    val trans = rememberInfiniteTransition(label = "VoiceWaves")
    val fraction by trans.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        infiniteRepeatable(animation = tween(400, easing = FastOutLinearInEasing), repeatMode = RepeatMode.Reverse),
        label = "tr"
    )
    Box(modifier = modifier.drawWithCache {

        val itemWidthPx = itemWidth.toPx()

        val cornerRadius = CornerRadius(itemRadius.toPx())

        val spaceWidthPx = spaceWidth.toPx()

        val minHeightPx = minHeight.toPx().coerceAtMost(size.height)

        val amplitude = size.height.minus(minHeightPx)

        val frequency = 0.5f

        val shiftOffset = doubleArrayOf(0.1 * Math.PI, 1 * Math.PI, 1.8 * Math.PI)

        val randomOffset = DoubleArray(count) {
            shiftOffset[Random.nextInt(shiftOffset.size)]
        }

        onDrawWithContent {
            var left = 0f
            repeat(count) {
                val phaseShift = (it.toFloat() / (count - 1)) * Math.PI  // 平滑过渡的相位偏移
                val h =
                    (amplitude * (1 + sin(2 * Math.PI * frequency * fraction + phaseShift + randomOffset[it])) / 2 + minHeightPx).toFloat()
                val y = size.height - h
                drawRoundRect(color = color, topLeft = Offset(left, y), size = Size(itemWidthPx, h), cornerRadius = cornerRadius)
                left += itemWidthPx + spaceWidthPx
            }
        }
    })
}