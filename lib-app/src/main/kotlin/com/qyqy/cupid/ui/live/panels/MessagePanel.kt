package com.qyqy.cupid.ui.live.panels

import android.os.Parcelable
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.cupid.ui.dialog.AnimatedDialog
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.home.message.MessageListContent
import com.qyqy.cupid.ui.relations.family.FamilySquareAction
import com.qyqy.ucoo.R
import kotlinx.parcelize.Parcelize

@Parcelize
data object MessageListDialog : AnimatedDialog<IVoiceLiveAction>(), Parcelable {

    override val restoreState: Boolean
        get() = true

    @Composable
    override fun Content(dialog: IDialog, onAction: IVoiceLiveAction?) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.8f)
                .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                .background(Color.White)
                .paint(
                    painter = painterResource(id = R.drawable.cupid_header_home),
                    contentScale = ContentScale.FillWidth,
                    alignment = Alignment.TopCenter,
                )
        ) {
            Text(
                text = stringResource(id = R.string.cupid_message), fontSize = 24.sp, maxLines = 1, fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(start = 12.dp, top = 12.dp, bottom = 10.dp, end = 12.dp)
            )
            MessageListContent({ nav ->
                nav.navigate(CupidRouters.FAMILY_SQUARE, mapOf("action" to FamilySquareAction.SHOW_REMIND_DIALOG))
            }) {
                onAction?.onNavigateTo(it)
            }
        }
    }
}