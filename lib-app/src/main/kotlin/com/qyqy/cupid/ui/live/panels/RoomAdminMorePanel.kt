package com.qyqy.cupid.ui.live.panels

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.cupid.ui.dialog.AnimatedDialog
import com.qyqy.cupid.ui.dialog.CommonAlertDialog
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.live.VoiceLiveChatRoom
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.utils.ComposeContent
import com.qyqy.ucoo.utils.OnClick
import kotlin.coroutines.Continuation
import kotlin.coroutines.suspendCoroutine


data class RoomAdminMoreDialog(
    private val userState: MutableState<AppUser>,
    private val currentRoomState: State<VoiceLiveChatRoom>,
) : AnimatedDialog<IVoiceLiveAction>() {

    @Composable
    override fun Content(dialog: IDialog, onAction: IVoiceLiveAction?) {
        RoomAdminMoreContent(userState, currentRoomState, dialog, onAction)
    }
}


@Composable
private fun RoomAdminMoreContent(
    userState: MutableState<AppUser>,
    currentRoomState: State<VoiceLiveChatRoom>,
    dialog: IDialog,
    onAction: IVoiceLiveAction?
) {

    val roomInfo by currentRoomState

    val style = TextStyle(MaterialTheme.colorScheme.onSecondary, 16.sp)

    val isOwner = roomInfo.basicInfo.isOwner
    val targetIsAdmin by remember {
        derivedStateOf {
            roomInfo.basicInfo.isRoomAdmin(userState.value.id)
        }
    }

    val isFollowed by remember {
        derivedStateOf {
            userState.value.followed
        }
    }

    val inBlackList by remember {
        derivedStateOf {
            userState.value.inRoomBlackList == true
        }
    }

    val dialogQueue = LocalDialogQueue.current

    val item = remember(dialog, onAction, isOwner, targetIsAdmin, isFollowed, inBlackList, dialogQueue) {
        buildList<ComposeContent> {
            if (roomInfo.basicInfo.isOwner) {
                add {
                    val alert = stringResource(id = if (targetIsAdmin) R.string.cpd_确定要取消管理员吗 else R.string.cpd_确定要设为管理员吗)
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(54.dp)
                            .click {
                                dialogQueue.push(CommonAlertDialog(alert) {
                                    onAction?.updateRoomAdmin(userState.value.userId, !targetIsAdmin)
                                }, true)
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(id = if (targetIsAdmin) R.string.cpd_取消房间管理员 else R.string.cpd_设置为房间管理员),
                            style = style
                        )
                    }
                }
            }

            add {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(54.dp)
                        .click {
                            onAction?.followUser(userState, !isFollowed)
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = stringResource(id = if (isFollowed) R.string.cpd_cancel_follow else R.string.cpd_focus),
                        style = style
                    )
                }
            }

            add {
                val alert = stringResource(id = if (inBlackList) R.string.cpd_确定要取消黑名单吗 else R.string.cpd_确定要拉入黑名单吗)
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(54.dp)
                        .click {
                            dialogQueue.push(CommonAlertDialog(alert) {
                                onAction?.blackUserInRoom(userState, !inBlackList)
                            }, true)
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = stringResource(id = if (inBlackList) R.string.cpd_取消黑名单 else R.string.cpd_加入黑名单),
                        style = style.copy(Color(0xFFF53F3F))
                    )
                }
            }

        }
    }

    IOSPanel(items = item.toTypedArray(), onCancel = {
        dialog.dismiss()
    })
}

@Composable
fun IOSPanel(vararg items: ComposeContent, onCancel: OnClick) {
    val dividerColor = Color(0xFFE5E6EB)
    val itemHeight = 54.dp
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .background(MaterialTheme.colorScheme.surface)
            .navigationBarsPadding()
    ) {
        items.forEach {
            it.invoke()
            HorizontalDivider(color = dividerColor, thickness = 0.5.dp)
        }
        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp)
                .background(Color(0xFFF2F3F5))
        )
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(itemHeight)
                .click(onClick = onCancel), contentAlignment = Alignment.Center
        ) {
            Text(
                text = stringResource(id = R.string.cpd_cancel),
                style = TextStyle(color = MaterialTheme.colorScheme.onSecondary, fontSize = 16.sp)
            )
        }
        HorizontalDivider(color = dividerColor, thickness = 0.5.dp)
    }
}

