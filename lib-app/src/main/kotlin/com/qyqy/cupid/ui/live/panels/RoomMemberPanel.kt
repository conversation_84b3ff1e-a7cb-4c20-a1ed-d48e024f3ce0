package com.qyqy.cupid.ui.live.panels

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.cupid.ui.dialog.AnimatedDialog
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.live.VoiceLiveChatRoom
import com.qyqy.cupid.widgets.state.CupidPullRefreshBox
import com.qyqy.cupid.widgets.state.CupidStateLayoutDefaults
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.ui.AgeGender
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.LoadResult
import com.qyqy.ucoo.compose.ui.rememberPaginateState
import com.qyqy.ucoo.user.UserManager
import kotlinx.coroutines.launch

data class RoomMemberListPanelDialog(
    private val currentRoomState: State<VoiceLiveChatRoom>,
) : AnimatedDialog<IVoiceLiveAction>() {

    @Composable
    override fun Content(dialog: IDialog, onAction: IVoiceLiveAction?) {
        RoomMemberListPanel(
            currentRoomState = currentRoomState,
            onAction = onAction,
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.8f)
        )
    }

}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RoomMemberListPanel(
    currentRoomState: State<VoiceLiveChatRoom>,
    onAction: IVoiceLiveAction?,
    modifier: Modifier = Modifier
) {
    var isRefreshing by rememberSaveable {
        mutableStateOf(true)
    }

    val roomId = currentRoomState.value.basicInfo.id
    val list = remember {
        mutableStateListOf<AppUser>().apply {
            addAll(currentRoomState.value.basicInfo.roomUserList)
        }
    }

    var count by rememberSaveable {
        mutableIntStateOf(list.size)
    }

    val paginateState = rememberPaginateState<Int>(nextEnable = false)

    val scope = rememberCoroutineScope()

    val listState = rememberLazyListState()

    paginateState.LaunchAttach(listState = listState) {
        UserManager.roomRepository.getMemberList(roomId, it.key!!).fold({
            count = it.totalMemberCount
            if (it.list.isNotEmpty()) {
                list.addAll(it.list)
                val key = it.list.last().memberId?.toInt()
                if (key != null) {
                    LoadResult.Page(key)
                } else {
                    LoadResult.Page(null)
                }
            } else {
                LoadResult.Page(null)
            }
        }) { e ->
            LoadResult.Error(e)
        }
    }

    Column(
        modifier = modifier.background(Color.White, shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(54.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = stringResource(id = R.string.cpd房间在线用户, count),
                color = Color(0xFF1D2129),
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
        }

        HorizontalDivider(thickness = 0.5.dp, color = Color(0xFFF1F2F3))

        val onRefresh: () -> Unit = {
            scope.launch {
                paginateState.nextEnable = false
                UserManager.roomRepository.getMemberList(roomId, 0).onSuccess {
                    count = it.totalMemberCount
                    if (it.list.isNotEmpty()) {
                        list.clear()
                        list.addAll(it.list)
                        val key = it.list.last().memberId?.toInt()
                        if (key != null) {
                            paginateState.resetNext(key = key)
                            paginateState.nextEnable = true
                        }
                    }
                }
                isRefreshing = false
            }
        }

        LaunchedEffect(key1 = Unit) {
            if (isRefreshing) {
                onRefresh()
            }
        }

        CupidPullRefreshBox(
            isRefreshing = isRefreshing,
            onRefresh = onRefresh,
            modifier = Modifier.fillMaxSize()
        ) {
            if (list.isEmpty()) {
                CupidStateLayoutDefaults.defaultEmpty(modifier = Modifier.verticalScroll(rememberScrollState()))
            } else {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    state = listState,
                    contentPadding = PaddingValues(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp),
                ) {
                    items(list) {
                        Row {
                            CircleComposeImage(
                                model = it.avatarUrl,
                                modifier = Modifier
                                    .padding(end = 8.dp)
                                    .size(56.dp)
                                    .noEffectClickable {
                                        onAction?.showUserInfoPanel(it)
                                    }
                            )
                            Column {
                                Text(
                                    text = it.nickname,
                                    maxLines = 1,
                                    overflow = TextOverflow.Ellipsis,
                                    fontSize = 16.sp,
                                    color = CpdColors.FF1D2129,
                                    fontWeight = FontWeight.Medium
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                                AgeGender(age = it.age, isBoy = it.isBoy)
                            }
                        }
                    }
                }
            }
        }
    }

}