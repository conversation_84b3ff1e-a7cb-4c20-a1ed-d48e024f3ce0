@file:OptIn( ExperimentalMaterial3Api::class)

package com.qyqy.cupid.ui.live.panels

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.util.fastForEachIndexed
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.cupid.widgets.click
import com.qyqy.cupid.widgets.state.CupidPullRefreshBox
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.state.EmptyView
import com.qyqy.ucoo.compose.theme.colorWhite50Alpha
import com.qyqy.ucoo.compose.ui.AnimatedDialog
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.im.chat.rank.HeartRankItem
import com.qyqy.ucoo.im.chat.rank.RankCategory
import com.qyqy.ucoo.im.chat.rank.RankItem
import com.qyqy.ucoo.im.chat.rank.RankParams
import com.qyqy.ucoo.im.chat.rank.RankRequestEvent
import com.qyqy.ucoo.im.chat.rank.RankTimely
import com.qyqy.ucoo.im.chat.rank.RankViewModel
import com.qyqy.ucoo.im.chat.rank.RichRankItem
import com.qyqy.ucoo.im.chat.rank.nickName
import com.qyqy.ucoo.tribe.bean.BriefUser

@Composable
fun RoomRankContent(
    list: List<RankItem>,
    isRefreshing: Boolean = false,
    onRefresh: () -> Unit = {},
    dismiss: () -> Unit = {},
    onChange: (RankParams) -> Unit = {}
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(375 / 476f)
            .background(
                Brush.verticalGradient(listOf(Color(0xFF2A2652), Color(0xFF1A1C29))),
                RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
            )
            .navigationBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        val rankRich = stringResource(R.string.cpd_rick_rank)
        val rankHot = stringResource(R.string.cpd_heart_rank)
        val rankTitles = remember {
            listOf(rankRich, rankHot)
        }
        var rankIndex by rememberSaveable {
            mutableIntStateOf(0)
        }
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 12.dp),
            horizontalArrangement = Arrangement.Center
        ) {
            rankTitles.fastForEachIndexed { i: Int, s: String ->
                Text(
                    text = s,
                    color = if (rankIndex == i) Color.White else Color.White.copy(0.5f),
                    fontSize = 16.sp,
                    modifier = Modifier
                        .noEffectClickable {
                            rankIndex = i
                        }
                        .padding(horizontal = 30.dp)
                )
            }
        }

        val subRankDay = stringResource(R.string.cpd_daily_rank)
        val subRankWeek = stringResource(R.string.cpd_week_rank)
        val subRankTitles = remember {
            listOf(subRankDay, subRankWeek)
        }
        var subRankIndex by rememberSaveable {
            mutableIntStateOf(0)
        }
        Row(
            modifier = Modifier
                .padding(top = 8.dp, bottom = 4.dp)
                .background(Color(0xFF343964), RoundedCornerShape(50))
                .padding(2.dp)
        ) {
            subRankTitles.forEachIndexed { i, s ->
                Text(
                    text = s,
                    color = if (i == subRankIndex) Color(0xFF1A1C2A) else colorWhite50Alpha,
                    modifier = Modifier
                        .clip(RoundedCornerShape(50))
                        .click {
                            subRankIndex = i
                        }
                        .background(
                            if (i == subRankIndex) Color.White else Color.Transparent,
                            RoundedCornerShape(50)
                        )
                        .padding(30.dp, 7.dp)
                )
            }
        }
        LaunchedEffect(key1 = rankIndex, key2 = subRankIndex) {
            onChange(RankParams(category = RankCategory.entries[rankIndex], rankTimely = RankTimely.entries[subRankIndex]))
        }
        CupidPullRefreshBox(modifier = Modifier.fillMaxSize(), isRefreshing = isRefreshing, onRefresh = onRefresh) {
            LazyColumn(modifier = Modifier.fillMaxSize(), verticalArrangement = Arrangement.spacedBy(8.dp)) {
                if (list.isEmpty()) {
                    item {
                        EmptyView(
                            iconRes = R.drawable.empty_dark, modifier = Modifier
                                .fillMaxWidth()
                                .aspectRatio(1f)
                        )
                    }
                } else {
                    itemsIndexed(list) { index: Int, item: RankItem ->
                        RoomRankItem(index = index + 1, item = item, dismiss)
                    }
                }
            }
        }
    }
}

@Composable
fun RoomRankItem(index: Int, item: RankItem, onDismiss: () -> Unit = {}) {
    val color = remember {
        Color.White.copy(0.8f)
    }
    val colorPink = remember {
        Color(0xFFFF5EB5)
    }
    val colorGold = remember {
        Color(0xFFFFB21A)
    }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = index.toString(),
            modifier = Modifier.widthIn(min = 20.dp),
            fontSize = 18.sp,
            color = color,
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.width(8.dp))
        val nav = LocalAppNavController.current
        CircleComposeImage(model = item.userInfo.avatarUrl, modifier = Modifier
            .size(48.dp)
            .click {
                onDismiss.invoke()
                nav.navigateToProfile(item.userInfo.id)
            })
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = item.nickName,
            color = MaterialTheme.colorScheme.surface,
            fontSize = 14.sp,
            modifier = Modifier.weight(1f),
            maxLines = 2,
            overflow = TextOverflow.Ellipsis
        )
        Spacer(modifier = Modifier.width(8.dp))
        val isRich = item is RichRankItem
        Image(
            painter = painterResource(id = if (isRich) R.drawable.ic_cpd_coin else R.drawable.cpd_ic_heart),
            contentDescription = "icon",
            modifier = Modifier.size(12.dp)
        )
        Spacer(modifier = Modifier.width(2.dp))
        Text(text = item.displayValue, fontSize = 12.sp, color = if (isRich) colorGold else colorPink)
    }
}

@Composable
fun rememberRankPanelState(
    roomId: Int, vm: RankViewModel = viewModel(key = roomId.toString()) { RankViewModel(roomId) }
) = run {
    val state = rememberSaveable {
        mutableStateOf(false)
    }
    if (state.value) {
        val dataState by vm.currentRankFlow.collectAsStateWithLifecycle()
        val list = dataState.getOrNull().orEmpty()
        val isRefreshing by vm.isRefreshing
        LaunchedEffect(key1 = roomId) {
            if (vm.cached) {
                vm.refresh(true)
            }
        }
        AnimatedDialog(onDismiss = { state.value = false }) {
            RoomRankContent(list = list, isRefreshing = isRefreshing, onRefresh = {
                vm.refresh(true)
            }, dismiss = {
                state.value = false
            }, onChange = { vm.sendEvent(RankRequestEvent(it)) })
        }
    }
    state
}


@Preview
@Composable
private fun RankPreview() {
    PreviewCupidTheme {
        RoomRankContent(listOf(RichRankItem(BriefUser(), 1000), HeartRankItem(BriefUser(), 800)))
    }
}