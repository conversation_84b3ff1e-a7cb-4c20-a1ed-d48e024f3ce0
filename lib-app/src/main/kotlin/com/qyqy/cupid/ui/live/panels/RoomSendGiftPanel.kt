

package com.qyqy.cupid.ui.live.panels

import android.os.Parcelable
import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.coerceAtLeast
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.util.fastDistinctBy
import com.qyqy.cupid.im.panel.gift.GiftListModel
import com.qyqy.cupid.im.panel.gift.GiftPanelContent
import com.qyqy.cupid.im.panel.gift.GiftPanelScaffold
import com.qyqy.cupid.im.panel.gift.GiftPosition
import com.qyqy.cupid.im.panel.gift.LuckGiftContent
import com.qyqy.cupid.ui.IGiftAction
import com.qyqy.cupid.ui.dialog.AnimatedDialog
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.live.VoiceLiveChatRoom
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.im.bean.CPGift
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.utils.EntityCallback
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue

private fun List<User>.identifyId(): String {
    return this.sortedBy { it.id }.joinToString(separator = "-")
}

@Composable
private fun RoomSendGiftPanelHeader(
    allUsers: List<User>,
    selectedUsers: List<User>,
    onUserClick: EntityCallback<User>,
    modifier: Modifier = Modifier,
    toggleAll: EntityCallback<Boolean> = {},
) {
    val idAllUsers = remember(allUsers) {
        allUsers.identifyId()
    }
    val idSelectedUsers = remember(selectedUsers) {
        selectedUsers.identifyId()
    }
    val isSelectAll = (idAllUsers == idSelectedUsers) && idAllUsers.isNotEmpty()
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        Text(text = stringResource(R.string.cpd_give_to), color = MaterialTheme.colorScheme.onSurfaceVariant, fontSize = 12.sp)
        Spacer(modifier = Modifier.width(8.dp))
        LazyRow(modifier = Modifier.weight(1f), horizontalArrangement = Arrangement.spacedBy(4.dp)) {
            items(allUsers) {
                ComposeImage(
                    model = it.avatarUrl,
                    modifier = Modifier
                        .size(24.dp)
                        .then(
                            if (selectedUsers.contains(it)) Modifier.border(
                                1.dp, MaterialTheme.colorScheme.primary, CircleShape
                            ) else Modifier
                        )
                        .clip(CircleShape)
                        .click { onUserClick(it) }
                )
            }
        }
        Spacer(modifier = Modifier.width(8.dp))
        Box(
            modifier = Modifier
                .clip(RoundedCornerShape(50))
                .height(24.dp)
                .click { toggleAll(!isSelectAll) }
                .background(if (isSelectAll) MaterialTheme.colorScheme.primary else Color(0xFF4A4A4A), RoundedCornerShape(50))
                .padding(horizontal = 14.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = if (isSelectAll) stringResource(R.string.cpd_select_none) else stringResource(R.string.cpd_select_all),
                fontSize = 10.sp,
                color = MaterialTheme.colorScheme.surface
            )
        }
    }
}

@Parcelize
data class RoomGiftDialog(
    private val position: GiftPosition?,
    private val giftListModelState: @RawValue State<GiftListModel>,
    private val currentRoomState: @RawValue State<VoiceLiveChatRoom>,
) : AnimatedDialog<IGiftAction>(), Parcelable {

    @Composable
    override fun Content(dialog: IDialog, onAction: IGiftAction?) {
        val giftModel by giftListModelState
        val currentRoom by currentRoomState

        val giftReceivers by remember {
            derivedStateOf {
                buildList {
                    if (!currentRoom.isCpRoom && !currentRoom.basicInfo.isOwner) {
                        add(currentRoom.basicInfo.owner)
                    }
                    currentRoom.micInfo.micList.forEach {
                        if (it.hasUser && !it.user.isSelf) {
                            add(it.user)
                        }
                    }
                }.fastDistinctBy { it.id }
            }
        }
        var selectedUsers by remember {
            mutableStateOf(emptyList<User>())
        }

        val displaySelectUsers by remember {
            derivedStateOf { selectedUsers.filter { giftReceivers.contains(it) } }
        }

        GiftPanelScaffold(giftModel) { onSelectedChange ->
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(MaterialTheme.colorScheme.inverseSurface)
                    .padding(start = 16.dp, end = 16.dp, bottom = 16.dp)
                    .padding(
                        bottom = with(LocalDensity.current) {
                            WindowInsets.navigationBars
                                .getBottom(this)
                                .toDp()
                        }.coerceAtLeast(15.dp)
                    )
            ) {
                RoomSendGiftPanelHeader(
                    allUsers = giftReceivers,
                    selectedUsers = selectedUsers,
                    onUserClick = {
                        if (selectedUsers.contains(it)) {
                            selectedUsers -= it
                        } else {
                            selectedUsers += it
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 20.dp)
                ) {
                    selectedUsers = if (it) {
                        giftReceivers
                    } else {
                        emptyList()
                    }
                }

                GiftPanelContent(
                    giftPosition = position,
                    giftModel = giftModel,
                    onRecharge = {
                        onAction?.onShowRechargePanel()
                    },
                    onSelectedChange = {
                        onAction?.shouldLuckyDataChange(it?.isLuckyBall == true)
                        onSelectedChange(it)
                    },
                    onSend = { gift, count, fromPacket ->
                        if (displaySelectUsers.isEmpty()) {
                            toastRes(R.string.cpd请选择收礼人)
                            return@GiftPanelContent false
                        }
                        onAction?.onSendGiftTo(displaySelectUsers.map { it.id }, gift, count, fromPacket)
                        return@GiftPanelContent true
                    },
                    onComboDone = {
                        onAction?.onSendGideComboFinish()
                    }
                )
            }

            DisposableEffect(key1 = Unit) {
                onDispose {
                    onAction?.shouldLuckyDataChange(false)
                }
            }
        }
    }
}
