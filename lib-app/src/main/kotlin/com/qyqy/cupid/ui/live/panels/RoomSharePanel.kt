

package com.qyqy.cupid.ui.live.panels

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.VerticalGrid

/**
 *  @time 8/30/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.live.panels
 */
@Composable
fun VoiceRoomShareContent(dialog: IDialog, onAction: IDialogAction?) {
    Column(
        modifier = Modifier
            .background(color = Color.White, shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .padding(vertical = 16.dp)
            .fillMaxWidth()
            .navigationBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(stringResource(id = R.string.cpd_room_share), fontSize = 16.sp, color = CpdColors.FF1D2129)
        Spacer(modifier = Modifier.height(20.dp))
        VerticalGrid(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp),
            verticalSpace = 20.dp,
            columns = 4,
            horizontalSpace = 8.dp
        ) {
            Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier.click {
                dialog.dismiss()
                (onAction as? IVoiceLiveAction)?.shareToFamily()
            }) {
                ComposeImage(model = R.drawable.ic_voice_share_family, modifier = Modifier.size(48.dp))
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = stringResource(id = R.string.cpd_room_share_family),
                    color = CpdColors.FF1D2129,
                    fontSize = 12.sp,
                    textAlign = TextAlign.Center
                )
            }

            Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier.click {
                dialog.dismiss()
                (onAction as? IVoiceLiveAction)?.shareToFriend()
            }) {
                ComposeImage(model = R.drawable.ic_voice_share_private, modifier = Modifier.size(48.dp))
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = stringResource(id = R.string.cpd_room_share_friend),
                    color = CpdColors.FF1D2129,
                    fontSize = 12.sp,
                    textAlign = TextAlign.Center
                )
            }
        }
        Spacer(modifier = Modifier.height(36.dp))
    }
}

@Composable
@Preview(showBackground = true)
private fun VoiceRoomSharePreview() {
    VoiceRoomShareContent(dialog = IDialog {}, onAction = object : IDialogAction {})
}