package com.qyqy.cupid.ui.live.panels

import android.Manifest
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.coerceAtLeast
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.im.panel.gift.GiftListModel
import com.qyqy.cupid.im.panel.gift.GiftPanelContent
import com.qyqy.cupid.im.panel.gift.GiftPanelScaffold
import com.qyqy.cupid.im.panel.gift.GiftPosition
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.cupid.ui.dialog.AnimatedDialog
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.home.message.icons.ActionIcons
import com.qyqy.cupid.ui.home.message.icons.Chat
import com.qyqy.cupid.ui.live.VoiceLiveChatRoom
import com.qyqy.cupid.ui.live.VoiceLiveHelper
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.cupid.ui.relations.family.icons.PlusBlack
import com.qyqy.cupid.utils.UserCallback
import com.qyqy.cupid.widgets.AvatarComposeView
import com.qyqy.cupid.widgets.CpdButton
import com.qyqy.cupid.widgets.GiftWallContent
import com.qyqy.cupid.widgets.LevelComposeView
import com.qyqy.cupid.widgets.click
import com.qyqy.cupid.widgets.rememberPermissionLauncher
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.ui.AgeGender
import com.qyqy.ucoo.compose.ui.AnimatedDialog
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.setting.ReportActivity
import com.qyqy.ucoo.user.UserManager
import com.qyqy.ucoo.utils.ComposeContent
import com.qyqy.ucoo.utils.EntityCallback
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.launch

data class RoomUserPanelDialog(
    private val userState: MutableState<AppUser>,
    private val currentRoomState: State<VoiceLiveChatRoom>,
    private val giftListModelState: State<GiftListModel>,
    private val giftPosition: GiftPosition?,
) : AnimatedDialog<IVoiceLiveAction>() {

    @Composable
    override fun Content(dialog: IDialog, onAction: IVoiceLiveAction?) {
        if (userState.value.isSelf) {
            LaunchedEffect(key1 = currentRoomState.value.basicInfo.id, key2 = userState.value.id) {
                UserManager.roomRepository.getRoomMemberInfo(currentRoomState.value.basicInfo.id, userState.value.id)?.also {
                    userState.value = it
                }
            }
            MyProfile(user = userState.value, dialog = dialog, onAction = onAction)
        } else {
            GiftPanelScaffold { onSelectedChange ->
                Column(modifier = Modifier.fillMaxWidth()) {
                    RoomUserPanelHeaderState(
                        userState = userState,
                        currentRoom = currentRoomState.value,
                        dialog = dialog,
                        onAt = {
                            dialog.dismiss()
                            onAction?.atUser(userState.value)
                        },
                        onKickMic = {
                            dialog.dismiss()
                            onAction?.kickDownMic(userState.value)
                        },
                        onInviteMic = {
                            dialog.dismiss()
                            onAction?.inviteUpMic(userState.value)
                        },
                        onMore = {
                            dialog.dismiss()
                            onAction?.showManageUserMenu(userState)
                        },
                        onClickGiftWall = {
                            dialog.dismiss()
                            onAction?.gotoGiftWallPage(userState.value.userId)
                        },
                        onClickFamily = { value ->
                            dialog.dismiss()
                            onAction?.gotoFamilyDetail(value)
                        },
                        onAction = onAction
                    )
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(MaterialTheme.colorScheme.inverseSurface)
                            .padding(start = 16.dp, end = 16.dp, bottom = 16.dp)
                            .padding(
                                bottom = with(LocalDensity.current) {
                                    WindowInsets.navigationBars
                                        .getBottom(this)
                                        .toDp()
                                }.coerceAtLeast(15.dp)
                            )
                    ) {
                        GiftPanelContent(
                            giftModel = giftListModelState.value,
                            onRecharge = {
                                onAction?.onShowRechargePanel()
                            },
                            onSelectedChange = {
                                onAction?.shouldLuckyDataChange(it?.isLuckyBall == true)
                                onSelectedChange(it)
                            },
                            onSend = { gift, count, fromPacket ->
                                onAction?.onSendGiftTo(listOf(userState.value.id), gift, count, fromPacket)
                                return@GiftPanelContent true
                            },
                            onComboDone = {
                                onAction?.onSendGideComboFinish()
                            },
                            giftPosition = giftPosition
                        )
                        DisposableEffect(key1 = Unit) {
                            onDispose {
                                onAction?.shouldLuckyDataChange(false)
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun RoomUserPanelHeader(
    user: User,
    iAmAdmin: Boolean,
    targetHasMic: Boolean,
    modifier: Modifier = Modifier,
    onAt: OnClick = {},
    onFollow: OnClick = {},
    onChat: OnClick = {},
    onReport: OnClick = {},
    onCheckProfile: OnClick = {},
    onKickMic: UserCallback,//踢下麦
    onInviteMic: UserCallback,//邀请上麦
    onMore: OnClick,
    onClickGiftWall: OnClick,
    onClickFamily: EntityCallback<Int>,
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 20.dp)
                .background(MaterialTheme.colorScheme.inverseSurface, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                .padding(bottom = 20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(68.dp))

            Text(
                text = user.nickname,
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.widthIn(max = 180.dp),
                maxLines = 1,
                color = MaterialTheme.colorScheme.surface,
                overflow = TextOverflow.Ellipsis
            )

            Row(
                modifier = Modifier
                    .padding(top = 4.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(4.dp, Alignment.CenterHorizontally),
                verticalAlignment = Alignment.CenterVertically
            ) {
                AgeGender(age = user.age, isBoy = user.isBoy)
                LevelComposeView(user = user)
            }

            Spacer(
                modifier = Modifier
                    .height(20.dp)
                    .fillMaxWidth()
            )
            Row(
                modifier = Modifier
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                val colorDivider = Color(0xFF4A4A4A)
                val style = remember {
                    TextStyle(color = Color.White.copy(0.5f), fontSize = 12.sp)
                }
                val mod = Modifier
                    .weight(1f)
                    .heightIn(min = 24.dp)
                Box(modifier = mod.click(onClick = onAt), contentAlignment = Alignment.Center) {
                    Text(text = stringResource(R.string.cpd_menu_at), style = style)
                }
                if (!user.followed && iAmAdmin.not()) {
                    VerticalDivider(thickness = 0.5.dp, modifier = Modifier.height(24.dp), color = colorDivider)
                    Box(modifier = mod.click(onClick = onFollow), contentAlignment = Alignment.Center) {
                        Row(verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.Center) {
                            Icon(imageVector = ActionIcons.PlusBlack, contentDescription = "", tint = style.color)
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(text = stringResource(R.string.cpd_menu_follow), style = style)
                        }
                    }
                }
                VerticalDivider(thickness = 0.5.dp, modifier = Modifier.height(24.dp), color = colorDivider)
                Box(modifier = mod.click(onClick = onChat), contentAlignment = Alignment.Center) {
                    Row(verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.Center) {
                        Icon(imageVector = ActionIcons.Chat, contentDescription = "", tint = style.color)
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(text = stringResource(R.string.cpd_menu_chat), style = style)
                    }
                }
                if (iAmAdmin) {
                    if (targetHasMic) {
                        VerticalDivider(thickness = 0.5.dp, modifier = Modifier.height(24.dp), color = colorDivider)
                        Box(modifier = mod.click(onClick = {
                            onKickMic.invoke(user)
                        }), contentAlignment = Alignment.Center) {
                            Row(verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.Center) {
                                Icon(
                                    painter = painterResource(id = R.drawable.cpd_room_kick_mic),
                                    contentDescription = "",
                                    tint = style.color
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(text = stringResource(R.string.cpd_kick_mic), style = style)
                            }
                        }
                    } else {
                        VerticalDivider(thickness = 0.5.dp, modifier = Modifier.height(24.dp), color = colorDivider)
                        Box(modifier = mod.click(onClick = {
                            onInviteMic.invoke(user)
                        }), contentAlignment = Alignment.Center) {
                            Row(verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.Center) {
                                Icon(
                                    painter = painterResource(id = R.drawable.ic_cpd_invite_mic),
                                    contentDescription = "",
                                    tint = style.color
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(text = stringResource(R.string.cpd_room_give_mic), style = style)
                            }
                        }
                    }

                    VerticalDivider(thickness = 0.5.dp, modifier = Modifier.height(24.dp), color = colorDivider)
                    Box(modifier = mod.click(onClick = onMore), contentAlignment = Alignment.Center) {
                        Row(verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.Center) {
                            Icon(
                                painter = painterResource(id = R.drawable.cpd_room_more),
                                contentDescription = "",
                                tint = style.color
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(text = stringResource(R.string.cpd_room_more), style = style)
                        }
                    }
                }
            }
            Spacer(modifier = Modifier.height(20.dp))

//            user.tribe?.let { tribe ->
//                Row(
//                    modifier = Modifier
//                        .padding(horizontal = 12.dp)
//                        .click {
//                            onClickFamily(tribe.id)
//                        }
//                        .background(color = CpdColors.FFFFEAF0, shape = RoundedCornerShape(8.dp))
//                        .padding(4.dp),
//                    verticalAlignment = Alignment.CenterVertically
//                ) {
//                    ComposeImage(
//                        model = tribe.avatarUrl, modifier = Modifier
//                            .size(40.dp)
//                            .clip(RoundedCornerShape(4.dp))
//                    )
//                    Spacer(modifier = Modifier.width(8.dp))
//                    Text((tribe.name ?: ""), color = CpdColors.FF1D2129, fontSize = 14.sp, modifier = Modifier.weight(1f), maxLines = 1, overflow = TextOverflow.Ellipsis)
//                    Text("${tribe.memberCnt}人", fontSize = 12.sp, color = CpdColors.FF86909C)
//                    Spacer(modifier = Modifier.width(12.dp))
//                }
//                Spacer(modifier = Modifier.height(8.dp))
//            }
            GiftWallContent(
                lightUpCnt = user.giftWallStats.lightUpCnt,
                totalCnt = user.giftWallStats.totalCnt
            ) {
                onClickGiftWall()
            }
        }
        AvatarComposeView(
            user = user,
            modifier = Modifier
                .align(Alignment.TopCenter)
                .size(80.dp),
            onClick = onCheckProfile
        )

        if (user.isSelf.not()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 20.dp), horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = stringResource(id = R.string.cpd举报),
                    modifier = Modifier
                        .click(onClick = onReport)
                        .padding(16.dp),
                    style = TextStyle(color = Color(0xFF86909C), fontSize = 12.sp)
                )
                Text(
                    text = stringResource(R.string.cpd_check_profile),
                    modifier = Modifier
                        .click(onClick = onCheckProfile)
                        .padding(16.dp),
                    color = MaterialTheme.colorScheme.primary,
                    fontSize = 12.sp
                )
            }
        }
    }
}


@Composable
private fun RoomUserPanelHeaderState(
    userState: MutableState<AppUser>,
    currentRoom: VoiceLiveChatRoom,
    dialog: IDialog,
    onAt: EntityCallback<User>,
    onKickMic: UserCallback,//踢下麦
    onInviteMic: UserCallback,//邀请上麦
    onMore: OnClick,
    onClickGiftWall: OnClick,
    onClickFamily: EntityCallback<Int>,
    onAction: IVoiceLiveAction?,
) {
    val usr = userState.value
    val uid = usr.id
    val roomInfo = currentRoom.basicInfo
    val micInfo = currentRoom.micInfo

    val isMyselfAnim = roomInfo.isOwner || (roomInfo.isAdmin && !roomInfo.isRoomOwner(usr.id) && !roomInfo.isRoomAdmin(usr.id))

    val targetHasMic = micInfo.isInMicById(usr.id)

    var user by userState

    LaunchedEffect(key1 = roomInfo.id, key2 = uid) {
        UserManager.roomRepository.getRoomMemberInfo(roomInfo.id, uid)?.also {
            user = it
        }
    }

    RoomUserPanelHeader(
        user = user,
        onAt = {
            onAt(user)
        },
        onChat = {
            dialog.dismiss()
            onAction?.onNavigateTo(CupidRouters.C2CChat, mapOf("user" to user))
        },
        onReport = {
            dialog.dismiss()
            onAction?.onNavigateTo(CupidRouters.REPORT_START, mapOf("type" to ReportActivity.TYPE_USER, "id" to user.id))
        },
        onFollow = {
            onAction?.followUser(userState, true)
        },
        onCheckProfile = {
            dialog.dismiss()
            onAction?.navigateToProfile(uid)
        },
        onMore = onMore,
        onKickMic = onKickMic,
        onInviteMic = onInviteMic,
        iAmAdmin = isMyselfAnim,
        targetHasMic = targetHasMic,
        onClickGiftWall = onClickGiftWall,
        onClickFamily = onClickFamily
    )
}


@Composable
fun RoomUserPanelContainer(user: User, modifier: Modifier = Modifier, content: ComposeContent = {}) {
    Box(
        modifier = modifier
            .fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 20.dp)
                .background(MaterialTheme.colorScheme.inverseSurface, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                .navigationBarsPadding(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(68.dp))
            Text(
                text = user.nickname,
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.widthIn(max = 180.dp),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                color = MaterialTheme.colorScheme.inverseOnSurface
            )

            Row(
                modifier = Modifier
                    .padding(top = 4.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(4.dp, Alignment.CenterHorizontally),
                verticalAlignment = Alignment.CenterVertically
            ) {
                AgeGender(age = user.age, isBoy = user.isBoy)
                LevelComposeView(user = user)
            }

            content()
            Spacer(modifier = Modifier.navigationBarsPadding())
        }

        AvatarComposeView(
            user = user,
            modifier = Modifier
                .align(Alignment.TopCenter)
                .size(80.dp)
        )
    }
}

@Composable
fun MyProfile(user: User, dialog: IDialog, onAction: IVoiceLiveAction?) {
    RoomUserPanelContainer(user = user) {
        Spacer(modifier = Modifier.height(16.dp))
        val tags = remember(user.height, user.nativeProfile) {
            buildList {
                val np = user.nativeProfile
                if (np!=null && np.cityCode.name.isNotEmpty()) {
                    add(np.cityCode.name)
                }
                if (user.height > 0) {
                    add("${user.height}cm")
                }
                if (np!=null && np.job.code != "0") {
                    add(np.job.name)
                }
            }.filter { it.isNotEmpty() }.joinToString(separator = " | ")
        }
        Text(text = tags, color = MaterialTheme.colorScheme.inverseSurface.copy(0.5f), textAlign = TextAlign.Center)
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 32.dp, bottom = 20.dp), contentAlignment = Alignment.Center
        ) {
            CpdButton(
                modifier = Modifier.widthIn(min = 220.dp),
                text = stringResource(R.string.cpd_check_my_profile),
                onClick = {
                    dialog.dismiss()
                    onAction?.navigateToProfile(user.id)
                }
            )
        }
    }

}

@Composable
fun RoomInviteMic(user: User, onAccept: OnClick = {}, onRefuse: OnClick = {}) {
    RoomUserPanelContainer(
        user = user, modifier = Modifier.fillMaxWidth()
    ) {
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = stringResource(R.string.cpd_invite_up_mic),
            fontSize = 16.sp,
            color = MaterialTheme.colorScheme.inverseOnSurface
        )
        Spacer(modifier = Modifier.height(40.dp))
        CpdButton(
            text = stringResource(R.string.cpd_accept_invite),
            onClick = onAccept,
            modifier = Modifier.widthIn(min = 220.dp)
        )
        Text(
            text = stringResource(R.string.cpd_hard_refuse),
            fontSize = 16.sp,
            color = MaterialTheme.colorScheme.inverseOnSurface.copy(0.5f),
            textAlign = TextAlign.Center,
            modifier = Modifier
                .click(onClick = onRefuse)
                .widthIn(min = 220.dp)
                .padding(vertical = 10.dp)
        )
    }
}

@Preview
@Composable
private fun RoomInviteMicPreview() {
    PreviewCupidTheme {
        RoomInviteMic(user = userForPreview)
    }
}


@Composable
fun rememberInviteUserPanelState(voiceLiveHelper: VoiceLiveHelper): MutableState<User?> {
    val userState: MutableState<User?> = remember {
        mutableStateOf(null)
    }
    val user = userState.value
    if (user != null) {
        val loadingState = LocalContentLoading.current
        val scope = rememberCoroutineScope()
        val upMic = remember {
            {
                scope.launch {
                    loadingState.value = true
                    voiceLiveHelper.acceptUpMic()
                    loadingState.value = false
                    userState.value = null
                }
            }
        }
        val permissionLauncher = rememberPermissionLauncher {
            upMic()
        }
        AnimatedDialog(onDismiss = { userState.value = null }) {
            RoomInviteMic(user = user, onAccept = {
                permissionLauncher.launch(arrayOf(Manifest.permission.RECORD_AUDIO))
            }, onRefuse = {
                userState.value = null
            })
        }
    }
    return userState
}