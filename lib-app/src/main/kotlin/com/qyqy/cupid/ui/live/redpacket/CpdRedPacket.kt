package com.qyqy.cupid.ui.live.redpacket

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.focus.FocusState
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.viewModelScope
import com.qyqy.cupid.ui.dialog.AnimatedDialog
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.dialog.NormalDialog
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.FinishEffect
import com.qyqy.ucoo.compose.data.RedEnvelope
import com.qyqy.ucoo.compose.presentation.redpackage.RedPackageViewModel
import com.qyqy.ucoo.compose.presentation.redpackage.bean.FieldValue
import com.qyqy.ucoo.compose.presentation.redpackage.bean.RedPacketSetting
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.theme.colorWhite30Alpha
import com.qyqy.ucoo.compose.theme.colorWhite50Alpha
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class CpdRedPacketDetailDialog(
    private val redPacketId: Int,
    val viewModel: RedPackageViewModel,
) : NormalDialog<IDialogAction>() {


    private val updateStateFlow = MutableStateFlow<RedEnvelope>(RedEnvelope.Loading)

    fun update(state: RedEnvelope) {
        updateStateFlow.value = state
    }

    /**
     * [updateStateFlow]优先
     */
    private val stateFlow by lazy {
        val previewState = updateStateFlow.value
        if (previewState != RedEnvelope.Loading) {
            updateStateFlow
        } else {
            viewModel.getRedPacketStateFlow(redPacketId).combine(updateStateFlow) { f1: RedEnvelope, f2: RedEnvelope ->
                if (f2 == RedEnvelope.None || f2 == RedEnvelope.Loading) {
                    f1
                } else {
                    f2
                }
            }
        }.stateIn(viewModel.viewModelScope, SharingStarted.Eagerly, previewState)
    }

    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        val data by stateFlow.collectAsState()
        CpdRedPacketUI(data = data,
            onSnatch = {
                val info = data as RedEnvelope.Info
                viewModel.grabRedPacket(redPacketId, info.user.nickname)
            }, onThank = {
//                val info = data as RedEnvelope.Success
//                viewModel.thank(info.senderName, true)
                dialog.dismiss()
            }, onConfirm = {
                dialog.dismiss()
            })
    }
}


/**
 * 发红包编辑dialog
 */
internal class EditRedPacketDialog(private val viewModel: RedPackageViewModel, private val onChangeTarget: OnClick) :
    AnimatedDialog<IDialogAction>() {
    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        val scope = rememberCoroutineScope()
        LaunchedEffect(key1 = Unit) {
            scope.launch {
                viewModel.effect.collectLatest {
                    if (it is FinishEffect) {
                        dialog.dismiss()
                    }
                }
            }
        }
        CpdRedPackageScreen(
            setting = viewModel.settingFlow,
            delayValueFlow = viewModel.delayValue,
            getConditionFlow = viewModel.flowGetCondition,
            onClickDelayType = {
                viewModel.setDelayValue(it)
            },
            onClickOpenCondition = onChangeTarget
        ) { goldCount, rpCount, words ->
            val setting = viewModel.settingFlow.value
            if (goldCount < setting.minCoin) {
                toastRes(R.string.cpd_rp_tip_invalid_gold_count, setting.minCoin)
                return@CpdRedPackageScreen
            }
            if (rpCount < setting.minNumber || rpCount > setting.maxNumber) {
                toastRes(R.string.cpd_rp_hint_count, setting.minNumber, setting.maxNumber)
                return@CpdRedPackageScreen
            }
            viewModel.send(goldCount, rpCount, words)
        }
    }
}

@Preview
@Composable
private fun EditPreview() {
    val setting = MutableStateFlow(RedPacketSetting())
    val c = MutableStateFlow(0)
    val d = MutableStateFlow(0)

    CpdRedPackageScreen(setting = setting, getConditionFlow = c, delayValueFlow = d, onClickDelayType = {
    }, onPost = { _, _, _ ->

    })
}

@Composable
private fun CpdRedPackageScreen(
    setting: StateFlow<RedPacketSetting>,
    getConditionFlow: StateFlow<Int>,
    delayValueFlow: StateFlow<Int>,
    maxWordsLength: Int = 20,
    onClickDelayType: (Int) -> Unit,
    onClickOpenCondition: () -> Unit = {},
    onPost: (goldCount: Int, rpCount: Int, words: String) -> Unit,
) {
    val settingData = setting.collectAsState().value
    val minCoin = settingData.minCoin
    val goldCountTrigger = settingData.bannerCoin
    val minCount = settingData.minNumber
    val maxCount = settingData.maxNumber
    val delayTypes = settingData.delayTypes
    val delayValue by delayValueFlow.collectAsState()
    val bgModifier = Modifier
        .background(Color(0xFF411525), Shapes.small)
        .padding(12.dp, 16.dp)
        .fillMaxWidth(1f)
    val keyboardAsState by keyboardAsState()
    val defaultMessage = stringResource(id = R.string.cpd_rp_default_msg)
    val stMessage = remember {
        mutableStateOf(TextFieldValue())
    }
    //红包个数
    val stCount = remember {
        mutableStateOf(TextFieldValue())
    }
    val stGoldCount = remember {
        mutableStateOf(TextFieldValue())
    }
    val coinVisible by remember {
        derivedStateOf { stGoldCount.value.text.isNotEmpty() }
    }


    fun getNumber(st: MutableState<TextFieldValue>) = st.value.text.toIntOrNull() ?: 0
    val onClickSend = object : () -> Unit {
        override fun invoke() {
            val goldCount = getNumber(stGoldCount)
            val rpCount = getNumber(stCount)
            val words = stMessage.value.text.takeIf { it.isNotEmpty() } ?: defaultMessage
            onPost(goldCount, rpCount, words)
        }

    }

    fun createOnFocus(st: MutableState<TextFieldValue>): (FocusState) -> Unit {
        return {
            st.value = st.value.copy(text = st.value.text, selection = TextRange(st.value.text.length))
        }
    }

    val onGoldFocusChanged: (FocusState) -> Unit = {
        if (it.isFocused) {
            stGoldCount.value =
                stGoldCount.value.copy(text = stGoldCount.value.text, selection = TextRange(stGoldCount.value.text.length))
        }
    }
    val onCountFocusChanged = createOnFocus(stCount)
    Column(
        modifier = Modifier
            .fillMaxWidth(1f)
            .background(Color(0xFF1E0008), RoundedCornerShape(topEnd = 16.dp, topStart = 16.dp))
            .windowInsetsPadding(WindowInsets.navigationBars)
            .paint(
                painter = painterResource(id = R.drawable.cpd_bg_rp),
                contentScale = ContentScale.FillWidth,
                alignment = Alignment.TopCenter
            )
            .padding(horizontal = 24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        AppText(
            text = stringResource(R.string.cpd_title_redpackage),
            color = Color.White,
            modifier = Modifier
                .padding(vertical = 16.dp),
            fontSize = 16.sp
        )
        Spacer(modifier = Modifier.height(8.dp))
        Row(modifier = bgModifier, verticalAlignment = Alignment.CenterVertically) {
            Text(text = stringResource(R.string.cpd_rp_total_gold), fontSize = 14.sp, color = Color.White)
            TextFieldWithHint(
                modifier = Modifier.weight(1f),
                textFieldModifier = Modifier.onFocusChanged(onGoldFocusChanged),
                keyboardOptions = KeyboardOptions.Default.copy(keyboardType = KeyboardType.Number),
                textAlign = TextAlign.End,
                textFieldValueState = stGoldCount,
                hint = stringResource(R.string.cpd_rp_hint_100_gold, minCoin),
                inputDigit = true,
                maxLength = 9
            )
            AnimatedVisibility(visible = coinVisible) {
                Row {
                    Spacer(modifier = Modifier.width(4.dp))
                    Image(
                        painter = painterResource(id = R.drawable.ic_cpd_coin),
                        contentDescription = "coin",
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
        Row(
            modifier = Modifier
                .fillMaxWidth(1f)
                .padding(vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(id = R.drawable.icon_alert),
                contentDescription = "alert",
                modifier = Modifier.size(12.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = stringResource(R.string.cpd_format_rp_banner, goldCountTrigger),
                fontSize = 12.sp,
                color = colorWhite30Alpha
            )
        }
//        红包个数
        Row(modifier = bgModifier) {
            Text(text = stringResource(R.string.cpd_rp_rp_count), fontSize = 14.sp, color = Color.White)
            TextFieldWithHint(
                modifier = Modifier.weight(1f),
                maxLength = 2,
                textFieldModifier = Modifier.onFocusChanged(onCountFocusChanged),
                keyboardOptions = KeyboardOptions.Default.copy(keyboardType = KeyboardType.Number),
                textAlign = TextAlign.End,
                textFieldValueState = stCount,
                hint = stringResource(R.string.cpd_rp_hint_count, minCount, maxCount),
                inputDigit = true,
            )
        }
        Spacer(modifier = Modifier.height(12.dp))
        Box(modifier = bgModifier) {
            TextFieldWithHint(textFieldValueState = stMessage, hint = defaultMessage, maxLength = maxWordsLength)
        }
//        选择红包开始条件
        Text(
            text = stringResource(R.string.cpd_rp_condition),
            color = colorWhite30Alpha,
            fontSize = 14.sp,
            modifier = Modifier.padding(vertical = 24.dp)
        )
        val st by getConditionFlow.collectAsState()
        Row(
            modifier = Modifier
                .clickable(onClick = onClickOpenCondition)
                .then(bgModifier),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(text = stringResource(R.string.cpd_rp_user_can_open), color = Color.White)
            Spacer(modifier = Modifier.weight(1f))
            Text(
                text = stringResource(
                    id = when (st) {
                        Const.GrabType.ALL -> R.string.cpd_rp_all_people_can_get
                        Const.GrabType.ONLY_TRIBE_MEMBER -> R.string.cpd_rp_triber_can_get
                        else -> R.string.cpd_rp_ff_get
                    }
                ),
                color = colorWhite50Alpha,
            )
            Image(
                painter = painterResource(id = R.drawable.ic_arrow_end_v2),
                contentDescription = "",
                modifier = Modifier.size(14.dp)
            )
        }
        Spacer(modifier = Modifier.height(12.dp))
        Row {
            Spacer(modifier = Modifier.size(24.dp))
            delayTypes.forEachIndexed { index, it ->
                Row(
                    modifier = Modifier
                        .padding(vertical = 10.dp)
                        .clickable(onClick = {
                            onClickDelayType(it.value)
                        }),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(id = if (delayValue == it.value) R.drawable.ic_rb_selected else R.drawable.ic_rb_unselected),
                        modifier = Modifier.size(24.dp),
                        contentDescription = "rd_sel"
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(text = it.name, fontSize = 12.sp, color = if (delayValue == it.value) Color.White else colorWhite50Alpha)
                }
                if (index == 0) {
                    Spacer(modifier = Modifier.weight(1f))
                }
            }
            Spacer(modifier = Modifier.size(24.dp))
        }
        Spacer(modifier = Modifier.height(30.dp))
        Box(
            modifier = Modifier
                .size(260.dp, 48.dp)
                .paint(painterResource(id = R.drawable.cpd_rp_btn))
                .clickable(onClick = {
//                    LogUtils.d("redpacket","goldCount:${stGoldCount.value.text} => ${stGoldCount.value.text.toIntOrNull()}")
                    val goldCount = stGoldCount.value.text.toIntOrNull() ?: 0
                    val rpCount = stCount.value.text.toIntOrNull() ?: 0
                    val words = stMessage.value.text.takeIf { it.isNotEmpty() } ?: defaultMessage
                    onPost(goldCount, rpCount, words)
                }),
            contentAlignment = Alignment.Center
        ) {
            Text(text = stringResource(id = R.string.cpd_dispatch_redpackage))
        }
        Spacer(modifier = Modifier.size(8.dp))
        settingData.tips?.forEach {
            Text(text = it, color = colorWhite50Alpha, fontSize = 12.sp)
        }
        Spacer(modifier = Modifier.size(30.dp))
    }
}

@Composable
private fun TextFieldWithHint(
    modifier: Modifier = Modifier,
    textFieldModifier: Modifier = Modifier,
    textAlign: TextAlign = TextAlign.Start,
    textFieldValueState: MutableState<TextFieldValue>,
    hint: String,
    maxLength: Int = 500,
    keyboardOptions: KeyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Text),
    hintColor: Color = colorWhite50Alpha,
    textColor: Color = Color(0xFFFFE8AA),
    contentAlignment: Alignment = Alignment.CenterStart,
    inputDigit: Boolean = false,
) {
    val onChanged: (TextFieldValue) -> Unit = { fieldValue ->
        val text = fieldValue.text.let { if (inputDigit) it.filter { txt -> txt.isDigit() } else it }.take(maxLength)
        textFieldValueState.value = fieldValue.copy(text = text)
    }
    val hintVisible by remember {
        derivedStateOf { textFieldValueState.value.text.isEmpty() }
    }
    Box(modifier = modifier, contentAlignment = contentAlignment) {
        BasicTextField(
            value = textFieldValueState.value, onValueChange = onChanged,
            modifier = textFieldModifier
                .fillMaxWidth(1f),
            singleLine = true,
            keyboardOptions = keyboardOptions,
            textStyle = TextStyle.Default.merge(
                textAlign = textAlign,
                color = textColor,
                fontSize = 14.sp,
                lineHeight = 15.sp
            ),
            cursorBrush = SolidColor(Color.White)
        )
        if (hintVisible) {
            Text(
                text = hint,
                color = hintColor,
                textAlign = textAlign,
                lineHeight = 15.sp,
                fontSize = 14.sp,
                modifier = Modifier.fillMaxWidth(1f)
            )
        }
    }
}

@Composable
private fun keyboardAsState(): State<Boolean> {
    val isKeyboardOpen = remember { mutableStateOf(false) }
    val view = LocalView.current.rootView
    val focusManager = LocalFocusManager.current
    view.setOnApplyWindowInsetsListener { _, insets ->
        val compatInsets = WindowInsetsCompat.toWindowInsetsCompat(insets)
        val isVisible = compatInsets.getInsets(WindowInsetsCompat.Type.ime()).bottom > 0
        isKeyboardOpen.value = isVisible
        if (!isVisible) {
            focusManager.clearFocus(true)
        }
        insets
    }
    DisposableEffect(key1 = view) {
        onDispose {
            view.setOnApplyWindowInsetsListener(null)
        }
    }
    return isKeyboardOpen
}

data class CpdRPOpenConditionDialog(private val viewModel: RedPackageViewModel) : AnimatedDialog<IDialogAction>() {
    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        val valueList = viewModel.settingFlow.collectAsState().value.grabTypes
        CpdRPOpenCondition(valueList) {
            val setting = viewModel.settingFlow.value
            when (it) {
                Const.GrabType.ONLY_TRIBE_MEMBER -> {
                    if (setting.hasTribe.not()) {
                        toastRes(R.string.cpd_rp_toast_no_tribe)
                        return@CpdRPOpenCondition
                    }
                }

                // FIXME: 日区还没有亲友团
                Const.GrabType.ONLY_FAMILY_AND_FRIEND -> {
                    if (setting.hasRelationship.not()) {
                        toastRes(R.string.rp_toast_no_ff)
                        return@CpdRPOpenCondition
                    }
                }

                else -> {}
            }
            if (it != -1) {
                viewModel.setCondition(it)
            }
            dialog.dismiss()
        }
    }
}

@Composable
private fun CpdRPOpenCondition(valueList: List<FieldValue>, onItemClick: (Int) -> Unit = {}) {
    val itemModifier = Modifier
        .height(54.dp)
        .fillMaxWidth(1f)
    Column(
        modifier = Modifier
            .fillMaxWidth(1f)
            .background(Color.White, RoundedCornerShape(topEnd = 16.dp, topStart = 16.dp)),
    ) {
        Text(
            text = stringResource(R.string.cpd_rp_title_get_condition),
            modifier = itemModifier,
            lineHeight = 54.sp,
            color = MaterialTheme.colorScheme.onSecondary.copy(alpha = 0.5f),
            textAlign = TextAlign.Center
        )

        valueList.forEach {
            HorizontalDivider(color = Color(0xFFF0F0F0))
            Text(
                text = it.name,
                color = MaterialTheme.colorScheme.onSecondary,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(onClick = {
                        onItemClick.invoke(it.value)
                    })
                    .padding(16.dp)
            )
        }

        HorizontalDivider(color = Color(0xFFF0F0F0), thickness = 8.dp)
        Text(
            text = stringResource(id = R.string.cpd_cancel),
            color = MaterialTheme.colorScheme.onSecondary,
            textAlign = TextAlign.Center,
            modifier = itemModifier
                .clickable(onClick = { onItemClick.invoke(-1) })
                .padding(16.dp)
        )
        Spacer(modifier = Modifier.height(25.dp))
    }
}