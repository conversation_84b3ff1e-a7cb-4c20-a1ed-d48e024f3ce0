package com.qyqy.cupid.ui.live.redpacket

import android.os.Vibrator
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.ParagraphStyle
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.LineHeightStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.getSystemService
import com.overseas.common.sntp.SNTPManager
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.data.RedEnvelope
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import kotlinx.coroutines.delay


private val redPacketSize = DpSize(280.dp, 368.dp)


@Composable
private fun SnatchRedEnvelope(data: RedEnvelope.Info, onSnatch: () -> Unit = {}) {
    Box(modifier = Modifier.size(redPacketSize)) {
        Column(
            modifier = Modifier
                .size(redPacketSize)
                .paint(painterResource(id = R.drawable.cpd_bg_redpacket), contentScale = ContentScale.Crop)
                .padding(top = 32.dp, bottom = 20.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            CircleComposeImage(
                model = data.user.avatarUrl,
                modifier = Modifier
                    .size(64.dp)
                    .border(1.dp, Color.White, CircleShape),
            )
            Spacer(modifier = Modifier.height(20.dp))
            Text(
                text = buildAnnotatedString {
                    val text = stringResource(R.string.cpd_xxx的红包, data.user.nickname)
                    withStyle(
                        style = ParagraphStyle(
                            lineHeight = 24.sp, lineHeightStyle = LineHeightStyle(
                                LineHeightStyle.Alignment.Top, LineHeightStyle.Trim.None
                            )
                        )
                    ) {
                        withStyle(style = SpanStyle(fontSize = 18.sp)) {
                            append(text)
                        }
                    }
                    append(data.desc)
                },
                modifier = Modifier.padding(horizontal = 15.dp),
                color = Color.White,
                fontSize = 14.sp,
                textAlign = TextAlign.Center,
                maxLines = 3,
                overflow = TextOverflow.Ellipsis
            )
            Spacer(modifier = Modifier.weight(1f))
            if (data.ps.isNotEmpty()) {
                AppText(text = data.ps, color = Color.White, fontSize = 14.sp)
            }
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_cpd_coin),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(end = 5.dp)
                        .size(20.dp)
                )
                AppText(
                    text = stringResource(R.string.cpd_红包余额_金币, data.balance),
                    color = Color(0xFFFFE8AA),
                    fontSize = 14.sp
                )
            }
        }
        Column(
            modifier = Modifier
                .padding(bottom = 75.dp)
                .size(110.dp)
                .align(Alignment.BottomCenter),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            val activeTimestamp = data.activeTimestamp
            TimeIndicator(activeTimestamp, onSnatch)
        }
    }
}

@Composable
private fun TimeIndicator(activeTimestamp: Long, onSnatch: () -> Unit) {
    var activeTime by remember(activeTimestamp) {
        mutableStateOf("")
    }
    val lt = activeTimestamp.minus(SNTPManager.now())
    if (lt > 0) {
        LaunchedEffect(key1 = activeTimestamp, block = {
            var now = SNTPManager.now()
            while (activeTimestamp > now) {
                val left = activeTimestamp.minus(now)
                activeTime = formatTime(left)
                if (left > 1500) {
                    delay(1000)
                } else {
                    delay(left)
                    break
                }
                now = SNTPManager.now()
            }
            activeTime = ""
        })
    }
    if (activeTime.isNotEmpty()) {
        Column(
            modifier = Modifier
                .height(89.dp)
                .padding(horizontal = 8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(text = activeTime, color = Color(0xFF9F002F), fontSize = 20.sp, fontWeight = FontWeight.Bold)
            Text(text = "スタート", fontSize = 12.sp, color = Color(0xFF9F002F))
        }
    } else {
        val context = LocalContext.current
        Box(
            modifier = Modifier
                .size(89.dp)
                .noEffectClickable {
                    //倒计时状态震动反馈
                    if (activeTime.isNotEmpty()) {
                        context
                            .getSystemService<Vibrator>()
                            ?.vibrate(500L)
                    } else {
                        onSnatch()
                    }
                },
            contentAlignment = Alignment.Center
        ) {
            Text(text = "開けて", fontSize = 18.sp, color = Color(0xFF9F002F), fontWeight = FontWeight.Bold)
        }
    }
}

@Composable
private fun RedPacketLoading(text: String? = null) {
    Box(
        modifier = Modifier
            .size(redPacketSize),
        contentAlignment = Alignment.Center
    ) {

        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            CircularProgressIndicator(color = Color.White)
            if (text != null) {
                Text(text = text, color = Color.White, fontSize = 14.sp)
            }
        }
    }
}

@Composable
private fun SuccessResult(goldCount: Int, onConfirm: () -> Unit = {}) {
    Column(
        modifier = Modifier
            .size(280.dp, 368.dp)
            .paint(painterResource(id = R.drawable.bg_rp_result))
            .padding(top = 60.dp, bottom = 24.dp),
        verticalArrangement = Arrangement.SpaceBetween,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {

        AppText(text = "獲得おめでとう", color = Color(0xFFFF1E5B), fontSize = 16.sp, fontWeight = FontWeight.Medium)
        Text(
            text = "x $goldCount",
            fontSize = 32.sp,
            color = Color(0xFFFF1E5B),
            modifier = Modifier.padding(top = 26.dp),
            fontWeight = FontWeight.Bold
        )
        Box(
            modifier = Modifier
                .size(200.dp, 44.dp)
                .paint(painterResource(id = R.drawable.cpd_btn_redpacket))
                .noEffectClickable(onClick = onConfirm),
            contentAlignment = Alignment.Center
        ) {
            Text(text = stringResource(id = R.string.cpd_收下并表示感谢), color = Color(0xFFFF2440), fontSize = 16.sp)
        }
    }
}

@Composable
private fun FailedResult(onConfirm: () -> Unit = {}) {
    Column(
        modifier = Modifier
            .size(280.dp, 368.dp)
            .paint(painterResource(id = R.drawable.cpd_bg_rp_failed))
            .padding(top = 60.dp, bottom = 24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceBetween
    ) {
        val c = Color(0xFFFF1E5B)
        Text(text = "残念ですが", color = c, fontSize = 16.sp)
        Text(text = "ラッキーバッグはなくなりました", color = c, modifier = Modifier.padding(top = 32.dp))
        Box(
            modifier = Modifier
                .size(200.dp, 44.dp)
                .paint(painterResource(id = R.drawable.cpd_rp_btn))
                .noEffectClickable(onClick = onConfirm),
            contentAlignment = Alignment.Center
        ) {
            Text(text = "わかった", color = Color(0xFFFF2440), fontSize = 16.sp)
        }
    }
}

@Preview
@Composable
private fun FailedPreview() {
    FailedResult()
}

@Composable
private fun CommonResult(message: String, buttonText: String, onConfirm: () -> Unit = {}) {
    Column(
        modifier = Modifier
            .size(280.dp, 368.dp)
            .paint(painterResource(id = R.drawable.bg_rp_result))
            .padding(top = 60.dp, bottom = 24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceBetween
    ) {
        Spacer(modifier = Modifier.height(20.dp))

        AppText(
            text = message,
            color = Color(0xFFDA7314),
            fontSize = 16.sp,
            modifier = Modifier
                .padding(top = 20.dp)
                .padding(horizontal = 24.dp)
        )

        Box(
            modifier = Modifier
                .size(200.dp, 44.dp)
                .paint(painterResource(id = R.drawable.cpd_rp_btn))
                .noEffectClickable(onClick = onConfirm),
            contentAlignment = Alignment.Center
        ) {
            Text(text = buttonText, color = Color(0xFFFF2440), fontSize = 16.sp)
        }
    }
}

fun formatTime(time: Long): String {
    if (time <= 0) {
        return ""
    }
    val secondTime = time.plus(500).div(1000).toInt() // 四舍五入
    val minute = secondTime.div(60)
    val second = secondTime.rem(60)
    return "${if (minute > 9) minute else "0$minute"}:${if (second > 9) second else "0$second"}"
}

@Preview
@Composable
private fun RedEnvelopesPreview() {
    Column {
        SnatchRedEnvelope(
            RedEnvelope.Info(
                userForPreview,
                "恭喜发财，大吉大利",
                "本部落成员可抢",
                1000,
                System.currentTimeMillis().plus(30_000),
            )
        )
    }
}

@Preview
@Composable
private fun RPSuccessPreview() {
    SuccessResult(999)
}

@Composable
fun CpdRedPacketUI(data: RedEnvelope, onSnatch: () -> Unit, onThank: () -> Unit, onConfirm: () -> Unit) {
    when (data) {
        is RedEnvelope.Info -> {
            SnatchRedEnvelope(data, onSnatch)
        }

        RedEnvelope.Loading -> {
            RedPacketLoading()
        }

        RedEnvelope.None -> {
            RedPacketLoading(stringResource(R.string.loading_failed))
        }

        RedEnvelope.Failed -> {
            FailedResult(
                onConfirm
            )
        }

        RedEnvelope.Expired -> {
            CommonResult(
                message = stringResource(R.string.cpd_rp_expired),
                buttonText = stringResource(id = R.string.cpd_confirm),
                onConfirm
            )
        }

        is RedEnvelope.Success -> {
            SuccessResult(
                data.goldCount,
                onThank
            )
        }

        is RedEnvelope.AlreadyCollected -> {
            CommonResult(
                message = stringResource(R.string.cpd_rp_have_collected, data.coinCount),
                buttonText = stringResource(id = R.string.cpd_confirm),
                onConfirm
            )
        }
    }
}

@Preview
@Composable
private fun CommonResultPreview() {
    CommonResult(
        message = stringResource(R.string.cpd_rp_have_collected, 1),
        buttonText = stringResource(id = R.string.cpd_confirm),
        {}
    )
}