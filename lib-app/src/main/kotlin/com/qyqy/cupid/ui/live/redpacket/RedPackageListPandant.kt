package com.qyqy.cupid.ui.live.redpacket

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.cupid.ui.dialog.DialogQueue
import com.qyqy.cupid.ui.live.VoiceLiveViewModel
import com.qyqy.cupid.widgets.AlignHorizontalContainer
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.data.isGrabResult
import com.qyqy.ucoo.compose.data.isUncovered
import com.qyqy.ucoo.compose.presentation.redpackage.JaApiRedPackage
import com.qyqy.ucoo.compose.presentation.redpackage.RedPackageViewModel
import com.qyqy.ucoo.compose.presentation.redpackage.bean.RedPacketList
import com.qyqy.ucoo.compose.presentation.room.formatTime
import com.qyqy.ucoo.compose.ui.fixCurrentPage
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.im.compat.ConversationType
import com.qyqy.ucoo.im.compat.SendParams
import com.qyqy.ucoo.utils.EntityCallback
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import java.util.UUID

@Composable
private fun CpdRedPackagePendant(
    list: List<RedPacketList.RedPackageItem>,
    modifier: Modifier = Modifier,
    onItemClicked: EntityCallback<Int> = {},
) {
    val pagerState = rememberPagerState {
        list.size
    }

    if (list.size > 1) {
        LaunchedEffect(key1 = "pager-looper", block = {
            while (true) {
                delay(5000)
                try {
                    pagerState.animateScrollToPage((pagerState.fixCurrentPage + 1) % pagerState.pageCount)
                } catch (e: CancellationException) {
                    throw e
                } catch (_: Exception) {
                }
            }
        })
    }

    Box(
        modifier = modifier
            .width(52.dp)
            .heightIn(min = 64.dp)
    ) {
        if (pagerState.pageCount <= 0) {
            return
        }
        HorizontalPager(state = pagerState, modifier = Modifier.align(Alignment.BottomCenter)) {
            RedPackageComposItem(list[it], onItemClicked)
        }
        Box(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .height(20.dp)
                .widthIn(min = 20.dp)
                .background(Color(0xFFFD5840), RoundedCornerShape(10.dp))
                .border(0.5.dp, Color.White, RoundedCornerShape(10.dp)),
            contentAlignment = Alignment.Center
        ) {
            Text(text = "${pagerState.pageCount}", color = Color.White, fontSize = 12.sp, lineHeight = 20.sp)
        }
    }
}

@Composable
private fun RedPackageComposItem(item: RedPacketList.RedPackageItem, onItemClicked: EntityCallback<Int>) {
    Box(modifier = Modifier
        .size(48.dp, 54.dp)
        .click {
            onItemClicked.invoke(item.id)
        }) {
        Image(
            painter = painterResource(id = R.drawable.cpd_ic_small_rp),
            contentDescription = "",
            modifier = Modifier.size(48.dp)
        )
        val key1 = item.id
        var text by remember(key1 = key1) {
            mutableStateOf("")
        }
        var remainTime = item.getRemainDuration()
        if (remainTime > 0) {
            LaunchedEffect(key1 = key1, block = {
                while (true) {
                    remainTime = item.getRemainDuration()
                    text = formatTime(remainTime)
//                    LogUtil.d("remain text ---:$text,$remainTime")
                    if (remainTime > 0) {
                        val delay = if (remainTime % 1000 == 0L) {
                            1000
                        } else {
                            remainTime % 1000
                        }
                        delay(delay)
                    } else {
                        text = ""
                        break
                    }
                }
            })
        }
        if (text.isNotEmpty()) {
            Text(
                text = text, color = Color(0xFFFFCD5D), fontSize = 10.sp,
                lineHeight = 14.sp,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .width(46.dp)
                    .padding(horizontal = 3.dp)
                    .background(
                        Brush.verticalGradient(listOf(Color(0x80000000), Color(0xC9FF294F))),
                        shape = RoundedCornerShape(7.dp)
                    )
                    .border(0.5.dp, Color(0xFFFFCD5D), shape = RoundedCornerShape(7.dp))
            )
        }
    }

}

@Composable
@Preview
fun RedPacketListPreviewer() {
    CpdRedPackagePendant(mutableListOf(RedPacketList.RedPackageItem()))
}


@Composable
fun BoxScope.CpdRedPackagePendantFloat(
    viewModel: VoiceLiveViewModel,
    dialogQueue: DialogQueue<IVoiceLiveAction>,
    onAction: IVoiceLiveAction,
) {
    val roomId = viewModel.roomId
    val rpViewModel = androidx.lifecycle.viewmodel.compose.viewModel {
        RedPackageViewModel(
            roomId = roomId.toString(),
            sendParams = viewModel.sendParams,
            api = createApi<JaApiRedPackage>(),
            isJapan = true
        )
    }

    LaunchedEffect(key1 = roomId) {
        rpViewModel.rpEffect.collectLatest { effect ->
            if (effect is RedPackageViewModel.ShowDetailDialogEffect) {
                val rpId = effect.id
                val envelope = effect.state
                val replace = envelope.isGrabResult || envelope.isUncovered
                val d = CpdRedPacketDetailDialog(rpId, rpViewModel).also {
                    it.update(envelope)
                }
                if (replace) {
                    dialogQueue.dismiss {
                        it.key.startsWith("redpacket")
                    }
                }
                dialogQueue.pushWithKey(d, "redpacket-${UUID.randomUUID()}")
            }
        }
    }

    val data by rpViewModel.flowRedPacketList.collectAsState()

    val list = data.list

    //此时显示红包挂件
    AlignHorizontalContainer(
        visible = list.isNotEmpty(),
        modifier = Modifier
            .align(Alignment.BottomEnd)
            .padding(bottom = 20.dp),
        tag = "pendant_red"
    ) {
        CpdRedPackagePendant(
            list = list,
            modifier = Modifier.padding(end = 8.dp)
        ) {
            dialogQueue.pushWithKey(CpdRedPacketDetailDialog(it, rpViewModel), "redpacket-${UUID.randomUUID()}", true)
        }
    }

}