

package com.qyqy.cupid.ui.live.settings

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.ucoo.R
import com.qyqy.ucoo.asActivity
import com.qyqy.ucoo.compose.data.BackgroundProp
import com.qyqy.ucoo.compose.domain.usecase.room.FetchBackgroundListUseCase
import com.qyqy.ucoo.compose.domain.usecase.room.SetBackgroundUseCase
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.ComposeContentScale
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.vm.room.BackgroundSettingMvi
import com.qyqy.ucoo.compose.vm.room.BackgroundSettingViewModel
import com.qyqy.ucoo.im.room.RoomRepository
import com.qyqy.ucoo.toast
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

/**
 *  @time 8/28/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.live.settings
 */
@Composable
fun CupidBackgroundSettingScreenRouter(roomId: Int, disableTip: String?) {
    val viewModel = viewModel(initializer = {
        val repository = RoomRepository()
        BackgroundSettingViewModel(
            roomId,
            FetchBackgroundListUseCase(repository),
            SetBackgroundUseCase(repository),
        )
    })
    val uiState by viewModel.states.collectAsStateWithLifecycle()
    val navController = LocalAppNavController.current

    LaunchedEffect(key1 = Unit, block = {
        viewModel.effects.onEach { effect ->
            when (effect) {
                is BackgroundSettingMvi.ViewEffect.Toast -> {
                    toast(effect.msg)
                }

                is BackgroundSettingMvi.ViewEffect.Back -> {
                    navController.popBackStack()
                }
            }
        }.launchIn(this)
    })
    BackgroundSettingPage(uiState.selectIndex, uiState.list, disableTip, {
        viewModel.processEvent(BackgroundSettingMvi.ViewEvent.SelectBackground(it))
    }) {
        viewModel.processEvent(BackgroundSettingMvi.ViewEvent.SetBackground)
    }
}

@Composable
private fun BackgroundSettingPage(
    selectIndex: Int,
    propList: List<BackgroundProp>,
    disableTip: String? = null,
    onSelected: (Int) -> Unit = {},
    onSave: () -> Unit = {},
) {

    val enabled = disableTip.isNullOrBlank()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .systemBarsPadding(),
    ) {
        CupidAppBar(title = stringResource(id = R.string.cpd房间背景设置))
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 12.dp, bottom = 8.dp),
            contentAlignment = Alignment.Center
        ) {
            if (!enabled)
                AppText(
                    text = disableTip!!,
                    fontSize = 12.sp,
                    color = colorResource(id = R.color.white_alpha_50)
                )
        }

        LazyVerticalGrid(
            columns = GridCells.Fixed(2),
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .padding(bottom = 25.dp)
                .fillMaxWidth()
                .weight(1f)
                .selectableGroup(),
            contentPadding = PaddingValues(top = 10.dp, bottom = 10.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalArrangement = Arrangement.spacedBy(24.dp),
        ) {
            itemsIndexed(
                propList,
                key = { _, item ->
                    item.id
                },
            ) { index, item ->
                BackgroundItem(enabled, index == selectIndex, item) {
                    onSelected(index)
                }
            }
        }

        Button(
            onClick = onSave,
            enabled = enabled && selectIndex >= 0,
            modifier = Modifier
                .padding(start = 16.dp, end = 16.dp, bottom = 16.dp)
                .navigationBarsPadding()
                .fillMaxWidth()
                .height(48.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = CpdColors.FFFF5E8B,
                disabledContainerColor = CpdColors.FFFF5E8B.copy(0.8f),
                contentColor = Color.White,
                disabledContentColor = Color(0x80FFFFFF),
            )
        ) {
            Text(text = stringResource(id = R.string.cpd确认使用), fontSize = 15.sp)
        }
    }
}

@Composable
private fun BackgroundItem(enabled: Boolean, selected: Boolean, prop: BackgroundProp, onClick: () -> Unit) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(0.6875f),
            color = Color.Transparent,
            enabled = enabled,
            selected = selected,
            onClick = onClick,
            shape = Shapes.small,
            border = if (selected) BorderStroke(1.5.dp, CpdColors.FFFF5E8B) else null
        ) {
            ComposeImage(
                model = prop.icon,
                modifier = Modifier.fillMaxSize(),
                alignment = Alignment.BottomCenter,
                contentScale = ComposeContentScale.ProxyCrop,
                preview = painterResource(id = R.drawable.background_chat_room_for_pk_mode),
            )
        }

        Spacer(modifier = Modifier.height(12.dp))
        AppText(text = prop.name, fontSize = 14.sp, color = Color.White)
    }
}

@Preview(widthDp = 375, heightDp = 812)
@Composable
fun Preview() {
    var selectedIndex by remember {
        mutableIntStateOf(0)
    }
    BackgroundSettingPage(selectedIndex, List(5) {
        BackgroundProp(it, "默认")
    }, onSelected = {
        selectedIndex = it
    })
}