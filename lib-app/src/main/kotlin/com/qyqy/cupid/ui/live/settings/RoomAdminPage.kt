

package com.qyqy.cupid.ui.live.settings

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.ui.CupidMainActivity
import com.qyqy.cupid.ui.CupidViewModel
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.composeClick
import com.qyqy.cupid.widgets.state.CupidStateListView
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.state.StateViewModelWithIntPage
import com.qyqy.ucoo.compose.ui.AgeGender
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppearanceStatusBars
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.http.ApiException
import com.qyqy.ucoo.im.room.RoomRepository
import com.qyqy.ucoo.toast
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 *  @time 8/27/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.live.settings
 */

/**
 * 语音房列表
 *
 * @property roomId 语音房id
 */


//region 房间管理员
private class RoomAdminViewModel(val roomId: Int) : StateViewModelWithIntPage<AppUser>() {
    private val repo by lazy {
        RoomRepository()
    }

    private val _loading = MutableStateFlow(false)
    val loading = _loading.asStateFlow()

    override suspend fun loadData(pageNum: Int): Result<List<AppUser>> {
        val id = if (pageNum == firstPage) 0 else (listFlow.value.lastOrNull()?.relation_id ?: 0)
        val result = repo.getAdminList(roomId, id)
        return if (result.isSuccess) {
            Result.success(result.getOrNull()?.map { it as? AppUser }?.filterNotNull() ?: emptyList())
        } else {
            Result.failure<List<AppUser>>(result.exceptionOrNull() ?: ApiException(msg = "error")).also {
                toast(it.exceptionOrNull()?.message ?: "")
            }
        }
    }

    fun cancelAdmin(userId: Int) {
        viewModelScope.launch {
            _loading.emit(true)
            repo.updateRoomAdmin(roomId, userId, false).onSuccess {
                _loading.emit(false)
//                updateItem({ it.userId == userId }) {
//                    it.copy(isRoomAdmin = false)
//                }
                removeItems { it.userId == userId }
            }.onFailure {
                _loading.emit(false)
            }.toastError()
        }
    }
}

@Composable
fun VoiceRoomAdminListPage(roomId: Int) {
    val viewModel = viewModel(modelClass = RoomAdminViewModel::class.java, factory = viewModelFactory {
        initializer {
            RoomAdminViewModel(roomId)
        }
    })

    LaunchedEffect(key1 = Unit) {
        viewModel.refresh()
    }
    val activity = LocalContext.current as CupidMainActivity
    val cupidViewModel = viewModel(modelClass = CupidViewModel::class.java, viewModelStoreOwner = activity)
    val loadingFlag = LocalContentLoading.current

    LaunchedEffect(Unit) {
        viewModel.loading.collectLatest {
            loadingFlag.value = it
        }
    }

    BackHandler {
        cupidViewModel.voiceLiveHelper.expandRoom()
    }

    AppearanceStatusBars(isLight = true)

    Column() {
        CupidAppBar(title = stringResource(id = R.string.cpd_room_administrator), navigationIcon = {
            IconButton(composeClick {
                cupidViewModel.voiceLiveHelper.expandRoom()
            }) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_cpd_navigation_back),
                    contentDescription = "back",
                    tint = Color(0xFF1D2129),
                )
            }
        })
        CupidStateListView(viewModel = viewModel,
            empty = {
                EmptyView(text = stringResource(id = R.string.cpd_room_admin_empty))
            },
            keyProvider = { _, member -> member.id }) { member, _, _, _ ->
            UserItem(user = member) {
                AppButton(
                    text = stringResource(id = R.string.cpd_room_admin_cancel),
                    onClick = composeClick {
                        viewModel.cancelAdmin(member.userId)
                    },
                    enabled = (member as? AppUser)?.isRoomAdmin ?: true,
                    border = BorderStroke(1.dp, color = CpdColors.FFFF5E8B),
                    background = Color.White,
                    textStyle = TextStyle(color = CpdColors.FFFF5E8B, fontSize = 14.sp),
                    contentPadding = PaddingValues(horizontal = 12.dp),
                    modifier = Modifier
                        .height(32.dp)
                        .widthIn(80.dp)
                )
            }
        }
    }
}

//endregion

//region 房间黑名单

private class RoomBlackListViewModel(val roomId: Int) : StateViewModelWithIntPage<AppUser>() {
    private val repo by lazy {
        RoomRepository()
    }

    private val _loading = MutableStateFlow(false)
    val loading = _loading.asStateFlow()

    override suspend fun loadData(pageNum: Int): Result<List<AppUser>> {
        val id = if (pageNum == firstPage) 0 else (listFlow.value.lastOrNull()?.relation_id ?: 0)
        val result = repo.getBlackList(roomId, id)
        return if (result.isSuccess) {
            Result.success(result.getOrNull()?.mapNotNull { it as? AppUser } ?: emptyList())
        } else {
            Result.failure<List<AppUser>>(result.exceptionOrNull() ?: ApiException(msg = "error")).also {
                toast(it.exceptionOrNull()?.message ?: "")
            }
        }
    }

    fun cancelBlack(userId: Int) {
        viewModelScope.launch {
            _loading.emit(true)
            repo.updateRoomBlackList(roomId, userId, false).onSuccess {
                _loading.emit(false)
                removeItems({ it.userId == userId })
            }.onFailure {
                _loading.emit(false)
            }.toastError()
        }
    }
}

@Composable
fun VoiceRoomBlackListPage(roomId: Int) {
    val viewModel = viewModel(modelClass = RoomBlackListViewModel::class.java, factory = viewModelFactory {
        initializer {
            RoomBlackListViewModel(roomId)
        }
    })
    val activity = LocalContext.current as CupidMainActivity
    val cupidViewModel = viewModel(modelClass = CupidViewModel::class.java, viewModelStoreOwner = activity)
    val loadingFlag = LocalContentLoading.current

    LaunchedEffect(Unit) {
        viewModel.loading.collectLatest {
            loadingFlag.value = it
        }
    }

    AppearanceStatusBars(isLight = true)

    BackHandler {
        cupidViewModel.voiceLiveHelper.expandRoom()
    }
    LaunchedEffect(key1 = Unit) {
        viewModel.refresh()
    }
    Column() {
        CupidAppBar(title = stringResource(id = R.string.cpd_room_blacklist), navigationIcon = {
            IconButton(composeClick {
                cupidViewModel.voiceLiveHelper.expandRoom()
            }) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_cpd_navigation_back),
                    contentDescription = "back",
                    tint = Color(0xFF1D2129),
                )
            }
        })
        CupidStateListView(viewModel = viewModel,
            empty = {
                EmptyView(text = stringResource(id = R.string.cpd_room_blacklist_empty))
            },
            keyProvider = { _, member -> member.id }) { member, _, _, _ ->
            UserItem(user = member) {
                AppButton(
                    text = stringResource(id = R.string.cpd_room_blacklist_cancel),
                    onClick = composeClick {
                        viewModel.cancelBlack(member.userId)
                    },
                    enabled = member.inRoomBlackList ?: true,
                    border = BorderStroke(1.dp, color = CpdColors.FFFF5E8B),
                    background = Color.White,
                    textStyle = TextStyle(color = CpdColors.FFFF5E8B, fontSize = 14.sp),
                    contentPadding = PaddingValues(horizontal = 12.dp),
                    modifier = Modifier
                        .height(32.dp)
                        .widthIn(80.dp)
                )
            }
        }
    }
}

//endregion


@Composable
private fun EmptyView(text: String) {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally, verticalArrangement = Arrangement.Center
    ) {
        ComposeImage(model = R.drawable.ic_cpd_empty_voice)
        Spacer(modifier = Modifier.height(20.dp))
        Text(
            text = text, fontSize = 12.sp,
            color = CpdColors.FF86909C
        )
    }
}

@Composable
private fun UserItem(user: User, actions: @Composable () -> Unit) {
    Column {
        Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.padding(vertical = 10.dp)) {
            Spacer(modifier = Modifier.width(16.dp))
            CircleComposeImage(
                model = user.avatarUrl,
                modifier = Modifier
                    .size(48.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Column(
                modifier = Modifier.weight(1f),
            ) {
                Text(
                    user.nickname,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    fontSize = 16.sp, color = CpdColors.FF1D2129
                )
                Spacer(modifier = Modifier.height(10.dp))
                AgeGender(age = user.age, isBoy = user.isBoy)
            }
            actions()
            Spacer(modifier = Modifier.width(16.dp))
        }
        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .height(0.5.dp)
                .padding(horizontal = 16.dp)
                .background(CpdColors.FFF1F2F3)
        )
    }
}


@Composable
@Preview
fun RoomAdminPreView() {
    Column {
        UserItem(user = AppUser(nickname = "幼儿园班花")) {
            AppButton(
                text = "取消管理员",
                onClick = {

                },
                border = BorderStroke(1.dp, color = CpdColors.FFFF5E8B),
                background = Color.White,
                textStyle = TextStyle(color = CpdColors.FFFF5E8B, fontSize = 14.sp),
                contentPadding = PaddingValues(horizontal = 12.dp),
                modifier = Modifier
                    .height(32.dp)
                    .widthIn(80.dp)
            )
        }

        UserItem(user = AppUser(nickname = "幼儿园班花")) {
            AppButton(
                text = "取消拉黑",
                onClick = {

                },
                border = BorderStroke(1.dp, color = CpdColors.FFFF5E8B),
                background = Color.White,
                textStyle = TextStyle(color = CpdColors.FFFF5E8B, fontSize = 14.sp),
                contentPadding = PaddingValues(horizontal = 12.dp),
                modifier = Modifier
                    .height(32.dp)
                    .widthIn(80.dp)
            )
        }
    }
}

@Composable
@Preview
fun EmptyPreview() {
    EmptyView(text = "暂无管理员")
}