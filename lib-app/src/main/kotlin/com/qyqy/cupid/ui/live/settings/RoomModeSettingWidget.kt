package com.qyqy.cupid.ui.live.settings

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.dialog.ContentAlertDialog
import com.qyqy.cupid.ui.dialog.DialogButton
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.live.RoomMode
import com.qyqy.cupid.ui.live.VoiceLiveChatRoom
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.AppButton

@Composable
fun RoomModePanel(room: VoiceLiveChatRoom, onDismiss: () -> Unit = {}, onConfirm: (RoomMode) -> Unit = {}) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = Color(0xFF292929),
                shape = RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp)
            )
            .padding(bottom = 20.dp)
            .navigationBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        var selectedMode by remember {
            mutableStateOf(room.settings.roomMode)
        }

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(54.dp),
            contentAlignment = Alignment.CenterStart
        ) {
            IconButton(onClick = onDismiss) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_cpd_navigation_back),
                    contentDescription = null,
                    tint = Color.White
                )
            }
            Text(
                text = stringResource(id = R.string.cpd_room_mode),
                modifier = Modifier.align(Alignment.Center),
                color = Color.White,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
        }

        Spacer(modifier = Modifier.height(36.dp))

        RoomMode.entries.forEach {
            if (it != RoomMode.COUPLE_MIC_MODE) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp)
                        .clickable {
                            selectedMode = it
                        }
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = it.roomName,
                        modifier = Modifier.weight(1f),
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )

                    Image(
                        painter = if (selectedMode == it) {
                            painterResource(id = R.drawable.ic_cpd_checked)
                        } else {
                            painterResource(id = R.drawable.ic_un_checked)
                        },
                        contentDescription = null,
                        modifier = Modifier.size(22.dp),
                        contentScale = ContentScale.Crop
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(40.dp))

        val dialogQueue = LocalDialogQueue.current
        val context = LocalContext.current
        AppButton(
            text = stringResource(id = R.string.cpd确认),
            modifier = Modifier.size(311.dp, 48.dp),
            fontSize = 16.sp,
            maxLines = 1,
        ) {
            if (room.settings.roomMode == selectedMode) {
                onDismiss()
            } else {
                if (room.isPkRoom) {
                    dialogQueue.pushCenterDialog(immediatelyShow = true) { dialog, onAction ->
                        ContentAlertDialog(
                            content = context.getString(R.string.cpdpk模式切换),
                            startButton = DialogButton(stringResource(id = R.string.cpd取消)) {
                                dialog.dismiss()
                            },
                            endButton = DialogButton(stringResource(id = R.string.cpd确认)) {
                                dialog.dismiss()
                                onConfirm(selectedMode)
                            },
                        )
                    }
                } else {
                    onConfirm(selectedMode)
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewRoomModePanel() {
    PreviewCupidTheme {
        RoomModePanel(VoiceLiveChatRoom.preview)
    }
}