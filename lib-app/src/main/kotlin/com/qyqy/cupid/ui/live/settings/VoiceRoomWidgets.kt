@file:OptIn(ExperimentalLayoutApi::class)

package com.qyqy.cupid.ui.live.settings

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.overseas.common.utils.isPreviewOnCompose
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.ui.live.BasicRoomInfo
import com.qyqy.cupid.ui.live.VoiceLiveChatRoom
import com.qyqy.cupid.widgets.click
import com.qyqy.cupid.widgets.composeClick
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.VerticalGrid
import com.qyqy.ucoo.im.room.RoomRepository
import kotlinx.coroutines.launch

/**
 *  @time 8/27/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.live.settings
 */
//region 语聊房设置_房间管理
/**
 * 房间设置的面板
 * 1. 房间管理 -> RoomAdminPage
 * 2. 上麦管理
 * 3. 举报房间 -> ReportStartPage
 * 4. 拉黑房间
 * 5. 锁定房间(需要绘制UI)
 * 6. 关闭礼物特效
 * 7. 房间背景 -> CupidBackedSettingPage
 * 8. 收起房间
 * 9. 退出房间
 *
 */

sealed interface VoiceRoomAction {
    data object NavigateBlackList : VoiceRoomAction
    data object NavigateAdministrator : VoiceRoomAction
    data class UpdateRoomName(val newTitle: String) : VoiceRoomAction
    data object RefreshShuffleTitle : VoiceRoomAction
    data object EditNotice : VoiceRoomAction
}

sealed class VoiceRoomSettingPanelItem(
    @StringRes val title: Int,
    @DrawableRes val drawableRes: Int,
) {

    data object RoomAdmin : VoiceRoomSettingPanelItem(R.string.cpd_room_manage, R.drawable.ic_cpd_voice_settings)
    data object RoomMode : VoiceRoomSettingPanelItem(R.string.cpd_room_mode, R.drawable.ic_cpd_voice_mode)
    data object ReportRoom : VoiceRoomSettingPanelItem(R.string.cpd_room_report, R.drawable.ic_cpd_voice_report)
    data object BlackRoom : VoiceRoomSettingPanelItem(R.string.cpd_room_black, R.drawable.ic_cpd_voice_blackroom)
    data object LockRoom : VoiceRoomSettingPanelItem(R.string.cpd_room_lock, R.drawable.ic_cpd_voice_lock)
    data object UnLockRoom : VoiceRoomSettingPanelItem(R.string.cpd_room_unlock, R.drawable.ic_cpd_voice_unlock)
    data object CollapseRoom : VoiceRoomSettingPanelItem(R.string.cpd_room_collapse, R.drawable.ic_cpd_voice_expand)
    data object ExitRoom : VoiceRoomSettingPanelItem(R.string.cpd_room_exit, R.drawable.ic_cpd_voice_exit)

}



@Composable
fun VoiceRoomSettingPanel(roomInfo: VoiceLiveChatRoom, onAction: (VoiceRoomSettingPanelItem) -> Unit = {}) {
    val items = remember(roomInfo) {

        val isVisible = !roomInfo.isCpRoom && (roomInfo.basicInfo.isOwner || roomInfo.basicInfo.isAdmin)
        buildList {
            if (isVisible) {
                add(VoiceRoomSettingPanelItem.RoomAdmin)
                add(VoiceRoomSettingPanelItem.RoomMode)
            }
            add(VoiceRoomSettingPanelItem.ReportRoom)

            if (!roomInfo.isCpRoom && !roomInfo.basicInfo.isOwner) {
                add(VoiceRoomSettingPanelItem.BlackRoom)
            }

            if (isVisible) {
                if (roomInfo.settings.locked) {
                    add(VoiceRoomSettingPanelItem.UnLockRoom)
                } else {
                    add(VoiceRoomSettingPanelItem.LockRoom)
                }
            }

            add(VoiceRoomSettingPanelItem.CollapseRoom)

            add(VoiceRoomSettingPanelItem.ExitRoom)
        }
    }

    Column(
        modifier =
        Modifier
            .fillMaxWidth()
            .background(
                color = Color(0xFF292929),
                shape = RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp)
            )
            .padding(vertical = 16.dp)
            .navigationBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            stringResource(id = R.string.cpd_room_settings),
            color = Color.White,
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.height(36.dp))
        VerticalGrid(
            modifier = Modifier.fillMaxWidth(),
            verticalSpace = 20.dp,
            columns = 4,
        ) {
            items.forEach {
                Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier.click {
                    onAction(it)
                }) {
                    ComposeImage(model = it.drawableRes, modifier = Modifier.size(48.dp))
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(stringResource(id = it.title), color = CpdColors.FFF1F2F3, fontSize = 12.sp)
                }
            }
        }
        Spacer(modifier = Modifier.height(36.dp))
    }
}

@Composable
@Preview
private fun VoiceRoomSettingPanelPreview() {
    VoiceRoomSettingPanel(
        VoiceLiveChatRoom.preview.copy(
            basicInfo = BasicRoomInfo(1, "一起打遊戲嗎", "https://img.cdn.zhiyun-tech.com", "", AppUser(), listOf(), listOf())
        )
    )
}

//endregion

//region 房间信息管理面板

@Composable
fun VoiceRoomInfoSetting(settings: VoiceLiveChatRoom, onAction: (VoiceRoomAction) -> Unit = {}) {
    var selectedIdx by rememberSaveable {
        mutableStateOf(0)
    }

    if (selectedIdx == 0) {
        Column(
            modifier = Modifier
                .background(color = Color.White, shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                .padding(vertical = 16.dp)
                .fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(stringResource(id = R.string.cpd_room_settings), fontSize = 16.sp, color = CpdColors.FF1D2129)
            Spacer(modifier = Modifier.height(20.dp))
            VoiceRoomSettingItem(
                title = stringResource(id = R.string.cpd_room_name),
                content = { Text(settings.basicInfo.title, fontSize = 12.sp, color = CpdColors.FF86909C) },
                {
//                onAction(VoiceRoomAction.UpdateRoomName)
                    selectedIdx = 1
                })
            //公告
            if (settings.basicInfo.isOwner) {
                HorizontalDivider(modifier = Modifier.padding(horizontal = 16.dp), thickness = 0.5.dp, color = CpdColors.FFF1F2F3)
                VoiceRoomSettingItem(title = stringResource(id = R.string.cpd房间公告), onAction = {
                    onAction(VoiceRoomAction.EditNotice)
                })
            }
            if (settings.basicInfo.isOwner) {
                HorizontalDivider(modifier = Modifier.padding(horizontal = 16.dp), thickness = 0.5.dp, color = CpdColors.FFF1F2F3)
                VoiceRoomSettingItem(title = stringResource(id = R.string.cpd_room_administrator), onAction = {
                    onAction(VoiceRoomAction.NavigateAdministrator)
                })
            }
            HorizontalDivider(modifier = Modifier.padding(horizontal = 16.dp), thickness = 0.5.dp, color = CpdColors.FFF1F2F3)
            VoiceRoomSettingItem(title = stringResource(id = R.string.cpd_room_blacklist_admin), onAction = {
                onAction(VoiceRoomAction.NavigateBlackList)
            })

            Spacer(modifier = Modifier.height(20.dp))
        }
    } else {
        VoiceRoomTitleEditPanel(settings.basicInfo.title) {
            onAction(it)
        }
    }
}


/**
 * 语音房标题编辑面板
 * 这个写的太臭了, 得想个办法把事件往上提一提
 *
 * @param title 房间标题
 * @param onAction 时间传递
 */
@Composable
fun VoiceRoomTitleEditPanel(title: String, onAction: (VoiceRoomAction) -> Unit = {}) {
    var displayText by remember {
        mutableStateOf(title)
    }

    val repo by remember {
        mutableStateOf(if (isPreviewOnCompose) null else RoomRepository())
    }

    Column(
        modifier = Modifier
            .background(color = Color.White, shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .padding(vertical = 16.dp)
            .fillMaxWidth()
            .imePadding()
            .navigationBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        val focusManager = LocalFocusManager.current

        val focusRequester = remember {
            FocusRequester()
        }

        var hasFocus by remember { mutableStateOf(false) }

        val softwareKeyboardController = LocalSoftwareKeyboardController.current

        val scope = rememberCoroutineScope()

        Text(stringResource(id = R.string.cpd_room_title_tip), fontSize = 16.sp, color = CpdColors.FF1D2129)
        Spacer(modifier = Modifier.height(20.dp))

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .background(Color(0xFFFAFAFA), Shapes.corner12)
                .padding(12.dp)
                .click(noEffect = false) {
                    if (!hasFocus) {
                        focusRequester.requestFocus()
                    }
                }
        ) {
            BasicTextField(
                value = displayText,
                onValueChange = { displayText = it.take(10) },
                singleLine = true,
                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                keyboardActions = KeyboardActions(onDone = {
                    if (hasFocus) {
                        focusManager.clearFocus(true)
                    } else {
                        softwareKeyboardController?.hide()
                    }
                    onAction(VoiceRoomAction.UpdateRoomName(displayText))
                }),
                modifier = Modifier
                    .align(Alignment.Center)
                    .focusRequester(focusRequester)
                    .onFocusChanged {
                        hasFocus = it.isFocused
                    },
                textStyle = MaterialTheme.typography.bodyMedium.copy(
                    lineHeight = 18.sp,
                    fontSize = 14.sp,
                    textAlign = TextAlign.Center,
                    color = CpdColors.FF1D2129,
                ),

                cursorBrush = SolidColor(CpdColors.FFFF5E8B),
                decorationBox = { fn ->
                    fn.invoke()
                    if (displayText.isEmpty()) {
                        Text(
                            text = stringResource(id = R.string.cpd_room_title_hint),
                            style = MaterialTheme.typography.labelMedium,
                            color = CpdColors.FF86909C,
                        )
                    }
                }
            )
            Icon(painter = painterResource(id = R.drawable.ic_cpd_voice_shuffle_title),
                contentDescription = "",
                tint = Color(0xFF86909C),
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .click {
                        scope.launch {
                            val shuffleName = repo?.getRandomRoomName()
                            shuffleName?.let {
                                displayText = it
                            }
                        }
//                        onAction(VoiceRoomAction.RefreshShuffleTitle)
                    }
            )
        }

        Spacer(modifier = Modifier.height(40.dp))
        AppButton(text = stringResource(id = R.string.cpd_room_submit_confirm),
            color = Color.White,
            background = CpdColors.FFFF5E8B,
            contentPadding = PaddingValues(horizontal = 12.dp),
            modifier = Modifier
                .widthIn(min = 220.dp)
                .height(44.dp),
            onClick = composeClick {
                if (hasFocus) {
                    focusManager.clearFocus(true)
                } else {
                    softwareKeyboardController?.hide()
                }
                onAction(VoiceRoomAction.UpdateRoomName(displayText))
            })
        Spacer(modifier = Modifier.height(20.dp))

//        LaunchedEffect(key1 = Unit) {
//            if (hasFocus) {
//                softwareKeyboardController?.show()
//            } else {
//                focusRequester.requestFocus()
//            }
//        }
    }
}

@Composable
private fun VoiceRoomSettingItem(title: String, content: @Composable () -> Unit = {}, onAction: () -> Unit = {}) {
    Row(modifier = Modifier
        .click { onAction() }
        .fillMaxWidth()
        .height(56.dp)
        .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(title, fontSize = 16.sp, color = CpdColors.FF1D2129, modifier = Modifier.weight(1f))
        content()
        Icon(Icons.AutoMirrored.Filled.KeyboardArrowRight, contentDescription = "", tint = Color(0xFF86909C))
    }
}

@Composable
@Preview(showBackground = true)
private fun VoiceRoomTitlePreview() {
    VoiceRoomTitleEditPanel("")
}

@Composable
@Preview(showBackground = true)
private fun VoiceRoomInfoPreview() {
    VoiceRoomInfoSetting(
        VoiceLiveChatRoom.preview.copy(
            basicInfo = BasicRoomInfo(1, "一起打遊戲嗎", "https://img.cdn.zhiyun-tech.com", "", AppUser(), listOf(), listOf())
        )
    )
}

//endregion