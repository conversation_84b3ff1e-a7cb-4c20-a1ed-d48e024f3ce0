package com.qyqy.cupid.ui.login

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.OffsetMapping
import androidx.compose.ui.text.input.TransformedText
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.AppBasicTextField
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.CommonButton

@Composable
fun LoginButton(
    text: String,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    onClick: () -> Unit = { },
) {
    CommonButton(
        modifier = modifier.height(56.dp),
        gradient = if (enabled) Brush.horizontalGradient(listOf(Color(0xFFFF5E8B), Color(0xFFFF4000)))
        else Brush.horizontalGradient(listOf(Color(0x90FF5E8B), Color(0x80FF4000))),
        shape = CircleShape,
        enabled = enabled,
        onClick = onClick,
    ) {
        AppText(
            text = text,
            color = if (enabled) colorResource(id = R.color.white) else colorResource(id = R.color.white_alpha_50),
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
        )
    }
}


@Composable
internal fun LoginInputCard(
    modifier: Modifier,
    prefix: String,
    content: String,
    hintValue: String = "",
    selection: TextRange? = null,
    enabled: Boolean = true,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    onValueChange: (String) -> Unit = {},
) {
    Row(
        modifier = modifier
            .clip(CircleShape)
            .background(colorResource(id = R.color.FFF1F2F3))
            .padding(horizontal = 24.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(text = prefix, fontSize = 16.sp, color = colorResource(id = R.color.FF86909C))
        AppBasicTextField(
            value = content,
            onValueChange = onValueChange,
            modifier = Modifier.fillMaxWidth(),
            hintValue = hintValue,
            selection = selection,
            enabled = enabled,
            singleLine = true,
            textStyle = TextStyle(
                color = colorResource(id = R.color.FF4E5969),
                textAlign = TextAlign.End,
                letterSpacing = 1.sp,
                fontSize = 16.sp,
            ),
            hintStyle = TextStyle(
                color = colorResource(id = R.color.FF86909C),
                textAlign = TextAlign.End,
                letterSpacing = 1.sp,
                fontSize = 16.sp,
            ),
            cursorBrush = SolidColor(Color.White),
            keyboardOptions = keyboardOptions,
            keyboardActions = keyboardActions,
        )
    }
}

@Composable
internal fun LoginInputNumberCard(
    modifier: Modifier,
    prefix: String,
    content: String,
    hintValue: String = stringResource(id = R.string.手机号码),
    enabled: Boolean = true,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    onValueChange: (String) -> Unit = {},
) {
    val mask = "000 0000 0000"
    val maskNumber = '0'
    val pattern = remember { Regex("^\\d+\$") }
    Row(
        modifier = modifier
            .clip(CircleShape)
            .background(colorResource(id = R.color.FFF1F2F3))
            .padding(horizontal = 24.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(text = prefix, fontSize = 16.sp, color = colorResource(id = R.color.FF86909C))
        AppBasicTextField(
            value = content,
            onValueChange = { value ->
                if (value.isEmpty() || value.replace("\\s".toRegex(), "").matches(pattern)) {
                    val newValue = value.take(mask.count { it == maskNumber })
                    if (newValue != content) {
                        onValueChange(newValue)
                    }
                }
            },
            modifier = Modifier.fillMaxWidth(),
            hintValue = hintValue,
            enabled = enabled,
            singleLine = true,
            textStyle = TextStyle(
                color = colorResource(id = R.color.FF4E5969),
                textAlign = TextAlign.End,
                letterSpacing = 1.sp,
                fontSize = 16.sp,
            ),
            hintStyle = TextStyle(
                color = colorResource(id = R.color.FF86909C),
                textAlign = TextAlign.End,
                letterSpacing = 1.sp,
                fontSize = 16.sp,
            ),
            cursorBrush = SolidColor(Color.White),
            visualTransformation = com.qyqy.ucoo.compose.presentation.login.PhoneVisualTransformation(mask, maskNumber),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number, imeAction = ImeAction.Done),
            keyboardActions = keyboardActions,
        )
    }
}

class PhoneVisualTransformation(val mask: String, val maskNumber: Char) : VisualTransformation {

    private val maxLength = mask.count { it == maskNumber }

    override fun filter(text: AnnotatedString): TransformedText {
        val trimmed = if (text.length > maxLength) text.take(maxLength) else text

        val annotatedString = buildAnnotatedString {
            if (trimmed.isEmpty()) return@buildAnnotatedString

            var maskIndex = 0
            var textIndex = 0
            while (textIndex < trimmed.length && maskIndex < mask.length) {
                if (mask[maskIndex] != maskNumber) {
                    val nextDigitIndex = mask.indexOf(maskNumber, maskIndex)
                    append(mask.substring(maskIndex, nextDigitIndex))
                    maskIndex = nextDigitIndex
                }
                append(trimmed[textIndex++])
                maskIndex++
            }
        }

        return TransformedText(annotatedString, PhoneOffsetMapper(mask, maskNumber))
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is PhoneVisualTransformation) return false
        if (mask != other.mask) return false
        if (maskNumber != other.maskNumber) return false
        return true
    }

    override fun hashCode(): Int {
        return mask.hashCode()
    }
}

private class PhoneOffsetMapper(val mask: String, val numberChar: Char) : OffsetMapping {

    override fun originalToTransformed(offset: Int): Int {
        var noneDigitCount = 0
        var i = 0
        while (i < offset + noneDigitCount) {
            if (mask[i++] != numberChar) noneDigitCount++
        }
        return offset + noneDigitCount
    }

    override fun transformedToOriginal(offset: Int): Int =
        offset - mask.take(offset).count { it != numberChar }
}
