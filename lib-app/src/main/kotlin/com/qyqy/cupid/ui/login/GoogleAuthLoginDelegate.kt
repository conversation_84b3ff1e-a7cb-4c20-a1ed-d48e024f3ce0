package com.qyqy.cupid.ui.login

import com.qyqy.ucoo.AppUserPartition
import androidx.credentials.CredentialManager
import androidx.credentials.GetCredentialRequest
import androidx.credentials.GetCredentialResponse
import androidx.credentials.exceptions.GetCredentialCancellationException
import androidx.credentials.exceptions.GetCredentialException
import androidx.credentials.exceptions.NoCredentialException
import androidx.fragment.app.FragmentActivity
import com.google.android.libraries.identity.googleid.GetGoogleIdOption
import com.google.android.libraries.identity.googleid.GetSignInWithGoogleOption
import com.google.android.libraries.identity.googleid.GoogleIdTokenCredential
import com.overseas.common.utils.EncryptionUtils
import com.qyqy.ucoo.BuildConfig
import com.qyqy.ucoo.R
import com.qyqy.ucoo.login.third.AuthLoginType
import com.qyqy.ucoo.login.third.IAuthLoginDelegate
import com.qyqy.ucoo.toast

class GoogleAuthLoginDelegate(private val activity: FragmentActivity, private val serverClientId: String) : IAuthLoginDelegate {

    override val type: AuthLoginType = AuthLoginType.GOOGLE
    private val signupWithGoogleOption by lazy(LazyThreadSafetyMode.NONE) {
        // 文档说当登录凭证找不到时用注册的方式
        GetGoogleIdOption.Builder()
            .setFilterByAuthorizedAccounts(false)
            .setServerClientId(BuildConfig.GOOGLE_WEB_CLIENT_ID)
            .build()
    }

    suspend fun googleSignIn(signIn: Boolean = true): GetCredentialResponse? {
        val signInWithGoogleOption = GetSignInWithGoogleOption.Builder(BuildConfig.GOOGLE_WEB_CLIENT_ID)
            .setNonce(EncryptionUtils.generateRandomString())
            .build()

        val request: GetCredentialRequest = GetCredentialRequest.Builder()
            .addCredentialOption(if (signIn) signInWithGoogleOption else signupWithGoogleOption)
            .build()

        try {
            val result = CredentialManager.create(activity).getCredential(
                request = request,
                context = activity,
            )
            return result
        } catch (e: GetCredentialException) {
            return handleFailure(signIn, e)
        }
        return null
    }

    private suspend fun handleFailure(signIn: Boolean, e: GetCredentialException): GetCredentialResponse? {
        if (e is GetCredentialCancellationException) {
            throw e
        } else if (signIn && e is NoCredentialException) {
            return googleSignIn(false)
        } else {
            val msg = "google signIn error(${e.javaClass.simpleName}): ${e.message}"
            toast(msg)
            return null;
        }
    }

    override suspend fun startAuth(): String? {
        return try {
            val result = googleSignIn(true) ?: return null
            val credential = result.credential
            val googleIdTokenCredential = GoogleIdTokenCredential.createFrom(credential.data)
            googleIdTokenCredential.idToken
        } catch (e: GetCredentialException) {
            if (e is GetCredentialCancellationException) {
                throw IllegalStateException(if (AppUserPartition.isUCOO) {
                    activity.getString(R.string.绑定已取消)
                } else{
                    activity.getString(R.string.cpd绑定已取消)
                })
            } else {
                throw e
            }
        } catch (e: Throwable) {
            throw e
        }
    }
}