package com.qyqy.cupid.ui.login

import android.content.Context
import android.content.Intent
import androidx.activity.result.contract.ActivityResultContract
import com.linecorp.linesdk.Scope
import com.linecorp.linesdk.auth.LineAuthenticationParams
import com.linecorp.linesdk.auth.LineLoginApi
import com.linecorp.linesdk.auth.LineLoginResult
import com.qyqy.ucoo.R

/**
 *  @time 8/15/24
 *  <AUTHOR>
 *  @package com.qyqy.ucoo.compose.presentation.login
 */
class LineLoginContract : ActivityResultContract<Unit, LineLoginResult>() {
    override fun createIntent(context: Context, input: Unit): Intent {
        return LineLoginApi.getLoginIntent(
            context,
            context.getString(R.string.line_channel_id),
            LineAuthenticationParams.Builder().scopes(listOf(Scope.PROFILE)).build()
        )
    }

    override fun parseResult(resultCode: Int, intent: Intent?): LineLoginResult {
        return LineLoginApi.getLoginResultFromIntent(intent)
    }
}

interface LineLoginCallback {
    fun onSuccess(result: LineLoginResult)
}

class LineLoginCallbackContract : ActivityResultContract<LineLoginCallback, Unit>() {
    private var callback: LineLoginCallback? = null
    override fun createIntent(context: Context, input: LineLoginCallback): Intent {
        this.callback = input
        return LineLoginApi.getLoginIntent(
            context,
            context.getString(R.string.line_channel_id),
            LineAuthenticationParams.Builder().scopes(listOf(Scope.PROFILE)).build()
        )
    }

    override fun parseResult(resultCode: Int, intent: Intent?) {
        callback?.onSuccess(LineLoginApi.getLoginResultFromIntent(intent))
    }
}
