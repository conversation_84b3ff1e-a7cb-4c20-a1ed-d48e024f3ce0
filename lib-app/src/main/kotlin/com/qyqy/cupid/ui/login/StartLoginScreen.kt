

package com.qyqy.cupid.ui.login

import android.content.Intent
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.config.CupidConst
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.AppearanceStatusBars
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.isRelease
import com.qyqy.ucoo.login.LoginChannel
import com.qyqy.ucoo.login.LoginChannelType
import com.qyqy.ucoo.setting.DebugActivity
import com.qyqy.ucoo.setting.LoginConfigs
import com.qyqy.ucoo.utils.web.JsBridgeWebActivity
import com.qyqy.ucoo.utils.web.addTitleByUrl
import com.qyqy.ucoo.utils.web.cacheEnableByUrl

@Composable
fun CupidStartLoginScreen(configs: LoginConfigs, onStartLogin: (@LoginChannelType Int) -> Unit = {}) {
    val context = LocalContext.current
    var backEnable by remember {
        mutableStateOf(true)
    }
    BackHandler(enabled = backEnable) {
        val homeIntent = Intent(Intent.ACTION_MAIN)
        homeIntent.addCategory(Intent.CATEGORY_HOME)
        homeIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        try {
            context.startActivity(homeIntent)
        } catch (e: Exception) {
            backEnable = false
        }
    }
    AppearanceStatusBars(isLight = true)

    Column(
        modifier = Modifier
            .background(color = Color.White)
            .paint(painterResource(id = R.drawable.cupid_header_home), contentScale = ContentScale.FillWidth, alignment = Alignment.TopStart),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.574f)
                .clickable(enabled = !isRelease) {
                    context.startActivity(Intent(context, DebugActivity::class.java))
                }
        ) {
            Column(
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxWidth(1f)
                    .align(Alignment.Center)
                    .padding(horizontal = 20.dp)
            ) {
                ComposeImage(
                    model = R.drawable.ic_cpd_launcher, modifier = Modifier
                        .size(96.dp)
                )
                AnimatedVisibility(
                    visible = configs.guess_native_region == 1 && configs.slogan.isNotEmpty(),
                    modifier = Modifier.padding(top = 20.dp)
                ) {
                    Text(
                        text = configs.slogan,
                        fontSize = 14.sp,
                        color = Color(0xFFFF5584),
                        textAlign = TextAlign.Center
                    )
                }
            }
        }


        val onlyOneLoginWay = configs.onlyOneLoginWay
        if (onlyOneLoginWay) {
            if (configs.hasFastLogin) {
                FastLoginWay(configs, onStartLogin)
            } else {
                AnimatedVisibility(
                    visible = true, modifier = Modifier.padding(
                        horizontal = 56.dp
                    )
                ) {
                    Button(
                        onClick = {
                            val channelType = when {
                                configs.hasFacebookLogin -> LoginChannel.FACEBOOK2
                                configs.hasLineLogin -> LoginChannel.LINE
                                configs.hasGoogleLogin -> LoginChannel.GOOGLE
                                else -> LoginChannel.PHONE
                            }
                            onStartLogin(channelType)
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(56.dp),
                        shape = CircleShape,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(id = R.color.FFFF5E8B),
                            contentColor = Color.White,
                        ),
                    ) {
                        val res = when {
                            configs.hasFacebookLogin -> R.drawable.icon_facebook to R.string.cpd_login_way_facebook
                            configs.hasLineLogin -> R.drawable.ic_line_fastlogin to R.string.cpd_login_way_line
                            configs.hasGoogleLogin -> R.drawable.icon_google to R.string.cpd_login_way_google
                            else -> R.drawable.icon_phone to R.string.cpd_login_way_phone
                        }
                        Image(
                            painter = painterResource(id = res.first),
                            contentDescription = "icon",
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(text = stringResource(id = res.second), fontSize = 16.sp, fontWeight = FontWeight.Medium)
                    }
                }
            }
        } else {
            if (configs.hasFastLogin) {
                FastLoginWay(configs, onStartLogin)
            } else if (configs.hasLineLogin) {
                AnimatedVisibility(
                    visible = configs.hasLineLogin,
                    modifier = Modifier.padding(horizontal = 48.dp)
                ) {
                    Button(
                        onClick = {
                            onStartLogin(LoginChannel.LINE)
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(56.dp),
                        shape = CircleShape,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(id = R.color.FFFF5E8B),
                            contentColor = Color.White,
                        ),
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_line_fastlogin),
                            contentDescription = "icon",
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(text = stringResource(id = R.string.cpd_login_way_line), fontSize = 16.sp, fontWeight = FontWeight.Medium)
                    }
                }
            }
            Spacer(modifier = Modifier.fillMaxHeight(0.3f))
            AnimatedVisibility(
                visible = configs.hasPhoneLogin or configs.hasFacebookLogin or configs.hasGoogleLogin or configs.hasLineLogin,
                modifier = Modifier.padding(horizontal = 56.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    HorizontalDivider(
                        modifier = Modifier.weight(1f),
                        color = colorResource(id = R.color.FF86909C),
                        thickness = 0.5.dp
                    )
                    Text(
                        text = stringResource(id = R.string.cpd_other_login_methods),
                        fontSize = 12.sp,
                        color = colorResource(id = R.color.FF86909C)
                    )
                    HorizontalDivider(
                        modifier = Modifier.weight(1f),
                        color = colorResource(id = R.color.FF86909C),
                        thickness = 0.5.dp
                    )
                }
            }

            Row(
                modifier = Modifier.padding(top = 15.dp),
                horizontalArrangement = Arrangement.spacedBy(40.dp)
            ) {
                AnimatedVisibility(
                    visible = configs.hasPhoneLogin,
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_cpd_phone_login),
                        contentDescription = null,
                        modifier = Modifier
                            .size(40.dp)
                            .clickable {
                                onStartLogin(LoginChannel.PHONE)
                            }
                    )
                }

                AnimatedVisibility(
                    visible = configs.hasLineLogin && configs.hasFastLogin,
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_line),
                        contentDescription = null,
                        modifier = Modifier
                            .size(40.dp)
                            .clickable {
                                onStartLogin(LoginChannel.LINE)
                            }
                    )
                }

                AnimatedVisibility(
                    visible = configs.hasFacebookLogin,
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_cpd_facebook_login),
                        contentDescription = null,
                        modifier = Modifier
                            .size(40.dp)
                            .clickable {
                                onStartLogin(LoginChannel.FACEBOOK2)
                            }
                    )
                }

                AnimatedVisibility(
                    visible = configs.hasGoogleLogin,
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_cpd_google_login),
                        contentDescription = null,
                        modifier = Modifier
                            .size(40.dp)
                            .clickable {
                                onStartLogin(LoginChannel.GOOGLE)
                            }
                    )
                }
            }
        }

        val annotatedText = buildAnnotatedString {

            val x1 = stringResource(id = R.string.cpd_用户服务协议_)
            val x2 = stringResource(id = R.string.cpd_隐私协议_)

            val text = stringResource(id = R.string.cpd登录即同意协议, x1, x2)

            append(text)

            var start = text.indexOf(x1)
            addLink(LinkAnnotation.Clickable(tag = "用户服务协议", styles = TextLinkStyles(SpanStyle(color = colorResource(id = R.color.FF4E5969)))) {
                context.startActivity(
                    JsBridgeWebActivity.newIntentFromLogin(
                        context,
                        CupidConst.URL.USER_SERVICE.addTitleByUrl(context.getString(R.string.cpd用户服务协议)).cacheEnableByUrl(false)
                    )
                )
            }, start, start.plus(x1.length))

            start = text.indexOf(x2)
            addLink(LinkAnnotation.Clickable(tag = "隐私协议", styles = TextLinkStyles(SpanStyle(color = colorResource(id = R.color.FF4E5969)))) {
                context.startActivity(
                    JsBridgeWebActivity.newIntentFromLogin(
                        context,
                        CupidConst.URL.PRIVACY_POLICY.addTitleByUrl(context.getString(R.string.cpd隐私协议)).cacheEnableByUrl(false)
                    )
                )
            }, start, start.plus(x2.length))
        }

        Spacer(modifier = Modifier.weight(1f))

        Text(
            text = annotatedText,
            modifier = Modifier
                .padding(bottom = 16.dp, start = 20.dp, end = 20.dp)
                .navigationBarsPadding(),
            style = TextStyle.Default.copy(
                color = colorResource(id = R.color.FFC9CDD4),
                fontSize = 12.sp
            )
        )
    }
}

@Composable
private fun ColumnScope.FastLoginWay(
    configs: LoginConfigs,
    onStartLogin: (@LoginChannelType Int) -> Unit,
) {
    AnimatedVisibility(
        visible = configs.hasFastLogin,
        modifier = Modifier.padding(horizontal = 48.dp)
    ) {
        Button(
            onClick = {
                onStartLogin(LoginChannel.ONETAP)
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp),
            shape = CircleShape,
            colors = ButtonDefaults.buttonColors(
                containerColor = colorResource(id = R.color.FFFF5E8B),
                contentColor = Color.White,
            ),
        ) {
            Text(text = stringResource(id = R.string.cpd_quick_login), fontSize = 16.sp, fontWeight = FontWeight.Medium)
        }
    }
}

@Preview(widthDp = 375, heightDp = 812, locale = "ja-rJP")
@Composable
fun StartLoginScreenPreview() {
    MaterialTheme {
        CupidStartLoginScreen(
            LoginConfigs(
                hasFacebookLogin = true,
                hasFastLogin = false,
                hasGoogleLogin = true,
                hasLineLogin = true,
                hasPhoneLogin = true,
                userFromChina = false,
                slogan = "新たなる恋活新たなる恋活",
                guess_native_region = 1,
            )
        )
    }
}