package com.qyqy.cupid.ui.membership

import android.content.Context
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asComposePath
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.overseas.common.utils.isPreviewOnCompose
import com.qyqy.cupid.config.CupidConst
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.DataPoint
import com.qyqy.ucoo.R
import com.qyqy.ucoo.ReportExposureCompose
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.asActivity
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.state.LoadMoreView
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AnimatedComposeImage
import com.qyqy.ucoo.compose.ui.AppearanceStatusBars
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.PathShape
import com.qyqy.ucoo.compose.ui.overScrollVertical
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.multilingual.currentLocale
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.sUserKV
import com.qyqy.ucoo.user.MemberRightItem
import com.qyqy.ucoo.user.ORDER_TYPE_MEMBERSHIP
import com.qyqy.ucoo.user.WalletContract
import com.qyqy.ucoo.user.WalletViewModel
import com.qyqy.ucoo.utils.OnClick
import com.qyqy.ucoo.utils.SimplePath
import com.qyqy.ucoo.utils.web.addTitleByUrl
import com.qyqy.ucoo.utils.web.cacheEnableByUrl
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import java.text.SimpleDateFormat
import java.util.Locale
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.hours
import kotlin.time.DurationUnit

@Composable
fun MembershipActivePage(from: String) {

    val vm = viewModel<WalletViewModel>()
    val vipState by vm.vipInfoFlow.collectAsState()
    val vipInfo = vipState.getOrNull()

    val scope = rememberCoroutineScope()
    val memberData = remember {
        MembershipDataTransformer(scope, vm.vipInfoFlow)
    }
    val currentGood by memberData.currentGoodState
    val currentPay by memberData.currentPayChannelState

    LaunchedEffect(key1 = vm) {
        vm.sendEvent(WalletContract.Event.FetchCupidVipConfig)
    }
    AppearanceStatusBars(isLight = false)
    val act = LocalContext.current.asActivity

    ReportExposureCompose(exposureName = "${from}_expose")

    Scaffold(
        containerColor = Color(0xFF1C1D1E),
        modifier = Modifier
            .fillMaxSize(),
        topBar = {
            CupidAppBar(
                containerColor = Color(0xFF1C1D1E),
                title = stringResource(R.string.cpd_open_ucoo_membership),
                navigationIconContentColor = Color.White,
                titleContentColor = Color.White
            )
        },
        bottomBar = {
            if (currentGood != null && vipInfo != null) {
                BottomBar(
                    buttonText = stringResource(
                        if (vipInfo.isVip) R.string.cpd_continue_vip else R.string.cpd_active_m,
                        "${currentGood?.currencyMark}${currentGood?.currencyNumber}"
                    )
                ) {
                    Analytics.appReportEvent(DataPoint.clickBody("${from}_click"))
                    val payWay = currentPay
                    val curGood = currentGood
                    if (payWay != null && act != null && curGood != null) {
                        vm.sendEvent(
                            WalletContract.Event.Buy(
                                act,
                                payWay.fkType,
                                curGood.productId,
                                curGood.fkLink,
                                ORDER_TYPE_MEMBERSHIP//会员
                            )
                        )
                    }
                }
            }
        }
    ) { pd ->
        if (vipInfo != null) {
            val scrollState = rememberScrollState()
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(scrollState)
                    .overScrollVertical()
                    .padding(pd)
                    .padding(horizontal = 16.dp)
            ) {

                val goods = memberData.goodsList

                val payList = memberData.payWayList
                Spacer(modifier = Modifier.height(16.dp))
                if (vipInfo.isVip) {
                    VipCard(user = sUser, vipInfo.expireTimestamp * 1000L)
                } else {
                    MembershipGuide1Card(memberCount = vipInfo.membership)
                }
                Spacer(modifier = Modifier.height(16.dp))
                LazyRow(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = if (goods.size > 3) Arrangement.spacedBy(12.dp) else Arrangement.SpaceBetween
                ) {
                    items(goods) { item ->
                        MembershipItem(
                            extra = item.label,
                            title = item.equity,
                            unit = item.currencyMark,
                            amount = item.currencyNumber,
                            desc = item.extraNote,
                            needCountDown = item.needCountDown,
                            selected = item == currentGood
                        ) {
                            memberData.selectGood(item)
                        }
                    }
                }
                Spacer(modifier = Modifier.height(40.dp))
                FullTitle(title = stringResource(R.string.cpd_vip_dress))
                Spacer(modifier = Modifier.height(16.dp))
                DressGrid(list = vipInfo.vipDressRightsInfo)
                Spacer(modifier = Modifier.height(24.dp))
                FullTitle(title = stringResource(R.string.cpd_vip_social_rights))
                Spacer(modifier = Modifier.height(16.dp))
                SocialGrid(list = vipInfo.vipSocialRightsInfo)
                Spacer(modifier = Modifier.height(40.dp))
                val shape = with(LocalDensity.current) {
                    val radius = 8.dp.toPx()
                    remember {
                        PathShape(builder = { size, layoutDirection ->
                            SimplePath.builder()
                                .apply {
                                    moveTo(0f, size.height / 2)
                                    lineTo(0f, 0f, radius, radius)
                                    lineTo(size.width, 0f, radius, radius)
                                    lineTo(size.width - 10.dp.toPx(), size.height, radius, radius)
                                    lineTo(0f, size.height, radius, radius)
                                    close()
                                }.build().asComposePath()
                        })
                    }
                }

                if (payList.size > 1) {
                    Box(
                        modifier = Modifier
                            .height(32.dp)
                            .background(Color(0xFF2A2B2C), shape)
                            .border(
                                0.5.dp,
                                Brush.horizontalGradient(listOf(Color(0xFF2F2F2C), Color(0xFF645742))), shape
                            )
                            .padding(start = 10.dp, end = 16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        BoldTitle(title = stringResource(R.string.cpd_select_pay_way))
                    }
                    Spacer(modifier = Modifier.height(16.dp))
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color(0x14FFFFFF), RoundedCornerShape(8.dp))
                            .padding(horizontal = 16.dp)
                            .heightIn(max = 1000.dp)
                    ) {
                        itemsIndexed(payList) { index, item ->
                            PayWayItem(icon = item.icon, title = item.name, selected = currentPay == item) {
                                memberData.selectPayWay(item)
                            }
                            HorizontalDivider(thickness = 0.5.dp, color = Color(0x1AFFFFFF))
                        }
                    }
                }

                Spacer(modifier = Modifier.height(40.dp))
            }
        } else {
            Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                LoadMoreView(onLoadMore = {})
            }
        }
    }
}


@Composable
fun DressGrid(list: List<MemberRightItem>) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(3), modifier = Modifier
            .fillMaxWidth()
            .heightIn(max = 1000.dp)
            .border(1.dp, Color(0xFFFABE64), RoundedCornerShape(24.dp))
            .padding(16.dp, 20.dp)
    ) {
        items(list) { item ->
            Column(modifier = Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
                AnimatedComposeImage(model = item.icon, modifier = Modifier.size(56.dp))
                Spacer(modifier = Modifier.height(12.dp))
                Text(text = item.name, fontSize = 12.sp, color = Color(0xFFF3DAB3))
            }
        }
    }
}

@Composable
fun PayWayItem(icon: String, title: String, selected: Boolean, onClick: OnClick = {}) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .click(onClick = onClick)
            .padding(vertical = 16.dp), verticalAlignment = Alignment.CenterVertically
    ) {
        ComposeImage(model = icon, modifier = Modifier.size(24.dp))
        Spacer(modifier = Modifier.width(8.dp))
        Text(text = title, color = Color.White, modifier = Modifier.weight(1f))
        Image(
            painter = painterResource(id = if (selected) R.drawable.ic_m_s else R.drawable.ic_m_us),
            contentDescription = "sel",
            modifier = Modifier.size(24.dp)
        )
    }
}

@Composable
fun SocialGrid(list: List<MemberRightItem>) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(3), modifier = Modifier
            .fillMaxWidth()
            .heightIn(max = 1000.dp)
            .padding(horizontal = 16.dp)
    ) {
        items(list) { item ->
            Column(modifier = Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
                AnimatedComposeImage(model = item.icon, modifier = Modifier.size(56.dp))
                Spacer(modifier = Modifier.height(12.dp))
                Text(text = item.name, fontSize = 14.sp, color = Color(0xFFF3DAB3), fontWeight = FontWeight.Medium)
            }
        }
    }
}

/**
 * 商品Item
 *
 * @param extra 额外信息：超值优惠，送300金币
 * @param title 商品名称：月会员，周会员
 * @param unit 单位$
 * @param amount 金额
 * @param desc 说明：连续包月
 * @param needCountDown 是否需要倒计时
 * @param selected 是否选中
 */
@Composable
fun MembershipItem(
    extra: String,
    title: String,
    unit: String,
    amount: String,
    desc: String,
    needCountDown: Boolean,
    selected: Boolean,
    modifier: Modifier = Modifier,
    onClick: OnClick = {},
) {
    val colorDark = Color(0xFF6F4F23)
    val colorLight = Color(0xFFFFDBA9)
    val color = if (selected) colorDark else colorLight
    val brushDark = remember {
        Brush.verticalGradient(listOf(Color(0xFF4C4C4C), Color(0xFF333333)))
    }
    val brushLight = remember {
        Brush.verticalGradient(listOf(Color(0xFFFFEFD4), Color(0xFFFFC46C)))
    }
    Box(
        modifier = modifier
            .size(96.dp, 114.dp)
            .clip(Shapes.corner12)
            .click(onClick = onClick)

    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(if (selected) brushLight else brushDark, shape = Shapes.corner12)
                .then(
                    if (selected) Modifier.border(1.5.dp, Color(0xFFA1732B), Shapes.corner12) else
                        Modifier.border(
                            0.5.dp,
                            Color(0xFF625F5A),
                            Shapes.corner12
                        )
                ),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.weight(1f))
            Text(text = title, color = color, fontSize = 14.sp, maxLines = 1, overflow = TextOverflow.Ellipsis)
            Spacer(modifier = Modifier.height(2.dp))
            Row(verticalAlignment = Alignment.Bottom) {
                Text(text = unit, fontSize = 16.sp, fontWeight = FontWeight.Medium, color = color)
                Spacer(modifier = Modifier.width(1.5.dp))
                Text(text = amount, fontSize = 24.sp, fontWeight = FontWeight.Bold, color = color)
            }
            Text(
                text = desc,
                color = Color(0xFF74695A),
                fontSize = 12.sp,
                modifier = Modifier.heightIn(min = 28.dp),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
        Box(
            modifier = Modifier
                .background(
                    Brush.horizontalGradient(colors = listOf(Color(0xFFFF7070), Color(0xFFF53F3F))),
                    shape = RoundedCornerShape(8.dp, 0.dp, 8.dp, 0.dp)
                )
                .padding(horizontal = 8.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(text = extra, color = MaterialTheme.colorScheme.surface, fontSize = 10.sp, fontWeight = FontWeight.Medium)
        }

        var counter by rememberSaveable(needCountDown) {
            mutableIntStateOf(
                if (needCountDown) {
                    val secondsOfDay = 1.days.toInt(DurationUnit.SECONDS)
                    if (isPreviewOnCompose) {
                        secondsOfDay
                    } else {
                        sUserKV.getLong("cupid_price_tick_start_mills", 0).let {
                            if (it <= 0L) {
                                sUserKV.putLong("cupid_price_tick_start_mills", System.currentTimeMillis())
                                secondsOfDay
                            } else {
                                secondsOfDay - System.currentTimeMillis().minus(it).coerceAtLeast(0).div(1000).rem(secondsOfDay).toInt()
                            }
                        }
                    }
                } else {
                    0
                }
            )
        }

        if (counter > 0) {

            val context = LocalContext.current
            var counterStr by remember {
                mutableStateOf(formatCounterString(context, counter))
            }

            LaunchedEffect(Unit) {
                while (isActive) {
                    delay(1000)
                    counter--
                    counterStr = formatCounterString(context, counter)
                }
            }

            Box(
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .fillMaxWidth()
                    .height(24.dp)
                    .background(
                        Brush.horizontalGradient(colors = listOf(Color(0xFFFF7070), Color(0xFFF53F3F))),
                        shape = RoundedCornerShape(0.dp, 0.dp, 8.dp, 8.dp)
                    )
                    .padding(horizontal = 8.dp),
                contentAlignment = Alignment.Center
            ) {
                AutoSizeText(
                    text = counterStr,
                    color = Color.White,
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

/**
 * 把秒数格式化为00:00:00
 * @param value 输入的秒数
 * @return 格式化后的字符串，例如 "01:23:45"
 */
private fun formatCounterString(context: Context, value: Int): String {
    if (value < 0) {
        return ""
    }

    val hours = value / 3600 // 计算小时部分
    val minutes = (value % 3600) / 60 // 计算分钟部分
    val seconds = value % 60 // 计算秒部分

    return context.getString(R.string.cpd限时_模板, String.format(context.resources.currentLocale, "%02d:%02d:%02d", hours, minutes, seconds))
}


@Composable
fun FullTitle(title: String, modifier: Modifier = Modifier) {
    Row(modifier = modifier.padding(horizontal = 30.dp), verticalAlignment = Alignment.CenterVertically) {
        Spacer(
            modifier = Modifier
                .weight(1f)
                .height(2.dp)
                .background(Brush.horizontalGradient(listOf(Color(0x00FFC266), Color(0xFFFFC266))))
        )
        Spacer(modifier = Modifier.width(8.dp))
        BoldTitle(title = title)
        Spacer(modifier = Modifier.width(8.dp))
        Spacer(
            modifier = Modifier
                .weight(1f)
                .height(2.dp)
                .background(Brush.horizontalGradient(listOf(Color(0xFFFFC266), Color(0x00FFC266))))
        )
    }
}

@Composable
fun BoldTitle(title: String) {
    Text(
        text = title,
        fontWeight = FontWeight.ExtraBold,
        style = TextStyle(brush = Brush.horizontalGradient(listOf(Color(0xFFF2DBB5), Color(0xFFEDB664))), fontSize = 16.sp)
    )
}


@Preview
@Composable
private fun Preview() {
    PreviewCupidTheme {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.Black)
        ) {
            FullTitle(title = "VIP社交特权", modifier = Modifier.fillMaxWidth())
            MembershipItem(extra = "超值特惠", title = "周会员", unit = "$", amount = "0.99", desc = "连续包月", true, selected = true)
            Spacer(modifier = Modifier.height(10.dp))
            MembershipItem(extra = "超值特惠", title = "周会员", unit = "$", amount = "0.99", desc = "", false, selected = false)
            BottomBar(buttonText = "立即开通会员（8元）")
            Spacer(modifier = Modifier.height(10.dp))

            val shape = with(LocalDensity.current) {
                val radius = 8.dp.toPx()
                remember {
                    PathShape(builder = { size, layoutDirection ->
                        SimplePath.builder()
                            .apply {
                                moveTo(0f, 0f, radius, radius)
                                lineTo(size.width, 0f, radius, radius)
                                lineTo(size.width - 16.dp.toPx(), size.height, radius, radius)
                                lineTo(0f, size.height, radius, radius)
                                close()
                            }.build().asComposePath()
                    })
                }
            }

            Box(
                modifier = Modifier
                    .height(32.dp)
                    .background(Color(0xFF1C1D1E), shape)
                    .border(
                        0.5.dp,
                        Brush.horizontalGradient(listOf(Color(0xFF2F2F2C), Color(0xFF645742))), RoundedCornerShape(8.dp)
                    )
                    .padding(horizontal = 10.dp),
                contentAlignment = Alignment.Center
            ) {
                BoldTitle(title = stringResource(R.string.cpd_select_pay_way))
            }
            MembershipGuide1Card(memberCount = 123123)
            MembershipGuide2Card(memberCount = 123123)

            Spacer(modifier = Modifier.height(10.dp))
            VipCard(user = userForPreview)
        }
    }
}

@Composable
private fun BottomBar(buttonText: String, onClick: OnClick = {}) {
    val a1 = stringResource(id = R.string.cpd_ag_membership)
    val a2 = stringResource(id = R.string.cpd_ag_reg)
    val agreements = stringResource(id = R.string.cpd_vip_agreement, a1, a2)
    val nav = LocalAppNavController.current
    val textAgreement = buildAnnotatedString {
        append(agreements)
        var start = agreements.indexOf(a1)
        var end = start + a1.length
        addLink(
            LinkAnnotation.Clickable("a1") {
                nav.navigateWeb(CupidConst.URL.VIP_SERVICE.addTitleByUrl(a1).cacheEnableByUrl(false))
            }, start, end
        )
        start = agreements.indexOf(a2)
        end = start + a2.length
        addLink(
            LinkAnnotation.Clickable("a2") {
                nav.navigateWeb(CupidConst.URL.VIP_SUB_SERVICE.addTitleByUrl(a2))
            }, start, end
        )
    }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color(0xFF333333))
            .padding(horizontal = 16.dp)
            .navigationBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.height(16.dp))
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .click(onClick = onClick)
                .background(Brush.horizontalGradient(listOf(Color(0xFFF2DBB5), Color(0xFFEDB664))), Shapes.chip)
                .heightIn(min = 56.dp)
                .padding(horizontal = 20.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(text = buttonText, color = Color(0xFF593A0C), fontSize = 16.sp, fontWeight = FontWeight.Medium)
        }
        Text(
            text = textAgreement,
            fontSize = 11.sp,
            lineHeight = 13.sp,
            color = Color.White.copy(alpha = 0.3f),
            modifier = Modifier.padding(50.dp, 6.dp),
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun VipCard(user: User, expireTs: Long = 0) {
    val context = LocalContext.current
    val expireText = remember(expireTs) {
        context.getString(
            R.string.cpd_format_date_to, SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(
                expireTs
            )
        )
    }
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .paint(painterResource(id = R.drawable.cpd_vip_bg), contentScale = ContentScale.FillBounds)
            .aspectRatio(1029f / 300),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.width(20.dp))
        CircleComposeImage(model = user.avatarUrl, modifier = Modifier.size(60.dp))
        Spacer(modifier = Modifier.width(12.dp))
        Column {
            Text(text = user.nickname, color = Color(0xFFB57514), fontSize = 16.sp, fontWeight = FontWeight.Medium)
            Spacer(modifier = Modifier.height(10.dp))
            Text(text = expireText, color = Color(0xFFAB997E), fontSize = 12.sp)
        }
    }
}


@Composable
fun MembershipGuide1Card(memberCount: Int) {
    val color = Color(0xFFB57514)
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(1029f / 432)
            .paint(painterResource(id = R.drawable.cpd_header_vip_no), contentScale = ContentScale.FillBounds)
            .padding(horizontal = 24.dp)
    ) {
        Spacer(modifier = Modifier.height(28.dp))
        Text(
            text = stringResource(R.string.cpd_join_member_family),
            color = color,
            fontSize = 16.sp,
            lineHeight = 40.sp,
            fontWeight = FontWeight.Bold
        )
        Text(
            text = stringResource(R.string.cpd_get_joy_more),
            color = color,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold
        )
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.CenterStart) {
            Text(text = stringResource(R.string.cpd_format_member_count, memberCount), color = color, fontSize = 12.sp)
        }
    }
}

@Composable
fun MembershipGuide2Card(memberCount: Int) {
    val color = Color(0xFFB57514)
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(1125f / 288)
            .paint(painterResource(id = R.drawable.cpd_header_vip_thin_no), contentScale = ContentScale.FillBounds)
            .padding(horizontal = 40.dp, vertical = 2.dp),
        verticalArrangement = Arrangement.SpaceAround
    ) {
        Text(
            text = stringResource(R.string.cpd_join_member_family),
            color = color,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold
        )
        Text(
            text = stringResource(R.string.cpd_get_joy_more),
            color = color,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold
        )
        Text(text = stringResource(R.string.cpd_format_member_count, memberCount), color = color, fontSize = 12.sp)
    }
}
