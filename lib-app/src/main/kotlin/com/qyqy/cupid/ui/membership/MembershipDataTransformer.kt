package com.qyqy.cupid.ui.membership

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.qyqy.ucoo.base.DataState
import com.qyqy.ucoo.user.ActivateOption
import com.qyqy.ucoo.user.VipInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

class MembershipDataTransformer(scope: CoroutineScope, val state: StateFlow<DataState<VipInfo>>) {

    val goodsList = mutableStateListOf<ActivateOption>()
    val payWayList = mutableStateListOf<VipInfo.Good>()
    val currentPayChannelState = mutableStateOf<VipInfo.Good?>(null)
    val currentGoodState = mutableStateOf<ActivateOption?>(null)

    val currentGood:ActivateOption?
        get() = currentGoodState.value
    val currentPayWay:VipInfo.Good?
        get() = currentPayChannelState.value

    init {
        scope.launch {
            state.collectLatest { ds ->
                val vipInfo = ds.getOrNull() ?: return@collectLatest
                val payWays = vipInfo.goods
                payWayList.clear()
                payWayList.addAll(payWays)
                selectPayWay(payWays.firstOrNull())
            }
        }
    }

    fun selectPayWay(payWay: VipInfo.Good?) {
        payWay ?: return
        currentPayChannelState.value = payWay
        goodsList.clear()
        goodsList.addAll(payWay.activateOptions)
        currentGoodState.value = goodsList.firstOrNull()
    }

    fun selectGood(good: ActivateOption?) {
        good ?: return
        currentGoodState.value = good
    }
}