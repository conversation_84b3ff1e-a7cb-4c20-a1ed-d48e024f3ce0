package com.qyqy.cupid.ui.profile

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.INVALID_USER
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.user.UserManager
import com.qyqy.ucoo.user.UserRepository
import com.qyqy.ucoo.utils.LogUtils
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.serialization.json.decodeFromJsonElement

class CupidProfileViewModel(private val userId: String) : ViewModel() {

//    private val pair: Pair<StateFlow<User>, () -> Unit> = UserManager.getUserState(viewModelScope, userId)
//
//    val user: StateFlow<User> = pair.first

    /**
     * 2.40.0 使用UserManager的api/ucuser/v1/user/info接口获取的信息不包含isCoinTrade信息
     * 这里直接换api/ucuser/v1/user/brief/profile进行请求
     *
     * 所以也就不需要上面的state管理了, 只是用自身的user
     */
    private val _user = MutableStateFlow(INVALID_USER)
    val user = _user.asStateFlow()

    private val userRepo = UserRepository()

    fun refresh() {
        viewModelScope.launch {
            userRepo.getUserProfileInfo(userId.toInt()).onSuccess { json ->
                val newUser = sAppJson.decodeFromJsonElement<AppUser>(json)
                UserManager.updateUserIfExist(userId) {
                    newUser
                }
                _user.emit(newUser)
//                var tryCnt = 0
//                while (++tryCnt < 3) {
//                    delay(tryCnt * 500L)
//                    val cacheUser = UserManager.updateUserIfExist(userId) {
//                        newUser
//                    }
//                    if (cacheUser == null) {
//                        LogUtils.i("CupidProfileViewModel", "第%d次存入,等待UserManager存入数据..%s", tryCnt, newUser)
//                    }
//                }
            }
        }
    }

    fun toggleFollow() {
        viewModelScope.launch {
            val ns = !user.value.followed
            userRepo.updateFriendShip(userId, ns)
                .onSuccess {
                    _user.emit(_user.value.copy(followed = ns))
                    UserManager.updateUserIfExist(userId) { u ->
                        u.copy(followed = ns)
                    }
                }.toastError()
        }
    }

    suspend fun addToBlackList() = userRepo.updateBlackShip(userId, true)

}
