package com.qyqy.cupid.ui.profile

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.DialogProperties
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.dialog.NormalDialog
import com.qyqy.cupid.ui.profile.guard.TwoAvatarRow
import com.qyqy.cupid.widgets.CpdButton
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.utils.OnClick

@Composable
fun SingleButtonDialog(message: String, buttonText: String, onClick: OnClick = {}) {
    Column(
        Modifier
            .width(238.dp)
            .background(Color.White, Shapes.small)
            .padding(16.dp, 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(text = message, color = Color(0xFF86909C), fontSize = 15.sp, modifier = Modifier.fillMaxWidth())
        Spacer(modifier = Modifier.height(20.dp))
        CpdButton(text = buttonText, onClick = onClick, modifier = Modifier.widthIn(min = 160.dp))
    }
}

@Composable
fun TwoAvatarDialog(avatarLeft: String, avatarRight: String, message: String, buttonText: String, onClick: OnClick = {}) {
    Box(modifier = Modifier.width(270.dp), contentAlignment = Alignment.TopCenter) {

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 20.dp)
                .background(Color.White, Shapes.small)
                .padding(16.dp, 20.dp), horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(60.dp))
            Text(
                text = message,
                color = Color(0xFF86909C),
                fontSize = 15.sp,
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(20.dp))
            CpdButton(text = buttonText, onClick = onClick, modifier = Modifier.widthIn(min = 160.dp))
        }

        TwoAvatarRow(leftAvatar = avatarLeft, rightAvatar = avatarRight)

    }
}

class GuardDialog(private val goldCount: Int, val onClick: OnClick) : NormalDialog<IDialogAction>() {

    override val properties: DialogProperties
        get() = DialogProperties(usePlatformDefaultWidth = false)

    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        SingleButtonDialog(
            message = stringResource(id = R.string.cpd_hint_protect, goldCount),
            buttonText = stringResource(id = R.string.cpd_protect_her)
        ) {
            dialog.dismiss()
            onClick.invoke()
        }
    }

}

class ReplaceDialog(
    private val avatarLeft: String,
    private val avatarRight: String,
    private val goldCount: Int,
    val onClick: OnClick
) :
    NormalDialog<IDialogAction>() {

    override val properties: DialogProperties
        get() = DialogProperties(usePlatformDefaultWidth = false)

    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        TwoAvatarDialog(
            avatarLeft = avatarLeft,
            avatarRight = avatarRight,
            message = stringResource(id = R.string.cpd_replace_protect, goldCount),
            buttonText = stringResource(R.string.cpd_become_guard),
            onClick = {
                dialog.dismiss()
                onClick.invoke()
            }
        )
    }
}

class YouDialog(
    private val avatarLeft: String,
    private val avatarRight: String,
) :
    NormalDialog<IDialogAction>() {

    override val properties: DialogProperties
        get() = DialogProperties(usePlatformDefaultWidth = false)

    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        TwoAvatarDialog(
            avatarLeft = avatarLeft,
            avatarRight = avatarRight,
            message = stringResource(id = R.string.cpd_right_you),
            buttonText = stringResource(R.string.cpd_iknow),
            onClick = {
                dialog.dismiss()
            }
        )
    }
}