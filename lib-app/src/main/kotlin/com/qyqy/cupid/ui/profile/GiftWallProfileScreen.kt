@file:OptIn( ExperimentalMaterial3Api::class)

package com.qyqy.cupid.ui.profile

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ColorMatrix
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.cupid.model.GiftWallViewModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.widgets.CpdAppTabRow
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.CupidGiftWallPage
import com.qyqy.cupid.widgets.GiftWallContainerStyle
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.data.BottomRadiusItem
import com.qyqy.ucoo.compose.data.CategoryGiftWall
import com.qyqy.ucoo.compose.data.CategoryTitleItem
import com.qyqy.ucoo.compose.data.GiftItem
import com.qyqy.ucoo.compose.data.SpaceItem
import com.qyqy.ucoo.compose.data.SpanItem
import com.qyqy.ucoo.compose.data.TopRadiusItem
import com.qyqy.ucoo.compose.ui.AppTab
import com.qyqy.ucoo.compose.ui.AppearanceStatusBars
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.bean.MyGift

/**
 *  @time 9/3/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.profile
 */
@Composable
fun GiftProfileScreen(userId: Int) {
    val viewModel = viewModel(
        modelClass = GiftWallViewModel::class,
        factory = viewModelFactory {
            initializer {
                GiftWallViewModel(userId.toString())
            }
        }
    )
    val giftWallData = viewModel.giftWallData.collectAsStateWithLifecycle()
    AppearanceStatusBars(isLight = false)
    LaunchedEffect(key1 = Unit) {
        viewModel.refresh()
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(color = Color(0xFF29275C))
    ) {
        CupidAppBar(
            title = stringResource(id = R.string.cpd_gift_summary),
            containerColor = Color.Transparent,
            navigationIconContentColor = Color.White,
            titleContentColor = Color.White
        )

        val containerStyle = remember {
            GiftWallContainerStyle(
                backgroundColor = Color(0xFF23214F),

                activeTitleColor = Color(0xCCffffff),
                inactiveTitleColor = Color(0x80ffffff),

                activeCountColor = CpdColors.FFECDFB9,
                inactiveCountColor = Color(0xCCffffff),

                activeItemBgImg = R.drawable.ic_cpd_gift_profile_light,
                inactiveItemBgImg = R.drawable.ic_cpd_gift_profile,

                categoryTitleColor = Color(0xffCAC8FF),

                leftArrowImgRes = R.drawable.ic_cpd_gift_profile_left,
                rightArrowImgRes = R.drawable.ic_cpd_gift_profile_right,

                marginValues = PaddingValues(5.dp),
//                paddingValues = PaddingValues(8.dp)
            )
        }
        if (giftWallData.value.isNotEmpty()) {
            Column(modifier = Modifier.fillMaxSize(), horizontalAlignment = Alignment.CenterHorizontally) {
                val pagerState = rememberPagerState(0) { giftWallData.value.size }
//                CpdAppTabRow(tabs =, pagerState =)
//                CpdAppScrollableTabRow(
                CpdAppTabRow(
                    tabs = giftWallData.value.map {
                        AppTab(it.category)
                    },
                    pagerState = pagerState,
//                    spacePadding = 40.dp, edgePadding = 0.dp
                    tabSelectedColor = Color.White,
                    tabUnSelectedColor = Color(0x80ffffff),
                    modifier = Modifier
                        .fillMaxWidth(0.6f)
                        .padding(top = 8.dp, bottom = 12.dp)
                )
                HorizontalPager(
                    modifier = Modifier.fillMaxSize(),
                    verticalAlignment = Alignment.Top,
                    state = pagerState
                ) { page ->
                    CupidGiftWallPage(
                        2,
                        giftWallData.value[page].items,
                        Modifier
                            .padding(horizontal = 16.dp)
                            .fillMaxWidth(),
                        containerStyle
                    )
                }
            }
        }
    }
}

@Composable
private fun GiftProfileItem(giftItem: GiftItem) {
    Column(
        modifier = Modifier
            .padding(horizontal = 3.dp, vertical = 5.dp)
            .paint(
                painter = painterResource(
                    id = if (giftItem.count > 0)
                        R.drawable.ic_cpd_gift_profile_light
                    else
                        R.drawable.ic_cpd_gift_profile
                ),
                contentScale = ContentScale.FillBounds
            )
            .widthIn(104.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        val colorFilter = if (giftItem.count > 0) {
            null
        } else {
            ColorFilter.colorMatrix(ColorMatrix().apply { setToSaturation(0f) })
        }
        Spacer(modifier = Modifier.height(6.dp))

        ComposeImage(
            model = giftItem.gift.icon,
            modifier = Modifier.size(56.dp),
            contentScale = ContentScale.Crop,
            colorFilter = colorFilter,
            contentDescription = null,
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = giftItem.gift.name,
            modifier = Modifier
                .height(18.dp),
            color = if (giftItem.count > 0) Color(0xCCffffff) else Color(0x80ffffff),
            fontSize = 12.sp,
            textAlign = TextAlign.Center,
            overflow = TextOverflow.Ellipsis,
            maxLines = 1,
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = "x${giftItem.count}",
            modifier = Modifier
                .height(16.dp),
            color = if (giftItem.count > 0) CpdColors.FFECDFB9 else Color(0xCCffffff),
            fontSize = 10.sp,
            maxLines = 1,
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(6.dp))
    }
}

@Composable
@Preview(showBackground = true)
private fun GiftProfileContentPreview() {
    val containerStyle = remember {
        GiftWallContainerStyle(
            backgroundColor = Color(0xFF23214F),

            activeTitleColor = Color(0xCCffffff),
            inactiveTitleColor = Color(0x80ffffff),

            activeCountColor = CpdColors.FFECDFB9,
            inactiveCountColor = Color(0xCCffffff),

            activeItemBgImg = R.drawable.ic_cpd_gift_profile_light,
            inactiveItemBgImg = R.drawable.ic_cpd_gift_profile,

            categoryTitleColor = Color(0xffCAC8FF),

            leftArrowImgRes = R.drawable.ic_cpd_gift_profile_left,
            rightArrowImgRes = R.drawable.ic_cpd_gift_profile_right,

            marginValues = PaddingValues(5.dp),
//                paddingValues = PaddingValues(0.dp)
            itemHeight = 104
        )
    }
    val giftData =
        sAppJson.decodeFromString<MyGift>(
            "{\"gift\":{\"t\":0,\"id\":395,\"name\":\"ジョーカーカード\",\"price_type\":3,\"price\":100,\"icon\":\"https://media.ucoofun.com/opsite/gift/icon/NO.102310__%E5%B0%8F%E4%B8%91%E5%8D%A1-%E6%84%9A%E4%BA%BA%E8%8A%82_r6xF8pK.png\",\"effect_file\":\"https://media.ucoofun.com/opsite/gift/effect/%E5%B0%8F%E4%B8%91%E5%8D%A1_right_YM2iDGA.mp4\",\"native_region\":1,\"superscript_icon\":\"\",\"banner_img\":\"\",\"banner_link\":\"\",\"is_exchangeable\":false,\"app_min_version\":\"\",\"scene_type\":0,\"room_mode\":0},\"count\":0}"
        )
    val giftList = listOf(giftData, giftData, giftData, giftData, giftData)
    val categoryGiftWall = CategoryGiftWall(
        "测试礼物",
        buildList {
            add(TopRadiusItem)
            add(CategoryTitleItem("这是一个测试标题"))
            giftList.forEach { gift ->
                add(GiftItem(gift.gift, gift.count))
            }
            val lastLineCount = giftList.size.rem(4)
            if (lastLineCount != 0) {
                val leftSpan = 4.minus(lastLineCount)
                add(size.minus(lastLineCount), SpanItem(leftSpan))
                add(SpanItem(leftSpan))
            }
            add(BottomRadiusItem)
            add(SpaceItem(16))
        }
    )

    Column(modifier = Modifier.fillMaxSize(), horizontalAlignment = Alignment.CenterHorizontally) {
        //                CpdAppTabRow(tabs =, pagerState =)
//                CpdAppScrollableTabRow(
        val pagerState = rememberPagerState(0) { 2 }

        CpdAppTabRow(
            tabs = listOf(
                AppTab("tab1"),
                AppTab("tab2")
            ),
            pagerState = pagerState,
//                    spacePadding = 40.dp, edgePadding = 0.dp
            tabSelectedColor = Color.White,
            tabUnSelectedColor = Color(0x80ffffff),
            modifier = Modifier
                .fillMaxWidth(0.6f)
                .padding(top = 8.dp, bottom = 12.dp)
        )
        CupidGiftWallPage(
            1,
            categoryGiftWall.items,
            Modifier
                .fillMaxWidth()
                .padding(horizontal = 10.dp),
            containerStyle
        )
    }
}
