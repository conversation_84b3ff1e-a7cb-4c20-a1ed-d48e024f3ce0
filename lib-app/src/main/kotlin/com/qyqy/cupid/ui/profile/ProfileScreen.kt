package com.qyqy.cupid.ui.profile

import android.app.Activity
import android.content.Context
import androidx.annotation.StringRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.ElevatedButton
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.view.WindowCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.data.users.introEmptyRes
import com.qyqy.cupid.theme.CupidTheme
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.DestinationRoute
import com.qyqy.cupid.ui.INavAction
import com.qyqy.cupid.ui.IUserMenuAction
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.dialog.BlackAlertDialog
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.dialog.MoreActionDialog
import com.qyqy.cupid.ui.dialog.rememberDialogQueue
import com.qyqy.cupid.ui.home.mine.CpdMineTab
import com.qyqy.cupid.ui.home.mine.crony.CpZonePage
import com.qyqy.cupid.ui.home.mine.crony.CronyHomePage
import com.qyqy.cupid.ui.home.mine.crony.CronyViewModel
import com.qyqy.cupid.ui.home.mine.edit.ChangeableProperty
import com.qyqy.cupid.ui.home.mine.edit.CupidEditViewModel
import com.qyqy.cupid.ui.home.mine.edit.editorController
import com.qyqy.cupid.ui.navigatorToProfileEdit
import com.qyqy.cupid.ui.relations.family.AudioWaves
import com.qyqy.cupid.ui.relations.family.icons.IconFamily
import com.qyqy.cupid.utils.getVoiceLiveHelper
import com.qyqy.cupid.utils.navigateToFamilyHome
import com.qyqy.cupid.widgets.CpdAppScrollableTabRow
import com.qyqy.cupid.widgets.GiftWallContent
import com.qyqy.cupid.widgets.LevelComposeView
import com.qyqy.cupid.widgets.TextLabel
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.currentRoomId
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.isInvalid
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.ComposeLifecycleObserve
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AgeGender
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.fixCurrentPage
import com.qyqy.ucoo.self
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.tribe.bean.Tribe
import com.qyqy.ucoo.tribe.bean.tribeId
import com.qyqy.ucoo.utils.EntityCallback
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

private sealed interface CupidProfileAction {
    data object Back : CupidProfileAction
    data object ToggleFocus : CupidProfileAction
    data object ClickMore : CupidProfileAction
    data class ClickVoiceRoom(val roomId: Int) : CupidProfileAction
    data class ClickGiftWall(val userId: Int) : CupidProfileAction
}

@Composable
fun CupidProfileScreen(
    userId: String,
    onAction: INavAction,
    modifier: Modifier = Modifier,
    vm: CupidProfileViewModel = viewModel {
        CupidProfileViewModel(userId)
    },
) {
    val navHostController = LocalAppNavController.current
    val userState = vm.user.collectAsStateWithLifecycle()
    val user by userState
    val scope = rememberCoroutineScope()
    val loadingState = LocalContentLoading.current

    LaunchedEffect(key1 = vm) {
        vm.refresh()
    }

    val dialogQueue = rememberDialogQueue<IUserMenuAction>()

    val onMenuAction = remember(onAction) {
        object : IUserMenuAction {
            override fun onShowReport() {
                onAction.onNavigateTo(CupidRouters.REPORT_START, arguments = mapOf("type" to 1, "id" to userId))
            }

            override fun onShowBlack() {
                dialogQueue.push(BlackAlertDialog)
            }

            override fun onBlack(dialog: IDialog) {
                dialog.dismiss()
                scope.launch {
                    loadingState.value = true
                    vm.addToBlackList()
                        .onSuccess {
                            toastRes(R.string.cupid_result_success)
                        }.toastError()
                    loadingState.value = false
                }
            }
        }
    }

    val h = getVoiceLiveHelper()

    val profileAction: (CupidProfileAction) -> Unit = remember {
        { action: CupidProfileAction ->
            when (action) {
                CupidProfileAction.Back -> {
                    navHostController.popBackStack()
                }

                CupidProfileAction.ClickMore -> {
                    dialogQueue.push(MoreActionDialog)
                }

                is CupidProfileAction.ClickVoiceRoom -> {
                    h.joinVoiceLiveRoom(action.roomId, from = "jp_profile_room_click")
                }

                CupidProfileAction.ToggleFocus -> {
                    vm.toggleFollow()
                }

                is CupidProfileAction.ClickGiftWall -> {
                    navHostController.navigate(CupidRouters.GIFT_WALL, mapOf("userId" to action.userId))
                }
            }
        }
    }

    val context = LocalContext.current

    val viewModel = viewModel<CronyViewModel> {
        CronyViewModel(userId)
    }

    LaunchedEffect(Unit) {
        viewModel.fetchCronyTable()
    }

//    if (user.isSelf && user.publicCP != null) {
//        ComposeLifecycleObserve { event ->
//            if (event == Lifecycle.Event.ON_RESUME) {
//                viewModel.fetchCpZone()
//            }
//        }
//    }

    val tabList by remember(viewModel) {
        derivedStateOf {
            buildList {
//                if (viewModel.cpZone != null) {
//                    add(CpdMineTab.CpZone(context.getString(R.string.cpd情侣空间)))
//                }
                add(CpdMineTab.Info(context.getString(R.string.cpd_mine_profile)))
                if (user.isSelf || user.publicCP != null || viewModel.cronyTable != null) {
                    add(CpdMineTab.Crony(context.getString(R.string.cpd亲密关系)))
                }
            }
        }
    }

    dialogQueue.DialogContent(onMenuAction)

    CupidProfileContent(
        user,
        tabList,
        modifier,
        onAction,
        profileAction
    )
}

@Composable
@OptIn(ExperimentalMaterial3Api::class)
private fun CupidProfileContent(
    user: User,
    tabList: List<CpdMineTab>,
    modifier: Modifier,
    onAction: INavAction,
    onProfileAction: (CupidProfileAction) -> Unit = {},
) {
    val vm = viewModel<CupidEditViewModel>()
    val editorController = editorController(ChangeableProperty.NONE, vm)
    val scrollState = rememberScrollState()
    val fraction by remember {
        derivedStateOf {
            (scrollState.value / (scrollState.maxValue * 0.75f)).coerceIn(0f, 1f)
        }
    }

    val showText by remember {
        derivedStateOf {
            fraction > 0.5f
        }
    }

    val tintColor by remember {
        derivedStateOf { if (showText) Color(0xFF1D2129) else Color.White }
    }

    val coverColor by remember {
        derivedStateOf { Color.Black.copy(0.1f * (1f - fraction)) }
    }

    val view = LocalView.current

    if (showText) {
        LaunchedEffect(key1 = Unit) {
            val window = (view.context as? Activity)?.window ?: return@LaunchedEffect
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = true
        }
    } else {
        LaunchedEffect(key1 = Unit) {
            val window = (view.context as? Activity)?.window ?: return@LaunchedEffect
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = false
        }
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .navigationBarsPadding()
    ) {
        ProfileContent(user, tabList = tabList, scrollState = scrollState, onEditBaseInfo = {
            onAction.navigatorToProfileEdit()
        }, onEditIntro = {
            editorController.invoke(ChangeableProperty.INTRO, user.shortIntro)
        }, onClickVoiceRoom = {
            onProfileAction(CupidProfileAction.ClickVoiceRoom(it))
        }, onClickGiftWall = {
            onProfileAction(CupidProfileAction.ClickGiftWall(it))
        })
        //title bar
        CenterAlignedTopAppBar(
            colors = TopAppBarDefaults.topAppBarColors(containerColor = Color.Transparent),
            modifier = Modifier
                .background(Color.White.copy(fraction))
                .padding(horizontal = 16.dp),
            title = {
                if (showText) {
                    Text(
                        text = user.nickname,
                        style = MaterialTheme.typography.headlineMedium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.widthIn(max = 180.dp)
                    )
                }
            },
            navigationIcon = {
                Box(
                    modifier = Modifier
                        .size(32.dp)
                        .clip(CircleShape)
                        .background(coverColor)
                        .clickable(onClick = { onProfileAction(CupidProfileAction.Back) }),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_navigation_back),
                        contentDescription = "",
                        tint = tintColor,
                        modifier = Modifier
                            .size(24.dp)
                            .padding(4.dp)
                    )
                }
            }, actions = {
                AnimatedVisibility(visible = user.isSelf) {
                    Box(
                        modifier = Modifier
                            .size(32.dp)
                            .clip(CircleShape)
                            .background(coverColor)
                            .clickable {
                                onAction.navigatorToProfileEdit()
                            }, contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.cupid_ic_edit),
                            modifier = Modifier
                                .size(24.dp),
                            tint = tintColor,
                            contentDescription = "edit"
                        )
                    }
                }
                if (!user.isSelf) {
                    Spacer(modifier = Modifier.width(10.dp))
                    Box(
                        modifier = Modifier
                            .size(32.dp)
                            .clip(CircleShape)
                            .background(coverColor)
                            .clickable(onClick = { onProfileAction(CupidProfileAction.ClickMore) }),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_more_menu),
                            modifier = Modifier
                                .size(24.dp),
                            tint = tintColor,
                            contentDescription = "more"
                        )
                    }
                }
            })

        //bottom buttons
        val buttonsVisible = user.isInvalid().not() && user.isSelf.not()
        AnimatedVisibility(
            visible = buttonsVisible,
            enter = fadeIn() + expandVertically(),
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(10.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        Brush.verticalGradient(
                            listOf(
                                Color.Transparent,
                                MaterialTheme.colorScheme.surface,
                                MaterialTheme.colorScheme.surface

                            )
                        )
                    )
                    .padding(bottom = 8.dp, top = 43.dp, start = 24.dp, end = 24.dp)
            ) {
                ElevatedButton(
                    modifier = Modifier.widthIn(min = 64.dp),
                    contentPadding = PaddingValues(10.dp, 12.dp),
                    colors = ButtonDefaults.elevatedButtonColors(
                        containerColor = if (!user.followed) Color(0xFFFFD862) else Color(0xFFF1F2F3),
                        contentColor = if (user.followed) MaterialTheme.typography.labelMedium.color else Color(0xFF1D2129)
                    ),
                    onClick = { onProfileAction(CupidProfileAction.ToggleFocus) }
                ) {
                    Text(
                        text = if (user.followed) stringResource(id = R.string.cpd_cancel_follow) else stringResource(id = R.string.cpd_focus),
                        fontSize = 14.sp
                    )
                }
                Spacer(modifier = Modifier.width(16.dp))
                ElevatedButton(
                    modifier = Modifier.fillMaxWidth(),
                    contentPadding = PaddingValues(vertical = 12.dp),
                    colors = ButtonDefaults.elevatedButtonColors(
                        containerColor = MaterialTheme.colorScheme.primary,
                        contentColor = Color.White
                    ),
                    onClick = { onAction.onNavigateTo(CupidRouters.C2CChat, mapOf("user" to user)) }) {
                    Image(
                        painter = painterResource(id = R.drawable.cupid_ic_chat),
                        contentDescription = "",
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(text = stringResource(id = R.string.cupid_main_hi))
                }
            }
        }
    }
}


@Composable
@OptIn(ExperimentalFoundationApi::class)
private fun ProfileContent(
    user: User,
    tabList: List<CpdMineTab>,
    scrollState: ScrollState,
    onEditIntro: OnClick = {},
    onEditBaseInfo: OnClick = {},
    onClickVoiceRoom: EntityCallback<Int> = {},
    onClickGiftWall: EntityCallback<Int> = {},
) {
    val scope = rememberCoroutineScope()
    val context = LocalContext.current
    val anc = LocalAppNavController.current
    val dq = rememberDialogQueue<IDialogAction>()
    dq.DialogContent()
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(scrollState)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(1f)
        ) {

            val imageList = remember(user.avatarUrl, user.albumList) {
                buildList {
                    add(user.avatarUrl)
                    addAll(user.albumList.map { it.url })
                }
            }

            val pagerState = rememberPagerState {
                imageList.size
            }
            LaunchedEffect(pagerState) {
                while (isActive) {
                    delay(2500)
                    pagerState.animateScrollToPage(pagerState.fixCurrentPage.plus(1).rem(pagerState.pageCount))
                }
            }

            HorizontalPager(
                state = pagerState,
                verticalAlignment = Alignment.Bottom,
                modifier = Modifier
                    .fillMaxSize(),
            ) { index ->
                ComposeImage(
                    model = imageList[index],
                    modifier = Modifier.fillMaxSize(),
                )
            }

            Row(
                Modifier
                    .align(Alignment.BottomEnd)
                    .padding(16.dp, 32.dp)
                    .height(56.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.End
            ) {
                if (user.isSelf) {
                    Box(
                        modifier = Modifier
                            .clip(RoundedCornerShape(8.dp))
                            .click {
                                anc.navigate(CupidRouters.ALBUM_EDIT)
                            }
                            .background(Color(0x99000000))
                            .size(56.dp), contentAlignment = Alignment.Center
                    ) {
                        Text(text = "+", fontSize = 28.sp, color = Color.White, fontWeight = FontWeight.Thin)
                    }
                }

                Spacer(modifier = Modifier.width(8.dp))
                LazyRow(modifier = Modifier.widthIn(max = 192.dp), horizontalArrangement = Arrangement.End) {
                    itemsIndexed(imageList) { index: Int, item: String ->
                        ComposeImage(
                            model = item, modifier = Modifier
                                .size(56.dp)
                                .clip(RoundedCornerShape(8.dp))
                                .clickable {
                                    scope.launch {
                                        pagerState.animateScrollToPage(index)
                                    }
                                })
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                }
            }
        }
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .offset(0.dp, (-24).dp)
                .clip(RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp))
                .background(Color.White)
                .padding(start = 16.dp, end = 16.dp, top = 24.dp)
        ) {
            val nav = LocalAppNavController.current
            val guardian = (user as? AppUser)?.guardInfo
            Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                CupidBriefInfo(user = user, modifier = Modifier.weight(1f))
                Box(
                    modifier = Modifier
                        .size(64.dp)
                        .click {
                            if (user.isSelf) {
                                nav.navigate(CupidRouters.MY_GUARD)
                            } else {
                                if (guardian?.existGuardian == true) {
                                    if (guardian.guardianUserid == self.userId) {
                                        dq.push(YouDialog(user.avatarUrl, self.avatarUrl))
                                    } else if (guardian.guardianIsHidden) {
                                        toast(guardian.guardianHideHint)
                                    } else {
                                        dq.push(ReplaceDialog(user.avatarUrl, guardian.guardianAvatar, guardian.guardianValue) {
                                            nav.navigate(CupidRouters.C2CChat, mapOf("user" to user))
                                        }, true)
                                    }
                                } else {
                                    dq.push(GuardDialog(guardian?.guardThreshold ?: 9999) {
                                        nav.navigate(CupidRouters.C2CChat, mapOf("user" to user))
                                    }, true)
                                }
                            }
                        }, contentAlignment = Alignment.Center
                ) {
                    val guardianAvatar = guardian?.guardianAvatar
                    if (!guardianAvatar.isNullOrEmpty() && (!guardian.guardianIsHidden || guardian.guardianUserid == self.userId)) {
                        CircleComposeImage(
                            model = guardianAvatar,
                            modifier = Modifier.size(50.dp)
                        )
                    }

                    Image(
                        painter = painterResource(
                            when {
                                guardian?.existGuardian != true -> R.drawable.cpd_frame_empty
                                !guardian.guardianIsHidden || guardian.guardianUserid == self.userId -> R.drawable.cpd_frame_protect
                                else -> R.drawable.cpd_frame_hidden
                            }
                        ),
                        contentDescription = "frame",
                        modifier = Modifier.fillMaxSize()
                    )
                }
            }
            Spacer(modifier = Modifier.height(8.dp))

            AnimatedVisibility(visible = user.currentRoomId != null) {
                AudioStateBar(user) {
                    onClickVoiceRoom(it)
                }
                Spacer(modifier = Modifier.height(8.dp))
            }

            FamilyStateBar(
                tribe = user.tribe, user.isBoy, modifier = Modifier
                    .clip(Shapes.small)
                    .click {
                        val tribe = user.tribe
                        if (tribe != null) {
                            if (user.isSelf) {
                                anc.navigateToFamilyHome()
                            } else {
                                anc.navigate(CupidRouters.FAMILY_DETAIL, mapOf("family_id" to tribe.tribeId))
                            }
                        }
                    })
            Spacer(modifier = Modifier.height(8.dp))
            GiftWallContent(lightUpCnt = user.giftWallStats.lightUpCnt, totalCnt = user.giftWallStats.totalCnt) {
                onClickGiftWall(user.userId)
            }

            Spacer(modifier = Modifier.height(10.dp))

            val pagerState = rememberPagerState { tabList.size }

            CpdAppScrollableTabRow(
                tabs = tabList,
                pagerState = pagerState,
                spacePadding = 28.dp,
                edgePadding = 0.dp
            )

            HorizontalPager(
                modifier = Modifier
                    .fillMaxSize()
                    .heightIn(min = 630.dp),
                verticalAlignment = Alignment.Top,
                state = pagerState
            ) { page ->
                when (val tab = tabList[page]) {
                    is CpdMineTab.Info -> {
                        ProfileInfo(user, onEditIntro, context, onEditBaseInfo)
                    }

                    is CpdMineTab.Crony -> {
                        CronyHomePage(true)
                    }

                    is CpdMineTab.CpZone -> {
                        CpZonePage(true)
                    }

                    else -> Unit
                }
            }
        }
    }
}

@Composable
fun AudioStateBar(
    user: User, openRoom: EntityCallback<Int> = {},
) {
    val c = MaterialTheme.colorScheme.primary
    val roomId = user.currentRoomId
    if (roomId != null) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(6.dp))
                .click {
                    openRoom(roomId)
                }
                .background(Color(0xFFF6EBF0))
                .padding(16.dp, 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            AudioWaves()
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = stringResource(R.string.cpd_audio_parting),
                fontSize = 12.sp,
                fontWeight = FontWeight.Bold,
                color = c
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = stringResource(if (user.isBoy) R.string.cpd_chat_to_he else R.string.cpd_chat_to_her),
                fontSize = 12.sp,
                color = c
            )
            Spacer(modifier = Modifier.weight(1f))
            Icon(
                painter = painterResource(id = R.drawable.ic_arrow_right),
                contentDescription = "",
                modifier = Modifier.size(12.dp),
                tint = c
            )
        }
    }
}

@Preview
@Composable
private fun AudioStateBarPreview() {
    CupidTheme {
        AudioStateBar(user = userForPreview.copy(roomId = 9, isInAudioRoom = true))
    }
}

@Composable
fun FamilyStateBar(tribe: Tribe?, isBoy: Boolean, modifier: Modifier = Modifier) {
    if (tribe != null) {
        Row(
            modifier = modifier
                .fillMaxWidth()
                .background(Color(0xFFFAFAFA), Shapes.small)
                .padding(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            ComposeImage(
                model = tribe.avatarUrl, modifier = Modifier
                    .size(48.dp)
                    .clip(Shapes.small)
            )
            Text(
                text = tribe.name.orEmpty(),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = 8.dp)
            )
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Image(imageVector = IconFamily, contentDescription = "", modifier = Modifier.size(24.dp))
                Spacer(modifier = Modifier.height(4.dp))
                Text(
//                    text = stringResource(if (isBoy) R.string.cpd_his_family else R.string.cpd_her_family),
                    text = stringResource(R.string.cpd_oppo_family),
                    style = MaterialTheme.typography.labelMedium
                )
            }
        }
    }
}

@Preview
@Composable
private fun FamilyStatePreview() {
    CupidTheme {
        FamilyStateBar(tribe = Tribe(name = "我的家族"), isBoy = false)
    }
}

@Composable
fun CupidBriefInfo(user: User, modifier: Modifier = Modifier) {
    Column(modifier = modifier) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(
                text = user.nickname,
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.weight(1f, false),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            AgeGender(age = user.age, isBoy = user.isBoy)

            LevelComposeView(user = user)
        }
        Spacer(modifier = Modifier.height(10.dp))
        Row(verticalAlignment = Alignment.CenterVertically) {
            val textStyle = remember {
                TextStyle(
                    color = Color(0xFF4E5969),
                    fontSize = 12.sp,
                )
            }
            if (user.isCoinTrade) {
                Text(
                    stringResource(id = R.string.cpd官方代充), fontSize = 12.sp, lineHeight = 12.sp,
                    fontWeight = FontWeight.Medium, color = Color.White,
                    modifier = Modifier
                        .padding(end = 2.dp)
                        .background(
                            color = Color(0xFF0D94F6),
                            shape = CircleShape
                        )
                        .padding(horizontal = 8.dp, vertical = 2.dp)
                )
            }
            val mod = remember {
                Modifier
                    .height(20.dp)
                    .background(Color(0xFFF1F2F3), RoundedCornerShape(50))
                    .padding(horizontal = 8.dp)
            }
            val city = user.nativeProfile?.cityCode?.name
            if (!city.isNullOrBlank()) {
                TextLabel(text = city, textStyle = textStyle, modifier = mod)
                Spacer(modifier = Modifier.width(4.dp))
            }
            TextLabel(text = "ID ${user.publicId}", textStyle = textStyle, modifier = mod)
        }
    }
}

@Composable
fun ProfileIntro(
    intro: String,
    @StringRes emptyRes: Int,
    editEnable: Boolean = false,
    onEditIntro: OnClick = {},
) {
    Box(modifier = Modifier.fillMaxWidth()) {
        Text(stringResource(R.string.cupid_self_intro), style = MaterialTheme.typography.headlineMedium)
        if (editEnable) {
            Icon(
                painter = painterResource(id = R.drawable.cupid_edit),
                contentDescription = "",
                modifier = Modifier
                    .size(24.dp)
                    .align(Alignment.CenterEnd)
                    .clickable(onClick = onEditIntro)
                    .padding(4.dp)
            )
        }
    }
    Spacer(modifier = Modifier.height(20.dp))
    Text(text = intro.ifEmpty { stringResource(id = emptyRes) }, style = TextStyle(fontSize = 14.sp, color = Color(0xFF4E5969)))
}

/**
 * content
 * spanCount
 */
typealias ItemInfo = Pair<AnnotatedString, Int>

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun ProfileInformation(
    list: List<ItemInfo>,
    editEnable: Boolean = false,
    onEditBaseInfo: OnClick = {},
) {
    Box(modifier = Modifier.fillMaxWidth()) {
        Text(stringResource(R.string.cupid_base_info), style = MaterialTheme.typography.headlineMedium)
        if (editEnable) {
            Icon(
                painter = painterResource(id = R.drawable.cupid_edit),
                contentDescription = "",
                modifier = Modifier
                    .size(24.dp)
                    .align(Alignment.CenterEnd)
                    .clickable(onClick = onEditBaseInfo)
                    .padding(4.dp)
            )
        }
    }
    Spacer(modifier = Modifier.height(10.dp))
    FlowRow(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(10.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        maxItemsInEachRow = 2
    ) {
        list.forEachIndexed { index, item ->
            Text(
                text = item.first,
                fontSize = 14.sp,
                modifier = if (item.second == 2) {
                    Modifier.fillMaxWidth()
                } else {
                    if (index.rem(2) == 0) {
                        Modifier.fillMaxWidth(0.5f)
                    } else {
                        Modifier.weight(1f)
                    }
                }
            )
        }
    }
}

@Preview(showSystemUi = false, showBackground = true)
@Composable
private fun ProfilePreview() {
    PreviewCupidTheme {
        CupidProfileContent(user = userForPreview, listOf(), modifier = Modifier, onAction = object : INavAction {
            override fun onNavigateTo(destination: DestinationRoute) {

            }
        }, {})
    }
}

@Composable
private fun ProfileInfo(
    user: User,
    onEditIntro: OnClick,
    context: Context,
    onEditBaseInfo: OnClick,
) {
    Column {
        Spacer(modifier = Modifier.height(20.dp))
        ProfileIntro(
            intro = user.shortIntro,
            user.introEmptyRes,
            editEnable = user.isSelf,
            onEditIntro = onEditIntro
        )
        Spacer(modifier = Modifier.height(40.dp))
        val baseInfoList = remember(user) {
            user.getDisplayInfoList(context)
        }
        ProfileInformation(
            list = baseInfoList,
            editEnable = user.isSelf,
            onEditBaseInfo = onEditBaseInfo
        )
        Spacer(modifier = Modifier.height(160.dp))
    }
}
