package com.qyqy.cupid.ui.profile

import android.os.Parcelable
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyGridItemScope
import androidx.compose.foundation.lazy.grid.LazyGridState
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.ui.dialog.AnimatedDialog
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.ucoo.DataPoint
import com.qyqy.ucoo.DataTrace
import com.qyqy.ucoo.DataTraceClick
import com.qyqy.ucoo.DataTraceExposure
import com.qyqy.ucoo.R
import com.qyqy.ucoo.ReportExposureCompose
import com.qyqy.ucoo.asActivity
import com.qyqy.ucoo.base.show
import com.qyqy.ucoo.compose.theme.D_DIN
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.overScrollVertical
import com.qyqy.ucoo.compose.ui.rememberOverscrollFlingBehavior
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.user.AppPurchaseHelper
import com.qyqy.ucoo.user.Purchase
import com.qyqy.ucoo.user.WalletContract
import com.qyqy.ucoo.user.WalletViewModel
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.parcelize.Parcelize

@Parcelize
data object RechargeDialog : AnimatedDialog<IDialogAction>(), Parcelable {
    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        RechargePage {
            dialog.dismiss()
        }
    }
}


/**
 * 不需要内部fetch列表,之外布局自定义的充值
 *
 * @param content
 */
@Composable
fun RechargePageWithoutListScaffold(
    content: @Composable (
        onConfirm: (
            fkType: Int,
            productId: String,
            fkLink: String,
            orderType: Int
        ) -> Unit,
    ) -> Unit
) {
    val context = LocalContext.current

    val viewModel = viewModel<WalletViewModel>()
    LaunchedEffect(Unit) {
        viewModel.effect.onEach {
            it.show()
        }.launchIn(this)
    }
    content { fkType, productId, fkLink, orderType ->
        viewModel.sendEvent(
            WalletContract.Event.Buy(
                activity = context.asActivity!!,
                fkType = fkType,
                productId = productId,
                fkLink = fkLink,
                orderType = orderType
            )
        )
    }
}

@Composable
fun RechargePageScaffold(
    viewModel: WalletViewModel = viewModel<WalletViewModel>(),
    content: @Composable (
        purchaseHelper: AppPurchaseHelper,
        flow: Flow<List<Purchase>>,
        onConfirm: () -> Unit,
    ) -> Unit,
) {

    val scope = rememberCoroutineScope()

    val purchaseHelper = remember {
        AppPurchaseHelper()
    }

    val flow = remember {
        purchaseHelper.getRechargeFlow(scope, viewModel.chargeItemsFlow)
    }

    val context = LocalContext.current

    val loading = LocalContentLoading.current

    LaunchedEffect(Unit) {

        viewModel.uiState.onEach {
            loading.value = it.loadingState.isLoading
        }.launchIn(this)

        viewModel.effect.onEach {
            it.show()
        }.launchIn(this)

        viewModel.sendEvent(WalletContract.Event.Fetch)
    }

    content(purchaseHelper, flow) {
        val selected = purchaseHelper.getSelectedItem()
        selected?.also {
            viewModel.sendEvent(
                WalletContract.Event.Buy(
                    activity = context.asActivity!!,
                    fkType = selected.fkType,
                    productId = it.id.orEmpty(),
                    fkLink = it.good?.link.orEmpty(),
                    orderType = selected.goodType
                )
            )
        }
    }
}

@Composable
fun RechargePage(onClose: OnClick = {}) {
    val vm = viewModel<WalletViewModel>()
    LaunchedEffect(key1 = vm) {
        vm.actionFlow.onEach {
            when (it) {
                WalletViewModel.ACTION_OPEN_AGENT_CHAT -> {
                    onClose()
                }

                else -> {}
            }
        }.launchIn(this)
    }
    RechargePageScaffold(viewModel = vm) { purchaseHelper, flow, onConfirm ->
        RechargePage(purchaseHelper = purchaseHelper, flow = flow, onConfirm = onConfirm, onClose = onClose)
    }
}

@Composable
private fun RechargePage(
    purchaseHelper: AppPurchaseHelper,
    flow: Flow<List<Purchase>>,
    onConfirm: () -> Unit = {},
    onClose: OnClick = {},
) {
    val density = LocalDensity.current
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White, RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp))
            .padding(start = 16.dp, end = 16.dp, bottom = with(density) {
                androidx.compose.foundation.layout.WindowInsets.navigationBars
                    .getBottom(density)
                    .toDp()
            }.coerceAtLeast(15.dp)), horizontalAlignment = Alignment.CenterHorizontally
    ) {

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(44.dp)
        ) {
            Text(
                text = stringResource(id = R.string.cpd选择充值金额),
                modifier = Modifier.align(Alignment.Center),
                color = Color(0xFF1D2129),
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium
            )

            IconButton(onClick = onClose, modifier = Modifier.align(Alignment.CenterEnd)) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_cpd_close), contentDescription = "close", tint = Color(0xFFC9CDD4)
                )
            }
        }

        Spacer(modifier = Modifier.height(12.dp))

        RechargePageContent(purchaseHelper = purchaseHelper, flow = flow, onConfirm = onConfirm)
    }
}


@Composable
fun ColumnScope.RechargePageContent(
    purchaseHelper: AppPurchaseHelper,
    flow: Flow<List<Purchase>>,
    modifier: Modifier = Modifier,
    buttonText: String = stringResource(id = R.string.cpd_confirm_pay),
    @DataTraceExposure exposureName: String = DataTrace.Exposure.Recharge.其他入口_充值页面,
    @DataTraceClick clickName: String = DataTrace.Click.Recharge.其他入口_充值页面_充值按钮点击,
    onConfirm: () -> Unit = {},
) {
    if (exposureName.isNotEmpty()) {
        ReportExposureCompose(exposureName = exposureName, reExposureMode = false)
    }

    val list by flow.collectAsStateWithLifecycle(initialValue = emptyList())

    val isPreview by remember {
        derivedStateOf {
            list.isEmpty()
        }
    }

    val columnState: LazyGridState = rememberLazyGridState()
    LazyVerticalGrid(
        columns = GridCells.Fixed(3),
        modifier = Modifier
            .animateContentSize()
            .then(modifier)
            .fillMaxWidth()
            .heightIn(max = 560.dp)
            .overScrollVertical(),
        state = columnState,
        verticalArrangement = Arrangement.spacedBy(8.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        flingBehavior = rememberOverscrollFlingBehavior { columnState }
    ) {

        if (isPreview) {
            items(count = 6, key = { "preview_$it" }, contentType = { 0 }) {
                Spacer(
                    modifier = Modifier
                        .animateItem()
                        .fillMaxWidth()
                        .height(72.dp)
                        .clip(RoundedCornerShape(8.dp))
                        .background(Color(0xFFF5F7F9)),
                )
            }
        } else {
            items(
                items = list,
                key = {
                    "recharge-${it.id}"
                },
                span = {
                    when (it) {
                        is Purchase.Title -> {
                            GridItemSpan(3)
                        }

                        is Purchase.IGood -> {
                            GridItemSpan(1)
                        }

                        is Purchase.FkChannel -> {
                            GridItemSpan(3)
                        }
                    }
                },
                contentType = {
                    it::class.java.name
                }
            ) {
                when (it) {
                    is Purchase.Title -> {
                        Text(
                            text = stringResource(id = it.resId),
                            modifier = Modifier
                                .animateItem()
                                .padding(top = 16.dp, bottom = 4.dp),
                            color = Color(0xFF1D2129),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                        )
                    }

                    is Purchase.IGood -> {
                        BuyGoodItem(
                            selected = it.isSelected,
                            goodName = it.name,
                            price = it.price.toString(),
                            bonusLabel = it.bouns_label,
                            modifier = Modifier
                                .clickable {
                                    purchaseHelper.selectItem(it)
                                }
                        )
                    }

                    is Purchase.FkChannel -> {
                        Row(
                            modifier = Modifier
                                .animateItem()
                                .fillMaxWidth()
                                .height(48.dp)
                                .clip(RoundedCornerShape(8.dp))
                                .border(0.5.dp, Color(0xFFEAEAEA), RoundedCornerShape(8.dp))
                                .clickable {
                                    purchaseHelper.selectItem(it)
                                },
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            ComposeImage(
                                model = it.icon,
                                modifier = Modifier
                                    .padding(start = 10.dp)
                                    .size(28.dp)
                            )

                            Row(
                                modifier = Modifier.weight(1f),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = it.name,
                                    modifier = Modifier
                                        .padding(start = 8.dp)
                                        .weight(1f, false)
                                        .basicMarquee(Int.MAX_VALUE),
                                    color = Color(0xFF1D2129),
                                    fontSize = 14.sp,
                                    maxLines = 1,
                                )

                                if (it.extra != null) {
                                    Box(
                                        modifier = Modifier
                                            .padding(start = 8.dp)
                                            .height(18.dp)
                                            .background(Color(0xFFF53F3F), RoundedCornerShape(4.dp))
                                            .padding(horizontal = 6.dp),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Text(
                                            text = it.extra,
                                            color = Color.White,
                                            fontSize = 10.sp,
                                            lineHeight = 10.sp,
                                        )
                                    }
                                }
                            }


                            Image(
                                painter = if (it.isSelected) {
                                    painterResource(id = R.drawable.ic_cpd_checked)
                                } else {
                                    painterResource(id = R.drawable.ic_un_checked)
                                },
                                contentDescription = null,
                                modifier = Modifier
                                    .padding(start = 6.dp, end = 10.dp)
                                    .size(18.dp),
                                contentScale = ContentScale.Crop
                            )
                        }
                    }
                }
            }
        }
    }

    Spacer(modifier = Modifier.height(16.dp))

    AppButton(
        text = buttonText,
        modifier = Modifier
            .height(44.dp)
            .fillMaxWidth(),
        background = Color(0xFFFF5E8B),
        color = Color.White,
        enabled = !isPreview,
        onClick = {
            if (clickName.isNotEmpty()) {
                Analytics.appReportEvent(DataPoint.clickBody(clickName))
            }
            onConfirm()
        },
    )

    Spacer(modifier = Modifier.height(10.dp))
}


@Composable
private fun LazyGridItemScope.BuyGoodItem(
    selected: Boolean,
    goodName: String,
    price: String,
    bonusLabel: String,
    modifier: Modifier = Modifier,
    goodIcon: @Composable (() -> Unit)? = {
        Image(
            painter = painterResource(id = R.drawable.ic_cpd_coin), contentDescription = null, modifier = Modifier.size(16.dp)
        )
    },
) {
    Column(
        modifier = Modifier
            .animateItem()
            .fillMaxWidth()
            .height(72.dp)
            .clip(RoundedCornerShape(8.dp))
            .run {
                if (selected) {
                    background(Color(0xFFFFEBF1)).border(0.75.dp, Color(0xFFFF5E8B), RoundedCornerShape(8.dp))
                } else {
                    background(Color(0xFFF5F7F9))
                }
            }
            .then(modifier),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceBetween) {
        if (bonusLabel.isNotBlank()) {
            Text(
                bonusLabel, modifier = Modifier
                    .align(Alignment.Start)
                    .height(20.dp)
                    .background(
                        color = Color(0xFFF53F3F), shape = RoundedCornerShape(topStart = 8.dp, bottomEnd = 8.dp)
                    )
                    .padding(horizontal = 3.dp), color = Color.White,
                fontWeight = FontWeight.SemiBold, fontSize = 10.sp,
                lineHeight = 20.sp
            )

        } else {
            Spacer(modifier = Modifier.height(10.dp))
        }

        Row(verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.spacedBy(3.dp)) {
            if (goodIcon != null) {
                goodIcon()
            }
            AppText(
                text = goodName,
                color = if (selected) Color(0xFFFF5E8B) else Color(0xFF1D2129),
                fontSize = 20.sp,
                fontFamily = D_DIN
            )
        }
        AppText(
            text = price,
            color = if (selected) Color(0xFFFF5E8B) else Color(0xFF86909C),
            fontSize = 12.sp,
            lineHeight = 12.sp,
            fontFamily = D_DIN,
            modifier = Modifier.padding(bottom = 8.dp)
        )
    }
}

@Preview
@Composable
private fun PreviewRechargePage() {
    RechargePage(AppPurchaseHelper(), emptyFlow())
}