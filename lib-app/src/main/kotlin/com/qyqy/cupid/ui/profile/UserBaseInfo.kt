package com.qyqy.cupid.ui.profile

import android.content.Context
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.buildAnnotatedString
import com.qyqy.cupid.ui.home.mine.edit.ChangeableProperty
import com.qyqy.cupid.ui.home.mine.edit.getBaseInfo
import com.qyqy.cupid.ui.home.mine.edit.textRes
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.compose.presentation.room.color

interface UserBaseInfo {
    //居住地
    val locationAddress: String

    //出生地
    val bornAddress: String

    //学历
    val academicQualification: String
    val height: String

    val job: String

    //体型
    val bodySize: String

    //婚史
    val marryHistory: String

    //吸烟
    val smoke: String

    //结婚意愿
    val marryState: String

    //约会意愿
    val dateState: String
}

data class UserBaseInfoImpl(
    override val locationAddress: String = "暂未填写",
    override val bornAddress: String = "暂未填写",
    override val academicQualification: String = "暂未填写",
    override val height: String = "暂未填写",
    override val job: String = "暂未填写",
    override val bodySize: String = "暂未填写",
    override val marryHistory: String = "暂未填写",
    override val smoke: String = "暂未填写",
    override val marryState: String = "暂未填写",
    override val dateState: String = "暂未填写"
) : UserBaseInfo

fun UserBaseInfo.getFormattedInfoList(context: Context): List<ItemInfo> {
    val colorTitle = Color(0xFF86909C)
    val colorDesc = Color(0xFF1D2129)
    val list = buildList {
        add(appendInfo(context, colorTitle, colorDesc, R.string.cpd_location_address, locationAddress) to 1)
        add(appendInfo(context, colorTitle, colorDesc, R.string.cpd_born_address, bornAddress) to 1)
        add(appendInfo(context, colorTitle, colorDesc, R.string.cpd_academic, academicQualification) to 1)
        add(appendInfo(context, colorTitle, colorDesc, R.string.cpd_job, job) to 1)
        add(appendInfo(context, colorTitle, colorDesc, R.string.cpd_height, height) to 1)
        add(appendInfo(context, colorTitle, colorDesc, R.string.cpd_body_size, bodySize) to 1)
        add(appendInfo(context, colorTitle, colorDesc, R.string.cpd_marry_history, marryHistory) to 1)
        add(appendInfo(context, colorTitle, colorDesc, R.string.cpd_smoke, smoke) to 1)
        add(appendInfo(context, colorTitle, colorDesc, R.string.cpd_marry_intent, marryState) to 2)
        add(appendInfo(context, colorTitle, colorDesc, R.string.cpd_date_intent, dateState) to 2)
    }
    return list
}

val propertyList = listOf(
    ChangeableProperty.LIVE_ADDRESS,
    ChangeableProperty.BORN_ADDRESS,
    ChangeableProperty.ACADEMY,
    ChangeableProperty.JOB,
    ChangeableProperty.HEIGHT,
    ChangeableProperty.BODY_SIZE,
    ChangeableProperty.MARRY_HISTORY,
    ChangeableProperty.SMOKE,
    ChangeableProperty.MARRY_INTENT,
    ChangeableProperty.DATE_INTENT
)

fun User.getDisplayInfoList(context: Context): List<ItemInfo> {
    val colorTitle = Color(0xFF86909C)
    val colorDesc = Color(0xFF1D2129)
    val noValue = context.getString(R.string.cupid_no_value)
    return buildList {
        propertyList.forEach {
            add(appendInfo(context, colorTitle, colorDesc, it.textRes, getBaseInfo(it).ifEmpty { noValue }) to it.spanCount)
        }
    }
}


private fun appendInfo(
    context: Context,
    colorTitle: Color,
    colorDesc: Color,
    titleRes: Int,
    text: String
): AnnotatedString {
    return buildAnnotatedString {
        val t1 = context.getString(titleRes)
        color(colorTitle) {
            append(t1)
        }
        append(": ")
        color(colorDesc) {
            append(text)
        }
    }
}

