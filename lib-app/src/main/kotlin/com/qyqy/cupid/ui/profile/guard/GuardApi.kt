package com.qyqy.cupid.ui.profile.guard

import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.http.createApi
import kotlinx.serialization.json.JsonObject
import retrofit2.http.GET

interface GuardApi {
    companion object{
        val instance = createApi<GuardApi>()
    }

    @GET("/api/ucuser/v1/japan/user/guard")
    suspend fun fetchGuardList(): ApiResponse<JsonObject>

    @GET("/api/ucuser/v1/japan/user/guardian")
    suspend fun fetchGuardianList(): ApiResponse<JsonObject>
}
