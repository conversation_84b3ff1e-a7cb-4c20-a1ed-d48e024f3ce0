package com.qyqy.cupid.ui.profile.guard

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.compose.state.LiState
import com.qyqy.ucoo.compose.state.LiStateInfo
import com.qyqy.ucoo.http.getOrNull
import com.qyqy.ucoo.http.parseValue
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.utils.ComposeState
import kotlinx.coroutines.launch
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonObject

class GuardListViewModel(private val guardType: GuardType, private val liState: LiStateInfo = LiStateInfo()) : ViewModel(),
    LiState by liState {

    private val _dataList: MutableState<List<GuardUser>> = mutableStateOf(emptyList())
    val dataList: ComposeState<List<GuardUser>> = _dataList

    enum class GuardType {
        /**
         * 守护我的
         */
        IN,

        /**
         * 我守护的
         */
        OUT
    }

    companion object {
        var ruleLink: String? = null
    }

    init {
        refresh()
    }

    fun refresh() {
        viewModelScope.launch {
            if (liState.isProgressing) return@launch
            liState.onStartRefresh()
            runApiCatching {
                GuardApi.instance.let {
                    when (guardType) {
                        GuardType.IN -> it.fetchGuardianList()
                        GuardType.OUT -> it.fetchGuardList()
                    }
                }
            }.onSuccess {
                val ruleUrl = it.parseValue<String>("ruler_url")
                ruleLink = ruleUrl
                val list = it.getOrNull("list")?.jsonArray?.map { el ->
                    val u = sAppJson.decodeFromJsonElement<AppUser>(el)
                    val value = el.jsonObject.parseValue<Int>("guard_value") ?: 0
                    val isGuardAngel = el.jsonObject.parseValue<Boolean>("is_guard_angel") ?: false
                    GuardUser(u, value, isGuardAngel)
                }.orEmpty()
                _dataList.value = list
                liState.onLoadComplete(true)
            }.onFailure {
                liState.onLoadError()
                it.toast()
            }
        }
    }
}