@file:OptIn(ExperimentalMaterial3Api::class)

package com.qyqy.cupid.ui.profile.guard

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.draw.paint
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.theme.navigationPadding
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.PagerTabLayout
import com.qyqy.cupid.widgets.click
import com.qyqy.cupid.widgets.state.CupidPullRefreshBox
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.self
import com.qyqy.ucoo.utils.ComposeContent
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.launch

@Composable
fun MyGuardScreen(guardType: GuardListViewModel.GuardType = GuardListViewModel.GuardType.IN) {
    val nav = LocalAppNavController.current
    Scaffold(
        modifier = Modifier
            .fillMaxSize(),
        containerColor = Color(0xFFFFD4EE),
        topBar = {
            CupidAppBar(containerColor = Color.Transparent, title = stringResource(R.string.cpd_my_guard), actions = {
                Image(
                    painter = painterResource(id = R.drawable.cpd_question),
                    contentDescription = "question",
                    modifier = Modifier
                        .size(24.dp)
                        .click {
                            GuardListViewModel.ruleLink?.also {
                                nav.navigateWeb(it)
                            }
                        }
                )
                Spacer(modifier = Modifier.width(16.dp))
            })
        }
    ) {
        Box {
            val colorPink = MaterialTheme.colorScheme.primary
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(it)
                    .padding(horizontal = 16.dp)
                    .padding(top = 16.dp)
                    .navigationBarsPadding()
            ) {

                val pagerState = rememberPagerState(initialPage = GuardListViewModel.GuardType.entries.indexOf(guardType)) {
                    2
                }
                val tab1 = stringResource(id = R.string.cpd_tab_guard_me)
                val tab2 = stringResource(id = R.string.cpd_tab_i_guard)
                val titles = remember {
                    listOf(tab1, tab2)
                }
                val scope = rememberCoroutineScope()
                val brush = remember {
                    Brush.verticalGradient(listOf(Color(0xFFFF53BC), Color(0xFFFF93D5)))
                }
                val density = LocalDensity.current
                val size = remember {
                    with(density) {
                        Size(124.dp.toPx(), 36.dp.toPx())
                    }
                }
                val left by animateFloatAsState(
                    targetValue = with(density) { (124 * pagerState.settledPage).dp.toPx() },
                    label = "indicator"
                )
                Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Center) {
                    PagerTabLayout(
                        modifier = Modifier
                            .width((124 * pagerState.pageCount).dp)
                            .background(Color(0xFFFFB2E0), Shapes.chip)
                            .drawWithContent {
                                drawRoundRect(
                                    brush,
                                    topLeft = Offset(left, 0f),
                                    size = size,
                                    cornerRadius = CornerRadius(with(density) {
                                        18.dp.toPx()
                                    })
                                )
                                drawContent()
                            },
                        containerColor = Color.Transparent,
                        pagerState = pagerState,
                        indicator = {
                        },
                        tabs = {
                            titles.forEachIndexed { index, s ->
                                Box(
                                    modifier = Modifier
                                        .size(124.dp, 36.dp)
                                        .click {
                                            scope.launch {
                                                pagerState.scrollToPage(index)
                                            }
                                        }
                                        .clip(Shapes.chip), contentAlignment = Alignment.Center)
                                {
                                    Text(
                                        text = s,
                                        fontSize = 14.sp,
                                        color = if (pagerState.settledPage == index) Color.White else colorPink
                                    )
                                }
                            }
                        })
                }
                Spacer(modifier = Modifier.height(24.dp))
                HorizontalPager(state = pagerState) { index ->
                    when (index) {
                        0 -> GuardsForMe()
                        else -> GuardOutPage()
                    }
                }
            }

            Row(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
                    .height(114.dp)
                    .background(brush = Brush.verticalGradient(listOf(Color(0xFFFF3EB4), Color(0xFFFF93D5))))
                    .drawWithContent {
                        drawRoundRect(
                            color = Color(color = 0xFFFFD569),
                            size = size.copy(height = 2.dp.toPx())
                        )
                        drawContent()
                    }
                    .padding(horizontal = 16.dp)
                    .navigationPadding(minimumPadding = 34.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier
                        .padding(end = 16.dp)
                        .weight(1f)
                ) {
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = stringResource(id = R.string.cpd隐藏守护我的人),
                            fontSize = 14.sp,
                            lineHeight = 14.sp,
                            color = Color.White,
                            fontWeight = FontWeight.Medium
                        )

                        Row(
                            modifier = Modifier
                                .height(20.dp)
                                .background(
                                    brush = Brush.horizontalGradient(
                                        listOf(
                                            Color(0xFFF2DBB5),
                                            Color(0xFFEDB664)
                                        )
                                    ),
                                    shape = RoundedCornerShape(4.dp)
                                )
                                .padding(horizontal = 4.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(2.dp)
                        ) {
                            Image(
                                painter = painterResource(R.drawable.ic_cpd_mine_vip),
                                modifier = Modifier.size(16.dp),
                                contentDescription = null
                            )

                            Text(
                                text = stringResource(R.string.cpd_vip_特权),
                                fontSize = 12.sp,
                                lineHeight = 12.sp,
                                color = Color(0xFF593A0C),
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = stringResource(id = R.string.cpd隐藏守护开启后),
                        fontSize = 12.sp,
                        color = Color.White,
                        lineHeight = 16.sp
                    )
                }

                AppButton(
                    text = stringResource(R.string.cpd去设置),
                    modifier = Modifier.size(74.dp, 32.dp),
                    background = Color(0xFFFFD569),
                    color = Color(0xFF6A4610),
                    fontSize = 14.sp
                ) {
                    if (sUser.isVip) {
                        nav.navigate(CupidRouters.SETTINGS)
                    } else{
                        nav.navigate(CupidRouters.MEMBERSHIP_ACTIVE_PAGE)
                    }
                }
            }
        }
    }
}

@Composable
fun GuardsForMe(
    vm: GuardListViewModel = viewModel(key = GuardListViewModel.GuardType.IN.name) {
        GuardListViewModel(GuardListViewModel.GuardType.IN)
    }
) {
    val list by vm.dataList
    val top1 = list.firstOrNull()
    val nav = LocalAppNavController.current
    CupidPullRefreshBox(modifier = Modifier.fillMaxSize(), isRefreshing = vm.isRefreshing, onRefresh = { vm.refresh() }) {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(bottom = 120.dp)
        ) {
            if (vm.isLoaded) {
                item {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth(),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        TwoAvatarRow(leftAvatar = self.avatarUrl, rightAvatar = top1?.userInfo?.avatarUrl) {
                            top1 ?: return@TwoAvatarRow
                            nav.navigate(CupidRouters.C2CChat, mapOf("user" to top1.userInfo))
                        }
                        if (top1 != null) {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .heightIn(min = 64.dp), contentAlignment = Alignment.Center
                            ) {
                                GuardValue(text = stringResource(id = R.string.cpd_guard_value, top1.value))
                            }
                        }
                    }
                }
                if (list.isEmpty()) {
                    item {
                        EmptyGuard(
                            text = stringResource(R.string.cpd_no_guard), modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 120.dp)
                        )
                    }
                } else {
                    item {
                        Header()
                    }

                    itemsIndexed(list) { index, item ->
                        GuardItem(
                            rankNumber = index + 1,
                            icon = item.userInfo.avatarUrl,
                            name = item.userInfo.nickname,
                            score = item.value
                        ) {
                            nav.navigateToProfile(item.userInfo.id)
                        }
                    }

                    item {
                        Footer()
                    }

                }
            }
        }
    }
}

@Composable
fun EmptyGuard(text: String, modifier: Modifier = Modifier) {
    Column(modifier = modifier, verticalArrangement = Arrangement.Center, horizontalAlignment = Alignment.CenterHorizontally) {
        Image(
            painter = painterResource(id = R.drawable.cpd_guard_empty),
            contentDescription = "empty-guard",
            modifier = Modifier.size(80.dp)
        )
        Spacer(modifier = Modifier.height(12.dp))
        Text(text = text, color = Color(0xFFFF0BA0), fontSize = 12.sp, fontWeight = FontWeight.Medium)
    }
}


@Preview
@Composable
private fun MyGuardScreenPreview() {
    PreviewCupidTheme {
        MyGuardScreen()
    }
}

@Composable
fun GuardItem(
    rankNumber: Int,
    icon: String,
    name: String,
    score: Int,
    withIndex: Boolean = true,
    isGuardAngel: Boolean = false,
    onAvatarClick: OnClick = {}
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(if (rankNumber % 2 == 0) Color(0xFFFF93D5) else Color(0xFFFF7BCC))
            .padding(vertical = 10.dp)
            .padding(start = 6.dp, end = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (withIndex) {
            Box(modifier = Modifier.size(24.dp), contentAlignment = Alignment.Center) {
                when (rankNumber) {
                    1 -> Image(
                        painter = painterResource(id = R.drawable.cpd_rank1),
                        contentDescription = "rank",
                        modifier = Modifier.fillMaxSize()
                    )

                    2 -> Image(
                        painter = painterResource(id = R.drawable.cpd_rank2),
                        contentDescription = "rank",
                        modifier = Modifier.fillMaxSize()
                    )

                    3 -> Image(
                        painter = painterResource(id = R.drawable.cpd_rank3),
                        contentDescription = "rank",
                        modifier = Modifier.fillMaxSize()
                    )

                    else ->
                        Text(
                            text = rankNumber.toString(),
                            fontSize = 14.sp,
                            color = MaterialTheme.colorScheme.surface,
                            fontWeight = FontWeight.Medium
                        )
                }
            }
            Spacer(modifier = Modifier.width(6.dp))
            CircleComposeImage(
                model = icon, modifier = Modifier
                    .size(56.dp)
                    .noEffectClickable(onClick = onAvatarClick)
                    .border(2.dp, Color(0xFFFF1EA7), CircleShape)
                    .clip(CircleShape)
            )
        } else {
            Box(
                modifier = Modifier
                    .size(76.dp, 56.dp)
                    .then(
                        if (isGuardAngel) Modifier.paint(
                            painterResource(id = R.drawable.cpd_frame_wing),
                            contentScale = ContentScale.FillWidth
                        )
                        else Modifier
                    )
            ) {
                if (rankNumber == 1) {
                    CircleComposeImage(
                        model = icon, modifier = Modifier
                            .size(46.dp)
                            .noEffectClickable(onClick = onAvatarClick)
                            .clip(CircleShape)
                            .align(Alignment.Center)
                    )
                } else {
                    CircleComposeImage(
                        model = icon, modifier = Modifier
                            .size(56.dp)
                            .noEffectClickable(onClick = onAvatarClick)
                            .border(2.dp, Color(0xFFFF1EA7), CircleShape)
                            .clip(CircleShape)
                            .align(Alignment.Center)
                    )
                }
            }
        }

        Text(
            text = name,
            color = MaterialTheme.colorScheme.surface,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier
                .padding(horizontal = 8.dp)
                .weight(1f),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )

        Row(
            modifier = Modifier
                .background(Color(0xFFFF3EB4), Shapes.chip)
                .padding(8.dp, 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(text = score.toString(), color = Color(0xFFFFFFFF), fontSize = 14.sp)
            Spacer(modifier = Modifier.width(4.dp))
            Image(
                painter = painterResource(id = R.drawable.cpd_guard_heart),
                contentDescription = "heart",
                modifier = Modifier.width(16.dp)
            )
        }
    }
}

@Preview
@Composable
private fun GuardItemPreview() {
    PreviewCupidTheme {
        Column(modifier = Modifier.fillMaxWidth()) {
            Header()
            GuardItem(rankNumber = 1, icon = "", name = "有哈哈哈", score = 123)
            GuardItem(rankNumber = 2, icon = "", name = "有哈哈哈", score = 123)
            GuardItem(rankNumber = 3, icon = "", name = "有哈哈哈", score = 123)
            GuardItem(rankNumber = 4, icon = "", name = "有哈哈哈", score = 123)
            GuardItem(rankNumber = 5, icon = "", name = "有哈哈哈", score = 123)
            Footer()
        }
    }
}

@Composable
fun GuardValue(text: String, modifier: Modifier = Modifier) {
    val brush = remember {
        Brush.linearGradient(listOf(Color(0x00FF0BA0), Color(0x4DFF0BA0), Color(0x00FF0BA0)))
    }
    val density = LocalDensity.current
    val strokeWidth = with(density) {
        1.dp.toPx()
    }
    Box(
        modifier = modifier
            .background(Brush.linearGradient(listOf(Color(0x00FF0BA0), Color(0x26FF0BA0), Color(0x00FF0BA0))))
            .drawWithContent {
                drawLine(brush, Offset(0f, 0f), Offset(size.width, 0f), strokeWidth)
                drawLine(brush, Offset(0f, size.height - strokeWidth), Offset(size.width, size.height - strokeWidth), strokeWidth)
                drawContent()
            }
            .padding(30.dp, 6.dp),
    ) {
        Text(text = text, color = MaterialTheme.colorScheme.primary)
    }
}

@Preview
@Composable
private fun GuardValuePreview() {
    PreviewCupidTheme {
        Box(modifier = Modifier.padding(vertical = 10.dp)) {
            GuardValue(stringResource(R.string.cpd_guard_value, "12321"))
        }
    }
}


@Composable
fun AvatarWrapper(pure: Boolean, content: ComposeContent, modifier: Modifier = Modifier, outerPadding: Dp = 3.dp) {
    Box(
        modifier = modifier
            .then(
                if (pure) Modifier.background(Color(0xFFFF53BC), CircleShape) else Modifier.background(
                    Brush.verticalGradient(
                        listOf(
                            Color(0xFFFF57BE),
                            Color(0xFFFFEBF7),
                            Color(0xFFFFEBF7),
                            Color(0xFFFF44B7)
                        )
                    ),
                    CircleShape
                )
            )
            .padding(outerPadding)
            .border(2.dp, Color(0xFFFFB772), CircleShape)
            .padding(4.dp),
        contentAlignment = Alignment.Center
    ) {
        content()
    }
}


@Composable
fun TwoAvatarRow(leftAvatar: String, rightAvatar: String?, onRightAvatarClick: OnClick = {}) {
    Box(modifier = Modifier.size(204.dp, 88.dp)) {

        //ring
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 10.dp), horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Image(
                painter = painterResource(id = R.drawable.cpd_right_wing),
                contentDescription = "left-wing",
                modifier = Modifier.size(26.dp, 40.dp)
            )

            Image(
                painter = painterResource(id = R.drawable.cpd_left_wing),
                contentDescription = "right-wing",
                modifier = Modifier.size(26.dp, 40.dp)
            )
        }

        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp)
        ) {
            AvatarWrapper(false, content = {
                CircleComposeImage(model = leftAvatar, modifier = Modifier.fillMaxSize())
            }, modifier = Modifier.size(88.dp))

            AvatarWrapper(
                rightAvatar == null,
                content = {
                    if (rightAvatar.isNullOrEmpty()) {
                        Image(painter = painterResource(id = R.drawable.cpd_empty_rank), contentDescription = "empty")
                    } else {
                        CircleComposeImage(model = rightAvatar, modifier = Modifier.fillMaxSize())
                    }
                }, modifier = Modifier
                    .size(88.dp)
                    .noEffectClickable(onClick = onRightAvatarClick)
                    .align(Alignment.CenterEnd)
            )
        }

        //heart
        Image(
            painter = painterResource(id = R.drawable.cpd_icon_heart),
            contentDescription = "heart",
            modifier = Modifier
                .size(40.dp)
                .align(Alignment.Center)
        )
    }
}

@Preview
@Composable
private fun TowAvatarRowPreview() {
    PreviewCupidTheme {
        TwoAvatarRow("", null)
    }
}

@Composable
private fun Header(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(52.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(28.dp)
                .background(Color(0xFFFF93D5), RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp))
                .align(Alignment.BottomCenter)
        )
        Box(
            modifier = Modifier
                .size(206.dp, 42.dp)
                .align(Alignment.BottomCenter)
                .padding(bottom = 10.dp)
                .paint(painter = painterResource(id = R.drawable.cpd_button_guard), contentScale = ContentScale.FillWidth)
        ) {
            Text(
                text = stringResource(R.string.cpd_guard_rank),
                fontSize = 16.sp,
                color = MaterialTheme.colorScheme.surface,
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
    }
}

@Composable
private fun Footer(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(26.dp)
            .background(Color(0xFFFF93D5), RoundedCornerShape(bottomStart = 24.dp, bottomEnd = 24.dp))
    )
}

@Composable
fun GiveOutList(list: List<GuardUser>, modifier: Modifier = Modifier, loaded: Boolean = true) {
    val nav = LocalAppNavController.current
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .navigationBarsPadding()
            .let {
                if (list.isEmpty()) {
                    it
                } else {
                    it
                        .background(Color(0xFFFF93D5), RoundedCornerShape(24.dp))
                        .padding(vertical = 26.dp)
                }
            },
        contentPadding = PaddingValues(bottom = 120.dp)
    ) {
        if (loaded) {
            if (list.isEmpty()) {
                item {
                    EmptyGuard(
                        text = stringResource(id = R.string.cpd_no_guard_target),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 208.dp)
                    )
                }
            } else {
                itemsIndexed(list) { index, item ->
                    GuardItem(
                        rankNumber = index + 1,
                        icon = item.userInfo.avatarUrl,
                        name = item.userInfo.nickname,
                        score = item.value,
                        withIndex = false,
                        isGuardAngel = item.isGuardAngel
                    ) {
                        nav.navigateToProfile(item.userInfo.id)
                    }
                }
            }
        }
    }
}

@Composable
fun GuardOutPage(
    vm: GuardListViewModel = viewModel(key = GuardListViewModel.GuardType.OUT.name) {
        GuardListViewModel(GuardListViewModel.GuardType.OUT)
    }
) {
    val list by vm.dataList
    CupidPullRefreshBox(isRefreshing = vm.isRefreshing, onRefresh = { vm.refresh() }) {
        GiveOutList(list = list, loaded = vm.isLoaded)
    }
}

data class GuardUser(
    val userInfo: User,
    val value: Int,
    val isGuardAngel: Boolean = false,
    val displayValue: String = value.toString()
)

@Preview
@Composable
private fun GiveOutListPreview() {
    PreviewCupidTheme {
        val list = buildList<GuardUser> {
            repeat(5) {
                add(GuardUser(userForPreview, 120))
            }
        }
        GiveOutList(list = list)
    }
}