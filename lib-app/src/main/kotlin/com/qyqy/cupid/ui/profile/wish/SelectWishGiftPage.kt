package com.qyqy.cupid.ui.profile.wish

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.overScrollVertical
import com.qyqy.ucoo.compose.ui.rememberOverscrollFlingBehavior


@Composable
fun SelectWishGiftPage(
    editWishEntryState: MutableState<EditWishEntry>,
    wishViewModel: WishViewModel,
) {
    val editWishEntry = editWishEntryState.value

    val list = wishViewModel.wishGiftList

    val isPreview by remember {
        derivedStateOf {
            wishViewModel.wishGiftList.isEmpty()
        }
    }

    val scope = rememberCoroutineScope()

    if (isPreview) {
        LaunchedEffect(key1 = Unit) {
            wishViewModel.fetchWishGiftList(scope)
        }
    }

    val state = if (editWishEntry.giftId == -1 || isPreview) {
        rememberLazyGridState()
    } else {
        rememberLazyGridState(remember {
            list.forEachIndexed { index, gift ->
                if (gift.id == editWishEntry.giftId) {
                    return@remember index
                }
            }
            0
        })
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(372.dp)
            .background(color = Color.White, shape = Shapes.corner12)
            .padding(horizontal = 8.dp, vertical = 16.dp)
    ) {

        LazyVerticalGrid(
            columns = GridCells.Fixed(4),
            modifier = Modifier
                .fillMaxSize()
                .overScrollVertical(),
            state = state,
            horizontalArrangement = Arrangement.spacedBy(5.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp),
            userScrollEnabled = !isPreview,
            flingBehavior = rememberOverscrollFlingBehavior { state },
        ) {
            if (isPreview) {
                items(12, key = { "preview_$it" }, contentType = { 0 }) {
                    Spacer(
                        modifier = Modifier
                            .animateItem()
                            .height(108.dp)
                            .clip(RoundedCornerShape(8.dp))
                            .background(Color(0xFFF1F2F3))
                    )
                }
            } else {
                items(list, contentType = { 1 }) { item ->
                    val selected = item.id == editWishEntry.giftId
                    Column(
                        modifier = Modifier
                            .animateItem()
                            .height(108.dp)
                            .clip(RoundedCornerShape(8.dp))
                            .run {
                                if (selected) {
                                    background(Color(0x1AFF5E8B)).border(1.dp, Color(0xFFFF5E8B), RoundedCornerShape(8.dp))
                                } else {
                                    this
                                }
                            }
                            .clickable {
                                editWishEntryState.value = editWishEntry.copy(
                                    giftId = item.id,
                                    giftName = item.name
                                )
                            },
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        Spacer(modifier = Modifier.height(6.dp))
                        ComposeImage(model = item.icon, modifier = Modifier.size(58.dp), loading = null)
                        Spacer(modifier = Modifier.height(4.dp))
                        AutoSizeText(
                            text = item.name,
                            color = Color(0xFF1D2129),
                            fontSize = 12.sp,
                            maxLines = 1,
                            minTextSize = 10.sp,
                            maxTextSize = 12.sp
                        )
                        Spacer(modifier = Modifier.height(3.dp))
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_cpd_coin),
                                contentDescription = null,
                                modifier = Modifier.size(10.dp)
                            )
                            Spacer(modifier = Modifier.width(2.dp))
                            AppText(text = "${item.price}", color = Color(0xFFFFB71A), fontSize = 10.sp)
                        }
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewSelectWishGiftPage() {
    PreviewCupidTheme {
        SelectWishGiftPage(remember {
            mutableStateOf(EditWishEntry())
        }, C2CWishViewModel.Preview)
    }
}