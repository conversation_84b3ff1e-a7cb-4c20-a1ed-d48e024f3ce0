package com.qyqy.cupid.ui.profile.wish

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.widgets.CpdAppTabRow
import com.qyqy.cupid.widgets.state.CupidPullRefreshBox
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppTab
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WishListPage(
    wishViewModel: C2CWishViewModel,
    onNavToRecord: OnClick = {},
    onAddWish: OnClick = {},
    onRemoveWish: (Int, WishEntry) -> Unit = { _, _ -> },
    onSendGift: (gift: WishGift) -> Unit = {},
) {
    Column(modifier = Modifier.fillMaxWidth()) {
        Row(
            modifier = Modifier
                .align(Alignment.End)
                .padding(end = 16.dp, bottom = 10.dp)
                .clickable(onClick = onNavToRecord),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(text = stringResource(id = R.string.cpd心愿助力记录), color = Color.White, fontSize = 12.sp)
            Icon(
                painter = painterResource(id = R.drawable.ic_cpd_arrow_right),
                contentDescription = null,
                tint = Color.White
            )
        }
        val pagerState = rememberPagerState(initialPage = wishViewModel.wishListPage) {
            2
        }

        wishViewModel.wishListPage = pagerState.currentPage

        CpdAppTabRow(
            tabs = listOf(
                AppTab(stringResource(id = R.string.cpd我的心愿单)),
                AppTab(stringResource(id = R.string.cpdTA的心愿单))
            ),
            pagerState = pagerState,
            modifier = Modifier.padding(horizontal = 50.dp),
            indicatorColor = Color.White,
            tabSelectedColor = Color.White,
            tabUnSelectedColor = Color.White.copy(0.5f),
            tabHeight = 28.dp
        )

        val scope = rememberCoroutineScope()

        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.Top
        ) { page ->
            val isSelf = page == 0
            val wishEntries = if (isSelf) wishViewModel.myWishEntries else wishViewModel.targetWishEntries

            var isRefreshing by remember {
                mutableStateOf(if (isSelf) wishViewModel.autoRefresh1 else wishViewModel.autoRefresh2)
            }

            val onRefresh: () -> Unit = {
                scope.launch {
                    if (isSelf) {
                        wishViewModel.querySelfWishList()
                    } else {
                        wishViewModel.queryTargetWishList()
                    }
                    isRefreshing = false
                }
            }

            if (isRefreshing) {
                LaunchedEffect(key1 = Unit) {
                    if (isRefreshing) {
                        onRefresh()
                    }
                }
            }

            CupidPullRefreshBox(
                isRefreshing = isRefreshing,
                onRefresh = onRefresh,
                modifier = Modifier.fillMaxWidth(),
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 450.dp)
                        .verticalScroll(rememberScrollState())
                        .padding(horizontal = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(10.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = if (isSelf) wishViewModel.wishListConfig.myWishlistDesc else wishViewModel.wishListConfig.taWishlistDesc,
                        modifier = Modifier
                            .align(Alignment.Start)
                            .padding(top = 15.dp),
                        color = Color(0xFFEFE4FF),
                        fontSize = 12.sp
                    )

                    wishEntries.forEachIndexed { index, wishEntry ->
                        Box {
                            WishItem(isSelf, index, wishEntry) {
                                if (isSelf) {
                                    onRemoveWish(index + 1, wishEntry)
                                } else {
                                    onSendGift(wishEntry.gift)
                                }
                            }
                            if (wishEntry.process >= wishEntry.count) {
                                Box(
                                    modifier = Modifier
                                        .background(
                                            color = Color(0xFF8D42F5),
                                            shape = RoundedCornerShape(topStart = 8.dp, bottomEnd = 8.dp)
                                        )
                                        .padding(horizontal = 4.dp)
                                ) {
                                    Text(text = stringResource(id = R.string.cpd已达成), color = Color.White, fontSize = 12.sp)
                                }
                            }
                        }
                    }

                    if (isSelf && wishEntries.size < wishViewModel.wishListConfig.countLimit) {
                        AddWishItem(onAddWish)
                    } else if (!isSelf && wishEntries.isEmpty()) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_cpd_empty_wish),
                            contentDescription = null,
                            modifier = Modifier
                                .padding(top = 45.dp)
                                .size(120.dp)
                        )

                        Text(
                            text = stringResource(id = R.string.cpd暂无心愿),
                            color = colorResource(id = R.color.white_alpha_50),
                            fontSize = 12.sp
                        )
                    }

                    if (isSelf && wishViewModel.wishListConfig.rule.isNotEmpty()) {
                        Text(
                            text = wishViewModel.wishListConfig.rule,
                            modifier = Modifier.padding(top = 16.dp),
                            color = Color.White,
                            fontSize = 12.sp,
                            lineHeight = 16.sp,
                        )
                    } else if (!isSelf && wishViewModel.wishListConfig.taRule.isNotEmpty()) {
                        Text(
                            text = wishViewModel.wishListConfig.taRule,
                            modifier = Modifier.padding(top = 16.dp),
                            color = Color.White,
                            fontSize = 12.sp,
                            lineHeight = 16.sp,
                        )
                    }
                }
            }
        }
    }
}



@Composable
private fun WishItem(self: Boolean, index: Int, wishEntry: WishEntry, onClick: OnClick = {}) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(80.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(Color(0xFFD48EFF))
            .border(
                width = 0.5.dp,
                brush = Brush.verticalGradient(listOf(Color(0x4DFFFFFF), Color(0x1AFFFFFF))),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(horizontal = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {

        Box(
            modifier = Modifier
                .size(64.dp)
                .background(Color(0xFFE0ADFF), RoundedCornerShape(4.dp))
        ) {
            ComposeImage(
                model = wishEntry.gift.icon,
                modifier = Modifier
                    .size(56.dp)
                    .align(Alignment.Center),
                loading = null
            )
        }

        Column(
            modifier = Modifier
                .weight(1f)
                .height(60.dp),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            AppText(
                text = stringResource(id = R.string.cpd心愿, index + 1, wishEntry.gift.name),
                color = Color.White,
                fontSize = 14.sp,
                maxLines = 1
            )
            Row(verticalAlignment = Alignment.CenterVertically) {
                LinearProgressIndicator(
                    progress = { wishEntry.process.toFloat().div(wishEntry.count).coerceIn(0f, 1f) },
                    modifier = Modifier
                        .fillMaxWidth(0.65f)
                        .height(8.dp)
                        .clip(CircleShape),
                    color = Color(0xFFFFE353),
                    trackColor = Color(0xFFE1B0FF),
                )
                AppText(
                    text = "${wishEntry.process}/${wishEntry.count}",
                    modifier = Modifier.padding(start = 8.dp),
                    color = Color(0xFFFFE353),
                    fontSize = 12.sp,
                    maxLines = 1
                )
            }
            AppText(
                text = stringResource(id = R.string.cpd感恩方式_, wishEntry.thanks.orEmpty()),
                modifier = Modifier.basicMarquee(),
                color = Color.White,
                fontSize = 12.sp,
                maxLines = 1
            )
        }

        if (self) {
            Icon(
                painter = painterResource(id = R.drawable.ic_cpd_close_grey),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.Top)
                    .offset(x = 4.dp, y = 3.dp)
                    .size(16.dp)
                    .noEffectClickable(onClick = onClick),
                tint = colorResource(id = R.color.white_alpha_70)
            )
        } else {
            AppButton(
                text = stringResource(id = R.string.cpd_txt_give),
                brush = Brush.verticalGradient(listOf(Color(0xFFFD8EFF), Color(0xFF8247FF))),
                modifier = Modifier.size(56.dp, 24.dp),
                color = Color.White,
                fontSize = 12.sp,
                onClick = onClick
            )
        }
    }
}

@Composable
private fun AddWishItem(onClick: OnClick = {}) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(80.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(Color(0xFFBD8EFF))
            .clickable(onClick = onClick),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp, Alignment.CenterHorizontally)
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_cpd_add_wish),
            contentDescription = null,
            modifier = Modifier.size(24.dp)
        )
        Text(text = stringResource(id = R.string.cpd添加我的心愿), color = Color.White, fontSize = 14.sp)
    }
}

@Preview
@Composable
private fun PreviewWishListPage() {
    WishPageScaffold {
        WishListPage(C2CWishViewModel.Preview)
    }
}