package com.qyqy.cupid.ui.profile.wish

import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.User
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.text.SimpleDateFormat
import java.util.Date


sealed class WishPage {
    data object Home : WishPage()
    data object Record : WishPage()
    data object Add : WishPage()
    data class Detail(val index: Int, val userId: String?) : WishPage()
}

enum class AddWishPage {
    Home,
    Gift,
    GiftCount
}

@Serializable
data class WishListModel(
    @SerialName("wishlists")
    val list: List<WishEntry>,
)

@Serializable
data class RoomWishListModel(
    @SerialName("wishlists")
    val list: List<RoomUserWishListModel>,
)

@Serializable
data class RoomUserWishListModel(
    @SerialName("user")
    val user: AppUser? = null,
    @SerialName("wishlists")
    val list: List<WishEntry>? = null,
) {
    init {
        if (!list.isNullOrEmpty()) {
            check(user != null)
        }
    }
}

@Serializable
data class WishHelpListModel(
    @SerialName("wishlist_help_records")
    val list: List<WishHelpEntry>,
)

@Serializable
data class WishEntry(
    val id: Int,
    val gift: WishGift,
    val count: Int = 1,
    val process: Int = 0,
    val thanks: String? = null,
)

data class EditWishEntry(
    val giftId: Int = -1,
    val giftName: String = "",
    val selectCount: Int = -1,
    val inputCount: Int = -1,
    val thanks: String? = null,
) {

    val isOk: Boolean
        get() = giftId != -1 && count > 0

    val count get() = if (inputCount <= 0) selectCount else inputCount

    val countText: String
        get() {
            val c = count
            return if (c <= 0) {
                ""
            } else {
                c.toString()
            }
        }

    val inputCountText get() = if (inputCount <= 0) "" else inputCount.toString()

}

@Serializable
data class WishListConfig(
    @SerialName("count_limit")
    val countLimit: Int = 0, // 3
    @SerialName("gift_count_limit")
    val giftCountLimit: Int = 0, // 150
    @SerialName("gift_recommended_count")
    val giftRecommendedCount: List<Int> = listOf(),
    @SerialName("recommended_thanks")
    val recommendedThanks: List<String> = listOf(),
    val rule: String = "", // 1V1专属心愿，仅聊天对方可见，每次设置的心愿，将在次月1日0点失效
    @SerialName("ta_rule")
    val taRule: String = "", // 1V1专属心愿，仅聊天对方可见，每次设置的心愿，将在次月1日0点失效
    @SerialName("thanks_word_count")
    val thanksWordCount: Int = 0, // 15
    @SerialName("desc")
    val myWishlistDesc: String = "", // 1V1专属心愿单，让TA帮你实现
    @SerialName("ta_desc")
    val taWishlistDesc: String = "", // 1V1专属心愿单，帮TA完成心愿
) {

    companion object {
        val Preview = WishListConfig(
            countLimit = 3,
            giftCountLimit = 100,
            thanksWordCount = 20,
            giftRecommendedCount = listOf(1, 5, 10, 20, 50, 100, 1314),
            recommendedThanks = listOf("哈哈大师傅", "的时间分", "过分了看见", "过分了看监控", "轻微破俄日", "哦屁肉头贷款"),
            myWishlistDesc = "1V1专属心愿单，让TA帮你实现",
            taWishlistDesc = "1V1专属心愿单，帮TA完成心愿",
            rule = "「わたし」の欲しいものリストとは？ 1.欲しいものリストは、1V1チャットで相手からどうんなギフトを貰う願いです。欲しいギフトを設定する際に、自分のお礼返し方法設定することができます。相手があなたの欲しいギフトをくれたら、あなたの欲しいものリストをサポートしたとみられます。「サポート履歴」で誰がサポートしたを確認し、事前に設定した方法でお礼返しことができます。そうすることで、より多くギフトを手に入れることができますよ！ 2.設定した欲しいものリストは、チャットページでお互いに表示され、あなたとプライベートチャットしたユーザー全員同じように表示されます。設定したウィッシュリストは、プライベートチャットページでお互いに表示され、あなたとのプライベートチャットページでは、全員が同じウィッシュリストを見ることができます。 欲しいものリストに設定したギフトの数量を達すると、その欲しいものリストは達成され、新しい欲しいものギフトを設定することができます。 3.毎回設定たびに有効期限は翌月の1日0:00までになり、リセットすることができます。",
            taRule = "很舒服哈哈发多少分的身份哈收到回复大还是饭后发的啥发货都是",
        )
    }
}

@Serializable
data class WishHelpEntry(
    val user: AppUser,
    @SerialName("wishlist")
    val wishEntry: WishEntry,
    val count: Int = 1, // 10
    val timestamp: Int = 0, // 1727101617
) {
    fun format(dateFormat: SimpleDateFormat): String = dateFormat.format(Date(timestamp * 1000L))
}

@Serializable
data class WishGiftListModel(
    @SerialName("gifts")
    val list: List<WishGift>,
)

@Serializable
data class WishGift(
    @SerialName("effect_file")
    val effectFile: String = "",
    val icon: String = "", // /media/opsite/gift/icon/image34.jpeg
    val id: Int = 0, // 380
    val name: String = "", // dasda
    @SerialName("native_region")
    val nativeRegion: Int = 0, // 1
    val price: Int = 0, // 1111
    @SerialName("price_type")
    val priceType: Int = 0, // 3
    val t: Int = 0, // 0
    @SerialName("blindbox_info")
    val blindbox: WishGift? = null,
) {
    val rawGiftId: Int
        get() = if (t == 4) {
            blindbox?.id ?: id
        } else {
            id
        }
}