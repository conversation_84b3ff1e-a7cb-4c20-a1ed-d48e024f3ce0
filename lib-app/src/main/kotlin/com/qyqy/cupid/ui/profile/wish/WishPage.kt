package com.qyqy.cupid.ui.profile.wish

import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.imePadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.qyqy.cupid.im.panel.gift.GiftPosition
import com.qyqy.cupid.theme.navigationPadding
import com.qyqy.cupid.ui.IC2CAction
import com.qyqy.cupid.ui.dialog.AnimatedDialog
import com.qyqy.cupid.ui.dialog.ContentAlertDialog
import com.qyqy.cupid.ui.dialog.DialogButton
import com.qyqy.cupid.ui.dialog.DialogQueue
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser

data class WishListTableDialog(val wishViewModel: C2CWishViewModel) : AnimatedDialog<IC2CAction>() {

    @Composable
    override fun Content(dialog: IDialog, onAction: IC2CAction?) {
        WishHostPage(wishViewModel, onSendGift = {
            onAction?.onShowGiftPanel(
                GiftPosition(it.rawGiftId)
            )
        }) {
            onAction?.navigateToProfile(it.id)
        }
    }

}


@Composable
fun WishPageScaffold(minHeight: Dp = 420.dp, content: @Composable ColumnScope.() -> Unit) {
    Box(
        modifier = Modifier
            .imePadding()
            .fillMaxWidth()
            .heightIn(min = minHeight),
    ) {
        Column(
            modifier = Modifier.matchParentSize()
        ) {
            Image(
                painter = painterResource(id = R.drawable.bg_cpd_wish_page_top),
                contentDescription = null,
                modifier = Modifier.fillMaxWidth(),
                contentScale = ContentScale.FillWidth
            )
            Spacer(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .background(Color(0xFFA26FE9))
            )
            Image(
                painter = painterResource(id = R.drawable.bg_cpd_wish_page_bottom),
                contentDescription = null,
                modifier = Modifier.fillMaxWidth(),
                contentScale = ContentScale.FillWidth
            )
        }
        Column(
            modifier = Modifier
                .animateContentSize()
                .fillMaxWidth()
                .navigationPadding(20.dp)
        ) {
            Spacer(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(9.4f)
            )
            content()
        }
    }
}

@Composable
fun WishHostPage(
    wishViewModel: C2CWishViewModel,
    dialogQueue: DialogQueue<*> = LocalDialogQueue.current,
    onSendGift: (gift: WishGift) -> Unit = {},
    onNavProfile: (AppUser) -> Unit = {},
) {

    val scope = rememberCoroutineScope()

    BackHandler(wishViewModel.wishPage != WishPage.Home) {
        wishViewModel.navToPage(WishPage.Home)
    }

    WishPageScaffold {
        AnimatedContent(targetState = wishViewModel.wishPage, label = "host") {
            when (it) {
                WishPage.Home -> WishListPage(
                    wishViewModel = wishViewModel,
                    onNavToRecord = {
                        wishViewModel.navToPage(WishPage.Record)
                    },
                    onAddWish = {
                        wishViewModel.navToPage(WishPage.Add)
                    },
                    onRemoveWish = { index, item ->
                        dialogQueue.pushCenterDialog(true) { dialog, _ ->
                            ContentAlertDialog(
                                content = stringResource(id = R.string.cpd确定要删除心愿一吗, index),
                                startButton = DialogButton(stringResource(id = R.string.cpd_cancel)) {
                                    dialog.dismiss()
                                },
                                endButton = DialogButton(stringResource(id = R.string.cpd_confirm)) {
                                    dialog.dismiss()
                                    wishViewModel.removeWish(scope, item)
                                }
                            )
                        }
                    },
                    onSendGift = onSendGift
                )

                WishPage.Add -> EditWishHostPage(
                    wishViewModel = wishViewModel,
                    onBack = {
                        wishViewModel.navToPage(WishPage.Home)
                    },
                    onSave = { item ->
                        wishViewModel.saveNewWish(scope, item)
                    }
                )

                WishPage.Record -> WishHelpRecordPage(
                    wishViewModel = wishViewModel,
                    onBack = {
                        wishViewModel.navToPage(WishPage.Home)
                    },
                    onNavProfile = onNavProfile
                )

                else -> Unit
            }
        }
    }
}

@Preview
@Composable
fun PreviewWishHostPage() {
    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.BottomCenter) {
        WishHostPage(C2CWishViewModel.Preview, DialogQueue<IDialogAction>())
    }
}