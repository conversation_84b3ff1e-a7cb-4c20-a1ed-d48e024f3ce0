package com.qyqy.cupid.ui.profile.wish

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import com.overseas.common.utils.ResultContinuation
import com.qyqy.cupid.event.IMEvent
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.IMCompatListener
import com.qyqy.ucoo.im.compat.MsgFilter
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.sUser
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query
import java.util.Stack

interface WishListApi {

    /**
     * 心愿单配置
     */
    @GET("api/ucuser/v1/wishlist/conf")
    suspend fun queryWishListConfig(): ApiResponse<WishListConfig>

    /**
     * 心愿单列表
     */
    @GET("api/ucuser/v1/wishlist/list")
    suspend fun queryWishList(@Query("userid") userId: String): ApiResponse<WishListModel>

    /**
     * 创建心愿单
     */
    @POST("api/ucuser/v1/wishlist/create")
    suspend fun createWish(@Body body: Map<String, String>): ApiResponse<WishListModel>

    /**
     * 删除心愿单
     */
    @POST("api/ucuser/v1/wishlist/delete")
    suspend fun deleteWish(@Body body: Map<String, Int>): ApiResponse<WishListModel>

    /**
     * 助力我的心愿
     */
    @GET("api/ucuser/v1/wishlist/helped")
    suspend fun queryHelpMineWishList(): ApiResponse<WishHelpListModel>

    /**
     * 我助力的心愿
     */
    @GET("api/ucuser/v1/wishlist/help")
    suspend fun queryMyHelpWishList(): ApiResponse<WishHelpListModel>

    /**
     * 心愿礼物列表
     */
    @GET("api/ucuser/v1/wishlist/gifts")
    suspend fun queryWishGiftList(): ApiResponse<WishGiftListModel>


    /**
     * 心愿单配置
     */
    @GET("api/audioroom/v1/wishlist/conf")
    suspend fun queryRoomWishListConfig(): ApiResponse<WishListConfig>

    /**
     * 心愿单列表
     */
    @GET("api/audioroom/v1/wishlist/list")
    suspend fun queryRoomWishList(@Query("room_id") id: Int): ApiResponse<RoomWishListModel>

    /**
     * 创建心愿单
     */
    @POST("api/audioroom/v1/wishlist/create")
    suspend fun createRoomWish(@Body body: Map<String, String>): ApiResponse<WishListModel>

    /**
     * 删除心愿单
     */
    @POST("api/audioroom/v1/wishlist/delete")
    suspend fun deleteRoomWish(@Body body: Map<String, Int>): ApiResponse<WishListModel>

    /**
     * 助力我的心愿
     */
    @GET("api/audioroom/v1/wishlist/helped")
    suspend fun queryRoomHelpMineWishList(): ApiResponse<WishHelpListModel>

    /**
     * 我助力的心愿
     */
    @GET("api/audioroom/v1/wishlist/help")
    suspend fun queryRoomMyHelpWishList(): ApiResponse<WishHelpListModel>

    /**
     * 心愿礼物列表
     */
    @GET("api/audioroom/v1/wishlist/gifts")
    suspend fun queryRoomWishGiftList(): ApiResponse<WishGiftListModel>
}

@Composable
fun rememberC2CWishViewModel(targetId: String): C2CWishViewModel {
    val scope = rememberCoroutineScope()
    return remember {
        C2CWishViewModel(targetId).apply {
            scope.launch {
                fetchWishListConfig(false)

                launch {
                    querySelfWishList(false)
                }

                launch {
                    queryTargetWishList(false)
                }
            }
        }
    }.also {
        DisposableEffect(key1 = it) {
            onDispose {
                it.onClear()
            }
        }
    }
}

@Composable
fun rememberRoomWishViewModel(roomId: Int): RoomWishViewModel {
    val scope = rememberCoroutineScope()
    return remember {
        RoomWishViewModel(roomId).apply {
            scope.launch {
                fetchWishListConfig(false)

                launch {
                    queryRoomWishList(false)
                }
            }
        }
    }.also {
        DisposableEffect(key1 = it) {
            onDispose {
                it.onClear()
            }
        }
    }
}

class C2CWishViewModel(
    private val targetId: String,
    wishListConfig: WishListConfig = WishListConfig(),
) : WishViewModel(wishListConfig, 0) {

    companion object {
        val Preview = C2CWishViewModel("0", WishListConfig.Preview).apply {
            myWishEntries.add(WishEntry(0, WishGift(), 10, 6))
            targetWishEntries.add(WishEntry(0, WishGift(), 10, 6))
            helpMineWishEntries.add(WishHelpEntry(userForPreview, WishEntry(0, WishGift(), 10, 6)))
            myHelpWishEntries.add(WishHelpEntry(userForPreview, WishEntry(0, WishGift(), 10, 6)))
        }
    }

    val myWishEntries = mutableStateListOf<WishEntry>()

    val targetWishEntries = mutableStateListOf<WishEntry>()

    fun resetState(self: Boolean) {
        if (self) {
            wishListPage = 0
            autoRefresh1 = true
        } else {
            wishListPage = 1
            autoRefresh2 = true
        }
    }

    suspend fun querySelfWishList(toast: Boolean = true) {
        if (!hasConfig) {
            val success = result?.suspendUntil()
            if (success == false) {
                fetchWishListConfig()
                if (result != null) {
                    return
                }
            }
        }

        runApiCatching {
            wishListApi.queryWishList(sUser.id)
        }.onSuccess {
            autoRefresh1 = false
            myWishEntries.clear()
            myWishEntries.addAll(it.list)
        }.apply {
            if (toast) {
                toastError()
            }
        }
    }

    suspend fun queryTargetWishList(toast: Boolean = true) {
        runApiCatching {
            wishListApi.queryWishList(targetId)
        }.onSuccess {
            autoRefresh2 = false
            targetWishEntries.clear()
            targetWishEntries.addAll(it.list)
        }.apply {
            if (toast) {
                toastError()
            }
        }
    }

    fun saveNewWish(scope: CoroutineScope, editWishEntry: EditWishEntry) {
        scope.launch {
            saveNewWish(buildMap {
                put("gift_id", editWishEntry.giftId.toString())
                put("count", editWishEntry.count.toString())
                if (editWishEntry.thanks != null) {
                    put("thanks", editWishEntry.thanks)
                }
            }).onSuccess {
                myWishEntries.clear()
                myWishEntries.addAll(it.list)
                navToPage(WishPage.Home)
            }.toastError()
        }
    }

    fun removeWish(scope: CoroutineScope, wishEntry: WishEntry) {
        scope.launch {
            removeWish(wishEntry).onSuccess {
                myWishEntries.clear()
                myWishEntries.addAll(it.list)
            }.toastError()
        }
    }
}

class RoomWishViewModel(
    private val roomId: Int,
    wishListConfig: WishListConfig = WishListConfig(),
) : WishViewModel(wishListConfig, 1) {

    companion object {
        val Preview = RoomWishViewModel(0, WishListConfig.Preview).apply {
            repeat(6) {
                userWishEntries.add(RoomUserWishListModel(userForPreview, listOf(WishEntry(0, WishGift(), 10, 6))))
            }
            helpMineWishEntries.add(WishHelpEntry(userForPreview, WishEntry(0, WishGift(), 10, 5)))
            myHelpWishEntries.add(WishHelpEntry(userForPreview, WishEntry(0, WishGift(), 10, 5)))
        }
    }

    val userWishEntries = mutableStateListOf<RoomUserWishListModel>()

    private val wishPageStack: Stack<WishPage> = Stack()

    private val listener = object : IMCompatListener {

        override val filter: MsgFilter = MsgFilter(roomId.toString())

        override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
            if (message.cmd == IMEvent.AUDIOROOM_WISHLIST_CHANGE) {
                message.getJsonValue<List<RoomUserWishListModel>>("wishlists")?.also {
                    userWishEntries.clear()
                    userWishEntries.addAll(it)
                }
            }
        }
    }

    init {
        IMCompatCore.addIMListener(listener)
    }

    override fun onClear() {
        IMCompatCore.removeIMListener(listener)
    }

    suspend fun queryRoomWishList(toast: Boolean = true) {
        if (!hasConfig) {
            val success = result?.suspendUntil()
            if (success == false) {
                fetchWishListConfig()
                if (result != null) {
                    return
                }
            }
        }

        runApiCatching {
            wishListApi.queryRoomWishList(roomId)
        }.onSuccess {
            autoRefresh1 = false
            userWishEntries.clear()
            userWishEntries.addAll(it.list)
        }.apply {
            if (toast) {
                toastError()
            }
        }
    }

    fun saveNewWish(scope: CoroutineScope, editWishEntry: EditWishEntry) {
        scope.launch {
            saveNewWish(buildMap {
                put("room_id", roomId.toString())
                put("gift_id", editWishEntry.giftId.toString())
                put("count", editWishEntry.count.toString())
                if (editWishEntry.thanks != null) {
                    put("thanks", editWishEntry.thanks)
                }
            }).onSuccess { result ->
                val index = userWishEntries.indexOfFirst { it.user?.id == sUser.id }
                if (index != -1) {
                    val item = userWishEntries[index]
                    userWishEntries[index] = item.copy(user = item.user, list = result.list)
                }
                pop()
            }.toastError()
        }
    }

    fun removeWish(scope: CoroutineScope, wishEntry: WishEntry) {
        scope.launch {
            removeWish(wishEntry).onSuccess { result ->
                val index = userWishEntries.indexOfFirst { it.user?.id == sUser.id }
                if (index != -1) {
                    val item = userWishEntries[index]
                    userWishEntries[index] = item.copy(user = item.user, list = result.list)
                }
            }.toastError()
        }
    }

    override fun navToPage(wishPage: WishPage) {
        if (this.wishPage != wishPage) {
            wishPageStack.push(this.wishPage)
            super.navToPage(wishPage)
        }
    }

    fun pop(): Boolean {
        if (wishPageStack.isEmpty()) {
            super.navToPage(WishPage.Home)
            return false
        }
        val item = wishPageStack.pop()
        if (item == this.wishPage) {
            return true
        }
        this.wishPage = item
        return true
    }

}

abstract class WishViewModel(wishListConfig: WishListConfig, protected val sceneType: Int) {

    var wishPage: WishPage by mutableStateOf(WishPage.Home)
        protected set

    var wishListConfig: WishListConfig by mutableStateOf(wishListConfig)
        protected set

    val helpMineWishEntries = mutableStateListOf<WishHelpEntry>()

    val myHelpWishEntries = mutableStateListOf<WishHelpEntry>()

    val wishGiftList = mutableStateListOf<WishGift>()

    protected val wishListApi = createApi<WishListApi>()

    protected var result: ResultContinuation<Boolean>? = null

    protected var hasConfig = false

    var wishListPage = 0

    var autoRefresh1 = true
        protected set

    var autoRefresh2 = true
        protected set

    var autoRefresh3 = true
        protected set

    var autoRefresh4 = true
        protected set

    open fun onClear() = Unit

    protected suspend fun saveNewWish(params: Map<String, String>): Result<WishListModel> {
        return runApiCatching {
            if (sceneType != 1) {
                wishListApi.createWish(params)
            } else {
                wishListApi.createRoomWish(params)
            }
        }
    }

    protected suspend fun removeWish(wishEntry: WishEntry): Result<WishListModel> {
        return runApiCatching {
            if (sceneType != 1) {
                wishListApi.deleteWish(mapOf("wishlist_id" to wishEntry.id))
            } else {
                wishListApi.deleteRoomWish(mapOf("wishlist_id" to wishEntry.id))
            }
        }
    }

    suspend fun fetchWishListConfig(toast: Boolean = true) {
        if (!hasConfig) {
            result = ResultContinuation()
        }
        runApiCatching {
            if (sceneType != 1) {
                wishListApi.queryWishListConfig()
            } else {
                wishListApi.queryRoomWishListConfig()
            }
        }.onSuccess {
            result?.resume(true)
            result = null
            wishListConfig = it
            hasConfig = true
        }.apply {
            if (!hasConfig && toast) {
                toastError()
            }
        }.onFailure {
            result?.resume(false)
        }
    }

    fun fetchWishGiftList(scope: CoroutineScope) {
        scope.launch {
            runApiCatching {
                if (sceneType != 1) {
                    wishListApi.queryWishGiftList()
                } else {
                    wishListApi.queryRoomWishGiftList()
                }
            }.onSuccess {
                wishGiftList.clear()
                wishGiftList.addAll(it.list)
            }.toastError()
        }
    }

    suspend fun queryHelpMineWishList() {
        runApiCatching {
            if (sceneType != 1) {
                wishListApi.queryHelpMineWishList()
            } else {
                wishListApi.queryRoomHelpMineWishList()
            }
        }.onSuccess {
            autoRefresh3 = false
            helpMineWishEntries.clear()
            helpMineWishEntries.addAll(it.list)
        }.toastError()
    }

    suspend fun queryMyHelpWishList() {
        runApiCatching {
            if (sceneType != 1) {
                wishListApi.queryMyHelpWishList()
            } else {
                wishListApi.queryRoomMyHelpWishList()
            }
        }.onSuccess {
            autoRefresh4 = false
            myHelpWishEntries.clear()
            myHelpWishEntries.addAll(it.list)
        }.toastError()
    }

    open fun navToPage(wishPage: WishPage) {
        this.wishPage = wishPage
    }

}