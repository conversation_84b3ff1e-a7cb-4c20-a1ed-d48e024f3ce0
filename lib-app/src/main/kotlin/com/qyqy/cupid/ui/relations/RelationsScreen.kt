package com.qyqy.cupid.ui.relations

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Tab
import androidx.compose.material3.TabPosition
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.theme.CupidTheme
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.INavAction
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.cupid.widgets.CupidBasicAppBar
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.UserInfo
import com.qyqy.ucoo.account.toAppUser
import com.qyqy.ucoo.accountManager
import com.qyqy.ucoo.compose.LaunchOnceEffect
import com.qyqy.ucoo.compose.presentation.ff.UserItem
import com.qyqy.ucoo.compose.state.EmptyView
import com.qyqy.ucoo.compose.state.PullRefreshBox
import com.qyqy.ucoo.compose.state.StateLayoutDefaults
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.fixCurrentPage
import com.qyqy.ucoo.mine.FriendsContract
import com.qyqy.ucoo.mine.FriendsViewModel
import com.qyqy.ucoo.utils.EntityCallback
import com.qyqy.ucoo.utils.OnClick
import eu.bambooapps.material3.pullrefresh.PullRefreshIndicatorDefaults
import kotlinx.coroutines.launch

//<editor-fold desc="Tabs">
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun TabLayout(
    pagerState: PagerState,
    titles: List<String>,
    modifier: Modifier = Modifier,
    indicatorHeight: Dp = 2.dp,
    indicator: @Composable BoxScope.() -> Unit = {
        Box(
            modifier = Modifier
                .width(10.dp)
                .height(indicatorHeight)
                .align(Alignment.TopCenter)
                .clip(Shapes.chip)
                .background(color = MaterialTheme.colorScheme.primary)
        )
    },
    itemContent: @Composable (String, Boolean) -> Unit = { title, selected ->
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(36.dp),
            contentAlignment = Alignment.Center
        ) {
            AppText(
                text = title,
                color = if (selected) Color(0xFF1D2129) else Color(0xFF86909C),
                fontSize = (16.sp).takeIf { selected } ?: 14.sp,
                maxLines = 1
            )
        }
    }
) {
    val scope = rememberCoroutineScope()
    val selectedTabIndex by remember {
        derivedStateOf { pagerState.fixCurrentPage }
    }
    TabRow(
        modifier = modifier,
        selectedTabIndex = selectedTabIndex,
        containerColor = Color.Transparent,
        contentColor = Color.Transparent,
        indicator = { tabPositions: List<TabPosition> ->
            if (selectedTabIndex < tabPositions.size) {
                Box(
                    Modifier
                        .tabIndicatorOffset(tabPositions[selectedTabIndex])
                        .height(4.dp),
                ) {
                    indicator()
                }
            }
        },
        divider = {},
    ) {
        titles.forEachIndexed { index, item ->
            val selected = selectedTabIndex == index
            Tab(
                selected = selected,
                selectedContentColor = Color.Transparent,
                unselectedContentColor = Color.Transparent,
                onClick = { scope.launch { pagerState.animateScrollToPage(index) } }
            ) {
                itemContent(item, selected)
            }
        }
    }
}


@Composable
private fun TabLayoutPreview() {
    val pagerState = rememberPagerState(0) { 2 }

    TabLayout(
        pagerState = pagerState, titles = listOf("个人资料", "礼物墙"),
    )
}
//</editor-fold>

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun RelationsScreen(@RelationType visibleRelationType: Int = Relations.FOCUS, onAction: INavAction) {
    val pagerState = rememberPagerState(
        initialPage = when (visibleRelationType) {
            Relations.FOCUS -> 0
            Relations.FANS -> 1
            else -> 2
        }
    ) {
        3
    }
    val t1 = stringResource(id = R.string.cpd_focus)
    val vm1: FriendsViewModel = viewModel(key = "relation-1")
    val t2 = stringResource(id = R.string.label_fans)
    val vm2: FriendsViewModel = viewModel(key = "relation-2")
    val t3 = stringResource(id = R.string.label_friend)
    val vm3: FriendsViewModel = viewModel(key = "relation-3")

    val titles = remember {
        listOf(t1, t2, t3)
    }
    val changed = remember {
        mutableStateOf(false)
    }
    DisposableEffect(key1 = Unit) {
        onDispose {
            if (changed.value) {
                accountManager.refreshSelfUserByRemote()
            }
        }
    }
    Scaffold(topBar = {
        CupidBasicAppBar(title = {
            TabLayout(modifier = Modifier, pagerState = pagerState, titles = titles)
        }, actions = { Box(modifier = Modifier.size(40.dp)) })//占位
    }) { pv ->
        HorizontalPager(
            state = pagerState, modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.surface)
                .padding(pv)
        ) { page: Int ->
            val relationType = when (page) {
                0 -> Relations.FOCUS
                1 -> Relations.FANS
                else -> Relations.FRIENDS
            }
            RelationList(
                relationType = relationType,
                vm = when (page) {
                    0 -> vm1
                    1 -> vm2
                    else -> vm3
                },
                onChat = {
                    onAction.onNavigateTo(CupidRouters.C2CChat, mapOf("user" to it.toAppUser()))
                },
                onChanged = {
                    changed.value = true
                },
                onAvatarClick = {
                    onAction.navigateToProfile(it.id)
                }
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RelationList(
    @RelationType relationType: Int, vm: FriendsViewModel,
    onChat: EntityCallback<UserInfo>,
    modifier: Modifier = Modifier,
    onChanged: OnClick = {},
    onAvatarClick: EntityCallback<UserInfo>
) {
    val state by vm.uiState.collectAsStateWithLifecycle()
    val isRefreshing by remember {
        derivedStateOf { state.refreshState.isLoading }
    }
    val list by remember {
        derivedStateOf { state.listState.getSuccessValue() }
    }
    val hasMore by vm.hasMore
    val loaded by vm.loaded
    val isEmpty by remember {
        derivedStateOf {
            val ls = state.listState
            ls is FriendsContract.FriendsState.Success && ls.list.isEmpty()
        }
    }
    LocalContentLoading.current.value = vm.posting.value

    LaunchOnceEffect(relationType) {
        vm.sendEvent(FriendsContract.Event.Fetch(relationType))
    }
    PullRefreshBox(modifier = modifier,
        isRefreshing = isRefreshing,
        colors = PullRefreshIndicatorDefaults.colors(
            contentColor = MaterialTheme.colorScheme.primary,
            containerColor = Color.White
        ),
        onRefresh = { vm.sendEvent(FriendsContract.Event.Fetch(relationType)) }) {
        LazyColumn(modifier = Modifier.fillMaxSize()) {
            items(list) { item ->
                UserItem(user = item, nameColor = MaterialTheme.colorScheme.onSurface, onAvatarClicked = {
                    onAvatarClick(item)
                }) {
                    when (relationType) {
                        Relations.FOCUS -> FocusButton {
                            vm.sendEvent(FriendsContract.Event.CancelFocus(item.id))
                            onChanged()
                        }

                        Relations.FANS -> FansButton(item.iFollowBack) {
                            vm.sendEvent(FriendsContract.Event.Focus(item.id))
                            onChanged()
                        }

                        else -> FriendButton {
                            onChat(item)
                        }
                    }
                }
            }

            if (loaded && !isEmpty && !isRefreshing && hasMore) {
                item {
                    LaunchedEffect(true) {
                        vm.sendEvent(FriendsContract.Event.LoadMore(relationType))
                    }
                    StateLayoutDefaults.BottomLoadBar(hasMore = true)
                }
            }
        }
        if (loaded && isEmpty) {
            Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                EmptyView(
                    textRes = when (relationType) {
                        Relations.FOCUS -> R.string.cpd暂无关注的人
                        Relations.FANS -> R.string.cpd暂无粉丝
                        else -> R.string.cpd暂无好友
                    },
                    iconRes = R.drawable.cupid_empty
                )
            }
        }
    }
}

@Composable
private fun FocusButton(onCancelFocus: OnClick) {
    val mod = Modifier
        .widthIn(min = 60.dp)
        .padding(horizontal = 10.dp)
        .defaultMinSize(minHeight = 32.dp)
    OutlinedButton(
        modifier = mod,
        border = BorderStroke(1.dp, Color(0xFFE5E6EB)),
        colors = ButtonDefaults.outlinedButtonColors(contentColor = Color(0xFF86909C)),
        onClick = onCancelFocus
    ) {
        AutoSizeText(text = stringResource(id = R.string.cpd_cancel_follow))
    }
}

@Composable
private fun FansButton(iFollowBack: Boolean = false, onFocus: OnClick) {
    val mod = Modifier
        .widthIn(min = 60.dp)
        .padding(horizontal = 10.dp)
        .defaultMinSize(minHeight = 32.dp)
    if (iFollowBack) {
        OutlinedButton(
            modifier = mod,
            border = BorderStroke(1.dp, Color(0xFFE5E6EB)),
            colors = ButtonDefaults.outlinedButtonColors(contentColor = Color(0xFF86909C)),
            enabled = false,
            onClick = {}
        ) {
            AutoSizeText(text = stringResource(id = R.string.cupid_focus_each))
        }
    } else {
        OutlinedButton(
            modifier = mod,
            border = BorderStroke(1.dp, MaterialTheme.colorScheme.primary),
            colors = ButtonDefaults.outlinedButtonColors(contentColor = MaterialTheme.colorScheme.primary),
            onClick = onFocus
        ) {
            AutoSizeText(text = stringResource(id = R.string.cupid_follow_back))
        }
    }
}

@Composable
private fun FriendButton(
    onChat: OnClick,
) {
    val mod = Modifier
        .widthIn(min = 60.dp)
        .padding(horizontal = 10.dp)
        .defaultMinSize(minHeight = 32.dp)
    OutlinedButton(
        modifier = mod,
        border = BorderStroke(1.dp, MaterialTheme.colorScheme.primary),
        colors = ButtonDefaults.outlinedButtonColors(contentColor = MaterialTheme.colorScheme.primary),
        onClick = onChat
    ) {
        //私聊
        AutoSizeText(text = "チャット")
    }
}

@Preview
@Composable
private fun Preview1() {
    CupidTheme {
        FocusButton {

        }
    }
}

@Preview
@Composable
private fun Preview2() {
    CupidTheme {
        FansButton(iFollowBack = true) {

        }
    }
}

@Preview
@Composable
private fun Preview3() {
    CupidTheme {
        FriendButton {

        }
    }
}