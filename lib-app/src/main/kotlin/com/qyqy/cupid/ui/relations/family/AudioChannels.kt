@file:OptIn( ExperimentalMaterial3Api::class)

package com.qyqy.cupid.ui.relations.family

import androidx.compose.animation.core.FastOutLinearInEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.qyqy.cupid.theme.CupidTheme
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.live.VoiceLiveHelper
import com.qyqy.cupid.widgets.AvatarsRow
import com.qyqy.cupid.widgets.CpdButton
import com.qyqy.cupid.widgets.click
import com.qyqy.cupid.widgets.state.CupidPullRefreshBox
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.app
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.state.EmptyView
import com.qyqy.ucoo.compose.state.LoadMoreView
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AnimatedDialog
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.core.page.PageLoadEvent
import com.qyqy.ucoo.im.bean.Room
import com.qyqy.ucoo.im.room.RoomRepository
import com.qyqy.ucoo.sUserFlow
import com.qyqy.ucoo.tribe.main.voice_channel.VoiceChannelViewModel
import com.qyqy.ucoo.utils.EntityCallback
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.launch

@Composable
fun AudioChannelItem(
    icon: String,
    title: String,
    avatars: List<String>,
    peopleCount: Int,
    modifier: Modifier = Modifier,
    hasCoinWinnerGame: Boolean = false,
    hasRedPacket: Boolean = false
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.surface, Shapes.corner12)
            .padding(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {

        ComposeImage(
            model = icon, modifier = Modifier
                .size(72.dp)
                .clip(Shapes.extraSmall)
        )
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(start = 10.dp), verticalArrangement = Arrangement.Center
        ) {
            Text(text = title, style = MaterialTheme.typography.headlineMedium, maxLines = 1, overflow = TextOverflow.Ellipsis)
            Spacer(modifier = Modifier.height(10.dp))
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                AvatarsRow(avatars = avatars, modifier = Modifier
                    .weight(1f)
                    .padding(end = 8.dp))

                Row(verticalAlignment = Alignment.CenterVertically) {
                    if (hasRedPacket) {
                        Image(
                            painter = painterResource(id = R.drawable.cpd_ic_small_rp),
                            contentDescription = "rp",
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                    }
                    if (hasCoinWinnerGame) {
                        Image(
                            painter = painterResource(id = R.drawable.cpd_winner_widget),
                            contentDescription = "coin_winner",
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                    }
                    AudioWaves()
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(text = peopleCount.toString(), style = MaterialTheme.typography.labelMedium.copy(fontSize = 12.sp))
                }
            }
        }
    }
}

@Composable
fun AudioWaves() {
    val color = MaterialTheme.colorScheme.primary
    val mod = remember {
        Modifier
            .width(2.dp)
            .background(color, RoundedCornerShape(1.dp))
    }
    val trans = rememberInfiniteTransition(label = "wave")
    val fraction by trans.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        infiniteRepeatable(animation = tween(400, easing = FastOutLinearInEasing), repeatMode = RepeatMode.Reverse),
        label = "tr"
    )
    Row(modifier = Modifier.size(10.dp), horizontalArrangement = Arrangement.SpaceBetween, verticalAlignment = Alignment.Bottom) {
        Box(modifier = mod.height(calcHeight(6, 4, fraction)))
        Box(modifier = mod.height(calcHeight(10, 4, fraction)))
        Box(modifier = mod.height(calcHeight(8, 8, fraction)))
    }
}

private fun calcHeight(start: Int, range: Int, fraction: Float): Dp {
    val t = start + range * fraction
    return if (t > 10) (10 - (t - 10)).dp else t.dp
}

@Composable
fun AudioChannelList(familyId: String, vm: VoiceChannelViewModel, voiceLiveHelper: VoiceLiveHelper) {
    LaunchedEffect(key1 = familyId) {
        vm.sendEvent(PageLoadEvent.Refresh)
    }
    val listState by vm.dataListFlow.collectAsStateWithLifecycle()
    val nav = LocalAppNavController.current
    CupidPullRefreshBox(isRefreshing = vm.isRefreshing, onRefresh = { vm.sendEvent(PageLoadEvent.Refresh) }) {
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp, 8.dp)
        ) {
            if (listState.isSuccess) {
                val list = listState.get()
                if (list.isEmpty()) {
                    item {
                        EmptyView(
                            modifier = Modifier
                                .fillMaxWidth()
                                .aspectRatio(0.75f),
                        )
                    }
                } else {
                    items(list) {
                        AudioChannelItem(
                            icon = it.owner.avatarUrl,
                            title = it.title,
                            avatars = it.sampleAudience.take(5).map { r -> r.avatarUrl },
                            peopleCount = it.audienceCnt,
                            modifier = Modifier
                                .clip(Shapes.corner12)
                                .click {
                                    voiceLiveHelper.joinVoiceLiveRoom(it.id, from = "jp_tribe_ar_list_lick")
                                },
                            hasRedPacket = it.hasRedPacket,
                            hasCoinWinnerGame = it.hasOngoingCoinPKGame
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                }
            }
            if (vm.allowLoad) {
                item {
                    LoadMoreView(onLoadMore = {
                        vm.sendEvent(PageLoadEvent.PageLoadMore)
                    })
                }
            }
        }
    }
}

@Preview
@Composable
private fun AudioChannelItemPreview() {
    CupidTheme {
        AudioChannelItem("", "Super Mario", listOf("", "", "", "", "", "", "", "", "", "", "", "", "", "", ""), 123, hasRedPacket = true, hasCoinWinnerGame = true)
    }
}

@Composable
fun IconButton(
    text: String,
    modifier: Modifier = Modifier,
    brush: Brush = Brush.horizontalGradient(listOf(Color(0xFFFFA3BC), Color(0xFFFF5E8B))),
    painter: Painter = painterResource(id = R.drawable.ic_my_room),
) {
    val density = LocalDensity.current
    Row(
        modifier = modifier
            .clip(RoundedCornerShape(50))
            .graphicsLayer {
                this.shadowElevation = with(density) { 2.dp.toPx() }
            }
            .background(brush, RoundedCornerShape(50))
            .padding(10.dp),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(painter = painter, contentDescription = "icon", modifier = Modifier.size(24.dp))
        Spacer(modifier = Modifier.width(4.dp))
        Text(text = text, fontSize = 16.sp, color = MaterialTheme.colorScheme.surface)
    }
}

@Preview
@Composable
private fun IconButtonPreview() {
    CupidTheme {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(4f),
            contentAlignment = Alignment.Center
        ) {
            IconButton("我的房间", modifier = Modifier.widthIn(min = 196.dp))
        }
    }
}

@Composable
fun CreateRoomContent(onCreateSuccess: EntityCallback<Room> = {}) {

    val roomRepo = remember {
        RoomRepository()
    }

    var tfv = rememberSaveable(saver = TextFieldValue.Saver) {
        TextFieldValue()
    }

    var textFieldValue by remember {
        mutableStateOf(tfv)
    }
    val scope = rememberCoroutineScope()
    var key by remember {
        mutableLongStateOf(System.currentTimeMillis())
    }
    LaunchedEffect(key1 = key) {
        launch {
            val name = roomRepo.getRandomRoomName()
            textFieldValue = TextFieldValue(name)
            tfv = textFieldValue
        }
    }
    val loadingState = LocalContentLoading.current
    val enable = loadingState.value.not() && textFieldValue.text.isNotEmpty()
    RoomNameEditor(textFieldValue = textFieldValue, onValueChange = {
        val newText = it.text
        val text = newText.replace("\n", "").take(10)
        textFieldValue = if (newText != text) TextFieldValue(text, selection = TextRange(text.length)) else it
        tfv = textFieldValue
    }, enable = enable, onRandomName = { key = System.currentTimeMillis() }, onClick = {
        scope.launch {
            loadingState.value = true
            roomRepo.createChatRoom(textFieldValue.text, false)
                .onSuccess {
                    onCreateSuccess(it)
                }.toastError()
            loadingState.value = false
        }
    })
}


@Composable
fun RoomNameEditor(
    textFieldValue: TextFieldValue,
    onValueChange: EntityCallback<TextFieldValue>,
    modifier: Modifier = Modifier,
    buttonText: String = stringResource(R.string.cpd_create_room),
    hint: String = stringResource(R.string.cpd_max_input_10),
    enable: Boolean = false,
    onRandomName: OnClick = {},
    onClick: OnClick = {},
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.surface, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .padding(16.dp, 20.dp)
            .imePadding()
            .navigationBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(text = stringResource(R.string.cpd_input_room_title), style = MaterialTheme.typography.headlineMedium)
        Spacer(modifier = Modifier.height(43.dp))
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color(0xFFF1F2F3), Shapes.small)
                .padding(16.dp, 14.dp)
        ) {
            BasicTextField(
                value = textFieldValue,
                onValueChange = onValueChange,
                modifier = Modifier
                    .padding(8.dp)
                    .fillMaxWidth(),
                textStyle = MaterialTheme.typography.bodyMedium.copy(lineHeight = 18.sp),
                decorationBox = { fn ->
                    Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
                        fn.invoke()
                        if (textFieldValue.text.isEmpty()) {
                            Text(text = hint, style = MaterialTheme.typography.labelMedium)
                        }
                    }
                }
            )
            Icon(
                painter = painterResource(id = R.drawable.ic_title_random),
                contentDescription = "",
                tint = Color(0xFFBBC1C7),
                modifier = Modifier
                    .size(20.dp)
                    .click(onClick = onRandomName)
                    .align(Alignment.CenterEnd)
            )
        }
        Spacer(modifier = Modifier.height(40.dp))
        Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
            CpdButton(
                text = buttonText,
                onClick = onClick,
                modifier = Modifier.widthIn(min = 220.dp),
                enabled = enable
            )
        }
        Spacer(modifier = Modifier.height(20.dp))
    }
}


@Preview
@Composable
private fun CreateRoomContentPreview() {
    CupidTheme {
        RoomNameEditor(textFieldValue = TextFieldValue(), onValueChange = {})
    }
}

@Composable
fun RoomNameEditor(
    textFieldValue: TextFieldValue,
    onValueChange: EntityCallback<TextFieldValue>,
    modifier: Modifier = Modifier,
    buttonText: String = stringResource(R.string.cpd_create_room),
    enable: Boolean = false,
    onRandomName: OnClick = {},
    onClick: OnClick = {},
) {
    val hint = stringResource(R.string.cpd_max_input_10)
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.surface, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .padding(16.dp, 20.dp)
            .imePadding()
            .navigationBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(text = stringResource(R.string.cpd_input_room_title), style = MaterialTheme.typography.headlineMedium)
        Spacer(modifier = Modifier.height(43.dp))
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color(0xFFF1F2F3), Shapes.small)
                .padding(16.dp, 14.dp)
        ) {
            BasicTextField(
                value = textFieldValue,
                onValueChange = onValueChange,
                modifier = Modifier
                    .padding(8.dp)
                    .fillMaxWidth(),
                textStyle = MaterialTheme.typography.bodyMedium.copy(lineHeight = 18.sp),
                decorationBox = { fn ->
                    Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
                        fn.invoke()
                        if (textFieldValue.text.isEmpty()) {
                            Text(text = hint, style = MaterialTheme.typography.labelMedium)
                        }
                    }
                }
            )
            Icon(
                painter = painterResource(id = R.drawable.ic_title_random),
                contentDescription = "",
                tint = Color(0xFFBBC1C7),
                modifier = Modifier
                    .size(20.dp)
                    .click(onClick = onRandomName)
                    .align(Alignment.CenterEnd)
            )
        }
        Spacer(modifier = Modifier.height(40.dp))
        Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
            CpdButton(
                text = buttonText,
                onClick = onClick,
                modifier = Modifier.widthIn(min = 220.dp),
                enabled = enable
            )
        }
        Spacer(modifier = Modifier.height(20.dp))
    }
}

@Composable
fun MyRoomButton(
    from: String,
    voiceLiveHelper: VoiceLiveHelper,
    modifier: Modifier = Modifier,
    brush: Brush = Brush.horizontalGradient(listOf(Color(0xFFFFA3BC), Color(0xFFFF5E8B))),
    clickCallback:OnClick = {},
    onRefresh: OnClick = {},
) {
    val myself by sUserFlow.collectAsStateWithLifecycle()
    var showCreateRoomDialog by rememberSaveable {
        mutableStateOf(false)
    }
    if (showCreateRoomDialog) {
        AnimatedDialog(onDismiss = { showCreateRoomDialog = false }) {
            CreateRoomContent(onCreateSuccess = {
                onRefresh.invoke()
                voiceLiveHelper.joinVoiceLiveRoom(it.roomId, from = from)
                showCreateRoomDialog = false
                // update self
                app.accountManager.refreshSelfUserByRemote()
            })
        }
    }
    val hasRoom = (myself as AppUser).room != null
    IconButton(if (!hasRoom) {
        stringResource(R.string.cpd_create_room)
    } else {
        stringResource(R.string.cpd_my_room)
    }, modifier = modifier
        .clip(RoundedCornerShape(50))
        .click {
            clickCallback.invoke()
            val myRoom = (myself as AppUser).room
            if (myRoom == null) {
                showCreateRoomDialog = true
            } else {
                voiceLiveHelper.joinVoiceLiveRoom(myRoom.roomId, from = from)
            }
        }
        .widthIn(min = 196.dp),
        brush = brush,
        painter = painterResource(id = if (!hasRoom) R.drawable.ic_create_room else R.drawable.ic_my_room)
    )
}