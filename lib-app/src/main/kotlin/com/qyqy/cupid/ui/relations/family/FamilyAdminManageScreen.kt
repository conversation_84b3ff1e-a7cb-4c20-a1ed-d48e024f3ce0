@file:OptIn(ExperimentalMaterial3Api::class)

package com.qyqy.cupid.ui.relations.family

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.cupid.model.FamilyJoinApplyViewModel
import com.qyqy.cupid.model.FamilyMemberListViewModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.dialog.DialogButton
import com.qyqy.cupid.ui.dialog.TitleAlertDialog
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.cupid.utils.CupidFamilyManager
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.state.CupidStateListView
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.ui.AgeGender
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.http.gson
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.tribe.bean.TribeInfo
import com.qyqy.ucoo.tribe.bean.TribeMemberItem
import kotlinx.coroutines.launch

/**
 *  @time 2024/6/25
 *  <AUTHOR>
 *  @package com.qyqy.ucoo.compose.presentation.chatgroup.page
 */

private sealed interface FamilyManagerAction {
    data class UpdateAdminRole(val member_id: Int, val setAdmin: Boolean) : FamilyManagerAction
    data object GoSetAdmin : FamilyManagerAction

    data class RemoveMember(val member_id: Int) : FamilyManagerAction

    data class AgreeJoin(val member_id: Int) : FamilyManagerAction
    data class DisagreeJoin(val member_id: Int) : FamilyManagerAction
    data object DisbandFamily : FamilyManagerAction
}

//region 家族管理员

/**
 * 家族管理员设置页
 * @param id 家族id
 *
 */
@Composable
fun FamilyAdminManageScreen(id: String) {
    val viewModel = viewModel(modelClass = FamilyMemberListViewModel::class.java, factory = viewModelFactory {
        initializer {
            FamilyMemberListViewModel(id, listOf(TribeMemberItem.ROLE_ADMIN))
        }
    })
    val controller = LocalAppNavController.current
    val loadingFlag = LocalContentLoading.current
    val scope = rememberCoroutineScope()

    val onAction: (FamilyManagerAction) -> Unit = { action: FamilyManagerAction ->
        when (action) {
            is FamilyManagerAction.UpdateAdminRole -> {
                scope.launch {
                    loadingFlag.value = true
                    viewModel.setAdminMember(action.member_id, action.setAdmin)
                    loadingFlag.value = false
                }
            }

            FamilyManagerAction.GoSetAdmin -> {
                controller.navigate(CupidRouters.FAMILY_ADMIN_MANAGE_ADD, mapOf("family_id" to id))
            }

            else -> {

            }
        }
    }


    LaunchedEffect(key1 = Unit) {
        viewModel.refresh()
    }
    Column() {
        CupidAppBar(title = stringResource(id = R.string.cpd_family_add_admin_title))
        Box {
            CupidStateListView(viewModel = viewModel,
                keyProvider = { _, member -> member.member_id.toString() }) { member, _, _, _ ->
                FamilyMemberItemWidget(member = member) {
                    if (member.isAdmin) {
                        AppButton(
                            text = stringResource(id = R.string.cpd_family_remove_admin),
                            onClick = { onAction(FamilyManagerAction.UpdateAdminRole(member.member_id, false)) },
                            border = BorderStroke(1.dp, color = Color(0xFFE5E6EB)),
                            background = Color.White,
                            textStyle = TextStyle(color = CpdColors.FF86909C, fontSize = 14.sp),
                            contentPadding = PaddingValues(horizontal = 12.dp),
                            modifier = Modifier.height(32.dp)
                        )
                    } else {
                        AppButton(
                            text = stringResource(id = R.string.cpd_family_add_admin),
                            onClick = { onAction(FamilyManagerAction.UpdateAdminRole(member.member_id, true)) },
                            contentPadding = PaddingValues(horizontal = 12.dp),
                            modifier = Modifier.height(32.dp)
                        )
                    }
                }
            }

            Column(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 35.dp, start = 20.dp, end = 20.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {

                AppButton(text = stringResource(id = R.string.cpd_family_add_admin),
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(44.dp),
                    background = CpdColors.FFFF5E8B,
                    onClick = {
                        if (viewModel.listFlow.value.size > 3) {
                            toastRes(R.string.cpd_family_add_admin_tips)
                        } else {
                            onAction(FamilyManagerAction.GoSetAdmin)
                        }
                    })
                Spacer(modifier = Modifier.height(8.dp))
                Text(stringResource(id = R.string.cpd_family_add_admin_tips), color = CpdColors.FF86909C, fontSize = 12.sp)
            }
        }
    }
}

/**
 * 家族管理员设置页
 * @param id 家族id
 */
@Composable
fun FamilyAdminSetScreen(
    id: String,
) {
    val viewModel = viewModel(modelClass = FamilyMemberListViewModel::class.java, factory = viewModelFactory {
        initializer {
            FamilyMemberListViewModel(id, listOf(TribeMemberItem.ROLE_COMMON))
        }
    })
    val loadingFlag = LocalContentLoading.current
    val scope = rememberCoroutineScope()

    val onAction: (FamilyManagerAction) -> Unit = { action: FamilyManagerAction ->
        when (action) {
            is FamilyManagerAction.UpdateAdminRole -> {
                scope.launch {
                    loadingFlag.value = true
                    viewModel.setAdminMember(action.member_id, action.setAdmin)
                    loadingFlag.value = false
                }
            }

            else -> {

            }
        }
    }

    LaunchedEffect(key1 = Unit) {
        viewModel.refresh()
    }
    Column() {
        CupidAppBar(title = stringResource(id = R.string.cpd_family_add_admin))
        Box {
            CupidStateListView(viewModel = viewModel,
                keyProvider = { _, member -> member.member_id.toString() }) { member, _, _, _ ->
                FamilyMemberItemWidget(member = member) {
                    if (member.isAdmin) {
                        AppButton(
                            text = stringResource(id = R.string.cpd_family_remove_admin),
                            onClick = { onAction(FamilyManagerAction.UpdateAdminRole(member.member_id, false)) },
                            border = BorderStroke(1.dp, color = Color(0xFFE5E6EB)),
                            background = Color.White,
                            textStyle = TextStyle(color = CpdColors.FF86909C, fontSize = 14.sp),
                            contentPadding = PaddingValues(horizontal = 12.dp),
                            modifier = Modifier.height(32.dp)
                        )
                    } else {
                        AppButton(
                            text = stringResource(id = R.string.cpd_family_add_admin),
                            onClick = { onAction(FamilyManagerAction.UpdateAdminRole(member.member_id, true)) },
                            contentPadding = PaddingValues(horizontal = 12.dp),
                            modifier = Modifier.height(32.dp)
                        )
                    }
                }
            }
        }
    }
}

//endregion

//region 家族成员


@Composable
fun FamilyRemoveMemberScreen(
    family: TribeInfo,
) {
    val viewModel = viewModel(modelClass = FamilyMemberListViewModel::class.java, factory = viewModelFactory {
        initializer {
            FamilyMemberListViewModel(family.id.toString(), buildList {
                if (family.iAmOwner) {
                    add(TribeMemberItem.ROLE_ADMIN)
                }
                add(TribeMemberItem.ROLE_COMMON)
            })
        }
    })
    val loadingFlag = LocalContentLoading.current
    val scope = rememberCoroutineScope()

    val onAction: (FamilyManagerAction) -> Unit = { action ->
        when (action) {
            is FamilyManagerAction.RemoveMember -> {
                scope.launch {
                    loadingFlag.value = true
                    viewModel.kickMember(action.member_id)
                    loadingFlag.value = false
                }
            }

            else -> {

            }
        }
    }

    LaunchedEffect(key1 = Unit) {
        viewModel.refresh()
    }
    Column() {
        CupidAppBar(title = stringResource(id = R.string.cpd_family_member_manage))
        CupidStateListView(viewModel = viewModel,
            keyProvider = { _, member -> member.member_id.toString() }) { member, _, _, _ ->
            FamilyRemoveListItem(member = member, onAction)
        }
    }
}

@Composable
private fun FamilyRemoveListItem(member: TribeMemberItem, onAction: (FamilyManagerAction) -> Unit = {}) {
    FamilyMemberItemWidget(member = member) {
        AppButton(
            text = stringResource(id = R.string.cpd_family_kickout),
            onClick = { onAction(FamilyManagerAction.RemoveMember(member.member_id)) },
            border = BorderStroke(1.dp, color = Color(0xFFE5E6EB)),
            background = Color.White,
            textStyle = TextStyle(color = CpdColors.FF86909C, fontSize = 14.sp),
            contentPadding = PaddingValues(horizontal = 12.dp),
            modifier = Modifier.height(32.dp)
        )
    }
}


//endregion

//region 家族申请人员列表

@Composable
fun FamilyApplyScreen() {
    val family = CupidFamilyManager.myTribe ?: return
    val viewModel = viewModel(modelClass = FamilyJoinApplyViewModel::class.java, factory = viewModelFactory {
        initializer {
            FamilyJoinApplyViewModel(family.id.toString())
        }
    })
    val scope = rememberCoroutineScope()
    val loadingFlag = LocalContentLoading.current
    val controller = LocalAppNavController.current

    val onAction: (FamilyManagerAction) -> Unit = { action ->
        when (action) {
            is FamilyManagerAction.AgreeJoin -> {
                viewModel.applyJoin(action.member_id, true)
            }

            is FamilyManagerAction.DisagreeJoin -> {
                viewModel.applyJoin(action.member_id, false)
            }

            FamilyManagerAction.DisbandFamily -> {
                scope.launch {
                    loadingFlag.value = true
                    val disbandFamily = viewModel.disbandFamily()
                    if (disbandFamily != null) {
                        disbandFamily.onSuccess {
                            loadingFlag.value = false
                        }.onFailure {
                            loadingFlag.value = false
                        }.toastError()
                    } else {
                        loadingFlag.value = false
                    }
                }
            }

            else -> {

            }
        }
    }
    var showDialogType by remember {
        mutableStateOf(0)
        //0 不显示 1 名称修改 2简介修改 3 退出家族 4 解散家族
    }
    LaunchedEffect(key1 = Unit) {
        viewModel.refresh()
    }
    Box {
        Column() {
            CupidAppBar(title = stringResource(id = R.string.cpd_family_requests))
            CupidStateListView(viewModel = viewModel,
                keyProvider = { _, member -> member.apply_id.toString() }) { member, _, _, _ ->
                FamilyApplyMemberItem(member = member, onAction)
            }
        }
        if (family.isOwner) {
            AppButton(
                text = stringResource(id = R.string.cpd_family_disband),
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(horizontal = 32.dp, vertical = 16.dp)
                    .fillMaxWidth(),
                background = Color(0xFFFF5E8B),
                color = Color(0xFFFFFFFF),
            ) {
                showDialogType = 4
            }

            if (showDialogType != 0) {
                val onClose = { showDialogType = 0 }
                when (showDialogType) {
                    4 -> {
                        Dialog(onDismissRequest = { showDialogType = 0 }) {
                            TitleAlertDialog(
                                title = stringResource(id = R.string.cpd_family_disband_tips),
                                startButton = DialogButton(stringResource(id = R.string.cpd_cancel)) {
                                    showDialogType = 0
                                },
                                endButton = DialogButton(stringResource(id = R.string.cpd_confirm)) {
                                    //退出家族
                                    onAction(FamilyManagerAction.DisbandFamily)
                                    showDialogType = 0
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun FamilyApplyMemberItem(member: TribeMemberItem, onAction: (FamilyManagerAction) -> Unit) {
    FamilyMemberItemWidget(member = member) {
        Column {
            AppButton(
                text = if (member.apply_status == 1) stringResource(R.string.cpd_request_accept_done) else stringResource(R.string.cpd_request_accept),
                onClick = {
                    if (member.apply_status == 0) {
                        onAction(FamilyManagerAction.AgreeJoin(member.user.userid))
                    }
                },
                enabled = member.apply_status == 0,
                contentPadding = PaddingValues(horizontal = 12.dp),
                modifier = Modifier
                    .height(32.dp)
                    .widthIn(88.dp)
            )
            Spacer(modifier = Modifier.height(12.dp))
            AppButton(
                text = if (member.apply_status == 2) stringResource(R.string.cpd_request_decline_done) else stringResource(R.string.cpd_request_decline),
                onClick = {
                    if (member.apply_status == 0) {
                        onAction(FamilyManagerAction.DisagreeJoin(member.user.userid))
                    }
                },
                border = BorderStroke(1.dp, color = CpdColors.FFFF5E8B),
                background = Color.White,
                textStyle = TextStyle(color = CpdColors.FFFF5E8B, fontSize = 14.sp),
                enabled = member.apply_status == 0,
                contentPadding = PaddingValues(horizontal = 12.dp),
                modifier = Modifier
                    .height(32.dp)
                    .widthIn(88.dp)
            )
        }
    }
}

//endregion

@Composable
private fun FamilyMemberItemWidget(member: TribeMemberItem, actions: @Composable () -> Unit) {
    Column {
        Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.padding(vertical = 10.dp)) {
            Spacer(modifier = Modifier.width(16.dp))
            CircleComposeImage(
                model = member.user.avatarUrl,
                modifier = Modifier
                    .size(48.dp)
                    .noEffectClickable {
                        AppLinkManager.controller?.navigateToProfile(member.user.id)
                    }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Column(
                modifier = Modifier.weight(1f),
            ) {
                Text(
                    member.user.nickname,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    fontSize = 16.sp, color = CpdColors.FF1D2129
                )
                Spacer(modifier = Modifier.height(10.dp))
                AgeGender(age = member.user.age, isBoy = member.user.isBoy)
            }
            actions()
            Spacer(modifier = Modifier.width(16.dp))
        }
        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .height(0.5.dp)
                .padding(horizontal = 16.dp)
                .background(CpdColors.FFF1F2F3)
        )
    }
}

@Composable
@Preview(showBackground = true)
private fun ApplyManagerScreenPreview() {
    val sampleJson =
        "{\"member_id\":43,\"role\":10,\"is_online\":true,\"user\":{\"userid\":1430,\"public_id\":\"101882\",\"nickname\":\"兔兔子\",\"avatar_url\":\"https://s.test.ucoofun.com/aacd0W?x-oss-process=image/format,webp\",\"gender\":2,\"age\":24,\"height\":170,\"avatar_frame\":\"https://s.ucoofun.com/aabLdz\",\"medal\":{\"icon\":\"https://s.ucoofun.com/aabOPn\",\"width\":72,\"height\":24},\"medal_list\":[{\"icon\":\"https://s.ucoofun.com/aabOPn\",\"width\":72,\"height\":24}],\"level\":44,\"country_flag\":\"https://media.ucoofun.com/opsite%2Fcountryflag%2FL_slices%2FCN.png\"}}"
    val memberList = mutableListOf<TribeMemberItem>()
    for (i in 0..20) {
        memberList.add(gson.fromJson(sampleJson, TribeMemberItem::class.java))
    }

    Column {
        AppTitleBar(title = stringResource(id = R.string.cpd_family_add_admin_title))
        Box {
            LazyColumn {
                items(memberList) {
                    FamilyApplyMemberItem(member = it) {

                    }
                }
            }
        }
    }
}