@file:OptIn(ExperimentalMaterial3Api::class)

package com.qyqy.cupid.ui.relations.family

import android.annotation.SuppressLint
import android.net.Uri
import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.event.AppLinkEvent
import com.qyqy.cupid.im.panel.gift.GiftViewModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.theme.CupidTheme
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.CupidViewModel
import com.qyqy.cupid.ui.DestinationRoute
import com.qyqy.cupid.ui.IIMAction
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.dialog.FamilyBanContent
import com.qyqy.cupid.ui.dialog.FamilyBuiltDialog
import com.qyqy.cupid.ui.dialog.FirstRechargeRewardFloat
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.home.message.C2CBottomPanel
import com.qyqy.cupid.ui.home.message.ChatBottomBar
import com.qyqy.cupid.ui.home.message.MenuItemButton
import com.qyqy.cupid.ui.home.message.icons.ActionIcons
import com.qyqy.cupid.ui.home.message.icons.Audio
import com.qyqy.cupid.ui.home.message.icons.CupidIconHome
import com.qyqy.cupid.ui.home.message.icons.Emoji
import com.qyqy.cupid.ui.home.message.icons.Gift
import com.qyqy.cupid.ui.home.message.icons.IconGroupPeople
import com.qyqy.cupid.ui.home.message.icons.Keyboard
import com.qyqy.cupid.ui.home.message.icons.Photo
import com.qyqy.cupid.ui.profile.RechargeDialog
import com.qyqy.cupid.ui.relations.TabLayout
import com.qyqy.cupid.ui.relations.family.icons.TaskFill
import com.qyqy.cupid.ui.relations.family.panels.SendGiftPanelState
import com.qyqy.cupid.ui.relations.family.panels.rememberSendGiftPanelState
import com.qyqy.cupid.ui.relations.family.panels.rememberUserPanelState
import com.qyqy.cupid.utils.CupidFamilyManager
import com.qyqy.cupid.utils.observeChannel
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.asComponentActivity
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.chatgroup.page.ChatGroupViewModel
import com.qyqy.ucoo.compose.presentation.chatgroup.page.LocalMsgEventHandler
import com.qyqy.ucoo.compose.presentation.chatgroup.page.MsgEventHandler
import com.qyqy.ucoo.compose.presentation.chatgroup.page.MsgEvents
import com.qyqy.ucoo.compose.presentation.config.LocalChatUIProvider
import com.qyqy.ucoo.compose.presentation.room.AudioPanel
import com.qyqy.ucoo.compose.presentation.room.CGUserImageContent
import com.qyqy.ucoo.compose.presentation.room.EmojiPanel
import com.qyqy.ucoo.compose.presentation.room.IMPanel
import com.qyqy.ucoo.compose.presentation.room.KeyboardLayout
import com.qyqy.ucoo.compose.presentation.room.KeyboardPanelState
import com.qyqy.ucoo.compose.presentation.room.LocalBubbleTheme
import com.qyqy.ucoo.compose.presentation.room.LocalLinkHandler
import com.qyqy.ucoo.compose.presentation.room.LocalMsgAudioPlayer
import com.qyqy.ucoo.compose.presentation.room.keyboardSystemBarsPadding
import com.qyqy.ucoo.compose.presentation.room.rememberMsgAudioPlayer
import com.qyqy.ucoo.compose.presentation.room.rememberPanelState
import com.qyqy.ucoo.compose.presentation.room.withAutoHidePanel
import com.qyqy.ucoo.compose.presentation.room.withInputField
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppPhotoPreviewer
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.fixCurrentPage
import com.qyqy.ucoo.compose.ui.overScrollVertical
import com.qyqy.ucoo.compose.ui.photo.IPhotoPreviewModel
import com.qyqy.ucoo.compose.ui.photo.LocalPhotoPreviewState
import com.qyqy.ucoo.compose.ui.photo.rememberPhotoGestureState
import com.qyqy.ucoo.compose.ui.photo.rememberPhotoPreviewState
import com.qyqy.ucoo.compose.ui.rememberOverscrollFlingBehavior
import com.qyqy.ucoo.compose.vm.room.UIAction
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.core.page.PageLoadEvent
import com.qyqy.ucoo.glide.ninepatch.BubbleTheme
import com.qyqy.ucoo.im.compat.ConversationType
import com.qyqy.ucoo.im.compat.MessageBundle
import com.qyqy.ucoo.im.compat.UCInstanceMessage
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.tribe.bean.TribeInfo
import com.qyqy.ucoo.tribe.main.voice_channel.VoiceChannelViewModel
import com.qyqy.ucoo.utils.ComposeState
import com.qyqy.ucoo.utils.EntityCallback
import com.qyqy.ucoo.utils.OnClick
import com.qyqy.ucoo.utils.rememberRequestAlbumPermissionHelper
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlin.math.abs

@Composable
fun FamilyHomeScreen(hasAudioChannel: Boolean = true) {
    val familyInfo = CupidFamilyManager.tribeInfoFlow.collectAsStateWithLifecycle()
    val tribe by familyInfo

    LaunchedEffect(key1 = Unit) {
        CupidFamilyManager.refreshFamilyInfo()
    }
    if (tribe != null) {
        val familyId = tribe?.tribeId.orEmpty()
        val rcGroupId = tribe?.conversationId.orEmpty()
        val scope = rememberCoroutineScope()
        val pagerState = rememberPagerState {
            2
        }
        val viewModel = viewModel<ChatGroupViewModel>(initializer = {
            ChatGroupViewModel(rcGroupId)
        })
        val gestureState = with(LocalDensity.current) {
            rememberPhotoGestureState(
                extraHorizontalOffset = 80.dp.toPx(),
                extraVerticalOffset = 100.dp.toPx(),
            )
        }
        var previewList by rememberSaveable {
            mutableStateOf(emptyList<IPhotoPreviewModel>())
        }
        val previewState = rememberPhotoPreviewState(
            gestureState = gestureState,
            onGestureClose = { _, offsetY, velocityY ->
                abs(velocityY) > 1000 || abs(offsetY) > 60.dp.toPx()
            }
        ) {
            previewList
        }
        val chatCount by remember {
            derivedStateOf { tribe?.membersRoomInfo?.size ?: 0 }
        }
        var showChatTip by rememberSaveable {
            mutableStateOf(true)
        }
        if (showChatTip && pagerState.fixCurrentPage == 1) {
            showChatTip = false
        }

        val giftViewModel = viewModel<GiftViewModel>(initializer = {
            GiftViewModel(familyId, ConversationType.GROUP, rcGroupId)
        })

        val dialogQueue = LocalDialogQueue.current
        val giftPanelState = rememberSendGiftPanelState(familyId = familyId, giftViewModel = giftViewModel) {
            dialogQueue.push(RechargeDialog, true)
        }
        observeChannel<AppLinkEvent>() { event ->
            if (event.route == CupidRouters.FAMILY_HOME) {
                when (event.getAction()) {
                    "goAudioChannel" -> {
                        scope.launch {
                            pagerState.scrollToPage(1)
                        }
                    }

                    "ShowGiftPanel" -> {
                        scope.launch {
                            giftPanelState.showPanel(initialGiftId = event.params.get("giftId")?.toInt() ?: -1)
                        }
                    }

                    else -> {}
                }
            }
        }

        Box(modifier = Modifier.fillMaxSize()) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .background(MaterialTheme.colorScheme.background)
            ) {
                TitleBar(tribe, pagerState = pagerState, hasAudioChannel)
                if (hasAudioChannel) {

                    HorizontalPager(
                        modifier = Modifier
                            .fillMaxSize(),
                        state = pagerState
                    ) { page ->
                        when (page) {
                            0 -> CompositionLocalProvider(
                                LocalFamilyPageInfo provides familyInfo,
                                LocalPhotoPreviewState provides previewState
                            ) {
                                FamilyChatScreen(giftPanelState, giftViewModel, viewModel, onPreview = {
                                    val (index, list) = viewModel.getPreviewImageList(true, it.message)
                                    previewList = list
                                    previewState.startPreview(index)
                                })
                            }

                            else -> Box(modifier = Modifier.fillMaxSize()) {
                                val vm: VoiceChannelViewModel = viewModel {
                                    VoiceChannelViewModel(familyId)
                                }

                                val activity = LocalContext.current.asComponentActivity!!
                                val mainViewModel = viewModel<CupidViewModel>(viewModelStoreOwner = activity)
                                val voiceLiveHelper = mainViewModel.voiceLiveHelper

                                AudioChannelList(familyId, vm, voiceLiveHelper)
                                MyRoomButton(
                                    from = "jp_tribe_ar_list_my_room_click",
                                    voiceLiveHelper = voiceLiveHelper,
                                    modifier = Modifier
                                        .padding(bottom = 50.dp)
                                        .navigationBarsPadding()
                                        .align(Alignment.BottomCenter),
                                    onRefresh = {
                                        vm.sendEvent(PageLoadEvent.Refresh)
                                    }
                                )
                            }
                        }
                    }
                } else {
                    CompositionLocalProvider(
                        LocalFamilyPageInfo provides familyInfo,
                        LocalPhotoPreviewState provides previewState
                    ) {
                        FamilyChatScreen(giftPanelState, giftViewModel, viewModel, onPreview = {
                            val (index, list) = viewModel.getPreviewImageList(true, it.message)
                            previewList = list
                            previewState.startPreview(index)
                        })
                    }
                }
            }
            if (chatCount > 0 && showChatTip) {
                ChatPeopleTip(
                    count = chatCount,
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(end = 22.dp, top = 60.dp)
                        .statusBarsPadding()
                )
            }

            FirstRechargeRewardFloat(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(bottom = 160.dp)
            )

            AppPhotoPreviewer(previewState = previewState)
        }
    }

    val familyState = CupidFamilyManager.familyState.collectAsStateWithLifecycle()

    if (familyState.value != 0) {
        Dialog(onDismissRequest = { }) {
            FamilyBanContent(familyState.value) {

            }
        }
    }
}

@Composable
fun ChatPeopleTip(count: Int, modifier: Modifier = Modifier) {
    Text(
        text = stringResource(R.string.cpd_chat_people_count, count), color = Color.White, fontSize = 11.sp,
        modifier = modifier
            .background(
                MaterialTheme.colorScheme.primary,
                RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp, bottomEnd = 8.dp)
            )
            .padding(horizontal = 7.dp), lineHeight = 22.sp
    )
}

@Composable
fun FamilyChatScreen(
    giftPanelState: SendGiftPanelState,
    giftViewModel: GiftViewModel,
    viewModel: ChatGroupViewModel,
    onPreview: EntityCallback<CGUserImageContent> = {},
) {
    val uiProvider = remember {
        FamilyChatUiProvider()
    }

    val paginateState = viewModel.paginateState

    val isLoading by remember {
        derivedStateOf {
            paginateState.nextLoadState.isLoading
        }
    }
    val dialogQueue = LocalDialogQueue.current

    val textFieldValue = rememberSaveable(stateSaver = TextFieldValue.Saver) {
        mutableStateOf(TextFieldValue())
    }

    val listState = rememberLazyListState()


    //用户资料面板
    val userPanelState = rememberUserPanelState(onAt = {
        if (viewModel.addAtUser(it)) {
            val tfv = textFieldValue.value
            val newText = "${tfv.text}@${it.nickname} "
            textFieldValue.value = tfv.copy(text = newText, selection = TextRange(newText.length))
        }
    }, onSendGift = {
        giftPanelState.showPanel(listOf(it))
    })
    val controller = LocalAppNavController.current
    val linkHandler: EntityCallback<String> = remember {
        {
            try {
                val uri = Uri.parse(it)
                if (uri.authority == "action") {
                    if (uri.path == "/scene_chat_group/press_at_user") {
                        val uid = uri.getQueryParameter("uid")
                        if (uid.isNullOrEmpty().not()) {
                            userPanelState.value = uid
                        }
                    }
                } else {
                    controller.navigateByLink(it)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    val msgEventHandler: MsgEventHandler = remember {
        object : MsgEventHandler {
            override fun handle(event: MsgEvents) {
                when (event) {
                    is MsgEvents.SeeUser -> {
                        val user = event.user
                        userPanelState.value = user.id
                    }

                    MsgEvents.EditFamilyBuilt -> {
                        dialogQueue.push(FamilyBuiltDialog)
                    }

                    is MsgEvents.OnPreview -> {
                        onPreview(event.image)
                    }

                    else -> {}
                }
            }
        }
    }

    LaunchedEffect(key1 = viewModel) {
        viewModel.effect.onEach {
            when (it) {
                is UIAction.OnClickImage -> onPreview(it.item)

                else -> Unit
            }
        }.launchIn(this)
    }

    val panels = remember {
        arrayOf(
            IMPanel(EmojiPanel), // 表情面板
            IMPanel(AudioPanel, autoHideEnable = false) // 录音面板
        )
    }
    val panelState = rememberPanelState(listState, panels)

    CompositionLocalProvider(
        LocalBubbleTheme provides BubbleTheme(
            color1 = Color(0x0DFFFFFF),
            color2 = Color(0x0DFFFFFF),
            translateColor = Color(0x0DFFFFFF),
            paddingValues = PaddingValues(12.dp)
        ),
        LocalMsgAudioPlayer provides rememberMsgAudioPlayer(),
        LocalMsgEventHandler provides msgEventHandler,
        LocalChatUIProvider provides uiProvider,
        LocalLinkHandler provides linkHandler
    ) {
        Box(modifier = Modifier.fillMaxSize()) {
            FamilyChatContent(
                viewModel,
                listState = listState,
                isLoading = isLoading,
                panelState = panelState,
                textFieldValue = textFieldValue,
                onShowGiftPanel = {
                    giftPanelState.showPanel()
                },
                onAction = object : IIMAction {

                    override fun onSendMessage(message: MessageBundle) {
                        viewModel.sendMessage(message)
                    }

                    override fun onSendMultipleMessage(messages: List<MessageBundle>) {
                        if (messages.isNotEmpty()) {
                            viewModel.sendMessages(messages)
                        }
                    }

                    override fun onResendMessage(message: UCInstanceMessage) {
                        viewModel.sendMessage(message.base)
                    }

                    override fun onNavigateTo(destination: DestinationRoute) = Unit

                },
                overlayContent = {

                }
            )
            //礼物特效
            giftViewModel.GiftEffectView()

            dialogQueue.DialogContent()
        }
    }

}

@Composable
fun FamilyHomeScaffold(
    panelState: KeyboardPanelState,
    modifier: Modifier = Modifier,
    topBar: @Composable ColumnScope.() -> Unit = {},
    bottomBar: @Composable ColumnScope.() -> Unit = {},
    panelContent: @Composable BoxScope.() -> Unit = {},
    overlayContent: @Composable BoxScope.() -> Unit = {},
    messageContent: @Composable BoxScope.() -> Unit = {},
) {
    Box(modifier = modifier) {
        KeyboardLayout(panelState = panelState, modifier = Modifier.fillMaxSize()) {
            Column(
                modifier = Modifier
                    .fillMaxSize()

            ) {
                topBar()
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                ) {
                    messageContent()
                }
                bottomBar()
            }
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.TopCenter
            ) {
                panelContent()
            }
        }

        overlayContent()
    }
}

@Composable
fun FamilyChatContent(
    vm: ChatGroupViewModel,
    listState: LazyListState,
    isLoading: Boolean,
    panelState: KeyboardPanelState,
    textFieldValue: MutableState<TextFieldValue>,
    onShowGiftPanel: OnClick,
    onAction: IIMAction,
    overlayContent: @Composable BoxScope.() -> Unit,
) {
    val audioPanel = panelState[AudioPanel]
    val emojiPanel = panelState[EmojiPanel]
    val showSendBtn by remember {
        derivedStateOf {
            textFieldValue.value.text.isNotEmpty() && !panelState.isShowing(audioPanel) && !panelState.isShowing(emojiPanel)
        }
    }
    val tribeInfo by CupidFamilyManager.tribeInfoFlow.collectAsStateWithLifecycle()
    val controller = LocalAppNavController.current
    FamilyHomeScaffold(
        panelState = panelState,
        modifier = Modifier
            .fillMaxSize(),
        topBar = {
            val applyCount = tribeInfo?.let {
                if (it.iAmOwner || it.iAmAdmin) it.member_apply_wait_cnt else 0
            } ?: 0
            TopBar(applyCount) {
                tribeInfo?.let {
                    controller.navigate(CupidRouters.FAMILY_ADMIN_APPLY, mapOf("family" to it.toTribe()))
                }
            }
        },
        bottomBar = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(MaterialTheme.colorScheme.surface)
                    .keyboardSystemBarsPadding(panelState)
            ) {
                BottomBar(
                    textFieldValue = textFieldValue,
                    showSendBtn = showSendBtn,
                    audioPanelVisible = panelState.isShowing(audioPanel),
                    showTribeBoxEntry = tribeInfo?.showTribeBoxEntry ?: false,
                    switchToAudioPanel = {
                        panelState.switchPanel(audioPanel)
                    },
                    emojiPanelVisible = panelState.isShowing(emojiPanel),
                    switchEmojiPanel = { panelState.switchPanel(emojiPanel) },
                    onShowGiftPanel = onShowGiftPanel,
                    onAction = onAction,
                    textFieldModifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 10.dp)
                        .withInputField(panelState)
                )
            }
        },
        panelContent = {
            C2CBottomPanel(panelState, textFieldValue, onAction)
        },
        overlayContent = overlayContent,
    ) {
        GroupMessageList(
            vm,
            listState = listState,
            isLoading = isLoading,
            modifier = Modifier
                .withAutoHidePanel(panelState)
                .fillMaxSize()
                .noEffectClickable {
                    panelState.hideAll(false)
                }
                .background(Color(0xFFF5F7F9))
                .overScrollVertical()
        )
    }
}

@Composable
fun GroupMessageList(viewModel: ChatGroupViewModel, listState: LazyListState, isLoading: Boolean, modifier: Modifier = Modifier) {

    val context = LocalContext.current
    val density = LocalDensity.current

    viewModel.bindListState(listState)
    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        LazyColumn(
            modifier = modifier,
            state = listState,
            contentPadding = PaddingValues(vertical = 16.dp),
            reverseLayout = true,
            verticalArrangement = Arrangement.spacedBy(16.dp),
            flingBehavior = rememberOverscrollFlingBehavior { listState }
        ) {
            items(items = viewModel.messageList, key = {
                "familychat-${it.key}"
            }) {
                val item = remember(context, density, it) {
                    ChatGroupViewModel.mapToMessageItem(context, density, it, viewModel._effect)
                }
                item.apply {
                    it.refresh
                    Content()
                }
            }

            if (isLoading) {
                item(key = "isLoading", contentType = "isLoading") {
                    Box(
                        modifier = Modifier
                            .padding(top = 10.dp)
                            .fillMaxWidth(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            color = Color(0xFFFF5E8B),
                            strokeWidth = 1.5.dp
                        )
                    }
                }
            }
        }

        val unreadMsgInfo = viewModel.unreadMsgInfo
        val unreadAtInfo = viewModel.unreadAtInfo

        if (unreadMsgInfo != null || unreadAtInfo != null) {
            Column(modifier = Modifier.align(Alignment.BottomEnd), horizontalAlignment = Alignment.End) {
                val mod =
                    Modifier
                        .background(Color(0xFFF6EBF0), RoundedCornerShape(topStartPercent = 50, bottomStartPercent = 50))
                        .padding(8.dp)
                val color = MaterialTheme.colorScheme.primary
                if (unreadMsgInfo != null) {
                    val unreadMessageCount = unreadMsgInfo.unreadCount
                    val text = if (unreadMessageCount > 99) stringResource(id = R.string.cpd_99_not_read)
                    else stringResource(id = R.string.cpd_format_not_read, unreadMessageCount)
                    AppText(
                        text = text,
                        modifier = mod.clickable(onClick = { viewModel.scrollToUnreadMessage() }),
                        color = color,
                        fontSize = 12.sp
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }

                if (unreadAtInfo != null) {
                    AppText(
                        text = stringResource(id = R.string.cpd_at_you),
                        modifier = mod.clickable(onClick = {
                            viewModel.scrollToAtMessage()
                        }),
                        color = color,
                        fontSize = 12.sp
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }
                Spacer(modifier = Modifier.height(20.dp))
            }
        }
    }
}

@Composable
private fun BottomBar(
    textFieldValue: MutableState<TextFieldValue>,
    showSendBtn: Boolean,
    audioPanelVisible: Boolean,
    showTribeBoxEntry: Boolean,
    switchToAudioPanel: OnClick,
    emojiPanelVisible: Boolean,
    switchEmojiPanel: OnClick,
    onShowGiftPanel: OnClick,
    onAction: IIMAction,
    @SuppressLint("ModifierParameter")
    textFieldModifier: Modifier = Modifier,
) {
    val scope = rememberCoroutineScope()
    val uploadImageHelper =
        rememberRequestAlbumPermissionHelper(context = LocalContext.current, needCrop = false) { selectMedia ->
            scope.launch {
                val messages = selectMedia.list.map { media ->
                    MessageBundle.Image.create(
                        media.uri,
                        media.path,
                        media.width,
                        media.height,
                        selectMedia.originalFlag
                    )
                }

                if (messages.isNotEmpty()) {
                    onAction.onSendMultipleMessage(messages)
                } else {
                    toastRes(R.string.消息发送失败)
                }
            }

        }
    val nav = LocalAppNavController.current
    ChatBottomBar(
        textFieldValue = textFieldValue,
        sendButtonVisible = showSendBtn,
        emojiMenuItem = {
            MenuItemButton(
                imageVector = if (emojiPanelVisible) ActionIcons.Keyboard else ActionIcons.Emoji,
                onClick = switchEmojiPanel
            )
        },
        menuItems = {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceEvenly,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 6.dp)
            ) {
                if (showTribeBoxEntry) {
                    MenuItemButton(
                        imageVector = TaskFill,
                        tint = Color(0xFFFF6720),
                        onClick = {
                            nav.navigateWeb("${Const.Url.baseURL}/h5/group_task")
                        }
                    )
                }
                MenuItemButton(
                    imageVector = if (audioPanelVisible) ActionIcons.Keyboard else ActionIcons.Audio,
                    onClick = switchToAudioPanel
                )
                MenuItemButton(imageVector = ActionIcons.Photo, onClick = {
                    uploadImageHelper.start()
                })
                MenuItemButton(
                    imageVector = ActionIcons.Gift,
                    tint = MaterialTheme.colorScheme.primary,
                    onClick = onShowGiftPanel
                )
            }
        },
        onAction = onAction,
        textFieldModifier = textFieldModifier
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TitleBar(tribeInfo: TribeInfo?, pagerState: PagerState, hasAudioChannel: Boolean) {
    val backPressedDispatcher = LocalOnBackPressedDispatcherOwner.current
    val controller = LocalAppNavController.current

    Column(modifier = Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
        Box(modifier = Modifier.fillMaxWidth()) {
            CenterAlignedTopAppBar(
                colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.White),
                title = {
                    tribeInfo?.let { info ->
                        Row {
                            Text(
                                info.name ?: "", modifier = Modifier.weight(1f, false),
                                fontSize = 14.sp,
                                color = CpdColors.FF1D2129,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                "(${info.memberCnt}人)",
                                fontSize = 14.sp,
                                color = CpdColors.FF1D2129,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                },
                navigationIcon = {
                    IconButton(onClick = {
                        backPressedDispatcher?.onBackPressedDispatcher?.onBackPressed()
                    }) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_cpd_navigation_back),
                            contentDescription = "back",
                        )
                    }
                },
                actions = {
                    val nav = LocalAppNavController.current
                    IconButton(
                        modifier = Modifier.size(24.dp), onClick = {
                            nav.navigate(CupidRouters.FAMILY_RANK)
                        }) {
                        Icon(
                            painter = painterResource(id = R.drawable.chart_bar_line),
                            contentDescription = "back",
                            tint = Color.Black
                        )
                    }
                    Spacer(modifier = Modifier.width(12.dp))
                    IconButton(
                        modifier = Modifier.size(24.dp),
                        onClick = {
                            tribeInfo?.let {
                                controller.navigate(CupidRouters.FAMILY_DETAIL, mapOf("family_id" to it.id.toString()))
                            }
                        },
                    ) {
                        Icon(
                            imageVector = CupidIconHome,
                            contentDescription = "menu",
                            tint = Color.Black
                        )
                    }
                    Spacer(modifier = Modifier.width(12.dp))
                }
            )
        }
        HorizontalDivider(thickness = 0.5.dp, color = Color(0xFFF0F0F0))
        if (hasAudioChannel) {
            val t1 = stringResource(id = R.string.cupid_family_hall)
            val t2 = stringResource(id = R.string.cupid_audio_channel)
            val titles = remember {
                listOf(t1, t2)
            }
            Box(modifier = Modifier.padding(50.dp, 12.dp)) {
                TabLayout(pagerState = pagerState, titles = titles)
            }
        }
    }
}

@Composable
private fun TopBar(applyCount: Int = 0, onClickApply: OnClick = {}) {
    AnimatedVisibility(visible = applyCount > 0) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp, 8.dp),
            contentAlignment = Alignment.Center
        ) {
            val c = Color(0xFFF53F3F)
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(36.dp)
                    .click(onClick = onClickApply)
                    .background(Color(0xFFF5DBDD), Shapes.extraSmall)
                    .padding(horizontal = 12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = ActionIcons.IconGroupPeople,
                    contentDescription = "",
                    tint = c,
                    modifier = Modifier.size(12.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = "${applyCount}件以上のグループチャット参加申請中",
                    color = c,
                    modifier = Modifier.weight(1f),
                    fontSize = 12.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Text(
                    "${if (applyCount > 99) "99+" else applyCount}",
                    fontSize = 11.sp,
                    color = Color.White,
                    modifier = Modifier
                        .background(
                            color = c,
                            shape = CircleShape
                        )
                        .padding(horizontal = 4.dp),
                    lineHeight = 12.sp,
                    textAlign = TextAlign.Center
                )
                Icon(painter = painterResource(id = R.drawable.ic_arrow_right), "", tint = c, modifier = Modifier.size(12.dp))
            }
        }
    }
}

@Preview
@Composable
private fun TopBarPreview() {
    CupidTheme {
        TopBar(applyCount = 100)
    }
}

@Preview
@Composable
private fun BottomBarPreview() {
    PreviewCupidTheme {
        val textFieldValue = remember {
            mutableStateOf(TextFieldValue())
        }
        Column {
            BottomBar(
                textFieldModifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 10.dp),
                textFieldValue = textFieldValue,
                showSendBtn = false,
                showTribeBoxEntry = false,
                audioPanelVisible = false,
                switchToAudioPanel = { },
                emojiPanelVisible = false,
                switchEmojiPanel = { },
                onShowGiftPanel = { },
                onAction = IIMAction.Empty
            )
        }
    }
}

val LocalFamilyPageInfo = staticCompositionLocalOf<ComposeState<TribeInfo?>> { mutableStateOf(null) }

