

package com.qyqy.cupid.ui.relations.family

import android.net.Uri
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ElevatedButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.data.FamilyConfig
import com.qyqy.cupid.model.FamilyViewModel
import com.qyqy.cupid.theme.CupidTheme
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.utils.CupidFamilyManager
import com.qyqy.cupid.utils.navigateToFamilyHome
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AnimatedDialog
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.self
import com.qyqy.ucoo.tribe.TribeManageEffect
import com.qyqy.ucoo.tribe.TribeManagerEvent
import com.qyqy.ucoo.utils.LogUtil
import com.qyqy.ucoo.utils.OnClick
import com.qyqy.ucoo.utils.SelectMedia
import com.qyqy.ucoo.utils.rememberRequestAlbumPermissionHelper
import io.github.album.MediaData
import kotlinx.coroutines.flow.collectLatest

@Composable
fun familyCreator(vm: FamilyViewModel = viewModel()): OnClick {
    val state by vm.uiState.collectAsStateWithLifecycle()
    val loadingState = LocalContentLoading.current
//    loadingState.value = state is LoadingState.Loading
    var visible by rememberSaveable {
        mutableStateOf(false)
    }
    val nav = LocalAppNavController.current
    LaunchedEffect(key1 = Unit) {
        vm.refreshConfig()
        vm.effect.collectLatest { effect: TribeManageEffect ->
            LogUtil.d("collect latest => $effect")
            when (effect) {
                is TribeManageEffect.CreateFinished -> {
                    visible = false
                    CupidFamilyManager.updateFamilyInfo(effect.tribe, false)
                    nav.navigateToFamilyHome()
                    loadingState.value = false
                }

                is TribeManageEffect.Toast -> {
                    LogUtil.d("toast : ${effect.msg}")
                    loadingState.value = false
                }

                TribeManageEffect.DestroyFinished,
                TribeManageEffect.UpdateFinished -> {
                    loadingState.value = false
                }

                TribeManageEffect.CreateFailed -> {
                    loadingState.value = false
                }
            }
        }
    }
    var selectImage by remember {
        mutableStateOf<MediaData?>(null)
    }
    val context = LocalContext.current
    val launcher =
        rememberRequestAlbumPermissionHelper(
            context = context,
            needCrop = true,
        ) { data: SelectMedia ->
            selectImage = data.list.getOrNull(0)
        }
    var textFieldValue by remember {
        mutableStateOf(TextFieldValue())
    }

    val config = vm.familyCreatorConfig.collectAsStateWithLifecycle()

    if (visible) {
        AnimatedDialog(onDismiss = { visible = false }) {
            Content(selectImage?.uri, textFieldValue, onChange = {
                textFieldValue = it
            }, onPhotoClick = {
                launcher.start()
            }, config = config.value, onClick = {
                selectImage?.run {
                    loadingState.value = true
                    vm.sendEvent(TribeManagerEvent.Create(this, textFieldValue.text))
                }
            })
        }
    }
    val creator = remember {
        { visible = true }
    }
    return creator
}

@Composable
private fun Content(
    selectImage: Uri?,
    textField: TextFieldValue,
    onPhotoClick: OnClick,
    config: FamilyConfig,
    onChange: (TextFieldValue) -> Unit,
    onClick: OnClick = {}
) {
    Column(
        modifier = Modifier
            .background(Color.White, RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp))
            .fillMaxWidth()
            .padding(16.dp)
            .padding(bottom = 20.dp)
            .imePadding()
            .navigationBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(text = stringResource(R.string.cupid_create_family), color = Color(0xFF1D2129), fontSize = 16.sp)
        Spacer(modifier = Modifier.height(32.dp))
        Box(
            modifier = Modifier
                .size(80.dp)
                .clip(Shapes.small)
                .click(onClick = onPhotoClick)
        ) {
            Image(
                painter = painterResource(id = R.drawable.cupid_ic_photo),
                contentDescription = "",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds
            )
            if (selectImage != null) {
                ComposeImage(model = selectImage, modifier = Modifier.fillMaxSize())
            }
        }
        Spacer(modifier = Modifier.height(24.dp))
        Text(
            text = stringResource(R.string.cupid_title_family_name),
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.fillMaxWidth()
        )
        val hint = stringResource(R.string.cupid_hint_create_family)
        Spacer(modifier = Modifier.height(10.dp))
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color(0xFFF1F2F3), Shapes.corner12)
                .heightIn(min = 48.dp)
                .padding(12.dp)
        ) {
            BasicTextField(
                value = textField,
                onValueChange = onChange,
                modifier = Modifier

                    .fillMaxWidth(),
                textStyle = MaterialTheme.typography.bodyMedium.copy(lineHeight = 18.sp, fontSize = 14.sp),
                decorationBox = { fn ->
                    fn.invoke()
                    if (textField.text.isEmpty()) {
                        Text(text = hint, color = Color(0x804E5969))
                    }
                }
            )
        }
        Spacer(modifier = Modifier.height(24.dp))
        Box(
            modifier = Modifier
                .align(Alignment.CenterHorizontally)

        ) {
            ElevatedButton(
                onClick,
                enabled = selectImage != null && textField.text.isNotEmpty(),
                modifier = Modifier.widthIn(min = 220.dp),
                colors = ButtonDefaults.elevatedButtonColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    disabledContainerColor = Color(0x80FF5E8B)
                )
            ) {
                Text(
                    text =
                    if (config.needCoin == 0)
                        stringResource(id = R.string.cupid_btn_create_family)
                    else
                        stringResource(id = R.string.cpd_family_creator_text, config.needCoin),
                    fontSize = 16.sp,
                    color = Color.White
                )
            }
            if (self.isBoy) {
                ComposeImage(
                    model = R.drawable.ic_cpd_vip, modifier = Modifier
                        .align(Alignment.TopEnd)
                        .offset(y = (-8).dp)
                )
            }
        }
    }
}

@Preview
@Composable
private fun Preview() {
    CupidTheme {
        Content(null, TextFieldValue(), {}, config = FamilyConfig(false, 100), {}) {}
    }
}