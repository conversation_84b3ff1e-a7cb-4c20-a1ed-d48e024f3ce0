@file:OptIn( ExperimentalMaterial3Api::class)

package com.qyqy.cupid.ui.relations.family

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.absoluteOffset
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Badge
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.TextUnitType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.cupid.model.FamilyPageViewModel
import com.qyqy.cupid.model.FamilyViewModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.share.ShareSource
import com.qyqy.cupid.utils.navigateToFamilyHome
import com.qyqy.cupid.widgets.click
import com.qyqy.cupid.widgets.composeClick
import com.qyqy.cupid.widgets.cpdNavigationIcon
import com.qyqy.cupid.widgets.state.CupidStateGridWidget
import com.qyqy.cupid.widgets.state.CupidStateLayoutDefaults
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.compose.presentation.chatgroup.page.ExpandableText
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.IconLoading
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.verticalScrollWithScrollbar
import com.qyqy.ucoo.tribe.bean.BriefUser
import com.qyqy.ucoo.tribe.bean.TribeConst
import com.qyqy.ucoo.tribe.bean.TribeInfo
import com.qyqy.ucoo.tribe.bean.TribeMemberItem
import com.qyqy.ucoo.tribe.bean.TribeUser

/**
 *  @time 8/16/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.relations.family
 */

private sealed interface FamilyDetailAction {
    data object JoinFamily : FamilyDetailAction
    data object GoSettings : FamilyDetailAction
    data class GoUserProfile(val user: User) : FamilyDetailAction
    data object GoChatPage : FamilyDetailAction
    data object InviteMember : FamilyDetailAction
}


@Composable
fun FamilyDetailPage(
    id: String
) {
    val controller = LocalAppNavController.current
    val viewModel = viewModel(FamilyViewModel::class.java, factory = viewModelFactory {
        initializer {
            FamilyViewModel(id)
        }
    })
    val loadingFlag = LocalContentLoading.current

    val info = viewModel.familyFlow.collectAsStateWithLifecycle()

    val isLoading = viewModel.isLoading.collectAsStateWithLifecycle()

    LaunchedEffect(key1 = isLoading.value) {
        loadingFlag.value = isLoading.value
    }

    LaunchedEffect(key1 = Unit) {
        viewModel.refresh()
    }

    if ((info.value.id ?: -1) == 0) {
        Box(contentAlignment = Alignment.Center, modifier = Modifier.fillMaxSize()) {
            IconLoading()
        }
    } else {
        FamilyDetailContent(info = info.value, {
            viewModel.refresh()
        }) { action ->
            when (action) {
                FamilyDetailAction.GoSettings -> {
                    controller.navigate(CupidRouters.FAMILY_SETTINGS, mapOf("family_id" to (info.value.id ?: 0)))
                }

                is FamilyDetailAction.GoUserProfile -> {
                    controller.navigate(CupidRouters.PROFILE, mapOf("userId" to action.user.userId))
                }

                FamilyDetailAction.JoinFamily -> {
                    //加入家族
                    viewModel.joinFamily()
                }

                FamilyDetailAction.GoChatPage -> {
                    //如果是从聊天页进来, 且这个id和当前页一致, 就直接返回
                    if (
                        controller.getPrevRoute()?.startsWith(CupidRouters.FAMILY_HOME) == true) {
                        controller.popBackStack()
                    } else {
                        controller.navigateToFamilyHome()
                    }
                }

                FamilyDetailAction.InviteMember -> {
                    controller.navigate(
                        CupidRouters.SHARE_TO_FRIENDS,
                        mapOf(
                            "sourceType" to ShareSource.SHARE_FAMILY,
                            "voiceRoomId" to ""
                        )
                    )
                }
            }
        }
    }
}

@Composable
private fun FamilyDetailContent(
    info: TribeInfo,
    onRefreshed: () -> Unit = {},
    onAction: (FamilyDetailAction) -> Unit = { }
) {
    //滑动状态
    val scrollState = rememberLazyGridState()
    //滑动透明度
    val titleAlpha = remember(scrollState) {
        derivedStateOf {
            if (scrollState.firstVisibleItemIndex == 0) {
                val offsetY = scrollState.firstVisibleItemScrollOffset
                if (offsetY < 200f) 0f else if (offsetY < 600f) offsetY / 600f else 1f
            } else {
                1f
            }
        }
    }

    val descDialogShow = remember {
        mutableStateOf(false)
    }

    val vm = viewModel(modelClass = FamilyPageViewModel::class.java, factory = viewModelFactory {
        initializer {
            FamilyPageViewModel(info.tribeId)
        }
    })


    Box(
        modifier = Modifier
            .background(color = Color.White)
            .fillMaxSize()
    ) {

        Box(modifier = Modifier.fillMaxSize()) {
            CupidStateGridWidget(vm,
                onRefreshed = onRefreshed,
                scrollState = scrollState,
                spanCount = 5, bottomLoading = { viewModel ->
                    Column {
                        CupidStateLayoutDefaults.LoadMoreIndicator(viewModel)
                        Spacer(modifier = Modifier.height(56.dp))
                    }
                }) { members ->
                item(span = { GridItemSpan(5) }) {
                    Box {
                        //顶部
                        ComposeImage(
                            model = info.avatarUrl, contentDescription = "family info header img",
                            contentScale = ContentScale.FillWidth,
                            modifier = Modifier
                                .height(200.dp)
                                .fillMaxWidth()
                        )
                        Spacer(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(200.dp)
                                .background(Color.Black.copy(alpha = 0.7f))
                        )
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier
                                .fillMaxWidth()
                        ) {
                            Spacer(modifier = Modifier.height(176.dp))

                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                                modifier = Modifier
                                    .background(color = Color.White, shape = RoundedCornerShape(24.dp))
                                    .padding(top = 64.dp)
                            ) {
                                Text(
                                    info.name ?: "",
                                    color = CpdColors.FF1D2129,
                                    fontWeight = FontWeight.Medium,
                                    fontSize = 16.sp,
                                )

                                ExpandableText(
                                    text = info.bulletin ?: "",
                                    style = SpanStyle(color = Color(0xFF4E5969))
                                    // Composable 大小的动画效果
                                    , modifier = Modifier
                                        .padding(top = 16.dp, start = 32.dp, end = 32.dp)
                                        .animateContentSize(),
                                    showAllStyle = SpanStyle(
                                        fontWeight = FontWeight.Bold,
                                        color = CpdColors.FFFF5E8B,
                                    ),
                                    expandText = ("..." + stringResource(id = R.string.cupid_family_detail_expand)) to stringResource(id = R.string.cupid_family_detail_expand),
                                    collapsedMaxLine = 5
                                ) {
                                    descDialogShow.value = !descDialogShow.value
                                }
                                Spacer(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(top = 24.dp)
                                        .height(8.dp)
                                        .background(color = Color(0xfFFFAFAFA))
                                )
                                //群组成员信息title
                                Row(
                                    horizontalArrangement = Arrangement.Center,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(16.dp, 24.dp)
                                ) {
                                    Text(
                                        stringResource(id = R.string.cpd_family_member),
                                        color = CpdColors.FF1D2129,
                                        modifier = Modifier.weight(1f)
                                    )
                                    Text(
                                        "${info.onlineMemberCnt}/${info.memberCnt}",
                                        color = Color(0xff8c8c8d)
                                    )
                                }
                            }
                        }
                        //家族介绍
                        Column(modifier = Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
                            Spacer(modifier = Modifier.height(128.dp))
                            ComposeImage(
                                model = info.avatarUrl,
                                Modifier
                                    .size(96.dp)
                                    .clip(RoundedCornerShape(8.dp))
                            )
                        }

                    }
                }
                itemsIndexed(members, key = { idx, r -> idx }) { idx, member ->
                    FamilyMemberContent(idx = idx, member = member, onAction)
                }
            }

            Row(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
                    .absoluteOffset(y = -20.dp)
                    .padding(horizontal = 24.dp)
                    .navigationBarsPadding(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                if (info.relationWithMe == TribeConst.RELATION_APPLYING) {
                    AppButton(
                        text = stringResource(id = R.string.cpd_family_wait_apply),
                        onClick = composeClick {

                        },
                        color = CpdColors.FF86909C,
                        fontSize = 16.sp,
                        enabled = false,
                        background = CpdColors.FFF1F2F3,
                        modifier = Modifier.weight(2f)
                    )
                } else if (info.relationWithMe == TribeConst.RELATION_NONE) {
                    AppButton(
                        text = stringResource(id = R.string.cpd_family_request_join),
                        onClick = composeClick {
                            onAction(FamilyDetailAction.JoinFamily)
                        },
                        color = Color.White,
                        fontSize = 16.sp,
                        background = CpdColors.FFFF5E8B,
                        modifier = Modifier.weight(2f)
                    )
                } else {
                    AppButton(
                        text = stringResource(id = R.string.cpd_invite),
                        fontSize = 16.sp, onClick = composeClick {
                            onAction(FamilyDetailAction.InviteMember)
                        },
                        color = CpdColors.FF1D2129,
                        background = Color(0xFFFFD862),
                        modifier = Modifier.weight(1f)
                    )
                    AppButton(
                        text = stringResource(id = R.string.cpd_family_go_chat),
                        fontSize = 16.sp, onClick = composeClick {
                            onAction(FamilyDetailAction.GoChatPage)
                        }, color = Color.White,
                        background = CpdColors.FFFF5E8B,
                        modifier = Modifier.weight(2f)
                    )
                }
            }

            if (descDialogShow.value) {
                val descScrollState = rememberScrollState()
                Dialog(onDismissRequest = {
                    descDialogShow.value = false
                }) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth(0.72f)
                            .height(320.dp)
                            .background(color = Color.White, shape = RoundedCornerShape(8.dp))
                            .padding(horizontal = 16.dp, vertical = 20.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(stringResource(R.string.cpd_family_intro), color = CpdColors.FF1D2129, fontSize = 17.sp)
                        Spacer(modifier = Modifier.height(4.dp))
                        Box(
                            modifier = Modifier
                                .verticalScrollWithScrollbar(descScrollState)
                                .weight(1f)
                        ) {
                            Text(
                                info.bulletin ?: "",
                                color = CpdColors.FF86909C
                            )
                        }
                        AppButton(
                            text = stringResource(id = R.string.cpd_iknow),
                            background = CpdColors.FFFF5E8B,
                            color = Color.White,
                            fontSize = 16.sp,
                            modifier = Modifier
                                .widthIn(min = 160.dp)
                                .padding(top = 12.dp)
                                .height(36.dp)
                        ) {
                            descDialogShow.value = false
                        }
                    }
                }
            }
        }
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White.copy(titleAlpha.value))
                .systemBarsPadding(),
        ) {
            cpdNavigationIcon(if (titleAlpha.value == 0f) Color.White else CpdColors.FF1D2129).invoke()

            Text(
                stringResource(id = R.string.cpd_family_detail),
                color = if (titleAlpha.value == 1f) CpdColors.FF1D2129 else Color.Transparent,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.align(Alignment.Center)
            )

            if (info.isMyTribe) {
                IconButton(onClick = composeClick {
                    onAction(FamilyDetailAction.GoSettings)
                }, modifier = Modifier.align(Alignment.TopEnd)) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_more_menu),
                        tint = if (titleAlpha.value == 0f) Color.White else CpdColors.FF1D2129,
                        contentDescription = "more setting"
                    )
                }
            }
        }
    }
}

@Composable
private fun FamilyMemberContent(idx: Int, member: TribeMemberItem, onAction: (FamilyDetailAction) -> Unit) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .padding(
                top = if (idx < 5) 0.dp else 24.dp,
                start = if (idx % 5 == 0) 4.dp else 0.dp,
                end = if (idx % 5 == 4) 4.dp else 0.dp,
            )
            .click {
                onAction(FamilyDetailAction.GoUserProfile(member.user.toAppUser()))
            }
    ) {
        //头像
        Box {
            CircleComposeImage(
                model = member.user.avatarUrl,
                modifier = Modifier
                    .size(56.dp)
            )
            if (member.role == TribeMemberItem.ROLE_ADMIN || member.role == TribeMemberItem.ROLE_OWNER) {
                Text(
                    if (member.role == TribeMemberItem.ROLE_OWNER)
                        stringResource(id = R.string.cpd_role_owner)
                    else stringResource(id = R.string.cpd_role_admin),
                    modifier = Modifier
                        .background(
                            Color(
                                if (member.role == TribeMemberItem.ROLE_OWNER)
                                    0xFFFF385C else 0xFF15ABFF
                            ),
                            shape = CircleShape
                        )
                        .padding(horizontal = 6.dp, vertical = 2.dp)
                        .align(Alignment.BottomCenter),
//                        textAlign = TextAlign.Center,
                    lineHeight = 11.sp,
                    fontSize = 10.sp,
                    color = Color.White
                )
            }


            if (member.is_online) {
                Badge(
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .offset(-(2).dp, (-2).dp)
                        .size(10.dp)
                        .alpha(1f)
                        .border(0.25.dp, Color(0xFFFFFFFF), CircleShape),
                    containerColor = Color(0xFF18E046),
                    contentColor = Color.White,
                ) {

                }
            }
        }
        //名称
        Text(
            member.user.nickname,
            lineHeight = TextUnit(12f, TextUnitType.Unspecified),
            fontSize = 12.sp,
            maxLines = 1,
            color = Color(0xFF4E5969),
            textAlign = TextAlign.Center,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier
                .padding(top = 8.dp)
                .width(64.dp)
        )
    }
}


@Composable
@Preview
fun FamilyMemberContentPreview() {
    Column {
        FamilyMemberContent(
            idx = 0, member = TribeMemberItem(
                apply_id = -1, member_id = 1, true, TribeMemberItem.ROLE_ADMIN, TribeUser(1, "", "1", "管理员")
            ), {})

        FamilyMemberContent(
            idx = 0, member = TribeMemberItem(
                apply_id = -1, member_id = 1, true, TribeMemberItem.ROLE_OWNER, TribeUser(1, "", "1", "群主")
            ), {})

        FamilyMemberContent(
            idx = 0, member = TribeMemberItem(
                apply_id = -1, member_id = 1, true, TribeMemberItem.ROLE_COMMON, TribeUser(1, "", "1", "普通")
            ), {})
    }
}

@Composable
@Preview
private fun FamilyDetailPagePreview() {
    FamilyDetailContent(info = TribeInfo(relationWithMe = 10, iAmAdmin = true, iAmOwner = true))
}

