

package com.qyqy.cupid.ui.relations.family

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.rememberScrollableState
import androidx.compose.foundation.gestures.scrollable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.cupid.model.FamilyViewModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.dialog.ContentAlertDialog
import com.qyqy.cupid.ui.dialog.DialogButton
import com.qyqy.cupid.ui.dialog.FamilyBanContent
import com.qyqy.cupid.ui.dialog.FamilyBanDialog
import com.qyqy.cupid.ui.dialog.TitleAlertDialog
import com.qyqy.cupid.ui.home.mine.edit.EditorPanel
import com.qyqy.cupid.ui.home.mine.edit.EditorTextField
import com.qyqy.cupid.utils.CupidFamilyManager
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.AnimatedDialog
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.home.main.MySwitch
import com.qyqy.ucoo.tribe.bean.TribeInfo
import com.qyqy.ucoo.utils.rememberRequestAlbumPermissionHelper

/**
 *  @time 8/19/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.relations.family
 */

private sealed interface FamilySettingAction {
    data object ChangeAvatar : FamilySettingAction
    data class ChangeFamilyName(val newName: String) : FamilySettingAction
    data class ChangeFamilyBuilt(val built: String) : FamilySettingAction
    data object GoAdminList : FamilySettingAction
    data object GoRemoveMember : FamilySettingAction
    data object SwitchJoinAudit : FamilySettingAction
    data object SwitchDontDisturb : FamilySettingAction

    data object ExitFamily : FamilySettingAction
    data object DisbandFamily : FamilySettingAction
}

private data object FamilySettingDialogType {
    const val NONE = 0
    const val CHANGE_NAME = 1
    const val CHANGE_DESCRIPTION = 2
    const val EXIT_FAMILY = 3
    const val DISBAND_FANILY = 4
}

/**
 * 家族设置, 设置页会依赖上一个页面的viewStoreOwner, 所以数据和上一个是共享的
 * 如果不存在上一个对象, 或者上一个对象没有FamilyViewModel, 则会重新创建一个viewModel
 */
@Composable
fun FamilySettingPage(
    id: String
) {
    val loadingFlag = LocalContentLoading.current
    val controller = LocalAppNavController.current
    val context = LocalContext.current

    val viewModel = viewModel(modelClass = FamilyViewModel::class.java, factory = viewModelFactory {
        initializer {
            FamilyViewModel(id)
        }
    }, viewModelStoreOwner = controller.composeNav.previousBackStackEntry ?: controller.composeNav.currentBackStackEntry!!)

    val familyInfo by viewModel.familyFlow.collectAsStateWithLifecycle()

    //图片选择(跳转到另外的activity)
    val uploadImageHelper = rememberRequestAlbumPermissionHelper(context) {
        viewModel.updateFamilyAvatar(it.list)
    }

    val isLoading = viewModel.isLoading.collectAsStateWithLifecycle()
    LaunchedEffect(key1 = isLoading.value) {
        loadingFlag.value = isLoading.value
    }

    val info = familyInfo
    Box {
        FamilySettingContent(info = info) { action ->
            when (action) {
                FamilySettingAction.ChangeAvatar -> {
                    uploadImageHelper.start()
                }

                is FamilySettingAction.ChangeFamilyBuilt -> {
                    viewModel.updateFamilyInfo(familyIntro = action.built)
                }

                is FamilySettingAction.ChangeFamilyName -> {
                    viewModel.updateFamilyInfo(familyName = action.newName)
                }

                FamilySettingAction.GoAdminList -> {
                    controller.navigate(CupidRouters.FAMILY_ADMIN_MANAGER, mapOf("family_id" to id))
                }

                FamilySettingAction.GoRemoveMember -> {
                    controller.navigate(CupidRouters.FAMILY_ADMIN_MEMBER_REMOVE, mapOf("family" to info))
                }

                FamilySettingAction.SwitchDontDisturb -> {
                    viewModel.updateFamilyInfo(dontDisturb = !info.dontDisturbEnable)
                }

                FamilySettingAction.SwitchJoinAudit -> {

                }

                FamilySettingAction.DisbandFamily -> {
                    viewModel.disbandFamily {
                        loadingFlag.value = false
                    }
                }

                FamilySettingAction.ExitFamily -> {
                    viewModel.exitFamily {
                        loadingFlag.value = false
                    }
                }
            }
        }
        val familyState = CupidFamilyManager.familyState.collectAsStateWithLifecycle()

        if (familyState.value != 0) {
            Dialog(onDismissRequest = { }) {
                FamilyBanContent(familyState.value){

                }
            }
        }
    }
}

@Composable
private fun FamilySettingContent(info: TribeInfo, onAction: (FamilySettingAction) -> Unit = {}) {
    val scrollState = rememberScrollableState {
        0f
    }

    var showDialogType by remember {
        mutableStateOf(0)
        //0 不显示 1 名称修改 2简介修改 3 退出家族 4 解散家族
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .systemBarsPadding()
    ) {
        Column(modifier = Modifier.scrollable(scrollState, Orientation.Vertical)) {
            CupidAppBar(title = stringResource(id = R.string.cpd_family_settings))
            if (info.iAmOwner || info.iAmAdmin) {
                if (info.iAmOwner) {
                    FamilySettingItem(title = stringResource(R.string.cpd_settings_avatar), {
                        onAction(FamilySettingAction.ChangeAvatar)
                    }) {
                        CircleComposeImage(model = info.avatarUrl, modifier = Modifier.size(40.dp))
                        Image(painter = painterResource(id = R.drawable.ic_arrow_right), contentDescription = "right arrow")
                    }
                    Divider(modifier = Modifier.padding(horizontal = 16.dp))
                    FamilySettingItem(title = stringResource(R.string.cpd_settings_name), {
                        showDialogType = FamilySettingDialogType.CHANGE_NAME
                    }) {
                        Text(info.name ?: "", fontSize = 12.sp, color = Color(0xFF787979))
                        Image(painter = painterResource(id = R.drawable.ic_arrow_right), contentDescription = "right arrow")
                    }
                    Divider(modifier = Modifier.padding(horizontal = 16.dp))
                }
                FamilySettingItem(title = stringResource(R.string.cpd_settings_description), {
                    showDialogType = FamilySettingDialogType.CHANGE_DESCRIPTION
                }) {
                    Text(
                        info.bulletin ?: "", fontSize = 12.sp, color = Color(0xFF787979), maxLines = 1, overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.widthIn(max = 180.dp)
                    )
                    Image(painter = painterResource(id = R.drawable.ic_arrow_right), contentDescription = "right arrow")
                }
                if (info.iAmOwner) {
                    Divider(modifier = Modifier.padding(horizontal = 16.dp))
                    FamilySettingItem(title = stringResource(R.string.cpd_settings_manager), {
                        onAction(FamilySettingAction.GoAdminList)
                    }) {
                        Text(stringResource(R.string.cpd_settings_admin_maximum), fontSize = 12.sp, color = Color(0xFF787979))
                        Image(painter = painterResource(id = R.drawable.ic_arrow_right), contentDescription = "right arrow")
                    }
                }
                Divider(modifier = Modifier.padding(horizontal = 16.dp))
                FamilySettingItem(title = stringResource(R.string.cpd_settings_remove_member), {
                    onAction(FamilySettingAction.GoRemoveMember)
                }) {
                    Image(painter = painterResource(id = R.drawable.ic_arrow_right), contentDescription = "right arrow")
                }
                Divider(modifier = Modifier.padding(horizontal = 16.dp))
            }
            FamilySettingItem(title = stringResource(id = R.string.cpd_family_dontdisturb), {
                onAction(FamilySettingAction.SwitchDontDisturb)
            }) {
                MySwitch(active = info.dontDisturbEnable,
                    activeColor = CpdColors.FFFF5E8B,
                    onClick = {
                        onAction(FamilySettingAction.SwitchDontDisturb)
                    })
            }
        }

        //底部按钮
        if (info.iAmOwner) {
            AppButton(
                text = stringResource(id = R.string.cpd_family_disband),
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(horizontal = 32.dp, vertical = 16.dp)
                    .fillMaxWidth(),
                background = Color(0xFFFF5E8B),
                color = Color(0xFFFFFFFF),
            ) {
                showDialogType = FamilySettingDialogType.DISBAND_FANILY
            }
        } else {
            AppButton(
                text = stringResource(id = R.string.cpd_family_exit),
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(horizontal = 32.dp, vertical = 16.dp)
                    .fillMaxWidth(),
                background = Color(0xFFFF5E8B),
                color = Color(0xFFFFFFFF),
            ) {
                showDialogType = FamilySettingDialogType.EXIT_FAMILY
            }
        }

        if (showDialogType != 0) {
            val onClose = { showDialogType = 0 }
            when (showDialogType) {
                FamilySettingDialogType.CHANGE_NAME -> {
                    AnimatedDialog(onDismiss = { showDialogType = 0 }) {
                        var displayText by remember {
                            mutableStateOf(info.name ?: "")
                        }
                        EditorPanel(
                            title = stringResource(R.string.cupid_modify_nickname),
                            composeContent = {
                                EditorTextField(
                                    displayText,
                                    {
                                        displayText = it.replace("\n", "").take(20)
                                    },
                                    hint = stringResource(id = R.string.cpd_please_input_your_nick_name),
                                    maxLength = 20,
                                    modifier = Modifier
                                        .padding(top = 20.dp, bottom = 20.dp)
                                        .height(100.dp)
                                )
                            }, onSave = {
                                if (displayText.isNotEmpty()) {
                                    onAction(FamilySettingAction.ChangeFamilyName(displayText))
                                    onClose()
                                }
                            }, onClose = onClose
                        )
                    }
                }

                FamilySettingDialogType.CHANGE_DESCRIPTION -> {
                    AnimatedDialog(onDismiss = { showDialogType = 0 }) {
                        var displayText by remember {
                            mutableStateOf(info.bulletin ?: "")
                        }
                        EditorPanel(
                            title = stringResource(R.string.cpd_modify_family_built),
                            composeContent = {
                                EditorTextField(
                                    displayText,
                                    {
                                        displayText = it.take(500)
                                    },
                                    hint = stringResource(id = R.string.cpd_family_built_hint),
                                    maxLength = 500,
                                    modifier = Modifier
                                        .padding(top = 20.dp, bottom = 20.dp)
                                        .height(168.dp)
                                )
                            }, onSave = {
                                if (displayText.isNotEmpty()) {
                                    onAction(FamilySettingAction.ChangeFamilyBuilt(displayText))
                                    onClose()
                                }
                            }, onClose = onClose
                        )
                    }
                }

                FamilySettingDialogType.EXIT_FAMILY -> {
                    Dialog(onDismissRequest = { showDialogType = 0 }) {
                        ContentAlertDialog(
                            content = stringResource(id = R.string.cpd_family_exit_tips),
                            startButton = DialogButton(stringResource(id = R.string.cpd_cancel)) {
                                showDialogType = 0
                            },
                            endButton = DialogButton(stringResource(id = R.string.cpd_confirm)) {
                                //退出家族
                                onAction(FamilySettingAction.ExitFamily)
                                showDialogType = 0
                            }
                        )
                    }
                }

                FamilySettingDialogType.DISBAND_FANILY -> {
                    Dialog(onDismissRequest = { showDialogType = 0 }) {
                        TitleAlertDialog(
                            title = stringResource(id = R.string.cpd_family_disband_tips),
                            startButton = DialogButton(stringResource(id = R.string.cpd_cancel)) {
                                showDialogType = 0
                            },
                            endButton = DialogButton(stringResource(id = R.string.cpd_confirm)) {
                                //退出家族
                                onAction(FamilySettingAction.DisbandFamily)
                                showDialogType = 0
                            }
                        )
                    }
                }
            }
        }
    }
}

//region widgets

@Composable
private fun FamilySettingItem(title: String, onClick: (() -> Unit)? = null, content: @Composable () -> Unit) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable {
                onClick?.invoke()
            }
            .padding(horizontal = 16.dp)
            .height(64.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            modifier = Modifier.weight(1f),
            text = title,
            fontSize = 16.sp,
            color = CpdColors.FF1D2129
        )
        content()
    }
}

@Composable
private fun Divider(modifier: Modifier = Modifier) {
    Spacer(
        modifier = modifier
            .fillMaxWidth()
            .height(1.dp)
            .background(color = Color(0x14FFFFFF))
    )
}

//endregion

@Composable
@Preview
private fun FamilySettingPagePreview() {
    FamilySettingPage(id = "")
}