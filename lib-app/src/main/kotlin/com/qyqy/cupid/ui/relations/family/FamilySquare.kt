@file:OptIn( ExperimentalMaterial3Api::class)

package com.qyqy.cupid.ui.relations.family

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.model.FamilySquarePageViewModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.theme.CupidColors
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.NavListener
import com.qyqy.cupid.ui.dialog.FamilyRemindDialog
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.global.BannerView
import com.qyqy.cupid.ui.global.ScreenLocation
import com.qyqy.cupid.ui.relations.family.party.PartyListScreen
import com.qyqy.cupid.utils.CupidFamilyManager
import com.qyqy.cupid.utils.eventbus.EventBusListener
import com.qyqy.cupid.utils.isTodayHasRan
import com.qyqy.cupid.widgets.CpdAppTabRow
import com.qyqy.cupid.widgets.CupidBasicAppBar
import com.qyqy.cupid.widgets.click
import com.qyqy.cupid.widgets.defaultNavigationIcon
import com.qyqy.ucoo.R
import com.qyqy.ucoo.ReportExposureCompose
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.compose.LaunchOnceEffect
import com.qyqy.ucoo.compose.getSampleImageUrl
import com.qyqy.ucoo.compose.state.EmptyView
import com.qyqy.ucoo.compose.state.PullRefreshBox
import com.qyqy.ucoo.compose.state.StateLayoutDefaults
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppTab
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.core.page.PageLoadEvent
import com.qyqy.ucoo.tribe.bean.Tribe
import com.qyqy.ucoo.tribe.bean.TribeConst
import com.qyqy.ucoo.utils.LogUtils
import com.qyqy.ucoo.utils.OnClick
import eu.bambooapps.material3.pullrefresh.PullRefreshIndicatorDefaults
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

object FamilySquareAction {
    const val CREATE_FAMILY = "create_family"
    const val SHOW_REMIND_DIALOG = "show_remind_dialog"
    const val SHOW_AUDIO_CHANNEL = "goAudioChannel"
}

@Composable
fun FamilySquareScreen(withNavigation: Boolean = true, effect: Flow<String>? = null) {
    val vm: FamilySquarePageViewModel = viewModel()
    val controller = LocalAppNavController.current
    val dialogQueue = LocalDialogQueue.current
    val tribe by CupidFamilyManager.tribeFlow.collectAsStateWithLifecycle()

    LaunchOnceEffect {
        vm.sendEvent(PageLoadEvent.Refresh)
    }

    val creator = familyCreator()

    LaunchedEffect(key1 = effect) {
        effect?.onEach {
            when (it) {

                FamilySquareAction.SHOW_REMIND_DIALOG -> {
                    dialogQueue.push(FamilyRemindDialog)
                }

                else -> {}
            }
        }?.launchIn(this)
    }
    Scaffold(
        topBar = {
            CupidBasicAppBar(
                navigationIcon = {
                    if (withNavigation) defaultNavigationIcon.invoke()
                },
                title = {
                    Text(
                        text = stringResource(R.string.cupid_family_square),
                        style = MaterialTheme.typography.titleLarge
                    )
                }, actions = {
                    Text(
                        text = if (tribe == null) stringResource(R.string.cupid_create) else stringResource(
                            id = R.string.cpd_family_mytribe
                        ),
                        color = Color(0xFFFF5E8B),
                        modifier = Modifier
                            .click(onClick = {
                                if (tribe == null) {
                                    creator.invoke()
                                } else {
                                    controller.navigate(CupidRouters.FAMILY_HOME)
                                }
                            })
                            .padding(16.dp, 4.dp)
                    )
                })
        }
    ) { pv ->
        val dataState by vm.dataListFlow.collectAsStateWithLifecycle()
        val l = dataState.getOrNull()
        val tribeList = remember(l) {
            l?.distinctBy { it.id }.orEmpty()
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(pv)
                .padding(horizontal = 16.dp)
        ) {
            BannerView(
                location = ScreenLocation.FAMILY_SQUARE,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp)
            )
            PullRefreshBox(
                modifier = Modifier
                    .fillMaxSize(),
                colors = PullRefreshIndicatorDefaults.colors(
                    containerColor = MaterialTheme.colorScheme.surface,
                    MaterialTheme.colorScheme.primary
                ),
                isRefreshing = vm.isRefreshing,
                onRefresh = { vm.sendEvent(PageLoadEvent.Refresh) }) {
                LazyColumn(modifier = Modifier.fillMaxSize()) {
                    if (dataState.isSuccess) {

                        if (tribeList.isEmpty()) {
                            item {
                                EmptyView(
                                    textRes = R.string.cupid_no_data,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .aspectRatio(0.75f)
                                )
                            }
                        } else {
                            items(tribeList) { item: Tribe ->
                                val avatars = remember(item.sampleMembers) {
                                    item.sampleMembers.orEmpty().take(5).map { it.avatarUrl }
                                }
                                Spacer(modifier = Modifier.height(8.dp))
                                FamilyCard(
                                    title = item.name.orEmpty(),
                                    icon = item.avatarUrl.orEmpty(),
                                    desc = item.bulletin.orEmpty(),
                                    peopleCount = item.memberCnt ?: 1,
                                    relationWithMe = item.relationWithMe ?: 0,
                                    avatars = avatars
                                ) {
                                    controller.navigate(
                                        CupidRouters.FAMILY_DETAIL,
                                        mapOf("family_id" to item.id)
                                    )
                                }
                            }
                            item {
                                Spacer(modifier = Modifier.height(8.dp))
                            }
                            if (vm.allowLoad) {
                                item {
                                    Box(modifier = Modifier.fillMaxWidth()) {
                                        LaunchedEffect(true) {
                                            vm.sendEvent(PageLoadEvent.PageLoadMore)
                                        }
                                        StateLayoutDefaults.BottomLoadBar(hasMore = true)
                                    }
                                }
                            }
                            item {
                                Spacer(modifier = Modifier.height(8.dp))
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun FamilySquareTab() {
    val controller = LocalAppNavController.current
    val dialogQueue = LocalDialogQueue.current

    val config by UIConfig.configFlow.collectAsStateWithLifecycle()
    val tabs by remember {
        derivedStateOf { config.getOrNull()?.squareTabs.orEmpty() }
    }
    val pagerState = rememberPagerState {
        tabs.size
    }

    val scope = rememberCoroutineScope()
    val listener = remember(dialogQueue, scope) {
        object : NavListener(HOME_SUB_NAV) {
            override fun handleNav(route: String): Boolean {
                when (route) {

                    FamilySquareAction.SHOW_REMIND_DIALOG -> {
                        val index = tabs.indexOfFirst { it.type == 101 }
                        if (index != -1) {
                            scope.launch {
                                pagerState.scrollToPage(index)
                            }
                            return false
                        }
                    }

                    FamilySquareAction.SHOW_AUDIO_CHANNEL -> {
                        val index = tabs.indexOfFirst { it.type == 102 }
                        if (index != -1) {
                            scope.launch {
                                pagerState.scrollToPage(index)
                            }
                            return true
                        }
                    }

                    else -> {
                        val t = route.toIntOrNull()
                        val index = tabs.indexOfFirst { it.type == t }
                        if (index != -1) {
                            scope.launch {
                                pagerState.scrollToPage(index)
                            }
                            return true
                        }
                    }
                }
                return false
            }
        }
    }
    EventBusListener(listener = listener)

    Column(modifier = Modifier.fillMaxSize()) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .systemBarsPadding(),
            contentAlignment = Alignment.TopCenter
        ) {
            val titles = remember(tabs) {
                return@remember tabs.map { AppTab(it.name) }
            }
            CpdAppTabRow(
                tabs = titles,
                pagerState = pagerState,
                indicatorColor = CpdColors.FFFF5E8B,
                tabSelectedColor = CpdColors.FF1D2129,
                tabUnSelectedColor = CpdColors.FF86909C,
                modifier = Modifier
                    .fillMaxWidth(0.6f)

            )
        }


        HorizontalPager(state = pagerState, beyondViewportPageCount = 0) { pageIndex ->
            val tab = tabs[pageIndex]
            when (tab.type) {
                101 -> {//家族列表
                    val tribe by CupidFamilyManager.tribeFlow.collectAsStateWithLifecycle()

                    val creator = familyCreator()

                    val vm: FamilySquarePageViewModel = viewModel()
                    LaunchedEffect(Unit) {
                        vm.sendEvent(PageLoadEvent.Refresh)
                    }
                    val dataState by vm.dataListFlow.collectAsStateWithLifecycle()
                    val l = dataState.getOrNull()
                    val tribeList = remember(l) {
                        l?.distinctBy { it.id }.orEmpty()
                    }

                    ReportExposureCompose(exposureName = TracePoints.VISIT_LIVE_FAMILY_SQUARE) {
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(horizontal = 16.dp)
                        ) {
                            var isSwitchTab by remember {
                                mutableStateOf(false)
                            }
                            val familyEventListener = remember(dialogQueue, scope) {
                                object : NavListener(HOME_SUB_NAV) {
                                    override fun handleNav(route: String): Boolean {
                                        when (route) {
                                            FamilySquareAction.SHOW_REMIND_DIALOG -> {
                                                isSwitchTab = false
                                                dialogQueue.push(FamilyRemindDialog)
                                                LogUtils.i(
                                                    "FamilySquare",
                                                    "show family remain dialog by message cell click"
                                                )
                                                return true
                                            }

                                            "homeSwitchTab" -> {
                                                isSwitchTab = true
                                                return true
                                            }

                                            "showFamilyCreator" -> {
                                                creator.invoke()
                                                return true
                                            }

                                            else -> {
                                                return false
                                            }
                                        }

                                    }
                                }
                            }
                            EventBusListener(listener = familyEventListener)

                            if (isSwitchTab) {
                                isTodayHasRan("key_family_dialog_show") {
                                    LogUtils.i(
                                        "FamilySquare",
                                        "show family remain dialog by bottom click"
                                    )
                                    dialogQueue.push(FamilyRemindDialog)
                                    return@isTodayHasRan true
                                }
                            }
                            PullRefreshBox(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(vertical = 8.dp),
                                colors = PullRefreshIndicatorDefaults.colors(
                                    containerColor = MaterialTheme.colorScheme.surface,
                                    MaterialTheme.colorScheme.primary
                                ),
                                isRefreshing = vm.isRefreshing,
                                onRefresh = { vm.sendEvent(PageLoadEvent.Refresh) }) {
                                Box(modifier = Modifier.fillMaxSize()) {
                                    LazyColumn(modifier = Modifier.fillMaxSize()) {
                                        if (dataState.isSuccess) {

                                            if (tribeList.isEmpty()) {
                                                item {
                                                    EmptyView(
                                                        textRes = R.string.cupid_no_data,
                                                        modifier = Modifier
                                                            .fillMaxWidth()
                                                            .aspectRatio(0.75f)
                                                    )
                                                }
                                            } else {
                                                items(tribeList) { item: Tribe ->
                                                    val avatars = remember(item.sampleMembers) {
                                                        item.sampleMembers.orEmpty().take(5)
                                                            .map { it.avatarUrl }
                                                    }
                                                    Spacer(modifier = Modifier.height(8.dp))
                                                    FamilyCard(
                                                        title = item.name.orEmpty(),
                                                        icon = item.avatarUrl.orEmpty(),
                                                        desc = item.bulletin.orEmpty(),
                                                        peopleCount = item.memberCnt ?: 1,
                                                        relationWithMe = item.relationWithMe ?: 0,
                                                        avatars = avatars
                                                    ) {
                                                        Analytics.reportClickEvent(TracePoints.CLICK_LIVE_FAMILY_SQUARE_CELL)
                                                        controller.navigate(
                                                            CupidRouters.FAMILY_DETAIL,
                                                            mapOf("family_id" to item.id)
                                                        )
                                                    }
                                                }
                                                item {
                                                    Spacer(modifier = Modifier.height(8.dp))
                                                }
                                                if (vm.allowLoad) {
                                                    item {
                                                        Box(modifier = Modifier.fillMaxWidth()) {
                                                            LaunchedEffect(true) {
                                                                vm.sendEvent(PageLoadEvent.PageLoadMore)
                                                            }
                                                            StateLayoutDefaults.BottomLoadBar(
                                                                hasMore = true
                                                            )
                                                        }
                                                    }
                                                }
                                                item {
                                                    Spacer(modifier = Modifier.height(56.dp))
                                                }
                                            }
                                        }
                                    }

                                    Row(
                                        modifier = Modifier
                                            .padding(bottom = 8.dp)
                                            .clip(RoundedCornerShape(50))
                                            .click {
                                                Analytics.reportClickEvent(TracePoints.CLICK_LIVE_FAMILY_CREATE_BUTTON)
                                                if (tribe == null) {
                                                    creator.invoke()
                                                } else {
                                                    controller.navigate(CupidRouters.FAMILY_HOME)
                                                }
                                            }
                                            .align(Alignment.BottomCenter)
                                            .background(
                                                Brush.horizontalGradient(
                                                    listOf(
                                                        Color(0xFFFFA3BC),
                                                        CpdColors.FFFF5E8B
                                                    )
                                                ),
                                                RoundedCornerShape(50)
                                            )
                                            .fillMaxWidth(0.57f)
                                            .height(44.dp),
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.Center
                                    ) {
                                        Image(
                                            painter = painterResource(if (tribe == null) (R.drawable.ic_cpd_family_creator) else (R.drawable.ic_cpd_family_mine)),
                                            "",
                                            modifier = Modifier.size(24.dp)
                                        )
                                        Spacer(modifier = Modifier.width(4.dp))
                                        AppText(
                                            text = if (tribe == null) stringResource(R.string.cupid_create) else stringResource(
                                                id = R.string.cpd_family_mytribe
                                            ),
                                            fontSize = 16.sp,
                                            color = Color.White
                                        )
                                    }
                                }
                            }
                        }
                    }
                }

                102 -> {//语音房列表
                    ReportExposureCompose(exposureName = TracePoints.VISIT_LIVE_AUDIO_ROOM_LIST) {
                        PartyListScreen()
                    }
                }

                else -> Unit
            }
        }
    }

}

@Composable
private fun FamilyCard(
    title: String,
    icon: String,
    desc: String,
    peopleCount: Int,
    avatars: List<String>,
    relationWithMe: Int = 0,
    modifier: Modifier = Modifier,
    onClick: OnClick = {},
) {
    Card(
        shape = Shapes.small, modifier = modifier
            .fillMaxWidth()
            .click { onClick() },
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(modifier = Modifier.padding(8.dp)) {
            Row {
                ComposeImage(
                    model = icon, modifier = Modifier
                        .size(72.dp)
                        .clip(Shapes.small)
                )
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 10.dp)
                        .padding(top = 10.dp),
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.headlineMedium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    Spacer(modifier = Modifier.height(12.dp))
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Box {
                            avatars.forEachIndexed { index: Int, s: String ->
                                Box(
                                    modifier = Modifier
                                        .padding(start = (index * 20).dp)
                                        .zIndex(-1f * index)
                                ) {
                                    ComposeImage(
                                        model = s, modifier = Modifier
                                            .size(24.dp)
                                            .clip(CircleShape)
                                            .border(0.5.dp, Color.White, CircleShape)
                                    )
                                }
                            }
                        }
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = stringResource(R.string.cupid_format_people_count, peopleCount),
                            color = Color(0xFF86909C),
                            fontSize = 12.sp
                        )
                    }
                }
            }
            Spacer(modifier = Modifier.height(12.dp))
            HorizontalDivider(thickness = 0.5.dp, color = CupidColors.Divider)
            Spacer(modifier = Modifier.height(12.dp))
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = desc,
                    modifier = Modifier
                        .weight(1f)
                        .padding(end = 12.dp),
                    color = Color(0xFF4E5969),
                    fontSize = 12.sp,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                val color = when (relationWithMe) {
                    TribeConst.RELATION_NONE -> MaterialTheme.colorScheme.primary
                    TribeConst.RELATION_APPLYING -> CpdColors.FF86909C
                    TribeConst.RELATION_MEMBER -> CpdColors.FF86909C
                    else -> MaterialTheme.colorScheme.primary
                }
                OutlinedButton(
                    onClick = onClick,
                    border = BorderStroke(
                        1.dp,
                        if (relationWithMe == 0) color else Color.Transparent
                    ),
                    modifier = Modifier.height(30.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        containerColor = if (relationWithMe == 0) Color.White else CpdColors.FFF1F2F3
                    ),
                    contentPadding = PaddingValues(horizontal = 12.dp)
                ) {
                    when (relationWithMe) {
                        TribeConst.RELATION_NONE -> {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_add_photo),
                                contentDescription = "",
                                tint = color,
                                modifier = Modifier.size(14.dp)
                            )
                            Spacer(modifier = Modifier.width(2.dp))
                            Text(
                                text =
                                stringResource(R.string.cupid_join), color = color, fontSize = 14.sp
                            )
                        }

                        TribeConst.RELATION_APPLYING -> {
                            Text(
                                text = stringResource(R.string.cupid_applying),
                                color = color,
                                fontSize = 14.sp
                            )
                        }

                        TribeConst.RELATION_MEMBER -> {
                            Text(
                                text = stringResource(R.string.cupid_joined),
                                color = color,
                                fontSize = 14.sp
                            )
                        }

                        else -> {}
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun CardPreview() {
    PreviewCupidTheme {
        FamilyCard(
            "Super Man",
            getSampleImageUrl(400, 400),
            "If you like to play Mario, remember to join in",
            128,
            List(5) { "" },
            relationWithMe = 5
        )
    }
}

@Preview
@Composable
private fun Preview() {
    PreviewCupidTheme {
        FamilySquareScreen()
    }
}