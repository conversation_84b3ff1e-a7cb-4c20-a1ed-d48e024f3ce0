

package com.qyqy.cupid.ui.relations.family

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.im.messages.MessageReminder
import com.qyqy.cupid.theme.CupidTheme
import com.qyqy.cupid.widgets.LevelComposeView
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroup
import com.qyqy.ucoo.compose.presentation.chatgroup.page.LocalMsgEventHandler
import com.qyqy.ucoo.compose.presentation.chatgroup.page.MsgEvents
import com.qyqy.ucoo.compose.presentation.config.BaseChatUIProvider
import com.qyqy.ucoo.compose.presentation.room.SpanText
import com.qyqy.ucoo.compose.presentation.room.TextSpan
import com.qyqy.ucoo.compose.presentation.room.buildTextSpan
import com.qyqy.ucoo.compose.presentation.room.color
import com.qyqy.ucoo.compose.presentation.room.inlineTextContent
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.im.compat.UCInstanceMessage
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.tribe.bean.TribeInfo
import com.qyqy.ucoo.utils.ComposeState

class FamilyChatUiProvider : BaseChatUIProvider(
    isFamilyConversation = true,
    showBottomGiftIcon = false
) {
    override val showBottomGiftIcon: Boolean
        get() = false

    @Composable
    override fun ItemLayout(entry: UIMessageEntry<UCInstanceMessage>, content: @Composable ColumnScope.() -> Unit) {
        val isSelf = entry.isSelf
        val user = entry.user
        val listener = LocalMsgEventHandler.current
        val maxContentWidth = 235.dp
        val pageInfo = LocalFamilyPageInfo.current
        val avatarModifier = Modifier
            .size(40.dp)
            .clip(CircleShape)
            .click {
                listener?.handle(MsgEvents.SeeUser(user))
            }
        if (isSelf) {
            Row(
                modifier = Modifier
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.End
            ) {
                Column(modifier = Modifier.widthIn(max = maxContentWidth), horizontalAlignment = Alignment.End) {
                    Text(text = user.nickname, style = MaterialTheme.typography.bodyMedium)
                    RoleLabel(pageInfo, user)
                    content()
                    MessageReminder(entry, Modifier.padding(top = 5.dp))
                }
                Box(modifier = Modifier.padding(horizontal = 16.dp)) {
                    ComposeImage(model = user.avatarUrl, modifier = avatarModifier)
                }
            }
        } else {
            Row(
                modifier = Modifier
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.Start
            ) {
                Box(modifier = Modifier.padding(horizontal = 16.dp)) {
                    ComposeImage(model = user.avatarUrl, modifier = avatarModifier)
                }
                Column(modifier = Modifier.widthIn(max = maxContentWidth)) {
                    Text(text = user.nickname, style = MaterialTheme.typography.bodyMedium)
                    RoleLabel(pageInfo, user)
                    content()
                    MessageReminder(entry, Modifier.padding(top = 5.dp))
                }
            }
        }
    }
}

@Composable
private fun RoleLabel(infoState: ComposeState<TribeInfo?>, user: User) {
    val pageInfo = infoState.value
    val density = LocalDensity.current
    val role = if (pageInfo?.ownerId == user.id) {
        ChatGroup.Role.OWNER
    } else if (pageInfo?.adminList?.contains(user.id) == true) {
        ChatGroup.Role.ADMIN
    } else {
        ChatGroup.Role.MEMBER
    }
    val textSpan = remember(density, user, role) {
        buildUserSpan(density, user, role)
    }
    Spacer(modifier = Modifier.height(4.dp))
    SpanText(textSpan = textSpan)
    Spacer(modifier = Modifier.height(8.dp))
}

private fun buildUserSpan(
    density: Density,
    user: User,
    role: Int,
): TextSpan {
    val paddingValues = PaddingValues(start = 4.dp, top = 2.dp, bottom = 2.dp)
    val list = buildList {
        if (role == ChatGroup.Role.OWNER) {
            add(inlineTextContent(key = "会长", alternateText = "会长", paddingValues = PaddingValues(vertical = 2.dp)) {
                Text(
                    text = stringResource(R.string.cpd_role_owner),
                    modifier = Modifier
                        .background(Color(0xFFFF385C), RoundedCornerShape(50))
                        .padding(horizontal = 6.dp),
                    color = Color.White,
                    fontSize = 10.sp,
                    lineHeight = 18.sp
                )
            })
        } else if (role == ChatGroup.Role.ADMIN) {
            add(inlineTextContent(key = "管理者", alternateText = "管理者", paddingValues = PaddingValues(vertical = 2.dp)) {
                Text(
                    text = stringResource(R.string.cpd_role_admin),
                    modifier = Modifier
                        .background(Color(0xFF15ABFF), RoundedCornerShape(50))
                        .padding(horizontal = 6.dp),
                    color = Color.White,
                    fontSize = 10.sp,
                    lineHeight = 18.sp
                )
            })
        }

        add(
            inlineTextContent(
                key = "level",
                density = density,
                width = 84,
                height = 16,
                paddingValues = if (role == ChatGroup.Role.MEMBER) PaddingValues(vertical = 2.dp) else paddingValues
            ) { modifier ->
                LevelComposeView(user = user, modifier = modifier)
            }
        )

        user.medalList.forEachIndexed { index, medal ->
            add(inlineTextContent(
                key = "medal_$index",
                density = density,
                width = medal.width,
                height = medal.height,
                paddingValues = paddingValues
            ) { modifier ->
                ComposeImage(
                    model = medal.icon, modifier = modifier
                )
            })
        }
    }
    return buildTextSpan {
        it.appendInlineContentList(list)
    }
}

@Preview
@Composable
private fun ItemLayoutPreview() {
    val uiProvider = FamilyChatUiProvider()
    CupidTheme {
//        uiProvider.ItemLayout(message = Message(), user = userForPreview) {
//
//        }
    }
}

@Composable
fun FamilyTipContent(message: String, icon: String? = null, user: User? = null) {
    val mh = LocalMsgEventHandler.current
    Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
        Row(
            modifier = Modifier
                .widthIn(max = 300.dp)
                .heightIn(min = 40.dp)
                .background(Color(0xFFF6EBF0), Shapes.small)
                .padding(8.dp, 6.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (!icon.isNullOrEmpty()) {
                CircleComposeImage(model = icon, modifier = Modifier
                    .size(28.dp)
                    .clip(CircleShape)
                    .click {
                        if (user != null && mh != null) {
                            mh.handle(MsgEvents.SeeUser(user))
                        }
                    })
                Spacer(modifier = Modifier.width(8.dp))
            }
            Text(text = message, fontSize = 12.sp, color = MaterialTheme.colorScheme.primary, lineHeight = 14.sp)
        }
    }
}

@Preview
@Composable
private fun StatusPreview() {
    CupidTheme {
        FamilyTipContent(message = "リーダーが家族のお知らせを更新しました")
    }
}

@Composable
fun FamilyGiftContent(
    icon: String,
    messageReceiver: AnnotatedString,
    messageGift: AnnotatedString,
    senderIsMe: Boolean,
    isMultiReceivers: Boolean,
) {

    if (senderIsMe) {
        val shape = RoundedCornerShape(topStart = 12.dp, bottomStart = 12.dp, bottomEnd = 12.dp)
        Row(
            modifier = Modifier
                .width(245.dp)
                .background(Brush.horizontalGradient(listOf(Color(0xFFFFF3F9), Color(0xFFFFE0EF))), shape)
                .border(0.5.dp, Color(0xFFFFC8E9), shape)
                .padding(12.dp), verticalAlignment = Alignment.CenterVertically
        ) {
            ComposeImage(model = icon, modifier = Modifier.size(48.dp))
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 12.dp)
            ) {
                Text(text = messageReceiver, fontSize = if (isMultiReceivers) 12.sp else 16.sp)
                Spacer(modifier = Modifier.height(10.dp))
                Text(
                    text = messageGift,
                    color = Color(0xFF86909C),
                    fontSize = 12.sp,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    } else {
        val shape = RoundedCornerShape(topEnd = 12.dp, bottomStart = 12.dp, bottomEnd = 12.dp)
        Row(
            modifier = Modifier
                .width(245.dp)
                .background(Brush.horizontalGradient(listOf(Color(0xFFFFE0EF), Color(0xFFFFF3F9))), shape)
                .border(0.5.dp, Color(0xFFFFC8E9), shape)
                .padding(12.dp), verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 12.dp)
            ) {
                Text(
                    text = messageReceiver,
                    fontSize = if (isMultiReceivers) 12.sp else 16.sp,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                Spacer(modifier = Modifier.height(10.dp))
                Text(
                    text = messageGift,
                    color = Color(0xFF86909C),
                    fontSize = 12.sp,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
            ComposeImage(model = icon, modifier = Modifier.size(48.dp))
        }
    }
}

@Preview
@Composable
private fun FamilyGiftContentPreview() {
    CupidTheme {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(2f), contentAlignment = Alignment.Center
        ) {
            FamilyGiftContent(
                icon = "",
                messageReceiver = buildAnnotatedString {
                    color(Color(0xFF1D2129)) {
                        append("送给")
                    }
                    color(Color(0xFFFF5E8B)) {
                        append("Sakura、itoko332、kaz")
                    }
                },
                messageGift = buildAnnotatedString { append("梦幻城包x1") },
                senderIsMe = true,
                isMultiReceivers = true
            )
        }
    }
}

