package com.qyqy.cupid.ui.relations.family.icons


import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import kotlin.Suppress

val TaskFill: ImageVector
    get() {
        if (_TaskFill != null) {
            return _TaskFill!!
        }
        _TaskFill = ImageVector.Builder(
            name = "TaskFill",
            defaultWidth = 25.dp,
            defaultHeight = 24.dp,
            viewportWidth = 25f,
            viewportHeight = 24f
        ).apply {
            group {
                path(
                    fill = SolidColor(Color(0xFFFF6720)),
                    pathFillType = PathFillType.EvenOdd
                ) {
                    moveTo(15.5f, 2f)
                    curveTo(15.851f, 2f, 16.196f, 2.092f, 16.5f, 2.268f)
                    curveTo(16.804f, 2.444f, 17.056f, 2.696f, 17.232f, 3f)
                    horizontalLineTo(18.5f)
                    curveTo(19.03f, 3f, 19.539f, 3.211f, 19.914f, 3.586f)
                    curveTo(20.289f, 3.961f, 20.5f, 4.47f, 20.5f, 5f)
                    verticalLineTo(17f)
                    curveTo(20.5f, 18.326f, 19.973f, 19.598f, 19.035f, 20.535f)
                    curveTo(18.098f, 21.473f, 16.826f, 22f, 15.5f, 22f)
                    horizontalLineTo(6.5f)
                    curveTo(5.97f, 22f, 5.461f, 21.789f, 5.086f, 21.414f)
                    curveTo(4.711f, 21.039f, 4.5f, 20.53f, 4.5f, 20f)
                    verticalLineTo(5f)
                    curveTo(4.5f, 4.47f, 4.711f, 3.961f, 5.086f, 3.586f)
                    curveTo(5.461f, 3.211f, 5.97f, 3f, 6.5f, 3f)
                    horizontalLineTo(7.768f)
                    curveTo(7.944f, 2.696f, 8.196f, 2.444f, 8.5f, 2.268f)
                    curveTo(8.804f, 2.092f, 9.149f, 2f, 9.5f, 2f)
                    horizontalLineTo(15.5f)
                    close()
                    moveTo(15.324f, 9.379f)
                    lineTo(11.082f, 13.622f)
                    lineTo(9.667f, 12.207f)
                    curveTo(9.478f, 12.025f, 9.226f, 11.924f, 8.964f, 11.926f)
                    curveTo(8.701f, 11.929f, 8.451f, 12.034f, 8.265f, 12.219f)
                    curveTo(8.08f, 12.405f, 7.975f, 12.655f, 7.972f, 12.918f)
                    curveTo(7.97f, 13.18f, 8.071f, 13.432f, 8.253f, 13.621f)
                    lineTo(10.303f, 15.672f)
                    curveTo(10.405f, 15.774f, 10.526f, 15.855f, 10.66f, 15.911f)
                    curveTo(10.793f, 15.966f, 10.936f, 15.994f, 11.081f, 15.994f)
                    curveTo(11.226f, 15.994f, 11.369f, 15.966f, 11.502f, 15.911f)
                    curveTo(11.636f, 15.855f, 11.757f, 15.774f, 11.859f, 15.672f)
                    lineTo(16.739f, 10.793f)
                    curveTo(16.832f, 10.7f, 16.906f, 10.59f, 16.956f, 10.468f)
                    curveTo(17.006f, 10.347f, 17.032f, 10.217f, 17.032f, 10.086f)
                    curveTo(17.032f, 9.954f, 17.006f, 9.824f, 16.955f, 9.703f)
                    curveTo(16.905f, 9.582f, 16.831f, 9.471f, 16.739f, 9.378f)
                    curveTo(16.646f, 9.286f, 16.535f, 9.212f, 16.414f, 9.162f)
                    curveTo(16.293f, 9.112f, 16.163f, 9.086f, 16.031f, 9.086f)
                    curveTo(15.9f, 9.086f, 15.77f, 9.112f, 15.648f, 9.162f)
                    curveTo(15.527f, 9.212f, 15.417f, 9.286f, 15.324f, 9.379f)
                    close()
                    moveTo(15f, 4f)
                    horizontalLineTo(10f)
                    curveTo(9.883f, 4f, 9.77f, 4.041f, 9.68f, 4.116f)
                    curveTo(9.59f, 4.191f, 9.529f, 4.295f, 9.508f, 4.41f)
                    lineTo(9.5f, 4.5f)
                    verticalLineTo(5.5f)
                    curveTo(9.5f, 5.617f, 9.541f, 5.73f, 9.616f, 5.82f)
                    curveTo(9.691f, 5.91f, 9.795f, 5.971f, 9.91f, 5.992f)
                    lineTo(10f, 6f)
                    horizontalLineTo(15f)
                    curveTo(15.117f, 6f, 15.23f, 5.959f, 15.32f, 5.884f)
                    curveTo(15.41f, 5.809f, 15.471f, 5.705f, 15.492f, 5.59f)
                    lineTo(15.5f, 5.5f)
                    verticalLineTo(4.5f)
                    curveTo(15.5f, 4.383f, 15.459f, 4.27f, 15.384f, 4.18f)
                    curveTo(15.309f, 4.09f, 15.205f, 4.029f, 15.09f, 4.008f)
                    lineTo(15f, 4f)
                    close()
                }
            }
        }.build()

        return _TaskFill!!
    }

@Suppress("ObjectPropertyName")
private var _TaskFill: ImageVector? = null
