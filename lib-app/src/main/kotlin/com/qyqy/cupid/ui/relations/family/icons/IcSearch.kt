package com.qyqy.cupid.ui.relations.family.icons


import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import kotlin.LazyThreadSafetyMode

val IcSearch: ImageVector by lazy(LazyThreadSafetyMode.NONE) {
    ImageVector.Builder(
        name = "IcSearch",
        defaultWidth = 20.dp,
        defaultHeight = 20.dp,
        viewportWidth = 20f,
        viewportHeight = 20f
    ).apply {
        path(
            stroke = SolidColor(Color(0xFF86909C)),
            strokeLineWidth = 2f,
            strokeLineCap = StrokeCap.Round,
            strokeLineJoin = StrokeJoin.Round
        ) {
            moveTo(9.583f, 17.499f)
            curveTo(13.955f, 17.499f, 17.5f, 13.955f, 17.5f, 9.583f)
            curveTo(17.5f, 5.21f, 13.955f, 1.666f, 9.583f, 1.666f)
            curveTo(5.211f, 1.666f, 1.666f, 5.21f, 1.666f, 9.583f)
            curveTo(1.666f, 13.955f, 5.211f, 17.499f, 9.583f, 17.499f)
            close()
        }
        path(
            stroke = SolidColor(Color(0xFF86909C)),
            strokeLineWidth = 2f,
            strokeLineCap = StrokeCap.Round,
            strokeLineJoin = StrokeJoin.Round
        ) {
            moveTo(18.333f, 18.334f)
            lineTo(15.414f, 15.414f)
        }
    }.build()
}
