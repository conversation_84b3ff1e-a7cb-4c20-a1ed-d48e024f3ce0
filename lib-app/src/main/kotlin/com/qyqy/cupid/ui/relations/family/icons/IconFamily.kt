package com.qyqy.cupid.ui.relations.family.icons

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import kotlin.LazyThreadSafetyMode

val IconFamily: ImageVector by lazy(LazyThreadSafetyMode.NONE) {
    ImageVector.Builder(
        name = "IconFamily",
        defaultWidth = 32.dp,
        defaultHeight = 32.dp,
        viewportWidth = 32f,
        viewportHeight = 32f
    ).apply {
        group {
            path(
                fill = Brush.linearGradient(
                    colorStops = arrayOf(
                        0f to Color(0xFFD38CFF),
                        1f to Color(0xFFC25EFF)
                    ),
                    start = Offset(24.677f, 4.073f),
                    end = Offset(17.208f, 24.593f)
                )
            ) {
                moveTo(22.418f, 4.005f)
                lineTo(13.061f, 7.17f)
                curveTo(12.009f, 7.522f, 10.988f, 8.701f, 10.793f, 9.79f)
                lineTo(9.155f, 18.619f)
                curveTo(8.865f, 20.195f, 9.849f, 21.915f, 11.355f, 22.463f)
                lineTo(23.065f, 26.725f)
                curveTo(24.56f, 27.269f, 26.434f, 26.577f, 27.221f, 25.194f)
                lineTo(31.64f, 17.378f)
                curveTo(32.181f, 16.415f, 32.157f, 14.855f, 31.587f, 13.913f)
                lineTo(26.45f, 5.484f)
                curveTo(25.659f, 4.178f, 23.85f, 3.52f, 22.418f, 4.005f)
                close()
            }
            path(
                fill = Brush.linearGradient(
                    colorStops = arrayOf(
                        0f to Color(0xFFFF8CAC),
                        1f to Color(0xFFFF5E8B)
                    ),
                    start = Offset(15.998f, 2.91f),
                    end = Offset(15.998f, 29.081f)
                )
            ) {
                moveTo(13.426f, 3.759f)
                lineTo(4.185f, 11.159f)
                curveTo(3.145f, 11.986f, 2.479f, 13.733f, 2.705f, 15.04f)
                lineTo(4.479f, 25.654f)
                curveTo(4.799f, 27.548f, 6.612f, 29.081f, 8.533f, 29.081f)
                horizontalLineTo(23.467f)
                curveTo(25.374f, 29.081f, 27.201f, 27.534f, 27.521f, 25.654f)
                lineTo(29.295f, 15.04f)
                curveTo(29.508f, 13.733f, 28.841f, 11.986f, 27.815f, 11.159f)
                lineTo(18.574f, 3.772f)
                curveTo(17.147f, 2.625f, 14.84f, 2.625f, 13.426f, 3.759f)
                close()
            }
            path(
                fill = Brush.linearGradient(
                    colorStops = arrayOf(
                        0f to Color(0xFFFFE9EF),
                        1f to Color(0xFFFFD6E1)
                    ),
                    start = Offset(16f, 13.33f),
                    end = Offset(16f, 25.523f)
                )
            ) {
                moveTo(20.399f, 13.873f)
                curveTo(21.818f, 14.7f, 22.816f, 16.379f, 22.772f, 18.338f)
                curveTo(22.679f, 22.475f, 17.016f, 25.523f, 16f, 25.523f)
                curveTo(14.984f, 25.523f, 9.32f, 22.475f, 9.228f, 18.338f)
                curveTo(9.184f, 16.379f, 10.182f, 14.7f, 11.601f, 13.873f)
                curveTo(12.929f, 13.1f, 14.597f, 13.095f, 16f, 14.236f)
                curveTo(17.403f, 13.095f, 19.071f, 13.099f, 20.399f, 13.873f)
                close()
            }
        }
    }.build()
}
