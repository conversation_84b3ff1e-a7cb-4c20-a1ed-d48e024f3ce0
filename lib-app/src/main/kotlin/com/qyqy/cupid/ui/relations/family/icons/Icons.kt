package com.qyqy.cupid.ui.relations.family.icons

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import com.qyqy.cupid.ui.home.message.icons.ActionIcons
import kotlin.LazyThreadSafetyMode

val ActionIcons.PlusBlack: ImageVector by lazy(LazyThreadSafetyMode.NONE) {
    ImageVector.Builder(
        name = "PlusBlack",
        defaultWidth = 12.dp,
        defaultHeight = 12.dp,
        viewportWidth = 12f,
        viewportHeight = 12f
    ).apply {
        path(
            fill = SolidColor(Color(0xFF4E5969)),
            pathFillType = PathFillType.EvenOdd
        ) {
            moveTo(7f, 1f)
            curveTo(7f, 0.448f, 6.552f, 0f, 6f, 0f)
            curveTo(5.448f, 0f, 5f, 0.448f, 5f, 1f)
            verticalLineTo(5f)
            horizontalLineTo(1f)
            curveTo(0.448f, 5f, 0f, 5.448f, 0f, 6f)
            curveTo(0f, 6.552f, 0.448f, 7f, 1f, 7f)
            horizontalLineTo(5f)
            verticalLineTo(11f)
            curveTo(5f, 11.552f, 5.448f, 12f, 6f, 12f)
            curveTo(6.552f, 12f, 7f, 11.552f, 7f, 11f)
            verticalLineTo(7f)
            horizontalLineTo(11f)
            curveTo(11.552f, 7f, 12f, 6.552f, 12f, 6f)
            curveTo(12f, 5.448f, 11.552f, 5f, 11f, 5f)
            horizontalLineTo(7f)
            verticalLineTo(1f)
            close()
        }
    }.build()
}
