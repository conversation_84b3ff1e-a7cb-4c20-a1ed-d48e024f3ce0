package com.qyqy.cupid.ui.relations.family.panels

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.data.ResultData
import com.qyqy.cupid.data.data
import com.qyqy.cupid.data.isLoading
import com.qyqy.cupid.data.resultData
import com.qyqy.cupid.data.setData
import com.qyqy.cupid.data.setLoading
import com.qyqy.cupid.theme.CupidTheme
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.home.message.icons.ActionIcons
import com.qyqy.cupid.ui.home.message.icons.Chat
import com.qyqy.cupid.ui.home.message.icons.Gift
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.cupid.ui.relations.family.icons.PlusBlack
import com.qyqy.cupid.widgets.AvatarComposeView
import com.qyqy.cupid.widgets.CpdButton
import com.qyqy.cupid.widgets.GiftWallContent
import com.qyqy.cupid.widgets.LevelComposeView
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.compose.domain.usecase.profile.ToggleFollowUseCase
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.ui.AgeGender
import com.qyqy.ucoo.compose.ui.AnimatedDialog
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.setting.ReportActivity
import com.qyqy.ucoo.user.UserManager
import com.qyqy.ucoo.utils.ComposeState
import com.qyqy.ucoo.utils.EntityCallback
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch


@Composable
fun ProfilePanel(
    user: User,
    modifier: Modifier = Modifier,
    onReport: OnClick = {},
    onCheckProfile: OnClick = {},
    onAt: OnClick = {},
    onFollow: OnClick = {},
    onSendGift: OnClick = {},
    onChat: OnClick = {},
    onClickGiftWall: OnClick = {},
) {

    Box(
        modifier = modifier
            .fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 20.dp)
                .background(Color.White, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(68.dp))

            Text(
                text = user.nickname,
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.widthIn(max = 180.dp),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            Row(
                modifier = Modifier.padding(top = 4.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(4.dp, Alignment.CenterHorizontally),
                verticalAlignment = Alignment.CenterVertically
            ) {
                AgeGender(age = user.age, isBoy = user.isBoy)
                LevelComposeView(user = user)
            }

            Spacer(modifier = Modifier.height(10.dp))

            val tags = remember(user.height, user.nativeProfile) {
                buildList {
                    val np = user.nativeProfile
                    if (np!=null &&np.cityCode.name.isNotEmpty()) {
                        add(np.cityCode.name)
                    }
                    if (user.height > 0) {
                        add("${user.height}cm")
                    }
                    if (np!=null && np.job.code != "0") {
                        add(np.job.name)
                    }
                }.filter { it.isNotEmpty() }.joinToString(separator = " | ")
            }
            Text(text = tags, color = Color(0xFF4E5969), fontSize = 14.sp, textAlign = TextAlign.Center)
            if (user.shortIntro.isNotEmpty()) {
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = user.shortIntro,
                    modifier = Modifier
                        .padding(horizontal = 16.dp),
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.labelMedium.copy(fontSize = 12.sp)
                )
            }
            Spacer(modifier = Modifier.height(16.dp))
            GiftWallContent(
                lightUpCnt = user.giftWallStats.lightUpCnt,
                totalCnt = user.giftWallStats.totalCnt,
                modifier = Modifier.padding(horizontal = 8.dp)
            ) {
                onClickGiftWall()
            }

            if (user.isSelf) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 32.dp, bottom = 20.dp), contentAlignment = Alignment.Center
                ) {
                    CpdButton(
                        modifier = Modifier.widthIn(min = 220.dp),
                        text = stringResource(R.string.cpd_check_my_profile),
                        onClick = onCheckProfile
                    )
                }
            } else {
                Spacer(
                    modifier = Modifier
                        .height(40.dp)
                        .fillMaxWidth()
                )
                HorizontalDivider(thickness = 0.5.dp)
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(min = 40.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    val style = remember {
                        TextStyle(color = Color(0xFF4E5969), fontSize = 12.sp)
                    }
                    val mod = Modifier
                        .weight(1f)
                        .heightIn(min = 40.dp)
                    Box(modifier = mod.click(onClick = onAt), contentAlignment = Alignment.Center) {
                        Text(text = stringResource(R.string.cpd_menu_at), style = style)
                    }
                    if (!user.followed) {
                        VerticalDivider(thickness = 0.5.dp, modifier = Modifier.height(24.dp))
                        Box(modifier = mod.click(onClick = onFollow), contentAlignment = Alignment.Center) {
                            Row(verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.Center) {
                                Icon(imageVector = ActionIcons.PlusBlack, contentDescription = "", tint = style.color)
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(text = stringResource(R.string.cpd_menu_follow), style = style)
                            }
                        }
                    }
                    VerticalDivider(thickness = 0.5.dp, modifier = Modifier.height(24.dp))
                    Box(modifier = mod.click(onClick = onSendGift), contentAlignment = Alignment.Center) {
                        Row(verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.Center) {
                            Icon(
                                imageVector = ActionIcons.Gift,
                                contentDescription = "",
                                tint = MaterialTheme.colorScheme.primary
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(text = stringResource(R.string.cpd_send_gift), style = style)
                        }
                    }
                    VerticalDivider(thickness = 0.5.dp, modifier = Modifier.height(24.dp))
                    Box(modifier = mod.click(onClick = onChat), contentAlignment = Alignment.Center) {
                        Row(verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.Center) {
                            Icon(imageVector = ActionIcons.Chat, contentDescription = "", tint = style.color)
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(text = stringResource(R.string.cpd_menu_chat), style = style)
                        }
                    }
                }
            }
            Spacer(
                modifier = Modifier
                    .navigationBarsPadding()
                    .padding(bottom = 10.dp)
            )
        }

        AvatarComposeView(
            user = user,
            modifier = Modifier
                .size(80.dp)
                .align(Alignment.TopCenter)
        )
        if (user.isSelf.not()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 20.dp), horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = stringResource(id = R.string.cpd举报),
                    modifier = Modifier
                        .click(onClick = onReport)
                        .padding(16.dp),
                    style = TextStyle(color = Color(0xFF86909C), fontSize = 12.sp)
                )
                Text(
                    text = stringResource(R.string.cpd_check_profile),
                    modifier = Modifier
                        .click(onClick = onCheckProfile)
                        .padding(16.dp),
                    color = MaterialTheme.colorScheme.primary,
                    fontSize = 12.sp
                )
            }
        }
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF050505)
@Composable
private fun ProfilePanelPreview() {
    CupidTheme {
        ProfilePanel(
            user = userForPreview.copy(
                id = "123",
                shortIntro = "こんにちは、皆さん初めましてこんにちは、皆さん初めましてこんにちは、皆さん初めまして"
            )
        )
    }
}


class ProfilePanelViewModel(
    val uid: String,
    val followUseCase: ToggleFollowUseCase = ToggleFollowUseCase(),
) : ViewModel() {


    private val pair = UserManager.getUserState(viewModelScope, uid)
    private val _state: MutableState<ResultData<User>> = mutableStateOf(resultData<User>())
    val state: ComposeState<ResultData<User>> = _state

    init {
        _state.value = _state.value.setLoading(true)
        viewModelScope.launch {
            pair.first.collectLatest {
                _state.value = _state.value.setData(it)
            }
        }
    }

    fun refresh() {
        pair.second.invoke()
    }

    fun changeFollow(uid: String, follow: Boolean) {
        viewModelScope.launch {
            followUseCase.invoke(uid to follow)
                .onSuccess {
                    UserManager.updateUserIfExist(uid) { u ->
                        u.copy(followed = follow)
                    }
                }
        }
    }

}

@Composable
fun rememberUserPanelState(onAt: EntityCallback<User>, onSendGift: EntityCallback<User>): MutableState<String?> {
    val idState = rememberSaveable {
        mutableStateOf<String?>(null)
    }
    val uid = idState.value
    if (uid != null) {
        val vm: ProfilePanelViewModel = viewModel(key = uid) {
            ProfilePanelViewModel(uid)
        }
        val state by vm.state
        val loadingState = LocalContentLoading.current
        loadingState.value = state.isLoading
        LaunchedEffect(key1 = vm) {
            vm.refresh()
        }
        val user = state.data()
        if (user != null) {
            val nav = LocalAppNavController.current
            AnimatedDialog(onDismiss = { idState.value = null }) {
                ProfilePanel(user = user, onReport = {
                    nav.navigate(
                        CupidRouters.REPORT_START, mapOf(
                            "type" to ReportActivity.TYPE_USER,
                            "id" to user.id
                        )
                    )
                    idState.value = null
                }, onAt = {
                    onAt(user)
                    idState.value = null
                }, onCheckProfile = {
                    nav.navigateToProfile(uid)
                    idState.value = null
                }, onFollow = {
                    vm.changeFollow(user.id, !user.followed)
                }, onSendGift = {
                    onSendGift(user)
                }, onChat = {
                    nav.navigate(CupidRouters.C2CChat, mapOf("user" to user))
                    idState.value = null
                }, onClickGiftWall = {
                    nav.navigate(
                        CupidRouters.GIFT_WALL, mapOf(
                            "userId" to user.userId
                        )
                    )
                })
            }
        }
    }
    return idState
}