

package com.qyqy.cupid.ui.relations.family.panels

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ElevatedButton
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.CupidTheme
import com.qyqy.cupid.ui.home.message.icons.ActionIcons
import com.qyqy.cupid.ui.home.message.icons.RbSelected
import com.qyqy.cupid.ui.home.message.icons.RbUnselected
import com.qyqy.cupid.ui.relations.family.icons.IcSearch
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.BasicUser
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.state.EmptyView
import com.qyqy.ucoo.compose.state.LoadMoreView
import com.qyqy.ucoo.compose.state.OnLoadMore
import com.qyqy.ucoo.compose.ui.AgeGender
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.utils.ComposeContent
import com.qyqy.ucoo.utils.EntityCallback
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.delay

@Composable
fun SelectUserItem(user: BasicUser, isSelect: Boolean, onClick: OnClick = {}) {
    Row(modifier = Modifier.fillMaxWidth()) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .click(onClick = onClick)
                .padding(16.dp, 12.dp)
                .fillMaxWidth(),
        ) {
            CircleComposeImage(
                model = user.avatarUrl,
                modifier = Modifier
                    .size(48.dp),
            )

            Column(
                verticalArrangement = Arrangement.Center,
                modifier = Modifier
                    .padding(horizontal = 8.dp)
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = user.nickname,
                        fontSize = 16.sp,
                        modifier = Modifier.requiredWidthIn(10.dp, 120.dp),
                        overflow = TextOverflow.Ellipsis,
                        maxLines = 1
                    )
                }
                Spacer(modifier = Modifier.height(12.dp))
                AgeGender(age = user.age, isBoy = user.isBoy)
            }
            Spacer(modifier = Modifier.weight(1f, true))
            Icon(
                imageVector = if (isSelect) ActionIcons.RbSelected else ActionIcons.RbUnselected,
                contentDescription = "",
                tint = if (isSelect) MaterialTheme.colorScheme.primary else Color(0xFFDADADA),
                modifier = Modifier
                    .size(18.dp)
            )
        }
    }
}

@Preview
@Composable
private fun SelectUserItemPreview() {
    CupidTheme {
        SelectUserItem(user = userForPreview, isSelect = true)
    }
}

@Composable
fun SelectUserPanel(
    title: String,
    isLoaded: Boolean,
    allUsers: List<BasicUser>,
    selectUserList: List<BasicUser> = emptyList(),
    loadMoreEnable: Boolean = false,
    onLoadMore: OnLoadMore = {},
    onSelect: EntityCallback<List<BasicUser>> = {},
    onBack: OnClick = {},
    isSearching: Boolean = false,
    onSearch: EntityCallback<String> = {}
) {
    Scaffold(modifier = Modifier
        .fillMaxWidth(), topBar = {
        CupidAppBar(title = title, navigationIcon = {
            IconButton(onClick = onBack) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_cpd_navigation_back),
                    contentDescription = "back",
                    tint = Color(0xFF1D2129),
                )
            }
        })
    }) { pv ->
        Box(
            modifier = Modifier
                .background(Color.White)
                .padding(pv)
        ) {
            var selectUsers by remember(selectUserList) {
                mutableStateOf(buildList { addAll(selectUserList) })
            }
            var tfst by remember {
                mutableStateOf(TextFieldValue())
            }
            LaunchedEffect(key1 = tfst.text) {
                delay(600L)
                //search
                onSearch(tfst.text)
            }
            Column(modifier = Modifier.fillMaxSize()) {
                Spacer(modifier = Modifier.height(8.dp))
                SearchBar(value = tfst, onValueChange = {
                    tfst = it
                }, hintContent = {
                    Text(text = stringResource(R.string.cpd_member_search_hint), color = Color(0xFFC9CDD4), fontSize = 12.sp)
                }, isSearching = isSearching,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .background(
                            Color(0xFFF1F2F3),
                            RoundedCornerShape(50)
                        )
                        .padding(16.dp, 8.dp)
                )
                LazyColumn(modifier = Modifier.fillMaxSize()) {
                    if (isLoaded) {
                        items(allUsers) { u ->
                            SelectUserItem(user = u, isSelect = selectUsers.contains(u), onClick = {
                                if (selectUsers.contains(u)) {
                                    selectUsers -= u
                                } else {
                                    selectUsers += u
                                }
                            })
                            HorizontalDivider(
                                thickness = 0.5.dp,
                                modifier = Modifier.padding(horizontal = 16.dp),
                                color = Color(0xFFF1F2F3)
                            )
                        }
                        if (allUsers.isEmpty()) {
                            item {
                                EmptyView(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .aspectRatio(0.75f)
                                )
                            }
                        }
                        item{
                            Spacer(modifier = Modifier.height(80.dp))
                        }
                    }
                    if (loadMoreEnable) {
                        item {
                            LoadMoreView(onLoadMore)
                        }
                    }
                }
            }
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(84.dp)
                    .background(Brush.verticalGradient(listOf(Color(0x00FFFFFF), Color(0xCCFFFFFF), Color.White)))
                    .padding(horizontal = 32.dp)
                    .align(Alignment.BottomCenter)
            ) {
                ElevatedButton(
                    onClick = {
                        onSelect(selectUsers)
                    },
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.elevatedButtonColors(
                        containerColor = MaterialTheme.colorScheme.primary,
                        contentColor = Color.White
                    )
                ) {
                    Text(text = stringResource(id = R.string.cpd确认))
                }
            }
        }
    }
}

@Preview
@Composable
private fun SelectUserPanelPreview() {
    CupidTheme {
        SelectUserPanel(title = "选择送礼对象", false, allUsers = listOf(userForPreview, userForPreview))
    }
}

@Composable
fun SearchBar(
    value: TextFieldValue,
    onValueChange: (TextFieldValue) -> Unit,
    modifier: Modifier = Modifier,
    textStyle: TextStyle = MaterialTheme.typography.bodyMedium,
    isSearching: Boolean = false,
    hintContent: ComposeContent = {}
) {
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        Icon(imageVector = IcSearch, contentDescription = "", modifier = Modifier.size(20.dp), tint = Color(0xFF86909C))
        Spacer(modifier = Modifier.width(8.dp))
        BasicTextField(
            value = value,
            onValueChange = onValueChange,
            modifier = Modifier.weight(1f),
            textStyle = textStyle,
            decorationBox = {
                Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.CenterStart) {
                    if (value.text.isEmpty()) {
                        hintContent()
                    }
                    it.invoke()
                }
            })
        if (isSearching) {
            CircularProgressIndicator(
                modifier = Modifier.size(18.dp),
                color = MaterialTheme.colorScheme.primary,
                strokeWidth = 3.dp
            )
        }
    }
}