

package com.qyqy.cupid.ui.relations.family.panels

import android.util.Log
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.coerceAtLeast
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.im.panel.gift.GiftListModel
import com.qyqy.cupid.im.panel.gift.GiftPanelContent
import com.qyqy.cupid.im.panel.gift.GiftPanelScaffold
import com.qyqy.cupid.im.panel.gift.GiftPosition
import com.qyqy.cupid.im.panel.gift.GiftViewModel
import com.qyqy.cupid.im.panel.gift.LuckGiftContent
import com.qyqy.cupid.theme.CupidTheme
import com.qyqy.cupid.ui.GiftExtra
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.BasicUser
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.compose.LaunchOnceEffect
import com.qyqy.ucoo.compose.ui.AnimatedDialog
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.FullDialog
import com.qyqy.ucoo.im.bean.CPGift
import com.qyqy.ucoo.im.bean.GiftTab
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.tribe.bean.TribeMemberItem
import com.qyqy.ucoo.utils.OnClick

@Composable
fun SendGiftPanel(
    giftListModel: GiftListModel,
    userSelectorVisible: Boolean = true,
    selectUserList: List<BasicUser> = emptyList(),
    position: GiftPosition? = null,
    onAddClick: OnClick = {},
    onRecharge: OnClick = {},
    onSend: (CPGift, Int, GiftExtra) -> Boolean = { _, _, _ -> true },
    onGiftSelected: (CPGift?) -> Unit = {},
    onComboFinish: () -> Unit = {}
) {
    GiftPanelScaffold(giftListModel) { onSelectedChange ->
        val density = LocalDensity.current

        Column(
            modifier = Modifier
                .background(Color(0xFF292929))
                .padding(16.dp)
                .padding(
                    bottom = with(density) {
                        WindowInsets.navigationBars
                            .getBottom(this)
                            .toDp()
                    }.coerceAtLeast(15.dp)
                )
        ) {
            if (userSelectorVisible) {
                val listState = rememberLazyListState()
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 15.dp),
                    verticalAlignment = Alignment.CenterVertically,

                    ) {
                    Image(
                        painter = painterResource(id = R.drawable.cpd_ic_add_dash),
                        "add",
                        modifier = Modifier
                            .size(24.dp)
                            .click(onClick = onAddClick),
                    )

                    if (selectUserList.isEmpty()) {
                        Row(
                            modifier = Modifier
                                .height(32.dp)
                                .click(onClick = onAddClick)
                                .padding(start = 8.dp)
                                .weight(1f),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = stringResource(id = R.string.cupid_select_gift_receiver),
                                color = MaterialTheme.colorScheme.primary,
                                modifier = Modifier
                                    .weight(1f)
                            )
                            Icon(
                                painter = painterResource(id = R.drawable.ic_arrow_right), "",
                                modifier = Modifier
                                    .size(24.dp)
                                    .padding(4.dp), tint = Color.White.copy(0.5f)
                            )
                        }
                    } else {
                        LazyRow(
                            state = listState, modifier = Modifier
                                .fillMaxWidth()
                                .padding(start = 8.dp)
                        ) {
                            items(selectUserList) { u ->
                                ComposeImage(
                                    model = u.avatarUrl, modifier = Modifier
                                        .size(32.dp)
                                        .border(
                                            1.dp, MaterialTheme.colorScheme.primary,
                                            CircleShape
                                        )
                                        .clip(CircleShape)
                                )
                                Spacer(modifier = Modifier.width(5.dp))
                            }
                        }
                    }
                }
            }

            GiftPanelContent(
                giftModel = giftListModel,
                onRecharge = onRecharge,
                onSelectedChange = {
                    onGiftSelected(it)
                    onSelectedChange(it)
                },
                onSend = onSend,
                giftPosition = position,
                onComboDone = onComboFinish
            )
        }
    }
}

@Preview
@Composable
private fun SendGiftPanelPreview() {
    CupidTheme {
        SendGiftPanel(giftListModel = GiftListModel(false, 0, list = buildList {
            repeat(3) { index ->
                GiftTab("name$index", buildList {
                    repeat(10) {
                        add(CPGift(icon = "", name = "gift-${it}", priceType = 1, id = it, price = 10))
                    }
                }.toMutableList(), index, index)
            }
        }))
    }
}

@Stable
class SendGiftPanelState {
    private val statePanelVisible = mutableStateOf(false)
    val giftPanelVisible: Boolean
        get() = statePanelVisible.value

    val stateUserSelectorVisible = mutableStateOf(false)
    val userSelectorVisible: Boolean
        get() = stateUserSelectorVisible.value

    private val _initialGiftId = mutableStateOf(-1)
    val initialGiftId: Int
        get() = _initialGiftId.value

    val selectUserState: MutableState<List<BasicUser>> = mutableStateOf(emptyList())

    fun showPanel(receivers: List<BasicUser> = emptyList(), initialGiftId: Int = -1) {
        selectUserState.value = receivers
        statePanelVisible.value = true
        _initialGiftId.value = initialGiftId
    }

    fun hidePanel() {
        statePanelVisible.value = false
        selectUserState.value = emptyList()
    }
}


/**
 * 这个送礼的panel 仅在家族页面使用
 *
 * @param familyId 家族id
 * @param giftViewModel 礼物VM
 * @param onRecharge 需要充值的回调
 * @return
 */
@Composable
fun rememberSendGiftPanelState(familyId: String, giftViewModel: GiftViewModel, onRecharge: OnClick): SendGiftPanelState {
    val state = remember {
        SendGiftPanelState()
    }
    val selectUsers by state.selectUserState
    if (state.giftPanelVisible) {
        LaunchOnceEffect {
            giftViewModel.fetchGift()
        }
        val giftListModel by giftViewModel.giftListModelState
        AnimatedDialog(onDismiss = {
            state.hidePanel()
            giftViewModel.finishGiftCombo()
            giftViewModel.stopRefreshComboGift()
        }) {
            SendGiftPanel(giftListModel = giftListModel,
                userSelectorVisible = true,
                selectUserList = selectUsers,
                onAddClick = {
                    state.stateUserSelectorVisible.value = true
                }, onSend = { cpGift, i, extra ->
                    val targets = selectUsers.map { it.id }
                    if (targets.isEmpty()) {
                        toastRes(R.string.cupid_select_gift_receiver)
                        return@SendGiftPanel false
                    } else {
                        giftViewModel.sendGiftToGroup(targets = targets, cpGift, i, extra)
                        return@SendGiftPanel true
                    }
                },
                onGiftSelected = { gift ->
                    if (gift?.isLuckyBall == true) {
                        giftViewModel.startRefreshComboGiftInfo()
                    } else {
                        giftViewModel.stopRefreshComboGift()
                    }
                },
                onRecharge = onRecharge,
                position = if (state.initialGiftId == -1) null else GiftPosition(state.initialGiftId),
                onComboFinish = {
                    giftViewModel.finishGiftCombo()
                }
            )
        }
    }
    val userSelectorVM = viewModel<UserSelectorViewModel> {
        UserSelectorViewModel(
            familyId,
            arrayListOf(TribeMemberItem.ROLE_ADMIN, TribeMemberItem.ROLE_OWNER, TribeMemberItem.ROLE_COMMON)
        )
    }
    if (state.userSelectorVisible) {
        LaunchedEffect(key1 = userSelectorVM) {
            userSelectorVM.refresh()
        }
        val memberList = userSelectorVM.memberList
        val searchList = userSelectorVM.searchMemberList
        val searchText by userSelectorVM.searchText
        val list =
            remember(
                searchText,
                memberList.firstOrNull(),
                memberList.lastOrNull(),
                searchList.lastOrNull(),
                searchList.firstOrNull()
            ) {
                if (searchText.isNotEmpty()) {
                    searchList
                } else {
                    memberList
                }.map { it.user }.filter { it.isSelf.not() }.distinctBy { it.id }
            }
        val loadMoreEnable = userSelectorVM.allowLoad && searchText.isEmpty()
        val isSearching by userSelectorVM.isSearching
        FullDialog(onDismissRequest = {
            state.stateUserSelectorVisible.value = false
        }) {
            SelectUserPanel(
                stringResource(R.string.cupid_select_gift_receiver),
                userSelectorVM.isLoaded,
                selectUserList = selectUsers,
                allUsers = list,
                loadMoreEnable = loadMoreEnable,
                onLoadMore = {
                    userSelectorVM.loadMore()
                },
                onSelect = {
                    state.selectUserState.value = it
                    state.stateUserSelectorVisible.value = false
                },
                onBack = {
                    state.stateUserSelectorVisible.value = false
                },
                isSearching = isSearching,
                onSearch = {
                    userSelectorVM.search(it)
                })
        }
    }
    return state
}