package com.qyqy.cupid.ui.relations.family.panels

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.state.LiState
import com.qyqy.ucoo.compose.state.LiStateInfo
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.tribe.bean.TribeMemberItem
import com.qyqy.ucoo.tribe.model.TribeApi
import com.qyqy.ucoo.utils.ComposeState
import kotlinx.coroutines.launch

class UserSelectorViewModel(
    private val familyId: String,
    roles: ArrayList<Int>,
    private val stateInfo: LiStateInfo = LiStateInfo()
) :
    ViewModel(), LiState by stateInfo {

    private val paramsRoles = roles.joinToString(",")
    private val api = createApi<TribeApi>()

    val memberList = mutableStateListOf<TribeMemberItem>()
    val searchMemberList = mutableStateListOf<TribeMemberItem>()
    private val _searchText: MutableState<String> = mutableStateOf("")
    val searchText: ComposeState<String> = _searchText

    private suspend fun load(refresh: Boolean): List<TribeMemberItem> {
        val lastMemberId = when (refresh) {
            true -> "0"
            false -> memberList.lastOrNull()?.member_id?.toString() ?: "0"
        }
        val result = runApiCatching {
            api.getMemberListWithRoles(familyId, paramsRoles, lastMemberId)
        }.getOrNull()?.members ?: emptyList()
        stateInfo.setLoaded(true)
        if (result.isEmpty()) {
            stateInfo.setHasMore(false)
        }
        stateInfo.setRefresh(false)
        stateInfo.setLoading(false)
        return result
    }

    fun refresh() {
        viewModelScope.launch {
            stateInfo.setRefresh(true)
            stateInfo.setHasMore(true)
            val list = load(true)
            memberList.clear()
            memberList.addAll(list)
        }
    }

    fun loadMore() {
        viewModelScope.launch {
            stateInfo.setLoading(true)
            val list = load(false)
            memberList.addAll(list)
        }
    }

    private val _isSearching: MutableState<Boolean> = mutableStateOf(false)
    val isSearching: ComposeState<Boolean> = _isSearching
    fun search(searchContent: String) {
        if (searchContent.isEmpty()) {
            _searchText.value = ""
            searchMemberList.clear()
            return
        }
        viewModelScope.launch {
            _isSearching.value = true
            runApiCatching {
                api.search(familyId, searchContent)
            }.onSuccess {
                searchMemberList.clear()
                searchMemberList.addAll(it.members)
                _searchText.value = searchContent
            }
                .toastError()
            _isSearching.value = false
        }
    }
}