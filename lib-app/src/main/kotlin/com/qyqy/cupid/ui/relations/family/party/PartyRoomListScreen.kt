@file:OptIn(ExperimentalMaterial3Api::class)

package com.qyqy.cupid.ui.relations.family.party

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.global.BannerView
import com.qyqy.cupid.ui.global.ScreenLocation
import com.qyqy.cupid.ui.relations.family.AudioChannelItem
import com.qyqy.cupid.ui.relations.family.MyRoomButton
import com.qyqy.cupid.utils.getVoiceLiveHelper
import com.qyqy.cupid.widgets.click
import com.qyqy.cupid.widgets.state.CupidPullRefreshBox
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.compose.state.EmptyView
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.config.MainFeature
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.core.Analytics

@Composable
fun PartyListScreen(vm: PartyViewModel = viewModel()) {
    val data by vm.state
    val list = data.rooms
    val vh = getVoiceLiveHelper()
    val nav = LocalAppNavController.current
    LaunchedEffect(Unit) {
        vm.refresh()
    }
    val mainFeature by UIConfig.audioRoomFeature.collectAsStateWithLifecycle(MainFeature())
    val features = mainFeature.all
    Column(modifier = Modifier.fillMaxSize()) {
        if (features.isNotEmpty()) {
            LazyVerticalGrid(
                modifier = Modifier
                    .fillMaxWidth(1f)
                    .padding(start = 16.dp, end = 16.dp, top = 8.dp)
                    .heightIn(max = 2000.dp),
                columns = GridCells.Fixed(2),
                verticalArrangement = Arrangement.spacedBy(12.dp),
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                features.forEach { item ->
                    item(span = { GridItemSpan(item.spanCount) }) {
                        ComposeImage(model = item.icon, contentScale = ContentScale.FillBounds,
                            modifier = Modifier
                                .fillMaxWidth(1f)
                                .aspectRatio(if (item.spanCount == 1) 170f / 72 else 343f / 72)
                                .click(noEffect = true) {
                                    nav.navigateByLink(item.jumpLink)
                                }
                        )
                    }
                }
            }
        } else {
            BannerView(
                location = ScreenLocation.ROOM_LIST,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                clickCallback = {
                    Analytics.reportClickEvent(TracePoints.CLICK_AUDIO_ROOM_LIST_BANNER)
                }
            )
        }
        CupidPullRefreshBox(
            isRefreshing = vm.isRefreshing,
            onRefresh = { vm.refresh() },
            modifier = Modifier
                .fillMaxSize()
                .padding(vertical = 8.dp, horizontal = 16.dp)
        ) {
            LazyColumn(modifier = Modifier.fillMaxSize()) {
                if (vm.isLoaded) {
                    if (list.isEmpty()) {
                        item {
                            EmptyView(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .aspectRatio(0.75f)
                            )
                        }
                    } else {
                        items(list) {
                            val avatars = remember(it.users) {
                                it.users.take(
                                    5
                                ).map { u -> u.avatarUrl }
                            }
                            AudioChannelItem(
                                icon = it.owner.avatarUrl,
                                title = it.title,
                                avatars = avatars,
                                peopleCount = it.inuseMicCnt.coerceAtLeast(1),
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .click {
                                        vh.joinVoiceLiveRoom(it.roomId, from = "jp_ar_list_click")
                                    },
                                hasRedPacket = it.hasRedPacket,
                                hasCoinWinnerGame = it.hasOngoingCoinPKGame
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                        }
                        item {
                            Spacer(modifier = Modifier.height(48.dp))
                        }
                    }
                }
            }

            val mod = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 8.dp)

            MyRoomButton(
                from = "jp_ar_list_my_room_click",
                voiceLiveHelper = vh,
                modifier = mod,
                brush = Brush.horizontalGradient(listOf(Color(0xFFC6A3FF), Color(0xFF885EFF))),
                clickCallback = {
//                    Analytics.reportClickEvent(TracePoints.CLICK_AUDIO_ROOM_LIST_BOTTOM_BUTTON)
                }
            )
        }
    }
}

