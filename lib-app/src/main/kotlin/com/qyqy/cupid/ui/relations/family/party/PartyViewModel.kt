package com.qyqy.cupid.ui.relations.family.party

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.state.LiState
import com.qyqy.ucoo.compose.state.LiStateInfo
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.im.bean.VirtualLoverResult
import com.qyqy.ucoo.im.room.ChatRoomApi
import com.qyqy.ucoo.utils.ComposeState
import kotlinx.coroutines.launch

class PartyViewModel(private val liState: LiStateInfo = LiStateInfo()) : ViewModel(), LiState by liState {
    private val api = createApi<ChatRoomApi>()

    private val _state: MutableState<VirtualLoverResult> = mutableStateOf(VirtualLoverResult())
    val state: ComposeState<VirtualLoverResult> = _state

    fun refresh() {
        //刷新banner配置
        UIConfig.refreshPendantConfig()
        viewModelScope.launch {
            if (liState.isProgressing) return@launch
            liState.onStartRefresh()
            runApiCatching { api.getVirtualLoverList() }
                .onSuccess {
                    _state.value = it
                    liState.onLoadComplete(false)
                }.onFailure {
                    liState.onLoadError()
                }.toastError()

        }
    }
}