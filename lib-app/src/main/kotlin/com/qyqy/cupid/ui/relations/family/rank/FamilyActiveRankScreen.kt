package com.qyqy.cupid.ui.relations.family.rank

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.cupid.widgets.ChipTabRow
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.state.LoadMoreView
import com.qyqy.ucoo.compose.state.OnLoadMore
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.tribe.rank.FamilyRankListViewModel
import com.qyqy.ucoo.tribe.rank.GiftRankInfo
import kotlinx.coroutines.launch

@Composable
fun FamilyRankScreen() {
    val dailyVM = viewModel(key = "daily") {
        FamilyRankListViewModel(1)
    }
    val weeklyVM = viewModel(key = "weekly") {
        FamilyRankListViewModel(2)
    }
    FamilyRankContent {
        RankPageState(
            vm = when (it) {
                0 -> dailyVM
                else -> weeklyVM
            }
        )
    }
}

@Composable
fun RankPageState(vm: FamilyRankListViewModel) {
    val list by vm.dataList
    RankPage(list = list, vm.isRefreshing, vm.allowLoad) {
        vm.loadMore()
    }
}


@Composable
private fun FamilyRankContent(pageContent: @Composable (Int) -> Unit) {
    val pagerState = rememberPagerState {
        2
    }
    val scope = rememberCoroutineScope()
    Box(modifier = Modifier.fillMaxSize()) {
        Image(
            painter = painterResource(id = R.drawable.cpd_family_rank_header),
            contentDescription = "header",
            modifier = Modifier.fillMaxWidth()
        )
        HorizontalPager(state = pagerState) { pageIndex ->
            pageContent.invoke(pageIndex)
        }
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .statusBarsPadding()
        ) {
            val backOwner = LocalOnBackPressedDispatcherOwner.current
            Box(
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .height(44.dp),
            ) {
                IconButton(
                    onClick = { backOwner?.onBackPressedDispatcher?.onBackPressed() },
                    modifier = Modifier.align(Alignment.CenterStart)
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_cpd_navigation_back),
                        contentDescription = "back",
                    )
                }
                Text(
                    text = stringResource(id = R.string.cpd_family_rank),
                    color = Color(0xFF1D2129),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.align(Alignment.Center)
                )
            }
            Spacer(modifier = Modifier.height(16.dp))
            Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Center) {
                ChipTabRow(
                    currentItem = pagerState.currentPage, itemCount = pagerState.pageCount, itemWidth = 124,
                    onClick = {
                        scope.launch {
                            pagerState.animateScrollToPage(it)
                        }
                    },
                    drawActiveBg = { offset, size ->
                        drawRoundRect(
                            Color(0xFFFF6720),
                            topLeft = offset,
                            size = size,
                            cornerRadius = CornerRadius(this.size.height / 2f)
                        )
                    },
                    modifier = Modifier
                        .height(36.dp)
                        .background(Color.White, Shapes.chip)
                ) { index, selected ->
                    Text(
                        text = when (index) {
                            0 -> stringResource(id = R.string.cpd_daily_rank)
                            else -> stringResource(id = R.string.cpd_week_rank)
                        }, color = when (selected) {
                            true -> Color.White
                            false -> Color(0xFF1D2129)
                        }
                    )
                }
            }

        }
    }
}

@Composable
fun VerticalItem(data: GiftRankInfo, index: Int) {
    val nav = LocalAppNavController.current
    Column(modifier = Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
        ComposeImage(
            model = data.user.avatarUrl, modifier = Modifier
                .fillMaxWidth(0.61f)
                .aspectRatio(1f)
                .border(2.dp, Color.White, CircleShape)
                .clip(CircleShape)
                .click {
                    nav.navigateToProfile(data.user.id)
                }
        )
        Spacer(modifier = Modifier.height(if (index == 0) 24.dp else 20.dp))
        Text(
            text = data.user.nickname,
            color = Color(0xFF7F360F),
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Text(text = data.contribution.toString(), color = Color(0xFFFFF0BC), fontSize = 12.sp)
            Spacer(modifier = Modifier.width(2.dp))
            Image(
                painter = painterResource(id = R.drawable.cpd_icon_active),
                contentDescription = "active",
                modifier = Modifier.size(12.dp)
            )
        }
    }
}

@Composable
fun RankPage(list: List<GiftRankInfo>, isRefreshing: Boolean = false, allowLoad: Boolean = false, onLoadMore: OnLoadMore = {}) {
    val top1 = list.getOrNull(0)
    val top2 = list.getOrNull(1)
    val top3 = list.getOrNull(2)
    val others = if (list.size > 3) {
        list.subList(3, list.size)
    } else null
    Column(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(1125 / 1074f),
            verticalArrangement = Arrangement.Bottom
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = if (list.size > 3) 10.dp else 1.dp),
                horizontalArrangement = Arrangement.Center
            ) {
                Box(
                    modifier = Modifier
                        .width(105.5.dp)
                        .padding(top = 36.dp)
                ) {
                    if (top2 != null) {
                        VerticalItem(data = top2, index = 1)
                    }
                }
                Box(modifier = Modifier.width(132.dp)) {

                    if (isRefreshing) {
                        CircularProgressIndicator(
                            modifier = Modifier
                                .fillMaxWidth(0.8f)
                                .aspectRatio(1f)
                                .align(Alignment.Center)
                        )
                    }
                    if (top1 != null) {
                        VerticalItem(data = top1, index = 0)
                    }
                }
                Box(
                    modifier = Modifier
                        .width(105.5.dp)
                        .padding(top = 36.dp)
                ) {
                    if (top3 != null) {
                        VerticalItem(data = top3, index = 2)
                    }
                }
            }
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(20.dp)
                    .background(
                        Color.White,
                        RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp)
                    )
            )
        }
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.White)
                .navigationBarsPadding()
        ) {
            if (others != null) {
                itemsIndexed(others) { index, data ->
                    Item(data = data, index = index + 4)
                }
                if (allowLoad) {
                    item {
                        LoadMoreView(onLoadMore = onLoadMore)
                    }
                }
            }
        }
    }
}

@Composable
private fun Item(data: GiftRankInfo, index: Int) {
    val nav = LocalAppNavController.current
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(end = 16.dp, bottom = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = index.toString(),
            color = Color(0xFF1D2129),
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.width(36.dp),
            textAlign = TextAlign.Center
        )
        CircleComposeImage(model = data.user.avatarUrl, modifier = Modifier
            .size(56.dp)
            .click {
                nav.navigateToProfile(data.user.id)
            })
        Text(
            text = data.user.nickname,
            color = Color(0xFF1D2129),
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier
                .padding(horizontal = 8.dp)
                .weight(1f),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
        Text(text = data.contribution.toString(), color = Color(0xFF1D2129), fontSize = 14.sp)
        Image(
            painter = painterResource(id = R.drawable.cpd_icon_active),
            contentDescription = "active",
            modifier = Modifier.size(14.67.dp)
        )
    }
}

@Preview
@Composable
private fun Preview() {
    PreviewCupidTheme {
        val list = buildList<GiftRankInfo> {
            repeat(10) {
                add(GiftRankInfo(userForPreview, 192321, 0))
            }
        }
        FamilyRankContent {
            RankPage(list = list)
        }
    }
}