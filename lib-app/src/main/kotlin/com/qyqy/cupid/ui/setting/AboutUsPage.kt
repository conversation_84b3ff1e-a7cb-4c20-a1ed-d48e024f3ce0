package com.qyqy.cupid.ui.setting

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.qyqy.cupid.config.CupidConst
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.BuildConfig
import com.qyqy.ucoo.R
import com.qyqy.ucoo.asFragmentActivity
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.core.upgrade.UpgradeEntityImpl
import com.qyqy.ucoo.core.upgrade.UpgradeManager
import com.qyqy.ucoo.isPlayChannel
import com.qyqy.ucoo.setting.DebugActivity
import com.qyqy.ucoo.setting.SettingsRepository
import com.qyqy.ucoo.utils.web.addTitleByUrl
import com.qyqy.ucoo.utils.web.cacheEnableByUrl
import kotlinx.coroutines.flow.mapNotNull

@Composable
fun AboutUsPage() {

    val context = LocalContext.current

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF5F7F9)),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        CupidAppBar(title = stringResource(id = R.string.cpd关于我们))

        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.fillMaxWidth()
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_cpd_launcher),
                contentDescription = null,
                modifier = Modifier
                    .padding(vertical = 40.dp)
                    .size(80.dp)
                    .clip(Shapes.corner12)
                    .click(time = 0) {
                        DebugActivity.clickIntoDebug(context)
                    }
            )


            Column(
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .background(color = Color.White)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(72.dp)
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(stringResource(id = R.string.cpd_current_version), style = TextStyle(fontSize = 16.sp, color = Color(0xFF1D2129)))
                    Spacer(modifier = Modifier.weight(1f))
                    Text(
                        text = BuildConfig.VERSION_NAME,
                        style = TextStyle(fontSize = 12.sp, color = Color(0xFF86909C))
                    )
                }

                @SuppressLint("StateFlowValueCalledInComposition")
                val appSettings by remember {
                    SettingsRepository.sSettingsFlow.mapNotNull {
                        it.getOrNull()
                    }
                }.collectAsStateWithLifecycle(SettingsRepository.sSettingsFlow.value.getOrNull())

                appSettings?.also {
                    Spacer(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(0.5.dp)
                            .background(color = Color(0xFFF0F0F0))
                    )

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(72.dp)
                            .clickable(enabled = it.hasNewVersion) {
                                val activity = context.asFragmentActivity!!
                                if (isPlayChannel) {
                                    activity.navigateToMarket("com.android.vending")
                                } else {
                                    UpgradeManager.showUpgradeDialog(activity, UpgradeEntityImpl.newUpgradeEntity(it))
                                }
                            }
                            .padding(horizontal = 16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(stringResource(R.string.cpd_new_version_detect), style = TextStyle(fontSize = 16.sp, color = Color(0xFF1D2129)))
                        Spacer(modifier = Modifier.weight(1f))
                        Text(
                            text = if (it.hasNewVersion) stringResource(R.string.cpd_has_new_version)
                            else stringResource(R.string.cpd_no_new_version),
                            style = TextStyle(fontSize = 12.sp, color = Color(0xFF86909C))
                        )
                        if (it.hasNewVersion) {
                            Spacer(
                                modifier = Modifier
                                    .offset(y = (-8).dp)
                                    .size(5.dp)
                                    .background(colorResource(id = R.color.red_100), CircleShape)
                            )
                        }
                    }
                }
            }
        }

        Spacer(modifier = Modifier.weight(1f))

        val annotatedText = buildAnnotatedString {
            pushStringAnnotation(tag = "用户服务协议", annotation = "")
            withStyle(style = SpanStyle(color = Color(0xFF57A9FB))) {
                append(stringResource(id = R.string.cpd_用户服务协议_))
            }
            pop()

            append(stringResource(id = R.string.cpd和))

            pushStringAnnotation(tag = "隐私协议", annotation = "")
            withStyle(style = SpanStyle(color = Color(0xFF57A9FB))) {
                append(stringResource(id = R.string.cpd_隐私协议_))
            }
            pop()
        }

        val navController = LocalAppNavController.current

        ClickableText(
            text = annotatedText,
            modifier = Modifier.padding(bottom = 10.dp),
            style = TextStyle(fontSize = 14.sp, color = Color(0xFF86909C)),
        ) { offset ->

            annotatedText.getStringAnnotations(
                tag = "用户服务协议",
                start = offset,
                end = offset
            ).firstOrNull()?.also { _ ->
                navController.navigateWeb(CupidConst.URL.USER_SERVICE.addTitleByUrl(context.getString(R.string.cpd用户服务协议)).cacheEnableByUrl(false))
            }

            annotatedText.getStringAnnotations(
                tag = "隐私协议",
                start = offset,
                end = offset
            ).firstOrNull()?.also { _ ->
                navController.navigateWeb(CupidConst.URL.PRIVACY_POLICY.addTitleByUrl(context.getString(R.string.cpd隐私协议)).cacheEnableByUrl(false))
            }
        }

        Text(
            text = stringResource(id = R.string.cpd_copy_right),
            modifier = Modifier.padding(bottom = 48.dp),
            style = TextStyle(fontSize = 14.sp, color = Color(0xFF86909C)),
        )
    }
}

private fun Activity.navigateToMarket(marketPkg: String, appPkg: String = packageName) {
    runCatching {
        val uri = Uri.parse("market://details?id=$appPkg")
        val intent = Intent(Intent.ACTION_VIEW)
        intent.data = uri
        intent.setPackage(marketPkg)
        startActivity(intent)
    }
}


@Composable
@Preview(showBackground = true, locale = "ja-jp")
fun AboutUsPagePreView() {
    PreviewCupidTheme {
        AboutUsPage()
    }
}