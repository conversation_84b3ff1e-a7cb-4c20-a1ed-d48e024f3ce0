@file:OptIn(ExperimentalMaterial3Api::class)

package com.qyqy.cupid.ui.setting

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.material3.contentColorFor
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.model.PhrasesViewModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.theme.CupidTheme
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.FilterMaxLength
import com.qyqy.cupid.widgets.composeClick
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.http.runApiCatching
import kotlinx.coroutines.launch

@Composable
fun AddPhrasesPage() {
    val controller = LocalAppNavController.current
    val loadingFlag = LocalContentLoading.current
    val viewModel = viewModel<PhrasesViewModel>()
    val scope = rememberCoroutineScope()

    AddPhrasesPageContent { content ->
        scope.launch {
            loadingFlag.value = true
            viewModel.addPhrases(content).onSuccess {
                loadingFlag.value = false
                controller.popBackStack()
            }.onFailure {
                loadingFlag.value = false
            }.toastError()
        }
    }
}

@Composable
private fun AddPhrasesPageContent(onAction: (value: String) -> Unit = {}) {
    val textValue = remember { FilterMaxLength(maxLength = 200) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(color = Color.White),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        CupidAppBar(title = stringResource(id = R.string.cpd_phrases_new))
        HorizontalDivider(color = CpdColors.FFF1F2F3, thickness = 0.5.dp)
        Column(modifier = Modifier.weight(1f)) {
            Box(
                modifier = Modifier
                    .padding(16.dp)
                    .background(color = CpdColors.FFFAFAFA, shape = RoundedCornerShape(12.dp))
                    .height(228.dp)
            ) {
                TextField(
                    value = textValue.getInputValue(),
                    modifier = Modifier
                        .fillMaxSize(),
                    placeholder = {
                        Text(
                            text = stringResource(id = R.string.cpd_phrases_add_placeholder),
                            style = TextStyle(color = Color(0xFF86909C), fontSize = 14.sp)
                        )
                    },
                    onValueChange = textValue.onValueChange(),
                    colors = TextFieldDefaults.textFieldColors(
                        containerColor = CpdColors.FFFAFAFA,
                        disabledTextColor = Color.Transparent,
                        focusedIndicatorColor = Color.Transparent,
                        unfocusedIndicatorColor = Color.Transparent,
                        disabledIndicatorColor = Color.Transparent,
                        cursorColor = Color(0xFFFFCF40)
                    ),
                )
                Text(
                    "${textValue.getLength()}/${textValue.getMaxLength()}",
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(12.dp),
                    style = TextStyle(fontSize = 14.sp, color = Color(0xFF86909C))
                )
            }
            Text(
                stringResource(id = R.string.cpd_phrases_add_tips),
                modifier = Modifier.padding(horizontal = 16.dp),
                fontSize = 12.sp,
                color = CpdColors.FF86909C,
            )
        }
        AppButton(
            text = buildAnnotatedString {
                append(stringResource(id = R.string.cpd_confirm))
            },
            onClick = composeClick {
                onAction(textValue.text)
            },
            colors = ButtonColors(
                containerColor = MaterialTheme.colorScheme.primary,
                contentColor = MaterialTheme.colorScheme.contentColorFor(MaterialTheme.colorScheme.primary),
                disabledContainerColor = MaterialTheme.colorScheme.onPrimary,
                disabledContentColor = MaterialTheme.colorScheme.contentColorFor(MaterialTheme.colorScheme.onPrimary),
            ),
            enabled = textValue.text.length > 0,
            textStyle = TextStyle(color = Color.White, fontSize = 14.sp),
            contentPadding = PaddingValues(horizontal = 12.dp),
            modifier = Modifier
                .padding(vertical = 20.dp, horizontal = 25.dp)
                .navigationBarsPadding()
                .fillMaxWidth()
                .height(44.dp)

        )
    }
}

@Preview
@Composable
private fun AddPhrasesPagePreview() {
    CupidTheme {
        AddPhrasesPageContent()
    }
}