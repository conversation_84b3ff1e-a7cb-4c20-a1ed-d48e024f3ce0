package com.qyqy.cupid.ui.setting

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandIn
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.model.FeedbackViewModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.FilterMaxLength
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.bean.FeedbackQABean
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.overScrollVertical
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.user.UserRepository
import kotlinx.coroutines.launch
import okio.ByteString.Companion.encodeUtf8

private data class UploadFeedback(val value: String)

@Composable
fun FeedbackPage() {
    val scope = rememberCoroutineScope()
    val loading = LocalContentLoading.current
    val navController = LocalAppNavController.current

    val viewModel: FeedbackViewModel = viewModel()
    val qalist = viewModel.feedbackqaList.collectAsStateWithLifecycle()

    LaunchedEffect(key1 = Unit) {
        viewModel.getFeedbackQA()
    }

    FeedbackPage(qalist.value) {
        when (it) {
            is UploadFeedback -> {
                scope.launch {
                    if (it.value.isBlank()) {
                        return@launch
                    }
                    loading.value = true
                    viewModel.updateFeedback(it.value)
                        .apply {
                            loading.value = false
                        }
                        .onSuccess {
                            navController.popBackStack()
                        }.toastError()
                }
            }

            else -> {}
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun FeedbackPage(suggests: List<FeedbackQABean> = listOf(), onAction: (Any) -> Unit = {}) {
    val textValue = remember { FilterMaxLength(maxLength = 500) }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .background(color = Color(0xFFFAFAFA))
    ) {
        item {
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                CupidAppBar(title = stringResource(id = R.string.cupid_feedback_and_suggestion))
                Spacer(modifier = Modifier.height(24.dp))
                Box(
                    modifier = Modifier
                        .padding(horizontal = 16.dp)
                        .background(color = Color.White, shape = RoundedCornerShape(12.dp))
                        .height(228.dp)
                ) {
                    TextField(
                        value = textValue.getInputValue(),
                        modifier = Modifier
                            .fillMaxSize(),
                        placeholder = {
                            Text(
                                text = stringResource(R.string.cupid_feedback_placeholder_text),
                                style = TextStyle(color = Color(0xFF86909C), fontSize = 14.sp)
                            )
                        },
                        onValueChange = textValue.onValueChange(),
                        colors = TextFieldDefaults.textFieldColors(
                            containerColor = Color.White,
                            disabledTextColor = Color.Transparent,
                            focusedIndicatorColor = Color.Transparent,
                            unfocusedIndicatorColor = Color.Transparent,
                            disabledIndicatorColor = Color.Transparent,
                            cursorColor = Color(0xFFFFCF40)
                        ),
                    )
                    Text(
                        "${textValue.getLength()}/${textValue.getMaxLength()}",
                        modifier = Modifier
                            .align(Alignment.BottomEnd)
                            .padding(12.dp),
                        style = TextStyle(fontSize = 14.sp, color = Color(0xFF86909C))
                    )
                }
                Spacer(modifier = Modifier.height(40.dp))
                AppButton(
                    text = stringResource(id = R.string.cupid_post_content),
                    modifier = Modifier
                        .padding(bottom = 20.dp)
                        .size(311.dp, 48.dp)
                        .padding(bottom = 8.dp),
                    color = Color.White,
                    background = Color(0xFFFF5E8B),
                    enabled = textValue.text.length > 0,
                    textStyle = LocalTextStyle.current.copy(fontSize = 16.sp, fontWeight = FontWeight.Medium),
                ) {
                    onAction(UploadFeedback(textValue.text))
                }
            }
        }
        item {
            if (suggests.isNotEmpty()) {
                Text(
                    stringResource(id = R.string.cpd热门问题),
                    color = CpdColors.FF1D2129,
                    fontSize = 16.sp,
                    lineHeight = 16.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(vertical = 10.dp, horizontal = 16.dp)
                )
            }
        }
        itemsIndexed(suggests) { index, item ->

            var isExpanded by rememberSaveable {
                mutableStateOf(false)
            }

            Column {
                Row(
                    modifier = Modifier.click(noEffect = true, time = 500) {
                        isExpanded = !isExpanded
                    },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        item.question, modifier = Modifier
                            .padding(horizontal = 16.dp, vertical = 10.dp)
                            .weight(1f), color = CpdColors.FF1D2129,
                        fontSize = 16.sp, lineHeight = 16.sp
                    )
                    ComposeImage(
                        model = R.drawable.ic_cpd_arrow_right,
                        modifier = Modifier
                            .padding(end = 16.dp)
                            .rotate(if (isExpanded) 90f else 0f)
                    )
                }

                if (isExpanded) {
                    Text(
                        item.answer,
                        color = CpdColors.FF4E5969,
                        fontSize = 16.sp,
                        lineHeight = 24.sp,
                        modifier = Modifier
                            .padding(horizontal = 16.dp)
                            .padding(bottom = 10.dp)
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewFeedbackPage() {
    FeedbackPage(listOf(FeedbackQABean("内容\\n哈哈哈哈", "标题1"),
        FeedbackQABean("内容\\n哈哈哈哈", "标题2"))) {}
}
