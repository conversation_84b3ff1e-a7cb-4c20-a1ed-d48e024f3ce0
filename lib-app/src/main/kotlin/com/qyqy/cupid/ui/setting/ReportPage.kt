@file:OptIn(ExperimentalFoundationApi::class)

package com.qyqy.cupid.ui.setting

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.model.ReportViewModel
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.widgets.AppTextFieldValue
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppPhotoPreviewer
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.IconLoading
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.photo.IImageSource
import com.qyqy.ucoo.compose.ui.photo.PhotoPreviewModel
import com.qyqy.ucoo.compose.ui.photo.rememberPhotoGestureState
import com.qyqy.ucoo.compose.ui.photo.rememberPhotoPreviewState
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.utils.Option
import com.qyqy.ucoo.utils.TypeMediaFilter
import com.qyqy.ucoo.utils.rememberRequestAlbumPermissionHelper
import io.github.album.MediaData
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlin.math.abs

/**
 *  @time 2024/7/18
 *  <AUTHOR>
 *  @package com.qyqy.keya.ui.settings
 */
interface IReportAction {
    data class RemovePicture(val index: Int) : IReportAction
    data class PreviewImage(val index: Int, val item: MediaData) : IReportAction
    data class AddPicture(val mediaResources: List<MediaData>) : IReportAction
    data class SubmitReport(val textContent: String) : IReportAction
    data class ChangeCategory(val category: Int) : IReportAction
}

@Composable
fun ReportPage(category: Int, type: Int, id: String) {
    val innerModel = viewModel(modelClass = ReportViewModel::class.java)
    val scope = rememberCoroutineScope()
    val controller = LocalAppNavController.current

    //region 初始化相册权限

    var previewList by rememberSaveable {
        mutableStateOf(emptyList<PhotoPreviewModel>())
    }
    val gestureState = with(LocalDensity.current) {
        rememberPhotoGestureState(
            extraHorizontalOffset = 80.dp.toPx(),
            extraVerticalOffset = 100.dp.toPx(),
        )
    }

    val previewState = rememberPhotoPreviewState(
        gestureState = gestureState,
        onGestureClose = { _, offsetY, velocityY ->
            abs(velocityY) > 1000 || abs(offsetY) > 60.dp.toPx()
        }
    ) {
        previewList
    }
    //endregion

    //region events

    LaunchedEffect(key1 = Unit) {
        innerModel.uploadResult.collectLatest {
            if (it) {
                controller.popBackStack(CupidRouters.REPORT_START, true)
            }
        }
    }

    //endregion

    innerModel.setReportTemplate(category = category, id = id, type = type)

    Box(modifier = Modifier.background(color = Color.White)) {
        val selectMediaList = innerModel.mediaResources.collectAsStateWithLifecycle()

        Content(selectMediaList.value, category) { action ->
            when (action) {
                is IReportAction.AddPicture -> {
                    innerModel.updateMediaResources(action.mediaResources, true)
                }

                is IReportAction.RemovePicture -> {
                    innerModel.removeMediaResources(action.index)
                }

                is IReportAction.SubmitReport -> {
                    innerModel.submitReport(action.textContent)
                }

                is IReportAction.ChangeCategory -> {
                }

                is IReportAction.PreviewImage -> {
                    scope.launch {
                        previewList =
                            selectMediaList.value.mapIndexed { idx, it -> PhotoPreviewModel(it.path, -1f, IImageSource.Path(it.path), IImageSource.Path(it.path), isVideo = it.isVideo) }.toList()

                        if (action.index >= 0) {
                            previewState.startPreview(index = action.index)
                        }
                    }
                }

                else -> {}
            }
        }
        AppPhotoPreviewer(previewState = previewState)

        val loadingState = innerModel.uploading.collectAsStateWithLifecycle()
        if (loadingState.value) {
            Dialog(onDismissRequest = {
                innerModel.cancelUpload()
            }) {
                IconLoading()
            }
        }
    }
}

@Composable
private fun Content(list: List<MediaData>, category: Int, onAction: (IReportAction) -> Unit = {}) {
    val textValue = remember {
        AppTextFieldValue("", maxLength = 200)
    }

    val launcher = rememberRequestAlbumPermissionHelper(context = LocalContext.current, selectedLimit = 9, needCrop = false) {
        if (it != null) {
            onAction(IReportAction.AddPicture(it.list))
        }
    }

    Column(
        modifier = Modifier
            .background(color = Color.White)
            .fillMaxSize()
    ) {
        CupidAppBar(title = stringResource(id = R.string.cpd举报))
        Box(modifier = Modifier.fillMaxSize()) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
            ) {

                Text(
                    text = stringResource(id = R.string.cpd_title_report_desc),
                    style = MaterialTheme.typography.titleSmall,
                    color = colorResource(id = R.color.FF86909C),
                    modifier = Modifier.padding(start = 16.dp)
                )
                Box(
                    modifier = Modifier
                        .padding(16.dp)
                        .background(
                            color = colorResource(id = R.color.FFFAFAFA), RoundedCornerShape(8.dp)
                        )
                ) {
                    TextField(
                        value = textValue.textFieldValue,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(140.dp),
                        placeholder = {
                            Text(
                                text = stringResource(id = R.string.cpd_hint_edit_report),
                                style = TextStyle(color = colorResource(id = R.color.FF86909C), fontSize = 14.sp)
                            )
                        },
                        onValueChange = textValue.onValueChange,
                        colors = TextFieldDefaults.colors(
                            disabledTextColor = Color.Transparent,
                            focusedIndicatorColor = Color.Transparent,
                            unfocusedIndicatorColor = Color.Transparent,
                            disabledIndicatorColor = Color.Transparent,
                            unfocusedContainerColor = Color.Transparent,
                            focusedContainerColor = Color.Transparent,
                            cursorColor = Color(0xFFFFCF40),
                        ),
                    )
                    Text(
                        "${textValue.text.length}/${textValue.maxLength}",
                        modifier = Modifier
                            .align(Alignment.BottomEnd)
                            .padding(12.dp),
                        style = MaterialTheme.typography.labelSmall, color = colorResource(id = R.color.FF86909C)
                    )
                }
                Text(
                    text = stringResource(id = R.string.cpd_report_file),
                    style = MaterialTheme.typography.titleSmall,
                    color = colorResource(id = R.color.FF86909C),
                    modifier = Modifier.padding(start = 16.dp)
                )

                PictureGrid(
                    list,
                    9, {
                        onAction(IReportAction.RemovePicture(it))
                    }, { idx, item ->
                        onAction(IReportAction.PreviewImage(idx, item))
                    }, { _, _ ->
                        launcher.start {
                            setSelectedList(list)
                            setAllString(Option.ALL.text)
                            setFilter(TypeMediaFilter(Option.ALL))
                        }
                    }
                )
                Text(
                    text = stringResource(id = R.string.cpd_report_file_desc),
                    style = MaterialTheme.typography.titleSmall,
                    color = colorResource(id = R.color.FF86909C),
                    modifier = Modifier.padding(start = 16.dp)
                )
                Spacer(modifier = Modifier.height(156.dp))
            }
            AppButton(
                text = stringResource(id = R.string.cpd_report_submit),
                modifier = Modifier
                    .padding(bottom = 24.dp)
                    .size(327.dp, 44.dp)
                    .align(Alignment.BottomCenter),
                color = Color.White,
                background = Color(0xFFFF5E8B),
                textStyle = LocalTextStyle.current.copy(fontSize = 16.sp, fontWeight = FontWeight.Medium),
            ) {
                onAction(IReportAction.SubmitReport(textValue.text))
            }

        }
    }

}


@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun PictureGrid(
    data: List<MediaData>,
    maxSelect: Int = 9,
    removePic: (idx: Int) -> Unit = {},
    onPreview: (idx: Int, item: MediaData) -> Unit = { _, _ -> },
    onChooseMedia: (type: Option, count: Int) -> Unit = { _, _ -> }
) {
    FlowRow(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 20.dp),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        data.forEachIndexed { index, item ->
            Box(
                modifier = Modifier
                    .size(88.dp)
                    .noEffectClickable {
                        onPreview(index, item)
                    },
            ) {
                ComposeImage(
                    model = item.uri,
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .matchParentSize()
                        .clip(RoundedCornerShape(8.dp))
                )
                if (item.isVideo) {
                    ComposeImage(
                        model = R.drawable.ic_video_pause, modifier = Modifier
                            .size(32.dp)
                            .align(Alignment.Center)
                    )
                }

                ComposeImage(model = R.drawable.ic_delete, modifier = Modifier
                    .padding(4.dp)
                    .align(Alignment.TopEnd)
                    .click {
                        removePic(index)
                    })
            }
        }
        if (data.size < maxSelect) {
            Box(
                modifier = Modifier
                    .size(88.dp)
                    .background(MaterialTheme.colorScheme.background, shape = RoundedCornerShape(8.dp))
                    .noEffectClickable {
                        onChooseMedia(Option.ALL, maxSelect)
                    },
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_cpd_plus),
                    contentDescription = "添加",
                    modifier = Modifier.size(40.dp)
                )
            }
        }
    }
}

@Composable
@Preview(showBackground = true)
private fun ReportPreView() {
    Content(emptyList(), 2)
}