

package com.qyqy.cupid.ui.setting

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.setting.ReportItem

/**
 *  @time 2024/7/30
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.setting
 *
 */

const val TYPE_USER = 1
const val TYPE_AUDIOROOM = 2
const val TYPE_PRIVATE_USER = 3
const val TYPE_MOMENT = 4

@Composable
fun ReportStartPage(type: Int = -1, id: String = "") {
    val context = LocalContext.current
    val controller = LocalAppNavController.current
    val reportItemList = remember {
        arrayOf(
            ReportItem(1, context.getString(R.string.cpd_polict_relations)),
            ReportItem(2, context.getString(R.string.cpd_low_sex)),
            ReportItem(3, context.getString(R.string.cpd_voliet)),
            ReportItem(4, context.getString(R.string.cpd_ads_make)),
            ReportItem(5, context.getString(R.string.cpd_report_trick)),
            ReportItem(6, context.getString(R.string.cpd_report_dirty_lang)),
            ReportItem(7, context.getString(R.string.cpd儿童安全相关)),
            ReportItem(0, context.getString(R.string.cpd_others))
        )
    }

    Column(modifier = Modifier.fillMaxSize()) {
        CupidAppBar(title = stringResource(id = R.string.cpd举报))
        reportItemList.forEach { reportItem ->
            Column {
                Row(
                    modifier = Modifier
                        .height(56.dp)
                        .click {
                            controller.navigate(
                                CupidRouters.REPORT,
                                mapOf(
                                    "category" to reportItem.type,
                                    "type" to type,
                                    "id" to id
                                ),
                            )
                        }
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = reportItem.desc, modifier = Modifier.weight(1f),
                        style = MaterialTheme.typography.titleMedium.copy(
                            color = colorResource(id = R.color.FF1D2129)
                        )
                    )
                    ComposeImage(model = R.drawable.ic_cpd_arrow_right)
                }
                HorizontalDivider(thickness = 0.5.dp, color = colorResource(id = R.color.FFF0F0F0))
            }
        }
    }
}

@Composable
@Preview
private fun ReportStartPreView() {
    ReportStartPage(1, "")
}