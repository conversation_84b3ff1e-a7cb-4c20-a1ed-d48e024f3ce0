package com.qyqy.cupid.ui.setting

import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.dialog.EnsureContent
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.BuildConfig
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.app
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.VisibleStateDialog
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.login.LoginReason
import com.qyqy.ucoo.self
import com.qyqy.ucoo.widget.Switch
import com.qyqy.ucoo.widget.SyncSwitchButton
import kotlinx.coroutines.launch

/**
 *  @time 2024/7/11
 *  <AUTHOR>
 *  @package com.qyqy.keya.ui.settings
 */
@Composable
fun SettingsPage(appNotifyEnable: Boolean = UIConfig.userConf.inAppNotifyEnabled) {
    val navController = LocalAppNavController.current
    val onAction: (SettingsData) -> Unit = remember {
        {
            when (it.id) {
                1 -> {
                    navController.navigate(CupidRouters.BLACK_LIST)
                }

                2 -> {
                    navController.navigate(CupidRouters.FEEDBACK)
                }

                3 -> {
                    navController.navigate(CupidRouters.ABOUT)
                }

                4 -> {
                    navController.navigate(CupidRouters.CHAT_PHRASES)
                }

                else -> {}
            }
        }
    }

    val visibleState = rememberSaveable {
        mutableStateOf(false)
    }

    DestroyAccountDialog(visibleState)
    val items = remember {
        buildList {
            add(SettingsData(0, R.string.cpd应用内通知))
            if (self.isBoy) {
                add(SettingsData(12, R.string.cpd_conv_cleaner))
            }
            if (UIConfig.userConf.showAgentHiSetting) {
                add(SettingsData(11, R.string.cpd_auto_match, R.string.cpd_auto_match_desc))
            }
            if (UIConfig.userConf.showPrologueSetting) {
                add(SettingsData(4, R.string.cpd_phrases_title))
            }
            add(SettingsData(5, R.string.cpd隐身访问, R.string.cpd隐身开启后, needVip = true))
            add(SettingsData(6, R.string.cpd隐藏守护, R.string.cpd隐藏守护开启后, needVip = true))
            add(SettingsData(1, R.string.cpd_black_list))
            add(SettingsData(2, R.string.cupid_feedback_and_suggestion))
            add(SettingsData(3, R.string.cpd关于我们))
        }
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .navigationBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        CupidAppBar(title = stringResource(id = R.string.cpd_setting))

        LazyColumn(
            modifier = Modifier.weight(1f)
        ) {
            if (BuildConfig.DEBUG) {
                item {
                    val nav = LocalAppNavController.current
                    Text(
                        text = "Debug 用", modifier = Modifier
                            .fillMaxWidth()
                            .click {
                                nav.navigate(CupidRouters.PAGE_TEST)
                            }
                            .padding(16.dp))
                }
            }
            itemsIndexed(items) { index, item ->
                when (item.id) {
                    0 -> {
                        SettingsItem(item) {
                            val switchState =
                                remember { mutableStateOf(Switch.valueOf(appNotifyEnable)) }
                            SyncSwitchButton(
                                switchState = switchState,
                                onSync = {
                                    UIConfig.excuteUpdateAppNotifyEnabled(it.value)
                                    true
                                },
                                width = 40.dp,
                                height = 20.dp,
                                uncheckedTrackColor = Color(0xFFCCCCCC),
                                checkedTrackColor = Color(0xFFFF5E8B),
                            )
                        }
                    }

                    5 -> {
                        SettingsItem(item) {
                            val conf by UIConfig.configFlow.collectAsStateWithLifecycle()
                            val switchState = remember(conf) {
                                mutableStateOf(Switch.valueOf(conf.isSuccess && conf.get().invisibleAccessEnabled))
                            }
                            SyncSwitchButton(
                                switchState = switchState,
                                onSync = { switch ->
                                    UIConfig.updateUserSettings("invisible_access_enabled", switch.value.toString()) {
                                        it.copy(invisibleAccessEnabled = switch.value)
                                    }.toastError().isSuccess
                                },
                                width = 40.dp,
                                height = 20.dp,
                                uncheckedTrackColor = Color(0xFFCCCCCC),
                                checkedTrackColor = Color(0xFFFF5E8B),
                            )
                        }
                    }

                    6 -> {
                        SettingsItem(item) {
                            val conf by UIConfig.configFlow.collectAsStateWithLifecycle()
                            val switchState = remember(conf) {
                                mutableStateOf(Switch.valueOf(conf.isSuccess && conf.get().hideGuardianEnabled))
                            }
                            SyncSwitchButton(
                                switchState = switchState,
                                onSync = { switch ->
                                    UIConfig.updateUserSettings("hide_guardian_enabled", switch.value.toString()) {
                                        it.copy(hideGuardianEnabled = switch.value)
                                    }.toastError().isSuccess
                                },
                                width = 40.dp,
                                height = 20.dp,
                                uncheckedTrackColor = Color(0xFFCCCCCC),
                                checkedTrackColor = Color(0xFFFF5E8B),
                            )
                        }
                    }

                    12 -> {
                        SettingsItem(item) {
                            val conf by UIConfig.configFlow.collectAsStateWithLifecycle()
                            val switchState = remember(conf) {
                                mutableStateOf(Switch.valueOf(conf.isSuccess && conf.get().autoCleanConversation))
                            }
                            SyncSwitchButton(
                                switchState = switchState,
                                onSync = {
                                    UIConfig.updateConversationCleanerState(it.value)
                                        .toastError().isSuccess
                                },
                                width = 40.dp,
                                height = 20.dp,
                                uncheckedTrackColor = Color(0xFFCCCCCC),
                                checkedTrackColor = Color(0xFFFF5E8B),
                            )
                        }
                    }

                    11 -> {
                        ItemAutoMatch(item)
                    }

                    else -> {
                        SettingsItem(item, Modifier.clickable {
                            onAction(item)
                        }) {
                            Icon(
                                Icons.AutoMirrored.Filled.KeyboardArrowRight,
                                contentDescription = "",
                                tint = Color(0xFF86909C)
                            )
                        }
                    }
                }
                HorizontalDivider(thickness = 0.5.dp, color = Color(0xFFF0F0F0))
            }
        }

        val scope = rememberCoroutineScope()

        AppButton(
            text = stringResource(id = R.string.cpd_logout),
            modifier = Modifier
                .size(311.dp, 48.dp)
                .padding(bottom = 8.dp),
            color = Color.White,
            background = Color(0xFFFF5E8B),
            textStyle = LocalTextStyle.current.copy(
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            ),
        ) {
            scope.launch {
                app.accountManager.logout(LoginReason.Exit)
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        AppButton(
            text = stringResource(id = R.string.cpd_destroy_account),
            modifier = Modifier
                .size(311.dp, 48.dp)
                .padding(bottom = 8.dp),
            color = Color(0xFF4E5969),
            background = Color(0xFFF1F2F3),
            textStyle = LocalTextStyle.current.copy(
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            ),
        ) {
            visibleState.value = true
        }

        Spacer(modifier = Modifier.height(15.dp))
    }
}

@Composable
private fun ItemAutoMatch(item: SettingsData) {
    val switchState = remember {
        mutableStateOf(Switch.valueOf(UIConfig.userConf.isAgentHiEnable))
    }
    val config by UIConfig.configFlow.collectAsStateWithLifecycle()
    switchState.value = Switch.valueOf(config.getOrNull()?.isAgentHiEnable ?: true)
    SettingsItem(item = item) {
        SyncSwitchButton(
            switchState = switchState,
            onSync = {
                UIConfig.updateAgentHi().isSuccess
            },
            width = 40.dp,
            height = 20.dp,
            uncheckedTrackColor = Color(0xFFCCCCCC),
            checkedTrackColor = Color(0xFFFF5E8B),
        )
    }
}

@Composable
private fun DestroyAccountDialog(visibleState: MutableState<Boolean>) {
    VisibleStateDialog(visibleState = visibleState) {
        val scope = rememberCoroutineScope()
        val loading = LocalContentLoading.current
        EnsureContent(
            title = stringResource(id = R.string.cpd_destroy_account),
            content = stringResource(id = R.string.cpd_tip_destroy_account),
            leftButtonText = stringResource(id = R.string.cpd_destroy_account_ok),
            rightButtonText = stringResource(id = R.string.cpd取消),
            onLeftClick = {
                scope.launch {
                    try {
                        loading.value = true
                        app.accountManager.logOff()
                    } finally {
                        loading.value = false
                        visibleState.value = false
                    }
                }
            },
            onRightClick = { visibleState.value = false }
        )
    }
}

private data class SettingsData(val id: Int, val titleId: Int, @StringRes val descResId: Int = -1, val needVip: Boolean = false)

@Composable
private fun SettingsItem(
    item: SettingsData,
    modifier: Modifier = Modifier,
    onContent: @Composable () -> Unit = {}
) {
    Row(
        modifier = Modifier
            .then(modifier)
            .padding(vertical = 20.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.width(16.dp))
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(end = 10.dp)
        ) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(id = item.titleId),
                    color = Color(0xFF1D2129),
                    fontSize = 16.sp,
                    lineHeight = 16.sp
                )
                if (item.needVip) {
                    Row(
                        modifier = Modifier
                            .height(20.dp)
                            .background(
                                brush = Brush.horizontalGradient(
                                    listOf(
                                        Color(0xFFF2DBB5),
                                        Color(0xFFEDB664)
                                    )
                                ),
                                shape = RoundedCornerShape(4.dp)
                            )
                            .padding(horizontal = 4.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(2.dp)
                    ) {
                        Image(
                            painter = painterResource(R.drawable.ic_cpd_mine_vip),
                            modifier = Modifier.size(16.dp),
                            contentDescription = null
                        )

                        Text(
                            text = stringResource(R.string.cpd_vip_特权),
                            fontSize = 12.sp,
                            lineHeight = 12.sp,
                            color = Color(0xFF593A0C),
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
            if (item.descResId != -1) {
                Spacer(modifier = Modifier.height(6.dp))
                Text(
                    text = stringResource(id = item.descResId),
                    color = Color(0xFF86909C),
                    fontSize = 12.sp,
                    lineHeight = 18.sp
                )
            }
        }
        onContent()
        Spacer(modifier = Modifier.width(16.dp))
    }
}

@Composable
@Preview(showBackground = true)
private fun FeedbackPreview() {
    PreviewCupidTheme {
        SettingsPage(appNotifyEnable = false)
    }
}

@Preview
@Composable
private fun Preview() {
    PreviewCupidTheme {
        EnsureContent(
            title = stringResource(id = R.string.cpd_destroy_account),
            content = stringResource(id = R.string.cpd_tip_destroy_account),
            leftButtonText = stringResource(id = R.string.cpd_destroy_account_ok),
            rightButtonText = stringResource(id = R.string.cpd取消),
            onLeftClick = {

            },
            onRightClick = { }
        )
    }
}