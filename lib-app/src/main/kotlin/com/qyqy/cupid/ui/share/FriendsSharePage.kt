@file:OptIn(ExperimentalFoundationApi::class, ExperimentalMaterial3Api::class)

package com.qyqy.cupid.ui.share

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.cupid.model.ShareToFriendsViewModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.ui.INavAction
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.cupid.ui.relations.RelationType
import com.qyqy.cupid.ui.relations.Relations
import com.qyqy.cupid.ui.relations.TabLayout
import com.qyqy.cupid.widgets.CupidAppBar
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.UserInfo
import com.qyqy.ucoo.account.toUserInfo
import com.qyqy.ucoo.accountManager
import com.qyqy.ucoo.compose.LaunchOnceEffect
import com.qyqy.ucoo.compose.presentation.ff.UserItem
import com.qyqy.ucoo.compose.state.EmptyView
import com.qyqy.ucoo.compose.state.PullRefreshBox
import com.qyqy.ucoo.compose.state.StateLayoutDefaults
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppearanceStatusBars
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.im.compat.conversation.AppConversationManger
import com.qyqy.ucoo.mine.FriendsContract
import com.qyqy.ucoo.utils.EntityCallback
import eu.bambooapps.material3.pullrefresh.PullRefreshIndicatorDefaults
import kotlinx.coroutines.flow.map

/**
 *  @time 8/19/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.relations.family
 */


@Composable
fun ShareToFriendPage(
    @RelationType visibleRelationType: Int = Relations.FRIENDS,
    @ShareSourceType shareSourceType: Int,
    voiceRoomId: String?,
    onAction: INavAction
) {
    val pagerState = rememberPagerState(
        initialPage = when (visibleRelationType) {
            Relations.FOCUS -> 1
            Relations.FANS -> 2
            else -> 0
        }
    ) {
        3
    }

    val t1 = stringResource(id = R.string.cpd最近聊天)
    val t2 = stringResource(id = R.string.cpd_focus)
    val t3 = stringResource(id = R.string.label_fans)

    val titles = remember {
        listOf(t1, t2, t3)
    }
    val changed = remember {
        mutableStateOf(false)
    }
    DisposableEffect(key1 = Unit) {
        onDispose {
            if (changed.value) {
                accountManager.refreshSelfUserByRemote()
            }
        }
    }

    Column {
        CupidAppBar(title = stringResource(id = R.string.cpd_invite_member), actions = {
            Box(modifier = Modifier.size(40.dp))
        })
        TabLayout(modifier = Modifier.padding(horizontal = 48.dp), pagerState = pagerState, titles = titles)
        HorizontalPager(
            state = pagerState, modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.surface)
        ) { page: Int ->
            val relationType = when (page) {
                1 -> Relations.FOCUS
                2 -> Relations.FANS
                0 -> Relations.FRIENDS
                else -> return@HorizontalPager
            }
            if (page == 0) {
                RecentlyChatList(
                    voiceRoomId = voiceRoomId,
                    relationType = relationType,
                    shareSourceType = shareSourceType,
                    onAvatarClick = {
                        onAction.navigateToProfile(it.id)
                    }
                )
            } else {
                FamilyInviteList(
                    voiceRoomId = voiceRoomId,
                    relationType = relationType,
                    shareSourceType = shareSourceType,
                    onAvatarClick = {
                        onAction.navigateToProfile(it.id)
                    }
                )
            }
        }
    }
}

@Composable
private fun FamilyInviteList(
    voiceRoomId: String?,
    @RelationType relationType: Int,
    shareSourceType: Int,
    modifier: Modifier = Modifier,
    onAvatarClick: EntityCallback<UserInfo>
) {
    val vm: ShareToFriendsViewModel = viewModel(key = relationType.toString(), factory = viewModelFactory {
        initializer {
            ShareToFriendsViewModel(ShareDestination.SHARE_TO_FRIEND)
        }
    })

    val state by vm.uiState.collectAsStateWithLifecycle()
    val isRefreshing by remember {
        derivedStateOf { state.refreshState.isLoading }
    }
    val list by remember {
        derivedStateOf { state.listState.getSuccessValue() }
    }
    val hasMore by vm.hasMore
    val loaded by vm.loaded
    val isEmpty by remember {
        derivedStateOf {
            val ls = state.listState
            ls is FriendsContract.FriendsState.Success && ls.list.isEmpty()
        }
    }
    LocalContentLoading.current.value = vm.posting.value

    LaunchOnceEffect(relationType) {
        vm.sendEvent(FriendsContract.Event.Fetch(relationType))
    }
    PullRefreshBox(
        modifier = modifier,
        isRefreshing = isRefreshing,
        colors = PullRefreshIndicatorDefaults.colors(
            contentColor = MaterialTheme.colorScheme.primary,
            containerColor = Color.White
        ),
        onRefresh = { vm.sendEvent(FriendsContract.Event.Fetch(relationType)) }) {
        LazyColumn(modifier = Modifier.fillMaxSize()) {
            items(list) { item ->
                UserItem(user = item, nameColor = MaterialTheme.colorScheme.onSurface, onAvatarClicked = {
                    onAvatarClick(item)
                }) {
                    AppButton(
                        text = stringResource(id = if (item.hasInvited) R.string.cpd_has_invited else R.string.cpd_invite),
                        onClick = {
                            if (shareSourceType == ShareSource.SHARE_FAMILY) {
                                vm.shareFamily(item)
                            } else if (shareSourceType == ShareSource.SHARE_VOICE_ROOM) {
                                vm.shareAudioRoom(voiceRoomId!!, item)
                            }
                        },
                        border = BorderStroke(1.dp, color = if (item.hasInvited) Color(0xFFE5E6EB) else CpdColors.FFFF5E8B),
                        background = Color.White,
                        textStyle = TextStyle(color = if (item.hasInvited) Color(0xFFE5E6EB) else CpdColors.FFFF5E8B, fontSize = 14.sp),
                        enabled = !item.hasInvited,
                        contentPadding = PaddingValues(horizontal = 12.dp),
                        modifier = Modifier
                            .height(32.dp)
                            .widthIn(88.dp)
                    )
                }
            }

            if (loaded && !isEmpty && !isRefreshing && hasMore) {
                item {
                    LaunchedEffect(true) {
                        vm.sendEvent(FriendsContract.Event.LoadMore(relationType))
                    }
                    StateLayoutDefaults.BottomLoadBar(hasMore = true)
                }
            }
        }
        if (loaded && isEmpty) {
            Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                EmptyView(
                    textRes = when (relationType) {
                        Relations.FOCUS -> R.string.cpd暂无关注的人
                        Relations.FANS -> R.string.cpd暂无粉丝
                        else -> R.string.cpd暂无好友
                    },
                    iconRes = R.drawable.cupid_empty
                )
            }
        }
    }
}

@Composable
fun RecentlyChatList(
    voiceRoomId: String?,
    @RelationType relationType: Int,
    shareSourceType: Int,
    modifier: Modifier = Modifier,
    onAvatarClick: EntityCallback<UserInfo>
) {
    val vm: ShareToFriendsViewModel = viewModel(key = relationType.toString(), factory = viewModelFactory {
        initializer {
            ShareToFriendsViewModel(ShareDestination.SHARE_TO_FRIEND)
        }
    })

    Box(modifier = modifier) {
        val list by AppConversationManger.c2cConvFlow.map { list ->
            list.map {
                it.user.toUserInfo()
            }
        }.collectAsStateWithLifecycle(emptyList())

        LazyColumn(modifier = Modifier.fillMaxSize()) {
            items(list) { item ->
                val hasInvited = rememberSaveable {
                    mutableStateOf(item.hasInvited)
                }
                UserItem(user = item, nameColor = MaterialTheme.colorScheme.onSurface, onAvatarClicked = {
                    onAvatarClick(item)
                }) {
                    AppButton(
                        text = stringResource(id = if (hasInvited.value) R.string.cpd_has_invited else R.string.cpd_invite),
                        onClick = {
                            if (shareSourceType == ShareSource.SHARE_FAMILY) {
                                vm.shareFamily(item, hasInvited)
                            } else if (shareSourceType == ShareSource.SHARE_VOICE_ROOM) {
                                vm.shareAudioRoom(voiceRoomId!!, item, hasInvited)
                            }
                        },
                        border = BorderStroke(1.dp, color = if (hasInvited.value) Color(0xFFE5E6EB) else CpdColors.FFFF5E8B),
                        background = Color.White,
                        textStyle = TextStyle(color = if (hasInvited.value) Color(0xFFE5E6EB) else CpdColors.FFFF5E8B, fontSize = 14.sp),
                        enabled = !hasInvited.value,
                        contentPadding = PaddingValues(horizontal = 12.dp),
                        modifier = Modifier
                            .height(32.dp)
                            .widthIn(88.dp)
                    )
                }
            }
        }

        if (list.isEmpty()) {
            Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                EmptyView(
                    textRes = R.string.cpd暂无好友,
                    iconRes = R.drawable.cupid_empty
                )
            }
        }
    }

}


@Composable
@Preview
private fun FamilyInvitePreview() {
    val t1 = stringResource(id = R.string.cpd_focus)
    val t2 = stringResource(id = R.string.label_fans)
    val t3 = stringResource(id = R.string.label_friend)
    val titles = remember {
        listOf(t1, t2, t3)
    }
    val visibleRelationType: Int = Relations.FOCUS
    val pagerState = rememberPagerState(
        initialPage = when (visibleRelationType) {
            Relations.FOCUS -> 0
            Relations.FANS -> 1
            else -> 2
        }
    ) {
        3
    }
    Column(modifier = Modifier.background(color = Color.White)) {
        CupidAppBar(title = stringResource(id = R.string.cpd_invite_member), actions = {
            Box(modifier = Modifier.size(40.dp))
        })
        TabLayout(modifier = Modifier.padding(horizontal = 48.dp), pagerState = pagerState, titles = titles)
//        HorizontalPager(
//            state = pagerState, modifier = Modifier
//                .fillMaxSize()
//        ) { page: Int ->
//            val relationType = when (page) {
//                0 -> Relations.FOCUS
//                1 -> Relations.FANS
//                else -> Relations.FRIENDS
//            }
//            Column(modifier = Modifier.fillMaxSize()) {
//
//            }
//        }
    }
}