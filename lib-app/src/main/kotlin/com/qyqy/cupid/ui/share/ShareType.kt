package com.qyqy.cupid.ui.share

import androidx.annotation.IntDef

/**
 *  @time 8/30/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.ui.share
 */

/**
 * 分享目的地
 */
data object ShareDestination {
    const val SHARE_TO_FAMILY = 1
    const val SHARE_TO_FRIEND = 2
    const val SHARE_TO_GROUP = 3
}

@IntDef(value = [ShareDestination.SHARE_TO_FAMILY, ShareDestination.SHARE_TO_FRIEND, ShareDestination.SHARE_TO_GROUP])
annotation class ShareDestinationType


data object ShareSource {
    const val SHARE_FAMILY = 1
    const val SHARE_VOICE_ROOM = 2
}

@IntDef(value = [ShareSource.SHARE_FAMILY, ShareSource.SHARE_VOICE_ROOM])
annotation class ShareSourceType