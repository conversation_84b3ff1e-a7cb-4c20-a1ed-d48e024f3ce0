package com.qyqy.cupid.utils

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.qyqy.ucoo.compose.rememberDateState
import com.qyqy.ucoo.im.conversation.NotificationPermissionHelper
import com.qyqy.ucoo.sIsLoginFlow
import com.qyqy.ucoo.sUserKV
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

object AppUsageUtil {

    private const val KEY_APP_DAYS_INCREASE_TIME = "app_days_increase_time"
    private const val KEY_APP_USAGE_DAY_COUNT = "app_usage_day_count"
    private const val KEY_GRANTED_NOTIFICATION_PERMISSION = "granted_notification_permission"
    private const val KEY_LACK_NFP_START_DAY = "lack_nfp_start_day"
    private const val KEY_NF_LAST_SHOW_DATE = "NF_LAST_SHOW_DATE"
    private const val TAG = "AppUsageUtil"

    private var grantedNotificationPermission: Boolean
        get() = sUserKV.getBoolean(KEY_GRANTED_NOTIFICATION_PERMISSION, false)
        set(value) {
            sUserKV.putBoolean(KEY_GRANTED_NOTIFICATION_PERMISSION, value)
        }

    /**
     * 上次展示通知alert的日期
     */
    var nfLastShowDate: String
        get() = sUserKV.getString(KEY_NF_LAST_SHOW_DATE, "")
        set(value) {
            sUserKV.putString(KEY_NF_LAST_SHOW_DATE, value)
        }


    /**
     * 记录使用天数，当天打开一次算一天
     */
    @Composable
    fun CollectUseDay() {
        val isLogin by sIsLoginFlow.collectAsStateWithLifecycle()
        if (isLogin) {
            val date by rememberDateState()
            LaunchedEffect(key1 = date) {
                tryIncreaseAppUsageDay()
            }
        }
    }

    private fun getNFPStartDay() = kotlin.run {
        sUserKV.getInt(KEY_LACK_NFP_START_DAY, 0)
    }

    fun checkShowNFPAlert(context: Context) = run {
        var start = getNFPStartDay()
        val usageDay = getAppUsageDayCount()
        val hasNotificationPermission = NotificationPermissionHelper.hasNotificationPermission(context)
        //先开后关
        if (grantedNotificationPermission && !hasNotificationPermission) {
            start = usageDay - 1
            sUserKV.putInt(KEY_LACK_NFP_START_DAY, start)
            grantedNotificationPermission = false
        } else if (!grantedNotificationPermission && hasNotificationPermission) {
            grantedNotificationPermission = true
        }

        (!hasNotificationPermission) && with(usageDay - start) {
            if (this > 10) {
                (this % 30) == 0
            } else {
                this == 1 || this == 3 || this == 10
            }
        }
    }

    private fun tryIncreaseAppUsageDay() {
        val kv = sUserKV
        val lastIncreaseTime = kv.getLong(KEY_APP_DAYS_INCREASE_TIME, 0L)
        if (formatDate(lastIncreaseTime) == formatDate(System.currentTimeMillis())) {
            return
        }
        kv.putInt(KEY_APP_USAGE_DAY_COUNT, getAppUsageDayCount().plus(1))
        kv.putLong(KEY_APP_DAYS_INCREASE_TIME, System.currentTimeMillis())
    }

    fun getAppUsageDayCount() = sUserKV.getInt(KEY_APP_USAGE_DAY_COUNT, 0)

    private fun formatDate(time: Long) =
        SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date(time))
}