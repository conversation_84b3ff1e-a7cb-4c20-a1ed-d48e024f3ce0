package com.qyqy.cupid.utils

import android.content.Context
import android.media.AudioAttributes
import android.media.SoundPool
import android.os.Vibrator
import com.qyqy.ucoo.R
import com.qyqy.ucoo.app

object CallRingHelper {
    private val vibrator by lazy {
        app.getSystemService(Context.VIBRATOR_SERVICE) as? Vibrator
    }

    private var soundPool: SoundPool? = null

    private var streamId: Int = -1

    fun stopRing() {
        synchronized(this){
            if (soundPool != null) {
                vibrator?.cancel()
                if (streamId != -1) {
                    soundPool?.stop(streamId)
                }
                streamId = -1
                soundPool?.release()
                soundPool = null
            }
        }
    }

    fun startRing(withVibrator: Boolean = false) {
        synchronized(this) {
            if (soundPool == null) {
                soundPool = SoundPool.Builder()
                    .setAudioAttributes(
                        AudioAttributes.Builder()
                            .setUsage(AudioAttributes.USAGE_ASSISTANCE_SONIFICATION)
                            .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                            .build()
                    )
                    .build()
            } else {
                if (streamId != -1) {
                    soundPool?.stop(streamId)
                }
            }
            soundPool?.let {
                if (withVibrator && vibrator?.hasVibrator() == true) {
                    vibrator?.vibrate(longArrayOf(100, 100, 200, 150, 300, 200, 200, 1000), 0)
                }
                val soundId = it.load(app, R.raw.best_match_audio_effect, 1)
                it.setOnLoadCompleteListener { _, sampleId, _ ->
                    if (sampleId == soundId) {
                        streamId = it.play(sampleId, 1f, 1f, 1, -1, 1f)
                    }
                }
            }
        }
    }
}