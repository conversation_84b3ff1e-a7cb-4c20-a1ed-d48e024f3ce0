package com.qyqy.cupid.utils

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.lifecycle.HasDefaultViewModelProviderFactory
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.CreationExtras
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.utils.LogUtils
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.launch
import kotlin.reflect.KClass

/**
 *  @time 9/12/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.utils.bus
 */

/**
 * 事件总线思路：
 * ①因为是一对多的广播关系，需要使用sharedFlow来搞定
 * ②需要在compose重组完成前保留数据(但是肯定不能保留在最上层的MainActivity里)
 * ③保留数据不会在compose第二次重组的时候再次收到此事件, 针对这个要求, 使用Channel比较好一点
 *
 * 基本实现:
 * 1. 创建一个stickyEventFlow和normalEventFlow，分别用于发送粘性事件和普通事件
 * 2. 在compose进行路由跳转的时实例化一个Viewmodel, 这个viewmodel只存在一个Channel
 * 3. 在发送事件时, sharedFlow会直接进行下发, 而channel会在compose重组完成后进行下发, 重新重组时不会再收到这个event
 */
object FlowEventBus {
    private val _stickyEventFlow: MutableSharedFlow<Any> = MutableSharedFlow(1)
    private val _normalEventFlow: MutableSharedFlow<Any> = MutableSharedFlow(0)

    val stickyEventFlow = _stickyEventFlow.asSharedFlow().shareIn(
        appCoroutineScope, SharingStarted.WhileSubscribed(
            replayExpirationMillis = 3000
        ), 1
    )
    val normalEventFlow = _normalEventFlow.asSharedFlow()

    fun send(obj: Any, dispatcher: CoroutineDispatcher = Dispatchers.Main) {
        appCoroutineScope.launch {
            listOfNotNull(_stickyEventFlow, _normalEventFlow).forEach { flow ->
                appCoroutineScope.launch(dispatcher) {
                    flow.emit(obj)
                }
            }
        }
    }

    fun <VM : ViewModel> ViewModelStoreOwner.get(
        modelClass: KClass<VM>,
        key: String? = null,
        factory: ViewModelProvider.Factory? = null,
        extras: CreationExtras = if (this is HasDefaultViewModelProviderFactory) {
            this.defaultViewModelCreationExtras
        } else {
            CreationExtras.Empty
        },
    ): VM {
        val provider = if (factory != null) {
            ViewModelProvider.create(this.viewModelStore, factory, extras)
        } else if (this is HasDefaultViewModelProviderFactory) {
            ViewModelProvider.create(this.viewModelStore, this.defaultViewModelProviderFactory, extras)
        } else {
            ViewModelProvider.create(this)
        }
        return if (key != null) {
            provider[key, modelClass]
        } else {
            provider[modelClass]
        }
    }

    inline fun <reified VM : ViewModel> ViewModelStoreOwner.getOrNull(key: String? = null): VM? {
        return try {
            if (key != null) {
                ViewModelProvider(this)[key, VM::class]
            } else {
                ViewModelProvider(this)[VM::class]
            }
        } catch (e: Exception) {
            null
        }
    }

    fun initWithComposeNavigation(owner: ViewModelStoreOwner) {
        owner.get(FlowBusViewModel::class)
    }

    class FlowBusViewModel : ViewModel() {
        internal val _effect: Channel<Any> = Channel()
        val effect = _effect.receiveAsFlow()

        @Volatile
        var isAvailable = false

        init {
            viewModelScope.launch {
                normalEventFlow.collect {
                    if (isAvailable) {
                        _effect.send(it)
                    }
                }
            }
        }

        override fun onCleared() {
            super.onCleared()
            _effect.close()
        }
    }
}

@Composable
inline fun <reified T> observe(isSticky: Boolean = false, crossinline block: (T) -> Unit) {
    LaunchedEffect(key1 = Unit) {
        (if (isSticky)
            FlowEventBus.stickyEventFlow
        else FlowEventBus.normalEventFlow)
            .collect {
                if (it is T) {
                    try {
                        block(it)
                    } catch (e: Exception) {
                        LogUtils.w("FlowBus", "observe run block failed. current event = %s ,error = %s", it, e)
                    }
                }
            }
    }
}

/**
 * 在compose 页面中接受event事件
 * todo 目前有个问题, 因为Channel组播的性质, 如果有一个观察者处理了事件, 其他观察者就不会再收到此事件
 * 详细的情况就是 如果在页面多次调用, 总是只有一个代码块被执行
 *
 * 这个地方更应该用SharedFlow冷流, 在没有被订阅的情况下存储资源. 但是这样做不可避免的会导致send进来的event不知道该在何时释放掉
 * 或许使用key来声明多个Channel更好一点? 先留个坑
 *
 * @param T 事件类型
 * @param key 无用
 * @param onResume 是否在页面resume的时候接受事件, create的状态下不受影响
 * @param block 事件处理
 */
@Composable
inline fun <reified T> observeChannel(key: String = "default-key", onlyResume: Boolean = false, crossinline block: (T) -> Unit) {
    val vm = viewModel(modelClass = FlowEventBus.FlowBusViewModel::class)
    DisposableEffect(key1 = Unit) {
        vm.isAvailable = true
        onDispose {
            if (onlyResume) {
                vm.isAvailable = false
            }
        }
    }

    LaunchedEffect(key1 = Unit) {
        vm.effect.collect {
            if (it is T) {
                try {
                    block(it)
                } catch (e: Exception) {
                    LogUtils.w("FlowBus", "observeChannel run block failed. current event = %s ,error = %s", it, e)
                }
            }
        }
    }
}