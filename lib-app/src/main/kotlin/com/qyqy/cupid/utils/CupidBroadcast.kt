package com.qyqy.cupid.utils

import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch

object CupidBroadcast {
    private val scope = MainScope()

    private val flowReal: MutableSharedFlow<BroadcastEvent<*>> = MutableSharedFlow()
    val flow = flowReal.asSharedFlow()

    fun sendEvent(event: BroadcastEvent<*>) {
        scope.launch {
            flowReal.emit(event)
        }
    }
}

object CupidEventName {
    //主动
    const val UPDATE_MEMBER_INFO = "update_family_member_info"
    const val KICK_MEMBER = "remove_family_member"

    //被动收到
    const val MEMBER_KICKED = "kick_family_member"
    const val MEMBER_QUITED = "quit_family_member"
}

data class BroadcastEvent<T>(val eventName: String, val value: T) {
    companion object {
        fun of(eventName: String) = BroadcastEvent(eventName, Unit)
    }
}