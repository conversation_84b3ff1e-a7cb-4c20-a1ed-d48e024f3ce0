package com.qyqy.cupid.utils

import com.overseas.common.ext.BaseKVData
import com.qyqy.cupid.apis.FamilyApi
import com.qyqy.cupid.data.FamilyEncourageInfo
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.accountManager
import com.qyqy.ucoo.app
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.bean.TribeUser
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.IMCompatListener
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.tribe.bean.Tribe
import com.qyqy.ucoo.tribe.bean.TribeInfo
import com.qyqy.ucoo.tribe.bean.tribeId
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonPrimitive
import kotlin.reflect.KProperty

/**
 *  @time 8/22/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.utils
 */
private val fastKVDir: String by lazy(LazyThreadSafetyMode.NONE) {
    "$filesDir/fastkv"
}
private val filesDir: String by lazy(LazyThreadSafetyMode.NONE) {
    app.filesDir.absolutePath
}

object CupidFamilyManager : BaseKVData(fastKVDir, "family_global_obj") {

    private val mapObject = mutableMapOf<String, Any?>()

    fun init() {
        kv
        val tribeInfo = myTribeInfo
        updateFamilyInfo(tribeInfo)
        IMCompatCore.addIMListener(object : IMCompatListener {

            override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
                onReceiveMessage(message)
            }

        })

        appCoroutineScope.launch {
            accountManager.userFlow
                .distinctUntilChanged { old, new ->
                    return@distinctUntilChanged old.tribe?.tribeId == new.tribe?.tribeId
                }
                .collectLatest {
                    updateMyFamily(it.tribe?.copy(relationWithMe = 10))
                }
        }
    }

    //region 家族信息管理
    var myTribeInfo: TribeInfo? by this
        private set

    var myTribe: Tribe? by this
        private set

    private val _tribeFlow: MutableStateFlow<Tribe?> = MutableStateFlow(myTribe)
    val tribeFlow = _tribeFlow.asStateFlow()
    private val _tribeInfoFlow: MutableStateFlow<TribeInfo?> = MutableStateFlow(myTribeInfo)
    val tribeInfoFlow = _tribeInfoFlow.asStateFlow()
    fun updateFamilyInfo(tribeInfo: TribeInfo?, async: Boolean = true) {
        if (tribeInfo != null && !tribeInfo.isMyTribe) {
            return
        } else if (tribeInfo == null) {
            clear()
            return
        }
        myTribeInfo = tribeInfo
        if (async) {
            appCoroutineScope.launch {
                _tribeInfoFlow.emit(myTribeInfo)
            }
        } else {
            _tribeInfoFlow.value = myTribeInfo
        }
        updateMyFamily(tribeInfo.toTribe())
    }

    fun updateFamily(block: (TribeInfo) -> TribeInfo) {
        updateFamilyInfo(myTribeInfo?.let(block))
    }

    fun updateMyFamily(tribe: Tribe?, async: Boolean = true) {
        if (tribe != null && tribe.isMyTribe.not()) {
            return
        } else if (tribe == null) {
            clear()
            return
        }
        myTribe = tribe
        if (async) {
            appCoroutineScope.launch {
                _tribeFlow.emit(myTribe)
            }
        } else {
            _tribeFlow.value = myTribe
        }
        _familyState.value = 0
    }

    private fun clear() {
        appCoroutineScope.launch {
            myTribe = null;
            myTribeInfo = null
            _tribeFlow.emit(myTribe);
            _tribeInfoFlow.emit(myTribeInfo)
            refreshFamilyEncourageInfo()
        }
    }

    //endregion

    private val api by lazy {
        createApi(FamilyApi::class.java)
    }

    fun refreshFamilyInfo(tribeId: Int? = null) {
        appCoroutineScope.launch {
            val id = tribeId ?: myTribe?.id ?: -1
            if (id == -1) {
                refreshFamilyEncourageInfo()
                //没有家族不需要刷新
                return@launch
            }
            runApiCatching {
                api.tribeDetail(id.toString())
            }.onSuccess {
                if (it.isMyTribe) {
                    updateFamilyInfo(it)
                }
            }
        }
    }

    private inline operator fun <reified T> getValue(
        thisRef: CupidFamilyManager, property: KProperty<*>,
    ): T? {
        val key = property.name
        return (mapObject[key] as? T) ?: getObject<T>(key).also { mapObject[key] = it }
    }

    private inline operator fun <reified T> setValue(
        thisRef: CupidFamilyManager, property: KProperty<*>, value: T?,
    ) {
        val key = property.name
        putObject(value, key)
        mapObject[key] = value
    }

    private inline fun <reified T> getObject(key: String? = null): T? {
        val k = key ?: T::class.java.name
        val json = kv.getString(k)
        return when {
            json.isNullOrBlank() -> null
            else -> try {
                sAppJson.decodeFromString(json)
            } catch (e: Exception) {
                null
            }
        }
    }

    private inline fun <reified T> putObject(t: T?, key: String? = null) {
        val k = key ?: T::class.java.name
        val json = t?.let { sAppJson.encodeToString(it) } ?: ""
        kv.putString(k, json)
    }

    //region 信令接受

    private fun onReceiveMessage(message: UCCustomMessage) {
        val cmd = message.cmd
        val data = message.customJson
        when (cmd) {
            MsgEventCmd.MEMBER_JOIN -> {
                data.get("user")?.let { userStr ->
                    val user = sAppJson.decodeFromJsonElement<TribeUser>(userStr)
                    if (user.userid == sUser.userId) {
                        accountManager.refreshSelfUserByRemote()
                        refreshFamilyInfo()
                    } else {
                        updateFamily {
                            it.copy(memberCnt = (it.memberCnt ?: 0) + 1)
                        }
                    }
                }
            }

            MsgEventCmd.MEMBER_QUIT -> {
                data.get("user")?.let { userStr ->
                    val user = sAppJson.decodeFromJsonElement<TribeUser>(userStr)
                    if (user.userid == sUser.userId) {
                        showFamilyDialog(1)
                        clear()
                    } else {
                        updateFamily {
                            it.copy(memberCnt = (it.memberCnt ?: 1) - 1)
                        }
                    }
                    CupidBroadcast.sendEvent(BroadcastEvent(CupidEventName.MEMBER_QUITED, user.userid))
                }
            }

            MsgEventCmd.MEMBER_KICKED_OUT -> {
                data.get("kicked_user")?.let { userStr ->
                    val user = sAppJson.decodeFromJsonElement<TribeUser>(userStr)
                    if (user.userid == sUser.userId) {
                        showFamilyDialog(2)
                        clear()
                    } else {
                        updateFamily {
                            it.copy(memberCnt = (it.memberCnt ?: 1) - 1)
                        }
                        CupidBroadcast.sendEvent(BroadcastEvent(CupidEventName.MEMBER_KICKED, user.userid))
                    }
                }
            }

            MsgEventCmd.TRIBE_DESTROYED -> {//部落销毁
                showFamilyDialog(if (myTribeInfo?.iAmOwner == true) 4 else 3)
                updateFamilyInfo(null)
            }

            MsgEventCmd.MEMBER_APPLY_CNT_CHANGE -> {
                data.get("member_apply_cnt")?.jsonPrimitive?.intOrNull?.let { cnt ->
                    updateFamily {
                        it.copy(member_apply_wait_cnt = cnt)
                    }
                }
            }

            MsgEventCmd.NAME_UPDATE -> {
                val tribe = data.get("tribe")?.let {
                    sAppJson.decodeFromJsonElement<Tribe>(it)
                }
                if (tribe != null) {
                    updateFamily {
                        it.copy(name = tribe.name)
                    }
                }
            }

            MsgEventCmd.BULLETIN_UPDATE -> {
                refreshFamilyInfo()
            }
        }
    }

    suspend fun joinTribe(tribe_id: Int): Result<JsonObject> {
        return runApiCatching {
            api.joinTribe(mapOf("tribe_id" to tribe_id))
        }
    }

    fun exitFamily() {
        showFamilyDialog(1)
        clear()
    }

    //0 正常 1 主动退出 2.被踢出 3.解散家族 4.解散家族(主动解散)
    private val _familyState = MutableStateFlow(0)
    val familyState = _familyState.asStateFlow()

    /**
     * 全部迁移到MessagePushGlobalDialog, 这样直接取controller和dialogQueue是不对的
     * @param dialogType 1.退出家族 2.被踢出家族 3.解散家族 4.解散家族(主动解散)
     */
    private fun showFamilyDialog(dialogType: Int) {
//        val controller = AppLinkManager.controller ?: return
//        //1.先看栈里有没有家族主页
//        var hasFamilyPage = controller.contains(CupidRouters.FAMILY_HOME)
//        if (!hasFamilyPage) {
//            //2. 如果false说明没有家族主页, 就直接尝试退到家族详情的上一级
//            hasFamilyPage = controller.contains(CupidRouters.FAMILY_DETAIL)
//        }
//        if (hasFamilyPage) {
//            controller.dialogQueue?.push(FamilyBanDialog(dialogType))
//        }
        appCoroutineScope.launch {
            _familyState.emit(dialogType)
        }
    }

    //endregion

    //region 2.29.0 家族渗透率
    private val _familyEncourageInfo = MutableStateFlow(FamilyEncourageInfo())
    val familyEncourageInfo = _familyEncourageInfo.asStateFlow()

    private fun refreshFamilyEncourageInfo() {
        appCoroutineScope.launch {
            runApiCatching {
                api.getEncourageInfo()
            }.onSuccess {
                _familyEncourageInfo.value = it
            }
        }
    }

    //endregion
}