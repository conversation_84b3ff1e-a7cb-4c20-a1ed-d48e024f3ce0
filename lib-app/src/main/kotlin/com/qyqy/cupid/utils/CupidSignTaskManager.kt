package com.qyqy.cupid.utils

import android.content.Context
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import com.qyqy.ucoo.AppInfo
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.presentation.sign_tasks.SignTaskApi
import com.qyqy.ucoo.compose.presentation.sign_tasks.TaskSeries
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonPrimitive

class CupidTaskManager(val hideFinishedTask: Boolean? = null, val prizeType: Int = -1) {

    private val api = createApi<SignTaskApi>()

    //新人任务
    private val _activityTaskList: SnapshotStateList<TaskSeries> = mutableStateListOf<TaskSeries>()
    val activityTaskList: List<TaskSeries> = _activityTaskList

    fun refreshTasks(coroutineScope: CoroutineScope = appCoroutineScope) {
        coroutineScope.launch {
            runApiCatching {
                if (hideFinishedTask != null) {
                    api.getTasksWithParams(hideFinishedTask, prizeType)
                } else {
                    api.getTasks()
                }
            }
                .onSuccess { tasks ->
                    _activityTaskList.clear()
                    _activityTaskList.addAll(tasks.taskSeriesList)
                }
        }
    }

    fun refreshSignInfo(coroutineScope: CoroutineScope = appCoroutineScope) {
        coroutineScope.launch {
            requestSignTask()
        }
    }

    private suspend fun requestSignTask() {
        val value = withContext(Dispatchers.Default) {
            runApiCatching { api.getSignInfo() }.mapCatching { obj ->
                obj.takeIf {
                    it.containsKey("series_id") && it.containsKey("today_finished")
                }?.let {
                    sAppJson.decodeFromJsonElement<TaskSeries>(it)
                }
            }
        }.getOrNull() ?: return

        val index = _activityTaskList.indexOfFirst {
            it.seriesType == TaskSeries.SERIES_TYPE_SIGN
        }
        if (index != -1) {
            _activityTaskList.removeAt(index)
            _activityTaskList.add(index, value)
        }
    }

    suspend fun startSign(seriesId: Int, callback: (JsonObject) -> Unit) = runApiCatching {
        val params = mutableMapOf("series_id" to seriesId.toString())
        val smId = AppInfo.smBoxId
        if (!smId.isNullOrEmpty()) {
            params["ism_device_id"] = smId
        }
        api.doSign(params)
    }.onSuccess { obj ->
        callback(obj)
        refreshSignInfo()
    }.toastError()


    suspend fun gotoCompleteTask(taskId: Int): Result<JsonObject> {
        return runApiCatching { api.toFinishTask(taskId) }
            .onSuccess {
                val url = it["jump_link"]?.jsonPrimitive?.contentOrNull ?: ""
                AppLinkManager.controller?.navigateByLink(url)
            }.toastError()
    }
}