package com.qyqy.cupid.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import java.util.concurrent.ConcurrentHashMap

object ImageCache {

    private val bitmaps = ConcurrentHashMap<String, MutableState<Bitmap?>>()

    fun getBitmap(context: Context, url: String, size: Float = 200f): MutableState<Bitmap?> {
        val bitmap = bitmaps.getOrPut(url) {
            mutableStateOf(null)
        }
        if (bitmap.value != null && !bitmap.value!!.isRecycled) {
            return bitmap
        }
        Glide.with(context).asBitmap().load(url)
            .override(size.toInt())
            .circleCrop()
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    bitmap.value = resource
                }

                override fun onLoadCleared(placeholder: Drawable?) {

                }
            })
        return bitmap
    }


    fun clear() {
        bitmaps.clear()
    }
}