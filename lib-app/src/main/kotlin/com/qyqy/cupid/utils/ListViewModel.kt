package com.qyqy.cupid.utils

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.ucoo.account.T
import com.qyqy.ucoo.compose.state.StateViewModelWithIntPage
import com.qyqy.ucoo.http.ApiException
import com.qyqy.ucoo.toast

/**
 *  @time 9/4/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.utils
 *  实验性代码
 */
class ListViewModel<T> private constructor(
    private val callback: ListViewCallback<T>
) : StateViewModelWithIntPage<T>() {
    override suspend fun loadData(pageNum: Int): Result<List<T>> {
        val loadResult = callback.builder.load!!(pageNum, listFlow.value)
        return if (loadResult.isSuccess) {
            Result.success(loadResult.getOrNull() ?: emptyList())
        } else {
            Result.failure<List<T>>(loadResult.exceptionOrNull() ?: ApiException(msg = "error")).also {
                toast(it.exceptionOrNull()?.message ?: "")
            }
        }
    }

    companion object {
        fun <T> build(callback: ListViewCallback<T>): ListViewModel<T> {
            return ListViewModel<T>(callback)
        }
    }
}

@Stable
class ListViewCallback<T> {
    internal lateinit var builder: Builder

    inner class Builder {
        var load: ((pageNum: Int, list: List<T>) -> Result<List<T>>)? = null
        var next: ((pageNum: Int, list: List<T>) -> Int)? = null

        fun build(): ListViewCallback<T> {
            return <EMAIL> {
                builder = this@Builder
            }
        }
    }
}

@Composable
inline fun <T> listViewModel(
    crossinline builder: ListViewCallback<T>.Builder.() -> Unit
): ListViewModel<T> {
    return viewModel(modelClass = ListViewModel::class, factory = viewModelFactory {
        initializer {
            ListViewModel.build(ListViewCallback<T>().Builder().also(builder).build())
        }
    }) as ListViewModel<T>
}


