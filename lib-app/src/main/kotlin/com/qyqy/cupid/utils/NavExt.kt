package com.qyqy.cupid.utils

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavOptions
import androidx.navigation.Navigator
import androidx.navigation.navOptions
import com.qyqy.cupid.ui.AppNavController
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.CupidViewModel
import com.qyqy.cupid.ui.getArgumentsKey
import com.qyqy.cupid.ui.live.VoiceLiveHelper
import com.qyqy.cupid.ui.navRoute
import com.qyqy.cupid.ui.profile.guard.GuardListViewModel
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.asComponentActivity
import com.qyqy.ucoo.utils.EntityCallback
import com.qyqy.ucoo.utils.LogUtils
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

fun AppNavController.navigateToFamilyHome() {
    launchSingTask(CupidRouters.FAMILY_HOME, navOptions = navOptions {
        launchSingleTop = true
        restoreState = true
    })
}

/**
 * 我的守护
 */
fun AppNavController.navigateToGuardPage(guardType: GuardListViewModel.GuardType = GuardListViewModel.GuardType.IN) =
    run {
        navigate(CupidRouters.MY_GUARD, mapOf("type" to guardType))
    }

fun AppNavController.launchSingTask(
    route: String,
    arguments: Map<String, Any> = emptyMap(),
    navigatorExtras: Navigator.Extras? = null,
    navOptions: NavOptions? = null,
) {
    val nav = this.composeNav
    try {
        val realRoute = navRoute(route)
        nav.getBackStackEntry(realRoute)
        popBackStack(route, false)
        val curRoute = composeNav.currentBackStackEntry?.destination?.route
        if (curRoute == realRoute) {
            //更新参数
            val handler = composeNav.currentBackStackEntry?.savedStateHandle
            handler?.also { h ->
                for ((k, v) in arguments) {
                    h[k] = v
                }
            }
        }
    } catch (e: Exception) {
        navigate(
            route,
            arguments = arguments,
            navOptions = navOptions,
            navigatorExtras = navigatorExtras,
        )
    }
}

@Composable
fun getVoiceLiveHelper(): VoiceLiveHelper {
    return viewModel<CupidViewModel>(viewModelStoreOwner = LocalContext.current.asComponentActivity!!).voiceLiveHelper
}

data class AppEnvironment(val appNavController: AppNavController)

private val cupidSF = MutableSharedFlow<EntityCallback<AppEnvironment>>()


suspend fun startEnvironment(appEnvironment: AppEnvironment) {
    pendingEvent?.invoke(appEnvironment)
    pendingEvent = null
    cupidSF.collectLatest {
        it.invoke(appEnvironment)
    }
}

private var pendingEvent: EntityCallback<AppEnvironment>? = null

fun runWithAppEnvironment(action: EntityCallback<AppEnvironment>) {
    appCoroutineScope.launch {
        var consumed = false
        cupidSF.tryEmit {
            action.invoke(it)
            consumed = true
        }
        delay(1000)
        if (!consumed) {
            pendingEvent = action
        }
    }
}
