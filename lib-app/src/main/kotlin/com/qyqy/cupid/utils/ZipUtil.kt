package com.qyqy.cupid.utils

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.util.Enumeration
import java.util.zip.ZipEntry
import java.util.zip.ZipFile


object ZipUtil {
    private const val TAG = "ZipUtil"


    /**
     * # 解压文件
     * [zipFile] 需要解压的文件
     * [folderPath] 解压文件目录
     */
    suspend fun upZipFile(zipFile: File?, folderPath: String) {
        if (zipFile == null) return
        val destFolderPath = if (folderPath.endsWith("/")) folderPath else "$folderPath/"
        Log.d(TAG, "upZipFile: start zipFile = ${zipFile.absolutePath}")
        withContext(Dispatchers.IO) {
            val zfile = ZipFile(zipFile)
            val zList: Enumeration<*> = zfile.entries()
            var ze: ZipEntry?
            val buf = ByteArray(10240)
            while (zList.hasMoreElements()) {
                ze = try {
                    zList.nextElement() as? ZipEntry
                } catch (e: Exception) {
                    e.printStackTrace()
                    Log.e(TAG, "upZipFile: ${e.message}")
                    null
                }
                if (ze == null) continue
                if (ze.isDirectory) {
                    Log.d(TAG, "解压目录 = " + ze.name)
                    var dirstr = destFolderPath + ze.name
                    dirstr = String(dirstr.toByteArray())
                    Log.d(TAG, "解压保存地址 = $dirstr")
                    val f = File(dirstr)
                    f.mkdir()
                } else {
                    val f = File(destFolderPath + ze.name.removeSuffix("/"))
                    val p = f.parentFile
                    if (p != null && p.exists().not()) {
                        p.mkdirs()
                    }
                    Log.d(TAG, "解压文件 = " + ze.name)
                    if (!f.exists()) {
                        try {
                            f.createNewFile()
                        } catch (e: Exception) {
                            e.printStackTrace()
                            Log.e(TAG, "createNewFile: ${e.message},f:${f.absolutePath}")
                        }
                    }
                    var `is`: InputStream? = null
                    var fos: FileOutputStream? = null
                    try {
                        `is` = zfile.getInputStream(ze)
                        fos = FileOutputStream(f)
                        var len: Int
                        while ((`is`.read(buf).also { len = it }) != -1) {
                            fos.write(buf, 0, len)
                        }

                    } catch (e: Exception) {
                        e.printStackTrace()
                        Log.e(TAG, "upZipFile: ${e.message}")
                    } finally {
                        `is`?.close()
                        fos?.close()
                    }
                }
            }
            zfile.close()
        }
        Log.d(TAG, "upZipFile: end zipFile = ${zipFile.absolutePath}")
    }

}