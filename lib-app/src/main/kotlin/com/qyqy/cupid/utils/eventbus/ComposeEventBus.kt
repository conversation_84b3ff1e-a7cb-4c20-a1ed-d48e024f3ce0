package com.qyqy.cupid.utils.eventbus

import android.os.SystemClock
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.staticCompositionLocalOf
import java.util.UUID


@Composable
fun EventBusListener(listener: EventBus.Listener, eventBus: EventBus = LocalEventBus.current) {
    DisposableEffect(key1 = listener, key2 = eventBus) {
        eventBus + listener
        onDispose {
            eventBus - listener
        }
    }
}

@Composable
fun EventBusListener(eventBus: EventBus = LocalEventBus.current, onProviderListener: EventBus.() -> EventBus.Listener) {
    DisposableEffect(key1 = eventBus) {
        val listener = eventBus.onProviderListener()
        eventBus + listener
        onDispose {
            eventBus - listener
        }
    }
}

val appEventBus = EventBus()

val LocalEventBus = staticCompositionLocalOf {
    appEventBus
}

data class StickyEvent(
    val id: String,
    val event: String,
    val time: Long,
)

class EventBus {

    companion object Sticky

    private data class CombinedListener(override val key: Key<*>) : Listener {

        private val list: MutableList<Listener> = mutableListOf()

        private val stickyEventList: MutableList<StickyEvent> = mutableListOf()

        val activeCurrent: Listener?
            get() = list.findLast {
                it.isActive
            }

        fun add(listener: Listener) {
            list.add(listener)
            listener.dispatchStickyEvent()
        }

        fun remove(listener: Listener) {
            list.remove(listener)
        }

        fun addStickyEvent(event: String): String {
            val id = UUID.randomUUID().toString()
            stickyEventList.add(
                StickyEvent(
                    id = id,
                    event = event,
                    time = SystemClock.elapsedRealtime()
                )
            )
            return id
        }

        fun removeStickEvent(id: String) {
            val existEvent = stickyEventList.find {
                it.id == id
            }
            stickyEventList.remove(existEvent)
        }

        fun dispatchStickyEvent() {
            list.forEach {
                it.dispatchStickyEvent()
            }
        }

        private fun Listener.dispatchStickyEvent() {
            if (stickyEventList.isNotEmpty()) {
                stickyEventList.toList().forEach {
                    if (isActive && handleStickyEvent(it)) {
                        stickyEventList.remove(it)
                    }
                }
            }
        }
    }

    interface Key<L : Listener>

    interface Listener {

        val key: Key<*>

        val isActive: Boolean
            get() = true

        fun handleStickyEvent(sticky: StickyEvent): Boolean {
            return false
        }
    }

    private val listeners = mutableListOf<CombinedListener>()

    operator fun <L : Listener> get(key: Key<L>): L? {
        val existsListener = listeners.find {
            it.key == key
        }
        @Suppress("UNCHECKED_CAST")
        return existsListener?.activeCurrent as L?
    }

    operator fun <L : Listener> plus(listener: L) {
        val existsListener = listeners.find {
            it.key == listener.key
        }
        if (existsListener != null) {
            existsListener.add(listener)
        } else {
            listeners.add(CombinedListener(listener.key).also {
                it.add(listener)
            })
        }
    }

    operator fun minus(listener: Listener) {
        val existsListener = listeners.find {
            it.key == listener.key
        }
        existsListener?.remove(listener)
    }

    fun deleteListenersByKey(key: Key<*>) {
        val existsListener = listeners.find {
            it.key == key
        }
        if (existsListener != null) {
            listeners.remove(existsListener)
        }
    }

    /**
     * 回调listener方法
     * @param key 获取callback需要的key
     * @param fallbackStickyEvent callback不存在或者当[block]返回==[EventBus.Sticky]，可以添加一个粘性事件
     * @param block 在这个方法中可以获取到callback, 并回调任意你想要的方法
     */
    fun <L : Listener> runListenerFunc(key: Key<L>, fallbackStickyEvent: (L?) -> String? = { null }, block: L.() -> Any): String {
        val fallbackEvent = this[key]?.run {
            val ret = block()
            if (ret === Sticky) {
                fallbackStickyEvent(this)
            } else {
                null
            }
        } ?: run {
            fallbackStickyEvent(null)
        }
        var id = ""
        if (!fallbackEvent.isNullOrEmpty()) {
            id = addStickyEvent(key, fallbackEvent)
        }
        return id
    }

    fun addStickyEvent(key: Key<*>, event: String, dispatch: Boolean = false): String {
        val combinedListener = listeners.find { it.key == key } ?: CombinedListener(key).also {
            listeners.add(it)
        }
        return combinedListener.addStickyEvent(event).apply {
            if (dispatch) {
                dispatchStickyEvent(key)
            }
        }
    }

    fun removeStickEvent(key: Key<*>, id: String) {
        listeners.find { it.key == key }?.removeStickEvent(id)
    }

    fun dispatchStickyEvent(key: Key<*>) {
        listeners.find { it.key == key }?.dispatchStickyEvent()
    }
}
