package com.qyqy.cupid.utils

import com.qyqy.ucoo.sUserKV
import kotlin.time.DurationUnit
import kotlin.time.toDuration

/**
 * 今天是否已经执行过了
 * @param key
 * @param callback
 */
fun isTodayHasRan(key: String, callback: () -> Boolean) {
    val lastTime = sUserKV.getLong(key, 0L)
    val current = System.currentTimeMillis()

    val after = maxOf(lastTime, current)
    val before = minOf(lastTime, current)
    val over1day =
        (after.toDuration(DurationUnit.MILLISECONDS) - before.toDuration(DurationUnit.MILLISECONDS) - 1.toDuration(DurationUnit.DAYS)).isNegative()
    if (!over1day) {
        if (callback()) {
            sUserKV.putLong(key, System.currentTimeMillis())
        }
    }
}