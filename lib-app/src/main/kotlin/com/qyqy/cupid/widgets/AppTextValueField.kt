package com.qyqy.cupid.widgets

import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue

class AppTextFieldValue(
    text: String,
    filter: ((String) -> String)? = null,
    selection: TextRange = TextRange.Zero,
    composition: TextRange? = null,
    val maxLength: Int = 50,
) {
    private var state = mutableStateOf(TextFieldValue(text, selection, composition))
    val textFieldValue: TextFieldValue
        get() = state.value


    val text: String
        get() = textFieldValue.text

    val onValueChange: (TextFieldValue) -> Unit = {
        var newText = if (filter != null) {
            filter(it.text)
        } else {
            it.text
        }
        if (newText.length <= maxLength) {
            state.value = it.copy(text = newText)
        } else {
            newText = newText.take(maxLength)
            state.value = TextFieldValue(newText, TextRange(newText.length))
        }
    }
}