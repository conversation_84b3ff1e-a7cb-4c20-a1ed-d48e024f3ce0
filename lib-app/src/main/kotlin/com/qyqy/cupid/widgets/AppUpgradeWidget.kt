package com.qyqy.cupid.widgets

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.FileProvider
import com.coolerfall.download.DownloadCallback
import com.coolerfall.download.Priority
import com.qyqy.ucoo.BuildConfig
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.core.upgrade.AppStoreUtil
import com.qyqy.ucoo.core.upgrade.UpgradeEntity
import com.qyqy.ucoo.isPlayChannel
import com.qyqy.ucoo.utils.OnClick
import com.qyqy.ucoo.utils.download.DownloadHelper
import com.qyqy.ucoo.utils.toastRes
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import java.io.File

@Composable
fun AppUpgradeContent(upgradeEntity: UpgradeEntity, onClose: OnClick) {

    Box(
        modifier = Modifier
            .fillMaxWidth()
    ) {

        Spacer(
            modifier = Modifier
                .padding(top = 30.dp)
                .matchParentSize()
                .background(Color.White, RoundedCornerShape(8.dp))
        )

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .paint(painterResource(id = R.drawable.cpd_bg_dialog_upgrade), contentScale = ContentScale.FillWidth, alignment = Alignment.TopStart)
                .padding(horizontal = 15.dp)
        ) {

            Spacer(modifier = Modifier.fillMaxHeight(0.18f))

            Text(
                text = upgradeEntity.title,
                modifier = Modifier.padding(start = 9.dp),
                color = Color(0xFF1D2129),
                fontSize = 24.sp,
                fontWeight = FontWeight.Medium
            )

            Box(
                modifier = Modifier
                    .padding(start = 9.dp, top = 16.dp)
                    .background(Color(0xFFFF5E8B), CircleShape)
                    .padding(horizontal = 8.dp, vertical = 3.dp)
            ) {
                Text(
                    text = upgradeEntity.newVersionName,
                    color = Color.White,
                    fontSize = 14.sp,
                    lineHeight = 15.sp
                )
            }

            Text(
                text = stringResource(id = R.string.cpd更新内容),
                modifier = Modifier.padding(start = 9.dp, top = 24.dp),
                color = Color(0xFF86909C),
                fontSize = 14.sp
            )

            Text(
                text = upgradeEntity.descriptionStr,
                modifier = Modifier.padding(top = 16.dp),
                color = Color(0xFF1D2129),
                fontSize = 14.sp
            )

            val context = LocalContext.current

            val helper = rememberInstallApkHelper(upgradeEntity = upgradeEntity)

            val launcher = rememberLauncherForActivityResult(contract = ActivityResultContracts.StartActivityForResult()) {
                if (hasPermission(context)) {
                    helper.apkFile?.also {
                        installApk(context, it)
                    } ?: run {
                        toastRes(R.string.cpd更新失败)
                    }
                } else {
                    toastRes(R.string.cpd没有未知来源安装权限_无法完成更新)
                }
            }

            Row(
                modifier = Modifier
                    .padding(top = 30.dp)
                    .fillMaxWidth(), horizontalArrangement = Arrangement.Center
            ) {
                if (helper.downloading) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(18.dp)
                            .clip(RoundedCornerShape(9.dp))
                            .background(Color(0xFF434343))
                    ) {
                        Spacer(
                            modifier = Modifier
                                .fillMaxWidth(helper.downloadProgress)
                                .fillMaxHeight()
                                .clip(RoundedCornerShape(9.dp))
                                .background(Color(0xFFFF5E8B))
                        )

                        Text(
                            text = stringResource(id = R.string.cpd已下载进度, helper.downloadProgress.times(100).toInt().coerceIn(0, 100)),
                            modifier = Modifier
                                .align(Alignment.CenterStart)
                                .padding(start = 12.dp),
                            color = Color.White,
                            fontSize = 14.sp,
                            lineHeight = 14.sp
                        )
                    }
                } else {
                    if (upgradeEntity.forceUpdate) {
                        AppButton(
                            text = stringResource(id = R.string.cpd立即升级),
                            modifier = Modifier
                                .widthIn(min = 184.dp)
                                .height(44.dp),
                            color = Color.White,
                            background = Color(0xFFFF5E8B),
                            contentPadding = PaddingValues(horizontal = 10.dp)
                        ) {
                            startAppUpgrade(upgradeEntity, context, helper, launcher)
                        }
                    } else {
                        AppButton(
                            text = stringResource(id = R.string.cpd暂不升级),
                            modifier = Modifier
                                .padding(start = 4.dp, end = 8.dp)
                                .weight(1f)
                                .height(44.dp),
                            color = Color(0xFF86909C),
                            fontSize = 16.sp,
                            maxLines = 2,
                            textStyle = TextStyle(textAlign = TextAlign.Center),
                            background = Color(0xFFF1F2F3),
                            contentPadding = PaddingValues(horizontal = 10.dp)
                        ) {
                            onClose()
                        }

                        AppButton(
                            text = stringResource(id = R.string.cpd立即升级),
                            modifier = Modifier
                                .padding(start = 8.dp, end = 4.dp)
                                .weight(1f)
                                .height(44.dp),
                            color = Color.White,
                            fontSize = 16.sp,
                            maxLines = 2,
                            textStyle = TextStyle(textAlign = TextAlign.Center),
                            background = Color(0xFFFF5E8B),
                            contentPadding = PaddingValues(horizontal = 10.dp)
                        ) {
                            startAppUpgrade(upgradeEntity, context, helper, launcher)
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(24.dp))
        }
    }
}

private fun startAppUpgrade(
    upgradeEntity: UpgradeEntity,
    context: Context,
    helper: InstallApkHelper,
    launcher: ManagedActivityResultLauncher<Intent, ActivityResult>,
) {
    if (isPlayChannel) {
        if (upgradeEntity.goStore) {
            AppStoreUtil.openAppStorePage(context)
        } else {
            context.startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(upgradeEntity.jumpLink)))
        }
    } else {
        helper.downloadApk(launcher)
    }
}

@Composable
private fun rememberInstallApkHelper(upgradeEntity: UpgradeEntity, context: Context = LocalContext.current): InstallApkHelper {
    val scope = rememberCoroutineScope()
    return remember(upgradeEntity, context, scope) {
        InstallApkHelper(upgradeEntity, context, scope)
    }

}

private class InstallApkHelper(
    private val upgradeEntity: UpgradeEntity,
    private val context: Context,
    private val scope: CoroutineScope,
) {

    var downloading by mutableStateOf(false)

    var downloadProgress by mutableFloatStateOf(0f)

    var apkFile: File? = null

    fun downloadApk(launcher: ManagedActivityResultLauncher<Intent, ActivityResult>) {
        scope.launch {
            apkFile = DownloadHelper.executeDownload(
                requestWrapper = DownloadHelper.createRequestWrapper(upgradeEntity.downloadUrl) {
                    priority(Priority.HIGH)
                },
                cancelDownloadIfCancelCoroutine = true
            ) {
                DownloadHelper.registerTaskByUrl(upgradeEntity.downloadUrl, object : DownloadCallback {

                    override fun onStart(downloadId: Int, totalBytes: Long) {
                        downloading = true
                    }

                    override fun onRetry(downloadId: Int) {
                        downloading = true
                    }

                    override fun onProgress(downloadId: Int, bytesWritten: Long, totalBytes: Long) {
                        downloadProgress = bytesWritten.toFloat().div(totalBytes)
                    }

                    override fun onSuccess(downloadId: Int, filepath: String) {
                        downloadProgress = 1f
                        downloading = false
                    }

                    override fun onFailure(downloadId: Int, statusCode: Int, errMsg: String?) {
                        downloading = false
                    }
                })
            }

            apkFile?.apply {
                if (hasPermission(context)) {
                    installApk(context, this)
                } else {
                    launcher.launch(Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES).apply {
                        data = Uri.parse("package:${BuildConfig.APPLICATION_ID}")
                    })
                }
            } ?: run {
                toastRes(R.string.cpd更新失败)
            }
        }
    }
}


private fun installApk(context: Context, file: File) {
    val intent = Intent(Intent.ACTION_VIEW)
    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
    //Android7.0以上
    val fileUri: Uri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        FileProvider.getUriForFile(context, BuildConfig.APPLICATION_ID + ".my_fileprovider", file)
    } else {
        Uri.fromFile(file)
    }
    intent.setDataAndType(fileUri, "application/vnd.android.package-archive")
    context.startActivity(intent)
}

private fun hasPermission(context: Context) = Build.VERSION.SDK_INT < Build.VERSION_CODES.O || context.packageManager.canRequestPackageInstalls()
