package com.qyqy.cupid.widgets

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import com.bumptech.glide.integration.compose.placeholder
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.ui.AnimatedComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.utils.OnClick

@Composable
fun AvatarComposeView(
    avatar: String,
    frame: String?,
    modifier: Modifier = Modifier,
    fillAvatarIfFrameIsEmpty: Boolean = true,
    onClick: OnClick = {},
    overlayContent: @Composable BoxScope.() -> Unit = {},
) {
    Box(
        modifier = modifier
            .aspectRatio(1f)
            .noEffectClickable(onClick = onClick),
        contentAlignment = Alignment.Center
    ) {

        Box(
            modifier = Modifier
                .fillMaxSize(
                    if (frame.isNullOrEmpty() && fillAvatarIfFrameIsEmpty) {
                        1f
                    } else {
                        0.7f
                    }
                )
                .clip(CircleShape)
        ) {
            ComposeImage(
                model = avatar,
                modifier = Modifier.fillMaxSize(),
                loading = placeholder(R.drawable.ic_default_male_unselected),
                failure = placeholder(R.drawable.ic_default_male_unselected),
                preview = painterResource(id = R.drawable.ic_default_male_selected),
            )

            overlayContent()
        }

        if (!frame.isNullOrEmpty()) {
            AnimatedComposeImage(
                model = frame,
                modifier = Modifier.fillMaxSize(),
            )
        }
    }
}

@Composable
fun AvatarComposeView(
    user: User,
    modifier: Modifier = Modifier,
    fillAvatarIfFrameIsEmpty: Boolean = true,
    onClick: OnClick = {},
    overlayContent: @Composable BoxScope.() -> Unit = {},
) {
    AvatarComposeView(
        avatar = user.avatarUrl,
        frame = user.avatarFrame,
        modifier = modifier,
        fillAvatarIfFrameIsEmpty = fillAvatarIfFrameIsEmpty,
        onClick = onClick,
        overlayContent = overlayContent
    )
}