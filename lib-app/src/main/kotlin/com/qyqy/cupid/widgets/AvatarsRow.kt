

package com.qyqy.cupid.widgets

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import com.qyqy.cupid.theme.CupidTheme
import com.qyqy.ucoo.compose.ui.ComposeImage

@Composable
fun AvatarsRow(avatars: List<String>, modifier: Modifier = Modifier, itemSize: Dp = 24.dp, offset: Int = 20) {
    Box(modifier = modifier) {
        avatars.forEachIndexed { index: Int, s: String ->
            Box(
                modifier = Modifier
                    .padding(start = (index * offset).dp)
                    .zIndex(-1f * index)
            ) {
                ComposeImage(
                    model = s, modifier = Modifier
                        .size(itemSize)
                        .clip(CircleShape)
                        .border(0.5.dp, Color.White, CircleShape)
                )
            }
        }
    }
}

@Preview
@Composable
private fun AvatarsRowPreview() {
    CupidTheme {
        AvatarsRow(avatars = listOf("", "", "", "", ""))
    }
}