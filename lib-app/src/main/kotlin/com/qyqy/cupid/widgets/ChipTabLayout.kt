package com.qyqy.cupid.widgets

import androidx.compose.animation.core.animateOffsetAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.PagerState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.ContentDrawScope
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.util.fastForEachIndexed
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.utils.ComposeContent
import com.qyqy.ucoo.utils.EntityCallback


@Composable
fun ChipTabRow(
    currentItem: Int,
    itemCount: Int,
    itemWidth: Int,
    modifier: Modifier = Modifier,
    onClick: (Int) -> Unit = {},
    drawActiveBg: ContentDrawScope.(Offset, Size) -> Unit = { _, _ -> },
    content: @Composable (Int, Boolean) -> Unit = { _, _ -> }
) {
    val density = LocalDensity.current
    val topLeft by animateOffsetAsState(targetValue = with(density) {
        Offset((itemWidth * currentItem).dp.toPx(), 0f)
    }, label = "offset")
    Row(modifier = modifier
        .drawWithContent {
            this.drawActiveBg(topLeft, this.size.copy(width = with(density) { itemWidth.dp.toPx() }))
            drawContent()
        }
        .width((itemCount * itemWidth).dp), horizontalArrangement = Arrangement.SpaceEvenly) {
        repeat(itemCount) {
            Box(
                modifier = Modifier
                    .clip(Shapes.chip)
                    .click {
                        onClick.invoke(it)
                    }
                    .weight(1f)
                    .fillMaxHeight(), contentAlignment = Alignment.Center
            ) {
                content(it, it == currentItem)
            }
        }
    }
}

@Preview
@Composable
private fun ChipTabRowPreview() {
    PreviewCupidTheme {
        ChipTabRow(
            currentItem = 1,
            itemCount = 2,
            itemWidth = 124,
            modifier = Modifier
                .background(Color.Gray, Shapes.chip)
                .height(36.dp),
            drawActiveBg = { offset, size ->
                drawRoundRect(
                    Color(0xFFFF6720),
                    topLeft = offset,
                    size = size,
                    cornerRadius = CornerRadius(this.size.height / 2f)
                )
            }
        ) { index, selected ->
            Text(
                text = when (index) {
                    0 -> "周榜"
                    else -> "周榜"
                }, color = when (selected) {
                    true -> Color.White
                    false -> Color.Black
                }
            )
        }
    }
}

@Composable
fun SimpleTabLayout(
    pagerState: PagerState,
    titles: List<String>,
    modifier: Modifier = Modifier,
    divider: ComposeContent = {},
    indicator: @Composable BoxScope.() -> Unit = {},
    onTabClick: EntityCallback<Int> = {},
    itemContent: @Composable (title: String, selected: Boolean) -> Unit
) {
    val currentPage = pagerState.fixCurrentPage
    val totalCount = pagerState.pageCount
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        titles.fastForEachIndexed { i, s ->
            Box(contentAlignment = Alignment.BottomCenter, modifier = Modifier.noEffectClickable {
                onTabClick(i)
            }) {
                itemContent.invoke(s, i == currentPage)
                if (i == currentPage) {
                    indicator()
                }
            }
            if (i < totalCount - 1) {
                divider()
            }
        }
    }
}


@Composable
fun <T> MultiTabs(
    tabs: List<T>,
    modifier: Modifier = Modifier,
    selectedIndex: Int = 0,
    horizontal: Arrangement.Horizontal = Arrangement.Center,
    itemContent: @Composable RowScope.(item: T, index: Int, isSelected: Boolean) -> Unit
) {
    key(selectedIndex) {
        Row(modifier = modifier, horizontalArrangement = horizontal, verticalAlignment = Alignment.CenterVertically) {
            tabs.forEachIndexed { index, t ->
                itemContent(t, index, selectedIndex == index)
            }
        }
    }
}