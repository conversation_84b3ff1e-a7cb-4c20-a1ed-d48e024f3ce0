

package com.qyqy.cupid.widgets

import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.AnimationVector1D
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.rotate
import androidx.compose.ui.graphics.drawscope.translate
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.imageResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.DialogProperties
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.overseas.common.sntp.SNTPManager
import com.qyqy.cupid.data.CoinWinnerConfigBean
import com.qyqy.cupid.data.CoinWinnerDetail
import com.qyqy.cupid.data.CoinWinnerPlayer
import com.qyqy.cupid.data.CoinWinnerState
import com.qyqy.cupid.data.CoinWinnerUser
import com.qyqy.cupid.model.CoinWinnerViewModel
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.cupid.ui.dialog.AnimatedDialog
import com.qyqy.cupid.ui.dialog.DialogQueue
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.LocalDialogQueue
import com.qyqy.cupid.ui.dialog.SimpleDialog
import com.qyqy.cupid.utils.ImageCache
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.compose.LaunchOnceEffect
import com.qyqy.ucoo.compose.enableClick
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.ui.AnyPopDialogProperties
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.IconLoading
import com.qyqy.ucoo.compose.ui.verticalScrollWithScrollbar
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.sUser
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch

//金币大赢家的动作
sealed interface GameAction {
    data class CREATE_GAME(val base_coin: Int) : GameAction
    data object CLOSE_GAME : GameAction
    data object START_GAME : GameAction
    data object JOIN_GAME : GameAction
    data object GO_ABOUT : GameAction
    data class RAISE_GAME(val coin: Int) : GameAction//加注

    data object CLOSE_WINDOW : GameAction
    data object HIDE_WINDOW : GameAction
}

//创建金币大赢家弹窗
object CoinWinnerPendingDialog : AnimatedDialog<IVoiceLiveAction>() {

    @Composable
    override fun Content(dialog: IDialog, onAction: IVoiceLiveAction?) {
        val viewModel = viewModel(modelClass = CoinWinnerViewModel::class)
        LaunchedEffect(key1 = Unit) {
            viewModel.refreshGameConfig()
        }
        val bean = viewModel.gameConfig.collectAsStateWithLifecycle()
        val dialogQueue = LocalDialogQueue.current
        CoinWinnerConfigWidget(bean.value) {
            when (it) {
                is GameAction.CREATE_GAME -> {
                    onAction?.onCoinWinnerCreate(base_coin = it.base_coin)
                    dialog.dismiss()
                }

                is GameAction.GO_ABOUT -> {
                    dialogQueue.push(CoinWinnerAboutDialog, true)
                }

                else -> {}
            }
        }
    }

}

object CoinWinnerAboutDialog : SimpleDialog() {

    @Composable
    override fun Content(dialog: IDialog) {
        val coinGameViewModel = viewModel(modelClass = CoinWinnerViewModel::class)
        LaunchedEffect(key1 = Unit) {
            coinGameViewModel.refreshGameConfig()
        }
        val gameConfig = coinGameViewModel.gameConfig.collectAsStateWithLifecycle()
        Column(
            modifier = Modifier
                .fillMaxWidth(0.72f)
                .height(320.dp)
                .background(color = Color.White, shape = RoundedCornerShape(8.dp))
                .padding(horizontal = 16.dp, vertical = 20.dp), horizontalAlignment = Alignment.CenterHorizontally
        ) {
            val descScrollState = rememberScrollState()
            Text(stringResource(id = R.string.cpd玩法规则), color = CpdColors.FF1D2129, fontSize = 17.sp)
            Spacer(modifier = Modifier.height(4.dp))
            Box(
                modifier = Modifier
                    .verticalScrollWithScrollbar(descScrollState)
                    .weight(1f)
            ) {
                Text(
                    gameConfig.value?.gameRule ?: "...", color = CpdColors.FF86909C
                )
            }
            AppButton(
                text = stringResource(id = R.string.cpd_iknow),
                background = CpdColors.FFFF5E8B,
                color = Color.White,
                fontSize = 16.sp,
                modifier = Modifier
                    .widthIn(min = 160.dp)
                    .padding(top = 12.dp)
                    .height(36.dp)
            ) {
                dialog.dismiss()
            }
        }
    }

}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun CoinWinnerConfigWidget(bean: CoinWinnerConfigBean?, onAction: (GameAction) -> Unit = { it -> }) {
    val selectedIdx = remember {
        mutableIntStateOf(0)
    }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(color = Color(0xff1f004c)),
    ) {
        if (bean == null) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(120.dp), contentAlignment = Alignment.Center
            ) {
                IconLoading()
            }
        } else {
            Box(
                modifier = Modifier.fillMaxWidth()
            ) {
                ComposeImage(
                    model = R.drawable.ic_winner_p_header, modifier = Modifier.fillMaxWidth(), contentScale = ContentScale.FillWidth
                )
                ComposeImage(model = R.drawable.cpd_winner_question, modifier = Modifier
                    .padding(14.dp)
                    .size(36.dp)
                    .padding(4.dp)
                    .click {
                        onAction(GameAction.GO_ABOUT)
                    })
            }
            Text(stringResource(id = R.string.cpd最低入场金币), color = Color.White, modifier = Modifier.padding(top = 20.dp, start = 16.dp))


            AnimatedVisibility(visible = bean.baseCoins.isNotEmpty()) {
                FlowRow(
                    modifier = Modifier
                        .padding(top = 4.dp)
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp, vertical = 12.dp),
                    verticalArrangement = Arrangement.spacedBy(6.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    maxItemsInEachRow = 3
                ) {
                    bean.baseCoins.forEachIndexed { index, value ->
                        Row(
                            modifier = Modifier
                                .weight(1f)
                                .height(36.dp)
                                .background(
                                    color = if (index == selectedIdx.intValue) Color(0xFF945EFF) else Color(0xFF412667),
                                    shape = RoundedCornerShape(8.dp)
                                )
                                .click {
                                    selectedIdx.intValue = index
                                }, horizontalArrangement = Arrangement.Center, verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(value.toString(), color = Color.White, fontSize = 16.sp, lineHeight = 16.sp)
                            ComposeImage(
                                model = R.drawable.ic_cpd_coin, modifier = Modifier
                                    .padding(start = 6.dp)
                                    .size(14.67.dp)
                            )
                        }
                    }
                }
            }

            AppButton(
                text = stringResource(id = R.string.cpd召唤参与者),
                modifier = Modifier
                    .padding(32.dp)
                    .fillMaxWidth(),
                background = Color(0xFF945EFF),
                color = Color.White,
                contentPadding = PaddingValues(horizontal = 4.dp),
                onClick = {
                    val coins = bean.baseCoins
                    onAction(GameAction.CREATE_GAME(if (coins.size <= selectedIdx.intValue) 0 else coins[selectedIdx.intValue]))
                },
            )
        }
    }
}


//创建金币大赢家弹窗
class CoinWinnerWidgetDialog(private val coinWinnerViewModel: CoinWinnerViewModel) : SimpleDialog() {

    override val isFull: Boolean = true

    override val properties: DialogProperties = DialogProperties(dismissOnBackPress = false)

    @Composable
    override fun Content(dialog: IDialog) {
        val dialogQueue = LocalDialogQueue.current as? DialogQueue<IVoiceLiveAction>
        val isShowing = coinWinnerViewModel.isGameShowing
        val data = coinWinnerViewModel.gameDetail.collectAsStateWithLifecycle().value
        if (!isShowing || data == null) {
            LaunchedEffect(Unit) {
                dialog.dismiss()
            }
            return
        }

        DisposableEffect(key1 = Unit) {
            onDispose {
                coinWinnerViewModel.isGameShowing = false
            }
        }

        CoinWinnerWidget(data) { action ->
            when (action) {
                GameAction.CLOSE_GAME -> {
                    coinWinnerViewModel.closeGame()
                }

                GameAction.START_GAME -> {
                    coinWinnerViewModel.startGame()
                }

                GameAction.JOIN_GAME -> {
                    coinWinnerViewModel.joinGame()
                }

                GameAction.CLOSE_WINDOW -> {
                    val coinWinnerDetail = coinWinnerViewModel.gameDetail.value
                    val ownerId = coinWinnerDetail?.round?.owner?.userid ?: -1
                    coinWinnerViewModel.closeWindow()
                    if (ownerId == sUser.userId) {
                        dialogQueue?.push(CoinWinnerPendingDialog)
                    }
                }

                GameAction.HIDE_WINDOW -> {
                    coinWinnerViewModel.isGameShowing = false
                }

                GameAction.GO_ABOUT -> {
                    dialogQueue?.push(CoinWinnerAboutDialog, true)
                }

                is GameAction.RAISE_GAME -> {
                    coinWinnerViewModel.raiseGame(action.coin)
                }

                else -> Unit
            }
        }
    }

}

/**
 * 金币大赢家本体
 * 状态:
 *   Pending(1),//等待玩家加入
 *      此状态没有时间限制,在不超过2人的情况下会一直存在
 *   Waiting(2),//等待游戏开始
 *      参加的人超过2人时候进入等待游戏开始的状态, 这个状态需要持续30s, 收到这个状态时候需要携带时间戳一个, 根据时间戳来计算还需要倒计时多少
 *      在此状态下, 可以通过 立即开始 和 倒计时结束 两种事件来进入下一个Playing状态
 *   Playing(3),//进行中
 *      在此状态下转盘需要一直转动, 在收到japan_coin_pk_player_lose时间后, 显示谁被淘汰了, 紧接着进入Raising加注状态
 *   Raising(4),//加注中
 *   Finished(10),//结束
 *   Abort(20)
 *
 *
 */
@Composable
fun CoinWinnerWidget(
    info: CoinWinnerDetail,
    onAction: (GameAction) -> Unit = {},
) {
    if (info.round.status == CoinWinnerState.Abort.status || info.round.getGameStatus() == CoinWinnerState.Unknown) {
        return
    }
    val turntableWidthRatio = 0.86f

    //转盘旋转动画
    val turntableAnim = remember { Animatable(0f) }

    ConstraintLayout(
        modifier = Modifier
            .fillMaxSize()
            .background(color = Color(0x4D000000))
            .enableClick()
    ) {
        val (turntable, headerImg, timer, topOperationBox, raiseHeader, userinfo, buttonBox) = createRefs()

        CoinTurntableWidget(info, turntableAnim,
            Modifier
                .constrainAs(turntable) {
                    top.linkTo(parent.top, 204.dp)
                    centerHorizontallyTo(parent)
                }
                .fillMaxWidth(turntableWidthRatio)
                .aspectRatio(1f))

        Column(modifier = Modifier
            .constrainAs(buttonBox) {
                top.linkTo(turntable.bottom)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            }
            .padding(top = 16.dp), horizontalAlignment = Alignment.CenterHorizontally) {
            val buttonTransition = rememberInfiniteTransition(label = "button_anim")
            val buttonTraction by buttonTransition.animateFloat(
                initialValue = 1.05f, targetValue = 0.9f, animationSpec = infiniteRepeatable(
                    tween(200, easing = LinearEasing), repeatMode = RepeatMode.Reverse
                ), label = "button_animation"
            )

            val transition = rememberInfiniteTransition(label = "active_anim")
            val fraction by transition.animateFloat(
                initialValue = 0f, targetValue = 30f, animationSpec = infiniteRepeatable(
                    tween(200, easing = LinearEasing), repeatMode = RepeatMode.Reverse
                ), label = "background"
            )

            //任何人都可以进行下注
            val gameStatus = info.round.getGameStatus()
            if (gameStatus == CoinWinnerState.Pending || gameStatus == CoinWinnerState.Waiting) {
                if (info.round.owner.userid != sUser.userId) {
                    Text(
                        stringResource(id = R.string.cpd至少2人可以开始),
                        color = Color.White,
                        fontSize = 14.sp,
                        lineHeight = 14.sp,
                        modifier = Modifier.padding(vertical = 16.dp)
                    )
                }

                if (info.contains(sUser.userId)) {
                    Row(
                        modifier = Modifier
                            .paint(painterResource(id = R.drawable.cpd_winner_grey_button))
                            .size(211.dp, 64.dp)
                            .padding(start = 8.dp, bottom = 22.dp),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            stringResource(id = R.string.cpd参加过),
                            color = Color.White,
                            fontSize = 16.sp,
                            lineHeight = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                } else {
                    //加入游戏
                    Row(
                        modifier = Modifier
                            .paint(painterResource(id = R.drawable.cpd_winner_yellow_button))
                            .size(211.dp, 64.dp)
                            .padding(start = 8.dp, bottom = 22.dp)
                            .click {
                                onAction(GameAction.JOIN_GAME)
                            }, horizontalArrangement = Arrangement.Center, verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            stringResource(id = R.string.cpd参加需要金币, info.round.baseCoin),
                            color = Color.White,
                            fontSize = 16.sp,
                            lineHeight = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                        ComposeImage(
                            model = R.drawable.ic_cpd_coin, modifier = Modifier
                                .padding(start = 6.dp)
                                .size(20.dp)
                        )
                    }
                }

                //只有房主才可以进行游戏开始和结束
                if (info.round.owner.userid == sUser.userId) {
                    Text(
                        stringResource(id = R.string.cpd至少2人可以开始),
                        color = Color.White,
                        fontSize = 14.sp,
                        lineHeight = 14.sp,
                        modifier = Modifier.padding(vertical = 16.dp)
                    )
                    Row {
                        ComposeImage(model = R.drawable.cpd_winner_close, Modifier.click {
                            onAction(GameAction.CLOSE_GAME)
                        })
                        Spacer(modifier = Modifier.width(13.dp))
                        if (info.players.size >= 2) {
                            ComposeImage(model = R.drawable.cpd_winner_start_active,
                                Modifier
                                    .scale(buttonTraction)
                                    .click {
                                        onAction(GameAction.START_GAME)
                                    })
                        } else {
                            ComposeImage(model = R.drawable.cpd_winner_start_inactive)
                        }

                    }
                }
            }
            if (gameStatus == CoinWinnerState.Raising && info.contains(sUser.userId)) {
                //只有通过上一轮的才可以进行加注?
                //加注
                (info.players.find { it.status < 3 && it.player.userid == sUser.userId })?.let { player ->
                    Box {
                        Row(verticalAlignment = Alignment.Bottom, modifier = Modifier.padding(bottom = 32.dp)) {
                            Box(
                                modifier = Modifier
                                    .size(10.dp, 48.dp)
                                    .background(
                                        color = Color.Transparent, shape = RoundedCornerShape(6.dp)
                                    )
                                    .border(0.5.dp, color = Color.White, shape = RoundedCornerShape(6.dp))
                            ) {
                                Spacer(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .height((info.getUserPercent(sUser.userId) * 48).dp)
                                        .background(
                                            color = Color(0xFFFE9A1E), shape = RoundedCornerShape(bottomStart = 6.dp, bottomEnd = 6.dp)
                                        )
                                        .align(Alignment.BottomCenter)
                                )
                            }
                            Column(
                                modifier = Modifier
                                    .padding(start = 4.dp, end = 21.dp)
                                    .height(48.dp), verticalArrangement = Arrangement.SpaceAround
                            ) {
                                Text(
                                    stringResource(id = R.string.cpd胜率),
                                    fontSize = 12.sp,
                                    lineHeight = 12.sp,
                                    color = Color.White,
                                    fontWeight = FontWeight.SemiBold
                                )
                                Text(
                                    "${(info.getUserPercent(sUser.userId) * 100).toInt()}%",
                                    fontSize = 16.sp,
                                    lineHeight = 16.sp,
                                    color = Color.White,
                                    fontWeight = FontWeight.SemiBold
                                )
                            }
                            info.raiseCoins.forEachIndexed { index, coin ->
                                if (index == info.raiseCoins.size - 1) {
                                    Column(horizontalAlignment = Alignment.CenterHorizontally,
                                        modifier = Modifier
                                            .scale(buttonTraction)
                                            .click(time = 200) {
                                                onAction(GameAction.RAISE_GAME(coin))
                                            }) {
                                        ComposeImage(model = R.drawable.cpd_winner_raise_tips)
                                        Row(
                                            modifier = Modifier
                                                .padding(top = 4.dp)
                                                .paint(painterResource(id = R.drawable.cpd_winner_raise_multi))
                                                .padding(start = 8.dp, end = 8.dp, bottom = 22.dp),
                                            horizontalArrangement = Arrangement.Center,
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Text(
                                                "+ ${info.round.baseCoin * 10}",
                                                color = Color.White,
                                                fontSize = 16.sp,
                                                lineHeight = 16.sp,
                                                fontWeight = FontWeight.Medium
                                            )
                                            ComposeImage(
                                                model = R.drawable.ic_cpd_coin, modifier = Modifier
                                                    .padding(start = 2.dp)
                                                    .size(20.dp)
                                            )
                                        }
                                    }
                                } else {
                                    Row(
                                        modifier = Modifier
                                            .padding(end = 8.dp)
                                            .paint(painterResource(id = R.drawable.cpd_winner_raise_single))
                                            .padding(start = 8.dp, end = 8.dp, bottom = 22.dp)
                                            .click(time = 200) {
                                                onAction(GameAction.RAISE_GAME(coin))
                                            }, horizontalArrangement = Arrangement.Center, verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            "+ ${info.round.baseCoin}",
                                            color = Color.White,
                                            fontSize = 16.sp,
                                            lineHeight = 16.sp,
                                            fontWeight = FontWeight.Medium
                                        )
                                        ComposeImage(
                                            model = R.drawable.ic_cpd_coin, modifier = Modifier
                                                .padding(start = 2.dp)
                                                .size(20.dp)
                                        )
                                    }
                                }
                            }
                        }

                        Box(
                            modifier = Modifier
                                .align(Alignment.BottomEnd)
                                .rotate(fraction)
                        ) {
                            ComposeImage(model = R.drawable.cpd_winner_raise_handle)
                        }

                    }
                }
            }
        }
        if ((info.round.status == CoinWinnerState.Finished.status || (info.round.status == CoinWinnerState.Rnnouncing.status && info.loser?.userid == sUser.userId)) && !turntableAnim.isRunning) {
            Spacer(
                modifier = Modifier
                    .background(color = Color(0x99000000))
                    .fillMaxSize()
            )
        }

        ComposeImage(model = R.drawable.cpd_winner_title_header,
            modifier = Modifier
                .height(96.dp)
                .constrainAs(headerImg) {
                    top.linkTo(turntable.top, margin = -48.dp)
                    centerHorizontallyTo(turntable)
                }
                .alpha(if (info.round.status == CoinWinnerState.Finished.status && info.round.finalReward > 0 && !turntableAnim.isRunning) 0.3f else 1f),
            contentScale = ContentScale.FillHeight)

        Box(modifier = Modifier
            .fillMaxWidth(0.86f)
            .constrainAs(topOperationBox) {
                top.linkTo(headerImg.top)
                centerHorizontallyTo(turntable)
            }) {
            //左上角说明
            ComposeImage(model = R.drawable.cpd_winner_question,
                modifier = Modifier
                    .padding(top = 20.dp)
                    .size(28.dp)
                    .align(Alignment.TopStart)
                    .click {
                        onAction(GameAction.GO_ABOUT)
                    })

            //右上角关闭或收起
            if (info.round.status == CoinWinnerState.Finished.status) {
                ComposeImage(model = R.drawable.cpd_winner_close_circle,
                    modifier = Modifier
                        .padding(top = 20.dp)
                        .size(28.dp)
                        .align(Alignment.TopEnd)
                        .click {
                            onAction(GameAction.CLOSE_WINDOW)
                        })
            } else {
                ComposeImage(model = R.drawable.cpd_winner_hide, modifier = Modifier
                    .padding(top = 20.dp)
                    .size(28.dp)
                    .align(Alignment.TopEnd)
                    .click {
                        onAction(GameAction.HIDE_WINDOW)
                    })
            }
        }
        if (info.round.status == CoinWinnerState.Raising.status) {
            ComposeImage(model = R.drawable.cpd_winner_raise_header, modifier = Modifier.constrainAs(raiseHeader) {
                bottom.linkTo(headerImg.top, 32.dp)
                centerHorizontallyTo(parent)
            })
        }
        if (!turntableAnim.isRunning) {
            if (info.round.getGameStatus() == CoinWinnerState.Rnnouncing) {//显示输家
                info.loser?.let { user ->
                    if (user.userid == sUser.userId) {
                        UserResultWidget(user = user, rewardCoin = 0, modifier = Modifier
                            .fillMaxWidth()
                            .constrainAs(userinfo) {
                                centerHorizontallyTo(turntable)
                                centerVerticallyTo(turntable)
                            })
                    }
                }
            } else if (info.round.getGameStatus() == CoinWinnerState.Finished) {//显示赢家
                info.winner?.let { user ->
                    UserResultWidget(user = user,
                        rewardCoin = info.reward_coin ?: info.round.finalReward,
                        modifier = Modifier
                            .fillMaxWidth()
                            .constrainAs(userinfo) {
                                top.linkTo(headerImg.top)
                                bottom.linkTo(turntable.bottom)
                            })
                }
            }
        }

        //倒计时
        TimeIntervalWidget(info = info, modifier = Modifier.constrainAs(timer) {
            top.linkTo(headerImg.top, -18.dp)
            centerHorizontallyTo(headerImg)
        })
    }
}


//region 各类组件

/**
 * 转盘结果组件
 *
 * @param user 赢家或输家?
 * @param rewardCoin >0时是赢家,<=0时是输家
 * @param paddingTop 距离顶部的距离, 因为赢家和输家的布局又不一样, 一定要加一个padding
 * @param modifier
 */
@Composable
private fun UserResultWidget(user: CoinWinnerUser, rewardCoin: Int, modifier: Modifier = Modifier) {
    if (rewardCoin <= 0) {
        Column(
            modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally, verticalArrangement = Arrangement.Center
        ) {
            Text(user.nickname, color = Color.White, fontSize = 18.sp, lineHeight = 18.sp, fontWeight = FontWeight.Medium)
            Box(
                modifier = Modifier
                    .padding(top = 10.dp, bottom = 20.dp)
                    .width(200.dp)

            ) {
                ComposeImage(
                    model = user.avatarUrl,
                    modifier = Modifier
                        .size(120.dp)
                        .align(Alignment.TopCenter)
                        .clip(CircleShape)
                        .border(2.dp, color = Color.White, shape = CircleShape)
                )
                ComposeImage(
                    model = R.drawable.cpd_winner_failure, modifier = Modifier.align(Alignment.BottomEnd)
                )
            }
            Text(stringResource(id = R.string.cpd进入下一轮), color = Color.White, fontSize = 14.sp, lineHeight = 14.sp)
        }
    } else {
        Box(modifier = modifier) {
            Box(
                modifier = Modifier
                    .paint(
                        painterResource(id = R.drawable.cpd_winner_succeed_body), contentScale = ContentScale.FillWidth
                    )
                    .align(Alignment.Center)
            ) {
                ComposeImage(
                    model = user.avatarUrl,
                    modifier = Modifier
                        .align(Alignment.Center)
                        .size(120.dp)
                        .clip(CircleShape)
                        .border(2.dp, color = Color.White, shape = CircleShape)
                )
            }

            ComposeImage(
                model = R.drawable.cpd_winner_succeed_header, modifier = Modifier.align(Alignment.TopCenter)
            )

            Column(
                modifier = Modifier
                    .align(Alignment.Center)
                    .padding(top = 256.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(user.nickname, color = Color.White, fontSize = 18.sp, lineHeight = 18.sp, fontWeight = FontWeight.Medium)
                Spacer(modifier = Modifier.height(16.dp))
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text("+${rewardCoin}", color = Color(0xFFFFE607), fontSize = 20.sp, lineHeight = 20.sp, fontWeight = FontWeight.SemiBold)
                    ComposeImage(model = R.drawable.ic_cpd_coin)
                }
            }
        }
    }
}

/**
 * 倒计时组件
 */

@Composable
private fun TimeIntervalWidget(info: CoinWinnerDetail, modifier: Modifier = Modifier) {
    val scope = rememberCoroutineScope()
    val waitingSeconds by remember {
        mutableStateOf(
            listOf(
                R.drawable.cpd_winner_time_1,
                R.drawable.cpd_winner_time_2,
                R.drawable.cpd_winner_time_3,
                R.drawable.cpd_winner_time_4,
                R.drawable.cpd_winner_time_5,
                R.drawable.cpd_winner_time_6,
                R.drawable.cpd_winner_time_7,
                R.drawable.cpd_winner_time_8,
                R.drawable.cpd_winner_time_9,
                R.drawable.cpd_winner_time_10,
                R.drawable.cpd_winner_time_11,
                R.drawable.cpd_winner_time_12,
                R.drawable.cpd_winner_time_13,
                R.drawable.cpd_winner_time_14,
                R.drawable.cpd_winner_time_15,
                R.drawable.cpd_winner_time_16,
                R.drawable.cpd_winner_time_17,
                R.drawable.cpd_winner_time_18,
                R.drawable.cpd_winner_time_19,
                R.drawable.cpd_winner_time_20,
                R.drawable.cpd_winner_time_21,
                R.drawable.cpd_winner_time_22,
                R.drawable.cpd_winner_time_23,
                R.drawable.cpd_winner_time_24,
                R.drawable.cpd_winner_time_25,
                R.drawable.cpd_winner_time_26,
                R.drawable.cpd_winner_time_27,
                R.drawable.cpd_winner_time_28,
                R.drawable.cpd_winner_time_29,
                R.drawable.cpd_winner_time_30
            )
        )
    }
    val raisingSeconds by remember {
        mutableStateOf(
            listOf(
                R.drawable.cpd_winner_time_red_1,
                R.drawable.cpd_winner_time_red_2,
                R.drawable.cpd_winner_time_red_3,
                R.drawable.cpd_winner_time_red_4,
                R.drawable.cpd_winner_time_red_5,
                R.drawable.cpd_winner_time_red_6,
                R.drawable.cpd_winner_time_red_7,
            )
        )
    }

    val timeFontSizeAnim = remember { Animatable(70f) }

    val remainSecond = remember {
        mutableStateOf(0)
    }
    if ((remainSecond.value in 1..waitingSeconds.size && info.round.getGameStatus() == CoinWinnerState.Waiting) || // waiting下最后5s是红色
        (remainSecond.value in 1..raisingSeconds.size && info.round.getGameStatus() == CoinWinnerState.Raising) //raising下最后5s是绿色
    ) {
        Box(
            modifier = modifier.size(80.dp, 60.dp)
        ) {
            ComposeImage(
                model = if (info.round.getGameStatus() == CoinWinnerState.Waiting) waitingSeconds[remainSecond.value - 1]
                else if (info.round.getGameStatus() == CoinWinnerState.Raising) raisingSeconds[remainSecond.value - 1]
                else "", modifier = Modifier
                    .align(Alignment.Center)
                    .width(timeFontSizeAnim.value.dp), contentScale = ContentScale.FillWidth
            )
        }
    }

    var timeJob by remember {
        mutableStateOf<Job?>(null)
    }

    LaunchedEffect(info.round.status) {
        val remainTime = (info.round.currentStageEndTimestamp - (SNTPManager.now() / 1000)).toInt()
        when (info.round.getGameStatus()) {
            CoinWinnerState.Waiting -> { //等待30s
                timeJob?.cancel()
                timeJob = null
                if (timeFontSizeAnim.isRunning) {
                    timeFontSizeAnim.stop()
                }
                remainSecond.value = if (remainTime < 0) 30 else remainTime
                timeJob = scope.launch {
                    while (remainSecond.value >= 0) {
                        timeFontSizeAnim.snapTo(70f)
                        timeFontSizeAnim.animateTo(1f, tween(1000, easing = FastOutSlowInEasing))
                        remainSecond.value -= 1
                    }
                    remainSecond.value = -1
                    timeJob = null
                }
            }

            CoinWinnerState.Raising -> { //等待5s
                timeJob?.cancel()
                timeJob = null
                remainSecond.value = if (remainTime < 0) 5 else remainTime
                if (timeFontSizeAnim.isRunning) {
                    timeFontSizeAnim.stop()
                }
                timeJob = scope.launch {
                    while (remainSecond.value >= 0) {
                        timeFontSizeAnim.snapTo(70f)
                        timeFontSizeAnim.animateTo(20f, tween(1000, easing = FastOutSlowInEasing))
                        remainSecond.value -= 1
                    }
                    timeJob = null
                }
            }

            CoinWinnerState.Rnnouncing, CoinWinnerState.Finished -> {
                timeJob?.cancel()
                timeJob = null
                remainSecond.value = 0
            }

            else -> {

            }
        }
    }
}

/**
 * 金币转盘组件
 *
 * @param info 金币大赢家信息
 * @param turntableAnim 转盘动画, 外部传入, 方便在外部进行控制
 * @param modifier
 */
@Composable
private fun CoinTurntableWidget(
    info: CoinWinnerDetail,
    turntableAnim: Animatable<Float, AnimationVector1D>,
    modifier: Modifier = Modifier,
) {
    //固定的颜色
    val colors = remember {
        mutableStateOf(
            listOf(
                Color(0xFFA04AFB),
                Color(0xFFFE729A),
                Color(0xFFFBA93F),
                Color(0xFFFED234),
                Color(0xFFC7E918),
                Color(0xFF78D428),
                Color(0xFF81FEF6),
                Color(0xFF2CA6FF),
                Color(0xff00Aaaa),
            )
        )
    }

    //region 转盘背景切换
    val borderTransition = rememberInfiniteTransition(label = "active_anim")
    val borderValue by borderTransition.animateFloat(
        initialValue = 0f, targetValue = 1f, animationSpec = infiniteRepeatable(
            tween(1000, easing = LinearEasing), repeatMode = RepeatMode.Restart
        ), label = "border"
    )
    //endregion

    val image = ImageBitmap.imageResource(id = R.drawable.cpd_winner_pointer)
    val context = LocalContext.current
    val pxValue = with(LocalDensity.current) { 48.dp.toPx() }

    LaunchOnceEffect("${info.round.startGameTimestamp}-${info.round.status}") {
        when (info.round.getGameStatus()) {
            CoinWinnerState.Rnnouncing -> {//中途了, 需要显示loser
                val loser = info.loser ?: return@LaunchOnceEffect
                turntableAnim.stop()
                turntableAnim.snapTo(turntableAnim.value % 360)

                val setion = info.getUserAngle(loser)
                turntableAnim.animateTo(
                    2160 - (setion.first + (setion.second - setion.first) / 2f),
                    tween(Math.max((info.getRotationTime() / 2), 4) * 1000, easing = LinearOutSlowInEasing)
                )
            }

            CoinWinnerState.Finished -> { //结束了,需要显示winner
                val winner = info.winner ?: return@LaunchOnceEffect
                turntableAnim.stop()
                turntableAnim.snapTo(turntableAnim.value % 360)

                val setion = info.getUserAngle(winner)
                turntableAnim.animateTo(
                    2160 - (setion.first + (setion.second - setion.first) / 2f),
                    tween(Math.max((info.getRotationTime() / 2), 4) * 1000, easing = LinearOutSlowInEasing)
                )
            }

            else -> {
                if (turntableAnim.value != 0f) {
                    turntableAnim.stop()
                    turntableAnim.snapTo(0f)
                }
            }
        }
    }

    //转盘
    Box(modifier = modifier
        .drawWithCache {
            onDrawBehind {
                val radius = (this.size.width) / 2
                //画底部
                var curProgress = 0f
                rotate(turntableAnim.value) {
                    if (info.players.isEmpty()) {
                        drawArc(colors.value[0], 0f, 360f, true)
                    } else {
                        val availablePlayers = info.getAvailablePlayers()
                        val angle = 360f / availablePlayers.size
                        availablePlayers.forEachIndexed { index, value ->
                            if (angle > 0f) {
                                drawArc(colors.value[index], curProgress - 90f, angle, true)
                                rotate(curProgress + angle / 2) {
                                    val bmp = ImageCache.getBitmap(context, value.player.avatarUrl, pxValue).value?.asImageBitmap()
                                    bmp?.let {
                                        translate(left = radius - it.width / 2f, radius * 0.32f) {
                                            rotate(-(curProgress + angle / 2), Offset(it.width / 2f, it.height / 2f)) {
                                                drawCircle(
                                                    color = Color.White,
                                                    it.width / 2 + 2f,
                                                    center = Offset(it.width / 2f, it.height / 2f),
                                                    style = Stroke(2.dp.toPx())
                                                )
                                                drawImage(it)
                                            }
                                        }
                                    }
                                }
                                curProgress += angle
                            }
                        }
                    }
                }
                //画指针
                drawImage(
                    image, Offset(
                        radius - image.width / 2, radius - image.height / 2 - 10.dp.toPx()
                    )
                )
            }
        }
        .paint(
            painterResource(
                id = if (borderValue < 0.5f) R.drawable.cpd_winner_turntable_circle1 else R.drawable.cpd_winner_turntable_circle2
            ), contentScale = ContentScale.FillBounds
        )) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.Center), horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_cpd_coin),
                contentDescription = "",
                modifier = Modifier
                    .padding(bottom = 10.dp)
                    .size(15.dp)
            )
            AutoSizeText(
                text = info.round.finalReward.toString(),
                modifier = Modifier
                    .size(56.dp, 20.dp)
                    .wrapContentWidth(Alignment.CenterHorizontally)
                    .sizeIn(maxWidth = 56.dp, maxHeight = 20.dp),
                color = Color(0xfffff500),
                style = TextStyle(textAlign = TextAlign.Center),
                fontSize = 18.sp
            )
//            Text(info.round.finalReward.toString(), fontSize = 18.sp, lineHeight = 18.sp, color = Color(0xfffff500))
        }
//        Image(
//            painter = painterResource(
//                id = if (borderValue < 0.5f)
//                    R.drawable.cpd_winner_turntable_circle1 else R.drawable.cpd_winner_turntable_circle2
//            ),
//            contentDescription = "",
//            contentScale = ContentScale.FillBounds,
//            modifier = Modifier.fillMaxWidth()
//        )
    }
}

//endregion

@Composable
@Preview
private fun PreviewCoinWinnerConfigWidget() {
    PreviewCupidTheme {
        CoinWinnerConfigWidget(null)
    }
}

@Composable
@Preview
private fun PreviewCoinWinnerWidget() {
//    val data = sAppJson.decodeFromString<CoinWinnerDetail>(
//        " {\"round\": {\"id\": 257, \"owner\": {\"userid\": 2574, \"public_id\": \"103419\", \"nickname\": \"\\u8001\\u7239\", \"avatar_url\": \"https://s.test.ucoofun.com/aacemD?x-oss-process=image/format,webp\", \"gender\": 1, \"age\": 11, \"height\": 170, \"avatar_frame\": \"https://s.ucoofun.com/aabLdz\", \"medal\": null, \"medal_list\": [], \"level\": 25, \"country_flag\": \"https://media.ucoofun.com/opsite%2Fcountryflag%2FL_slices%2FJP.png\", \"exp_level_info\": {\"charm_level\": 7, \"wealth_level\": 10}}, \"audioroom\": {\"id\": 192, \"title\": \"j11\\u6d4b\\u8bd5\\u6539\\u6807\\u989811\", \"room_locked\": false, \"text_only\": false}, \"base_coin\": 100, \"winner\": {\"userid\": 2574, \"public_id\": \"103419\", \"nickname\": \"\\u8001\\u7239\", \"avatar_url\": \"https://s.test.ucoofun.com/aacemD?x-oss-process=image/format,webp\", \"gender\": 1, \"age\": 11, \"height\": 170, \"avatar_frame\": \"https://s.ucoofun.com/aabLdz\", \"medal\": null, \"medal_list\": [], \"level\": 25, \"country_flag\": \"https://media.ucoofun.com/opsite%2Fcountryflag%2FL_slices%2FJP.png\", \"exp_level_info\": {\"charm_level\": 7, \"wealth_level\": 10}}, \"total_reward\": 300, \"owner_reward\": 30, \"final_reward\": 240, \"fee\": 30, \"status\": 10, \"start_game_timestamp\": 1732620937, \"end_game_timestamp\": 1732620964, \"stage_num\": 2, \"current_stage_start_timestamp\": 1732620959, \"current_stage_end_timestamp\": 1732620959}, \"players\": [{\"player\": {\"userid\": 1400, \"public_id\": \"101852\", \"nickname\": \"\\u70b9\\u51fb\", \"avatar_url\": \"https://s.test.ucoofun.com/aaceo4?x-oss-process=image/format,webp\", \"gender\": 1, \"age\": 24, \"height\": 0, \"avatar_frame\": \"https://s.ucoofun.com/aabLcV\", \"medal\": null, \"medal_list\": [{\"icon\": \"https://s.ucoofun.com/aabLeE\", \"width\": 72, \"height\": 24}], \"level\": 57, \"country_flag\": \"https://media.ucoofun.com/opsite%2Fcountryflag%2FL_slices%2FCN.png\", \"exp_level_info\": {\"charm_level\": 8, \"wealth_level\": 25}}, \"bet_coin\": 100, \"win_coin\": 0, \"status\": 4, \"bet_detail\": [100], \"lose_stage_num\": 2}, {\"player\": {\"userid\": 2574, \"public_id\": \"103419\", \"nickname\": \"\\u8001\\u7239\", \"avatar_url\": \"https://s.test.ucoofun.com/aacemD?x-oss-process=image/format,webp\", \"gender\": 1, \"age\": 11, \"height\": 170, \"avatar_frame\": \"https://s.ucoofun.com/aabLdz\", \"medal\": null, \"medal_list\": [], \"level\": 25, \"country_flag\": \"https://media.ucoofun.com/opsite%2Fcountryflag%2FL_slices%2FJP.png\", \"exp_level_info\": {\"charm_level\": 7, \"wealth_level\": 10}}, \"bet_coin\": 100, \"win_coin\": 240, \"status\": 3, \"bet_detail\": [100], \"lose_stage_num\": 0}, {\"player\": {\"userid\": 2584, \"public_id\": \"103442\", \"nickname\": \"\\u6d4b\\u8bd5\\u4efb\\u52a1\", \"avatar_url\": \"https://s.test.ucoofun.com/aaceqj?x-oss-process=image/format,webp\", \"gender\": 2, \"age\": 24, \"height\": 170, \"avatar_frame\": \"\", \"medal\": null, \"medal_list\": [], \"level\": 2, \"country_flag\": \"https://media.ucoofun.com/opsite%2Fcountryflag%2FL_slices%2FJP.png\", \"exp_level_info\": {\"charm_level\": 23, \"wealth_level\": 1}}, \"bet_coin\": 100, \"win_coin\": 0, \"status\": 4, \"bet_detail\": [100], \"lose_stage_num\": 1}], \"raise_coins\": [100, 1000]}"
//    )
//    CoinWinnerWidget(data.copy(round = data.round.copy(status = CoinWinnerState.Raising.status)))

    CoinWinnerWidget(CoinWinnerDetail(
        round = CoinWinnerDetail.Round(status = CoinWinnerState.Rnnouncing.status), raiseCoins = listOf(100, 1000), players = listOf(
            CoinWinnerPlayer(CoinWinnerUser(userid = 1), 100, 100, 1, listOf(1)),
            CoinWinnerPlayer(CoinWinnerUser(userid = 2), 100, 100, 1, listOf(1)),
        )
    ).apply {
//            loser = CoinWinnerUser(userid = 1)
//            winner = CoinWinnerUser(userid = 1)
    })

//    val turntableTop = 56.dp
//    val turntableWidthRatio = 0.86f
//    val context = LocalContext.current
//    val displayMetrics = context.resources.displayMetrics
//    val turntableHeight = ((displayMetrics.widthPixels * turntableWidthRatio) / (displayMetrics.densityDpi.toFloat() / DisplayMetrics.DENSITY_DEFAULT)).dp
//    val turntableAnim = remember { Animatable(0f) }
//
//    CoinTurntableWidget(
//        CoinWinnerDetail(
//            round = CoinWinnerDetail.Round(status = CoinWinnerState.Finished.status),
//            raiseCoins = listOf(100, 1000),
//            players = listOf(
//                CoinWinnerPlayer(CoinWinnerUser(userid = 1), 100, 100, 1, listOf(1)),
//                CoinWinnerPlayer(CoinWinnerUser(userid = 2), 100, 100, 1, listOf(1)),
//            )
//        ),
//        turntableAnim,
//        Modifier
//            .fillMaxWidth(0.86f)
//            .aspectRatio(1f)
////            .padding(top = turntableTop)
//    )
}

@Composable
@Preview
private fun PreviewUserResultWidget() {
    val data =
        sAppJson.decodeFromString<CoinWinnerPlayer>("{\"player\": {\"userid\": 1400, \"public_id\": \"101852\", \"nickname\": \"\\u70b9\\u51fb\", \"avatar_url\": \"https://s.test.ucoofun.com/aaceo4?x-oss-process=image/format,webp\", \"gender\": 1, \"age\": 24, \"height\": 0, \"avatar_frame\": \"https://s.ucoofun.com/aabLcV\", \"medal\": null, \"medal_list\": [{\"icon\": \"https://s.ucoofun.com/aabLeE\", \"width\": 72, \"height\": 24}], \"level\": 57, \"country_flag\": \"https://media.ucoofun.com/opsite%2Fcountryflag%2FL_slices%2FCN.png\", \"exp_level_info\": {\"charm_level\": 8, \"wealth_level\": 25}}, \"bet_coin\": 100, \"win_coin\": 0, \"status\": 3, \"bet_detail\": [100], \"lose_stage_num\": 2}")
    UserResultWidget(user = data.player, rewardCoin = 100)
}

@Composable
fun BoxScope.CoinWinnerGameFloat(roomId: Int, dialogQueue: DialogQueue<IVoiceLiveAction>) {
    val coinWinnerViewModel = viewModel<CoinWinnerViewModel>() {
        CoinWinnerViewModel(roomId)
    }

    val gameDetailState = coinWinnerViewModel.gameDetail.collectAsStateWithLifecycle()

    LaunchedEffect(coinWinnerViewModel) {
        snapshotFlow {
            if (coinWinnerViewModel.isGameShowing) {
                gameDetailState.value != null
            } else {
                false
            }
        }.distinctUntilChanged().collectLatest { visible ->
            if (visible) {
                dialogQueue.push(CoinWinnerWidgetDialog(coinWinnerViewModel), true)
            }
        }
    }

    AlignHorizontalContainer(
        visible = gameDetailState.value != null && !coinWinnerViewModel.isGameShowing,
        modifier = Modifier
            .align(Alignment.BottomEnd)
            .padding(bottom = 20.dp)
    ) {
        Image(
            painter = painterResource(id = R.drawable.cpd_winner_widget),
            contentDescription = "widget",
            modifier = Modifier.noEffectClickable { coinWinnerViewModel.isGameShowing = true },
            contentScale = ContentScale.FillWidth
        )
    }
}