@file:OptIn(ExperimentalMaterial3Api::class)

package com.qyqy.cupid.widgets

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ElevatedButton
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.TabPosition
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.CupidTheme
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.fixCurrentPage
import com.qyqy.ucoo.utils.OnClick

val defaultNavigationIcon: @Composable () -> Unit = {
    val backOwner = LocalOnBackPressedDispatcherOwner.current
    val color = LocalContentColor.current
    IconButton(composeClick {
        backOwner?.onBackPressedDispatcher?.onBackPressed()
    }) {
        Icon(
            painter = painterResource(id = R.drawable.ic_cpd_navigation_back),
            contentDescription = "back",
//            tint = Color(0xFF1D2129),
            tint = color,
        )
    }
}

fun cpdNavigationIcon(color: Color = Color(0xFF1D2129)): @Composable () -> Unit {
    return {
        val backOwner = LocalOnBackPressedDispatcherOwner.current
        IconButton(onClick = { backOwner?.onBackPressedDispatcher?.onBackPressed() }) {
            Icon(
                painter = painterResource(id = R.drawable.ic_cpd_navigation_back),
                contentDescription = "back",
                tint = color,
            )
        }
    }
}

@Composable
fun CupidAppBar(
    title: String,
    modifier: Modifier = Modifier,
    navigationIcon: @Composable () -> Unit = defaultNavigationIcon,
    containerColor: Color = Color.White,
    navigationIconContentColor: Color = Color(0xFF1D2129),
    titleContentColor: Color = navigationIconContentColor,
    actionIconContentColor: Color = navigationIconContentColor,
    actions: @Composable RowScope.() -> Unit = {},
) {
    CupidBasicAppBar(
        modifier, navigationIcon, {
            Text(text = title, style = MaterialTheme.typography.titleLarge, fontSize = 18.sp)
        }, containerColor = containerColor,
        navigationIconContentColor = navigationIconContentColor,
        titleContentColor = titleContentColor,
        actionIconContentColor = actionIconContentColor, actions
    )
}

@Composable
fun CupidBasicAppBar(
    modifier: Modifier = Modifier,
    navigationIcon: @Composable () -> Unit = defaultNavigationIcon,
    title: @Composable () -> Unit,
    containerColor: Color = Color.White,
    navigationIconContentColor: Color = Color(0xFF1D2129),
    titleContentColor: Color = Color(0xFF1D2129),
    actionIconContentColor: Color = Color(0xFF1D2129),
    actions: @Composable RowScope.() -> Unit = {},
) {
    CenterAlignedTopAppBar(
        modifier = modifier,
        navigationIcon = navigationIcon,
        title = title,
        actions = actions,
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
            containerColor = containerColor,
            navigationIconContentColor = navigationIconContentColor,
            titleContentColor = titleContentColor,
            actionIconContentColor = actionIconContentColor
        )
    )
}

@Preview
@Composable
private fun AppBarPreview() {
    CupidAppBar(title = "我的注意")
}

@Composable
fun CupidLoading(modifier: Modifier = Modifier) {
    Box(modifier = Modifier.size(60.dp), contentAlignment = Alignment.Center) {
        CircularProgressIndicator(modifier = Modifier.size(30.dp))
    }
}

@Composable
fun TextLabel(text: String, textStyle: TextStyle, modifier: Modifier = Modifier) {
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        AutoSizeText(text = text, style = textStyle)
    }
}

@Preview
@Composable
private fun TextLabelPreview() {
    val textStyle = remember {
        TextStyle(
            color = Color(0xFF4E5969),
            fontSize = 12.sp,
        )
    }
    val mod = remember {
        Modifier
            .height(20.dp)
            .background(Color(0xFFF1F2F3), RoundedCornerShape(50))
            .padding(horizontal = 8.dp)
    }
    Box(modifier = Modifier.size(100.dp), contentAlignment = Alignment.Center) {
        TextLabel(text = "东京·市区", textStyle = textStyle, modifier = mod)
    }
}

@Composable
fun CpdButton(
    text: String,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    containerColor: Color = MaterialTheme.colorScheme.primary,
    style: TextStyle = TextStyle(MaterialTheme.colorScheme.surface, 16.sp),
    onClick: OnClick = {},
) {
    ElevatedButton(
        onClick = onClick,
        enabled = enabled,
        contentPadding = PaddingValues(20.dp, 12.dp),
        colors = ButtonDefaults.elevatedButtonColors(
            containerColor = containerColor,
        ),
        modifier = modifier
    ) {
        Text(text = text, style = style)
    }
}

@Preview
@Composable
private fun CpdButtonPreview() {
    CupidTheme {
        CpdButton(text = "创建房间", onClick = { }, modifier = Modifier.widthIn(min = 220.dp))
    }
}

@Composable
fun PagerTabLayout(
    pagerState: PagerState, indicator: @Composable (tabPositions: List<TabPosition>) -> Unit,
    tabs: @Composable () -> Unit,
    modifier: Modifier = Modifier,
    containerColor: Color = TabRowDefaults.primaryContainerColor,
    contentColor: Color = TabRowDefaults.primaryContentColor,
) {
    TabRow(
        modifier = modifier,
        selectedTabIndex = pagerState.fixCurrentPage,
        indicator = indicator,
        tabs = tabs,
        divider = {},
        containerColor = containerColor,
        contentColor = contentColor
    )
}

