package com.qyqy.cupid.widgets

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.CpdColors

/**
 *  @time 9/12/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.widgets
 */
data class GiftWallIndicatorStyle(
    val backgroundColor: Color = Color(0xFFF1F2F3),
    val topSelectedColor: Color = CpdColors.FFFF5E8B,
    val topUnselectedColor: Color = Color.Transparent,
    val topSelectedTextColor: Color = Color.White,
    val topUnSelectedTextColor: Color = CpdColors.FF86909C
)

@Composable
fun CpdNormalTabRow(
    indicatorStyle: GiftWallIndicatorStyle,
    tabList: List<String>,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.background(indicatorStyle.backgroundColor, CircleShape)
    ) {
        var currentPage by remember { mutableStateOf(0) }
        tabList.onEachIndexed { index, item ->
            Box(
                modifier = Modifier
                    .size(88.dp, 34.dp)
                    .clip(CircleShape)
                    .background(
                        color = if (currentPage == index) indicatorStyle.topSelectedColor
                        else indicatorStyle.topUnselectedColor
                    )
                    .click(noEffect = true) {
                        currentPage = index
                    },
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = item,
                    fontSize = 14.sp,
                    color = if (currentPage == index) indicatorStyle.topSelectedTextColor
                    else indicatorStyle.topUnSelectedTextColor
                )
            }
        }
    }
}