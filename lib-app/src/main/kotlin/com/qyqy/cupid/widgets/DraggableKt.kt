package com.qyqy.cupid.widgets

import android.annotation.SuppressLint
import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.AnimatedVisibilityScope
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.VectorConverter
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.Saver
import androidx.compose.runtime.saveable.listSaver
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.LayoutCoordinates
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.layout.findRootCoordinates
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.onPlaced
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.layout.positionInParent
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.round
import androidx.compose.ui.unit.toSize
import androidx.compose.ui.zIndex
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@OptIn(ExperimentalFoundationApi::class)
@Preview
@Composable
private fun PreviewDrag() {

    val JetBlack = Color(0xFF000000)       // 极黑
    val BrightWhite = Color(0xFFFFFFFF)    // 纯白
    val FireEngineRed = Color(0xFFFF0000)  // 火焰红
    val ElectricBlue = Color(0xFF007FFF)   // 电蓝
    val NeonGreen = Color(0xFF39FF14)      // 霓虹绿
    val SunYellow = Color(0xFFFFD700)      // 太阳黄
    val HotPink = Color(0xFFFF69B4)        // 热粉
    val DeepPurple = Color(0xFF800080)     // 深紫
    val OrangePeel = Color(0xFFFFA500)     // 橙皮橙
    val CyanAqua = Color(0xFF00FFFF)       // 青色

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {

        val dragOverlapScope = rememberDragOverlapScope()

        Box(
            modifier = Modifier
                .align(Alignment.Center)
                .size(240.dp)
                .background(Color.Yellow)
                .clipToBounds()
        ) {

            val state1 = rememberAutoDraggableState(dragOverlapScope = dragOverlapScope, suggestionGap = 8.dp)

            Spacer(modifier = Modifier
                .size(60.dp)
                .autoDraggable(state1)
                .background(JetBlack)
                .combinedClickable(onDoubleClick = {
                    if (state1.alignOrientation == Orientation.Horizontal) {
                        state1.alignOrientation = Orientation.Vertical
                    } else {
                        state1.alignOrientation = Orientation.Horizontal
                    }
                }) {

                })

            val state2 = rememberAutoDraggableState(dragOverlapScope = dragOverlapScope, suggestionGap = 8.dp)

            Spacer(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .size(60.dp)
                    .autoDraggable(state2)
                    .background(BrightWhite)
            )

            val state3 = rememberAutoDraggableState(dragOverlapScope = dragOverlapScope, suggestionGap = 8.dp)

            Spacer(
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .size(60.dp)
                    .autoDraggable(state3)
                    .background(FireEngineRed)
            )

            val state4 = rememberAutoDraggableState(dragOverlapScope = dragOverlapScope, suggestionGap = 8.dp)

            Spacer(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .size(60.dp)
                    .autoDraggable(state4)
                    .background(ElectricBlue)
            )

            val state5 = rememberAutoDraggableState(dragOverlapScope = dragOverlapScope, suggestionGap = 8.dp)

            Spacer(
                modifier = Modifier
                    .align(Alignment.Center)
                    .size(60.dp)
                    .autoDraggable(state5)
                    .background(NeonGreen)
            )
        }

        Box(
            modifier = Modifier.fillMaxSize()
        ) {

            val state1 = rememberAutoDraggableState(dragOverlapScope = dragOverlapScope, suggestionGap = 8.dp)

            Spacer(
                modifier = Modifier
                    .size(60.dp)
                    .autoDraggable(state1)
                    .background(SunYellow)
            )

            val state2 = rememberAutoDraggableState(dragOverlapScope = dragOverlapScope, suggestionGap = 8.dp)

            Spacer(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .size(60.dp)
                    .autoDraggable(state2)
                    .background(HotPink)
            )

            val state3 = rememberAutoDraggableState(dragOverlapScope = dragOverlapScope, suggestionGap = 8.dp)

            Spacer(
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .size(60.dp)
                    .autoDraggable(state3)
                    .background(DeepPurple)
            )

            val state4 = rememberAutoDraggableState(dragOverlapScope = dragOverlapScope, suggestionGap = 8.dp)

            Spacer(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .size(60.dp)
                    .autoDraggable(state4)
                    .background(OrangePeel)
            )

            val state5 = rememberAutoDraggableState(dragOverlapScope = dragOverlapScope, suggestionGap = 8.dp)

            Spacer(
                modifier = Modifier
                    .align(Alignment.Center)
                    .size(60.dp)
                    .autoDraggable(state5)
                    .background(CyanAqua)
            )
        }
    }
}

val LocalDragOverlapScope = staticCompositionLocalOf<DragOverlapScope> {
    error("LocalDragOverlapScope is null")
}

@SuppressLint("UnnecessaryComposedModifier")
fun Modifier.autoDraggable(
    state: AutoDraggableState,
    autoDisposable: Boolean = true,
): Modifier = composed(factory = state.autoDraggableModifierFactory(autoDisposable))

fun Modifier.autoDraggable(
    autoDisposable: Boolean = true,
    dragEnable: Boolean = true, // 拖动开关
    overDragEnable: Boolean = true, // 是否可以越界拖动
    alignOrientation: Orientation = Orientation.Horizontal,// 贴边方向
    suggestionGap: Dp = 0.dp, // 可滑动组件之间的建议间隔，尽量满足，不满足则没有间隔
    horizontalAlignPadding: Dp = suggestionGap, // 自动贴边距边缘间隔
    verticalAlignPadding: Dp = suggestionGap,// 自动贴边距边缘间隔
    reverseAlignIfNoSpace: Boolean = true,
    tag: String = "AutoDraggableState",
): Modifier = composed {
    val state = rememberAutoDraggableState(
        dragEnable = dragEnable,
        overDragEnable = overDragEnable,
        alignOrientation = alignOrientation,
        suggestionGap = suggestionGap,
        horizontalAlignPadding = horizontalAlignPadding,
        verticalAlignPadding = verticalAlignPadding,
        reverseAlignIfNoSpace = reverseAlignIfNoSpace,
        tag = tag,
    )
    state.autoDraggableModifierFactory(autoDisposable)()
}

@Composable
fun rememberDragOverlapScope(): DragOverlapScope {
    return rememberSaveable(saver = Saver(
        save = {
            it.indexCounter
        }, restore = {
            DragOverlapScope(it)
        }
    )) {
        DragOverlapScope()
    }
}

class DragOverlapScope(initialIndexCounter: Float = 0f) {

    private val states = mutableListOf<AutoDraggableState>()

    var indexCounter: Float = initialIndexCounter
        private set

    internal fun incrementAndGet() = ++indexCounter

    internal fun addState(state: AutoDraggableState) {
        states.add(state)
    }

    internal fun removeState(state: AutoDraggableState) {
        states.remove(state)
    }

    internal fun transformAutoRect(state: AutoDraggableState, rect: Rect, parentRect: Rect, suggestionGap: Float): Rect? {
        return if (state.alignOrientation == Orientation.Horizontal) {
            val verticalList = states.mapNotNull {
                if (it === state) {
                    null
                } else {
                    it.getFinalRectInRoot()?.takeIf { other ->
                        if (other.top.inRange(parentRect.top, parentRect.bottom) || other.bottom.inRange(parentRect.top, parentRect.bottom)) {
                            rect.horizontalOverlaps(other)
                        } else {
                            false
                        }
                    }
                }
            }.sortedBy {
                it.top
            }
            findAutoVerticalRect(rect, parentRect, verticalList, suggestionGap)
        } else {
            val horizontalList = states.mapNotNull {
                if (it === state) {
                    null
                } else {
                    it.getFinalRectInRoot()?.takeIf { other ->
                        if (other.left.inRange(parentRect.left, parentRect.right) || other.right.inRange(parentRect.left, parentRect.right)) {
                            rect.verticalOverlaps(other)
                        } else {
                            false
                        }
                    }
                }
            }.sortedBy {
                it.left
            }
            findAutoHorizontalRect(rect, parentRect, horizontalList, suggestionGap)
        }
    }

    private fun findAutoVerticalRect(rect: Rect, parentRect: Rect, boxRectList: List<Rect>, suggestionGap: Float): Rect? {
        val overlapIndex = boxRectList.indexOfFirst {
            rect.verticalOverlaps(it)
        }

        return if (overlapIndex >= 0) { // 重叠了，寻找可用空位
            val overlapRect = boxRectList[overlapIndex]
            if (rect.center.y >= overlapRect.center.y) { // 优先向下寻找空位，如果找不到再向上寻找
                for (index in overlapIndex until boxRectList.size) {
                    val topBound = boxRectList[index].bottom
                    val bottomBound = boxRectList.getOrNull(index + 1)?.top ?: parentRect.bottom
                    if (bottomBound.minus(topBound) >= rect.height) {
                        return fixTranslateVerticalRect(topBound, bottomBound, rect, suggestionGap)
                    }
                }

                for (index in overlapIndex downTo 0) {
                    val topBound = boxRectList.getOrNull(index - 1)?.bottom ?: parentRect.top
                    val bottomBound = boxRectList[index].top
                    if (bottomBound.minus(topBound) >= rect.height) {
                        return fixTranslateVerticalRect(topBound, bottomBound, rect, suggestionGap)
                    }
                }
            } else { // 向上寻找空位，如果找不到再向下寻找
                for (index in overlapIndex downTo 0) {
                    val topBound = boxRectList.getOrNull(index - 1)?.bottom ?: parentRect.top
                    val bottomBound = boxRectList[index].top
                    if (bottomBound.minus(topBound) >= rect.height) {
                        return fixTranslateVerticalRect(topBound, bottomBound, rect, suggestionGap)
                    }
                }

                for (index in overlapIndex until boxRectList.size) {
                    val topBound = boxRectList[index].bottom
                    val bottomBound = boxRectList.getOrNull(index + 1)?.top ?: parentRect.bottom
                    if (bottomBound.minus(topBound) >= rect.height) {
                        return fixTranslateVerticalRect(topBound, bottomBound, rect, suggestionGap)
                    }
                }
            }
            null
        } else {
            boxRectList.forEachIndexed { index, other ->
                if (index == 0 && other.top >= rect.bottom) {
                    return fixTranslateVerticalRect(parentRect.top, other.top, rect, suggestionGap)
                } else if (other.bottom <= rect.top) {
                    val bottom = boxRectList.getOrNull(index + 1)?.top ?: parentRect.bottom
                    if (rect.bottom <= bottom) {
                        return fixTranslateVerticalRect(other.bottom, bottom, rect, suggestionGap)
                    }
                }
            }
            rect
        }
    }

    private fun fixTranslateVerticalRect(
        topBound: Float, bottomBound: Float, rect: Rect, suggestionGap: Float,
    ): Rect {
        val halfLeftGap = bottomBound.minus(topBound).minus(rect.height).div(2f)
        return if (halfLeftGap >= suggestionGap) { // 空间足够
            if (rect.top < topBound.plus(suggestionGap)) {
                Rect(
                    left = rect.left, top = topBound.plus(suggestionGap), right = rect.right, bottom = topBound.plus(suggestionGap).plus(rect.height)
                )
            } else if (rect.bottom > bottomBound.minus(suggestionGap)) {
                Rect(
                    left = rect.left,
                    top = bottomBound.minus(suggestionGap).minus(rect.height),
                    right = rect.right,
                    bottom = bottomBound.minus(suggestionGap)
                )
            } else {
                rect
            }
        } else {
            Rect(
                left = rect.left,
                top = topBound.plus(halfLeftGap),
                right = rect.right,
                bottom = topBound.plus(halfLeftGap).plus(rect.height)
            )
        }
    }

    private fun findAutoHorizontalRect(rect: Rect, parentRect: Rect, boxRectList: List<Rect>, suggestionGap: Float): Rect? {
        val overlapIndex = boxRectList.indexOfFirst {
            rect.horizontalOverlaps(it)
        }
        return if (overlapIndex >= 0) { // 重叠了，寻找可用空位
            val overlapRect = boxRectList[overlapIndex]
            if (rect.center.x >= overlapRect.center.x) { // 优先向右寻找空位，如果找不到再向左寻找
                for (index in overlapIndex until boxRectList.size) {
                    val leftBound = boxRectList[index].right
                    val rightBound = boxRectList.getOrNull(index + 1)?.left ?: parentRect.right
                    if (rightBound.minus(leftBound) >= rect.width) {
                        return fixTranslateHorizontalRect(leftBound, rightBound, rect, suggestionGap)
                    }
                }

                for (index in overlapIndex downTo 0) {
                    val leftBound = boxRectList.getOrNull(index - 1)?.right ?: parentRect.left
                    val rightBound = boxRectList[index].left
                    if (rightBound.minus(leftBound) >= rect.width) {
                        return fixTranslateHorizontalRect(leftBound, rightBound, rect, suggestionGap)
                    }
                }
            } else { // 向左寻找空位，如果找不到再向右寻找
                for (index in overlapIndex downTo 0) {
                    val leftBound = boxRectList.getOrNull(index - 1)?.right ?: parentRect.left
                    val rightBound = boxRectList[index].left
                    if (rightBound.minus(leftBound) >= rect.width) {
                        return fixTranslateHorizontalRect(leftBound, rightBound, rect, suggestionGap)
                    }
                }

                for (index in overlapIndex until boxRectList.size) {
                    val leftBound = boxRectList[index].right
                    val rightBound = boxRectList.getOrNull(index + 1)?.left ?: parentRect.right
                    if (rightBound.minus(leftBound) >= rect.width) {
                        return fixTranslateHorizontalRect(leftBound, rightBound, rect, suggestionGap)
                    }
                }
            }
            null
        } else {
            boxRectList.forEachIndexed { index, other ->
                if (index == 0 && other.left >= rect.right) {
                    return fixTranslateHorizontalRect(parentRect.left, other.left, rect, suggestionGap)
                } else if (other.right <= rect.left) {
                    val right = boxRectList.getOrNull(index + 1)?.left ?: parentRect.right
                    if (rect.right <= right) {
                        return fixTranslateHorizontalRect(other.right, right, rect, suggestionGap)
                    }
                }
            }
            rect
        }
    }

    private fun fixTranslateHorizontalRect(
        leftBound: Float, rightBound: Float, rect: Rect, suggestionGap: Float,
    ): Rect {
        val halfLeftGap = rightBound.minus(leftBound).minus(rect.width).div(2f)
        return if (halfLeftGap >= suggestionGap) { // 空间足够
            if (rect.left < leftBound.plus(suggestionGap)) {
                Rect(
                    left = leftBound.plus(suggestionGap), top = rect.top, right = leftBound.plus(suggestionGap).plus(rect.width), bottom = rect.bottom
                )
            } else if (rect.right > rightBound.minus(suggestionGap)) {
                Rect(
                    left = rightBound.minus(suggestionGap).minus(rect.width),
                    top = rect.top,
                    right = rightBound.minus(suggestionGap),
                    bottom = rect.bottom
                )
            } else {
                rect
            }
        } else {
            Rect(
                left = leftBound.plus(halfLeftGap), top = rect.top, right = leftBound.plus(halfLeftGap).plus(rect.width), bottom = rect.bottom
            )
        }
    }

    private fun Rect.horizontalOverlaps(other: Rect): Boolean {
        return !(right <= other.left || other.right <= left)
    }

    private fun Rect.verticalOverlaps(other: Rect): Boolean {
        return !(bottom <= other.top || other.bottom <= top)
    }

    private fun Float.inRange(min: Float, max: Float) = this > min && this < max

}


@Composable
fun AlignHorizontalContainer(
    visible: Boolean,
    modifier: Modifier = Modifier,
    tag: String = "AutoDraggableState",
    timeMillis: Long = 0,
    autoDraggableState: AutoDraggableState = rememberAutoDraggableState(
        suggestionGap = 3.dp,
        horizontalAlignPadding = 8.dp,
        tag = tag
    ),
    content: @Composable() AnimatedVisibilityScope.() -> Unit,
) {
    var realVisible by remember {
        mutableStateOf(false)
    }
    if (timeMillis > 0 && visible) {
        LaunchedEffect(Unit) {
            delay(timeMillis)
            realVisible = true
        }
    } else {
        realVisible = visible
    }
    AnimatedVisibility(
        visible = realVisible,
        modifier = modifier.autoDraggable(autoDraggableState, false),
        enter = slideInHorizontally {
            val rect = autoDraggableState.getPositionRect()
            if (rect.isEmpty || rect.left > 100) {
                it
            } else {
                -it
            }
        },
        exit = slideOutHorizontally {
            if (autoDraggableState.getPositionRect().left <= 100) {
                -it
            } else {
                it
            }
        },
        content = {
            content()
            if (realVisible) {
                autoDraggableState.AutoDisposable()
            }
        }
    )
}

@Composable
fun rememberAutoDraggableState(
    dragOverlapScope: DragOverlapScope = LocalDragOverlapScope.current,
    dragEnable: Boolean = true, // 拖动开关
    overDragEnable: Boolean = true, // 是否可以越界拖动
    alignOrientation: Orientation = Orientation.Horizontal,// 贴边方向
    suggestionGap: Dp = 0.dp, // 可滑动组件之间的建议间隔，尽量满足，不满足则没有间隔
    horizontalAlignPadding: Dp = suggestionGap, // 自动贴边距边缘间隔
    verticalAlignPadding: Dp = suggestionGap,// 自动贴边距边缘间隔
    reverseAlignIfNoSpace: Boolean = true,
    tag: String = "AutoDraggableState",
): AutoDraggableState {
    val density = LocalDensity.current
    return rememberSaveable(saver = listSaver(save = {
        listOf(
            it.dragEnable,
            it.overDragEnable,
            it.alignOrientation.name,
            it.suggestionGap,
            it.horizontalAlignPadding,
            it.verticalAlignPadding,
            it.reverseAlignIfNoSpace,
            it.offset.x,
            it.offset.y,
            it.zIndex,
            it.stabledRect?.left,
            it.stabledRect?.top,
            it.stabledRect?.right,
            it.stabledRect?.bottom,
        )
    }, restore = {
        AutoDraggableState(
            dragOverlapScope = dragOverlapScope,
            dragEnable = it[0] as Boolean,
            overDragEnable = it[1] as Boolean,
            alignOrientation = Orientation.valueOf(it[2] as String),
            suggestionGap = it[3] as Float,
            horizontalAlignPadding = it[4] as Float,
            verticalAlignPadding = it[5] as Float,
            reverseAlignIfNoSpace = it[6] as Boolean,
            initialOffset = Offset(it[7] as Float, it[8] as Float),
            initialZIndex = it[9] as Float,
            resetFlag = true,
            tag = tag
        ).apply {
            stabledRect = it[10]?.let { value ->
                Rect(value as Float, it[11] as Float, it[12] as Float, it[13] as Float)
            }
        }
    })) {
        with(density) {
            AutoDraggableState(
                dragOverlapScope = dragOverlapScope,
                dragEnable = dragEnable,
                overDragEnable = overDragEnable,
                alignOrientation = alignOrientation,
                suggestionGap = suggestionGap.toPx(),
                horizontalAlignPadding = horizontalAlignPadding.toPx(),
                verticalAlignPadding = verticalAlignPadding.toPx(),
                reverseAlignIfNoSpace = reverseAlignIfNoSpace,
                tag = tag
            )
        }
    }
}


class AutoDraggableState internal constructor(
    private val dragOverlapScope: DragOverlapScope,
    dragEnable: Boolean = true, // 拖动开关
    var overDragEnable: Boolean = true, // 是否可以越界拖动
    alignOrientation: Orientation = Orientation.Horizontal,// 贴边方向
    var suggestionGap: Float = 0f, // 可滑动组件之间的建议间隔，尽量满足，不满足则没有间隔
    horizontalAlignPadding: Float = suggestionGap, // 自动贴边距边缘间隔
    verticalAlignPadding: Float = suggestionGap, // 自动贴边距边缘间隔
    var reverseAlignIfNoSpace: Boolean = true,
    initialOffset: Offset = Offset.Zero,
    initialZIndex: Float = 0f,
    private var resetFlag: Boolean = false,
    val tag: String = "AutoDraggableState",
) {

    var dragEnable by mutableStateOf(dragEnable)

    var horizontalAlignPadding by mutableFloatStateOf(horizontalAlignPadding)

    var verticalAlignPadding by mutableFloatStateOf(verticalAlignPadding)

    var alignOrientation by mutableStateOf(alignOrientation)

    var offset by mutableStateOf(initialOffset)
        private set

    private var dragging by mutableStateOf(false)

    private var layoutCoordinates by mutableStateOf<LayoutCoordinates?>(null)

    private var isActive: Boolean = false

    private val animatable = Animatable(offset, Offset.VectorConverter)

    private var applyZIndex by mutableStateOf(false)

    var zIndex by mutableFloatStateOf(initialZIndex)
        private set

    var stabledRect: Rect? = null

    internal fun autoDraggableModifierFactory(autoDisposable: Boolean): @Composable Modifier.() -> Modifier = {
        if (!dragging) {
            if (layoutCoordinates != null) {
                LaunchedEffect(alignOrientation, horizontalAlignPadding, verticalAlignPadding) {
                    if (resetFlag) {
                        resetFlag = false
                        return@LaunchedEffect
                    }
                    val boxRect = layoutCoordinates?.toBoxRect() ?: return@LaunchedEffect
                    getAlignRect(boxRect, false).let {
                        dragOverlapScope.transformAutoRect(this@AutoDraggableState, it, boxRect.parentRect, suggestionGap) ?: run {
                            if (reverseAlignIfNoSpace) {
                                dragOverlapScope.transformAutoRect(
                                    state = this@AutoDraggableState,
                                    rect = getAlignRect(boxRect, true),
                                    parentRect = boxRect.parentRect,
                                    suggestionGap = suggestionGap
                                )
                            } else {
                                null
                            }
                        } ?: it
                    }.also {
                        stabledRect = it
                        animatable.snapTo(offset)
                        animatable.animateTo(
                            targetValue = Offset(offset.x + it.left.minus(boxRect.leftInRoot), offset.y + it.top.minus(boxRect.topInRoot)),
                        ) {
                            offset = this.value
                        }
                    }
                    applyZIndex = false
                }
            }
        }

        if (autoDisposable) {
            AutoDisposable()
        }

        Modifier
            .zIndex(if (applyZIndex) Float.MAX_VALUE else zIndex)
            .offset { offset.round() }
            .onGloballyPositioned {
                val rect = it.boundsInRoot()
                if (!rect.isEmpty) {
                    layoutCoordinates = it
//                    if (!dragging && !animatable.isRunning) {
//                        stabledRect = rect
//                    }
                }
            }
            .pointerInput(dragEnable) {
                if (dragEnable) {
                    detectDragGestures(
                        onDragStart = {
                            zIndex = dragOverlapScope.incrementAndGet()
                        },
                        onDragEnd = {
                            dragging = false
                        },
                        onDragCancel = {
                            dragging = false
                        }
                    ) { change, dragAmount ->
                        change.consume()
                        applyZIndex = true
                        dragging = true
                        if (overDragEnable) {
                            offset += dragAmount
                        } else {
                            val boxRect = layoutCoordinates?.toBoxRect() ?: return@detectDragGestures
                            val newLeft = (boxRect.left + dragAmount.x).coerceIn(0f, boxRect.parentWidth.minus(boxRect.childWidth))
                            val newTop = (boxRect.top + dragAmount.y).coerceIn(0f, boxRect.parentHeight.minus(boxRect.childHeight))
                            offset = Offset(x = offset.x.plus(newLeft.minus(boxRect.left)), y = offset.y.plus(newTop.minus(boxRect.top)))
                        }
                    }
                } else {
                    dragging = false
                }
            }
    }

    @Composable
    fun AutoDisposable() {
        dragOverlapScope.apply {
            val lifecycleOwner = LocalLifecycleOwner.current
            DisposableEffect(key1 = Unit) {
                addState(this@AutoDraggableState)

                val observer = LifecycleEventObserver { _, event ->
                    isActive = event.targetState.isAtLeast(Lifecycle.State.STARTED)
                }

                lifecycleOwner.lifecycle.addObserver(observer)

                onDispose {
                    removeState(this@AutoDraggableState)

                    lifecycleOwner.lifecycle.removeObserver(observer)
                }
            }
        }
    }


    private fun getAlignRect(boxRect: BoxRect, reverse: Boolean): Rect {
        return boxRect.run {
            val newLeftInRoot = if (alignOrientation == Orientation.Horizontal) {
                if ((!reverse && centerInRoot.x < parentCenterInRoot.x) || (reverse && centerInRoot.x >= parentCenterInRoot.x)) {
                    // 靠左
                    parentLeftInRoot.plus(horizontalAlignPadding)
                } else {
                    // 靠右
                    parentRightInRoot.minus(horizontalAlignPadding).minus(childWidth)
                }
            } else {
                leftInRoot.coerceIn(parentLeftInRoot.plus(horizontalAlignPadding), parentRightInRoot.minus(horizontalAlignPadding).minus(childWidth))
            }

            val newTopInRoot = if (alignOrientation == Orientation.Vertical) {
                if ((!reverse && centerInRoot.y < parentCenterInRoot.y) || (reverse && centerInRoot.y >= parentCenterInRoot.y)) {
                    // 靠上
                    parentTopInRoot.plus(verticalAlignPadding)
                } else {
                    // 靠下
                    parentBottomInRoot.minus(verticalAlignPadding).minus(childHeight)
                }
            } else {
                topInRoot.coerceIn(parentTopInRoot.plus(verticalAlignPadding), parentBottomInRoot.minus(verticalAlignPadding).minus(childHeight))
            }

            Rect(newLeftInRoot, newTopInRoot, newLeftInRoot + childWidth, newTopInRoot + childHeight)
        }
    }

    internal fun getFinalRectInRoot() = run {
        if (!isActive || dragging || stabledRect == null) {
            null
        } else {
            stabledRect
        }
    }

    fun getPositionRect() = stabledRect ?: layoutCoordinates?.toBoxRect()?.rectInParent ?: Rect.Zero

}

data class BoxRect(
    val childRect: Rect,
    val parentRect: Rect,
) {
    val childWidth: Float
        get() = childRect.width

    val childHeight: Float
        get() = childRect.height

    val parentWidth: Float
        get() = parentRect.width

    val parentHeight: Float
        get() = parentRect.height

    val left
        get() = leftInRoot - parentLeftInRoot

    val right
        get() = rightInRoot - parentLeftInRoot

    val top
        get() = topInRoot - parentTopInRoot

    val bottom
        get() = bottomInRoot - parentTopInRoot

    val center
        get() = centerInRoot - Offset(parentLeftInRoot, parentLeftInRoot)

    val leftInRoot
        get() = childRect.left

    val rightInRoot
        get() = childRect.right

    val topInRoot
        get() = childRect.top

    val bottomInRoot
        get() = childRect.bottom

    val centerInRoot
        get() = childRect.center

    val parentLeftInRoot
        get() = parentRect.left

    val parentRightInRoot
        get() = parentRect.right

    val parentTopInRoot
        get() = parentRect.top

    val parentBottomInRoot
        get() = parentRect.bottom

    val parentCenterInRoot
        get() = parentRect.center

    val rectInParent: Rect
        get() = Rect(left, top, right, bottom)
}


private fun LayoutCoordinates.toBoxRect(): BoxRect? {
    val parentCoordinates = try {
        parentLayoutCoordinates
    } catch (e: Exception) {
        null
    } ?: return null
    return BoxRect(realBoundsInRoot(), parentCoordinates.realBoundsInRoot())
}

private fun LayoutCoordinates.realBoundsInRoot(): Rect {
    return Rect(positionInRoot(), size.toSize())
}


fun Modifier.draggableToSide(orientation: Orientation, padding: Dp = 0.dp, enabled: Boolean = true): Modifier = composed {

    val scope = rememberCoroutineScope()

    val animOffset = rememberSaveable(saver = Saver(
        save = {
            it.value.let { offset ->
                listOf(offset.x, offset.y)
            }
        },
        restore = {
            Animatable(Offset(it[0], it[1]), Offset.VectorConverter)
        }
    )) { Animatable(Offset(0f, 0f), Offset.VectorConverter) }

    var layoutCoordinates by remember {
        mutableStateOf<LayoutCoordinates?>(null)
    }

    var dragging by remember {
        mutableStateOf(false)
    }

    val paddingPx = with(LocalDensity.current) {
        padding.toPx()
    }

    suspend fun reset() {
        val coordinates = layoutCoordinates ?: return
        val parentCoordinates = coordinates.parentLayoutCoordinates ?: coordinates.findRootCoordinates()
        val rect = parentCoordinates.localBoundingBoxOf(coordinates)
        val offset = animOffset.value

        val x = if (orientation == Orientation.Horizontal) {
            if (rect.left <= parentCoordinates.size.width.minus(coordinates.size.width).div(2f)) {
                // 靠左
                offset.x.minus(rect.left.minus(paddingPx))
            } else {
                // 靠右
                offset.x.plus(parentCoordinates.size.width.minus(rect.right).minus(paddingPx))
            }
        } else {
            offset.x.coerceIn(
                paddingPx.plus(offset.x.minus(rect.left)),
                parentCoordinates.size.width.minus(coordinates.size.width).plus(offset.x.minus(rect.left)).minus(paddingPx)
            )
        }

        val y = if (orientation == Orientation.Vertical) {
            if (rect.top <= parentCoordinates.size.height.minus(coordinates.size.height).div(2f)) {
                // 靠上
                offset.y.minus(rect.top.minus(paddingPx))
            } else {
                // 靠下
                offset.y.plus(parentCoordinates.size.height.minus(rect.bottom).minus(paddingPx))
            }
        } else {
            offset.y.coerceIn(
                paddingPx.plus(offset.y.minus(rect.top)),
                parentCoordinates.size.height.minus(coordinates.size.height).plus(offset.y.minus(rect.top)).minus(paddingPx)
            )
        }

        animOffset.animateTo(Offset(x, y))

    }

    if (layoutCoordinates != null && dragging.not()) {
        LaunchedEffect(key1 = Unit) {
            reset()
        }
    }

    offset { animOffset.value.round() }
        .onGloballyPositioned {
            layoutCoordinates = it
        }
        .pointerInput(enabled) {
            if (enabled) {
                detectDragGestures(
                    onDragEnd = {
                        dragging = false
                    },
                    onDragCancel = {
                        dragging = false
                    }
                ) { change, dragAmount ->
                    change.consume()
                    dragging = true
                    scope.launch {
                        val offset = animOffset.value
                        animOffset.snapTo(offset + Offset(dragAmount.x, dragAmount.y))
                    }
                }
            }
        }
}