package com.qyqy.cupid.widgets

import androidx.compose.foundation.clickable
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.semantics.Role
import com.qyqy.ucoo.compose.noEffectClickable

/**
 *  @time 2024/7/17
 *  <AUTHOR>
 *  @package com.qyqy.keya.extension
 */
const val VIEW_CLICK_INTERVAL_TIME = 800L//View的click方法的两次点击间隔时间

/**
 * 防止重复点击(有的人可能会手抖连点两次,造成奇怪的bug)
 */
@Composable
fun Modifier.click(
    time: Long = VIEW_CLICK_INTERVAL_TIME,
    enabled: Boolean = true,//中间这三个是clickable自带的参数
    onClickLabel: String? = null,
    role: Role? = null,
    noEffect: Boolean = false,
    onClick: () -> Unit,
) = composed {
    val realClickHandler = composeClick(time, onClick)
    if (noEffect) {
        this.noEffectClickable(enabled, onClickLabel, role, realClickHandler)
    } else {
        this.clickable(enabled, onClickLabel, role, realClickHandler)
    }
}

/**
 * 防止重复点击,比如用在Button时直接传入onClick函数
 */
@Composable
fun composeClick(
    time: Long = VIEW_CLICK_INTERVAL_TIME,
    onClick: () -> Unit,
): () -> Unit {
    val clickDebounce = remember { ClickDebounce(time) }
    return {
        clickDebounce.preformClick(onClick)
    }
}

class ClickDebounce(private val time: Long = VIEW_CLICK_INTERVAL_TIME) {

    private var lastClickTime = 0L//使用remember函数记录上次点击的时间

    fun preformClick(onClick: () -> Unit) {
        val currentTimeMillis = System.currentTimeMillis()
        if (currentTimeMillis - time >= lastClickTime) {//判断点击间隔,如果在间隔内则不回调
            onClick()
            lastClickTime = currentTimeMillis
        }
    }
}
