

package com.qyqy.cupid.widgets

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ColorMatrix
import androidx.compose.ui.graphics.painter.ColorPainter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.data.BottomRadiusItem
import com.qyqy.ucoo.compose.data.CategoryGiftWall
import com.qyqy.ucoo.compose.data.CategoryTitleItem
import com.qyqy.ucoo.compose.data.GiftItem
import com.qyqy.ucoo.compose.data.GiftWallItem
import com.qyqy.ucoo.compose.data.SpaceItem
import com.qyqy.ucoo.compose.data.SpanItem
import com.qyqy.ucoo.compose.data.TopRadiusItem
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.im.bean.Gift

data class GiftWallContainerStyle(
    val backgroundColor: Color = CpdColors.FFFFF3F6,

    val activeTitleColor: Color = CpdColors.FF1D2129,
    val inactiveTitleColor: Color = CpdColors.FF86909C,

    val activeCountColor: Color = activeTitleColor,
    val inactiveCountColor: Color = inactiveTitleColor,

    @DrawableRes
    val activeItemBgImg: Int = -1,
    @DrawableRes
    val inactiveItemBgImg: Int = -1,

    val categoryTitleColor: Color = Color(0xFFFFB71A),

    @DrawableRes
    val leftArrowImgRes: Int = R.drawable.ic_cpd_line_right_star,
    @DrawableRes
    val rightArrowImgRes: Int = R.drawable.ic_cpd_line_left_star,

    val paddingValues: PaddingValues = PaddingValues(horizontal = 3.dp, vertical = 5.dp),
    val marginValues: PaddingValues = PaddingValues(0.dp),

    val itemHeight: Int = 0
)


val LocalGiftContainerStyleProvider = staticCompositionLocalOf<GiftWallContainerStyle> {
    error("")
}

@Composable
fun CupidGiftWallPageRouter(
    profileType: Int,
    list: List<CategoryGiftWall>,
    indicatorStyle: GiftWallIndicatorStyle = GiftWallIndicatorStyle(),
    containerStyle: GiftWallContainerStyle = GiftWallContainerStyle()
) {
    var currentPage by remember {
        mutableIntStateOf(0)
    }
    Column(modifier = Modifier.fillMaxSize()) {
        Row(
            modifier = Modifier
                .align(Alignment.CenterHorizontally)
                .background(indicatorStyle.backgroundColor, CircleShape)
        ) {
            list.onEachIndexed { index, item ->
                Box(
                    modifier = Modifier
                        .size(88.dp, 34.dp)
                        .clip(CircleShape)
                        .background(
                            color = if (currentPage == index) indicatorStyle.topSelectedColor
                            else indicatorStyle.topUnselectedColor
                        )
                        .click(noEffect = true) {
                            currentPage = index
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = item.category,
                        fontSize = 14.sp,
                        color = if (currentPage == index) indicatorStyle.topSelectedTextColor
                        else indicatorStyle.topUnSelectedTextColor
                    )
                }
            }
        }
        Spacer(modifier = Modifier.height(12.dp))
        if (list.isNotEmpty()) {
            CupidGiftWallPage(profileType, list[currentPage].items, Modifier.fillMaxWidth(), style = containerStyle)
        }
    }
}

@Composable
fun CupidGiftWallPage(
    profileType: Int, list: List<GiftWallItem>,
    modifier: Modifier = Modifier,
    style: GiftWallContainerStyle = GiftWallContainerStyle()
) {
    CompositionLocalProvider(
        LocalGiftContainerStyleProvider provides style
    ) {
        LazyVerticalGrid(
            columns = GridCells.Fixed(8),
            modifier = modifier,
        ) {
            list.forEach {
                item(
                    span = {
                        GridItemSpan(it.span)
                    },
                    contentType = it.type
                ) {
                    when (it) {
                        is SpaceItem -> {
                            SpacerItem(it.height)
                        }

                        is TopRadiusItem -> {
                            TopRadiusItem()
                        }

                        is BottomRadiusItem -> {
                            BottomRadiusItem()
                        }

                        is SpanItem -> {
                            SpanItem()
                        }

                        is CategoryTitleItem -> {
                            CategoryTitleUIItem(it.title)
                        }

                        is GiftItem -> {
                            GiftUIItem(it.gift, it.count)
                        }
                    }
                }
            }
            item {
                when (profileType) {
                    0 -> SpacerItem(25)
                    1 -> SpacerItem(25)
                    else -> SpacerItem(111)
                }
            }
        }
    }
}


@Composable
private fun GiftUIItem(gift: Gift, count: Int) {
    val style = LocalGiftContainerStyleProvider.current

    Box(
        modifier = Modifier
            .background(color = style.backgroundColor)
            .fillMaxWidth()
            .heightIn(style.itemHeight.dp)
            .padding(style.marginValues)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .paint(
                    painter = if (style.activeItemBgImg != -1 && style.inactiveItemBgImg != -1) painterResource(
                        id = if (count > 0)
                            style.activeItemBgImg
                        else
                            style.inactiveItemBgImg
                    ) else ColorPainter(style.backgroundColor),
                    contentScale = ContentScale.FillBounds
                )
                .padding(style.paddingValues),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            val colorFilter = if (count > 0) {
                null
            } else {
                ColorFilter.colorMatrix(ColorMatrix().apply { setToSaturation(0f) })
            }

            ComposeImage(
                model = gift.icon,
                modifier = Modifier.size(56.dp),
                contentScale = ContentScale.Crop,
                colorFilter = colorFilter,
                contentDescription = null,
            )

            Text(
                text = gift.name,
                modifier = Modifier
                    .padding(top = 5.dp)
                    .height(18.dp),
                color = if (count > 0) style.activeTitleColor else style.inactiveTitleColor,
                fontSize = 12.sp,
                maxLines = 1,
            )
            Text(
                text = "x${count}",
                modifier = Modifier
                    .padding(top = 2.dp)
                    .height(16.dp),
                color = if (count > 0) style.activeCountColor else style.inactiveCountColor,
                fontSize = 10.sp,
                maxLines = 1,
            )
        }
    }
}

@Composable
private fun TopRadiusItem() {
    val style = LocalGiftContainerStyleProvider.current
    Spacer(
        modifier = Modifier
            .fillMaxWidth()
            .height(12.dp)
            .background(style.backgroundColor, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
    )
}

@Composable
private fun BottomRadiusItem() {
    val style = LocalGiftContainerStyleProvider.current
    Spacer(
        modifier = Modifier
            .fillMaxWidth()
            .height(12.dp)
            .background(style.backgroundColor, RoundedCornerShape(bottomStart = 12.dp, bottomEnd = 12.dp))
    )
}

@Composable
private fun CategoryTitleUIItem(title: String) {
    val style = LocalGiftContainerStyleProvider.current

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(style.backgroundColor)
            .padding(vertical = 6.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
    ) {
        Image(
            painter = painterResource(id = style.leftArrowImgRes),
            contentDescription = null,
            modifier = Modifier.width(54.dp),
            contentScale = ContentScale.FillWidth,
        )
        Text(
            text = title,
            modifier = Modifier.weight(1f, false),
            color = style.categoryTitleColor,
            fontSize = 14.sp,
            textAlign = TextAlign.Center
        )
        Image(
            painter = painterResource(id = style.rightArrowImgRes),
            contentDescription = null,
            modifier = Modifier.width(54.dp),
            contentScale = ContentScale.FillWidth,
        )
    }
}

@Composable
private fun SpacerItem(height: Int) {
    Spacer(
        modifier = Modifier
            .fillMaxWidth()
            .height(height.dp)
    )
}

@Composable
private fun SpanItem() {
    val style = LocalGiftContainerStyleProvider.current

    Box(
        modifier = Modifier
            .background(color = style.backgroundColor)
            .fillMaxWidth()
            .heightIn(style.itemHeight.dp)
            .padding(style.marginValues)
    ) {
        Column(
            modifier = Modifier
                .paint(
                    painter = if (style.activeItemBgImg != -1 && style.inactiveItemBgImg != -1) painterResource(
                        style.inactiveItemBgImg
                    ) else ColorPainter(style.backgroundColor),
                    contentScale = ContentScale.FillBounds,
                    alpha = 0f
                )
                .padding(style.paddingValues),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.size(56.dp))
            Text(
                text = stringResource(id = R.string.cpd_gift),
                modifier = Modifier
                    .padding(top = 5.dp)
                    .height(18.dp),
                color = Color.Transparent,
                fontSize = 12.sp,
                maxLines = 1,
            )
            Text(
                text = "x0",
                modifier = Modifier
                    .padding(top = 2.dp)
                    .height(16.dp),
                color = Color.Transparent,
                fontSize = 10.sp,
                maxLines = 1,
            )
        }
    }
}

@Preview
@Composable
fun CupidPreviewGiftWallPage() {
    CupidGiftWallPage(
        0, List(100) {
            GiftItem(Gift(icon = "https://media.ucoofun.com/opsite/avatar/male/avatar_male_18.jpg", name = "哈哈$it", desc = ""), it)
        }
    )
}