package com.qyqy.cupid.widgets

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.ucoo.R

/**
 *  @time 9/3/24
 *  <AUTHOR>
 *  @package com.qyqy.cupid.widgets
 */
//@Composable
//fun GiftWallWidget(userId: Int, onAction: () -> Unit = {}) {
//    val lightUpCnt =
//        if (isPreviewOnCompose) {
//            remember {
//                mutableStateOf(1)
//            }
//        } else {
//            val viewModel = viewModel(modelClass = GiftWallViewModel::class.java, factory = viewModelFactory {
//                initializer {
//                    GiftWallViewModel(userId.toString())
//                }
//            })
//            viewModel.lightUpCnt.collectAsStateWithLifecycle()
//        }
//
//
//    Box {
//        GiftWallContent(lightUpCnt = lightUpCnt.value, totalCnt = , onAction= onAction)
//    }
//}

@Composable
fun GiftWallContent(lightUpCnt: Int, totalCnt: Int, modifier: Modifier = Modifier, onAction: () -> Unit = {}) {
    Row(
        modifier = modifier
            .paint(
                painterResource(id = R.drawable.ic_cpd_gift_wall_background),
                contentScale = ContentScale.FillWidth,
                alignment = Alignment.TopStart,
            )
            .click {
                onAction()
            },
        horizontalArrangement = Arrangement.End,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            stringResource(id = R.string.cpd_gift_lightup, lightUpCnt, totalCnt),
            color = CpdColors.FFE3C4A8,
            fontSize = 12.sp
        )
        Spacer(modifier = Modifier.width(4.dp))
        Icon(
            painter = painterResource(id = R.drawable.ic_cpd_arrow_right), contentDescription = "",
            tint = CpdColors.FFE3C4A8,
        )
        Spacer(modifier = Modifier.width(12.dp))
    }
}

@Composable
@Preview
private fun GiftWallWidgetPreview() {
    GiftWallContent(1, 100)
}