package com.qyqy.cupid.widgets

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.charmLevel
import com.qyqy.ucoo.account.wealthLevel

@Composable
fun LevelComposeView(charmLevel: Int, wealthLevel: Int, modifier: Modifier = Modifier) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        LevelBadge(level = wealthLevel, wealthLevels, modifier = Modifier.size(40.dp, 16.dp))
        Spacer(modifier = Modifier.width(4.dp))
        LevelBadge(level = charmLevel, charmLevels, modifier = Modifier.size(40.dp, 16.dp))
    }
}

@Composable
fun CharmLevelComposeView(user: User, modifier: Modifier = Modifier) {
    LevelBadge(level = user.charmLevel, charmLevels, modifier = modifier)
}

@Composable
fun WealthLevelComposeView(user: User, modifier: Modifier = Modifier) {
    LevelBadge(level = user.wealthLevel, wealthLevels, modifier = modifier)
}

@Composable
fun LevelComposeView(user: User, modifier: Modifier = Modifier) {
    LevelComposeView(charmLevel = user.charmLevel, wealthLevel = user.wealthLevel, modifier = modifier)
}

@Preview
@Composable
private fun PreviewLevelComposeView() {
    LevelComposeView(100, 100)
}

@Composable
private fun LevelBadge(level: Int, levels: IntArray, modifier: Modifier = Modifier) {
    val index = level.minus(1).coerceIn(0, 100).div(10).coerceIn(0, 9)
    Row(
        modifier = modifier.paint(painterResource(id = levels[index]), contentScale = ContentScale.FillBounds),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.weight(7f))
        Text(
            text = level.coerceAtLeast(1).toString(),
            modifier = Modifier.weight(7f),
            color = Color.White,
            fontSize = 10.sp,
            maxLines = 1,
            fontWeight = FontWeight.SemiBold,
            lineHeight = 10.sp,
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.weight(3f))
    }
}


private val wealthLevels = intArrayOf(
    R.drawable.ic_cpd_wealth_level_10,
    R.drawable.ic_cpd_wealth_level_20,
    R.drawable.ic_cpd_wealth_level_30,
    R.drawable.ic_cpd_wealth_level_40,
    R.drawable.ic_cpd_wealth_level_50,
    R.drawable.ic_cpd_wealth_level_60,
    R.drawable.ic_cpd_wealth_level_70,
    R.drawable.ic_cpd_wealth_level_80,
    R.drawable.ic_cpd_wealth_level_90,
    R.drawable.ic_cpd_wealth_level_100,
)

private val charmLevels = intArrayOf(
    R.drawable.ic_cpd_charm_level_10,
    R.drawable.ic_cpd_charm_level_20,
    R.drawable.ic_cpd_charm_level_30,
    R.drawable.ic_cpd_charm_level_40,
    R.drawable.ic_cpd_charm_level_50,
    R.drawable.ic_cpd_charm_level_60,
    R.drawable.ic_cpd_charm_level_70,
    R.drawable.ic_cpd_charm_level_80,
    R.drawable.ic_cpd_charm_level_90,
    R.drawable.ic_cpd_charm_level_100,
)
