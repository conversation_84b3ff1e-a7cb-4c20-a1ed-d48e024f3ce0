package com.qyqy.cupid.widgets

import android.content.Intent
import android.net.Uri
import android.os.SystemClock
import android.provider.Settings
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableLongState
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.platform.LocalContext
import androidx.core.app.ActivityCompat
import com.overseas.common.utils.PermissionUtil
import com.qyqy.cupid.im.panel.audio.PERMISSION_DIALOG_THRESHOLD
import com.qyqy.ucoo.BuildConfig
import com.qyqy.ucoo.R
import com.qyqy.ucoo.asActivity
import com.qyqy.ucoo.toastRes

@Composable
fun rememberPermissionLauncher(
    toastTip: Boolean = true,
    onDenied: (Array<String>) -> Unit = {},
    onRun: (Array<String>) -> Unit
): PermissionLauncher<Unit> {
    return rememberPermissionLauncher(toastTip, { _, it ->
        onDenied(it)
    }) { _, it ->
        onRun(it)
    }
}

@Composable
fun <T> rememberPermissionLauncher(
    toastTip: Boolean = true,
    onDenied: (T?, Array<String>) -> Unit = { _, _ -> },
    onRun: (T?, Array<String>) -> Unit
): PermissionLauncher<T> {

    val context = LocalContext.current

    val currentOnDenied by rememberUpdatedState(newValue = onDenied)

    val currentOnRun by rememberUpdatedState(newValue = onRun)

    val permissionsState = remember {
        mutableStateOf<Array<String>>(arrayOf())
    }

    val requestPermissionStartTimeState = remember {
        mutableLongStateOf(0L)
    }

    val tempFlagBox = remember {
        TempFlagBox<T>(null)
    }

    val settingsLauncher = rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) {
        val permissions = permissionsState.value
        if (PermissionUtil.hasPermissions(*permissions)) {
            currentOnRun(tempFlagBox.value, permissions)
        } else {
            currentOnDenied(tempFlagBox.value, permissions)
            if (toastTip) {
                toastRes(R.string.cpd需要同意权限才能使用此功能)
            }
        }
        tempFlagBox.value = null
    }

    val permissionLauncher = rememberLauncherForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { ret ->
        val permissions = ret.keys.toTypedArray()
        if (ret.values.any { !it }) {
            if (
                SystemClock.elapsedRealtime().minus(requestPermissionStartTimeState.longValue) < PERMISSION_DIALOG_THRESHOLD
                && !ActivityCompat.shouldShowRequestPermissionRationale(context.asActivity!!, permissions.first())
            ) {
                // toast这个不需要被toastTip控制
                toastRes(R.string.cpd请到设置中授予权限)
                val packageUri = Uri.parse("package:${BuildConfig.APPLICATION_ID}")
                settingsLauncher.launch(Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, packageUri))
            } else {
                if (toastTip) {
                    toastRes(R.string.cpd需要同意权限才能使用此功能)
                }
                currentOnDenied(tempFlagBox.value, permissions)
            }
        } else {
            currentOnRun(tempFlagBox.value, permissions)
        }
        tempFlagBox.value = null
    }

    return remember {
        PermissionLauncher(tempFlagBox, permissionsState, requestPermissionStartTimeState, permissionLauncher)
    }
}

@Stable
class PermissionLauncher<T>(
    private val tempFlagBox: TempFlagBox<T>,
    private val permissionsState: MutableState<Array<String>>,
    private val requestPermissionStartTimeState: MutableLongState,
    private val permissionLauncher: ActivityResultLauncher<Array<String>>,
) {

    fun launch(flag: T, permissions: Array<String>) {
        tempFlagBox.value = flag
        launch(permissions)
    }

    fun launch(permissions: Array<String>) {
        if (permissions.isEmpty()) {
            return
        }
        permissionsState.value = permissions
        requestPermissionStartTimeState.longValue = SystemClock.elapsedRealtime()
        permissionLauncher.launch(permissions)
    }
}

class TempFlagBox<T>(var value: T?)
