package com.qyqy.cupid.widgets

import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue

/**
 *  @time 2024/7/10
 *  <AUTHOR>
 *  @package com.qyqy.keya.utils
 */
open class BaseFieldFilter(defaultText: String) {
    private var inputValue = mutableStateOf(TextFieldValue(defaultText))

    protected open fun onFilter(inputTextFieldValue: TextFieldValue, lastTextFieldValue: TextFieldValue): TextFieldValue {
        return TextFieldValue()
    }

    protected open fun computePos(): Int {
        return 0
    }

    protected fun getNewTextRange(
        lastTextFiled: TextFieldValue,
        inputTextFieldValue: TextFieldValue,
    ): TextRange? {
        return null
    }

    protected fun getNewText(
        lastTextFiled: TextFieldValue,
        inputTextFieldValue: TextFieldValue,
    ): TextRange? {
        return null
    }

    fun getInputValue(): TextFieldValue {
        return inputValue.value
    }

    fun onValueChange(): (TextFieldValue) -> Unit {
        return {
            inputValue.value = onFilter(it, inputValue.value)
        }
    }

    val text: String
        get() {
            return inputValue.value.text
        }
}


/**
 * 过滤输入内容长度
 *
 * @param maxLength 允许输入长度，如果 小于 0 则不做过滤，直接返回原数据
 * */
open class FilterMaxLength(
    @androidx.annotation.IntRange(from = 0L)
    private val maxLength: Int,
    defaultText: String = "",
) : BaseFieldFilter(defaultText) {
    override fun onFilter(
        inputTextFieldValue: TextFieldValue,
        lastTextFieldValue: TextFieldValue,
    ): TextFieldValue {
        return filterMaxLength(inputTextFieldValue, lastTextFieldValue, maxLength)
    }

    private fun filterMaxLength(
        inputTextField: TextFieldValue,
        lastTextField: TextFieldValue,
        maxLength: Int,
    ): TextFieldValue {
        if (maxLength < 0) return inputTextField // 错误的长度，不处理直接返回

        if (inputTextField.text.length <= maxLength) return inputTextField // 总计输入内容没有超出长度限制


        // 输入内容超出了长度限制
        // 这里要分两种情况：
        // 1. 直接输入的，则返回原数据即可
        // 2. 粘贴后会导致长度超出，此时可能还可以输入部分字符，所以需要判断后截断输入

        val inputCharCount = inputTextField.text.length - lastTextField.text.length
        if (inputCharCount > 1) { // 同时粘贴了多个字符内容
            val allowCount = maxLength - lastTextField.text.length
            // 允许再输入字符已经为空，则直接返回原数据
            if (allowCount <= 0) return lastTextField

            // 还有允许输入的字符，则将其截断后插入
            val newString = StringBuffer()
            newString.append(lastTextField.text)
            val newChar = inputTextField.text.substring(0, allowCount)
            newString.insert(lastTextField.selection.start, newChar)
            return lastTextField.copy(text = newString.toString(), selection = TextRange(lastTextField.selection.start + newChar.length))
        } else { // 正常输入
            return if (inputTextField.selection.collapsed) { // 如果当前不是选中状态，则使用上次输入的光标位置，如果使用本次的位置，光标位置会 +1
                lastTextField
            } else { // 如果当前是选中状态，则使用当前的光标位置
                lastTextField.copy(selection = inputTextField.selection)
            }
        }
    }

    fun getMaxLength() = maxLength
    fun getLength() = getInputValue().text.length
}