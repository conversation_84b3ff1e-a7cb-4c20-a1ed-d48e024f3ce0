package com.qyqy.cupid.widgets

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.draggable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.data.CallState
import com.qyqy.cupid.data.VoiceRoomInviteBean
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.ui.CupidViewModel
import com.qyqy.ucoo.R
import com.qyqy.ucoo.ReportExposureCompose
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.asComponentActivity
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.im.compat.IMCompatListener
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.WatchMessageEventEffect
import kotlinx.coroutines.flow.MutableStateFlow
import kotlin.math.roundToInt

// private val cupidVoiceInviteBean = MutableStateFlow<GlobalWidgetBean<VoiceRoomInviteBean>?>(null)
private val cupidVoiceInviteBean = MutableStateFlow<VoiceRoomInviteBean>(VoiceRoomInviteBean())

@Composable
fun CupidGlobalVoiceRoomInviteWidget() {
    val current = cupidVoiceInviteBean.collectAsStateWithLifecycle().value

    val activity = LocalContext.current.asComponentActivity!!
    val viewModel = viewModel<CupidViewModel>(viewModelStoreOwner = activity)
    val callState = viewModel.c2cCallingHelper.rememberC2cCallState()
    val voiceState = viewModel.voiceLiveHelper.voiceLiveValue

    val listener = object : IMCompatListener {

        override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
            if (!message.isSystemMsg || offline) {
                return
            }
            when (message.cmd) {
                "audioroom_recommend" -> {
                    message.getJsonValue<VoiceRoomInviteBean>("room_info")?.also {
                        cupidVoiceInviteBean.value = it
                    }
                }

                else -> {}
            }
        }
    }
    WatchMessageEventEffect(listener = listener)

    DisposableEffect(key1 = Unit) {

        onDispose {
            cupidVoiceInviteBean.value = VoiceRoomInviteBean()
        }
    }

//    LaunchOnceEffect {
//        cupidVoiceInviteBean.value = VoiceRoomInviteBean(
//            id = 10,1,2, VoiceRoomInviteBean.Owner("https://media.ucoofun.com/opsite/common/BG012x_2.png?x-oss-process=image/format,webp","用户米",123),
//            listOf(),0,"测试标题"
//        )
//    }

    // 语音房和音视频通话状态下, 不显示
    val isShowing = rememberSaveable(current) {
        mutableStateOf(current.id != -1 && callState.value == CallState.Idea && voiceState == null)
    }

//    val pageFilter = remember(callState.value, voiceState) {
//        derivedStateOf {
//            callState.value == CallState.Idea && voiceState == null
//        }
//    }
    // 如果在显示的状态下, 进入了音视频通话或语音房逻辑, 则直接隐藏
    LaunchedEffect(key1 = callState.value, key2 = voiceState) {
        if (isShowing.value && (callState.value != CallState.Idea || voiceState != null)) {
            isShowing.value = false
        }
    }

    AnimatedVisibility(
        visible = isShowing.value,
//                && pageFilter.value,
        enter = slideInVertically(),
        exit = slideOutVertically(targetOffsetY = { -it }),
    ) {
        ReportExposureCompose(exposureName = "jp_ar_intro_dialog_expose")

        VoiceRoomInviteWidget(current, onDismiss = {
            isShowing.value = false
        }) {
            viewModel.voiceLiveHelper.joinVoiceLiveRoom(it, from = "jp_ar_intro_dialog_click_accept")
            isShowing.value = false
        }
    }

//    GlobalWidget(state = cupidVoiceInviteBean, null) { data, dismiss ->
//        VoiceRoomInviteWidget(data) {
//            dismiss()
//        }
//    }
}

@Composable
private fun VoiceRoomInviteWidget(
    bean: VoiceRoomInviteBean,
    modifier: Modifier = Modifier,
    onDismiss: () -> Unit = {},
    onEnterRoom: (Int) -> Unit = {},
) {
    var offsetY by remember { mutableFloatStateOf(0f) }
    Box(
        modifier
            .statusBarsPadding()
            .offset { IntOffset(0, offsetY.roundToInt()) }
            .draggable(orientation = Orientation.Vertical, state = androidx.compose.foundation.gestures.rememberDraggableState { delta ->
                if (offsetY <= 0) {
                    offsetY += delta
                    if (offsetY > 0) {
                        offsetY = 0f
                    }
                }
            }, onDragStopped = {
                if (offsetY != 0f) {
                    onDismiss()
                }
            })
    ) {
        Column(
            modifier = Modifier
                .padding(top = 16.dp)
                .paint(
                    painterResource(id = R.drawable.ic_cpd_voice_invite_background), contentScale = ContentScale.FillWidth
                )
                .fillMaxWidth()
                .padding(start = 12.dp, end = 12.dp, bottom = 16.dp)
                .heightIn(194.dp, 212.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(56.dp))
            Text(
                text = stringResource(id = R.string.cpd_invite_to_voice, bean.owner.nickname),
                color = CpdColors.FF1D2129,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier
                    .fillMaxWidth()
                    .basicMarquee(),
                textAlign = TextAlign.Center,
                maxLines = 1
            )
            Box(modifier = Modifier.padding(vertical = 8.dp)) {
                bean.recommendUserList.forEachIndexed { index, user ->
                    ComposeImage(
                        model = user.avatarUrl, modifier = Modifier
                            .padding(start = (index * 20).dp)
                            .zIndex(-1f * index)
                            .size(24.dp)
                            .clip(CircleShape)
                    )
                }
            }
            Text(
                stringResource(id = R.string.cpd_invite_member_tips, bean.memberCnt),
                color = CpdColors.FF86909C,
                fontSize = 12.sp,
                modifier = Modifier
                    .weight(1f)
                    .padding(bottom = 4.dp)
            )
            Box(
                modifier = Modifier
                    .sizeIn(167.dp, 36.dp)
                    .background(
                        brush = Brush.verticalGradient(
                            listOf(Color(0xFFE6D7FF), Color(0xFFAC8FFF))
                        ), shape = CircleShape
                    )
                    .border(
                        BorderStroke(
                            0.5.dp, brush = Brush.verticalGradient(
                                listOf(Color.White.copy(0.3f), Color.White.copy(0.8f))
                            )
                        ), shape = CircleShape
                    )
                    .click {
                        onEnterRoom(bean.id)
                    }, contentAlignment = Alignment.Center
            ) {
                Text(stringResource(id = R.string.cpd_accept), color = Color.White, fontSize = 14.sp, fontWeight = FontWeight.Medium)
            }
            Spacer(modifier = Modifier.height(16.dp))
        }
        ComposeImage(
            model = bean.owner.avatarUrl, modifier = Modifier
                .size(64.dp)
                .clip(CircleShape)
                .align(Alignment.TopCenter)
                .border(1.dp, Color.White, CircleShape)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun VoiceRoomInvitePreview() {
    VoiceRoomInviteWidget(bean = VoiceRoomInviteBean(), modifier = Modifier.fillMaxWidth(), {}) {}
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun RecommendUserWidget(
    user: AppUser,
    desc: String,
    labelList: List<String>,
    buttonText: String,
    modifier: Modifier = Modifier,
    onDismiss: () -> Unit = {},
    onClick: () -> Unit = {},
) {
    var offsetY by remember { mutableFloatStateOf(0f) }
    Box(
        modifier
            .statusBarsPadding()
            .offset { IntOffset(0, offsetY.roundToInt()) }
            .draggable(orientation = Orientation.Vertical, state = androidx.compose.foundation.gestures.rememberDraggableState { delta ->
                if (offsetY <= 0) {
                    offsetY += delta
                    if (offsetY > 0) {
                        offsetY = 0f
                    }
                }
            }, onDragStopped = {
                if (offsetY != 0f) {
                    onDismiss()
                }
            })
    ) {
        Column(
            modifier = Modifier
                .padding(top = 16.dp)
                .paint(
                    painterResource(id = R.drawable.ic_cpd_voice_invite_background), contentScale = ContentScale.FillWidth
                )
                .fillMaxWidth()
                .padding(start = 12.dp, end = 12.dp, bottom = 16.dp)
                .heightIn(194.dp, 212.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(56.dp))

            Text(
                text = desc,
                color = CpdColors.FF1D2129,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier
                    .fillMaxWidth(),
                textAlign = TextAlign.Center,
                maxLines = 2
            )

            if (labelList.isNotEmpty()) {
                Spacer(modifier = Modifier.weight(1f))
                FlowRow(
                    modifier = Modifier
                        .padding(start = 12.dp, end = 12.dp)
                        .fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(4.dp, Alignment.CenterHorizontally),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    for (label in labelList) {
                        Text(
                            text = label,
                            color = Color(0xFF945EFF),
                            fontSize = 12.sp,
                            modifier = Modifier
                                .background(Color.White, Shapes.extraSmall)
                                .padding(horizontal = 6.dp, vertical = 4.dp)
                        )
                    }
                }
            }
            Spacer(modifier = Modifier.weight(1f))
            Box(
                modifier = Modifier
                    .sizeIn(167.dp, 36.dp)
                    .background(
                        brush = Brush.verticalGradient(
                            listOf(Color(0xFFE6D7FF), Color(0xFFAC8FFF))
                        ), shape = CircleShape
                    )
                    .border(
                        BorderStroke(
                            0.5.dp, brush = Brush.verticalGradient(
                                listOf(Color.White.copy(0.3f), Color.White.copy(0.8f))
                            )
                        ), shape = CircleShape
                    )
                    .click {
                        onClick()
                    },
                contentAlignment = Alignment.Center
            ) {
                Text(buttonText, color = Color.White, fontSize = 14.sp, fontWeight = FontWeight.Medium)
            }
            Spacer(modifier = Modifier.weight(1f))
        }

        ComposeImage(
            model = user.avatarUrl,
            modifier = Modifier
                .align(Alignment.TopCenter)
                .size(64.dp)
                .clip(CircleShape)
                .border(1.dp, Color.White, CircleShape)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun VoiceRoomInvitePreview2() {
    //            user.locationLabel,
//            if (user.height > 0) "${user.height}cm" else "",
//            user.career
    RecommendUserWidget(
        user = userForPreview,
        desc = "还是说",
        labelList = listOf("第三方哈市的飞机", "但是付款啦但是了", "都是粉丝的方式的话"),
        buttonText = stringResource(id = R.string.cpd接受邀请),
        modifier = Modifier.fillMaxWidth(),
        onDismiss = {}) {}
}