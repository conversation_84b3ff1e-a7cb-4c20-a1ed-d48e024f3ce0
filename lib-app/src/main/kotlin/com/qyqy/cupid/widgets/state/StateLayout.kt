package com.qyqy.cupid.widgets.state

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.rememberScrollableState
import androidx.compose.foundation.gestures.scrollable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.CpdColors
import com.qyqy.cupid.widgets.state.CupidStateLayoutDefaults.defaultEmpty
import com.qyqy.cupid.widgets.state.CupidStateLayoutDefaults.defaultError
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.state.ContentState
import com.qyqy.ucoo.compose.state.StateLayout
import com.qyqy.ucoo.compose.state.StateLayoutDefaults
import com.qyqy.ucoo.compose.state.StateValue
import com.qyqy.ucoo.compose.state.StateViewModel
import com.qyqy.ucoo.compose.state.rememberContentState
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.ui.LoadingLayout
import eu.bambooapps.material3.pullrefresh.PullRefreshIndicator
import eu.bambooapps.material3.pullrefresh.PullRefreshIndicatorColors
import eu.bambooapps.material3.pullrefresh.PullRefreshIndicatorDefaults
import eu.bambooapps.material3.pullrefresh.PullRefreshState
import eu.bambooapps.material3.pullrefresh.pullRefresh
import eu.bambooapps.material3.pullrefresh.rememberPullRefreshState


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CupidStateLayout(
    contentState: ContentState,
    isRefreshing: Boolean,
    onRefresh: () -> Unit,
    modifier: Modifier = Modifier,
    empty: @Composable BoxScope.(errorMsg: String?) -> Unit = {
        defaultEmpty(emptyText = it, modifier = Modifier.verticalScroll(rememberScrollState()))
    },
    error: @Composable BoxScope.(errorMsg: String?) -> Unit = {
        defaultError(errorText = it, modifier = Modifier.verticalScroll(rememberScrollState()))
    },
    content: @Composable BoxScope.() -> Unit,
) {
    val refreshState = rememberPullRefreshState(refreshing = isRefreshing, onRefresh)
    Box(modifier = modifier.pullRefresh(refreshState)) {
        LoadingLayout(modifier = Modifier.fillMaxSize()) {
            when (val state = contentState.current) {
                is StateValue.Empty -> {
                    empty(state.message)
                }

                is StateValue.Error -> Box {
                    error(state.message)
                }

                StateValue.Success, StateValue.Refreshing, StateValue.LoadMore -> content()
                else -> {}
            }
        }
        PullRefreshIndicator(
            refreshing = isRefreshing,
            state = refreshState,
            modifier = Modifier.align(Alignment.TopCenter),
            colors = PullRefreshIndicatorDefaults.colors(
                containerColor = Color.White,
                contentColor = CpdColors.FFFF5E8B,
            ),
            scale = true
        )
    }

}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CupidPullRefreshBox(
    modifier: Modifier = Modifier,
    isRefreshing: Boolean,
    onRefresh: () -> Unit,
    colors: PullRefreshIndicatorColors = PullRefreshIndicatorDefaults.colors(
        containerColor = Color.White,
        contentColor = CpdColors.FFFF5E8B,
    ),
    pullRefreshState: PullRefreshState = rememberPullRefreshState(refreshing = isRefreshing, onRefresh = onRefresh),
    contentAlignment: Alignment = Alignment.TopStart,
    content: @Composable BoxScope.() -> Unit,
) {
    Box(
        modifier = modifier.pullRefresh(pullRefreshState),
        contentAlignment = contentAlignment
    ) {
        content()
        PullRefreshIndicator(
            refreshing = isRefreshing,
            state = pullRefreshState,
            modifier = Modifier.align(Alignment.TopCenter),
            colors = colors,
            scale = true
        )
    }
}


object CupidStateLayoutDefaults {
    @Composable
    fun defaultError(
        @DrawableRes errorDrawableRes: Int = R.drawable.cupid_empty,
        errorText: String? = null,
        modifier: Modifier = Modifier,
    ): Unit {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .then(modifier),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Image(
                painter = painterResource(id = errorDrawableRes), contentDescription = "error", modifier = Modifier.size(
                    160.dp, 160.dp
                )
            )
            Spacer(modifier = Modifier.height(12.dp))
            if (!errorText.isNullOrEmpty()) {
                Text(fontSize = 12.sp, text = errorText, color = Color(0x66FFFFFF))
            }
        }
    }

    @Composable
    fun defaultEmpty(
        @DrawableRes emptyDrawableRes: Int = R.drawable.cupid_empty,
        emptyText: String? = null,
        modifier: Modifier = Modifier,
    ): Unit {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .then(modifier),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Image(
                painter = painterResource(id = emptyDrawableRes),
                contentDescription = "empty",
                modifier = Modifier.size(160.dp, 160.dp)
            )
            Spacer(modifier = Modifier.height(12.dp))
            if (!emptyText.isNullOrBlank()) {
                Text(fontSize = 12.sp, text = emptyText, color = MaterialTheme.colorScheme.onSurface)
            }
        }
    }

    @Composable
    fun LoadMoreIndicator(viewModel: StateViewModel<*, *>, @StringRes noMoreStringResId: Int = R.string.cupid_no_more_data) {
        val visible by viewModel.loadMoreVisibleFlow.collectAsState()
        val hasMore by viewModel.hasMoreFlow.collectAsState()
        if (visible) {
            BottomLoadBar(hasMore, noMoreStringResId = noMoreStringResId)
        }

        if (hasMore) {
            LaunchedEffect("loadMore") {
                viewModel.loadMore()
            }
        }
    }


    @Composable
    fun BottomLoadBar(
        hasMore: Boolean,
        modifier: Modifier = Modifier,
        @StringRes noMoreStringResId: Int = R.string.cupid_no_more_data,
    ) {
        Box(
            modifier = modifier
                .fillMaxWidth()
                .height(48.dp),
            contentAlignment = Alignment.Center
        ) {
            if (hasMore) {
                CircularProgressIndicator(modifier = Modifier.requiredSize(20.dp), strokeWidth = 4.dp)
            } else {
                Text(text = stringResource(id = noMoreStringResId), color = CpdColors.FF1D2129, fontSize = 12.sp)
            }
        }
    }

    @Composable
    fun LoadMoreIndicatorWithoutVM(visible: Boolean, hasMore: Boolean, noMoreStr: String, onLoadMore: () -> Unit = {}) {

        if (visible) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(48.dp),
                contentAlignment = Alignment.Center
            ) {
                if (hasMore) {
                    CircularProgressIndicator(modifier = Modifier.requiredSize(24.dp))
                } else {
                    Text(text = noMoreStr, color = CpdColors.FF1D2129, fontSize = 12.sp)
                }
            }
        }

        if (hasMore) {
            LaunchedEffect("loadMore") {
                onLoadMore()
            }
        }
    }
}


@Preview
@Composable
private fun CupidStateLayoutPreviewer() {
    val contentState = rememberContentState()
    StateLayout(contentState, true, onRefresh = {}) {

    }
}

@Preview
@Composable
private fun BottomBarPreview() {
    AppTheme {
        StateLayoutDefaults.BottomLoadBar(hasMore = true)
    }
}