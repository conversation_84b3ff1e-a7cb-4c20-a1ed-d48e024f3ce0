package com.qyqy.cupid.widgets.state

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyGridItemScope
import androidx.compose.foundation.lazy.grid.LazyGridItemSpanScope
import androidx.compose.foundation.lazy.grid.LazyGridScope
import androidx.compose.foundation.lazy.grid.LazyGridState
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.qyqy.cupid.widgets.state.CupidStateLayoutDefaults.defaultEmpty
import com.qyqy.cupid.widgets.state.CupidStateLayoutDefaults.defaultError
import com.qyqy.ucoo.compose.state.StateViewModel
import com.qyqy.ucoo.compose.state.StateViewModelWithIntPage
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import kotlinx.coroutines.CoroutineScope

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun <Page, T> CupidStateListView(
    viewModel: StateViewModel<Page, T>,
    keyProvider: ((Int, T) -> Any)? = null,
    modifier: Modifier = Modifier,
    empty: @Composable BoxScope.(errorMsg: String?) -> Unit = {
        defaultEmpty(emptyText = it, modifier = Modifier.verticalScroll(rememberScrollState()))
    },
    error: @Composable BoxScope.(errorMsg: String?) -> Unit = {
        defaultError(errorText = it, modifier = Modifier.verticalScroll(rememberScrollState()))
    },
    listItem: @Composable (item: T, index: Int, coroutineScope: CoroutineScope, loadingState: MutableState<Boolean>) -> Unit,
) {
    val coroutineScope = rememberCoroutineScope()
    val list by viewModel.listFlow.collectAsStateWithLifecycle()
    val isRefreshing by viewModel.isRefreshingFlow.collectAsState()
    CupidStateLayout(
        contentState = viewModel.contentState,
        isRefreshing = isRefreshing,
        empty = empty,
        error = error,
        modifier = modifier,
        onRefresh = {
            viewModel.refresh()
        }
    ) {
        val loadingState = LocalContentLoading.current
        LazyColumn(
            modifier = Modifier.fillMaxSize()
        ) {
            itemsIndexed(items = list, key = keyProvider) { index, item ->
                Box(modifier = Modifier.animateItemPlacement()) {
                    listItem(item, index, coroutineScope, loadingState)
                }
            }
            item {
                CupidStateLayoutDefaults.LoadMoreIndicator(viewModel)
            }
        }
    }
}

@Composable
fun <Page, T> CupidStateGrid(
    viewModel: StateViewModel<Page, T>,
    spanCount: Int = 2,
    span: (LazyGridItemSpanScope.(item: T) -> GridItemSpan)? = null,
    key: ((T) -> Any)? = null,
    modifier: Modifier = Modifier,
    horizontalArrangement: Arrangement.Horizontal = Arrangement.Start,
    verticalArrangement: Arrangement.Vertical = Arrangement.Top,
    itemContent: @Composable LazyGridItemScope.(item: T) -> Unit,
) {
    val list by viewModel.listFlow.collectAsStateWithLifecycle()
    val isRefreshing by viewModel.isRefreshingFlow.collectAsState()
    CupidStateLayout(viewModel.contentState, isRefreshing, onRefresh = {
        viewModel.refresh()
    }) {
        LazyVerticalGrid(
            columns = GridCells.Fixed(spanCount),
            content = {
                items(list, key, span, itemContent = itemContent)
                item(span = { GridItemSpan(spanCount) }) {
                    CupidStateLayoutDefaults.LoadMoreIndicator(viewModel)
                }
            }, horizontalArrangement = horizontalArrangement, verticalArrangement = verticalArrangement, modifier = modifier.fillMaxSize()
        )
    }
}

@Composable
fun <T> CupidStateListView(
    viewModel: StateViewModelWithIntPage<T>,
    keyProvider: ((Int, T) -> Any)? = null,
    modifier: Modifier = Modifier,
    empty: @Composable BoxScope.(errorMsg: String?) -> Unit = {
        defaultEmpty(emptyText = it, modifier = Modifier.verticalScroll(rememberScrollState()))
    },
    error: @Composable BoxScope.(errorMsg: String?) -> Unit = {
        defaultError(errorText = it, modifier = Modifier.verticalScroll(rememberScrollState()))
    },
    listItem: @Composable (item: T, index: Int, coroutineScope: CoroutineScope, loadingState: MutableState<Boolean>) -> Unit,
) {
    CupidStateListView<Int, T>(viewModel, keyProvider, modifier, empty, error, listItem)
}


//region new


@Composable
fun <Page, T> CupidStateGridWidget(
    viewModel: StateViewModel<Page, T>,
    modifier: Modifier = Modifier,
    spanCount: Int = 2,
    enableRefresh: Boolean = true,
    enableLoadMore: Boolean = true,
    onRefreshed: () -> Unit = {},
    scrollState: LazyGridState = rememberLazyGridState(),
    horizontalArrangement: Arrangement.Horizontal = Arrangement.Start,
    verticalArrangement: Arrangement.Vertical = Arrangement.Top,
    bottomLoading: @Composable ((StateViewModel<Page, T>) -> Unit)? = null,
    content: LazyGridScope.(List<T>) -> Unit,
) {
    val list by viewModel.listFlow.collectAsStateWithLifecycle()
    val isRefreshing by viewModel.isRefreshingFlow.collectAsState()
    CupidStateLayout(viewModel.contentState, isRefreshing, onRefresh = {
        if (enableRefresh) {
            viewModel.refresh()
            onRefreshed()
        }
    }) {
        LazyVerticalGrid(
            state = scrollState,
            columns = GridCells.Fixed(spanCount),
            horizontalArrangement = horizontalArrangement,
            verticalArrangement = verticalArrangement,
            modifier = modifier
        ) {
            content(list)
            item(span = { GridItemSpan(spanCount) }) {
                (bottomLoading?.invoke(viewModel)) ?: CupidStateLayoutDefaults.LoadMoreIndicator(viewModel)
            }
        }
    }
}


@Composable
fun <Page, T> CupidStateList(
    viewModel: StateViewModel<Page, T>,
    modifier: Modifier = Modifier,
    enableRefresh: Boolean = true,
    enableLoadMore: Boolean = true,
    horizontalArrangement: Arrangement.Horizontal = Arrangement.Start,
    verticalArrangement: Arrangement.Vertical = Arrangement.Top,
    bottomLoading: @Composable ((StateViewModel<Page, T>) -> Unit)? = null,
    empty: @Composable BoxScope.(errorMsg: String?) -> Unit = {
        defaultEmpty(emptyText = it, modifier = Modifier.verticalScroll(rememberScrollState()))
    },
    error: @Composable BoxScope.(errorMsg: String?) -> Unit = {
        defaultError(errorText = it, modifier = Modifier.verticalScroll(rememberScrollState()))
    },
    content: LazyListScope.(List<T>) -> Unit,
) {
    val list by viewModel.listFlow.collectAsStateWithLifecycle()
    val isRefreshing by viewModel.isRefreshingFlow.collectAsState()
    CupidStateLayout(viewModel.contentState,
        isRefreshing,
        empty = empty,
        error = error,
        onRefresh = {
            if (enableRefresh) {
                viewModel.refresh()
            }
        }) {
        Column {
            LazyColumn(
                verticalArrangement = verticalArrangement,
                modifier = modifier
            ) {
                content(list)
                item() {
                    (bottomLoading?.invoke(viewModel)) ?: CupidStateLayoutDefaults.LoadMoreIndicator(viewModel)
                }
            }
        }
    }
}

//endregion