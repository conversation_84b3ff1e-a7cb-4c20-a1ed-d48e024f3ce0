package com.qyqy.cupid.widgets.webview

import android.graphics.Bitmap
import android.webkit.WebChromeClient
import android.webkit.WebView

/**
 * Created By <PERSON> On 2024/1/29
 */

/**
 * AccompanistWebChromeClient
 *
 * A parent class implementation of WebChromeClient that can be subclassed to add custom behaviour.
 *
 * As Accompanist <PERSON> needs to set its own web client to function, it provides this intermediary
 * class that can be overriden if further custom behaviour is required.
 */
public open class AccompanistWebChromeClient : WebChromeClient() {
    public open lateinit var state: WebViewState
        internal set

    override fun onReceivedTitle(view: WebView, title: String?) {
        super.onReceivedTitle(view, title)
        state.pageTitle = title
    }

    override fun onReceivedIcon(view: WebView, icon: Bitmap?) {
        super.onReceivedIcon(view, icon)
        state.pageIcon = icon
    }

    override fun onProgressChanged(view: WebView, newProgress: Int) {
        super.onProgressChanged(view, newProgress)
        if (state.loadingState !is LoadingState.Loading) return
        state.loadingState = LoadingState.Loading(newProgress / 100.0f)
    }
}