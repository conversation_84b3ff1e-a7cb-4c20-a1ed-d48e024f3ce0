package com.qyqy.cupid.widgets.webview

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.webkit.CookieManager
import android.webkit.JsPromptResult
import android.webkit.PermissionRequest
import android.webkit.ValueCallback
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.DialogProperties
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.viewmodel.compose.viewModel
import com.google.gson.JsonObject
import com.hjq.language.MultiLanguages
import com.overseas.common.utils.postToMainThread
import com.qyqy.cupid.config.offline.OfflinePkgManager
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.dialog.DialogButton
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.dialog.NormalDialog
import com.qyqy.cupid.ui.dialog.TitleAlertDialog
import com.qyqy.cupid.ui.dialog.rememberSimpleDialogQueue
import com.qyqy.cupid.ui.navigateToProfile
import com.qyqy.cupid.widgets.rememberPermissionLauncher
import com.qyqy.ucoo.R
import com.qyqy.ucoo.accountManager
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.asActivity
import com.qyqy.ucoo.base.show
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.ui.AppearanceStatusBars
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.core.rtm.RtmManager
import com.qyqy.ucoo.core.rtm.channel.IMessageChannel
import com.qyqy.ucoo.im.compat.ConversationType
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.MessageBundle
import com.qyqy.ucoo.im.compat.WatchRecvNewCustomMessage
import com.qyqy.ucoo.im.compat.isMainRC
import com.qyqy.ucoo.im.compat.isMainTIM
import com.qyqy.ucoo.loginCoroutineScope
import com.qyqy.ucoo.user.WalletContract
import com.qyqy.ucoo.user.WalletViewModel
import com.qyqy.ucoo.utils.GiftEffectHelper
import com.qyqy.ucoo.utils.takeIsNotEmpty
import com.qyqy.ucoo.utils.web.AppBridgeWebView
import com.qyqy.ucoo.utils.web.WBH5FaceVerifySDK
import com.qyqy.ucoo.utils.web.bindWebViewToLifecycle
import com.qyqy.ucoo.utils.web.getValueByUrl
import com.qyqy.ucoo.utils.web.handler.BaseBridgeHandler
import com.qyqy.ucoo.utils.web.handler.CupidApiErrorHandler
import com.qyqy.ucoo.utils.web.handler.HandlerName
import com.qyqy.ucoo.utils.web.handler.WebBridgeHandler
import com.qyqy.ucoo.utils.web.putValueByUrl
import com.smallbuer.jsbridge.core.Bridge
import com.smallbuer.jsbridge.core.BridgeLog
import com.smallbuer.jsbridge.core.BridgeTiny
import com.smallbuer.jsbridge.core.CallBackFunction
import com.smallbuer.jsbridge.core.IWebView
import com.yy.yyeva.EvaAnimConfig
import com.yy.yyeva.inter.IEvaAnimListener
import com.yy.yyeva.util.EvaConstant
import com.yy.yyeva.view.EvaAnimViewV3
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import ren.yale.android.cachewebviewlib.WebViewCacheInterceptorInst
import java.io.File
import kotlin.time.Duration.Companion.seconds

@Stable
private data class WebEffect(
    val file: File,
    @Volatile var isPlayed: Boolean = false,
)

val handlerJumpAppCupid = object : BaseBridgeHandler() {
    override fun handlerV2(context: Context, data: JsonObject, callback: CallBackFunction) {
        val params = data.get("params")?.takeIf { it.isJsonObject }?.asJsonObject
        data.getString("target_name")?.also {
            if (it.startsWith("ucoo://page") || it.startsWith("http://") || it.startsWith("https://")) {
                AppLinkManager.controller?.navigateByLink(it)
                return
            }
            when (it) {
                "user_home" -> { // 用户主页
                    params?.getString("user_id")?.also { id ->
                        AppLinkManager.controller?.navigateToProfile(id)
                    }
                }

                "private_chat" -> {
                    val userId = params?.getString("user_id")?.takeIf { id ->
                        id.isNotEmpty()
                    } ?: kotlin.run {
                        return
                    }
                    val msg = params.getString("msg")?.takeIf { msg ->
                        msg.isNotBlank()
                    }
                    loginCoroutineScope.launch {
                        if (msg != null) {
                            IMCompatCore.sendC2CMessage(userId, MessageBundle.Text.create(msg))
                            delay(50)
                        }
                        AppLinkManager.controller?.navigate(
                            CupidRouters.C2CChat,
                            mapOf("userId" to userId)
                        )
                    }
                }

                "recharge_coin" -> {
                    AppLinkManager.controller?.navigateByLink("ucoo://page/dialog?action=recharge")
                }

                else -> {}
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WebComposeView(
    url: String,
    modifier: Modifier = Modifier,
    handlerJumpApp: BaseBridgeHandler = handlerJumpAppCupid,
    onBack: () -> Unit = {},
) {

    val navController = LocalAppNavController.current

    val navigator = rememberWebViewNavigator()

    val state = rememberSaveableWebViewState()

    var titleVisible by rememberSaveable {
        mutableStateOf(false)
    }

    var jsbTitle by remember {
        mutableStateOf<String?>(null)
    }

    val statusBars = WindowInsets.statusBars
    val navigationBars = WindowInsets.navigationBars
    val density = LocalDensity.current

    // 自带标题是白色的，所以标题显示时是黑字，默认是白字，因为一般网页背景都为深色
    AppearanceStatusBars(isLight = titleVisible)

    Column(modifier = modifier) {

        if (titleVisible) {
            TopAppBar(
                title = {
                    (jsbTitle ?: state.pageTitle)?.takeIsNotEmpty()?.also {
                        Text(text = it, modifier = Modifier.basicMarquee(), maxLines = 1)
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .statusBarsPadding(),
                navigationIcon = {
                    IconButton(onClick = {
                        if (navigator.canGoBack) {
                            navigator.navigateBack()
                        } else {
                            onBack()
                        }
                    }) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_cpd_navigation_back),
                            contentDescription = "back",
                            tint = Color(0xFF1D2129),
                        )
                    }
                }
            )

            HorizontalDivider(color = Color(0xFFF1F2F3), thickness = 1.dp)
        }


        Box(modifier = Modifier.fillMaxSize()) {

            val lifecycle = LocalLifecycleOwner.current.lifecycle

            val context = LocalContext.current

            var applyStatusBarPadding by remember {
                mutableStateOf(false)
            }

            var applyNavigationBarPadding by remember {
                mutableStateOf(false)
            }

            val dialogQueue = rememberSimpleDialogQueue(autoEnable = false)

            var permissionCallback by remember {
                mutableStateOf<Pair<BaseBridgeHandler, CallBackFunction>?>(null)
            }

            val launcher = rememberPermissionLauncher(onDenied = {
                val (handler, callback) = permissionCallback ?: return@rememberPermissionLauncher
                handler.apply {
                    callback.sendFailure(context, -1)
                }
                permissionCallback = null
            }) {
                val (handler, callback) = permissionCallback ?: return@rememberPermissionLauncher
                handler.apply {
                    callback.sendSuccess(context, 0)
                }
                permissionCallback = null
            }

            val viewModel = viewModel<WalletViewModel>()

            val loading = LocalContentLoading.current

            LaunchedEffect(Unit) {

                viewModel.uiState.onEach {
                    loading.value = it.loadingState.isLoading
                }.launchIn(this)

                viewModel.effect.onEach {
                    it.show()
                }.launchIn(this)

            }

            val effectFileState = remember {
                mutableStateOf<WebEffect?>(null)
            }

            val scope = rememberCoroutineScope()

            var mChannel by remember {
                mutableStateOf<IMessageChannel?>(null)
            }

            var timChannel by remember {
                mutableStateOf<IMessageChannel?>(null)
            }
            var webHandler: AppBridgeWebView? by remember {
                mutableStateOf(null)
            }
            WatchRecvNewCustomMessage {
                if (!it.isSystemMsg) {
                    return@WatchRecvNewCustomMessage
                }
                webHandler?.callHandler(
                    "receiveSystemMessage",
                    it.rawContent,
                    null
                )
            }
            WebView(
                state = state,
                modifier = Modifier
                    .alpha(0.99f) //1.0==alpha,在加载的过程中有bug
                    .run {
                        val then = if (applyStatusBarPadding && !titleVisible) {
                            statusBarsPadding()
                        } else {
                            this
                        }
                        if (applyNavigationBarPadding) {
                            then.navigationBarsPadding()
                        } else {
                            then
                        }
                    }
                    .fillMaxSize(),
                navigator = navigator,
                onCreated = { webView ->
                    webView.setBackgroundColor(android.graphics.Color.WHITE)
                    val title = url.getValueByUrl("title")
                    val wideViewPort = url.getValueByUrl("wideViewPort")?.toBoolean() ?: false

                    titleVisible = title?.isNotEmpty() == true
                    jsbTitle = title

                    with(webView.settings) {
                        setSupportZoom(false)
                        builtInZoomControls = false
                        defaultTextEncodingName = "utf-8"
                        defaultFontSize = 14
                        layoutAlgorithm = android.webkit.WebSettings.LayoutAlgorithm.SINGLE_COLUMN
                        //两者都可以
                        mixedContentMode = mixedContentMode
                        mixedContentMode = android.webkit.WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
                        useWideViewPort = wideViewPort
                    }

                    with(webView) {
                        CookieManager.getInstance().setAcceptThirdPartyCookies(this, true)
                        setVerticalScrollbarOverlay(false)
                        isHorizontalScrollBarEnabled = false
                        setHorizontalScrollbarOverlay(false)
                        overScrollMode = WebView.OVER_SCROLL_NEVER
                        isFocusable = true
                        isVerticalScrollBarEnabled = false
                        isHorizontalScrollBarEnabled = false

                        bindWebViewToLifecycle(lifecycle)
                    }

                    with(webView as AppBridgeWebView) {
                        webHandler = webView

                        addHandlerLocal(
                            HandlerName.API_ERROR,
                            CupidApiErrorHandler(navController, dialogQueue)
                        )

                        addHandlerLocal(HandlerName.FINISH, object : BaseBridgeHandler() {
                            override fun handlerV2(
                                context: Context,
                                data: JsonObject,
                                callback: CallBackFunction,
                            ) {
                                onBack()
                            }
                        })

                        addHandlerLocal(HandlerName.TITLE, object : BaseBridgeHandler() {

                            override fun handlerV2(
                                context: Context,
                                data: JsonObject,
                                callback: CallBackFunction,
                            ) {
                                val text = data.getString("title")
                                if (!text.isNullOrEmpty()) {
                                    jsbTitle = text
                                    titleVisible = true
                                } else {
                                    jsbTitle = null
                                    titleVisible = false
                                }
                            }
                        })

                        addHandlerLocal(HandlerName.IMMERSIVE, object : BaseBridgeHandler() {

                            override fun handlerV2(
                                context: Context,
                                data: JsonObject,
                                callback: CallBackFunction,
                            ) {
                                data.getBoolean("statusBar")?.also { hide ->
                                    applyStatusBarPadding = !hide
                                }
                                data.getBoolean("navigationBar")?.also { hide ->
                                    applyNavigationBarPadding = !hide
                                }
                            }
                        })

                        addHandlerLocal(HandlerName.STATUS_BAR, object : BaseBridgeHandler() {

                            override fun handlerV2(
                                context: Context,
                                data: JsonObject,
                                callback: CallBackFunction,
                            ) {
                                with(density) {
                                    callback.sendSuccess(
                                        context,
                                        statusBars.getTop(density).toDp().value.toInt()
                                    )
                                }
                            }
                        })

                        addHandlerLocal(HandlerName.NAVIGATION_BAR, object : BaseBridgeHandler() {

                            override fun handlerV2(
                                context: Context,
                                data: JsonObject,
                                callback: CallBackFunction,
                            ) {
                                with(density) {
                                    callback.sendSuccess(
                                        context,
                                        navigationBars.getBottom(density).toDp().value.toInt()
                                    )
                                }
                            }
                        })

                        addHandlerLocal(
                            HandlerName.REQUEST_ANDROID_PERMISSION,
                            object : BaseBridgeHandler() { // 权限请求

                                override fun handlerV2(
                                    context: Context,
                                    data: JsonObject,
                                    callback: CallBackFunction,
                                ) {
                                    val permissions =
                                        data.getString("scope")?.split(",")?.toTypedArray()
                                    if (permissions.isNullOrEmpty()) {
                                        callback.sendSuccess(context, 0)
                                        return
                                    }
                                    permissionCallback = this to callback
                                    launcher.launch(permissions)
                                }
                            })

                        addHandlerLocal(
                            HandlerName.REQUEST_PERMISSION,
                            object : BaseBridgeHandler() { // 权限请求

                                override fun handlerV2(
                                    context: Context,
                                    data: JsonObject,
                                    callback: CallBackFunction,
                                ) {
                                    val permissions =
                                        data.getString("scope")?.split(",")?.flatMap { name ->
                                            when (name) {
                                                "CAMERA" -> listOf(Manifest.permission.CAMERA)
                                                "ALBUM" -> {
                                                    if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.TIRAMISU) {
                                                        listOf(
                                                            Manifest.permission.READ_EXTERNAL_STORAGE,
                                                            Manifest.permission.WRITE_EXTERNAL_STORAGE
                                                        )
                                                    } else {
                                                        listOf(
                                                            Manifest.permission.READ_MEDIA_IMAGES,
                                                            Manifest.permission.READ_MEDIA_VIDEO
                                                        )
                                                    }
                                                }

                                                else -> emptyList()
                                            }
                                        }?.toTypedArray()
                                    if (permissions.isNullOrEmpty()) {
                                        callback.sendSuccess(context, 0)
                                        return
                                    }
                                    permissionCallback = this to callback
                                    launcher.launch(permissions)
                                }
                            })

                        addHandlerLocal(HandlerName.UCOO_PAY, object : BaseBridgeHandler() { // 付款

                            override fun handlerV2(
                                context: Context,
                                data: JsonObject,
                                callback: CallBackFunction,
                            ) {
                                val activity = context.asActivity ?: return
                                val productId =
                                    data.getString("product_id")?.takeIf { it.isNotEmpty() }
                                        ?: kotlin.run {
                                            callback.sendFailure(context, -1)
                                            return
                                        }
                                val fkLink = data.getString("fk_link").orEmpty()
                                val fkType = data.getInt("fk_type") ?: kotlin.run {
                                    callback.sendFailure(context, -2)
                                    return
                                }
                                val orderType = data.getInt("order_type") ?: kotlin.run {
                                    callback.sendFailure(context, -3)
                                    return
                                }
                                viewModel.sendEvent(
                                    WalletContract.Event.Buy(
                                        activity,
                                        fkType,
                                        productId,
                                        fkLink,
                                        orderType
                                    )
                                )
                                callback.sendSuccess(context, 0)
                            }
                        })

                        addHandlerLocal(HandlerName.WEB_VIDEO_EFFECT, object : BaseBridgeHandler() {

                            override fun handlerV2(
                                context: Context,
                                data: JsonObject,
                                callback: CallBackFunction,
                            ) {
                                val effectUrl =
                                    data.getString("effect_url")?.takeIf { it.isNotEmpty() }
                                        ?: return
                                scope.launch {
                                    val file = GiftEffectHelper.getGiftEffectFile(
                                        effectUrl,
                                        null,
                                        5.seconds
                                    )
                                    if (file != null) {
                                        effectFileState.value = WebEffect(file)
                                    }
                                }
                            }
                        })

                        val listener = object : IMessageChannel.Listener {
                            override fun onReceivedMessage(message: String) {
                                scope.launch {
                                    webView.callHandler("receiveRacingMessage", message, null)
                                }
                            }
                        }

                        addHandlerLocal(HandlerName.RC_CHANNEL, object : BaseBridgeHandler() {

                            override fun handlerV2(
                                context: Context,
                                data: JsonObject,
                                callback: CallBackFunction,
                            ) {
                                if (!IMCompatCore.imChannel.isMainRC) return
                                val channelName =
                                    data.getString("channel_name")?.takeIf { it.isNotEmpty() }
                                        ?: return
                                if (channelName == mChannel?.channelName) {
                                    callback.sendSuccess(context, 0)
                                    return
                                }
                                scope.launch {
                                    mChannel?.quit()
                                    mChannel = null
                                    mChannel = RtmManager.joinMessageChannel(channelName).also {
                                        it.setListener(listener)
                                    }
                                    if (mChannel != null) {
                                        callback.sendSuccess(context, 0)
                                    } else {
                                        callback.sendFailure(context)
                                    }
                                }
                            }
                        })

                        addHandlerLocal(
                            HandlerName.UCOO_TENCENT_CHANNEL,
                            object : BaseBridgeHandler() {

                                override fun handlerV2(
                                    context: Context,
                                    data: JsonObject,
                                    callback: CallBackFunction,
                                ) {
                                    if (!IMCompatCore.imChannel.isMainTIM) return
                                    val channelName =
                                        data.getString("channel_name")?.takeIf { it.isNotEmpty() }
                                            ?: return
                                    if (channelName == timChannel?.channelName) {
                                        callback.sendSuccess(context, 0)
                                        return
                                    }
                                    scope.launch {
                                        timChannel?.quit()
                                        timChannel = null
                                        timChannel =
                                            RtmManager.joinMessageChannel(channelName, false).also {
                                                it.setListener(listener)
                                            }
                                        if (timChannel != null) {
                                            callback.sendSuccess(context, 0)
                                        } else {
                                            callback.sendFailure(context)
                                        }
                                    }
                                }
                            })

                        addHandlerLocal(HandlerName.UCOO_H5_JUMP_APP, handlerJumpApp)

                        addHandlerLocal(JsbEvents.CUPID_WARM_TIP, object : BaseBridgeHandler() {
                            override fun handlerV2(
                                context: Context,
                                data: JsonObject,
                                callback: CallBackFunction,
                            ) {
                                data.getString("jump_link")?.takeIsNotEmpty()?.also {
                                    AppLinkManager.controller?.navigateByLink(it)
                                    accountManager.refreshSelfUserByRemote()
                                } ?: data.getString("content")?.also {
                                    dialogQueue.pushCenterDialog { dialog, actions ->
                                        TitleAlertDialog(
                                            modifier = Modifier.width(270.dp),
                                            title = stringResource(id = R.string.cpd_warn_tips),
                                            content = it,
                                            endButton = DialogButton(
                                                stringResource(id = R.string.cpd_iknow)
                                            ) { dialog.dismiss() }
                                        )
                                    }
                                    accountManager.refreshSelfUserByRemote()
                                }
                            }
                        })
                    }
                },
                onDispose = {
                    appCoroutineScope.launch {
                        webHandler = null
                        if (IMCompatCore.imChannel.isMainRC) {
                            mChannel?.quit()
                            mChannel = null
                        } else {
                            timChannel?.quit()
                            timChannel=null
                        }
                    }
                },
                onClient = {
                    it as AppBridgeWebView
                    val cacheEnable = url.getValueByUrl("cacheEnable")?.toBoolean()
                        ?: WebBridgeHandler.globalCacheEnable
                    object : BridgeWebViewClient2(it, it.bridgeTiny) {

                        override fun shouldInterceptRequest(
                            view: WebView?,
                            url: String?,
                        ): WebResourceResponse? {
                            val resp = OfflinePkgManager.interceptRequest(
                                view,
                                url?.let { Uri.parse(url) })
                            if (resp != null) {
                                return resp
                            }

                            return if (cacheEnable) {
                                WebViewCacheInterceptorInst.getInstance().interceptRequest(url)
                            } else {
                                super.shouldInterceptRequest(view, url)
                            }
                        }

                        override fun shouldInterceptRequest(
                            view: WebView?,
                            request: WebResourceRequest?,
                        ): WebResourceResponse? {
                            val resp = OfflinePkgManager.interceptRequest(view, request?.url)
                            if (resp != null) {
                                return resp
                            }
                            return if (cacheEnable) {
                                WebViewCacheInterceptorInst.getInstance().interceptRequest(request)
                            } else {
                                super.shouldInterceptRequest(view, request)
                            }
                        }


                        override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
                            val localUrl = OfflinePkgManager.getLocalUrl(url)
                            if (localUrl != null) {
                                view.loadUrl(localUrl)
                                return true
                            } else {
                                return super.shouldOverrideUrlLoading(view, url)
                            }
                        }
                    }
                },
                onChromeClient = {
                    it as AppBridgeWebView
                    BridgeWebViewChromeClient2(it, it.bridgeTiny)
                }
            ) {
                AppBridgeWebView(it)
            }

            var showProgressIndicator by remember {
                mutableStateOf(true)
            }

            LaunchedEffect(key1 = state) {
                snapshotFlow {
                    state.loadingState
                }.filter {
                    it is LoadingState.Loading
                }.collectLatest {
                    showProgressIndicator = true
                }
            }

            LaunchedEffect(navigator) {
                val bundle = state.viewState
                if (bundle == null) {
                    // This is the first time load, so load the home page.
                    val lowercase = url.lowercase()
                    if (lowercase.endsWith(".jpg") || lowercase.endsWith(".jpeg") || lowercase.endsWith(
                            ".png"
                        ) || lowercase.endsWith(".webp")
                    ) {
                        navigator.loadImage(getWrapImageUrl(url))
                    } else {
                        navigator.loadUrl(url)
                    }
                }
            }

            val loadingState = state.loadingState

            val progress by animateFloatAsState(
                targetValue = when (loadingState) {
                    is LoadingState.Loading -> {
                        loadingState.progress
                    }

                    is LoadingState.Initializing -> {
                        0f
                    }

                    else -> {
                        1f
                    }
                },
                animationSpec = if (state.loadingState is LoadingState.Finished) {
                    spring(visibilityThreshold = 0.01f, stiffness = 10f)
                } else {
                    spring(visibilityThreshold = 0.01f)
                },
            ) {
                if (it == 1f) {
                    showProgressIndicator = false
                }
            }

            if (showProgressIndicator) {
                LinearProgressIndicator(
                    progress = { progress },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(2.dp),
                    color = Color(0xFFFF5E8B),
                    trackColor = Color.Transparent,
                )
            }

            val playingEffect = effectFileState.value
            if (playingEffect != null) {

                AndroidView(
                    factory = {
                        EvaAnimViewV3(it).apply {
                            setVideoMode(EvaConstant.VIDEO_MODE_SPLIT_HORIZONTAL)
                        }
                    },
                    modifier = Modifier.fillMaxSize(),
                    onReset = {
                        it.stopPlay()
                        it.setAnimListener(null)
                    },
                    onRelease = {
                        it.stopPlay()
                        it.setAnimListener(null)
                    }
                ) {
                    if (playingEffect.isPlayed) {
                        return@AndroidView
                    }
                    playingEffect.isPlayed = true

                    it.setAnimListener(null)
                    it.stopPlay()
                    it.setAnimListener(object : IEvaAnimListener {
                        override fun onFailed(errorType: Int, errorMsg: String?) {
                            effectFileState.value = null
                        }

                        override fun onVideoComplete(lastFrame: Boolean) {
                            effectFileState.value = null
                        }

                        override fun onVideoDestroy() = Unit

                        override fun onVideoRender(frameIndex: Int, config: EvaAnimConfig?) = Unit

                        override fun onVideoRestart() = Unit

                        override fun onVideoStart(isRestart: Boolean) = Unit
                    })
                    it.startPlay(playingEffect.file)
                }
            }

            dialogQueue.DialogContent()
        }
    }
}

private fun getWrapImageUrl(url: String): String {
    val head = """
                        <head>
                        <style>
                        *{
                        margin:0px;
                        }
                        </style>
                        <meta name="viewport" content="width=device-width,initial-scale=1" />
                        </head>
                    """.trimIndent()
    val imageUrl =
        "<html>$head<body><img src='$url' style='width:100%;max-width:100%;overflow:hidden;object-fit:contain;'/></body></html>"
    return imageUrl
}

open class BridgeWebViewClient2 : AccompanistWebViewClient {
    private var bridgeTiny: BridgeTiny? = null
    private var bridgeWebView: IWebView? = null

    constructor(bridgeWebView: IWebView?, bridgeTiny: BridgeTiny?) {
        this.bridgeTiny = bridgeTiny
        this.bridgeWebView = bridgeWebView
    }

    fun attach(bridgeWebView: IWebView?, bridgeTiny: BridgeTiny?) {
        this.bridgeTiny = bridgeTiny
        this.bridgeWebView = bridgeWebView
    }

    override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
        if (url.startsWith("intent://")) {
            try {
                val context = view.context
                val intent = Intent.parseUri(url, Intent.URI_INTENT_SCHEME)
                val existPackage: Intent? =
                    context.packageManager.getLaunchIntentForPackage(intent.getPackage().orEmpty())
                if (existPackage != null) {
                    context.startActivity(intent)
                } else {
                    // 如果应用未安装，尝试跳转到 Google Play 商店
                    val marketIntent = Intent(Intent.ACTION_VIEW)
                    marketIntent.setData(Uri.parse("market://details?id=" + intent.getPackage()))
                    context.startActivity(marketIntent)
                }
                return true
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        view.loadUrl(url)
        return super.shouldOverrideUrlLoading(view, url)
    }

    override fun shouldOverrideUrlLoading(view: WebView, request: WebResourceRequest): Boolean {
        val url = request.url
        val scheme = url.scheme.orEmpty()
        when (scheme) {
            "ucoo" -> {
                AppLinkManager.controller?.navigateByLink(url.toString())
            }

            else -> {
                view.loadUrl(request.url.authority!!)
            }
        }
        return super.shouldOverrideUrlLoading(view, request)
    }

    override fun onPageFinished(view: WebView, url: String?) {
        super.onPageFinished(view, url)
        bridgeTiny?.webViewLoadJs(bridgeWebView)
    }
}

open class BridgeWebViewChromeClient2 : AccompanistWebChromeClient {

    private val TAG = "BridgeWebViewChromeClient"

    private var bridgeTiny: BridgeTiny? = null
    private var bridgeWebView: IWebView? = null

    constructor(bridgeWebView: IWebView?, bridgeTiny: BridgeTiny?) {
        this.bridgeTiny = bridgeTiny
        this.bridgeWebView = bridgeWebView
    }

    fun attach(bridgeWebView: IWebView?, bridgeTiny: BridgeTiny?) {
        this.bridgeTiny = bridgeTiny
        this.bridgeWebView = bridgeWebView
    }

    override fun onJsPrompt(
        view: WebView,
        url: String,
        message: String,
        defaultValue: String,
        result: JsPromptResult,
    ): Boolean {
        if (Bridge.INSTANCE.debug) {
            BridgeLog.d(TAG, "message->$message")
        }
        bridgeTiny?.onJsPrompt(bridgeWebView, message)
        result.confirm("do")
        return true
    }

    override fun onPermissionRequest(request: PermissionRequest) {
        if (request.origin != null && WBH5FaceVerifySDK.getInstance()
                .isTencentH5FaceVerify(request.origin.toString())
        ) {
            //判断是否为腾讯H5刷脸验证
        } else {
            postToMainThread(0) { request.grant(request.resources) }
        }
    }

    private fun checkPermission(activity: Activity): Boolean {
        if (ContextCompat.checkSelfPermission(
                activity,
                Manifest.permission.CAMERA
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            // 进入这儿表示没有权限
            if (ActivityCompat.shouldShowRequestPermissionRationale(
                    activity,
                    Manifest.permission.CAMERA
                )
            ) {
                // 提示已经禁止
                return false
            } else {
                ActivityCompat.requestPermissions(
                    activity,
                    arrayOf(Manifest.permission.CAMERA),
                    100
                )
                return false
            }
        } else {
            return true
        }
    }

    override fun onShowFileChooser(
        webView: WebView,
        filePathCallback: ValueCallback<Array<Uri>>,
        fileChooserParams: FileChooserParams,
    ): Boolean {
        if (WBH5FaceVerifySDK.getInstance()
                .isTencentH5FaceVerify(webView, fileChooserParams, null)
        ) {
            val activity = webView.context as Activity
            if (checkPermission(activity)) {
                return WBH5FaceVerifySDK.getInstance()
                    .recordVideoForApi21(webView, filePathCallback, activity, fileChooserParams)
            }
        }
        return true
    }
}

class WebDialog(
    val url: String,
    private val alignment: Alignment = Alignment.Center,
    private val cancelable: Boolean = true,
    private val modifier: Modifier = Modifier,
) : NormalDialog<IDialogAction>() {

    override val properties: DialogProperties =
        DialogProperties(dismissOnBackPress = cancelable, dismissOnClickOutside = cancelable)

    override val isFull: Boolean = true

    private val urlWithLan =
        url.putValueByUrl("app-language", MultiLanguages.getAppLanguage().toLanguageTag())

    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        WebBridgeHandler.init()
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black.copy(0.5f))
                .noEffectClickable {
                    if (cancelable) dialog.dismiss()
                },
            contentAlignment = alignment
        ) {
            WebComposeView(urlWithLan, modifier = modifier.noEffectClickable { }) {
                dialog.dismiss()
            }
        }
    }

}
