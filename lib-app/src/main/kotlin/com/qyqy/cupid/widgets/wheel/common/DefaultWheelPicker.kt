package com.qyqy.cupid.widgets.wheel.common

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.State
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.qyqy.cupid.widgets.wheel.common.core.WheelData
import com.qyqy.cupid.widgets.wheel.common.core.WheelTextPicker

@Immutable
internal class DefaultSelectorProperties(
    private val enabled: Boolean,
    private val borderColor: Color,
) : SelectorProperties {

    @Composable
    override fun enabled(): State<Boolean> {
        return rememberUpdatedState(enabled)
    }

    @Composable
    override fun borderColor(): State<Color> {
        return rememberUpdatedState(borderColor)
    }
}

object WheelPickerDefaults {
    @Composable
    fun selectorProperties(
        enabled: Boolean = true,
        borderColor: Color = Color(0xFF007AFF).copy(0.7f),
    ): SelectorProperties = DefaultSelectorProperties(
        enabled = enabled,
        borderColor = borderColor,
    )
}

interface SelectorProperties {
    @Composable
    fun enabled(): State<Boolean>

    @Composable
    fun borderColor(): State<Color>
}

private fun getData(data: List<WheelData>, rowIdx: Int, selectedIdxs: List<Int>): List<String>? {
    var returnData: List<WheelData>? = data
    var lastRowIdx = 0
    while (rowIdx > lastRowIdx) {
        if (returnData == null) {
            break
        }
        // fixme 区域选择第一级最多47个数据, 现在出现的问题selectedIdxs的第0位有几率为47, 用户是怎么超出最大选择范围的?
        val nextIdx = selectedIdxs[lastRowIdx]
        if (returnData.size > nextIdx) {
            returnData = returnData[nextIdx].children()
        } else {
            // 超出最大范围, 置为空, 下一级先不显示, 等待下次在选中
            returnData = null
        }
        lastRowIdx += 1
    }

    return returnData?.map { it.uniqueId }
}

@Composable
fun DefaultWheelPicker(
    data: List<WheelData>,
    wheelIndexs: List<Int>,
    modifier: Modifier = Modifier,
    height: Dp = 128.dp,
    startIndexs: Array<Int> = Array<Int>(wheelIndexs.size) { 0 },
    rowCount: Int = 3,
    textStyle: TextStyle = MaterialTheme.typography.titleMedium,
    textColor: Color = LocalContentColor.current,
    selectorProperties: SelectorProperties = WheelPickerDefaults.selectorProperties(),
    onSnapped: (rowIdx: Int, selectedIdx: Int) -> Unit
) {
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        if (selectorProperties.enabled().value) {
            HorizontalDivider(
                modifier = Modifier.padding(bottom = (height / rowCount)),
                thickness = (0.5).dp,
                color = selectorProperties.borderColor().value
            )
            HorizontalDivider(
                modifier = Modifier.padding(top = (height / rowCount)),
                thickness = (0.5).dp,
                color = selectorProperties.borderColor().value
            )
        }
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp, Alignment.CenterHorizontally)
        ) {
            for (columnIdx in wheelIndexs.indices) {
                val wheelKey = if (columnIdx > 0) wheelIndexs[columnIdx - 1] else Unit
                val list = remember(wheelKey, data, wheelIndexs) {
                    // 获取当前row的list, 每次都会生成一个新的list
                    getData(data, columnIdx, wheelIndexs)
                }
                if (list != null) {
                    WheelTextPicker(
                        modifier = Modifier.width(114.dp),
                        startIndex = startIndexs[columnIdx],
                        height = height,
                        texts = list,
                        rowCount = rowCount,
                        style = textStyle,
                        color = textColor,
                    ) { snappedIndex ->
                        onSnapped(columnIdx, snappedIndex)
                        return@WheelTextPicker snappedIndex
                    }
                }
            }
        }
    }
}
