package com.qyqy.cupid.widgets.wheel.common


import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.qyqy.cupid.widgets.wheel.common.core.WheelData

fun Modifier.noRippleEffect(
    onClick: () -> Unit
) = composed {
    this.clickable(
        interactionSource = remember { MutableInteractionSource() },
        indication = null,
        onClick = onClick
    )
}

object WheelPickerComponent {
    /***
     * modifier: Modifies the layout of the date picker.
     * title: Title displayed above the date picker.
     * doneLabel: Label for the "Done" button.
     * titleStyle: Style for the title text.
     * doneLabelStyle: Style for the "Done" label text.
     * startDate: Initial date selected in the picker.
     * minDate: Minimum selectable date.
     * maxDate: Maximum selectable date.
     * yearsRange: Initial years range.
     * height: height of the date picker component.
     * rowCount: Number of rows displayed in the picker and it's depending on height also.
     * showShortMonths: show short month name.
     * dateTextStyle: Text style for the date display.
     * dateTextColor: Text color for the date display.
     * hideHeader: Hide header of picker.
     * selectorProperties: Properties defining the interaction with the date picker.
     * onDoneClick: Callback triggered when the "Done" button is clicked, passing the selected date.
     * onDateChangeListener: Callback triggered when the Date is changed, passing the selected date.
     ***/

    @Composable
    fun WheelDataPicker(
        data: List<WheelData>,
        columnCount: Int,
        modifier: Modifier = Modifier,
        rowCount: Int = 3,
        title: String = "Due Date",
        doneLabel: String = "Done",
        startIndexs: Array<Int> = Array<Int>(columnCount) { 0 },
        titleStyle: TextStyle = LocalTextStyle.current,
        doneLabelStyle: TextStyle = LocalTextStyle.current,
        height: Dp = 128.dp,
        dateTextStyle: TextStyle = MaterialTheme.typography.titleMedium,
        dateTextColor: Color = LocalContentColor.current,
        hideHeader: Boolean = false,
        hideHeaderDivider: Boolean = false,
        selectorProperties: SelectorProperties = WheelPickerDefaults.selectorProperties(),
        onTitleClick: (List<Int>) -> Unit = {},
        onDoneClick: (List<Int>) -> Unit = {},
        onDateChangeListener: (List<Int>) -> Unit = {},
    ) {
        val selectedData = remember {
            mutableStateOf(List(columnCount) { 0 })
        }
        LaunchedEffect(key1 = selectedData) {
            if (hideHeader) {
                onDateChangeListener(selectedData.value)
            }
        }

        Column(modifier = modifier) {
            if (!hideHeader) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = title,
                        style = titleStyle,
                        modifier = Modifier.noRippleEffect {
                            onTitleClick(selectedData.value)
                        },
                    )

                    Spacer(modifier = Modifier.weight(1f))

                    Text(
                        text = doneLabel,
                        style = doneLabelStyle,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.noRippleEffect {
                            onDoneClick(selectedData.value)
                        }
                    )
                }

                if (!hideHeaderDivider) {
                    HorizontalDivider(
                        thickness = (0.5).dp,
                        color = Color.LightGray
                    )
                }
            }

            DefaultWheelPicker(
                data,
                selectedData.value,
                textColor = dateTextColor,
                selectorProperties = selectorProperties,
                rowCount = rowCount,
                height = height,
                startIndexs = startIndexs,
                modifier = Modifier.padding(top = 14.dp, bottom = 14.dp),
                textStyle = dateTextStyle,
                onSnapped = { rowIdx, seletedIdx ->
                    val newList = selectedData.value.toMutableList()
                    newList.forEachIndexed { index, i ->
                        if (index == rowIdx) {
                            newList[index] = seletedIdx
                        } else if (index > rowIdx) {
                            newList[index] = 0
                        }
                    }
                    selectedData.value = newList
                }
            )
        }
    }

}