package com.qyqy.cupid.widgets.wheel.common


import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties

@Composable
fun WheelPickerDialog(
    state: WheelState,
    columnCount: Int,
    rowCount: Int = 3,
    modifier: Modifier = Modifier,
    title: String = "Due Date",
    doneLabel: String = "Done",
    titleStyle: TextStyle = LocalTextStyle.current,
    doneLabelStyle: TextStyle = LocalTextStyle.current,
    startIndexs: Array<Int> = Array<Int>(columnCount){0},
    height: Dp,
    dateTextStyle: TextStyle = MaterialTheme.typography.titleMedium,
    dateTextColor: Color = LocalContentColor.current,
    hideHeader: Boolean = false,
    hideHeaderDivider: Boolean = false,
    containerColor: Color = Color.White,
    shape: Shape = RoundedCornerShape(10.dp),
    selectorProperties: SelectorProperties = WheelPickerDefaults.selectorProperties(),
    onTitleClick: (List<Int>) -> Unit = {},
    onDoneClick: (List<Int>) -> Unit = {},
    onDateChangeListener: (List<Int>) -> Unit = {},
    onDismiss: () -> Unit = {},
) {
    remember(onDismiss) {
        state.onDismiss = {
            onDismiss()
        }
        state
    }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            usePlatformDefaultWidth = false
        )
    ) {
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .fillMaxSize()
                .noRippleEffect(onClick = onDismiss)
        ) {
            Surface(
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .wrapContentSize()
                    .animateContentSize(),
                shape = shape,
                color = containerColor,
            ) {
                WheelPickerComponent.WheelDataPicker(
                    state.data,
                    columnCount = columnCount,
                    modifier = modifier,
                    title = title,
                    doneLabel = doneLabel,
                    titleStyle = titleStyle,
                    doneLabelStyle = doneLabelStyle,
                    height = height,
                    rowCount = rowCount,
                    startIndexs = startIndexs,
                    dateTextStyle = dateTextStyle,
                    dateTextColor = dateTextColor,
                    hideHeader = hideHeader,
                    hideHeaderDivider = hideHeaderDivider,
                    selectorProperties = selectorProperties,
                    onTitleClick = onTitleClick,
                    onDoneClick = onDoneClick,
                    onDateChangeListener = onDateChangeListener,
                )
            }
        }
    }
}