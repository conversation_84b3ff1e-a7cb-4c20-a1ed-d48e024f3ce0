package com.qyqy.cupid.widgets.wheel.common


import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.BottomSheetDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.qyqy.cupid.widgets.wheel.common.core.WheelData

@Stable
class WheelState(var onDismiss: () -> Unit = {}, val data: List<WheelData>) {
    fun hide() {
        onDismiss()
    }
}


/***
 * modifier: Modifies the layout of the date picker.
 * showDatePicker: Show and hide date picker.
 * title: Title displayed above the date picker.
 * doneLabel: Label for the "Done" button.
 * titleStyle: Style for the title text.
 * doneLabelStyle: Style for the "Done" label text.
 * startDate: Initial date selected in the picker.
 * minDate: Minimum selectable date.
 * maxDate: Maximum selectable date.
 * yearsRange: Initial years range.
 * height: height of the date picker component.
 * rowCount: Number of rows displayed in the picker and it's depending on height also.
 * showShortMonths: show short month name.
 * dateTextStyle: Text style for the date display.
 * dateTextColor: Text color for the date display.
 * hideHeader: Hide header of picker.
 * containerColor: The color used for the background of date picker.
 * shape: The shape of the date picker.
 * dateTimePickerView: For bottomsheet and diloag view.
 * dragHandle - Optional visual marker to swipe the bottom sheet.
 * selectorProperties: Properties defining the interaction with the date picker.
 * onDoneClick: Callback triggered when the "Done" button is clicked, passing the selected date.
 * onDateChangeListener: Callback triggered when the Date is changed, passing the selected date.
 * onDismiss: Callback triggered when the date picker is dismissed.
 ***/
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WheelPickerView(
    state: WheelState,
    columnCount: Int,
    rowCount: Int = 3,
    modifier: Modifier = Modifier,
    title: String = "Due Date",
    doneLabel: String = "Done",
    titleStyle: TextStyle = LocalTextStyle.current,
    doneLabelStyle: TextStyle = LocalTextStyle.current,
    height: Dp,
    viewType: Int = 0,
    startIndexs: Array<Int> = Array(columnCount) { 0 },
    dateTextStyle: TextStyle = MaterialTheme.typography.titleMedium,
    dateTextColor: Color = LocalContentColor.current,
    hideHeader: Boolean = false,
    hideHeaderDivider: Boolean = false,
    containerColor: Color = Color.White,
    shape: Shape = RoundedCornerShape(10.dp),
    selectorProperties: SelectorProperties = WheelPickerDefaults.selectorProperties(),
    dragHandle: @Composable (() -> Unit)? = { BottomSheetDefaults.DragHandle() },
    onTitleClick: (List<Int>) -> Unit = {},
    onDoneClick: (List<Int>) -> Unit = {},
    onDateChangeListener: (List<Int>) -> Unit = {},
    onDismiss: () -> Unit = {},
) {
    if (viewType == 0) {
        WheelPickerBottomSheet(
            state = state,
            columnCount = columnCount,
            modifier = modifier,
            title = title,
            doneLabel = doneLabel,
            titleStyle = titleStyle,
            doneLabelStyle = doneLabelStyle,
            height = height,
            rowCount = rowCount,
            dateTextStyle = dateTextStyle,
            dateTextColor = dateTextColor,
            startIndexs = startIndexs,
            hideHeader = hideHeader,
            hideHeaderDivider = hideHeaderDivider,
            containerColor = containerColor,
            shape = shape,
            selectorProperties = selectorProperties,
            dragHandle = dragHandle,
            onTitleClick = onTitleClick,
            onDoneClick = onDoneClick,
            onDateChangeListener = onDateChangeListener,
            onDismiss = onDismiss
        )
    } else {
        WheelPickerDialog(
            state = state,
            columnCount = columnCount,
            modifier = modifier,
            title = title,
            doneLabel = doneLabel,
            titleStyle = titleStyle,
            doneLabelStyle = doneLabelStyle,
            height = height,
            rowCount = rowCount,
            startIndexs = startIndexs,
            dateTextStyle = dateTextStyle,
            dateTextColor = dateTextColor,
            hideHeader = hideHeader,
            hideHeaderDivider = hideHeaderDivider,
            containerColor = containerColor,
            shape = shape,
            selectorProperties = selectorProperties,
            onTitleClick = onTitleClick,
            onDoneClick = onDoneClick,
            onDateChangeListener = onDateChangeListener,
            onDismiss = onDismiss
        )
    }
}
