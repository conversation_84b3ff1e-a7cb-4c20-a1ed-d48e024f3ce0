package com.qyqy.cupid.widgets.wheel.datetime


import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.material3.BottomSheetDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import kotlinx.coroutines.launch
import kotlinx.datetime.LocalDate


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WheelDatePickerBottomSheet(
    state: WheelDateState,
    modifier: Modifier = Modifier,
    title: String = "Due Date",
    doneLabel: String = "Done",
    titleStyle: TextStyle = LocalTextStyle.current,
    doneLabelStyle: TextStyle = LocalTextStyle.current,
    startDate: LocalDate = LocalDate.now(),
    minDate: LocalDate = LocalDate.MIN(),
    maxDate: LocalDate = LocalDate.MAX(),
    yearsRange: IntRange? = IntRange(1922, 2122),
    height: Dp,
    rowCount: Int = 3,
    formatter: DateTimeUnitFormatter = DateTimeUnitFormatterDefaults,
    dateTextStyle: TextStyle = MaterialTheme.typography.titleMedium,
    dateTextColor: Color = LocalContentColor.current,
    hideHeader: Boolean = false,
    hideHeaderDivider: Boolean = false,
    containerColor: Color = Color.White,
    shape: Shape = BottomSheetDefaults.ExpandedShape,
    dragHandle: @Composable (() -> Unit)? = { BottomSheetDefaults.DragHandle() },
    selectorProperties: SelectorProperties = WheelPickerDefaults.selectorProperties(),
    onTitleClick: (snappedDate: LocalDate) -> Unit = {},
    onDoneClick: (snappedDate: LocalDate) -> Unit = {},
    onDateChangeListener: (snappedDate: LocalDate) -> Unit = {},
    onDismiss: () -> Unit = {},
) {
    val modalBottomSheetState = rememberModalBottomSheetState()

    val scope = rememberCoroutineScope()

    remember(modalBottomSheetState, onDismiss) {
        state.onDismiss = {
            scope.launch {
                modalBottomSheetState.hide()
                onDismiss()
            }
        }
        state
    }

    ModalBottomSheet(
        onDismissRequest = onDismiss,
        sheetState = modalBottomSheetState,
        containerColor = containerColor,
        dragHandle = dragHandle,
        windowInsets = WindowInsets.navigationBars,
        shape = shape
    ) {
        WheelDatePickerComponent.WheelDatePicker(
            modifier = modifier,
            title = title,
            doneLabel = doneLabel,
            titleStyle = titleStyle,
            doneLabelStyle = doneLabelStyle,
            startDate = startDate,
            minDate = minDate,
            maxDate = maxDate,
            yearsRange = yearsRange,
            height = height,
            rowCount = rowCount,
            formatter = formatter,
            dateTextStyle = dateTextStyle,
            dateTextColor = dateTextColor,
            hideHeader = hideHeader,
            hideHeaderDivider = hideHeaderDivider,
            selectorProperties = selectorProperties,
            onTitleClick = onTitleClick,
            onDoneClick = onDoneClick,
            onDateChangeListener = onDateChangeListener,
        )
    }
}