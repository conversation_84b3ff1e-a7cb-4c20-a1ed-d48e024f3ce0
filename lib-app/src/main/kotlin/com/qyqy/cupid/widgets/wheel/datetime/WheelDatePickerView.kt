package com.qyqy.cupid.widgets.wheel.datetime


import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.BottomSheetDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import kotlinx.datetime.LocalDate

@Stable
class WheelDateState(var onDismiss: () -> Unit = {}) {

    fun hide() {
        onDismiss()
    }
}


/***
 * modifier: Modifies the layout of the date picker.
 * showDatePicker: Show and hide date picker.
 * title: Title displayed above the date picker.
 * doneLabel: Label for the "Done" button.
 * titleStyle: Style for the title text.
 * doneLabelStyle: Style for the "Done" label text.
 * startDate: Initial date selected in the picker.
 * minDate: Minimum selectable date.
 * maxDate: Maximum selectable date.
 * yearsRange: Initial years range.
 * height: height of the date picker component.
 * rowCount: Number of rows displayed in the picker and it's depending on height also.
 * showShortMonths: show short month name.
 * dateTextStyle: Text style for the date display.
 * dateTextColor: Text color for the date display.
 * hideHeader: Hide header of picker.
 * containerColor: The color used for the background of date picker.
 * shape: The shape of the date picker.
 * dateTimePickerView: For bottomsheet and diloag view.
 * dragHandle - Optional visual marker to swipe the bottom sheet.
 * selectorProperties: Properties defining the interaction with the date picker.
 * onDoneClick: Callback triggered when the "Done" button is clicked, passing the selected date.
 * onDateChangeListener: Callback triggered when the Date is changed, passing the selected date.
 * onDismiss: Callback triggered when the date picker is dismissed.
 ***/
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WheelDatePickerView(
    state: WheelDateState,
    modifier: Modifier = Modifier,
    title: String = "Due Date",
    doneLabel: String = "Done",
    titleStyle: TextStyle = LocalTextStyle.current,
    doneLabelStyle: TextStyle = LocalTextStyle.current,
    startDate: LocalDate = LocalDate.now(),
    minDate: LocalDate = LocalDate.MIN(),
    maxDate: LocalDate = LocalDate.MAX(),
    yearsRange: IntRange? = IntRange(1922, 2122),
    height: Dp,
    rowCount: Int = 3,
    formatter: DateTimeUnitFormatter = DateTimeUnitFormatterDefaults,
    dateTextStyle: TextStyle = MaterialTheme.typography.titleMedium,
    dateTextColor: Color = LocalContentColor.current,
    hideHeader: Boolean = false,
    hideHeaderDivider: Boolean = false,
    containerColor: Color = Color.White,
    shape: Shape = RoundedCornerShape(10.dp),
    dateTimePickerView: DateTimePickerView = DateTimePickerView.BOTTOM_SHEET_VIEW,
    selectorProperties: SelectorProperties = WheelPickerDefaults.selectorProperties(),
    dragHandle: @Composable (() -> Unit)? = { BottomSheetDefaults.DragHandle() },
    onTitleClick: (snappedDate: LocalDate) -> Unit = {},
    onDoneClick: (snappedDate: LocalDate) -> Unit = {},
    onDateChangeListener: (snappedDate: LocalDate) -> Unit = {},
    onDismiss: () -> Unit = {},
) {
    if (dateTimePickerView == DateTimePickerView.BOTTOM_SHEET_VIEW) {
        WheelDatePickerBottomSheet(
            state = state,
            modifier = modifier,
            title = title,
            doneLabel = doneLabel,
            titleStyle = titleStyle,
            doneLabelStyle = doneLabelStyle,
            startDate = startDate,
            minDate = minDate,
            maxDate = maxDate,
            yearsRange = yearsRange,
            height = height,
            rowCount = rowCount,
            formatter = formatter,
            dateTextStyle = dateTextStyle,
            dateTextColor = dateTextColor,
            hideHeader = hideHeader,
            hideHeaderDivider = hideHeaderDivider,
            containerColor = containerColor,
            shape = shape,
            selectorProperties = selectorProperties,
            dragHandle = dragHandle,
            onTitleClick = onTitleClick,
            onDoneClick = onDoneClick,
            onDateChangeListener = onDateChangeListener,
            onDismiss = onDismiss
        )
    } else if (dateTimePickerView == DateTimePickerView.DIALOG_VIEW) {
        WheelDatePickerDialog(
            state = state,
            modifier = modifier,
            title = title,
            doneLabel = doneLabel,
            titleStyle = titleStyle,
            doneLabelStyle = doneLabelStyle,
            startDate = startDate,
            minDate = minDate,
            maxDate = maxDate,
            yearsRange = yearsRange,
            height = height,
            rowCount = rowCount,
            formatter = formatter,
            dateTextStyle = dateTextStyle,
            dateTextColor = dateTextColor,
            hideHeader = hideHeader,
            hideHeaderDivider = hideHeaderDivider,
            containerColor = containerColor,
            shape = shape,
            selectorProperties = selectorProperties,
            onTitleClick = onTitleClick,
            onDoneClick = onDoneClick,
            onDateChangeListener = onDateChangeListener,
            onDismiss = onDismiss
        )
    }else{
//        Box(
//            contentAlignment = Alignment.Center,
//            modifier = Modifier
//                .fillMaxWidth()
//                .height(height)
//                .noRippleEffect(onClick = onDismiss)
//        ) {
//            Surface(
//                modifier = Modifier
//                    .padding(horizontal = 16.dp)
//                    .wrapContentSize()
//                    .animateContentSize(),
//                shape = shape,
//                color = containerColor,
//            ) {
//                WheelDatePickerComponent.WheelDatePicker(
//                    modifier = modifier,
//                    title = title,
//                    doneLabel = doneLabel,
//                    titleStyle = titleStyle,
//                    doneLabelStyle = doneLabelStyle,
//                    startDate = startDate,
//                    minDate = minDate,
//                    maxDate = maxDate,
//                    yearsRange = yearsRange,
//                    height = height,
//                    rowCount = rowCount,
//                    formatter = formatter,
//                    dateTextStyle = dateTextStyle,
//                    dateTextColor = dateTextColor,
//                    hideHeader = hideHeader,
//                    hideHeaderDivider = hideHeaderDivider,
//                    selectorProperties = selectorProperties,
//                    onTitleClick = onTitleClick,
//                    onDoneClick = onDoneClick,
//                    onDateChangeListener = onDateChangeListener,
//                )
//            }
//        }
    }
}
