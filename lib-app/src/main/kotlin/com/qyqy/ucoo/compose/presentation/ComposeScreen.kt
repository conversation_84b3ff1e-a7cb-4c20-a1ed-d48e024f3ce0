package com.qyqy.ucoo.compose.presentation

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.compose.runtime.Composable
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.qyqy.cupid.ui.home.message.FullScreenNotificationAlert
import com.qyqy.cupid.ui.home.mine.BlackListNavigator
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.orDefault
import com.qyqy.ucoo.compose.pages.CPHouseNavigator
import com.qyqy.ucoo.compose.pages.NewbieRecommendNavigator
import com.qyqy.ucoo.compose.presentation.chatgroup.page.ChatGroupNavigator
import com.qyqy.ucoo.compose.presentation.game.DiamondScreenNavigator
import com.qyqy.ucoo.compose.presentation.game.GameMatchScreenNavigator
import com.qyqy.ucoo.compose.presentation.global_chat.GlobalUserListNavigator
import com.qyqy.ucoo.compose.presentation.greets.CustomGreetingsNav
import com.qyqy.ucoo.compose.presentation.member.MemberCenterScreenNavigator
import com.qyqy.ucoo.compose.presentation.profile.UserProfileNavigator
import com.qyqy.ucoo.compose.presentation.room.BackgroundSettingNavigator
import com.qyqy.ucoo.compose.presentation.room.RoomPkCreateNavigator
import com.qyqy.ucoo.compose.presentation.select_user.SelectUserNavigator
import com.qyqy.ucoo.compose.presentation.select_user.UserSelectorNavigator
import com.qyqy.ucoo.compose.presentation.video_edit.PublishedVideoShowsNavigator
import com.qyqy.ucoo.compose.presentation.virtually.VirtualMatchNavigator
import com.qyqy.ucoo.compose.presentation.voice.VoiceListNav
import com.qyqy.ucoo.compose.presentation.wedding.WeddingNavigator
import com.qyqy.ucoo.compose.presentation.welfare_center.WelfareCenterNavigator
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.tribe.member.MemberNoCPNavigator
import com.qyqy.ucoo.tribe.member.UndergroundCPNavigator
import com.qyqy.ucoo.utils.LogUtil
import com.qyqy.ucoo.widget.video.XVideoView


interface ScreenNavigator {

    fun key(): String = this.javaClass.let { it.canonicalName ?: it.name }

    @Composable
    fun ContentScreen(activity: BaseActivity, bundle: Bundle)

    fun beforeCombination(activity: BaseActivity, bundle: Bundle) {}

    fun onClear(activity: BaseActivity) {
        XVideoView.releaseAll()
    }
}

class ComposeScreen : BaseActivity() {

    companion object {
        private const val SCREEN_NAME = "compose_screen_name"

        // 此处注入
        private val navControllers: Array<ScreenNavigator> = arrayOf(
            WelfareCenterNavigator,
            SelectUserNavigator,
            UserProfileNavigator,
            BackgroundSettingNavigator,
            VoiceListNav,
            VirtualMatchNavigator,
            PublishedVideoShowsNavigator,
            CustomGreetingsNav,
            MemberCenterScreenNavigator,
            DiamondScreenNavigator,
            GameMatchScreenNavigator,
            GlobalUserListNavigator,
            MainNavigator,
            ChatGroupNavigator,
            BlackListNavigator,
            FullScreenNotificationAlert,
            NewbieRecommendNavigator,
            CPHouseNavigator,
            UndergroundCPNavigator,
            MemberNoCPNavigator,
            WeddingNavigator,
            UserSelectorNavigator,
            RoomPkCreateNavigator
        )

        fun ScreenNavigator.navigate(context: Context, block: Intent.() -> Unit = {}) {
            context.startActivity(obtainIntent(context, block))
        }

        fun ScreenNavigator.obtainIntent(context: Context, block: Intent.() -> Unit = {}): Intent {
            return Intent(context, ComposeScreen::class.java)
                .putExtra(SCREEN_NAME, key()).also {
                    it.block()
                }
        }
    }

    private val mCurrentNavigator by lazy {
        findNavigator()
    }

    fun isScreen(navigator: ScreenNavigator): Boolean {
        return mCurrentNavigator == navigator
    }

    private fun findNavigator(): ScreenNavigator? {
        val screenName = intent.getStringExtra(SCREEN_NAME).orDefault(WelfareCenterNavigator.key())
        Firebase.crashlytics.log("latest screen name: $screenName")
        val navigator = navControllers.firstOrNull {
            it.key() == screenName
        }
        if (navigator == null) {
            toast("[${screenName.substringAfterLast('.')}] is NOT IN ComposeScreen.navControllers!!!")
        }
        return navigator
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.d("${mCurrentNavigator}")
        if (mCurrentNavigator != null) {
            val bundle = intent?.extras ?: Bundle()
            mCurrentNavigator!!.beforeCombination(this, bundle)
            setContent {
                AppTheme {
                    mCurrentNavigator!!.ContentScreen(this@ComposeScreen, bundle)
                }
            }
        } else {
            finish()
            return
        }


    }

    override fun onDestroy() {
        mCurrentNavigator?.onClear(this)
        super.onDestroy()
    }
}