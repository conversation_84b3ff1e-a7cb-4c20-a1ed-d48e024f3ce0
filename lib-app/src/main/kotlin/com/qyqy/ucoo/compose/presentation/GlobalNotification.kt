package com.qyqy.ucoo.compose.presentation

import android.app.Dialog
import android.view.ViewGroup
import android.view.WindowManager
import androidx.annotation.MainThread
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.MutableTransitionState
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.draggable
import androidx.compose.foundation.gestures.rememberDraggableState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavBackStackEntry
import androidx.navigation.compose.currentBackStackEntryAsState
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.overseas.common.ext.activityBagOfTags
import com.overseas.common.ext.asLifecycleOwner
import com.overseas.common.ext.doOnDestroy
import com.overseas.common.ext.launch
import com.qyqy.cupid.ui.AppNavController
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.getSubRouteNav
import com.qyqy.cupid.ui.toCupidRoute
import com.qyqy.ucoo.ActivityLifecycle
import com.qyqy.ucoo.R
import com.qyqy.ucoo.UserPartition
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.app
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.toAnnotatedString
import com.qyqy.ucoo.im.chat.ChatActivity
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.IMCompatListener
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.UCInstanceMessage
import com.qyqy.ucoo.im.compat.chat.utils.UIMessageUtils
import com.qyqy.ucoo.im.compat.getExtraBoolean
import com.qyqy.ucoo.im.compat.getExtraString
import com.qyqy.ucoo.im.compat.toUser
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.utils.LogUtils
import com.qyqy.ucoo.utils.blur.BlurTransformation
import com.qyqy.ucoo.utils.takeIsNotEmpty
import com.qyqy.ucoo.widget.dialog.AppTopSheetDialog
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlin.math.roundToInt


private const val KEY = "global_message_notification"

interface INotificationSetting {
    fun isNotificationEnable(): Boolean = true
}

private data class MessageNotification(
    val user: AppUser,
    val name: String,
    val avatar: String,
    val applyBlurAvatar: Boolean,
    val desc: AnnotatedString,
    val extra: String?,
)


@Composable
private fun GlobalMessageNotification(
    content: MessageNotification,
    onClick: () -> Unit = {},
    onDismiss: () -> Unit = {},
) {
    var offsetY by remember { mutableFloatStateOf(0f) }
    Row(
        modifier = Modifier
            .offset { IntOffset(0, offsetY.roundToInt()) }
            .statusBarsPadding()
            .padding(horizontal = 16.dp, vertical = 5.dp)
            .fillMaxWidth()
            .height(80.dp)
            .clip(Shapes.corner12)
            .paint(painterResource(id = R.drawable.bg_global_notification), contentScale = ContentScale.FillBounds)
            .draggable(orientation = Orientation.Vertical, state = rememberDraggableState { delta ->
                if (offsetY <= 0) {
                    offsetY += delta
                    if (offsetY > 0) {
                        offsetY = 0f
                    }
                }
            }, onDragStopped = {
                if (offsetY != 0f) {
                    onDismiss()
                }
            })
            .noEffectClickable(onClick = onClick), verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.width(16.dp))

        CircleComposeImage(model = content.user.avatarUrl, modifier = Modifier.size(48.dp)) {
            if (content.applyBlurAvatar) {
                it.transform(CircleCrop(), BlurTransformation(18))
            } else {
                it
            }
        }

        Spacer(modifier = Modifier.width(4.dp))

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = content.name,
                fontSize = 15.sp,
                color = Color.White,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = content.desc,
                fontSize = 15.sp,
                color = Color(0xA6FFFFFF),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }

        VerticalDivider(
            modifier = Modifier
                .padding(start = 6.dp)
                .height(32.dp), thickness = 1.5.dp, color = Color(0x4DFFFFFF)
        )

        Column(
            modifier = Modifier.padding(horizontal = 28.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                modifier = Modifier
                    .size(56.dp, 24.dp)
                    .background(
                        Brush.horizontalGradient(
                            listOf(
                                Color(0xFFAF64F5),
                                Color(0xFF8B56FC)
                            )
                        ), CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                AutoSizeText(
                    text = stringResource(id = R.string.回复),
                    color = Color.White,
                    fontSize = 14.sp
                )
            }
            if (content.extra != null) {
                Text(
                    text = content.extra,
                    modifier = Modifier.padding(top = 5.dp),
                    fontSize = 12.sp,
                    color = Color(0xFFFFD97D)
                )
            }
        }
    }
}

object GlobalNotification {

    private var userPartition: UserPartition? = null

    private val callback = object : IMCompatListener {

        override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
            ActivityLifecycle.resumeTopActivity ?: return
            if (offline) {
                return
            }
            if (message.isSystemMsg) {
                if (userPartition?.isUCOO == true && message.cmd == MsgEventCmd.RECOMMAND_NEWBIE_UPDATE) {
                    val user = message.getJsonValue<AppUser>("user") ?: return
                    val descStr = message.getJsonString("recommend_action") ?: return
                    val msgContent = MessageNotification(
                        user = user,
                        name = user.nickname,
                        avatar = user.avatarUrl,
                        applyBlurAvatar = false,
                        desc = buildAnnotatedString {
                            append(descStr)
                        },
                        extra = null
                    )
                    appCoroutineScope.launch {
                        showUCOOMessageNotification(msgContent)
                    }
                }
            }
        }

        override fun onMessageExpansionUpdate(message: UCInstanceMessage, add: Boolean, expansions: Map<String, String>) {
            ActivityLifecycle.resumeTopActivity ?: return
            if (!add || !message.isC2CMsg || message.isSelf) {
                return
            }
            if (expansions["in_app_notify"]?.toBoolean() != true) {
                return
            }

            val user = message.user ?: return

            val content = MessageNotification(
                user = user,
                name = expansions["ian_mask_nickname"]?.takeIsNotEmpty()
                    ?: message.getExtraString("ian_mask_nickname").ifEmpty { user.nickname },
                avatar = user.avatarUrl,
                applyBlurAvatar = expansions["ian_mask_avatar"]?.toBoolean()
                    ?: message.getExtraBoolean("ian_mask_avatar", false),
                desc = buildAnnotatedString {
                    (expansions["ian_preview_msg"]?.takeIsNotEmpty() ?: message.getExtraValue("ian_preview_msg"))?.also {
                        append(it)
                    } ?: run {
                        append(
                            UIMessageUtils.getMessageSummary(
                                context = app,
                                hasUnread = false,
                                message = message,
                                isUCOO = userPartition?.isUCOO ?: true
                            ).toAnnotatedString()
                        )
                    }
                },
                extra = expansions["ian_action_hint"]?.takeIsNotEmpty() ?: message.getExtraValue("ian_action_hint")
            )

            appCoroutineScope.launch {
                userPartition?.also {
                    if (it.isUCOO) {
                        showUCOOMessageNotification(content)
                    } else {
                        showCupidMessageNotification(content)
                    }
                }
            }
        }
    }

    fun registerRcMessage(userPartition: UserPartition) {
        this.userPartition = userPartition
        IMCompatCore.addIMListener(callback)
    }

    fun unregisterRcMessage() {
        userPartition = null
        IMCompatCore.removeIMListener(callback)
    }
}


private fun showCupidMessageNotification(content: MessageNotification) {
    cupidNotifyFlow.update {
        if (it != null) {
            MsgNotify.Replace(it.content, MsgNotify.Normal(content))
        } else {
            MsgNotify.Normal(content)
        }
    }
}

@MainThread
private fun showUCOOMessageNotification(content: MessageNotification) {
    val activity = ActivityLifecycle.resumeTopActivity ?: return

    if (activity is INotificationSetting && !activity.isNotificationEnable()) {
        // 当前页面不允许通知
        return
    }

    activity.activityBagOfTags?.apply {

        try {
            getAnyTag<Dialog>(KEY)?.dismiss()
            remove(KEY)
        } catch (ignore: Exception) {
        }

        val composeView = ComposeView(activity).also {
            it.layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
        }
        val dialog = AppTopSheetDialog.Builder(activity).also {
            it.setView(composeView)
            it.setWidth(WindowManager.LayoutParams.MATCH_PARENT)
            it.setCanceledOnTouchOutside(false)
        }.create()

        dialog.window?.also {
            val params = it.attributes
            params.flags = params.flags or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
            it.setDimAmount(0f)
        }

        composeView.setContent {
            GlobalMessageNotification(content, {
                dialog.dismiss()
                activity.startActivity(ChatActivity.createIntent(activity, content.user))
            }) {
                dialog.dismiss()
            }
        }

        try {
            dialog.show()
        } catch (e: Exception) {
            LogUtils.w("GlobalNotification", "dialog cannot show. case %s", e)
            return
        }

        setTag(KEY, dialog)

        val job = dialog.launch {
            delay(5000)
            try {
                dialog.dismiss()
            } catch (e: Exception) {
                return@launch
            }
        }

        activity.asLifecycleOwner.doOnDestroy {
            job.cancel()
            try {
                dialog.dismiss()
            } catch (e: Exception) {
                return@doOnDestroy
            }
        }
    }
}

private sealed class MsgNotify(val content: MessageNotification) {

    class Normal(content: MessageNotification) : MsgNotify(content)

    class Replace(from: MessageNotification, val to: Normal) : MsgNotify(from)
}

private val cupidNotifyFlow = MutableStateFlow<MsgNotify?>(null)

sealed interface IVisible {

    interface Animating : IVisible

    interface Final : IVisible

    object Enter : Animating

    object Exit : Animating

    object Show : Final

    object Hide : Final

    data object Idea : IVisible

    val isVisible: Boolean
        get() = this == Enter || this == Show
}

@Composable
fun rememberDialogState(onDismiss: ((DialogState) -> Unit)? = null): DialogState = remember {
    DialogState(onDismiss)
}.apply {
    onDismissState.value = onDismiss
}

class DialogState(updatedOnDismiss: ((DialogState) -> Unit)? = null) {

    var onDismissState = mutableStateOf(updatedOnDismiss)

    var dialogStatus by mutableStateOf<IVisible>(IVisible.Idea)

    internal val canShowing: Boolean
        get() = dialogStatus != IVisible.Hide

    internal val isVisible: Boolean
        get() = dialogStatus.isVisible

    fun close() {
        if (dialogStatus == IVisible.Idea) {
            onDismissState.value?.invoke(this) ?: dismiss()
        } else if (dialogStatus != IVisible.Exit && dialogStatus != IVisible.Hide) {
            dialogStatus = IVisible.Exit
        }
    }

    fun dismiss() {
        dialogStatus = IVisible.Hide
    }

    fun reset() {
        dialogStatus = IVisible.Idea
    }

    internal fun enter() {
        if (dialogStatus == IVisible.Idea) {
            dialogStatus = IVisible.Enter
        }
    }

    internal fun toFinalStatus(state: MutableTransitionState<Boolean>) {
        if (dialogStatus == IVisible.Exit) {
            if (state.isIdle && !state.currentState) {
                onDismissState.value?.invoke(this) ?: dismiss()
            }
        } else if (dialogStatus == IVisible.Enter) {
            if (state.isIdle && state.currentState) {
                dialogStatus = IVisible.Show
            }
        }
    }
}

@Composable
fun CupidGlobalMessageNotification() {

    val navController = LocalAppNavController.current

    val currentEntry by navController.composeNav.currentBackStackEntryAsState()

    val current = cupidNotifyFlow.collectAsStateWithLifecycle().value ?: return

    ActivityLifecycle.resumeTopActivity ?: return

    val dialogState = rememberDialogState {
        it.dismiss()
        if (!currentEntry.isNotificationEnable(navController)) {
            cupidNotifyFlow.value = null
        } else if (current is MsgNotify.Replace) {
            cupidNotifyFlow.value = current.to
            it.reset()
        } else {
            cupidNotifyFlow.value = null
        }
    }

    if (current is MsgNotify.Replace) {
        LaunchedEffect(Unit) {
            dialogState.close()
        }
    }

    val notification = current.content

    LaunchedEffect(key1 = currentEntry) {
        snapshotFlow {
            currentEntry.isNotificationEnable(navController)
        }.filter {
            !it
        }.collectLatest {
            dialogState.close()
        }
    }

    LaunchedEffect(key1 = notification) {
        delay(5000)
        dialogState.close()
    }


    AnimatedPopup(dialogState = dialogState, alignment = Alignment.TopCenter) {
        MessageNotificationLayout(notification, {
            dialogState.close()
            navController.navigate(CupidRouters.C2CChat, mapOf("user" to notification.user))
        }) {
            dialogState.close()
        }
    }
}

@Composable
private fun AnimatedPopup(
    dialogState: DialogState,
    alignment: Alignment = Alignment.TopStart,
    content: @Composable () -> Unit,
) {
    if (!dialogState.canShowing) {
        return
    }

    Popup(
        alignment = alignment, onDismissRequest = {
            dialogState.close()
        }, properties = PopupProperties(
            focusable = false,
            dismissOnBackPress = false,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = true,
        )
    ) {
        Box(
            modifier = Modifier.fillMaxWidth(), contentAlignment = alignment
        ) {
            val state = remember {
                MutableTransitionState(false)
            }.apply {
                targetState = dialogState.isVisible
            }

            AnimatedVisibility(
                visibleState = state,
                enter = slideInVertically(initialOffsetY = { -it }),
                exit = fadeOut() + slideOutVertically(targetOffsetY = { -it })
            ) {
                content()
            }

            dialogState.toFinalStatus(state)
        }

        SideEffect {
            dialogState.enter()
        }
    }
}


@Composable
private fun MessageNotificationLayout(
    content: MessageNotification,
    onClick: () -> Unit = {},
    onDismiss: () -> Unit = {},
) {
    var offsetY by remember { mutableFloatStateOf(0f) }
    Row(
        modifier = Modifier
            .offset { IntOffset(0, offsetY.roundToInt()) }
            .statusBarsPadding()
            .size(343.dp, 80.dp)
            .graphicsLayer {
                shadowElevation = 4.dp.toPx()
                shape = Shapes.corner12
            }
            .clip(Shapes.corner12)
            .background(
                brush = Brush.verticalGradient(
                    listOf(
                        Color(0xFFFFEFF4),
                        Color(0xFFFFFEFB)
                    )
                )
            )
            .draggable(orientation = Orientation.Vertical, state = rememberDraggableState { delta ->
                if (offsetY <= 0) {
                    offsetY += delta
                    if (offsetY > 0) {
                        offsetY = 0f
                    }
                }
            }, onDragStopped = {
                if (offsetY != 0f) {
                    onDismiss()
                }
            })
            .noEffectClickable(onClick = onClick), verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.width(16.dp))

        CircleComposeImage(model = content.user.avatarUrl, modifier = Modifier.size(40.dp)) {
            if (content.applyBlurAvatar) {
                it.transform(CircleCrop(), BlurTransformation(18))
            } else {
                it
            }
        }

        Spacer(modifier = Modifier.width(8.dp))

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = content.name,
                fontSize = 15.sp,
                color = Color(0xFF1D2129),
                fontWeight = FontWeight.Medium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(3.dp))

            Text(
                text = content.desc,
                fontSize = 13.sp,
                color = Color(0xFF1D2129),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }

        VerticalDivider(
            modifier = Modifier
                .padding(start = 6.dp)
                .height(24.dp), thickness = 1.dp, color = Color(0x14000000)
        )

        Column(modifier = Modifier.padding(horizontal = 28.dp)) {
            AppText(
                text = stringResource(id = R.string.cpd回复),
                color = Color(0xFFFF5E8B),
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
            if (content.extra != null) {
                Row(
                    modifier = Modifier.padding(top = 5.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = content.extra, fontSize = 12.sp, color = Color(0xFFFF5E8B)
                    )
                    Spacer(modifier = Modifier.width(1.dp))
                    Image(
                        painter = painterResource(id = R.drawable.ic_cpd_diamond),
                        contentDescription = null,
                        modifier = Modifier.size(12.dp)
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewMessageNotificationLayout() {
    MessageNotificationLayout(
        MessageNotification(
            user = userForPreview,
            name = "哈哈哈大地方",
            avatar = "",
            applyBlurAvatar = false,
            desc = buildAnnotatedString { append("哈哈哈大地方") },
            extra = "100"
        ), {}) {}
}

private fun NavBackStackEntry?.isNotificationEnable(navController: AppNavController): Boolean {
    if (this == null) {
        return false
    }
    val route = destination.route?.toCupidRoute()
    return when (route) {
        null, CupidRouters.C2CChat -> false
        CupidRouters.HOME -> {
            val subNav = getSubRouteNav(navController)
            subNav?.current != "14"
        }

        else -> true
    }
}

