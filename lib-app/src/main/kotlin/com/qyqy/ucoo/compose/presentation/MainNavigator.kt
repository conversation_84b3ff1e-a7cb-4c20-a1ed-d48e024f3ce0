package com.qyqy.ucoo.compose.presentation

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.orDefault
import com.qyqy.ucoo.compose.presentation.ComposeScreen.Companion.navigate
import com.qyqy.ucoo.compose.presentation.campaign.HeartBeatCPScreen
import com.qyqy.ucoo.compose.presentation.language.LanguageSettingsScreen
import com.qyqy.ucoo.compose.presentation.moment.MomentListPageRouter
import com.qyqy.ucoo.compose.presentation.profile.AttractionEditWidget
import com.qyqy.ucoo.compose.ui.newusers.GuideInfo
import com.qyqy.ucoo.compose.ui.newusers.NewcomerReceptionGuideScreen
import com.qyqy.ucoo.compose.ui.newusers.NewcomerReceptionPhotoSettingsScreen
import com.qyqy.ucoo.compose.ui.newusers.NewcomerReceptionSettingsScreen
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.feat.protential.PotentialScreen
import com.qyqy.ucoo.im.bean.CPRightPageInfo
import com.qyqy.ucoo.im.bean.Gift
import com.qyqy.ucoo.im.chat.ChatActivity
import com.qyqy.ucoo.sUser

object MainNavigator : ScreenNavigator {
    private const val KEY_START_DESTINATION = "start_destination"
    const val POTENTIAL_USERS = "potential_users"
    const val LANGUAGE_SETTINGS = "language_settings"
    const val ATTRACTIVE_SETTINGS = "attractive_settings"
    const val MOMENT_LIST = "moment_list"
    const val BECOME_CP = "become_cp"
    const val NEW_USER_ON_BOARDING = "new_user_on_boarding"
    const val NEW_USER_ON_BOARDING_SETTINGS = "new_user_on_boarding_settings"
    const val NEW_USER_ON_BOARDING_EDIT = "new_user_on_boarding_edit"

    @Composable
    override fun ContentScreen(activity: BaseActivity, bundle: Bundle) {
        val navController = rememberNavController()
        val startDestination = activity.intent.getStringExtra(KEY_START_DESTINATION) ?: POTENTIAL_USERS

        NavHost(
            navController, startDestination = startDestination,
            enterTransition = {
                slideInHorizontally(initialOffsetX = { it })
            },
            exitTransition = {
                slideOutHorizontally(targetOffsetX = { -it })
            },
            popEnterTransition = {
                slideInHorizontally(initialOffsetX = { -it })
            },
            popExitTransition = {
                slideOutHorizontally(targetOffsetX = { it })
            },
        ) {
            composable(POTENTIAL_USERS) {
                PotentialScreen()
            }
            composable(LANGUAGE_SETTINGS) {
                LanguageSettingsScreen()
            }
            composable(ATTRACTIVE_SETTINGS) {
                AttractionEditWidget() { activity.finish() }
            }
            composable(MOMENT_LIST) {
                MomentListPageRouter(remember {
                    activity.intent?.getParcelableExtra<AppUser>(Const.KEY_DATA).orDefault(sUser)
                })
            }
            composable(BECOME_CP) {
                val info = remember {
                    activity.intent?.getParcelableExtra<CPRightPageInfo>(Const.KEY_DATA).orDefault(CPRightPageInfo(gift = Gift()))
                }
                HeartBeatCPScreen(info, { activity.finish() }) {
                    activity.finish()
                    ChatActivity.startChatWithUser(activity, info.targetUser)
                }
            }

            composable(NEW_USER_ON_BOARDING) {
                val info = remember {
                    activity.intent?.getParcelableExtra<GuideInfo>(Const.KEY_DATA)
                } ?: run {
                    activity.finish()
                    return@composable
                }

                NewcomerReceptionGuideScreen(info = info, activity.intent?.getBooleanExtra(Const.KEY_FROM, true) == true)
            }

            composable(NEW_USER_ON_BOARDING_SETTINGS) {
                NewcomerReceptionSettingsScreen {
                    navController.navigate(NEW_USER_ON_BOARDING_EDIT)
                }
            }

            composable(NEW_USER_ON_BOARDING_EDIT) {
                NewcomerReceptionPhotoSettingsScreen()
            }
        }
    }

    fun start(context: Context, startDestination: String, block: (Intent) -> Unit = {}) {
        navigate(context) {
            putExtra(KEY_START_DESTINATION, startDestination)
            block(this)
        }
    }
}