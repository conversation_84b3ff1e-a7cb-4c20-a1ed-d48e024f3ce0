package com.qyqy.ucoo.compose.presentation.chatgroup

import com.qyqy.ucoo.chatgroup.data.ChatGroupBean
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroupBrief
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroupDetail
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroupListResp
import com.qyqy.ucoo.compose.presentation.chatgroup.data.JoinResult
import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.im.compat.conversation.AppConversationManger
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface ChatGroupApi {

    /**
     * 获取群组列表(简单版)
     */
    @GET("api/chatgroup/v1/chatgroup/list")
    suspend fun getChatGroupList(): ApiResponse<ChatGroupListResp>

    /**
     * 加入群组
     * chatgroup_id:Int
     */
    @POST("api/chatgroup/v1/member/apply")
    suspend fun joinGroup(@Body map: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JoinResult>

    /**
     * 获取聊天群简介
     */
    @Deprecated(
        "getBriefResult, 以便群聊会话列表及时追踪到详情的变化",
        replaceWith = ReplaceWith("getBriefResult(chatGroupId)", "com.qyqy.ucoo.compose.presentation.chatgroup.getBriefResult"),
        DeprecationLevel.WARNING
    )
    @GET("api/chatgroup/v1/chatgroup/brief")
    suspend fun getBrief(@Query("chatgroup_id") chatGroupId: Int): ApiResponse<ChatGroupBrief>

    /**
     * 获取聊天群详情
     */
    @Deprecated(
        "请使用getChatGroupDetailResult, 以便群聊会话列表及时追踪到详情的变化",
        replaceWith = ReplaceWith("getChatGroupDetailResult(chatGroupId)", "com.qyqy.ucoo.compose.presentation.chatgroup.getChatGroupDetailResult"),
        DeprecationLevel.WARNING
    )
    @GET("api/chatgroup/v1/chatgroup/detail")
    suspend fun getChatGroupDetail(@Query("chatgroup_id") chatGroupId: Int): ApiResponse<ChatGroupDetail>

    @POST(" api/chatgroup/v1/chatgroup/create")
    suspend fun createChatGroup(@Body map: Map<String, String>): ApiResponse<JsonObject>

    /**
     * 获取群组成员申请列表
     * chatgroup_id 群组id | 必传
     * last_apply_id    ? | 非必传
     */
    @GET("api/chatgroup/v1/memberapply/list")
    suspend fun getMemberRequestList(@Query("chatgroup_id") chatGroupId: Int, @Query("last_apply_id") lastApplyId: Int = 0): ApiResponse<JsonObject>

    /**
     * 同意/拒绝成员申请
     * chatgroup_id | 群组id | Int 必传
     * apply_id     | 成员id | Int 必传
     * agreed       | 是否同意 | Bool 必传
     */
    @POST("api/chatgroup/v1/memberapply/agree")
    suspend fun applyUserRequest(@Body params: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>

    /**
     * 退出群聊
     * chatgroup_id | 群组id | Int 必须
     */
    @POST("api/chatgroup/v1/member/quit")
    suspend fun applyUserQuit(@Body params: @JvmSuppressWildcards Map<String, Any>): ApiResponse<JsonObject>

    /**
     * 踢出成员
     * chatgroup_id | 群组id | Int必须
     * member_id    | 踢出成员id | Int 必须
     */
    @POST("api/chatgroup/v1/member/kick")
    suspend fun applyUserKick(@Body params: @JvmSuppressWildcards Map<String, Any>): ApiResponse<JsonObject>

    /**
     * 设置/取消管理员身份
     * chatgroup_id | 群组id | Int必须
     * member_id    | 踢出成员id | Int 必须
     * is_admin     | 是否设置管理员 | Bool 必须
     */
    @POST("api/chatgroup/v1/admin/set")
    suspend fun applyManagerRole(@Body params: @JvmSuppressWildcards Map<String, Any>): ApiResponse<JsonObject>

    @GET("api/chatgroup/v1/admin/list")
    suspend fun getManagerList(@Query("chatgroup_id") chatGroupId: Int): ApiResponse<JsonObject>

    /**
     * 获取群组内成员列表
     * chatgroup_id 群组id | 必传
     * last_apply_id    ? | 非必传
     */
    @GET("api/chatgroup/v1/member/list")
    suspend fun getGroupMemberList(@Query("chatgroup_id") chatGroupId: Int, @Query("last_member_id") lastMemberId: Int = 0): ApiResponse<JsonObject>

    /**
     * 申请群组解散
     * chatgroup_id 群组id | 必传
     */
    @POST("api/chatgroup/v1/chatgroup/disband")
    suspend fun applyGroupDisband(@Body params: @JvmSuppressWildcards Map<String, Any>): ApiResponse<JsonObject>

    /**
     * 更新群组信息
     * chatgroup_id         | 群组id | 必传
     * name                 | 群组名称 | 非必须
     * avatar_url           | 群组头像 | 非必须
     * intro                | 群组简介 | 非必须
     * enable_dont_disturb  | 开启免打扰 | 非必须
     * is_need_review       | 是否需要申请 | 非必须
     */
    @POST("api/chatgroup/v1/chatgroup/update")
    suspend fun updateGroupInfo(@Body params: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>

    /**
     * 获取正在语音房的群组成员
     *
     * @param chatGroupId 群组id
     */
    @GET("api/chatgroup/v1/members/in_room")
    suspend fun getMembersInRoom(@Query("chatgroup_id") chatGroupId: Int): ApiResponse<JsonObject>

    /**
     * 获取我加入的群聊列表
     * ps:在2.26.0 分享到群聊的需求中添加了这个接口
     * @return
     */
    @GET("api/chatgroup/v1/chatgroup/mylist")
    suspend fun getMyJoinChatGroups(): ApiResponse<ChatGroupBean>
}

suspend fun ChatGroupApi.getBriefResult(chatGroupId: Int): Result<ChatGroupBrief> {
    return runApiCatching {
        getBrief(chatGroupId)
    }.onSuccess {
        AppConversationManger.updateGroupInfo(it)
    }
}

suspend fun ChatGroupApi.getChatGroupDetailResult(chatGroupId: Int): Result<ChatGroupDetail> {
    return runApiCatching {
        getChatGroupDetail(chatGroupId)
    }.onSuccess {
        AppConversationManger.updateGroupInfo(it.toBrief())
    }
}