package com.qyqy.ucoo.compose.presentation.chatgroup

import androidx.compose.ui.res.stringResource
import androidx.lifecycle.lifecycleScope
import com.qyqy.ucoo.ActivityLifecycle
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroup
import com.qyqy.ucoo.compose.presentation.chatgroup.page.ChatGroupDestination
import com.qyqy.ucoo.compose.presentation.chatgroup.page.ChatGroupNavigator
import com.qyqy.ucoo.compose.ui.ThemeDialogContent
import com.qyqy.ucoo.compose.ui.dialogs.showComposeDialog
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.toast
import kotlinx.coroutines.launch

private val api = createApi<ChatGroupApi>()

fun launchChatGroup(chatGroupId: Int) {
    if (chatGroupId == 0) {
        toast("groupId is 0")
        return
    }
    val act = (ActivityLifecycle.topActivity as? BaseActivity) ?: return
    act.run {
        lifecycleScope.launch {
            showLoading()
            api.getBriefResult(chatGroupId)
                .onSuccess { brief ->
                    if (brief.relationWithMe == 10) {
                        ChatGroupNavigator.navigate(this@run, brief)
                    } else {
                        showComposeDialog {
                            ThemeDialogContent(
                                content = stringResource(id = R.string.tip_no_join),
                                buttonText = stringResource(id = R.string.confirm)
                            ) {
                                it.dismiss()
                            }
                        }
                    }
                }.toastError()
            hideLoading()
        }
    }
}

fun launchChatGroupDetail(chatGroupId: Int) {
    val act = (ActivityLifecycle.topActivity as? BaseActivity) ?: return
    act.run {
        lifecycleScope.launch {
            showLoading()
            api.getBriefResult(chatGroupId)
                .onSuccess { brief ->
//                    if (brief.relationWithMe == 10) {
//                        ChatGroupNavigator.navigate(this@run, brief,ChatGroupDestination.ChatGroupDetailDestination)
//                    } else {
//                        showComposeDialog {
//                            ThemeDialogContent(
//                                content = stringResource(id = R.string.tip_no_join),
//                                buttonText = stringResource(id = R.string.confirm)
//                            ) {
//                                it.dismiss()
//                            }
//                        }
//                    }
                    if (brief.relationWithMe == ChatGroup.Relation.JOINED) {
                        ChatGroupNavigator.navigate(this@run, brief)
                    } else {
                        ChatGroupNavigator.navigate(this@run, brief, ChatGroupDestination.ChatGroupDetailDestination)
                    }
                }.toastError()
            hideLoading()
        }
    }
}