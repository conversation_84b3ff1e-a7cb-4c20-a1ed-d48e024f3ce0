package com.qyqy.ucoo.compose.presentation.chatgroup.create

import android.net.Uri
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imeAnimationSource
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.isImeVisible
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.presentation.redpackage.TextFieldWithHint
import com.qyqy.ucoo.compose.theme.colorSecondBlack
import com.qyqy.ucoo.compose.theme.colorTheme
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.Loading
import com.qyqy.ucoo.compose.ui.LoadingWidget
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.user.RechargeDialogFragment
import com.qyqy.ucoo.utils.LogUtil

@OptIn( ExperimentalLayoutApi::class)
@Composable
fun CreateGroupPanel(
    avatar: Uri? = null,
    processing: Boolean = false,
    onClickAvatar: () -> Unit = {},
    onShowRechargeVip: () -> Unit = {},
    onCreateGroup: (String) -> Unit = {}
) {
    if (processing) {
        Loading()
    }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(color = colorSecondBlack, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        AppText(
            text = stringResource(id = R.string.create_group),
            fontSize = 16.sp,
            color = Color.White,
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(32.dp))
        Box(
            modifier = Modifier
                .size(80.dp)
                .clickable(onClick = onClickAvatar), contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(id = R.drawable.icon_create),
                contentDescription = "",
                modifier = Modifier.fillMaxSize()
            )
            if (avatar != null) {
                CircleComposeImage(
                    model = avatar, modifier = Modifier
                        .padding(2.dp)
                        .fillMaxSize()
                )
            }
        }
        Spacer(modifier = Modifier.height(24.dp))
        AppText(
            text = stringResource(id = R.string.group_name),
            fontSize = 14.sp,
            color = Color.White,
            modifier = Modifier.fillMaxWidth(),
        )
        Spacer(modifier = Modifier.height(10.dp))

        val textFieldValue = remember {
            mutableStateOf(TextFieldValue())
        }
        TextFieldWithHint(
            modifier = Modifier
                .background(Color(0x0DFFFFFF), RoundedCornerShape(8.dp))
                .padding(8.dp, 16.dp),
            textFieldValueState = textFieldValue,
            maxLength = 20,
            hint = stringResource(id = R.string.hint_create_group)
        )
        Spacer(modifier = Modifier.height(24.dp))

        val isEnable = !(avatar == null || textFieldValue.value.text.isEmpty() || processing)
        Box {
            AppButton(
                modifier = Modifier
                    .padding(12.dp)
                    .widthIn(min = 220.dp)
                    .background(
                        colorTheme, RoundedCornerShape(50)
                    ),
                text = stringResource(id = R.string.create_group),
                fontSize = 16.sp,
                isEnable = isEnable,
                color = Color.White,
            ) {
                if (textFieldValue.value.text.isEmpty()) {
                    toastRes(R.string.hint_create_group)
                    return@AppButton
                }
                onCreateGroup(textFieldValue.value.text)
            }
            AppText(
                text = stringResource(id = R.string.member_power),
                color = Color(0xFFFFD69C),
                fontSize = 9.sp,
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .offset(x = (-8).dp)
                    .background(
                        Brush.horizontalGradient(
                            listOf(
                                Color(0xFF464646),
                                colorSecondBlack
                            )
                        ),
                        RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp, bottomEnd = 12.dp)
                    )
                    .border(0.5.dp,Color(0x1AFFFFFF),shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp, bottomEnd = 12.dp))
                    .padding(6.dp, 4.dp)
            )
        }
        Spacer(modifier = Modifier.height(30.dp))
        val d = LocalDensity.current
//        AnimatedVisibility(visible = WindowInsets.isImeVisible) {
//            val h = WindowInsets.imeAnimationSource.getBottom(d)
//            val imeHeight by animateDpAsState(targetValue = with(d){h.toDp()})
        Spacer(modifier = Modifier.imePadding())
//        }
    }
}

@Preview
@Composable
fun CreateGroupPanelPreview() {
    CreateGroupPanel()
}