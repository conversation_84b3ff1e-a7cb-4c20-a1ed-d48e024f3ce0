package com.qyqy.ucoo.compose.presentation.chatgroup.create

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.core.view.setPadding
import androidx.fragment.app.viewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.BaseBottomSheetDialogFragment
import com.qyqy.ucoo.base.BaseFragment
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.user.vip.VipCenterFragment
import com.qyqy.ucoo.utils.RequestSinglePhotoHelper
import com.qyqy.ucoo.widget.dialog.AppBottomSheetDialog

class CreateGroupDialogFragment : BaseBottomSheetDialogFragment() {
    private val viewModel by viewModels<CreateGroupViewModel>()

    private val launcher = RequestSinglePhotoHelper(this) {
        viewModel.setPhoto(it)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val window = dialog?.window
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
        } else {
            window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        }
        val view = ComposeView(inflater.context).apply {
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            setContent {
                AppTheme {
                    val mediaData by viewModel.photoState.collectAsStateWithLifecycle()
                    val processing by viewModel.isProcessing.collectAsStateWithLifecycle()
                    CreateGroupPanel(processing = processing, avatar = mediaData?.uri, onClickAvatar = {
                        launcher.start()
                    }, onShowRechargeVip = {
                        toastRes(R.string.vip_not_active)
                        VipCenterFragment.newInstance("create_group").show(childFragmentManager, "create_group")
                    }) {
                        viewModel.performCreateChatGroup(it) {
                            // 创建成功
                            (parentFragment as? BaseFragment)?.performRefresh()
                            dismiss()
                        }
                    }
                }
            }
        }
        container?.also {
            it.addView(view, ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT))
        }
        return view
    }

    override fun initBuilder(builder: AppBottomSheetDialog.Builder<*>) {
        builder.setWidth(ViewGroup.LayoutParams.MATCH_PARENT)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val window = dialog?.window ?: return
        window.decorView.setPadding(0)
        window.decorView.background = ColorDrawable(Color.TRANSPARENT)
    }
}