package com.qyqy.ucoo.compose.presentation.chatgroup.create

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.presentation.chatgroup.ChatGroupApi
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroup
import com.qyqy.ucoo.compose.presentation.chatgroup.launchChatGroup
import com.qyqy.ucoo.compose.presentation.chatgroup.list.ChatGroupListViewModel
import com.qyqy.ucoo.core.oss.Uploader
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.parseValue
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.im.compat.ConversationType
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.MessageBundle
import com.qyqy.ucoo.im.compat.SendParams
import io.github.album.MediaData
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class CreateGroupViewModel(private val api: ChatGroupApi = createApi<ChatGroupApi>()) : ViewModel() {
    val photoState = MutableStateFlow<MediaData?>(null)
    private val _isProcessing = MutableStateFlow(false)
    val isProcessing = _isProcessing.asStateFlow()

    fun setPhoto(photo: MediaData) {
        viewModelScope.launch {
            photoState.emit(photo)
        }
    }

    fun performCreateChatGroup(name: String, onFinishPage: () -> Unit = {}) {
        viewModelScope.launch {
            val media = photoState.value ?: return@launch
            val mediaInfo = Uploader.uploadMediaData(media, "chatgroup")
            val url = mediaInfo?.mediaUrl ?: return@launch
            _isProcessing.value = true
            runApiCatching {
                val map = mapOf("name" to name, "avatar_url" to url)
                api.createChatGroup(map)
            }.onSuccess { jsonObject ->
                val id = jsonObject.parseValue<Int>("chatgroup_id") ?: 0
                val rcGroupId = jsonObject.parseValue<String>("rc_group_id") ?: ""
                //刷新列表
                ChatGroupListViewModel.effect.tryEmit(ChatGroup.Events.CHATGROUP_CREATED)
                // 创建成功 插入群聊设置消息 仅群主可见
                val bundle = MessageBundle.Custom.create(cmd = ChatGroup.Events.CHATGROUP_CREATED, data = jsonObject)
                IMCompatCore.insertLocalMessage(SendParams(rcGroupId, ConversationType.GROUP, isExcludedFromUnreadCount = true), bundle)

                // 跳转到聊天页面
                launchChatGroup(id)
                onFinishPage()
            }.toastError()
            _isProcessing.value = false
        }
    }
}