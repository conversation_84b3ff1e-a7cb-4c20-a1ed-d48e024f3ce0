package com.qyqy.ucoo.compose.presentation.chatgroup.detail

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.AppUserPartition
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.DataState
import com.qyqy.ucoo.base.successDataState
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.presentation.chatgroup.ChatGroupApi
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroupBrief
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroupDetail
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroupMember
import com.qyqy.ucoo.compose.presentation.chatgroup.data.MemberRoomInfo
import com.qyqy.ucoo.compose.presentation.chatgroup.getChatGroupDetailResult
import com.qyqy.ucoo.core.oss.Uploader
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getOrNull
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.compat.ConversationType
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.mine.url
import com.qyqy.ucoo.toastRes
import io.github.album.MediaData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.decodeFromJsonElement

class ChatGroupDetailViewModel(private val chatGroupId: Int, private val brief: ChatGroupBrief? = null) : ViewModel() {

    private val api = createApi<ChatGroupApi>()

    //详情
    private val _detailFlow = MutableStateFlow<DataState<ChatGroupDetail>>(DataState.Idle())
    val detailFlow = _detailFlow.asStateFlow()

    //是否加载中
    private val _isLoading = MutableStateFlow(false)
    val isLoading = _isLoading.asStateFlow()

    private val _adminListFlow = MutableStateFlow<DataState<List<ChatGroupMember>>>(DataState.Idle())
    val adminListFlow = _adminListFlow.asStateFlow()

    init {
        viewModelScope.launch {
            brief?.let {
                _detailFlow.emit(it.toDetail().successDataState())
            }
        }
    }

    fun loadDetail() {
        viewModelScope.launch {
            _isLoading.emit(true)
            api.getChatGroupDetailResult(chatGroupId)
                .onSuccess { detail ->
                    _detailFlow.emit(detail.successDataState())
                }.toastError()
            _isLoading.emit(false)
        }
    }

    fun update(g: ChatGroupBrief) {
        val current = _detailFlow.value.getOrNull()
        viewModelScope.launch {
            val newData =
                current?.copy(
                    memberCnt = g.memberCnt,
                    intro = g.intro,
                    name = g.name,
                    avatarUrl = g.avatarUrl
                )
                    ?: g.toDetail()
            _detailFlow.emit(newData.successDataState())
        }
    }

    fun changeApplyCount(applyCnt: Int) {
        if (applyCnt != -1) {
            val current = _detailFlow.value.getOrNull() ?: return
            viewModelScope.launch {
                val newData =
                    current.copy(
                        memberApplyWaitCnt = applyCnt
                    )
                _detailFlow.emit(newData.successDataState())
            }
        }
    }

    /**
     * 升级群组信息
     * @param groupName 群组名称
     * @param groupIntro 群组简介
     * @param enableDontDisturb 开启免打扰?
     * @param needReview 进群是否需要审核
     */
    fun updateGroupInfo(
        groupName: String? = null,
        groupIntro: String? = null,
        enableDontDisturb: Boolean? = null,
        //only group owner
        needReview: Boolean? = null,
    ) {
        val responseBody = buildMap {
            put("chatgroup_id", chatGroupId)
            groupName?.also { put("name", it) }
            groupIntro?.also { put("intro", it) }
            enableDontDisturb?.also { put("enable_dont_disturb", it.toString()) }
            needReview?.also { put("is_need_review", it.toString()) }
        }
        viewModelScope.launch {
            _isLoading.emit(true)
            runApiCatching {
                api.updateGroupInfo(responseBody)
            }.onSuccess {
                if (enableDontDisturb != null) {
                    onDisturbStatusUpdated(enableDontDisturb)
                }
                loadDetail()
            }.toastError()
            _isLoading.emit(false)
        }
    }

    /**
     * 升级融云的免打扰
     *
     * @param enableDontDisturb 是否开启免打扰
     */
    private fun onDisturbStatusUpdated(enableDontDisturb: Boolean) {
        val targetId = _detailFlow.value.getOrNull()?.rcGroupId ?: return
        IMCompatCore.setConversationReceiveMessageOpt(targetId, ConversationType.GROUP, enableDontDisturb)
    }

    /**
     * 更新群组头像
     */
    fun updateGroupAvatar(imgs: List<MediaData>) {
        viewModelScope.launch {
            _isLoading.emit(true)
            val list = Uploader.uploadImages(imgs, "avatar")
            val mediaInfo = list?.firstOrNull()
            if (mediaInfo != null) {
                runApiCatching {
                    api.updateGroupInfo(
                        mapOf(
                            "chatgroup_id" to chatGroupId,
                            "avatar_url" to mediaInfo.url
                        )
                    )
                }.onSuccess {
                    loadDetail()
                }.toastError()
            } else {
                if (AppUserPartition.isUCOO) {
                    toastRes(R.string.upload_image_failed)
                } else {
                    toastRes(R.string.cpd_upload_image_failed)
                }
            }
            _isLoading.emit(false)
        }
    }

    /**
     * 加入群组
     */
    fun joinGroup(onSuccess: () -> Unit) {
        viewModelScope.launch {
            _isLoading.emit(true)
            runApiCatching {
                api.joinGroup(mapOf("chatgroup_id" to chatGroupId))
            }.onSuccess {
                onSuccess()
            }.toastError()
            _isLoading.emit(false)
        }
    }

    /**
     * 退出群组
     */
    fun exitGroup(onSuccess: () -> Unit) {
        viewModelScope.launch {
            _isLoading.emit(true)
            runApiCatching {
                api.applyUserQuit(mapOf("chatgroup_id" to chatGroupId))
            }.onSuccess {
                onSuccess()
            }.toastError()
            _isLoading.emit(false)
        }
    }

    /**
     * 解散群组
     */
    fun disbandGroup(onSuccess: () -> Unit) {
        viewModelScope.launch {
            _isLoading.emit(true)
            runApiCatching {
                api.applyGroupDisband(mapOf("chatgroup_id" to chatGroupId))
            }.onSuccess {
                onSuccess()
            }.toastError()
            _isLoading.emit(false)
        }
    }

    /**
     * 设置或取消管理员身份
     */
    fun applyAdminRole(memberId: Int, setUp: Boolean, onSuccess: () -> Unit) {
        viewModelScope.launch {
            _isLoading.emit(true)
            runApiCatching {
//                * chatgroup_id | 群组id | Int必须
//                * member_id    | 踢出成员id | Int 必须
//                * is_admin     | 是否设置管理员 | Bool 必须
                api.applyManagerRole(
                    mapOf(
                        "chatgroup_id" to chatGroupId,
                        "member_id" to memberId,
                        "is_admin" to setUp.toString()
                    )
                )
            }.onSuccess {
                onSuccess()
            }.toastError()
            _isLoading.emit(false)
        }
    }

    /**
     * 踢出成员
     *
     * @param memberId 成员id
     */
    fun applyUserKick(memberId: Int, onSuccess: () -> Unit) {
        viewModelScope.launch {
            _isLoading.emit(true)
            runApiCatching {
//                * chatgroup_id | 群组id | Int必须
//                * member_id    | 踢出成员id | Int 必须
//                * is_admin     | 是否设置管理员 | Bool 必须
                api.applyUserKick(
                    mapOf(
                        "chatgroup_id" to chatGroupId,
                        "member_id" to memberId,
                    )
                )
            }.onSuccess {
                onSuccess()
            }.toastError()
            _isLoading.emit(false)
        }
    }

    /**
     * 处理用户的加入请求
     *
     * @param applyId 请求id
     * @param agreed 是否同意
     */
    fun applyUserRequest(applyId: Int, agreed: Boolean, onSuccess: () -> Unit) {
        viewModelScope.launch {
            _isLoading.emit(true)
            runApiCatching {
                api.applyUserRequest(
                    mapOf(
                        "chatgroup_id" to chatGroupId,
                        "apply_id" to applyId,
                        "agreed" to agreed.toString()
                    )
                )
            }.onSuccess {
                onSuccess()
            }.toastError()
            _isLoading.emit(false)
        }
    }

    suspend fun getMembersInRoom(chatGroupId: Int): List<MemberRoomInfo>? {
        return withContext(Dispatchers.IO) {
            runApiCatching {
                api.getMembersInRoom(chatGroupId)
            }.mapCatching {
                it.getOrNull("members_room_infos")?.let {
                    sAppJson.decodeFromJsonElement<List<MemberRoomInfo>>(it)
                }
            }
        }.getOrNull()
    }

}