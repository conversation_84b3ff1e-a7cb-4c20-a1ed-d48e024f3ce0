package com.qyqy.ucoo.compose.presentation.chatgroup.list

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.base.DataState
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.presentation.chatgroup.ChatGroupApi
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroupListResp
import com.qyqy.ucoo.compose.presentation.chatgroup.usecase.JoinGroupUseCase
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class ChatGroupListViewModel : ViewModel() {
    companion object {
        val effect = MutableSharedFlow<String>(extraBufferCapacity = 10, onBufferOverflow = BufferOverflow.DROP_OLDEST)
    }

    private val api = createApi<ChatGroupApi>()
    private val joinGroupUseCase = JoinGroupUseCase(api = api)
    private val _refreshFlow = MutableStateFlow(false)
    val refreshFlow = _refreshFlow.asStateFlow()

    private val _dataFlow = MutableStateFlow<DataState<ChatGroupListResp>>(DataState.Idle())
    val dataFlow = _dataFlow.asStateFlow()

    init {
        refresh()
    }

    fun refresh() {
        viewModelScope.launch {
            _refreshFlow.emit(true)
            runApiCatching {
                api.getChatGroupList()
            }.onSuccess {
                _dataFlow.emit(DataState.Success(it))
            }.toastError()
            _refreshFlow.emit(false)
        }
    }

    fun joinGroup(groupId: Int) {
        JoinGroupUseCase.joinGroup(groupId, joinGroupUseCase, viewModelScope)
    }

//    fun createGroup() {
//        viewModelScope.launch {
//            _refreshFlow.emit(true)
//            runApiCatching {
//                api.createGroup(mapOf("name" to "test by code", "avatar_url" to "https://res.cloudinary.com/xzwzztest/image/upload/v1651413270/avatar/eeb2ab39-d52e-4615-8bba-73ff4e7b124c.jpg"))
//            }.onSuccess {
//
//            }.toastError()
//            _refreshFlow.emit(false)
//        }
//    }
}