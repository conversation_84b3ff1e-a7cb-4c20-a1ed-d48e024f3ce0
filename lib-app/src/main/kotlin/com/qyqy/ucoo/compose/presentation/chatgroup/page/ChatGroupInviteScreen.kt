

package com.qyqy.ucoo.compose.presentation.chatgroup.page

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ElevatedButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.widgets.composeClick
import com.qyqy.ucoo.R
import com.qyqy.ucoo.asBase
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroupInviteUserBean
import com.qyqy.ucoo.compose.presentation.chatgroup.data.MemberRoomInfo
import com.qyqy.ucoo.compose.presentation.chatgroup.detail.ChatGroupDetailViewModel
import com.qyqy.ucoo.compose.router.LocalUCOONavController
import com.qyqy.ucoo.compose.state.StateListView
import com.qyqy.ucoo.compose.state.StateViewModelWithIntPage
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.theme.colorTheme
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.chat.ChatApi
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.tribe.launchAudioRoom
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonArray

/**
 *  @time 8/3/24
 *  <AUTHOR>
 *  @package com.qyqy.ucoo.compose.presentation.chatgroup.page
 */
class ChatGroupInviteViewModel : StateViewModelWithIntPage<ChatGroupInviteUserBean>() {
    private val api = createApi<ChatApi>()
    private var lastId = -1

    init {
        refresh()
    }

    override suspend fun loadData(pageNum: Int): Result<List<ChatGroupInviteUserBean>> {
        val isFirstPage = pageNum == firstPage
        val id = if (isFirstPage) 0 else lastId
        if (pageNum != firstPage && lastId == 0) {
            return Result.success(emptyList())
        }
        lastId = 0
        return runApiCatching { api.getRecentFriends() }.map { obj ->
            obj["users"]?.jsonArray?.let {
                sAppJson.decodeFromJsonElement<List<ChatGroupInviteUserBean>>(
                    it
                )
            }.orEmpty()
        }.toastError()
    }


    suspend fun inviteUser(chatGroupId: Int, user: ChatGroupInviteUserBean): Result<JsonObject> {
        return runApiCatching {
            api.inviteUserToGroup(
                mapOf(
                    "chatgroup_id" to chatGroupId.toString(),
                    "target_userid" to user.id.toString()
                )
            )
        }
    }
}


@Composable
fun ChatGroupInviteScreen(chatGroupId: Int) {
    val viewModel = viewModel<ChatGroupInviteViewModel>()
    val scope = rememberCoroutineScope()
    val controller = LocalUCOONavController.current
    AppTheme {
        val loadingFlag = LocalContentLoading.current
        Box(modifier = Modifier.windowInsetsPadding(WindowInsets.Companion.systemBars)) {
            Column(modifier = Modifier.fillMaxSize(1f)) {
                AppTitleBar(title = stringResource(id = R.string.邀请好友加入群聊), onBack = {
                    controller.popBackStack()
                })
                StateListView(
                    viewModel = viewModel,
                    keyProvider = { _, bean -> bean.id })
                { item: ChatGroupInviteUserBean, _: Int, _: CoroutineScope, _: MutableState<Boolean> ->
                    GroupInviteItem(item.avatarUrl, item.nickname, null) {
                        loadingFlag.value = true
                        scope.launch {
                            viewModel.inviteUser(chatGroupId, item).onSuccess {
                                loadingFlag.value = false
                                toastRes(R.string.group_invite_send_succeed)
                                controller.popBackStack()
                            }.onFailure {
                                loadingFlag.value = false
                            }.toastError()
                        }
                    }
                }
            }
        }
    }

}

@Composable
fun ChatGroupInVoiceRoomScreen(chatgroupId: Int) {
    val activity = LocalContext.current.asBase!!
    val viewModel = viewModel<ChatGroupDetailViewModel>(activity)

    val list = remember {
        mutableStateListOf<MemberRoomInfo>().apply {
            viewModel.detailFlow.value.getOrNull()?.also {
                addAll(it.membersRoomInfos)
            }
        }
    }

    LaunchedEffect(Unit) {
        viewModel.getMembersInRoom(chatgroupId)?.also {
            list.clear()
            list.addAll(it)
        }
    }

    Box(modifier = Modifier.windowInsetsPadding(WindowInsets.Companion.systemBars)) {
        Column(modifier = Modifier.fillMaxSize(1f)) {
            AppTitleBar(title = stringResource(id = R.string.语音房群友))
            LazyColumn {
                items(list) {
                    GroupInviteItem(it.avatarUrl, it.nickname, it.room?.let {
                        stringResource(id = R.string.正在房间, it.title)
                    }, button = stringResource(id = R.string.跟随进房)) {
                        val id = it.room?.id ?: return@GroupInviteItem
                        activity.launchAudioRoom(id)
                    }
                }
            }
        }
    }
}

@Composable
private fun GroupInviteItem(
    avatar: String,
    title: String,
    subTitle: String?,
    button: String = stringResource(id = R.string.邀请),
    onInviteUser: () -> Unit = {},
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .padding(16.dp, 12.dp)
            .fillMaxWidth(),
    ) {
        CircleComposeImage(
            model = avatar,
            modifier = Modifier.size(64.dp, 64.dp),
        )
        Spacer(modifier = Modifier.width(8.dp))
        Column(
            verticalArrangement = Arrangement.spacedBy(2.dp),
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                fontSize = 16.sp,
                overflow = TextOverflow.Ellipsis,
                maxLines = 1,
                style = TextStyle(
                    color = Color.White,
                )
            )
            if (subTitle != null) {
                Text(
                    text = subTitle,
                    fontSize = 12.sp,
                    overflow = TextOverflow.Ellipsis,
                    maxLines = 1,
                    style = TextStyle(
                        color = Color(0x80FFFFFF),
                    )
                )
            }
        }

        Spacer(modifier = Modifier.width(8.dp))
        ElevatedButton(
            onClick = composeClick {
                onInviteUser()
            }, modifier = Modifier
                .padding(16.dp)
                .widthIn(80.dp)
                .height(32.dp),
            contentPadding = PaddingValues(horizontal = 8.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = colorTheme,
                contentColor = Color.White
            ),
            elevation = null
        ) {
            Text(button, style = MaterialTheme.typography.titleSmall)
        }
    }
}

@Composable
@Preview(showBackground = true)
private fun ChatGroupInviteItemPreview() {
    GroupInviteItem(
        "https://media.ucoofun.com/opsite/avatar/male/avatar_male_18.jpg",
        "幼儿园班花幼儿园班花幼儿园班花幼儿园班花",
        null,
    )
}