package com.qyqy.ucoo.compose.presentation.chatgroup.page

import android.content.Context
import android.os.Build
import android.os.Bundle
import android.view.WindowManager
import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.viewModels
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navArgument
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.common.CommonTextEditContract
import com.qyqy.ucoo.common.TextEditParams
import com.qyqy.ucoo.compose.presentation.ComposeScreen.Companion.navigate
import com.qyqy.ucoo.compose.presentation.ScreenNavigator
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroup
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroupBrief
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroupDetail
import com.qyqy.ucoo.compose.presentation.chatgroup.detail.ChatGroupDetailViewModel
import com.qyqy.ucoo.compose.presentation.chatgroup.list.ChatGroupAdminListViewModel
import com.qyqy.ucoo.compose.presentation.chatgroup.list.ChatGroupExceptOwnerListViewModel
import com.qyqy.ucoo.compose.presentation.chatgroup.list.ChatGroupMemberListViewModel
import com.qyqy.ucoo.compose.presentation.chatgroup.list.ChatGroupOnlyMemberListViewModel
import com.qyqy.ucoo.compose.presentation.chatgroup.list.ChatGroupRequestListViewModel
import com.qyqy.ucoo.compose.presentation.profile.UserProfileNavigator
import com.qyqy.ucoo.compose.router.IDestination
import com.qyqy.ucoo.compose.router.LocalNavigationViewModelOwner
import com.qyqy.ucoo.compose.router.LocalUCOONavController
import com.qyqy.ucoo.compose.router.navigateTo
import com.qyqy.ucoo.compose.ui.ConfirmDialog
import com.qyqy.ucoo.compose.ui.ThemeDialogContent
import com.qyqy.ucoo.compose.ui.dialogs.showComposeDialog
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.im.compat.MsgFilter
import com.qyqy.ucoo.im.compat.WatchRecvNewCustomMessage
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.utils.LogUtil
import com.qyqy.ucoo.utils.rememberRequestAlbumPermissionHelper

//群组页面声明
sealed class ChatGroupDestination : IDestination {
    //群组详情
    data object ChatGroupDetailDestination : ChatGroupDestination() {
        override val route: String = "group_detail"
    }

    //群聊设置
    data object ChatGroupSettingsDestination : ChatGroupDestination() {
        override val route: String = "group_setting"
    }

    //入群请求
    data object ChatGroupRequestListDestination : ChatGroupDestination() {
        override val route: String = "group_request_list"
    }

    //群聊页
    data object ChatGroupScreenDestination : ChatGroupDestination() {
        override val route: String = "group_chat_screen"
    }

    //管理员列表
    data object ChatGroupAdminListDestination : ChatGroupDestination() {
        override val route: String = "group_admin_list"
    }

    //设置管理员
    data object ChatGroupAdminSetDestination : ChatGroupDestination() {
        override val route: String = "group_admin_set"
    }

    //删除成员
    data object ChatGroupMemberRemoveDestination : ChatGroupDestination() {
        override val route: String = "group_member_remove"
    }

    data object ChatGroupInviteDestination : ChatGroupDestination() {
        override val route: String = "group_invite_member/{chatgroup_id}"

        fun withIdRoute(chatgroup_id: Int): String = "group_invite_member/${chatgroup_id}"
    }

    data object ChatGroupInVoiceDestination : ChatGroupDestination() {
        override val route: String = "group_in_voice_room/{chatgroup_id}"

        fun withIdRoute(chatgroup_id: Int): String = "group_in_voice_room/${chatgroup_id}"
    }
}

//endregion

// region 群组相关的所有动作Action
sealed interface UIAction {
    data object Retry : UIAction//重试

    //detail
    data object Exit : UIAction//退出群组
    data object Join : UIAction//加入群组
    data object Disband : UIAction//解散群组
    data object SwitchNotify : UIAction //关闭/开启静音通知
    data object GoSettings : UIAction //跳转到群组设置
    data class GoUserProfile(val user: User) : UIAction

    //settings
    data object ChangeAvatar : UIAction//修改头像
    data object ChangeGroupName : UIAction {
        val REQUEST_CODE = 0x0001
    } //修改群组名称

    data object ChangeGroupDescription : UIAction {
        val REQUEST_CODE = 0x0002
    } //修改群组描述

    data object GoAdminList : UIAction//去往管理员列表
    data object GoSetAdmin : UIAction//去往管理员设置页面
    data object GoRemoveMember : UIAction //去往删除成员
    data object SwitchJoinAudit : UIAction //开关 - 是否开启入群审核

    data class RemoveMember(val memberId: Int) : UIAction //删除成员

    //应用管理员set/unset, @param isSet是否是设置,false为取消管理员
    data class ApplyManager(val memberId: Int, val setUp: Boolean) : UIAction//应用管理员set/unset


    //申请加入
    data class AcceptJoin(val apply_id: Int) : UIAction
    data class DeclineJoin(val apply_id: Int) : UIAction
}
//endregion


object ChatGroupNavigator : ScreenNavigator {
    private const val BRIEF_INFO = "brief_info"
    override fun beforeCombination(activity: BaseActivity, bundle: Bundle) {
        val window = activity.window
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
        } else {
            window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        }
    }

    @Composable
    override fun ContentScreen(activity: BaseActivity, bundle: Bundle) {
        //目的地字符串
        val key = bundle.getString(Const.KEY_ID)
        val destination = when (key) {
            ChatGroupDestination.ChatGroupSettingsDestination.route -> ChatGroupDestination.ChatGroupSettingsDestination
            ChatGroupDestination.ChatGroupRequestListDestination.route -> ChatGroupDestination.ChatGroupRequestListDestination
            ChatGroupDestination.ChatGroupDetailDestination.route -> ChatGroupDestination.ChatGroupDetailDestination
            else -> ChatGroupDestination.ChatGroupScreenDestination
        }

        val backOwner = LocalOnBackPressedDispatcherOwner.current
        val finishPage = {
            backOwner?.onBackPressedDispatcher?.onBackPressed() ?: activity.finish()
        }
        val brief = bundle.getParcelable<ChatGroupBrief>(BRIEF_INFO) ?: run {
            activity.finish()
            return
        }

        val viewModel by activity.viewModels<ChatGroupDetailViewModel> {
            viewModelFactory {
                initializer { ChatGroupDetailViewModel(brief.id, brief) }
            }
        }
        LaunchedEffect(key1 = viewModel) {
            viewModel.loadDetail()
        }


        //更新详情
        WatchRecvNewCustomMessage(MsgFilter(brief.rcGroupId)) {
            val cmd = it.cmd
            LogUtil.d("rec group msg $cmd", "xct")
            when (cmd) {
                ChatGroup.Events.CHATGROUP_MEMBER_QUIT,
                ChatGroup.Events.CHATGROUP_MEMBER_KICKED_OUT,
                ChatGroup.Events.CHATGROUP_NAME_CHANGE,
                -> {
                    val g = it.getJsonValue<ChatGroupBrief>("chatgroup") ?: return@WatchRecvNewCustomMessage
                    viewModel.update(g)
                }

                ChatGroup.Events.CHATGROUP_MEMBER_APPLY_CNT_CHANGE -> {
                    val applyCnt = it.getJsonInt("member_apply_cnt") ?: return@WatchRecvNewCustomMessage
                    viewModel.changeApplyCount(applyCnt)
                }

                ChatGroup.Events.CHATGROUP_DISBANDED -> {
                    val g = it.getJsonValue<ChatGroupBrief>("chatgroup") ?: return@WatchRecvNewCustomMessage
                    if (g.id != brief.id) {
                        return@WatchRecvNewCustomMessage
                    }
                    showComposeDialog(activity) { dialog ->
                        dialog.setCancelable(false)
                        ThemeDialogContent(
                            content = stringResource(id = R.string.group_destroyed),
                            buttonText = stringResource(id = R.string.confirm)
                        ) {
                            dialog.dismiss()
                            finishPage()
                        }
                    }
                }

                else -> {}
            }
        }


        val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()
        val groupInfo by viewModel.detailFlow.collectAsStateWithLifecycle()

        //region 详情页grid列表数据

        val memberViewModel = viewModel<ChatGroupMemberListViewModel>(activity) {
            ChatGroupMemberListViewModel(brief.id)
        }

        //endregion

        //region 管理员页面viewModel与数据管理


        val adminListViewModel by activity.viewModels<ChatGroupAdminListViewModel> {
            viewModelFactory {
                initializer { ChatGroupAdminListViewModel(brief.id) }
            }
        }
        val normalMemberListViewModel by activity.viewModels<ChatGroupOnlyMemberListViewModel> {
            viewModelFactory {
                initializer { ChatGroupOnlyMemberListViewModel(brief.id) }
            }
        }
        val exceptOwnerListViewModel by activity.viewModels<ChatGroupExceptOwnerListViewModel> {
            viewModelFactory {
                initializer { ChatGroupExceptOwnerListViewModel(brief.id) }
            }
        }
        //endregion

        //region 申请列表ViewModel管理

        val requestListViewModel by activity.viewModels<ChatGroupRequestListViewModel> {
            viewModelFactory {
                initializer { ChatGroupRequestListViewModel(brief.id) }
            }
        }
        //endregion

        val detail = groupInfo.getOrDefault(ChatGroupDetail())

        if (detail.isEmpty) {
            return
        }

        val context = LocalContext.current
        val navController = rememberNavController()

        //图片选择(跳转到另外的activity)
        val uploadImageHelper = rememberRequestAlbumPermissionHelper(context) {
            viewModel.updateGroupAvatar(it.list)
        }

        //文字编辑(跳转到另外的actvitiy)
        val editTextLauncher = rememberLauncherForActivityResult(contract = CommonTextEditContract()) { result ->
            val code = result?.first
            val str = result?.second
            if (code == UIAction.ChangeGroupName.REQUEST_CODE) {
                viewModel.updateGroupInfo(groupName = str)
            } else if (code == UIAction.ChangeGroupDescription.REQUEST_CODE) {
                viewModel.updateGroupInfo(groupIntro = str)
            }
        }

        val curRoute = navController.currentBackStackEntryAsState().value?.destination?.route ?: ""

        if (curRoute != ChatGroupDestination.ChatGroupDetailDestination.route && detail.relationWithMe == ChatGroup.Relation.NONE) {
            Dialog(onDismissRequest = { }) {
                ThemeDialogContent(
                    content = stringResource(id = R.string.tip_no_join),
                    buttonText = stringResource(id = R.string.confirm)
                ) {
                    finishPage()
                }
            }
        }
        //compose主体
        CompositionLocalProvider(
            LocalUCOONavController provides navController,
            LocalNavigationViewModelOwner provides activity
        ) {
            NavHost(
                navController = navController,
                startDestination = destination.route,
                modifier = Modifier.fillMaxSize(),
                enterTransition = {
                    slideInHorizontally(initialOffsetX = { it })
                },
                exitTransition = {
                    slideOutHorizontally(targetOffsetX = { -it })
                },
                popEnterTransition = {
                    slideInHorizontally(initialOffsetX = { -it })
                },
                popExitTransition = {
                    slideOutHorizontally(targetOffsetX = { it })
                },
            ) {
                composable(ChatGroupDestination.ChatGroupScreenDestination.route) {
                    ChatGroupScreen(detail = detail, activity)
                }
                composable(ChatGroupDestination.ChatGroupDetailDestination.route) {
                    GroupDetailPage(detail, isLoading) {
                        when (it) {
                            UIAction.GoSettings -> navController.navigate(ChatGroupDestination.ChatGroupSettingsDestination.route)

                            is UIAction.GoUserProfile -> {
                                UserProfileNavigator.navigate(activity, it.user)
                            }

                            UIAction.Join -> {
                                viewModel.joinGroup {
                                    viewModel.loadDetail()
                                    memberViewModel.refresh()
                                }
                            }

                            UIAction.Exit -> {
                                ConfirmDialog(context, context.getString(R.string.group_exit), context.getString(R.string.group_exit_confirm)) {
                                    viewModel.exitGroup { activity.finish() }
                                }.show()
                            }

                            UIAction.Disband -> {
                                ConfirmDialog(context, context.getString(R.string.group_disband), context.getString(R.string.group_disband_confirm)) {
                                    viewModel.disbandGroup { activity.finish() }
                                }.show()
                            }

                            UIAction.SwitchNotify -> {
                                viewModel.updateGroupInfo(enableDontDisturb = !detail.iEnableDontDisturb)
                            }

                            else -> {}
                        }
                    }
                }
                composable(ChatGroupDestination.ChatGroupSettingsDestination.route) {
                    GroupDetailSettingsPage(detail, isLoading) {
                        when (it) {
                            is UIAction.SwitchJoinAudit -> {
                                viewModel.updateGroupInfo(needReview = !detail.needReview)
                            }

                            is UIAction.ChangeAvatar -> {
                                uploadImageHelper.start()
                            }

                            is UIAction.ChangeGroupName -> {
                                editTextLauncher.launch(
                                    TextEditParams(
                                        UIAction.ChangeGroupName.REQUEST_CODE, "群组名称", "请输入群组名称",
                                        "保存", 20, 50,
                                        detail.name
                                    )
                                )
                            }

                            is UIAction.ChangeGroupDescription -> {
                                editTextLauncher
                                    .launch(
                                        TextEditParams(
                                            UIAction.ChangeGroupDescription.REQUEST_CODE, "群组简介", "请输入群组简介",
                                            "保存", 300, 240,
                                            detail.intro
                                        )
                                    )
                            }

                            is UIAction.GoAdminList -> {
                                navController.navigateTo(ChatGroupDestination.ChatGroupAdminListDestination.route)
                            }

                            is UIAction.GoRemoveMember -> {
                                navController.navigateTo(ChatGroupDestination.ChatGroupMemberRemoveDestination.route)
                            }

                            is UIAction.Disband -> {
                                ConfirmDialog(context, "解散群组", "确定要解散此群组吗?") {
                                    viewModel.disbandGroup { activity.finish() }
                                }.show()
                            }

                            else -> {}
                        }
                    }
                }
                composable(ChatGroupDestination.ChatGroupRequestListDestination.route) {
                    GroupJoinRequestListPage(requestListViewModel, isLoading) {
                        when (it) {
                            is UIAction.AcceptJoin -> {
                                viewModel.applyUserRequest(it.apply_id, true) {
                                    requestListViewModel.requestDone(it.apply_id, true)
                                }
                            }

                            is UIAction.DeclineJoin -> {
                                viewModel.applyUserRequest(it.apply_id, false) {
                                    requestListViewModel.requestDone(it.apply_id, false)
                                }
                            }

                            else -> {

                            }
                        }
                    }
                }
                composable(ChatGroupDestination.ChatGroupAdminListDestination.route) {
                    GroupAdminManageScreen(viewModel = adminListViewModel) {
                        when (it) {
                            is UIAction.ApplyManager -> {
                                viewModel.applyAdminRole(it.memberId, false) {
                                    adminListViewModel.refresh()
                                }
                            }

                            is UIAction.GoSetAdmin -> {
                                navController.navigateTo(ChatGroupDestination.ChatGroupAdminSetDestination.route)
                            }

                            else -> {}
                        }
                    }
                }
                composable(ChatGroupDestination.ChatGroupAdminSetDestination.route) {
                    GroupAdminSetScreen(viewModel = normalMemberListViewModel) {
                        when (it) {
                            is UIAction.ApplyManager -> {
                                viewModel.applyAdminRole(it.memberId, true) {
                                    adminListViewModel.refresh()
                                    navController.popBackStack(ChatGroupDestination.ChatGroupAdminListDestination.route, false)
                                }
                            }

                            else -> {}
                        }
                    }
                }
                composable(ChatGroupDestination.ChatGroupMemberRemoveDestination.route) {
                    GroupRemoveMemberScreen(viewModel = exceptOwnerListViewModel, isLoading) {
                        when (it) {
                            is UIAction.RemoveMember -> {
                                val memberId = it.memberId
                                viewModel.applyUserKick(memberId) {

                                    memberViewModel.removeUser(memberId)
                                    exceptOwnerListViewModel.removeUser(memberId)
                                    adminListViewModel.removeUser(memberId)
                                    normalMemberListViewModel.removeUser(memberId)

                                    viewModel.loadDetail()

                                    toastRes(R.string.group_remove_success)
                                }
                            }

                            else -> {}
                        }
                    }
                }
                composable(
                    ChatGroupDestination.ChatGroupInviteDestination.route,
                    arguments = listOf(navArgument("chatgroup_id") { type = NavType.StringType })
                ) {
                    var chatgroup_id = it.arguments!!.getString("chatgroup_id", "-1").toInt()
                    if (chatgroup_id == -1) {
                        chatgroup_id = bundle.getInt("chatgroup_id", -1)
                    }
                    ChatGroupInviteScreen(chatgroup_id)
                }

                composable(
                    ChatGroupDestination.ChatGroupInVoiceDestination.route,
                    arguments = listOf(navArgument("chatgroup_id") { type = NavType.StringType })
                ) {
                    var chatgroup_id = it.arguments!!.getString("chatgroup_id", "-1").toInt()
                    if (chatgroup_id == -1) {
                        chatgroup_id = bundle.getInt("chatgroup_id", -1)
                    }
                    ChatGroupInVoiceRoomScreen(chatgroup_id)
                }
            }
        }

    }


    fun navigate(
        context: Context,
        brief: ChatGroupBrief,
        destination: ChatGroupDestination = ChatGroupDestination.ChatGroupScreenDestination,
    ) {
        this.navigate(context) {
            putExtra(BRIEF_INFO, brief)
            putExtra(Const.KEY_ID, destination.route)
        }
    }

}