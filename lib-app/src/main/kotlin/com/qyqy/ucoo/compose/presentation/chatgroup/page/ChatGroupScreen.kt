package com.qyqy.ucoo.compose.presentation.chatgroup.page

import android.view.ViewGroup
import androidx.activity.ComponentActivity
import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatDialogFragment
import androidx.compose.animation.core.spring
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import androidx.navigation.compose.rememberNavController
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.base.EventViewModel
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroupDetail
import com.qyqy.ucoo.compose.presentation.config.LocalChatUIProvider
import com.qyqy.ucoo.compose.presentation.room.AudioPanel
import com.qyqy.ucoo.compose.presentation.room.ChatRoomScaffold
import com.qyqy.ucoo.compose.presentation.room.EmojiPanel
import com.qyqy.ucoo.compose.presentation.room.GamaPanelPage
import com.qyqy.ucoo.compose.presentation.room.IMPanel
import com.qyqy.ucoo.compose.presentation.room.KeyboardPanelState
import com.qyqy.ucoo.compose.presentation.room.LocalBubbleTheme
import com.qyqy.ucoo.compose.presentation.room.LocalMsgAudioPlayer
import com.qyqy.ucoo.compose.presentation.room.Scene
import com.qyqy.ucoo.compose.presentation.room.bottomBar
import com.qyqy.ucoo.compose.presentation.room.panelContent
import com.qyqy.ucoo.compose.presentation.room.rememberMsgAudioPlayer
import com.qyqy.ucoo.compose.presentation.room.rememberPanelState
import com.qyqy.ucoo.compose.presentation.room.withAutoHidePanel
import com.qyqy.ucoo.compose.router.LocalUCOONavController
import com.qyqy.ucoo.compose.router.navigateTo
import com.qyqy.ucoo.compose.ui.AppPhotoPreviewer
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.PaginateState
import com.qyqy.ucoo.compose.ui.overScrollVertical
import com.qyqy.ucoo.compose.ui.photo.IPhotoPreviewModel
import com.qyqy.ucoo.compose.ui.photo.LocalPhotoPreviewState
import com.qyqy.ucoo.compose.ui.photo.rememberPhotoGestureState
import com.qyqy.ucoo.compose.ui.photo.rememberPhotoPreviewState
import com.qyqy.ucoo.compose.ui.rememberOverscrollFlingBehavior
import com.qyqy.ucoo.compose.ui.topFadingEdge
import com.qyqy.ucoo.compose.vm.room.UIAction
import com.qyqy.ucoo.glide.ninepatch.BubbleTheme
import com.qyqy.ucoo.im.bean.GiftWrapper
import com.qyqy.ucoo.im.compat.MessageBundle
import com.qyqy.ucoo.im.compat.UCInstanceMessage
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.room.ChatRoomActivity
import com.qyqy.ucoo.im.room.RoomRepository
import com.qyqy.ucoo.im.room.panel.UserGiftPanelViewModel
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.tribe.OnAtUser
import com.qyqy.ucoo.tribe.TribeGiftPanelDialogFragment
import com.qyqy.ucoo.tribe.TribeUserPanelDialogFragment
import com.qyqy.ucoo.tribe.TribeUserPanelDialogFragment.Companion.makeShow
import com.qyqy.ucoo.tribe.model.ComposeSendGiftViewModel
import com.qyqy.ucoo.user.gift.ChatGiftLayout
import com.yy.yyeva.util.ScaleType
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlin.math.abs
import com.overseas.common.utils.dp as nativeDp

val LocalChatGroupDetail = staticCompositionLocalOf<ChatGroupDetail?> { null }

@Composable
fun ChatGroupScreen(detail: ChatGroupDetail, activity: BaseActivity? = null) {
    val view = LocalView.current

    val scope = rememberCoroutineScope()

    //region 2.39.0 用户资料卡 和 送礼物面板

    val showingFragment = remember {
        mutableStateOf<AppCompatDialogFragment?>(null)
    }

    /**
     * 为了和TribeUserPanelFragment拿到同一个UserGiftPanelViewModel
     * 这里对Activity做了判断,
     * 但是实际情况是activity不可能为null
     */
    if (activity != null) {
        val userGiftPanelViewModel = viewModel<UserGiftPanelViewModel>(activity)
        LaunchedEffect(key1 = Unit) {
            userGiftPanelViewModel.userGiftPanelChannel.collectLatest {
                activity.run {
                    showingFragment.value?.dismiss()
                    showingFragment.value = null
                    val fragment = TribeGiftPanelDialogFragment.newInstanceSingleUser(
                        bid = detail.chatGroupId,
                        imId = detail.rcGroupId,
                        from = ComposeSendGiftViewModel.FROM_GROUP,
                        selectedUserId = it.id,
                        selectedGiftId = -1,
                    )
                    val tag = "group_use_gift_panel"
                    fragment.show(supportFragmentManager.beginTransaction().addToBackStack(tag), tag)
                }
            }
        }
    }


    //endregion
    val density = LocalDensity.current
    val viewModel = viewModel<ChatGroupViewModel> {
        ChatGroupViewModel(detail.rcGroupId, hasAddNewMsgTag = true, fissionGiftMessageEnable = true)
    }

    var previewList by rememberSaveable {
        mutableStateOf(emptyList<IPhotoPreviewModel>())
    }

    val gestureState = with(density) {
        rememberPhotoGestureState(
            extraHorizontalOffset = 80.dp.toPx(),
            extraVerticalOffset = 100.dp.toPx(),
        )
    }

    val previewState = rememberPhotoPreviewState(
        gestureState = gestureState,
        onGestureClose = { _, offsetY, velocityY ->
            abs(velocityY) > 1000 || abs(offsetY) > 60.dp.toPx()
        }
    ) {
        previewList
    }

    val context = LocalContext.current


    val msgAudioPlayer = rememberMsgAudioPlayer()

    val showGamePanel = remember {
        mutableStateOf(false)
    }
    val uiProvider = remember(detail) {
        ChatGroupUIProvider(viewModel)
    }

    val msgEventHandler: MsgEventHandler = remember {
        object : MsgEventHandler {
            override fun handle(event: MsgEvents) {
                when (event) {
                    is MsgEvents.SeeUser -> {
                        val user = event.user
                        activity?.run {
                            TribeUserPanelDialogFragment.newInstance(
                                user.id,
                                from = ComposeSendGiftViewModel.FROM_GROUP,
                                roomId = detail.chatGroupId
                            ).apply {
                                showingFragment.value = this
                                this.addOnDismissListener {
                                    if (showingFragment.value == this) {
                                        showingFragment.value = null
                                    }
                                }
                            }.makeShow(
                                supportFragmentManager,
                                "group_user_panel"
                            )
                        }
                    }

                    is MsgEvents.OnPreview -> {
                        val (index, list) = viewModel.getPreviewImageList(true, event.image.message)
                        previewList = list
                        previewState.startPreview(index)
                    }

                    else -> {}
                }
            }
        }
    }

    CompositionLocalProvider(
        LocalBubbleTheme provides BubbleTheme(
            color1 = Color(0x0DFFFFFF),
            color2 = Color(0x0DFFFFFF),
            translateColor = Color(0x0DFFFFFF),
            paddingValues = PaddingValues(12.dp)
        ),
        LocalPhotoPreviewState provides previewState,
        LocalMsgAudioPlayer provides msgAudioPlayer,
        LocalMsgEventHandler provides msgEventHandler,
        LocalChatGroupDetail provides detail,
        LocalChatUIProvider provides uiProvider
    ) {

        val listState: LazyListState = rememberLazyListState()

        val panels = remember {
            arrayOf(
                IMPanel(EmojiPanel, forceRequestFocus = true), // 表情面板
                IMPanel(AudioPanel, realHeight = 0, autoHideEnable = false, applyNavigationBarsPadding = true, forceClearFocus = true) // 录音面板
            )
        }

        val panelState = rememberPanelState(listState, panels)

        viewModel.bindListState(listState)
        //初始化gift viewModel
        activity?.run {
            val giftViewModel by this.viewModels<ComposeSendGiftViewModel> {
                viewModelFactory {
                    initializer {
                        ComposeSendGiftViewModel(detail.chatGroupId, ComposeSendGiftViewModel.FROM_GROUP, detail.rcGroupId)
                    }
                }
            }

            LaunchedEffect(key1 = giftViewModel) {

                ChatGiftLayout(
                    view.rootView as ViewGroup,
                    lifecycle,
                    giftBannerTopMargin = 100.nativeDp,
                    giftPosition = GiftWrapper.tribe,
                    videoScaleType = ScaleType.CENTER_CROP
                ).also {
                    it.startCollect(giftViewModel)
                }

                viewModel.effect.onEach {
                    when (it) {
                        is UIAction.OnClickImage -> {
                            val (index, list) = viewModel.getPreviewImageList(true, it.item.message)
                            previewList = list
                            previewState.startPreview(index)
                        }

                        is UIAction.JoinAudioRoom -> {
                            val action = it
                            scope.launch {
                                RoomRepository().joinChatRoom(action.roomId, false).apply {
                                    onSuccess { result ->
                                        val room = result.first
                                        ChatRoomActivity.openRoomActivity(context, room, result.second, action.userId)
                                    }
                                    onFailure {
                                        toast(it.message)
                                    }
                                }
                            }
                        }

                        else -> Unit
                    }
                }.launchIn(this)
            }
        }

        ChatGroupPage(
            detail,
            messageList = viewModel.messageList,
            paginateState = viewModel.paginateState,
            listState = listState,
            panelState = panelState,
            onAction = {
                when (it) {

                    is UIAction.OnSendText -> {
                        viewModel.useAtInfo()?.also { info->
                            viewModel.sendMessageWithAt(MessageBundle.Text.create(it.content), info)
                        } ?: viewModel.sendMessage(MessageBundle.Text.create(it.content))
                    }

                    is UIAction.OnSendMessage -> {
                        viewModel.sendMessage(it.content)
                    }

                    is UIAction.OnGameCenter -> {
                        panelState.hideAll(true)
                        scope.launch {
                            delay(100)
                            showGamePanel.value = true
                        }
                    }

                    is UIAction.OnOpenGift -> {
                        activity?.run {
                            TribeGiftPanelDialogFragment.newInstance(
                                bid = detail.chatGroupId,
                                imId = detail.rcGroupId,
                                from = ComposeSendGiftViewModel.FROM_GROUP,
                                selectedUserId = ""
                            ).show(supportFragmentManager, "send_gift_panel")
                        }
                    }

                    else -> Unit
                }
            }
        ) {
            GamaPanelPage(Scene("7", detail.chatGroupId.toString()), showGamePanel) {
                viewModel.sendMessage(it)
            }

            AppPhotoPreviewer(previewState)
        }
    }
}

@Preview
@Composable
fun ChatGroupScreenPreview() {
    CompositionLocalProvider(LocalUCOONavController provides rememberNavController()) {
        ChatGroupScreen(ChatGroupDetail())
    }
}

@Composable
private fun ChatGroupPage(
    detail: ChatGroupDetail,
    messageList: List<MessageEntry<UCInstanceMessage>>,
    paginateState: PaginateState<*>,
    listState: LazyListState,
    panelState: KeyboardPanelState,
    onAction: (UIAction) -> Unit = {},
    overlayContent: @Composable() (BoxScope.() -> Unit) = {},
) {
    val navController = LocalUCOONavController.current
    val textFieldValue = remember {
        mutableStateOf(TextFieldValue())
    }
    val viewModel = viewModel<ChatGroupViewModel>()

    val context = LocalContext.current
    LaunchedEffect(key1 = textFieldValue) {
        val act = context as? ComponentActivity ?: return@LaunchedEffect
        val eventViewModel by act.viewModels<EventViewModel>()
        eventViewModel.events.collectLatest {
            if (it is OnAtUser) {
                if (viewModel.addAtUser( it.user)) {
                    val tfv = textFieldValue.value
                    val newText = "${tfv.text}@${it.user.nickname} "
                    textFieldValue.value = tfv.copy(text = newText, selection = TextRange(newText.length, newText.length))
                }
            }
        }
    }

    val onBackPressedDispatcherOwner = LocalOnBackPressedDispatcherOwner.current
    ChatRoomScaffold(
        panelState = panelState,
        topBar = {
            ChatGroupTitleBar(
                title = detail.name,
                memberCount = detail.memberCnt,
                applyCount = detail.memberApplyWaitCount,
                membersRoomInfos = detail.membersRoomInfos,
                onClickInvite = {
                    navController.navigateTo(ChatGroupDestination.ChatGroupInviteDestination.withIdRoute(detail.chatGroupId))
                },
                onClickApply = {
                    navController.navigateTo(ChatGroupDestination.ChatGroupRequestListDestination)
                },
                onClickMore = {
                    navController.navigateTo(ChatGroupDestination.ChatGroupDetailDestination)
                },
                onClickInRoom = {
                    navController.navigateTo(ChatGroupDestination.ChatGroupInVoiceDestination.withIdRoute(detail.chatGroupId))
                },
                onBack = {
                    onBackPressedDispatcherOwner?.onBackPressedDispatcher?.onBackPressed()
                })
        },
        bottomBar = bottomBar(panelState, textFieldValue, onAction),
        panelContent = panelContent(Color.Transparent, panelState, textFieldValue),
        overlayContent = overlayContent,
    ) {
        val isLoading by remember {
            derivedStateOf {
                paginateState.nextLoadState.isLoading
            }
        }

        val density = LocalDensity.current

        LazyColumn(
            modifier = Modifier
                .withAutoHidePanel(panelState)
                .fillMaxSize()
                .overScrollVertical()
                .topFadingEdge(
                    color = Color(0x33222222),
                    width = 30.dp,
                    spec = spring(),
                    isVisible = listState.canScrollBackward,
                ),
            state = listState,
            contentPadding = PaddingValues(horizontal = 16.dp, vertical = 16.dp),
            reverseLayout = true,
            verticalArrangement = Arrangement.spacedBy(16.dp),
            flingBehavior = rememberOverscrollFlingBehavior { listState }
        ) {
            items(
                items = messageList,
                key = {
                    "ChatGroupPage-${it.key}"
                }
            ) {
                val item = remember(context, density, it) {
                    ChatGroupViewModel.mapToMessageItem(context, density, it, viewModel._effect)
                }
                item.apply {
                    it.refresh
                    Content()
                }
            }

            if (isLoading) {
                item {
                    Box(
                        modifier = Modifier
                            .padding(top = 10.dp)
                            .fillMaxWidth(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            color = colorResource(id = R.color.color_primary_pink),
                            strokeWidth = 1.5.dp
                        )
                    }
                }
            }
        }

        val unreadMsgInfo = viewModel.unreadMsgInfo
        val unreadAtInfo = viewModel.unreadAtInfo

        if (unreadMsgInfo != null || unreadAtInfo != null) {
            Column(modifier = Modifier.align(Alignment.BottomEnd), horizontalAlignment = Alignment.End) {
                val modifier = Modifier
                        .background(Color(0xFF342A4B), RoundedCornerShape(topStartPercent = 50, bottomStartPercent = 50))
                        .padding(8.dp)
                val color = Color(0xFF945EFF)
                if (unreadMsgInfo != null) {
                    val text = if (unreadMsgInfo.unreadCount > 99) stringResource(id = R.string._99_not_read)
                    else stringResource(id = R.string.format_not_read, unreadMsgInfo.unreadCount)
                    AppText(text = text, modifier = modifier.clickable(onClick = {
                        viewModel.scrollToUnreadMessage()
                    }), color = color)
                    Spacer(modifier = Modifier.height(8.dp))
                }

                if (unreadAtInfo != null) {
                    AppText(
                        text = stringResource(id = R.string.at_you),
                        modifier = modifier.clickable(onClick = {
                            viewModel.scrollToAtMessage()
                        }),
                        color = color
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }
                Spacer(modifier = Modifier.height(20.dp))
            }
        }
    }
}