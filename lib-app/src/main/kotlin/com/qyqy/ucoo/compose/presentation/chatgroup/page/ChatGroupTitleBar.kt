package com.qyqy.ucoo.compose.presentation.chatgroup.page

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.presentation.chatgroup.data.MemberRoomInfo
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.CircleComposeImage

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ChatGroupTitleBar(
    title: String,
    memberCount: Int,
    applyCount: Int,
    membersRoomInfos: List<MemberRoomInfo> = emptyList(),
    onClickInvite: () -> Unit = {},
    onClickMore: () -> Unit = {},
    onClickApply: () -> Unit = {},
    onClickInRoom: () -> Unit = {},
    onBack: () -> Unit = {},
) {
    Column(modifier = Modifier.fillMaxWidth()) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(48.dp),
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_navigation_back),
                contentDescription = "back",
                contentScale = ContentScale.Inside,
                modifier = Modifier
                    .fillMaxHeight()
                    .aspectRatio(1f)
                    .clip(CircleShape)
                    .clickable(onClick = onBack)
            )

            Row(
                modifier = Modifier
                    .fillMaxSize(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                AppText(
                    text = title,
                    fontSize = 14.sp,
                    color = Color.White,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier
                        .widthIn(max = 200.dp)
                        .padding(end = 2.dp)
                        .basicMarquee()
                )
                AppText(text = "($memberCount)", fontSize = 14.sp, color = Color.White)
            }



            Row(
                modifier = Modifier
                    .fillMaxHeight()
                    .padding(horizontal = 16.dp)
                    .clickable(onClick = onClickMore)
                    .align(Alignment.TopEnd),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_chat_invite),
                    contentDescription = "clickInvite",
                    contentScale = ContentScale.Inside,
                    modifier = Modifier
                        .size(24.dp)
                        .clickable(onClick = onClickInvite)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Image(
                    painter = painterResource(id = R.drawable.ic_more_menu),
                    contentDescription = "clickMore",
                    contentScale = ContentScale.Inside,
                    modifier = Modifier
                        .size(24.dp)
                        .clickable(onClick = onClickMore)
                )
            }
        }

        AnimatedVisibility(visible = membersRoomInfos.isNotEmpty()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(44.dp)
                    .clickable(onClick = onClickInRoom)
                    .background(Color(0xFF2E2740))
                    .padding(horizontal = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(horizontalArrangement = Arrangement.spacedBy((-8).dp)) {
                    membersRoomInfos.take(6).forEach {
                        CircleComposeImage(
                            model = it.avatarUrl,
                            modifier = Modifier
                                .size(24.dp)
                                .border(0.5.dp, Color(0xFFFFFFFF), CircleShape)
                        )
                    }
                }

                Spacer(modifier = Modifier.width(6.dp))

                AutoSizeText(text = stringResource(id = R.string.群友正在语音房聊天, membersRoomInfos.size), modifier = Modifier.weight(1f), color = Color(0xFF945EFF), fontSize = 12.sp)

                Spacer(modifier = Modifier.width(6.dp))

                Text(text = stringResource(id = R.string.去看看), color = Color(0xFF945EFF), fontSize = 12.sp)

                Spacer(modifier = Modifier.width(4.dp))

                Image(
                    painter = painterResource(id = R.drawable.icon_arrow_blue),
                    contentDescription = "",
                    modifier = Modifier.size(12.dp),
                    colorFilter = ColorFilter.tint(Color(0xFF945EFF))
                )
            }
        }

        AnimatedVisibility(visible = applyCount > 0) {
            val count = if (applyCount > 99) "99+" else applyCount.toString()
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp, 8.dp)
                    .clickable(onClick = onClickApply)
                    .background(Color(0xFF16444B), RoundedCornerShape(6.dp))
                    .padding(12.dp),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Image(painter = painterResource(id = R.drawable.icon_group), contentDescription = "")
                Spacer(modifier = Modifier.width(4.dp))
                AppText(
                    text = stringResource(id = R.string.format_group_apply, count),
                    color = Color(0xFF00E1FF),
                    fontSize = 12.sp,
                    modifier = Modifier.weight(1f)
                )
                Spacer(modifier = Modifier.width(4.dp))
                AppText(
                    text = count, fontSize = 11.sp, color = Color.White,
                    modifier = Modifier
                        .background(
                            Color(0xFFF53F3F),
                            RoundedCornerShape(50)
                        )
                        .padding(4.dp, 2.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Image(painter = painterResource(id = R.drawable.icon_arrow_blue), contentDescription = "", modifier = Modifier.size(12.dp))
            }
        }
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF090808)
@Composable
fun ChatGroupTitleBarPreview() {
    ChatGroupTitleBar("ガールフレンドを探していますガールフレンドを探しています", 101, 100)
}