

package com.qyqy.ucoo.compose.presentation.chatgroup.page

import android.content.Context
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.im.messages.MessageReminder
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.nameColor
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroup
import com.qyqy.ucoo.compose.presentation.chatgroup.data.isAdmin
import com.qyqy.ucoo.compose.presentation.config.BaseChatUIProvider
import com.qyqy.ucoo.compose.presentation.room.LevelBadge
import com.qyqy.ucoo.compose.presentation.room.MessageTextFlow
import com.qyqy.ucoo.compose.presentation.room.PublishCp
import com.qyqy.ucoo.compose.presentation.room.TextSpan
import com.qyqy.ucoo.compose.presentation.room.buildTextSpan
import com.qyqy.ucoo.compose.presentation.room.inlineTextContent
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.im.compat.UCInstanceMessage
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry

class ChatGroupUIProvider(val viewModel: ChatGroupViewModel) :
    BaseChatUIProvider(isFamilyConversation = false, showBottomGiftIcon = false) {


    @Composable
    override fun ItemLayout(entry: UIMessageEntry<UCInstanceMessage>, content: @Composable ColumnScope.() -> Unit) {
        val handler = LocalMsgEventHandler.current
        val detail = LocalChatGroupDetail.current
        val user = entry.user
        val role = detail?.getUserRole(user.id) ?: ChatGroup.Role.MEMBER
        val ctx = LocalContext.current
        val density = LocalDensity.current
        val textSpan = remember(user, role) {
            buildUserSpan(ctx, density, user, role)
        }
        Column {
            Row(modifier = Modifier.padding(end = 20.dp)) {
                CircleComposeImage(
                    model = user.avatarUrl,
                    modifier = Modifier
                        .size(40.dp)
                        .clickable {
                            handler?.handle(MsgEvents.SeeUser(user))
                        },
                )
                Column(
                    modifier = Modifier.padding(start = 4.dp), verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    AppText(text = user.nickname, color = Color(user.nameColor))
                    MessageTextFlow(messageTextSpan = textSpan)
                    content()
                    MessageReminder(entry, Modifier)
                }
            }
        }
    }

    private fun buildUserSpan(
        context: Context,
        density: Density,
        user: User,
        role: Int = ChatGroup.Role.MEMBER,
        builder: (AnnotatedString.Builder).(TextSpan.Builder) -> Unit = {},
    ): TextSpan {
        val paddingValues = PaddingValues(start = 4.dp, top = 2.dp, bottom = 2.dp)
        val list = buildList {
            if (role != ChatGroup.Role.MEMBER) {
                val roleText =
                    context.getString(if (role == ChatGroup.Role.ADMIN) R.string.group_manager else R.string.group_owner)
                add(inlineTextContent(key = roleText, alternateText = roleText, paddingValues = paddingValues) { modifier ->
                    AppText(
                        text = roleText, color = Color.White, fontSize = 12.sp,
                        modifier = modifier
                            .background(
                                color = Color(if (role.isAdmin()) 0xFFFFAF15 else 0xFFFF385C),
                                shape = RoundedCornerShape(50)
                            )
                            .padding(8.dp, 2.dp)
                    )
                })
            }

            add(inlineTextContent(key = "level", paddingValues = paddingValues) { modifier ->
                LevelBadge(level = user.level, modifier = modifier)
            })

            user.publicCP?.also {
                add(inlineTextContent(
                    key = "publish_cp", density = density, width = 64, height = 18, paddingValues = paddingValues
                ) { modifier ->
                    PublishCp(
                        avatar = it.avatarUrl, bgUrl = it.cpExtraInfo?.levelInfo?.smallImgUrl, modifier = modifier
                    )
                })
            }

            user.medalList.forEachIndexed { index, medal ->
                add(inlineTextContent(
                    key = "medal_$index",
                    density = density,
                    width = medal.width,
                    height = medal.height,
                    paddingValues = paddingValues
                ) { modifier ->
                    ComposeImage(
                        model = medal.icon, modifier = modifier
                    )
                })
            }

            if (user.isVip) {
                add(inlineTextContent(
                    key = "vip", density = density, width = 39, height = 20, paddingValues = paddingValues
                ) { modifier ->
                    Image(
                        painter = painterResource(id = R.drawable.ic_vip_tag), contentDescription = "vip", modifier = modifier
                    )
                })
            }

        }
        return buildTextSpan {
            it.appendInlineContentList(list)
            builder(it)
        }
    }

}