package com.qyqy.ucoo.compose.presentation.chatgroup.page

import android.content.Context
import androidx.compose.ui.unit.Density
import com.qyqy.ucoo.AppUserPartition
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroup
import com.qyqy.ucoo.compose.presentation.room.FamilGiftMessageItem
import com.qyqy.ucoo.compose.presentation.room.FamilyCreatedMessageItem
import com.qyqy.ucoo.compose.presentation.room.FamilyEventMessageItem
import com.qyqy.ucoo.compose.presentation.room.GiftMessageItem
import com.qyqy.ucoo.compose.presentation.room.MessageItem
import com.qyqy.ucoo.compose.vm.room.UIAction
import com.qyqy.ucoo.im.compat.ConversationType
import com.qyqy.ucoo.im.compat.SendParams
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.UCEmojiMessage
import com.qyqy.ucoo.im.compat.UCGiftMessage
import com.qyqy.ucoo.im.compat.UCImageMessage
import com.qyqy.ucoo.im.compat.UCInstanceMessage
import com.qyqy.ucoo.im.compat.UCTextMessage
import com.qyqy.ucoo.im.compat.UCVoiceMessage
import com.qyqy.ucoo.im.compat.chat.IMMessageConfig
import com.qyqy.ucoo.im.compat.chat.ListStateMessageViewModel
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.im.message.MsgEventCmd
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.receiveAsFlow

class ChatGroupViewModel(
    private val rcGroupId: String,
    hasAddNewMsgTag: Boolean = false,
    fissionGiftMessageEnable: Boolean = false
) : ListStateMessageViewModel(
    sendParams = SendParams(receiver = rcGroupId, type = ConversationType.GROUP),
    config = IMMessageConfig(hasLoadUnreadMsgInfo = true, hasAddNewMsgTag = hasAddNewMsgTag, fissionGiftMessageEnable = fissionGiftMessageEnable)
) {

    companion object {

        fun mapToMessageItem(
            context: Context,
            density: Density,
            entry: MessageEntry<UCInstanceMessage>,
            effect: Channel<UIAction>? = null,
        ): MessageItem {
            val message = entry.message
            //家族事件
            if (AppUserPartition.isCupid) {
                if (message is UCGiftMessage) {
                    return FamilGiftMessageItem(context, entry as UIMessageEntry<UCGiftMessage>)
                } else if (message is UCCustomMessage) {
                    entry as UIMessageEntry<UCCustomMessage>
                    return if (message.cmd == MsgEventCmd.TRIBE_CUSTOM_CONTENT) {
                        FamilyCreatedMessageItem(entry)
                    } else {
                        FamilyEventMessageItem(context, entry)
                    }
                }
            }
            if (message is UCGiftMessage) {
                //fixed 2.40.3 写成this as UIMessageEntry 导致华语区群组消息崩溃
                return GiftMessageItem.from(context, entry as UIMessageEntry<UCGiftMessage>)
            }
            return MessageItem.valueOfUiMessage(context, density, entry, effect)
        }
    }

    val _effect: Channel<UIAction> = Channel()

    val effect = _effect.receiveAsFlow()

    override fun filterShownMessage(message: UCInstanceMessage): Boolean {
        return when (message) {
            is UCTextMessage, is UCImageMessage, is UCVoiceMessage, is UCGiftMessage -> true
            is UCEmojiMessage -> !AppUserPartition.isCupid
            is UCCustomMessage -> {
                if (AppUserPartition.isCupid) {
                    when (message.cmd) {
                        MsgEventCmd.MEMBER_JOIN,
                        MsgEventCmd.MEMBER_QUIT,
                        MsgEventCmd.MEMBER_KICKED_OUT,
                        MsgEventCmd.INVITE_TO_ROOM,
                        MsgEventCmd.NAME_UPDATE,
                        MsgEventCmd.BULLETIN_UPDATE,
                        MsgEventCmd.MEMBER_GET_REWARD,
                        MsgEventCmd.MEMBER_SIGN_IN,
                        MsgEventCmd.TRIBE_CUSTOM_CONTENT,
                            -> true

                        else -> false
                    }
                } else {
                    when (message.cmd) {
                        MsgEventCmd.TRIBE_CUSTOM_CONTENT -> true
                        MsgEventCmd.USER_ENTRANCE -> true
                        MsgEventCmd.INVITE_TO_ROOM -> true
                        MsgEventCmd.COMMON_CHATROOM_PUBLIC_MESSAGES -> false
                        MsgEventCmd.GAME_MATCH_INVITE -> true
                        ChatGroup.Events.CHATGROUP_CREATED,
                        ChatGroup.Events.CHATGROUP_MEMBER_JOIN,
                        ChatGroup.Events.CHATGROUP_MEMBER_QUIT,
                        ChatGroup.Events.CHATGROUP_MEMBER_KICKED_OUT,
                            -> true

                        else -> false
                    }
                }
            }

            else -> false
        }
    }
}