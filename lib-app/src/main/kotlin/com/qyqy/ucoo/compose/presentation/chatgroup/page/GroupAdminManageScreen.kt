package com.qyqy.ucoo.compose.presentation.chatgroup.page

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.DialogProperties
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroup
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroupMember
import com.qyqy.ucoo.compose.presentation.chatgroup.list.ChatGroupMemberListViewModel
import com.qyqy.ucoo.compose.presentation.chatgroup.list.ChatGroupOnlyMemberListViewModel
import com.qyqy.ucoo.compose.state.StateListView
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.Loading
import com.qyqy.ucoo.http.gson
import com.qyqy.ucoo.toastRes

/**
 *  @time 2024/6/25
 *  <AUTHOR>
 *  @package com.qyqy.ucoo.compose.presentation.chatgroup.page
 *  几个管理员才可以看到的页面: 管理员列表/设置取消管理/踢出成员
 */

/**
 * 群组管理员列表页面
 *
 * @param viewModel 配合StateList使用的Viewmodel
 * @param onAction 点击事件,有上层决定
 */
@Composable
fun GroupAdminManageScreen(
    viewModel: ChatGroupMemberListViewModel,
    onAction: (UIAction) -> Unit = {}
) {
    val onBackPressedDispatcher = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher
    LaunchedEffect(key1 = Unit) {
        viewModel.refresh()
    }
    Column(modifier = Modifier.systemBarsPadding()) {
        AppTitleBar(title = stringResource(R.string.group_admin_list_title), onBack = { onBackPressedDispatcher?.onBackPressed() })
        Box {
            StateListView(viewModel = viewModel, keyProvider = { _, member -> member.user.id }) { member, _, _, _ ->
                GroupAdminManagerItem(member = member, onAction)
            }
            Column(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 35.dp, start = 20.dp, end = 20.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Box(modifier = Modifier
                    .background(
                        Color(0xff9264f4),
                        RoundedCornerShape(50)
                    )
                    .fillMaxWidth()
                    .heightIn(56.dp)
                    .clickable {
                        if (viewModel.listFlow.value.size >= 3) {
                            toastRes(R.string.group_admin_tips)
                        } else {
                            onAction(UIAction.GoSetAdmin)
                        }
                    }) {
                    AppText(
                        text = stringResource(R.string.group_admin_add), color = Color.White, modifier = Modifier.align(Alignment.Center)
                    )
                }
                Spacer(modifier = Modifier.height(17.dp))
                Text(stringResource(id = R.string.group_admin_tips), color = Color.White)
            }
        }
    }
}

/**
 * 群组管理员添加和删除页面
 *
 * @param viewModel 配合StateList使用的Viewmodel
 * @param onAction 点击事件,向上层传递
 */
@Composable
fun GroupAdminSetScreen(
    viewModel: ChatGroupMemberListViewModel,
    onAction: (UIAction) -> Unit = {}
) {
    val onBackPressedDispatcher = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher
    LaunchedEffect(key1 = Unit) {
        viewModel.refresh()
    }
    Column(modifier = Modifier.systemBarsPadding()) {
        AppTitleBar(title = stringResource(R.string.group_admin_list_title), onBack = { onBackPressedDispatcher?.onBackPressed() })
        StateListView(viewModel = viewModel, keyProvider = { _, member -> member.user.id }) { member, _, _, _ ->
            if ((member.role == ChatGroup.Role.ADMIN) || (member.role == ChatGroup.Role.OWNER)) {

            } else {
                GroupAdminManagerItem(member = member, onAction)
            }
        }
    }
}

/**
 * 群组管理员列表相关
 *
 * @param member 群组成员
 * @param onAction 点击事件
 */

@Composable
fun GroupAdminManagerItem(member: ChatGroupMember, onAction: (UIAction) -> Unit = {}) {
    Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.padding(vertical = 10.dp)) {
        Spacer(modifier = Modifier.width(20.dp))
        CircleComposeImage(model = member.user.avatarUrl, modifier = Modifier.size(56.dp))
        Spacer(modifier = Modifier.width(12.dp))
        Text(
            member.user.nickname, color = Color.White, modifier = Modifier.weight(1f), fontSize = 16.sp,
        )

        Spacer(modifier = Modifier.width(12.dp))
        Box(
            modifier = Modifier
                .background(
                    Color(0xff383838),
                    RoundedCornerShape(50)
                )
                .padding(20.dp, 9.dp)
                .clickable {
                    onAction(
                        UIAction.ApplyManager(
                            member.member_id, member.role != ChatGroup.Role.ADMIN
                        )
                    )
                }
        ) {
            AppText(
                text = if (member.role == ChatGroup.Role.ADMIN)
                    stringResource(R.string.group_admin_remove)
                else stringResource(id = R.string.group_admin_add), color = Color.White
            )
        }
        Spacer(modifier = Modifier.width(20.dp))
    }
}


@Composable
fun GroupRemoveListItem(member: ChatGroupMember, onAction: (UIAction) -> Unit = {}) {
    Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.padding(vertical = 10.dp)) {
        Spacer(modifier = Modifier.width(20.dp))
        CircleComposeImage(model = member.user.avatarUrl, modifier = Modifier.size(56.dp))
        Spacer(modifier = Modifier.width(12.dp))
        Text(
            member.user.nickname, color = Color.White, modifier = Modifier.weight(1f), fontSize = 16.sp,
        )

        Spacer(modifier = Modifier.width(12.dp))
        Box(
            modifier = Modifier
                .background(
                    Color(0xff383838),
                    RoundedCornerShape(50)
                )
                .padding(20.dp, 9.dp)
                .clickable {
                    onAction(
                        UIAction.RemoveMember(member.member_id)
                    )
                }
        ) {
            AppText(
                text = stringResource(R.string.group_remove_member), color = Color.White
            )
        }
        Spacer(modifier = Modifier.width(20.dp))
    }
}


@Composable
fun GroupRemoveMemberScreen(
    viewModel: ChatGroupMemberListViewModel,
    isLoading: Boolean,
    onAction: (UIAction) -> Unit
) {
    val onBackPressedDispatcher = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher
    LaunchedEffect(key1 = Unit) {
        viewModel.refresh()
    }
    Box() {
        Column(modifier = Modifier.systemBarsPadding()) {
            AppTitleBar(title = stringResource(R.string.group_remove_member_title), onBack = { onBackPressedDispatcher?.onBackPressed() })
            Box {
                StateListView(viewModel = viewModel, keyProvider = { _, member -> member.user.id }) { member, _, _, _ ->
                    GroupRemoveListItem(member = member, onAction)
                }
            }
        }
        if (isLoading) {
            Loading(
                properties = DialogProperties(
                    false, false
                )
            )
        }
    }
}

@Composable
@Preview(showBackground = true)
private fun AdminManagerScreenPreView() {
    val sampleJson =
        "{\"member_id\":43,\"role\":10,\"is_online\":true,\"user\":{\"userid\":1430,\"public_id\":\"101882\",\"nickname\":\"兔兔子\",\"avatar_url\":\"https://s.test.ucoofun.com/aacd0W?x-oss-process=image/format,webp\",\"gender\":2,\"age\":24,\"height\":170,\"avatar_frame\":\"https://s.ucoofun.com/aabLdz\",\"medal\":{\"icon\":\"https://s.ucoofun.com/aabOPn\",\"width\":72,\"height\":24},\"medal_list\":[{\"icon\":\"https://s.ucoofun.com/aabOPn\",\"width\":72,\"height\":24}],\"level\":44,\"country_flag\":\"https://media.ucoofun.com/opsite%2Fcountryflag%2FL_slices%2FCN.png\"}}"
    val memberList = mutableListOf<ChatGroupMember>()
    for (i in 0..20) {
        memberList.add(gson.fromJson(sampleJson, ChatGroupMember::class.java))
    }
    Column {
        AppTitleBar(title = "管理员设置")
        Box {
            LazyColumn {
                items(memberList) {
                    GroupAdminManagerItem(member = it)
                }
            }
        }
    }
}