package com.qyqy.ucoo.compose.presentation.chatgroup.page

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppText

@Composable
fun GroupCreatedCard(onEditProfile: () -> Unit = {}, onSetAdmin: () -> Unit = {}) {
    Surface(color = Color.Transparent, contentColor = Color.White) {
        Column(
            modifier = Modifier
                .padding(34.dp, 8.dp)
                .background(Brush.horizontalGradient(listOf(Color(0xFF315062), Color(0xFF3F3361))), RoundedCornerShape(6.dp))
                .border(0.5.dp, Color(0xFF5ECFFF), RoundedCornerShape(6.dp))
                .padding(24.dp, 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            AppText(text = stringResource(id = R.string.title_group_created), textAlign = TextAlign.Center)
            Spacer(modifier = Modifier.height(16.dp))
            val m = Modifier
                .background(Color(0xFF945EFF), RoundedCornerShape(50.dp))
            AppButton(
                modifier = m,
                paddingValues = PaddingValues(12.dp, 7.dp),
                text = stringResource(id = R.string.edit_group_intro),
                onClick = onEditProfile
            )
            Spacer(modifier = Modifier.height(12.dp))
            AppButton(
                modifier = m,
                paddingValues = PaddingValues(12.dp, 7.dp),
                text = stringResource(id = R.string.set_group_admin),
                onClick = onSetAdmin
            )
        }
    }
}

@Preview
@Composable
fun GroupCreatedCardPreview() {
    GroupCreatedCard()
}