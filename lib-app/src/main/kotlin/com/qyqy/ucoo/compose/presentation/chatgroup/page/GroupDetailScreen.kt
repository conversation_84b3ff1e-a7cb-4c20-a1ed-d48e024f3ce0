package com.qyqy.ucoo.compose.presentation.chatgroup.page

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.rememberScrollableState
import androidx.compose.foundation.gestures.scrollable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.absoluteOffset
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyGridScope
import androidx.compose.foundation.lazy.grid.LazyGridState
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.ArrowBack
import androidx.compose.material3.Button
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.TextUnitType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.constraintlayout.compose.ConstraintSet
import androidx.constraintlayout.compose.Dimension
import androidx.constraintlayout.compose.MotionLayout
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroup
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroupDetail
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroupMember
import com.qyqy.ucoo.compose.presentation.chatgroup.data.JoinGroupRequest
import com.qyqy.ucoo.compose.presentation.chatgroup.list.ChatGroupMemberListViewModel
import com.qyqy.ucoo.compose.presentation.chatgroup.list.ChatGroupRequestListViewModel
import com.qyqy.ucoo.compose.router.LocalNavigationViewModelOwner
import com.qyqy.ucoo.compose.state.StateGridWidget
import com.qyqy.ucoo.compose.state.StateLayoutDefaults
import com.qyqy.ucoo.compose.state.StateListView
import com.qyqy.ucoo.compose.state.StateValue
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.Loading
import com.qyqy.ucoo.compose.ui.verticalScrollWithScrollbar
import com.qyqy.ucoo.home.main.MySwitch
import com.qyqy.ucoo.http.gson

/**
 *  @time 2024/6/20
 *  <AUTHOR>
 *  @package com.qyqy.ucoo.compose.presentation.group
 */


//region unused

//region MotionLayout 实验性代码

/**
 * 实验性代码, 如果追求效果的话可以使用MotionLayout对顶部布局进行折叠和展开
 */

@Composable
fun CollapsingHeader(
    progress: Float, backClick: () -> Unit, scrollableBody: @Composable () -> Unit
) {
    MotionLayout(start = collapsingConstrainSetStart(), end = collapsingConstrainSetEnd(), progress = progress) {
        //顶部
        Image(
            painter = painterResource(id = R.drawable.header_home),
            contentDescription = "group info header img",
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .height(200.dp)
                .layoutId("groupBg")
        )

        //群组介绍
        Box(
            modifier = Modifier
                .layoutId("info")
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier
                    .clip(
                        RoundedCornerShape(
                            topStart = 24.dp, topEnd = 24.dp
                        )
                    )
                    .fillMaxWidth()
                    .padding(top = 64.dp)
            ) {

                Text(
                    "", color = Color.White,
                    fontSize = 12.sp,
                )

                ExpandableText(
                    text = "",
                    style = SpanStyle(color = Color(0x80FFFFFF))
                    // Composable 大小的动画效果
                    , modifier = Modifier
                        .padding(top = 16.dp, bottom = 24.dp, start = 32.dp, end = 32.dp)
                        .animateContentSize(),
                    collapsedMaxLine = 5
                )
            }
            CircleComposeImage(
                model = "", modifier = Modifier
                    .size(96.dp)
                    .align(Alignment.TopCenter)
                    .absoluteOffset(y = -48.dp)
            )
        }
        Spacer(
            modifier = Modifier
                .height(12.dp)
                .layoutId("divider")
        )
        IconButton(onClick = { }, modifier = Modifier.layoutId("backBtn")) {
            Icon(imageVector = Icons.AutoMirrored.Rounded.ArrowBack, contentDescription = "")
        }

        Box(modifier = Modifier.layoutId("content")) {
            scrollableBody()
            Text("${progress}", color = Color.White)
        }
    }
}

@Composable
private fun collapsingConstrainSetStart() = ConstraintSet {
    val groupBg = createRefFor("groupBg")//背景图
    val backBtn = createRefFor("backBtn")//返回按钮
    val info = createRefFor("info")//信息
    val content = createRefFor("content")//底部人员
    val divider = createRefFor("divider")//底部人员

    //返回按钮始终在左上角
    this.constrain(backBtn) {
        start.linkTo(parent.start)
        top.linkTo(parent.top)
    }

    this.constrain(groupBg) {
        width = Dimension.matchParent
        start.linkTo(parent.start)
        end.linkTo(parent.end)
        top.linkTo(parent.top)
    }
    this.constrain(info) {
        width = Dimension.matchParent
        start.linkTo(parent.start)
        end.linkTo(parent.end)
        top.linkTo(groupBg.bottom)
    }
    this.constrain(divider) {
        width = Dimension.matchParent
        start.linkTo(parent.start)
        end.linkTo(parent.end)
        top.linkTo(info.bottom)
    }
    this.constrain(content) {
        top.linkTo(divider.bottom, margin = 12.dp)
        start.linkTo(parent.start)
        end.linkTo(parent.end)
        width = Dimension.matchParent
    }
}

@Composable
private fun collapsingConstrainSetEnd() = ConstraintSet {
    val groupBg = createRefFor("groupBg")
    val backBtn = createRefFor("backBtn")
    val info = createRefFor("info")
    val content = createRefFor("content")
    val divider = createRefFor("divider")//底部人员

    this.constrain(groupBg) {
        width = Dimension.matchParent
        start.linkTo(parent.start)
        end.linkTo(parent.end)
        top.linkTo(parent.top)
    }
    this.constrain(backBtn) {
        start.linkTo(parent.start)
        top.linkTo(parent.top)
    }
    this.constrain(info) {
        width = Dimension.value(0.dp)
        height = Dimension.value(56.dp)
        start.linkTo(parent.start)
        end.linkTo(parent.end)
        start.linkTo(parent.start)
    }
    this.constrain(divider) {
        width = Dimension.matchParent
        start.linkTo(parent.start)
        end.linkTo(parent.end)
        top.linkTo(info.bottom)
    }
    this.constrain(content) {
        top.linkTo(backBtn.bottom, margin = 8.dp)
        start.linkTo(parent.start)
        end.linkTo(parent.end)
        width = Dimension.matchParent
    }
}

//endregion



@Composable
fun GroupDetailPage3(members: List<Int>) {
    val gridState = rememberLazyGridState()
    Box {
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
            //顶部
            Image(
                painter = painterResource(id = R.drawable.header_nobody),
                contentDescription = "group info header img",
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .height(200.dp)
                    .fillMaxWidth()
            )

            //群组介绍
            Box() {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier
                        .clip(
                            RoundedCornerShape(
                                topStart = 24.dp, topEnd = 24.dp
                            )
                        )
                        .fillMaxWidth()
                        .padding(top = 64.dp)
                ) {

                    Text(
                        "ガールフレンドを探しています", color = Color.White,
                        fontSize = 12.sp,
                    )

                    ExpandableText(
                        text = "皆さん、こんにちは！ このグループチャットへようこそ。ここは気軽に話し合い、情報を共有し、お互いをサポートするためす。以下にいくつかのルールを記載していますので、参加する前に一読ください。リスペクトを持つこと...皆さん、こんにちは！ このグループチャットへようこそ。ここは気軽に話し合い、情報を共有し、お互いをサポートするための場所です。以下にいくつかのルールを記載していますので、参加する前に一読ください。リスペクトを持つこと...",
                        style = SpanStyle(color = Color(0x80FFFFFF))
                        // Composable 大小的动画效果
                        , modifier = Modifier
                            .padding(top = 16.dp, bottom = 24.dp, start = 32.dp, end = 32.dp)
                            .animateContentSize(),
                        collapsedMaxLine = 5
                    )
                }
                CircleComposeImage(
                    model = "", modifier = Modifier
                        .size(96.dp)
                        .align(Alignment.TopCenter)
                        .absoluteOffset(y = -48.dp)
                )
            }
            Spacer(modifier = Modifier.height(12.dp))
            //群组成员
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp, 24.dp)
            ) {
                Text(
                    "グループメンバー",
                    color = Color.White,
                    modifier = Modifier.weight(1f)
                )
                Text(
                    "27/200"

                )
            }

            LazyVerticalGrid(
                columns = GridCells.Fixed(5),
                state = gridState,
                modifier = Modifier
                    .fillMaxWidth(1f)
            ) {
                itemsIndexed(members, key = { idx, r -> idx }) { idx, member ->
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        //头像
                        Box {
                            CircleComposeImage(
                                model = "leftUrl", modifier = Modifier
                                    .size(56.dp)
                            )
                            if (member < 4) {
                                Box(
                                    modifier = Modifier
                                        .align(Alignment.BottomCenter)
                                        .clip(RoundedCornerShape(24.dp))
                                        .background(Color(if (member == 0) 0xFFFF385C else 0xFFFFAF15))
                                ) {
                                    Text(
                                        "オーナー", modifier = Modifier
                                            .padding(6.dp, 4.dp),
                                        fontSize = 10.sp,
                                        color = Color.White
                                    )
                                }
                            }
                        }
                        //名称
                        Text(
                            "あすか",
                            lineHeight = TextUnit(12f, TextUnitType.Unspecified),
                            fontSize = 12.sp,
                            color = Color.White,
                            modifier = Modifier.padding(top = 8.dp)
                        )
                    }
                }
            }
        }

        //底部按钮
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .absoluteOffset(y = -20.dp)
        ) {
            Button(
                {}, modifier = Modifier
                    .widthIn(220.dp)
                    .heightIn(44.dp)

            ) {
                Text(
                    text = "作成を確認", fontSize = 16.sp,
                )
            }
            Text(
                text = "会员特典",
                color = Color(0xFFFFD69C),
                fontSize = 9.sp,
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .clip(RoundedCornerShape(12.dp, 12.dp, 12.dp, 0.dp))
                    .background(
                        brush = Brush.horizontalGradient( // 设置渐变色
                            listOf(
                                Color(0xFF464646),
                                Color(0xFF222222),
                            )
                        )
                    )
                    .padding(6.dp, 4.dp)
            )
        }

        IconButton(onClick = { }) {
            Icon(imageVector = Icons.AutoMirrored.Rounded.ArrowBack, contentDescription = "")
        }
    }
}


@Composable
fun GroupDetailPage2(members: List<Int>) {
//    var animateToEnd by remember {
//        mutableStateOf(false)
//    }
//    val progress by animateFloatAsState(
//        targetValue = if (animateToEnd) 1f else 0f,
//        animationSpec = tween(800)
//    )
//    var progress by remember { mutableStateOf(0f) }
    var gridState = rememberLazyGridState()
    val pos = gridState.firstVisibleItemScrollOffset

    CollapsingHeader(progress = if (pos > 100) 1f else (pos / 100f), backClick = { }) {
        LazyVerticalGrid(
            state = gridState,
            columns = GridCells.Fixed(5),
            modifier = Modifier
                .fillMaxWidth(1f)
        ) {
            itemsIndexed(members, key = { idx, r -> idx }) { idx, member ->
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    //头像
                    Box {
                        CircleComposeImage(
                            model = "leftUrl", modifier = Modifier
                                .size(56.dp)
                        )
                        if (member < 4) {
                            Box(
                                modifier = Modifier
                                    .align(Alignment.BottomCenter)
                                    .clip(RoundedCornerShape(24.dp))
                                    .background(Color(if (member == 0) 0xFFFF385C else 0xFFFFAF15))
                            ) {
                                Text(
                                    "オーナー", modifier = Modifier
                                        .padding(6.dp, 4.dp),
                                    fontSize = 10.sp,
                                    color = Color.White
                                )
                            }
                        }
                    }
                    //名称
                    Text(
                        "あすか",
                        lineHeight = TextUnit(12f, TextUnitType.Unspecified),
                        fontSize = 12.sp,
                        color = Color.White,
                        modifier = Modifier.padding(top = 8.dp)
                    )
                }
            }
        }
//        Slider(value = progress, onValueChange = { progress = it })
    }
}


//endregion

//region 群组详情页面

/**
 * 可收缩的Text文本,会自动根据文本来进行裁剪
 */
@Composable
fun ExpandableText(
    text: String,
    style: SpanStyle,
    showAllStyle: SpanStyle = SpanStyle(
        fontWeight = FontWeight.Bold,
        color = Color(0xff945eff),
    ),
    expandText: Pair<String, String> = ("..." + stringResource(id = R.string.group_info_expand)) to stringResource(id = R.string.group_info_collaspe),
    modifier: Modifier = Modifier,
    collapsedMaxLine: Int = 3,
    onClick: (() -> Unit)? = null
) {
    var isExpanded by remember(text) { mutableStateOf(false) }
    var expandable by remember(text) { mutableStateOf(false) }
    var lastVisibleCharIndex by remember(text) { mutableIntStateOf(0) }

    val (showMoreText, showLessText) = expandText
    val toggleTextStyle = showAllStyle

    val annotatedText = buildAnnotatedString {
        if (expandable) {
            if (isExpanded) {
                withStyle(style) {
                    append(text)
                }

                pushStringAnnotation("TAG_TOGGLE", "ExpandableText")
                withStyle(style = toggleTextStyle) { append(showLessText) }
                pop()
            } else {
                val textWithToggleSpace = text.substring(startIndex = 0, endIndex = lastVisibleCharIndex)
                    .dropLast(showMoreText.length)
                withStyle(style) {
                    append(textWithToggleSpace)
                }

                pushStringAnnotation("TAG_TOGGLE", "ExpandableText")
                withStyle(style = toggleTextStyle) { append(showMoreText) }
                pop()
            }
        } else {
            withStyle(style) {
                append(text)
            }
        }
    }

    Column(
        modifier = Modifier.then(modifier),
    ) {
        val layoutResult = remember { mutableStateOf<TextLayoutResult?>(null) }
        val pressIndicator = Modifier.pointerInput(onClick) {
            detectTapGestures { pos ->
                layoutResult.value?.let { layoutResult ->
                    val offset = (layoutResult.getOffsetForPosition(pos))
                    val annotations = annotatedText.getStringAnnotations("TAG_TOGGLE", offset, offset)
                    annotations.firstOrNull()?.let {
                        if (onClick == null) {
                            isExpanded = !isExpanded
                        } else {
                            onClick()
                        }
                    }
                }
            }
        }

        BasicText(
            modifier = Modifier
                .fillMaxWidth()
                .animateContentSize().then(pressIndicator),
            text = annotatedText,
            maxLines = if (isExpanded) Int.MAX_VALUE else collapsedMaxLine,
            onTextLayout = {
                layoutResult.value = it
                if (!isExpanded && it.hasVisualOverflow) {
                    expandable = true
                    lastVisibleCharIndex = it.getLineEnd(collapsedMaxLine - 1)
                }
            }
        )
    }
}

/**
 * 群组详情页面
 */

@Composable
fun GroupDetailPage(
    info: ChatGroupDetail,
    isLoading: Boolean,
    onAction: (UIAction) -> Unit = { }
) {
    val scrollState = rememberLazyGridState()
    val titleAlpha = remember(scrollState) {
        derivedStateOf {
            if (scrollState.firstVisibleItemIndex == 0) {
                val offsetY = scrollState.firstVisibleItemScrollOffset
                if (offsetY < 200f) 0f else if (offsetY < 600f) offsetY / 600f else 1f
            } else {
                1f
            }
        }
    }
    val descDialogShow = remember {
        mutableStateOf(false)
    }

    val firstLoad = remember {
        mutableStateOf(true)
    }
    val onBackPressedDispatcher = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher

    val vm = viewModel(modelClass = ChatGroupMemberListViewModel::class.java, LocalNavigationViewModelOwner.current)
    LaunchedEffect(key1 = vm) {
        vm.refresh()
    }
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(color = Color(0xFF272829))
    ) {

        Box(modifier = Modifier.fillMaxSize()) {
            StateGridWidget(vm, spanCount = 5, bottomLoading = { viewModel ->
                Column {
                    StateLayoutDefaults.LoadMoreIndicator(viewModel)
                    Spacer(modifier = Modifier.height(56.dp))
                }
            }) { members ->
                item(span = { GridItemSpan(5) }) {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                    ) {
                        Box() {
                            //顶部
                            ComposeImage(
                                model = info.avatarUrl, contentDescription = "group info header img",
                                contentScale = ContentScale.FillWidth,
                                modifier = Modifier
                                    .height(200.dp)
                                    .fillMaxWidth()
                            )
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier
                                    .fillMaxWidth()
                            ) {
                                Spacer(modifier = Modifier.height(176.dp))

                                Column(
                                    horizontalAlignment = Alignment.CenterHorizontally,
                                    modifier = Modifier
                                        .background(color = Color(0xff28272a), shape = RoundedCornerShape(24.dp))
                                        .padding(top = 64.dp)
                                ) {
                                    Text(
                                        info.name, color = Color.White,
                                        fontSize = 12.sp,
                                    )

                                    ExpandableText(
                                        text = info.intro.ifEmpty { stringResource(R.string.group_no_description) },
                                        style = SpanStyle(color = Color(0x80FFFFFF))
                                        // Composable 大小的动画效果
                                        , modifier = Modifier
                                            .padding(top = 16.dp, start = 32.dp, end = 32.dp)
                                            .animateContentSize(),
                                        collapsedMaxLine = 5
                                    ) {
                                        descDialogShow.value = !descDialogShow.value
                                    }


                                    when (info.role) {
                                        ChatGroup.Role.NONE -> {//未加入
                                            Spacer(modifier = Modifier.height(24.dp))
                                        }

                                        ChatGroup.Role.OWNER, ChatGroup.Role.ADMIN -> {//群主
                                            Row(
                                                horizontalArrangement = Arrangement.SpaceBetween, verticalAlignment = Alignment.CenterVertically, modifier = Modifier
                                                    .fillMaxWidth()
                                                    .height(64.dp)
                                                    .padding(horizontal = 16.dp)
                                                    .clickable {
                                                        onAction(UIAction.GoSettings)
                                                        //                                        action("goGroupSettings", null)
                                                    }
                                            ) {
                                                Text(stringResource(R.string.group_detail_gosettings), fontSize = 16.sp, color = Color.White)
                                                Image(painter = painterResource(id = R.drawable.ic_arrow_right), contentDescription = "right arrow")
                                            }
                                            Spacer(
                                                modifier = Modifier
                                                    .fillMaxWidth()
                                                    .height(1.dp)
                                                    .padding(horizontal = 16.dp)
                                                    .background(color = Color(0x14FFFFFF))
                                            )
                                        }

                                        else -> {

                                        }
                                    }

                                    if (info.role >= 0) {//已经加入,有个静音通知开关
                                        Row(
                                            horizontalArrangement = Arrangement.SpaceBetween, verticalAlignment = Alignment.CenterVertically, modifier = Modifier
                                                .fillMaxWidth()
                                                .height(64.dp)
                                                .padding(horizontal = 16.dp)
                                        ) {
                                            Text(stringResource(R.string.group_detail_mute), fontSize = 16.sp, color = Color.White)
                                            MySwitch(active = info.iEnableDontDisturb, onClick = {
                                                onAction(UIAction.SwitchNotify)
                                            })
                                        }
                                    }

                                    Spacer(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .height(8.dp)
                                            .background(color = Color(0x0DFFFFFF))
                                    )
                                    //群组成员信息title
                                    Row(
                                        horizontalArrangement = Arrangement.Center,
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(16.dp, 24.dp)
                                    ) {
                                        Text(
                                            stringResource(id = R.string.group_member),
                                            color = Color.White,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            "${info.onlineMemberCnt}/${info.memberCnt}",
                                            color = Color(0xff8c8c8d)
                                        )


                                    }
                                }
                            }
                            //群组介绍
                            Column(modifier = Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
                                Spacer(modifier = Modifier.height(128.dp))
                                CircleComposeImage(
                                    model = info.avatarUrl, modifier = Modifier
                                        .size(96.dp)
                                )
                            }

                        }
                    }
                }
                itemsIndexed(members, key = { idx, r -> idx }) { idx, member ->
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier
                            .padding(
                                top = if (idx < 5) 0.dp else 24.dp,
                                start = if (idx % 5 == 0) 4.dp else 0.dp,
                                end = if (idx % 5 == 4) 4.dp else 0.dp,
                            )
                            .clickable {
                                onAction(UIAction.GoUserProfile(member.user))
                            }
                    ) {
                        //头像
                        Box {
                            CircleComposeImage(
                                model = member.user.avatarUrl, modifier = Modifier
                                    .size(56.dp)
                            )
                            if (member.role == ChatGroup.Role.ADMIN || member.role == ChatGroup.Role.OWNER) {
                                Box(
                                    modifier = Modifier
                                        .align(Alignment.BottomCenter)
                                        .clip(RoundedCornerShape(24.dp))
                                        .background(Color(if (member.role == ChatGroup.Role.OWNER) 0xFFFF385C else 0xFFFFAF15))
                                ) {
                                    Text(
                                        if (member.role == ChatGroup.Role.OWNER) stringResource(id = R.string.group_owner)
                                        else stringResource(id = R.string.group_manager),
                                        modifier = Modifier
                                            .padding(6.dp, 3.dp),
                                        fontSize = 10.sp,
                                        color = Color.White
                                    )
                                }
                            }
                        }
                        //名称
                        Text(
                            member.user.nickname,
                            lineHeight = TextUnit(12f, TextUnitType.Unspecified),
                            fontSize = 12.sp,
                            maxLines = 1,
                            color = Color.White,
                            textAlign = TextAlign.Center,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier
                                .padding(top = 8.dp)
                                .width(64.dp)
                        )
                    }
                }
            }

            //底部按钮
            if (!firstLoad.value || vm.contentState.current == StateValue.Success) {
                firstLoad.value = false
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .absoluteOffset(y = -20.dp)
                ) {
                    if (info.role == ChatGroup.Role.NONE) {
                        if (info.relationWithMe == ChatGroup.Relation.APPLYING) {
                            Button(
                                {}, modifier = Modifier
                                    .widthIn(220.dp)
                                    .heightIn(44.dp)
                            ) {
                                Text(
                                    text = stringResource(id = R.string.申请中), fontSize = 16.sp,
                                )
                            }
                        } else {
                            Button(
                                {
//                            action("joinGroup", null)
                                    onAction(UIAction.Join)
                                }, modifier = Modifier
                                    .widthIn(220.dp)
                                    .heightIn(44.dp)
                            ) {
                                Text(
                                    text = stringResource(id = R.string.group_join_group), fontSize = 16.sp,
                                )
                            }
//                            Text(
//                                text = stringResource(id = R.string.group_vip_benefits),
//                                color = Color(0xFFFFD69C),
//                                fontSize = 9.sp,
//                                modifier = Modifier
//                                    .align(Alignment.TopEnd)
//                                    .clip(RoundedCornerShape(12.dp, 12.dp, 12.dp, 0.dp))
//                                    .background(
//                                        brush = Brush.horizontalGradient( // 设置渐变色
//                                            listOf(
//                                                Color(0xFF464646),
//                                                Color(0xFF222222),
//                                            )
//                                        )
//                                    )
//                                    .padding(6.dp, 4.dp)
//                            )
                        }
                    } else if (info.role == ChatGroup.Role.OWNER) {
                        Button(
                            {
//                            action("removeGroup", null)
                                onAction(UIAction.Disband)
                            }, modifier = Modifier
                                .widthIn(220.dp)
                                .heightIn(44.dp)
                        ) {
                            Text(
                                text = stringResource(id = R.string.group_remove_group), fontSize = 16.sp,
                            )
                        }
                    } else {
                        Button(
                            {
//                            action("exitGroup", null)
                                onAction(UIAction.Exit)
                            }, modifier = Modifier
                                .widthIn(220.dp)
                                .heightIn(44.dp)
                        ) {
                            Text(
                                text = stringResource(id = R.string.group_exit_group), fontSize = 16.sp,
                            )
                        }
                    }
                }
            }

            if (descDialogShow.value) {
                var descScrollState = rememberScrollState()
                Dialog(onDismissRequest = {
                    descDialogShow.value = false
                }) {
                    Column(
                        modifier = Modifier
                            .size(270.dp, 320.dp)
                            .background(color = Color(0xff222222), shape = RoundedCornerShape(8.dp))
                            .padding(horizontal = 16.dp, vertical = 20.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(stringResource(R.string.group_detail_description), color = Color.White, fontSize = 17.sp)
                        Spacer(modifier = Modifier.height(4.dp))
                        Box(
                            modifier = Modifier
                                .verticalScrollWithScrollbar(descScrollState)
                                .weight(1f)
                        ) {
                            Text(
                                info.intro,
                                color = Color(0x80FFFFFF)
                            )
                        }
                        Button(
                            {
                                descDialogShow.value = false
                            }, modifier = Modifier
                                .widthIn(160.dp)
                                .heightIn(36.dp)
                                .padding(top = 12.dp)
                        ) {
                            Text(
                                text = stringResource(R.string.group_iknow), fontSize = 16.sp,
                            )
                        }
                    }
                }
            }
        }

        AppTitleBar(title = "",
            modifier = Modifier
                .background(Color(39, 40, 41, (255 * titleAlpha.value).toInt()))
                .padding(top = 32.dp),
            onBack = {
                onBackPressedDispatcher?.onBackPressed()
            })
        if (isLoading) {
            Loading(
                properties = DialogProperties(
                    false, false
                )
            )
        }

    }
}


//endregion

//region 群组设置页面, 从详情或聊天设置按钮跳转过来的

@Composable
private fun GroupSettingItem(title: String, onClick: (() -> Unit)? = null, content: @Composable () -> Unit) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .height(64.dp)
            .clickable {
                onClick?.invoke()
            },
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            modifier = Modifier.weight(1f),
            text = title,
            fontSize = 16.sp,
            color = Color(0xFFE5E5E5)
        )
        content()
    }
}

@Composable
private fun Divider(modifier: Modifier = Modifier) {
    Spacer(
        modifier = modifier
            .fillMaxWidth()
            .height(1.dp)
            .background(color = Color(0x14FFFFFF))
    )
}


@Composable
fun GroupDetailSettingsPage(info: ChatGroupDetail, isLoading: Boolean, onAction: (UIAction) -> Unit = {}) {
    if (info.role < ChatGroup.Role.ADMIN) {
        //不知道为什么要做一下拦截, 总之先判断一下吧
        return
    }
    val scrollState = rememberScrollableState {
        0f
    }
    Box(
        modifier = Modifier
            .fillMaxSize()
            .systemBarsPadding()
    ) {
        Column(modifier = Modifier.scrollable(scrollState, Orientation.Vertical)) {
            AppTitleBar(title = stringResource(R.string.group_settings_setting))
            GroupSettingItem(title = stringResource(R.string.group_settings_avatar), {
                onAction(UIAction.ChangeAvatar)
            }) {
                CircleComposeImage(model = info.avatarUrl, modifier = Modifier.size(40.dp))
                Image(painter = painterResource(id = R.drawable.ic_arrow_right), contentDescription = "right arrow")
            }
            Divider(modifier = Modifier.padding(horizontal = 16.dp))
            GroupSettingItem(title = stringResource(R.string.group_settings_name), {
                onAction(UIAction.ChangeGroupName)
            }) {
                Text(info.name, fontSize = 12.sp, color = Color(0xFF787979))
                Image(painter = painterResource(id = R.drawable.ic_arrow_right), contentDescription = "right arrow")
            }
            Divider(modifier = Modifier.padding(horizontal = 16.dp))
            GroupSettingItem(title = stringResource(R.string.group_settings_description), {
                onAction(UIAction.ChangeGroupDescription)
            }) {
                Text(
                    info.intro, fontSize = 12.sp, color = Color(0xFF787979), maxLines = 1, overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.widthIn(max = 180.dp)
                )
                Image(painter = painterResource(id = R.drawable.ic_arrow_right), contentDescription = "right arrow")
            }
            if (info.role == ChatGroup.Role.OWNER) {
                Divider(modifier = Modifier.padding(horizontal = 16.dp))
                GroupSettingItem(title = stringResource(R.string.group_settings_manager), {
                    onAction(UIAction.GoAdminList)
                }) {
                    Text(stringResource(R.string.group_settings_admin_maximum), fontSize = 12.sp, color = Color(0xFF787979))
                    Image(painter = painterResource(id = R.drawable.ic_arrow_right), contentDescription = "right arrow")
                }
            }
            Divider(modifier = Modifier.padding(horizontal = 16.dp))
            GroupSettingItem(title = stringResource(R.string.group_settings_remove_member), {
                onAction(UIAction.GoRemoveMember)
            }) {
                Image(painter = painterResource(id = R.drawable.ic_arrow_right), contentDescription = "right arrow")
            }
            if (info.role == ChatGroup.Role.OWNER || info.role == ChatGroup.Role.ADMIN) {
                Divider(modifier = Modifier.padding(horizontal = 16.dp))
                GroupSettingItem(title = stringResource(R.string.group_settings_audit)) {
                    MySwitch(active = info.needReview, onClick = {
                        onAction(UIAction.SwitchJoinAudit)
                    })
                }
            }
            Divider(modifier = Modifier.padding(horizontal = 16.dp))
        }

        //底部按钮
        if (info.role == ChatGroup.Role.OWNER) {
            Button(
                {
                    onAction(UIAction.Disband)
                }, modifier = Modifier
                    .widthIn(220.dp)
                    .heightIn(44.dp)
                    .align(Alignment.BottomCenter)
                    .absoluteOffset(y = -20.dp)
            ) {
                Text(
                    text = stringResource(id = R.string.group_remove_group), fontSize = 16.sp,
                )
            }
        }

        if (isLoading) {
            Loading(
                properties = DialogProperties(
                    false, false
                )
            )
        }
    }
}

//endregion

//region 群组申请列表


@Composable
private fun JoinRequestItem(user: JoinGroupRequest, onAction: (UIAction) -> Unit = {}) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween, modifier = Modifier
            .height(108.dp)
            .padding(horizontal = 16.dp), verticalAlignment = Alignment.CenterVertically
    ) {
        CircleComposeImage(
            model = user.user.avatarUrl, modifier = Modifier
                .size(64.dp)
        )
        Spacer(modifier = Modifier.width(12.dp))
        Text(user.user.nickname, modifier = Modifier.weight(1f), fontSize = 14.sp, color = Color.White)
        Spacer(modifier = Modifier.width(12.dp))
        Column {
            Text(
                if (user.status == 1) stringResource(R.string.group_request_accept_done) else stringResource(R.string.group_request_accept), modifier = Modifier
                    .size(88.dp, 32.dp)
                    .clip(RoundedCornerShape(24.dp))
                    .background(Color(0xFF945EFF))
                    .clickable {
                        onAction(UIAction.AcceptJoin(user.applyId))
                    }
                    .wrapContentHeight(),
                textAlign = TextAlign.Center,
                color = Color.White
            )
            Spacer(modifier = Modifier.height(12.dp))
            Text(
                if (user.status == 2) stringResource(R.string.group_request_decline_done) else stringResource(R.string.group_request_decline), modifier = Modifier
                    .size(88.dp, 32.dp)
                    .clip(RoundedCornerShape(24.dp))
                    .border(1.dp, Color(0xFF945EFF), shape = RoundedCornerShape(24.dp))
                    .clickable { onAction(UIAction.DeclineJoin(user.applyId)) }
                    .wrapContentHeight(),
                textAlign = TextAlign.Center,
                color = Color(0xFF945EFF)
            )
        }
    }
}

/**
 * 群组加入申请列表
 *
 * 这个页面应该是需要一个群组的id, 在通过ViewModel来获取到申请的列表
 */
@Composable
fun GroupJoinRequestListPage(viewModel: ChatGroupRequestListViewModel, isLoading: Boolean, onAction: (UIAction) -> Unit = {}) {
    val onBackPressedDispatcher = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher
    LaunchedEffect(key1 = Unit) {
        viewModel.refresh()
    }
    Box() {
        Column(modifier = Modifier.systemBarsPadding()) {
            AppTitleBar(title = stringResource(R.string.group_join_request), onBack = { onBackPressedDispatcher?.onBackPressed() })
            Box {
                StateListView(viewModel = viewModel, keyProvider = { _, member -> member.applyId }) { member, _, _, _ ->
                    JoinRequestItem(user = member, onAction)
                }
            }
        }
    }
}

//endregion

@Preview(backgroundColor = 0xff272829, showBackground = true)
@Composable
fun GroupSettingsPagePreview() {
    val info = ChatGroupDetail(
        role = ChatGroup.Role.ADMIN,
        avatarUrl = "https://s.test.ucoofun.com/aacd0W?x-oss-process=image/format,webp",
        iEnableDontDisturb = false,
        intro = "简介简介简介",
        name = "测试群组"
    )
    GroupDetailSettingsPage(info, false)
}
