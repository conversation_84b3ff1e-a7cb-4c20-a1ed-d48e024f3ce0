package com.qyqy.ucoo.compose.presentation.chatgroup.page

import androidx.compose.runtime.staticCompositionLocalOf
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.compose.presentation.room.CGUserImageContent

sealed class MsgEvents {
    data class SeeUser(val user: User) : MsgEvents()
    data object EditFamilyBuilt : MsgEvents()
    data class OnPreview(val image:CGUserImageContent): MsgEvents()
}

interface MsgEventHandler {
    fun handle(event: MsgEvents)
}

val LocalMsgEventHandler = staticCompositionLocalOf<MsgEventHandler?> {
    null
}