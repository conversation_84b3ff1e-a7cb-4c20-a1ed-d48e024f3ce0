package com.qyqy.ucoo.compose.presentation.chatgroup.usecase

import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.ActivityLifecycle
import com.qyqy.ucoo.base.loadingLaunchCompat
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.domain.ResultSuspendUseCase
import com.qyqy.ucoo.compose.presentation.chatgroup.ChatGroupApi
import com.qyqy.ucoo.compose.presentation.chatgroup.data.JoinResult
import com.qyqy.ucoo.compose.presentation.chatgroup.launchChatGroup
import com.qyqy.ucoo.getTopActivity
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.toast
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class JoinGroupUseCase(
    private val api: ChatGroupApi = createApi<ChatGroupApi>(),
    ioDispatcher: CoroutineDispatcher = Dispatchers.IO
) : ResultSuspendUseCase<Int, JoinResult>(ioDispatcher) {

    override suspend fun execute(parameters: Int): Result<JoinResult> {
        // 加入群组逻辑
        return runApiCatching { api.joinGroup(mapOf("chatgroup_id" to parameters.toString())) }
    }

    companion object {
        fun joinGroup(chatgroupId: Int, useCase: JoinGroupUseCase, coroutineScope: CoroutineScope) {
            coroutineScope.launch {
                getTopActivity()?.loadingLaunchCompat {
                    useCase(chatgroupId)
                        .onSuccess {
                            toast(it.msg)
                            val apply = it.apply
                            if (apply.status == 20) {
                                launchChatGroup(apply.chatgroupId)
                            }
                        }.toastError()
                }
            }
        }
    }

}