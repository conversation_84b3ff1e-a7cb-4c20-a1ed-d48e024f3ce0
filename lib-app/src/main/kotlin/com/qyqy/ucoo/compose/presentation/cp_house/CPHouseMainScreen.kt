package com.qyqy.ucoo.compose.presentation.cp_house

import android.app.Activity
import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PageSize
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.util.lerp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.Popup
import androidx.compose.ui.zIndex
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.LocalViewModelStoreOwner
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.dialog.rememberDialogQueue
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.ReportExposureCompose
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.asViewModelStoreOwner
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.clickWithShape
import com.qyqy.ucoo.compose.data.CPHouseFullInfo
import com.qyqy.ucoo.compose.data.CPHouseFullInfo.ICPHouseProp
import com.qyqy.ucoo.compose.pages.CPHouseDestination
import com.qyqy.ucoo.compose.presentation.cp_house.house.CoinPopup
import com.qyqy.ucoo.compose.presentation.cp_house.house.HouseCoinDialog
import com.qyqy.ucoo.compose.presentation.cp_house.house.HouseTaskDialog
import com.qyqy.ucoo.compose.presentation.cp_house.house.LocalHouseText
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.presentation.profile.UserProfileNavigator
import com.qyqy.ucoo.compose.router.LocalUCOONavController
import com.qyqy.ucoo.compose.router.UCOOPreviewTheme
import com.qyqy.ucoo.compose.router.navigateTo
import com.qyqy.ucoo.compose.ui.AnimatedDialog
import com.qyqy.ucoo.compose.ui.AnyPopDialogProperties
import com.qyqy.ucoo.compose.ui.AppScrollableTabRow
import com.qyqy.ucoo.compose.ui.AppTab
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.DirectionState
import com.qyqy.ucoo.compose.ui.LoadingLayout
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.vm.cp_house.CPHouseViewModel
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.im.room.ChatRoomActivity
import com.qyqy.ucoo.utils.GiftEffectHelper
import com.qyqy.ucoo.utils.LogUtils
import com.yy.yyeva.EvaAnimConfig
import com.yy.yyeva.inter.IEvaAnimListener
import com.yy.yyeva.util.EvaConstant
import com.yy.yyeva.view.EvaAnimViewV3
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import kotlin.math.absoluteValue

private sealed interface HomeUIAction {
    class ClickHouseAction(val item: CPHouseFullInfo.CPHouseSimplePropItem) : HomeUIAction//点击小屋 道具
    class ClickCarAction(val item: CPHouseFullInfo.CPHouseSimplePropItem) : HomeUIAction//点击小车 道具
    object UpgradeHouseAction : HomeUIAction//升级小屋
    object ClickPrivateRoomAction : HomeUIAction//点击私密小屋

    object ClickIntimacyAction : HomeUIAction//点击亲密度

    object ClickTreasureBoxAction : HomeUIAction//点击宝箱
    object ClickRankAction : HomeUIAction//点击排行榜
    object ClickDressUpAction : HomeUIAction//点击装扮中心
    object ClickMemoriesAction : HomeUIAction//点击美好回忆
    class ClickAvatar(val user: AppUser) : HomeUIAction//点击头像


    class ClickHousePropAction(val item: CPHouseFullInfo.CPHouseFullPropItem) :
        HomeUIAction//点击CP小屋道具

    object ClickPopBackAction : HomeUIAction//点击回退
    data object ClickCoin : HomeUIAction//金币
}

@Composable
fun CPHouseMainScreen(room_id: Int) {
    val controller = LocalUCOONavController.current
    val context = LocalContext.current
    val text = LocalHouseText.current

    val dq = rememberDialogQueue<IDialogAction>()
    dq.DialogContent()
    val nav = LocalUCOONavController.current

    val viewModel = viewModel(modelClass = CPHouseViewModel::class, factory = viewModelFactory {
        initializer {
            CPHouseViewModel(room_id = room_id)
        }
    }, viewModelStoreOwner = context.asViewModelStoreOwner(LocalViewModelStoreOwner.current!!))


    //其他页打开任务
    LaunchedEffect(key1 = "actionCollect") {
        viewModel.actionFlow.collectLatest { action ->
            when (action) {
                CPHouseViewModel.Action.ShowTask -> dq.push(HouseTaskDialog() {
                    viewModel.refreshHouseInfo()
                })
            }
        }
    }

    val houseInfo by viewModel.houseInfo.collectAsStateWithLifecycle()

    //是否显示道具选择框
    var isShowCpSelector by remember {
        mutableStateOf(false)
    }

    //点击的选择框道具类型
    var showCpSelectorType by remember {
        mutableStateOf(1)
    }

    LaunchedEffect(key1 = Unit) {
        viewModel.refreshCpHouseHomeProp(CPHouseFullInfo.CPHouseFullPropItem.TYPE_HOUSE)
        viewModel.refreshCpHouseHomeProp(CPHouseFullInfo.CPHouseFullPropItem.TYPE_CAR)
    }

    LoadingLayout {
        val loadingFlag = LocalContentLoading.current
        //事件处理
        val actionHandler = remember {
            val callback: (HomeUIAction) -> Unit = {
                when (it) {
                    HomeUIAction.ClickCoin -> {
                        dq.push(HouseCoinDialog(houseInfo.balance, text.coinTextArray.map { res ->
                            context.getString(res)
                        }))
                    }

                    is HomeUIAction.ClickHouseAction -> {
                        if (houseInfo.iAmAdmin()) {
                            Analytics.reportExposureEvent(TracePoints.CP_ROOM_HOUSE_CLICK, extra = "{\"cp_room_id\":${houseInfo.id},\"cp_room_house_id\": ${it.item.id}}", useShushu = true)
                            showCpSelectorType = CPHouseFullInfo.CPHouseFullPropItem.TYPE_HOUSE
                            isShowCpSelector = true
                        }
                    }

                    is HomeUIAction.ClickCarAction -> {
                        if (houseInfo.iAmAdmin()) {
                            Analytics.reportExposureEvent(TracePoints.CP_ROOM_CAR_CLICK, extra = "{\"cp_room_id\":${houseInfo.id},\"cp_room_car_id\": ${it.item.id}}", useShushu = true)
                            showCpSelectorType = CPHouseFullInfo.CPHouseFullPropItem.TYPE_CAR
                            isShowCpSelector = true
                        }
                    }

                    HomeUIAction.UpgradeHouseAction -> {//升级小屋
                        dq.push(HouseTaskDialog {
                            viewModel.refreshHouseInfo()
                        })
                    }

                    HomeUIAction.ClickRankAction -> {
                        nav.navigateTo(CPHouseDestination.HouseIntimateRank)
                    }

                    HomeUIAction.ClickTreasureBoxAction -> {
                        if (houseInfo.iAmAdmin()) {
                            Analytics.reportExposureEvent(TracePoints.CP_ROOM_LUCKY_BOX_CLICK, extra = "{\"cp_room_id\":${houseInfo.id}}", useShushu = true)
                            nav.navigateTo(CPHouseDestination.HouseTreasureBox)
                        }
                    }

                    HomeUIAction.ClickDressUpAction -> {
                        if (houseInfo.iAmAdmin()) {
                            Analytics.reportExposureEvent(TracePoints.CP_ROOM_DECORATE_CENTER_CLICK, extra = "{\"cp_room_id\":${houseInfo.id}}", useShushu = true)
                            controller.navigateTo(CPHouseDestination.HouseDressUpDest)
                        }
                    }

                    HomeUIAction.ClickMemoriesAction -> {
                        if (houseInfo.iAmAdmin()) {
                            Analytics.reportExposureEvent(TracePoints.CP_ROOM_MEMORY_LINE_CENTER_CLICK, extra = "{\"cp_room_id\":${houseInfo.id}}", useShushu = true)
                            controller.navigate(CPHouseDestination.MemoriesDestination.route)
                        }
                    }

                    HomeUIAction.ClickPopBackAction -> {
                        if (!controller.popBackStack()) {
                            (context as? Activity)?.finish()
                        }
                    }

                    is HomeUIAction.ClickAvatar -> {
                        //1和2代表是资料卡进来的
                        if ((viewModel.mFrom == 1 || viewModel.mFrom == 2) && it.user.userId == viewModel.mMasterUserId) {
                            //从资料卡点击进来的, 防止重复创建, 直接back
                            if (!controller.popBackStack()) {
                                (context as? Activity)?.finish()
                            }
                        } else {
                            /**
                             * 头痛的是, 如果进来之后两个头像交替的点, 就会无限创建小屋了
                             */
                            UserProfileNavigator.navigate(context, it.user)
                        }
                    }

                    HomeUIAction.ClickIntimacyAction -> {
                        if (houseInfo.iAmAdmin()) {
                            controller.navigate(CPHouseDestination.HouseIntimacyHistoryDestination.route)
                        }
                    }

                    HomeUIAction.ClickPrivateRoomAction -> {
                        loadingFlag.value = true
                        viewModel.joinPrivateRoom({
                            ChatRoomActivity.openRoomActivity(context, it.first, it.second)
                        }, {
                            loadingFlag.value = false
                        })
                    }

                    else -> {

                    }
                }
            }
            callback
        }

        CPHouseContent(houseInfo, actionHandler)

        if (isShowCpSelector) {
            var isShowFilterBoxActiveClose by remember { mutableStateOf(false) }
            AnimatedDialog(
                isActiveClose = isShowFilterBoxActiveClose,
                onDismiss = { isShowCpSelector = false },
                properties = AnyPopDialogProperties(direction = DirectionState.BOTTOM)
            ) {
                val list = viewModel.getCpHouseHomeProp(showCpSelectorType)
//                LaunchedEffect(key1 = Unit) {
//                    viewModel.refreshCpHouseHomeProp(showCpSelectorType)
//                }
                CPHouseItemSelector(houseInfo, list) {
                    if (it is HomeUIAction.ClickHousePropAction) {
                        isShowFilterBoxActiveClose = true
                        if (it.item.gainType == 6) {
                            controller.navigateTo(CPHouseDestination.HouseIntimateRank)
                        } else {
                            //点击了小屋 或 车辆 道具
                            when (it.item.subType) {
                                1 -> {//专属小屋, 点击升级
                                    dq.push(HouseTaskDialog())
                                }

                                else -> {
                                    if (it.item.obtained) {//已经获得了, 就去装扮中心
                                        controller.navigateTo(CPHouseDestination.HouseDressUpDest)
                                    } else {//否则就去抽奖
                                        nav.navigateTo(CPHouseDestination.HouseTreasureBox)
                                    }
                                }
                            }
                        }

                    }
                }
            }
        }
    }
}

@Composable
private fun CPHouseContent(houseInfo: CPHouseFullInfo, onAction: (HomeUIAction) -> Unit) {
    var moreVisible by remember {
        mutableStateOf(false)
    }

    val controller = LocalUCOONavController.current
    val context = LocalContext.current
    Box {
        ComposeImage(
            model = houseInfo.background,
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillHeight
        )
        if (moreVisible) {
            Popup(
                Alignment.TopEnd,
                onDismissRequest = { moreVisible = false }) {
                CoinPopup(
                    modifier = Modifier
                        .statusBarsPadding()
                        .padding(end = 12.dp, top = 44.dp)
                        .width(108.dp),
                    isAdmin = houseInfo.iAmAdmin(),
                    onRuleClick = {
                        AppLinkManager.open(context, houseInfo.rule)
                        moreVisible = false
                    }, onRecordClick = {
                        controller.navigateTo(CPHouseDestination.HCRecordDestination)
                        moreVisible = false
                    })
            }
        }
        Column(
            modifier = Modifier
                .fillMaxSize(1f)
                .systemBarsPadding()
        ) {
            AppTitleBar(
                title = if (!houseInfo.inValid()) "" else stringResource(id = if (houseInfo.iAmAdmin()) R.string.亲密小屋 else R.string.他们的CP小屋),
                onBack = {
                    onAction(HomeUIAction.ClickPopBackAction)
                }
            ) {
                ComposeImage(
                    model = R.drawable.ic_more_menu,
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .padding(end = 12.dp)
                        .size(40.dp)
                        .click {
                            moreVisible = true
                        }
                        .padding(8.dp)
                )
            }

            Row(
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .padding(top = 4.dp)
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    //基础信息
                    Row(
                        modifier = Modifier
                            .height(64.dp)
                            .background(
                                color = Color(0xFFE2FDFD),
                                shape = RoundedCornerShape(12.dp)
                            )
                            .padding(horizontal = 8.dp, vertical = 12.dp)
                    ) {
                        Box {
                            ComposeImage(
                                model = houseInfo.maleUser.avatarUrl,
                                modifier = Modifier
                                    .size(40.dp)
                                    .clip(CircleShape)
                                    .click {
                                        onAction(HomeUIAction.ClickAvatar(houseInfo.maleUser))
                                    }
                            )
                            ComposeImage(
                                model = houseInfo.femaleUser.avatarUrl, modifier = Modifier
                                    .padding(start = 30.dp)
                                    .size(40.dp)
                                    .clip(CircleShape)
                                    .click {
                                        onAction(HomeUIAction.ClickAvatar(houseInfo.femaleUser))
                                    }
                            )
                        }
                        Column(
                            modifier = Modifier
                                .fillMaxHeight()
                                .padding(start = 8.dp)
                                .weight(1f),
                            verticalArrangement = Arrangement.SpaceBetween
                        ) {
                            //私密小屋个人信息
                            Row(verticalAlignment = Alignment.CenterVertically) {
                                Text(
                                    houseInfo.maleUser.nickname,
                                    modifier = Modifier.weight(1f, false),
                                    overflow = TextOverflow.Ellipsis,
                                    maxLines = 1,
                                    color = Color(0xff3a6aa2),
                                    fontWeight = FontWeight.Medium,
                                    fontSize = 12.sp,
                                    lineHeight = 12.sp
                                )
                                Text(
                                    "&",
                                    color = Color(0xff3a6aa2),
                                    fontWeight = FontWeight.Medium,
                                    fontSize = 12.sp,
                                    lineHeight = 12.sp
                                )
                                Text(
                                    houseInfo.femaleUser.nickname,
                                    modifier = Modifier.weight(1f, false),
                                    overflow = TextOverflow.Ellipsis,
                                    maxLines = 1,
                                    color = Color(0xff3a6aa2),
                                    fontWeight = FontWeight.Medium,
                                    fontSize = 12.sp,
                                    lineHeight = 12.sp
                                )
                            }
                            AutoSizeText(
//                                    stringResource(id = R.string.已相伴N天, ),
                                text = buildAnnotatedString {
                                    if (houseInfo.cpExtraInfo.togatherDays.isNotBlank()) {
                                        append(houseInfo.cpExtraInfo.togatherDays)
                                        append("  ")
                                    }
                                    append(
                                        stringResource(
                                            id = R.string.CP值N,
                                            houseInfo.cpExtraInfo.cpValue
                                        )
                                    )
                                },
                                modifier = Modifier.fillMaxWidth(),
                                color = Color(0xff3a6aa2),
                                fontWeight = FontWeight.Medium,
                                fontSize = 12.sp,
                                lineHeight = 12.sp
                            )
                        }
                    }
                    //钱包
                    Row(modifier = Modifier.padding(top = 8.dp)) {
                        if (houseInfo.iAmAdmin()) {
                            Row(
                                modifier = Modifier
                                    .clickWithShape(RoundedCornerShape(24.dp)) {
                                        onAction.invoke(HomeUIAction.ClickCoin)
                                    }
                                    .background(
                                        color = Color(0x4D000000),
                                        shape = RoundedCornerShape(24.dp)
                                    )
                                    .padding(4.dp), horizontalArrangement = Arrangement.Center,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                ComposeImage(
                                    model = R.drawable.ic_house_coin,
                                    modifier = Modifier
                                        .padding(end = 2.dp)
                                        .size(16.dp)
                                )
                                Text(
                                    text = houseInfo.balance.toString(),
                                    color = Color.White,
                                    fontSize = 12.sp,
                                    lineHeight = 12.sp,
                                    fontWeight = FontWeight.Medium
                                )
                            }
                            Spacer(modifier = Modifier.width(8.dp))
                        }
                        Row(
                            modifier = Modifier
                                .background(
                                    color = Color(0x4D000000),
                                    shape = RoundedCornerShape(24.dp)
                                )
                                .padding(4.dp)
                                .clickWithShape(RoundedCornerShape(24.dp)) {
                                    onAction(HomeUIAction.ClickIntimacyAction)
                                }, horizontalArrangement = Arrangement.Center,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            ComposeImage(
                                model = R.drawable.ic_intimacy_value,
                                modifier = Modifier
                                    .padding(end = 2.dp)
                                    .size(16.dp)
                            )
                            Text(
                                text = houseInfo.intimacyScore.toString(),
                                color = Color.White,
                                fontSize = 12.sp,
                                lineHeight = 12.sp,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }


                    //排行榜
                    Row(
                        modifier = Modifier
                            .padding(top = 8.dp)
                            .background(
                                color = Color(0xFFFF5E8B),
                                shape = RoundedCornerShape(24.dp)
                            )
                            .padding(4.dp)
                            .click(noEffect = true) {
                                onAction(HomeUIAction.ClickRankAction)
                            },
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        ComposeImage(
                            model = R.drawable.ic_house_rank,
                            modifier = Modifier.size(16.dp)
                        )
                        Text(
                            text = stringResource(
                                id = R.string.亲密排行N,
                                if (houseInfo.intimacyRanking in 1..100) houseInfo.intimacyRanking.toString() else "99+"
                            ),
                            color = Color.White,
                            fontSize = 12.sp,
                            lineHeight = 12.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
                if (houseInfo.iAmAdmin()) {
                    Spacer(modifier = Modifier.width(16.dp))
                    Column {
                        Column(
                            modifier = Modifier
                                .background(
                                    color = Color(0xFFE2FDFD),
                                    shape = RoundedCornerShape(12.dp)
                                )
                                .size(64.dp)
                                .click(noEffect = true) {
                                    onAction(HomeUIAction.ClickPrivateRoomAction)
                                },
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            ComposeImage(model = R.drawable.ic_bed, modifier = Modifier.weight(1f))
                            AutoSizeText(
                                stringResource(id = R.string.私密小屋),
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .background(color = Color(0xFFFF4D8A), shape = CircleShape)
                                    .padding(horizontal = 8.dp, vertical = 4.dp),
                                fontSize = 12.sp, color = Color.White,
                                lineHeight = 12.sp, fontWeight = FontWeight.Medium,
                            )
                        }
                        ReportExposureCompose(onExposureStart = {
                            Analytics.reportExposureEvent(TracePoints.CP_ROOM_LUCKY_BOX_SHOW, extra = "{\"cp_room_id\":${houseInfo.id}}", useShushu = true)
                        }) {
                            Box(
                                modifier = Modifier
                                    .padding(top = 19.dp)
                                    .click(noEffect = true) {
                                        onAction(HomeUIAction.ClickTreasureBoxAction)
                                    }) {
                                ComposeImage(
                                    R.drawable.ic_house_treasurebox,
                                    modifier = Modifier
                                        .padding(bottom = 12.dp)
                                        .size(54.dp)
                                        .align(Alignment.TopCenter)
                                        .background(color = Color(0xa6ffffff), shape = CircleShape)
                                        .padding(6.dp)
                                )
                                Text(
                                    stringResource(id = R.string.幸运宝箱),
                                    modifier = Modifier
                                        .height(20.dp)
                                        .paint(painterResource(id = R.drawable.ic_house_module_bg))
                                        .align(Alignment.BottomCenter),
                                    textAlign = TextAlign.Center,
                                    fontSize = 12.sp,
                                    lineHeight = 20.sp,
                                    fontWeight = FontWeight.Medium,
                                )
                            }
                        }
                        ReportExposureCompose(onExposureStart = {
                            Analytics.reportExposureEvent(TracePoints.CP_ROOM_DECORATE_CENTER_SHOW, extra = "{\"cp_room_id\":${houseInfo.id}}", useShushu = true)
                        }) {
                            Box(
                                modifier = Modifier
                                    .padding(top = 12.dp)
                                    .click(noEffect = true) {
                                        onAction(HomeUIAction.ClickDressUpAction)
                                    }) {
                                ComposeImage(
                                    R.drawable.ic_house_dressup,
                                    modifier = Modifier
                                        .padding(bottom = 12.dp)
                                        .size(54.dp)
                                        .align(Alignment.TopCenter)
                                        .background(color = Color(0xa6ffffff), shape = CircleShape)
                                        .padding(6.dp)
                                )
                                Text(
                                    stringResource(id = R.string.装扮中心),
                                    modifier = Modifier
                                        .height(20.dp)
                                        .paint(painterResource(id = R.drawable.ic_house_module_bg))
                                        .align(Alignment.BottomCenter),
                                    textAlign = TextAlign.Center,
                                    fontSize = 12.sp,
                                    lineHeight = 20.sp,
                                    fontWeight = FontWeight.Medium,
                                )
                            }
                        }

                        ReportExposureCompose(onExposureStart = {
                            Analytics.reportExposureEvent(TracePoints.CP_ROOM_MEMORY_LINE_SHOW, extra = "{\"cp_room_id\":${houseInfo.id}}", useShushu = true)
                        }) {
                            Box(
                                modifier = Modifier
                                    .padding(top = 12.dp)
                                    .click(noEffect = true) {
                                        onAction(HomeUIAction.ClickMemoriesAction)
                                    }) {
                                ComposeImage(
                                    R.drawable.ic_house_memories,
                                    modifier = Modifier
                                        .padding(bottom = 12.dp)
                                        .size(54.dp)
                                        .align(Alignment.TopCenter)
                                        .background(color = Color(0xa6ffffff), shape = CircleShape)
                                        .padding(6.dp)
                                )
                                Text(
                                    stringResource(id = R.string.美好回忆),
                                    modifier = Modifier
                                        .height(20.dp)
                                        .paint(painterResource(id = R.drawable.ic_house_module_bg))
                                        .align(Alignment.BottomCenter),
                                    textAlign = TextAlign.Center,
                                    fontSize = 12.sp,
                                    lineHeight = 20.sp,
                                    fontWeight = FontWeight.Medium,
                                )
                            }
                        }
                    }
                }
            }

            Column(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                verticalArrangement = Arrangement.Bottom,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Row(
                    modifier = Modifier
                        .padding(horizontal = 16.dp)
                        .padding(bottom = 16.dp)
                ) {
                    Box(modifier = Modifier.weight(1f)) {
                        CPHousePropItem(
                            modifier = Modifier
                                .fillMaxWidth()
                                .click(noEffect = true) {
                                    if (houseInfo.iAmAdmin()) {
                                        houseInfo.house?.let {
                                            onAction(HomeUIAction.ClickHouseAction(it))
                                        }
                                    }
                                }, houseInfo.id, 1, houseInfo.house
                        )
                    }
                    Spacer(modifier = Modifier.width(8.dp))
                    Box(modifier = Modifier.weight(1f)) {
                        CPHousePropItem(
                            modifier = Modifier
                                .fillMaxWidth()
                                .click(noEffect = true) {
                                    if (houseInfo.iAmAdmin()) {
                                        houseInfo.car?.let {
                                            onAction(HomeUIAction.ClickCarAction(it))
                                        }
                                    }
                                }, houseInfo.id, 2, houseInfo.car
                        )
                    }
                }
                if (houseInfo.iAmAdmin()) {
                    Column(
                        modifier = Modifier
                            .padding(bottom = 16.dp, top = 8.dp)
                            .paint(painterResource(id = R.drawable.ic_house_upgrade_bg))
                            .click(noEffect = true) {
                                onAction(HomeUIAction.UpgradeHouseAction)
                            },
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(
                            stringResource(id = R.string.升级小屋),
                            color = Color.White,
                            fontSize = 16.sp,
                            lineHeight = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            stringResource(id = R.string.亲密度越高小屋越华丽),
                            color = Color.White,
                            fontSize = 12.sp,
                            lineHeight = 12.sp
                        )
                    }
                }
            }
        }
    }
}

/**
 * 主页单个道具显示
 *
 * @param modifier
 * @param item 小屋或小车
 */
@Composable
private fun CPHousePropItem(
    modifier: Modifier = Modifier,
    houseId: Int,
    type: Int,
    item: CPHouseFullInfo.CPHouseSimplePropItem?
) {
    if (item == null) {
        return
    }
    ReportExposureCompose(onExposureStart = {
        if (type == 1) {
            Analytics.reportExposureEvent(TracePoints.CP_ROOM_HOUSE_SHOW, extra = "{\"cp_room_id\":${houseId},\"cp_room_house_id\": ${item.id}}", useShushu = true)
        } else {
            Analytics.reportExposureEvent(TracePoints.CP_ROOM_CAR_SHOW, extra = "{\"cp_room_id\":${houseId},\"cp_room_car_id\": ${item.id}}", useShushu = true)
        }
    }) {
        Box(modifier = modifier) {
            ComposeImage(
                model = R.drawable.ic_house_item_bg,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter)
            )
            Column(modifier = Modifier.align(Alignment.BottomCenter)) {

                CPHouseEffectWidget(item = item)

                Column(
                    modifier = Modifier
                        .padding(2.dp)
                        .fillMaxWidth()
                        .height(68.dp)
                        .background(
                            brush = Brush.horizontalGradient(
                                listOf(
                                    Color(0xFFC1E9F3),
                                    Color(0xFFF7FDFF),
                                    Color(0xFFC1E9F3)
                                )
                            ),
                            shape = RoundedCornerShape(
                                bottomStart = 6.dp,
                                bottomEnd = 6.dp
                            )
                        ),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        item.name,
                        color = Color(0xFF3A6AA2),
                        fontSize = 16.sp,
                        lineHeight = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        stringResource(id = R.string.收益一天N币, item.profit),
                        color = Color(0xFF3A6AA2),
                        fontSize = 12.sp,
                        lineHeight = 12.sp
                    )
                }
            }
        }
    }
}

/**
 * 亲密小屋里的道具选择弹窗
 */
@Composable
private fun CPHouseItemSelector(
    houseInfo: CPHouseFullInfo,
    listState: StateFlow<List<CPHouseFullInfo.CPHouseFullPropItem>>,
    modifier: Modifier = Modifier,
    onAction: (HomeUIAction) -> Unit = {}
) {
    val list by listState.collectAsStateWithLifecycle()
    val wearingProp by remember {
        derivedStateOf {
            list.find { it.isUsing }
        }
    }

    val itemCategories by remember {
        derivedStateOf {
            list.groupBy { it.subTypeName }
        }
    }
    val titles by remember {
        derivedStateOf {
            itemCategories.keys.map {
                AppTab(it)
            }
        }
    }
    val pagerState = rememberPagerState(Math.max(titles.indexOfFirst { it.name == wearingProp?.subTypeName }, 0)) {
        titles.size
    }

    val selectedPropItem = remember {
        mutableStateOf<CPHouseFullInfo.CPHouseFullPropItem?>(null)
    }
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(
                color = Color(0xFF2EABF2),
                shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)
            )
            .padding(bottom = 16.dp, top = 8.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (titles.size > 1) {
            AppScrollableTabRow(
                tabs = titles, pagerState = pagerState,
                modifier = Modifier
                    .width(200.dp)
                    .height(54.dp),
            )
        } else if (titles.size == 1) {
            Text(
                titles[0].name,
                fontSize = 16.sp,
                lineHeight = 54.sp,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center,
                color = Color.White,
                modifier = Modifier.height(54.dp)
            )
        } else {
            Spacer(modifier = Modifier.height(54.dp))
        }

        HorizontalPager(state = pagerState, userScrollEnabled = false) {
            CPHousePagerItems(
                houseInfo.intimacyScore,
                itemCategories[titles[it].name] ?: listOf(),
                selectedPropItem
            )
        }

        selectedPropItem.value?.let { item ->
            Column(
                modifier = Modifier
                    .padding(bottom = 8.dp, top = 16.dp)
                    .paint(painterResource(id = if (item.obtained) R.drawable.ic_house_upgrade_bg2 else R.drawable.ic_house_upgrade_bg3))
                    .click(noEffect = true) {
                        onAction(HomeUIAction.ClickHousePropAction(item))
                    },
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    buildAnnotatedString {
                        val btnText = if (item.subType == 1) {
                            stringResource(id = R.string.升级小屋)
                        } else {
                            if (item.obtained) {
                                stringResource(id = R.string.已解锁)
                            } else {
                                stringResource(id = R.string.未获得)
                            }
                        }
                        append(btnText)
                    },
                    color = Color.White,
                    fontSize = 16.sp,
                    lineHeight = 16.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }

}

/**
 * 亲密小屋道具选择弹窗里的Pager
 *
 * @param currentScore
 * @param source
 * @param selectedItem
 */
@Composable
private fun CPHousePagerItems(
    currentScore: Int,
    source: List<CPHouseFullInfo.CPHouseFullPropItem>,
    selectedItem: MutableState<CPHouseFullInfo.CPHouseFullPropItem?>
) {
    if (source.isEmpty()) {
        return
    }

    val sortedList by remember {
        derivedStateOf {
            source.sortedBy { (if (it.obtained) Int.MIN_VALUE else 0) + it.profit }
        }
    }

    val titles by remember {
        derivedStateOf {
            sortedList.map { it.name }
        }
    }
    val pagerState = rememberPagerState(Math.max(sortedList.indexOfFirst { it.isUsing }, 0)) {
        titles.size
    }
    val scope = rememberCoroutineScope()

    val currentPageItem by remember {
        derivedStateOf {
            try {
                sortedList[pagerState.settledPage]
            } catch (e: IndexOutOfBoundsException) {
                sortedList[0]
            }
        }
    }

    LaunchedEffect(key1 = currentPageItem) {
        selectedItem.value = currentPageItem
    }

    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(horizontal = 16.dp)
        ) {
            ComposeImage(
                model = R.drawable.ic_arrow_left, modifier = Modifier
                    .size(24.dp)
                    .click(noEffect = true) {
                        scope.launch {
                            if (pagerState.canScrollBackward) {
                                pagerState.animateScrollToPage(pagerState.settledPage - 1)
                            }
                        }
                    })
            HorizontalPager(
                pagerState, pageSize = PageSize.Fixed(200.dp),
                contentPadding = PaddingValues(horizontal = 50.dp),
                modifier = Modifier.weight(1f),
                beyondViewportPageCount = 1
            ) {
                val item = sortedList[it]
                val offsetForPage = pagerState.calculateCurrentOffsetForPage(it)
                val absoluteValue = offsetForPage.absoluteValue
                Box(
                    modifier = Modifier
                        .graphicsLayer {
                            // We animate the scaleX + scaleY, between 85% and 100%
                            lerp(
                                start = 0.8f,
                                stop = 1f,
                                fraction = 1f - absoluteValue.coerceIn(0f, 1f)
                            ).also { scale ->
                                scaleX = scale
                                scaleY = scale
                            }

                            // We animate the alpha, between 50% and 100%
                            alpha = lerp(
                                start = 0.85f,
                                stop = 1f,
                                fraction = 1f - absoluteValue.coerceIn(0f, 1f)
                            )

                            translationX = lerp(
                                start = 0f,
                                stop = 100f,
                                fraction = offsetForPage
                            )
                        }
                        .zIndex(
                            if (it == pagerState.currentPage) titles.size.toFloat() else titles.size - (it - pagerState.settledPage).absoluteValue.toFloat()
                        )) {

                    CPHouseEffectWidget(item = item, blueBg = true, isPlaying = it == pagerState.settledPage)
                    if (!item.obtained) {
                        Box(
                            modifier = Modifier
                                .background(
                                    color = Color(0x60000000),
                                    shape = RoundedCornerShape(8.dp)
                                )
                                .fillMaxWidth()
                                .aspectRatio(1f),
                            contentAlignment = Alignment.Center
                        ) {
                            ComposeImage(
                                model = R.drawable.ic_house_item_locked,
                                modifier = Modifier.size(48.dp)
                            )
                        }
                    }
                }
            }
            ComposeImage(
                model = R.drawable.ic_arrow_right, modifier = Modifier
                    .size(24.dp)
                    .click(noEffect = true) {
                        scope.launch {
                            if (pagerState.canScrollForward) {
                                pagerState.animateScrollToPage(pagerState.settledPage + 1)
                            }
                        }
                    })
        }

        Text(
            currentPageItem.name,
            color = Color.White,
            fontSize = 16.sp,
            lineHeight = 16.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier
                .padding(top = 16.dp)
        )

        Text(
            text = buildAnnotatedString {
                if (currentPageItem.obtained) {
                    append(stringResource(id = R.string.已解锁))
                } else {
                    when (currentPageItem.gainType) {
                        4 -> {
                            append(stringResource(id = R.string.获取条件宝箱))
                        }

                        5 -> {
                            append(stringResource(id = R.string.获取条件系统))
                        }

                        6 -> {
                            append(stringResource(id = R.string.获取条件榜单))
                        }

                        else -> {
                            append(
                                stringResource(
                                    id = R.string.还差N亲密度,
                                    (currentPageItem.intimacyScoreLimit - currentScore).toString(),
                                )
                            )

                        }
                    }
                }
            },
            modifier = Modifier
                .padding(top = 4.dp)
                .background(color = Color(0x1A000000), shape = CircleShape)
                .padding(vertical = 4.dp, horizontal = 8.dp),
            color = Color.White,
            fontSize = 12.sp,
            lineHeight = 12.sp,
            fontWeight = FontWeight.Medium
        )
        Text(
            text = stringResource(id = R.string.收益一天N币, currentPageItem.profit),
            modifier = Modifier
                .padding(top = 4.dp)
                .background(color = Color(0x1A000000), shape = CircleShape)
                .padding(vertical = 4.dp, horizontal = 8.dp),
            color = Color.White,
            fontSize = 12.sp,
            lineHeight = 12.sp,
            fontWeight = FontWeight.Medium
        )

    }
}

@Composable
private fun CPHouseEffectWidget(item: ICPHouseProp, blueBg: Boolean = false, isPlaying: Boolean = true) {
    var effectFile by remember(item.effectFile) {
        mutableStateOf<File?>(null)
    }

    LaunchedEffect(key1 = item.effectFile) {
        if (effectFile == null) {
            effectFile = GiftEffectHelper.getGiftEffectFile(item.effectFile)
        }
    }
    Box(
        modifier = Modifier
            .background(
                color = if (blueBg) Color(0xFF93CFFB) else Color.Transparent,
                shape = RoundedCornerShape(8.dp)
            )
            .fillMaxWidth()
            .aspectRatio(1f)
            .clip(RoundedCornerShape(8.dp))
    ) {
        var isEvaPlaying by remember(isPlaying) {
            mutableStateOf(false)
        }
        if (!isEvaPlaying) {
            ComposeImage(model = item.icon, modifier = Modifier.fillMaxSize())
        }
        if (isPlaying && item.effectFile.isNotBlank() && item.effectFile.endsWith("mp4")) {
            if (effectFile != null) {
                AndroidView(
                    factory = {
                        EvaAnimViewV3(it).apply {
                            setVideoMode(EvaConstant.VIDEO_MODE_SPLIT_HORIZONTAL)
                            setLoop(true)
                            setMute(true)
                            setVideoFps(15)
                        }
                    },
                    onReset = {
                        it.stopPlay()
                        it.setAnimListener(null)
                    },
                    onRelease = {
                        it.stopPlay()
                        it.setAnimListener(null)
                    },
                    modifier = Modifier
                        .fillMaxSize(),
                ) { player ->
                    player.setAnimListener(object : IEvaAnimListener {
                        override fun onFailed(errorType: Int, errorMsg: String?) {
                        }

                        override fun onVideoComplete(lastFrame: Boolean) {
                        }

                        override fun onVideoDestroy() = Unit

                        override fun onVideoRender(frameIndex: Int, config: EvaAnimConfig?) {
                            if (frameIndex > 1 && !isEvaPlaying) {
                                isEvaPlaying = true
                            }
                        }

                        override fun onVideoRestart() = Unit

                        override fun onVideoStart(isRestart: Boolean) {
                        }
                    })
                    player.stopPlay()
                    effectFile?.let { player.startPlay(it) }
                }
            }
        }
    }

}

fun PagerState.calculateCurrentOffsetForPage(page: Int): Float {
    return (currentPage - page) + currentPageOffsetFraction
}

@Composable
@Preview
private fun CPHouseMainScreenPreview() {
    UCOOPreviewTheme {
        CPHouseContent(
            CPHouseFullInfo(
                femaleUser = AppUser(),
                maleUser = userForPreview,
                house = CPHouseFullInfo.CPHouseSimplePropItem(effectFile = ".mp4"),
            )
        ) {

        }
    }
}

@Composable
@Preview
private fun CPHouseMainScreenPreview2() {
    CPHouseItemSelector(
        CPHouseFullInfo(), MutableStateFlow(
            listOf(
                CPHouseFullInfo.CPHouseFullPropItem(
                    name = "测试",
                    intimacyScoreLimit = 100,
                    gainType = 4,
                    profit = 100,
                    obtained = true,
                    effectFile = ".mp4",
                    isUsing = true
                ), CPHouseFullInfo.CPHouseFullPropItem(
                    name = "测试",
                    intimacyScoreLimit = 100,
                    gainType = 4,
                    profit = 100,
                    obtained = false,
                    isUsing = true
                )
            )
        )
    )
}