package com.qyqy.ucoo.compose.presentation.global_chat

import com.qyqy.ucoo.compose.state.PageResult
import com.qyqy.ucoo.compose.state.StateViewModel
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching

class GlobalUserViewModel(private val roomId: String) : StateViewModel<String, GlobalUserInfo>() {
    private val api = createApi<GlobalUserApi>()
    override suspend fun loadPageData(pageNum: String?): Result<PageResult<String, GlobalUserInfo>> {
        val body = buildMap {
            put("room_id", roomId)
            pageNum?.also {
                put("token", pageNum)
            }
        }
        return runApiCatching { api.getUserList(body) }.map {
            if (it.users.isEmpty()) {
                PageResult.NoNext(emptyList())
            } else {
                PageResult.HasNext(it.users, it.nextToken)
            }
        }
    }
}