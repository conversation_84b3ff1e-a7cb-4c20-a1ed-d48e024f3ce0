package com.qyqy.ucoo.compose.presentation.global_chat

import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.http.ApiResponse
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import retrofit2.http.Body
import retrofit2.http.POST


@Serializable
data class GlobalUserInfo(
    val user: AppUser,
    @SerialName("online_state") val onlineState: String,
    @SerialName("last_online_time") val lastOnlineTime: Long = 0,
    val status:Int
)

@Serializable
data class GlobalUserListResult(val users: List<GlobalUserInfo>, @SerialName("next_token") val nextToken: String = "")

interface GlobalUserApi {

    @POST("api/audioroom/v1/globalchat/recent/users")
    suspend fun getUserList(@Body map:Map<String,String>): ApiResponse<GlobalUserListResult>
}