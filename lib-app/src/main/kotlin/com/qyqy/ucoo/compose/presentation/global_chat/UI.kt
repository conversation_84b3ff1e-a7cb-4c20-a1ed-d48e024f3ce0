

package com.qyqy.ucoo.compose.presentation.global_chat

import android.content.Context
import android.os.Bundle
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.ucoo.DataPoint
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.presentation.ComposeScreen.Companion.navigate
import com.qyqy.ucoo.compose.presentation.ScreenNavigator
import com.qyqy.ucoo.compose.presentation.profile.UserProfileNavigator
import com.qyqy.ucoo.compose.presentation.room.SpanText
import com.qyqy.ucoo.compose.presentation.room.buildTextSpan
import com.qyqy.ucoo.compose.presentation.room.inlineTextContent
import com.qyqy.ucoo.compose.preview_providers.UserProvider
import com.qyqy.ucoo.compose.state.StateListView
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.theme.colorWhite50Alpha
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.core.Analytics
import kotlinx.coroutines.CoroutineScope

@Composable
fun GlobalUserItem(modifier: Modifier = Modifier, info: GlobalUserInfo) {
    val user = info.user
    val context = LocalContext.current
    Row(modifier = modifier.clickable {
        UserProfileNavigator.navigate(context, user)
        Analytics.appReportEvent(DataPoint.clickBody("global_chat_userList_cell"))
    }, verticalAlignment = Alignment.CenterVertically) {
        Box(
            modifier = Modifier
                .size(64.dp)
                .padding(10.dp)
        ) {
            CircleComposeImage(model = user.avatarUrl, modifier = Modifier.fillMaxSize(1f))
        }
        Spacer(modifier = Modifier.width(4.dp))
        val density = LocalDensity.current
        SpanText(
            buildTextSpan {
                append(user.nickname)
                append(" ")
                it.appendInlineContent(
                    inlineTextContent(
                        key = "avatar",
                        density = density,
                        width = 20,
                        height = 15,
                    ) { modifier ->
                        ComposeImage(
                            model = user.countryFlag, contentDescription = "countryFlag", modifier = modifier
                        )
                    }
                )
            },
            color = Color.White, fontSize = 15.sp, modifier = Modifier.weight(1f)
        )
        val color = if (info.status == 1) Color(0xFF00B42A) else colorWhite50Alpha
        AppText(text = info.onlineState, fontSize = 12.sp, color = color)
    }
}

@Preview
@Composable
fun GlobalUserItemPreview() {
    GlobalUserItem(info = GlobalUserInfo(UserProvider.user, "在线", status = 1))
}

@Composable
fun GlobalUserList(viewModel: GlobalUserViewModel) {
    val modifier = Modifier.padding(horizontal = 16.dp, vertical = 6.dp)
    LaunchedEffect(key1 = Unit) {
        viewModel.refresh()
    }
    StateListView(
        viewModel,
        keyProvider = { i, globalUserInfo -> "GlobalUserList-${globalUserInfo.user.id}" }) { item: GlobalUserInfo, index: Int, coroutineScope: CoroutineScope, loadingState: MutableState<Boolean> ->
        GlobalUserItem(modifier = modifier, item)
    }
}

object GlobalUserListNavigator : ScreenNavigator {
    private const val KEY_ROOM_ID = "room_id"

    @Composable
    override fun ContentScreen(activity: BaseActivity, bundle: Bundle) {
        AppTheme {
            Column(modifier = Modifier.windowInsetsPadding(WindowInsets.systemBars)) {
                AppTitleBar(
                    title = stringResource(id = R.string.room_online_user),
                    onBack = { activity.onBackPressedDispatcher.onBackPressed() })
                GlobalUserList(viewModel = viewModel {
                    GlobalUserViewModel(activity.intent.getStringExtra(KEY_ROOM_ID).orEmpty())
                })
            }
        }
    }

    fun start(context: Context, roomId: String) {
        this.navigate(context) {
            putExtra(KEY_ROOM_ID, roomId)
        }
    }
}
