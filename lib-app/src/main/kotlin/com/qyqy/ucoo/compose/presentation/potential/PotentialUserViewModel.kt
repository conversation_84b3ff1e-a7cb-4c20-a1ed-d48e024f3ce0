package com.qyqy.ucoo.compose.presentation.potential

import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.compose.state.PageResult
import com.qyqy.ucoo.compose.state.StateViewModel

class PotentialUserViewModel : StateViewModel<String, User>() {
    override suspend fun loadPageData(pageNum: String?): Result<PageResult<String, User>> {
        return Result.failure(NullPointerException(""))
    }
}