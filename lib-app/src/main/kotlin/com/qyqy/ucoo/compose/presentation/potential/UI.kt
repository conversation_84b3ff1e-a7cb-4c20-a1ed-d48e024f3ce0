

package com.qyqy.ucoo.feat.protential

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.isRegionVisible
import com.qyqy.ucoo.account.signatureContent
import com.qyqy.ucoo.compose.presentation.potential.PotentialUserViewModel
import com.qyqy.ucoo.compose.preview_providers.UserProvider
import com.qyqy.ucoo.compose.state.StateListView
import com.qyqy.ucoo.compose.ui.AgeGender
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.UCOOScreen
import com.qyqy.ucoo.widget.custom.UserLevelView

@Composable
fun ProtentialItem(
    title: String = "优质用户推荐",
    desc: String = "周礼物积分2万以上&新人转化A级主播可进",
    onClick: () -> Unit = {}
) {
    Row(
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .clickable(onClick = onClick), verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_protential_avatar),
            contentDescription = "",
            modifier = Modifier.size(64.dp)
        )
        Spacer(modifier = Modifier.width(4.dp))
        Column(
            modifier = Modifier
                .weight(1f)
                .height(64.dp), verticalArrangement = Arrangement.Center
        ) {
            AppText(text = title, fontSize = 15.sp, color = Color(0xFFD638FF))
            Spacer(modifier = Modifier.height(8.dp))
            AppText(text = desc, fontSize = 13.sp, color = Color(0x99D638FF))
        }
        Image(
            painter = painterResource(id = R.drawable.ic_arrow_end_v2),
            contentDescription = "",
        )
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF050505)
@Composable
fun ProtentialItemPreview() {
    ProtentialItem()
}

@Composable
fun ProtentialUserItem(user: User = UserProvider.user, onAvatarClick: () -> Unit = {}, onClick: () -> Unit = {}) {
    Row(
        modifier = Modifier
            .padding(horizontal = 16.dp, vertical = 12.dp)
            .fillMaxWidth(1f), verticalAlignment = Alignment.CenterVertically
    ) {
        CircleComposeImage(model = user.avatarUrl, modifier = Modifier.size(68.dp))
        Spacer(modifier = Modifier.width(8.dp))
        Column(modifier = Modifier.weight(1f), verticalArrangement = Arrangement.Center) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                AppText(
                    text = user.nickname, color = Color.White, fontSize = 16.sp
                )
                Spacer(modifier = Modifier.width(4.dp))
                AgeGender(age = user.age, isBoy = user.isBoy)
                Spacer(modifier = Modifier.width(4.dp))
                AndroidView(factory = {
                    UserLevelView(it, null)
                }, modifier = Modifier.height(20.dp)) {
                    it.setLevel(user.level)
                }
            }
            Spacer(modifier = Modifier.height(6.dp))
            AnimatedVisibility(visible = user.isRegionVisible) {
                AppText(text = user.regionText, fontSize = 12.sp, color = Color(0xA6FFFFFF))
            }
            Spacer(modifier = Modifier.height(8.dp))
            AppText(text = user.signatureContent, fontSize = 12.sp, color = Color(0x59FFFFFF))
        }
        Spacer(modifier = Modifier.width(8.dp))
        Image(painter = painterResource(id = R.drawable.btn_hi), contentDescription = "")
    }
}

val User.regionText: String
    get() = listOf(this.regionLabel, this.regionReasonLabel).filter { it.isNotEmpty() }.joinToString(separator = " | ")

@Preview(showBackground = true, backgroundColor = 0xFF070707)
@Composable
fun ProtentialUserItemPreview() {
    ProtentialUserItem()
}


@Composable
fun PotentialScreen(viewModel:PotentialUserViewModel = viewModel()) {
    UCOOScreen(title = stringResource(R.string.high_quality_user_rec)){
        StateListView(viewModel = viewModel){
            item, index, coroutineScope, loadingState ->
            ProtentialItem()
        }
    }
}