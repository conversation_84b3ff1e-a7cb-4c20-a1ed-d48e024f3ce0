package com.qyqy.ucoo.compose.presentation.redpackage

import androidx.annotation.Keep
import com.qyqy.ucoo.compose.presentation.redpackage.bean.RedPacketDetail
import com.qyqy.ucoo.compose.presentation.redpackage.bean.RedPacketList
import com.qyqy.ucoo.compose.presentation.redpackage.bean.RedPacketResult
import com.qyqy.ucoo.compose.presentation.redpackage.bean.RedPacketSetting
import com.qyqy.ucoo.http.ApiResponse
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

/**
 * [sceneType]
 * 1	部落
 * 2	语音房
 * 3	私密小屋
 * 4	私聊
 * 5	动态
 * 6	个人主页
 * [sceneId] 若为房间场景，则为房间id
 */
@Serializable
data class PostRedPackageParams(
    @SerialName("coin_type")
    val coinType: Int,
    @SerialName("coin_value")
    val coinValue: Int,
    @SerialName("grab_type")
    val grabType: Int,
    val greets: String,
    val number: Int,
    @SerialName("scene_id")
    val sceneId: String,
    @SerialName("scene_type")
    val sceneType: Int,
    @SerialName("delay_duration")
    val delayDuration: Int,
)

@Keep
interface IApiRedPackage {

    suspend fun getRedPacketSettings(): ApiResponse<RedPacketSetting>

    suspend fun postRedPackage(params: PostRedPackageParams): ApiResponse<JsonObject>

    suspend fun grabRedPacket(body: Map<String, String>): ApiResponse<RedPacketResult>

    suspend fun getRedPacketDetail(redPacketId: Int): ApiResponse<RedPacketDetail>

    suspend fun getRedPacketList(
        sceneId: String,
        sceneType: Int
    ): ApiResponse<RedPacketList>

}

@Keep
interface ApiRedPackage : IApiRedPackage {

    @GET("api/redpacket/v1/redpacket/settings")
    override suspend fun getRedPacketSettings(): ApiResponse<RedPacketSetting>

    @POST("api/redpacket/v1/redpacket/create")
    override suspend fun postRedPackage(@Body params: PostRedPackageParams): ApiResponse<JsonObject>

    @POST("api/redpacket/v1/redpacket/grab")
    override suspend fun grabRedPacket(@Body body: Map<String, String>): ApiResponse<RedPacketResult>

    @GET("api/redpacket/v1/redpacket/detail")
    override suspend fun getRedPacketDetail(@Query("red_packet_id") redPacketId: Int): ApiResponse<RedPacketDetail>

    @GET("api/redpacket/v1/redpacket/list")
    override suspend fun getRedPacketList(
        @Query("scene_id") sceneId: String,
        @Query("scene_type") sceneType: Int
    ): ApiResponse<RedPacketList>

}

@Keep
interface JaApiRedPackage : IApiRedPackage {

    @GET("api/japanredpacket/v1/redpacket/settings")
    override suspend fun getRedPacketSettings(): ApiResponse<RedPacketSetting>

    @POST("api/japanredpacket/v1/redpacket/create")
    override suspend fun postRedPackage(@Body params: PostRedPackageParams): ApiResponse<JsonObject>

    @POST("api/japanredpacket/v1/redpacket/grab")
    override suspend fun grabRedPacket(@Body body: Map<String, String>): ApiResponse<RedPacketResult>

    @GET("api/japanredpacket/v1/redpacket/detail")
    override suspend fun getRedPacketDetail(@Query("red_packet_id") redPacketId: Int): ApiResponse<RedPacketDetail>

    @GET("api/japanredpacket/v1/redpacket/list")
    override suspend fun getRedPacketList(
        @Query("scene_id") sceneId: String,
        @Query("scene_type") sceneType: Int
    ): ApiResponse<RedPacketList>

}