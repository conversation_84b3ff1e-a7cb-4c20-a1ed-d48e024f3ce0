package com.qyqy.ucoo.compose.presentation.redpackage

import android.app.Dialog
import android.os.Bundle
import android.view.Gravity
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.ComposeDialogFragment
import com.qyqy.ucoo.base.FinishEffect
import com.qyqy.ucoo.base.UiEffect
import com.qyqy.ucoo.compose.ui.ComposeDialog
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.utils.LogUtil
import com.qyqy.ucoo.widget.dialog.AppBottomSheetDialog
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch


/**
 * 红包信息编辑DialogFragment
 */
class RPEditDialogFragment : ComposeDialogFragment() {


    private val viewModel by activityViewModels<RedPackageViewModel>()

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return AppBottomSheetDialog.Builder(requireContext()).also {
            buildDialog(it)
        }.create()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        LogUtil.d("on edit rp created")
        lifecycleScope.launch {
            viewModel.effect.collectLatest { value: UiEffect ->
                LogUtil.d("rp edit $value")
                when (value) {
                    FinishEffect -> {
                        dismiss()
                    }

                    else -> {}
                }
            }
        }
    }

    override val content: @Composable () -> Unit = {
        RedPackageScreen(
            setting = viewModel.settingFlow,
            delayValueFlow = viewModel.delayValue,
            getConditionFlow = viewModel.flowGetCondition,
            onClickDelayType = {
                viewModel.setDelayValue(it)
            },
            onClickOpenCondition = {
                RPGetConditionDF().show(childFragmentManager, "rp_get_condition")
            }) { goldCount, rpCount, words ->
            val setting = viewModel.settingFlow.value
            if (goldCount < setting.minCoin) {
                toast(getString(R.string.rp_tip_invalid_gold_count, setting.minCoin))
                return@RedPackageScreen
            }
            if (rpCount < setting.minNumber || rpCount > setting.maxNumber) {
                toast(getString(R.string.rp_hint_count, setting.minNumber, setting.maxNumber))
                return@RedPackageScreen
            }
            viewModel.send(goldCount, rpCount, words)
        }
    }
}

class RPGetConditionDF : DialogFragment() {

    private val viewModel by activityViewModels<RedPackageViewModel>()

    init {
        setStyle(STYLE_NORMAL, R.style.AppBaseBottomDialog)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return object : ComposeDialog(requireContext(), R.style.Animation_AppCompat_Dialog_SlideIn) {
            override fun onCreate(savedInstanceState: Bundle?) {
                super.onCreate(savedInstanceState)
                window?.attributes?.also {
                    it.gravity = Gravity.BOTTOM
                    it.width = -1
                }
            }

            @Composable
            override fun Content() {
                val valueList = viewModel.settingFlow.collectAsState().value.grabTypes
                RPOpenCondition(valueList) {
                    val setting = viewModel.settingFlow.value

                    when (it) {
                        Const.GrabType.ONLY_TRIBE_MEMBER -> {
                            if (setting.hasTribe.not()) {
                                toastRes(R.string.rp_toast_no_tribe)
                                return@RPOpenCondition
                            }
                        }

                        Const.GrabType.ONLY_FAMILY_AND_FRIEND -> {
                            if (setting.hasRelationship.not()) {
                                toastRes(R.string.rp_toast_no_ff)
                                return@RPOpenCondition
                            }
                        }

                        else -> {}
                    }
                    if (it != -1) {
                        viewModel.setCondition(it)
                    }
                    dismiss()
                }
            }
        }
    }
}

