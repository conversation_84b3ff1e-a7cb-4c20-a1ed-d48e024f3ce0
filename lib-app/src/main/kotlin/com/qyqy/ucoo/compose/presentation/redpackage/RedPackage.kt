package com.qyqy.ucoo.compose.presentation.redpackage

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusState
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.view.WindowInsetsCompat
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.presentation.redpackage.bean.FieldValue
import com.qyqy.ucoo.compose.presentation.redpackage.bean.RedPacketSetting
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.theme.colorBlackDivider
import com.qyqy.ucoo.compose.theme.colorSecondBlack
import com.qyqy.ucoo.compose.theme.colorWhite30Alpha
import com.qyqy.ucoo.compose.theme.colorWhite50Alpha
import com.qyqy.ucoo.compose.theme.colorWhite80Alpha
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.core.Const
import kotlinx.coroutines.flow.StateFlow

@Composable
fun RedPackageScreen(
    setting: StateFlow<RedPacketSetting>,
    getConditionFlow: StateFlow<Int>,
    delayValueFlow: StateFlow<Int>,
    maxWordsLength: Int = 20,
    onClickDelayType: (Int) -> Unit,
    onClickOpenCondition: () -> Unit = {},
    onPost: (goldCount: Int, rpCount: Int, words: String) -> Unit,
) {
    val settingData = setting.collectAsState().value
    val minCoin = settingData.minCoin
    val goldCountTrigger = settingData.bannerCoin
    val minCount = settingData.minNumber
    val maxCount = settingData.maxNumber
    val delayTypes = settingData.delayTypes
    val delayValue by delayValueFlow.collectAsState()
    val bgModifier = Modifier
        .background(Color(0xFF2D2D2D), Shapes.small)
        .padding(12.dp, 16.dp)
        .fillMaxWidth(1f)
    val keyboardAsState by keyboardAsState()
    val defaultMessage = stringResource(id = R.string.rp_default_msg)
    val stMessage = remember {
        mutableStateOf(TextFieldValue())
    }
    //红包个数
    val stCount = remember {
        mutableStateOf(TextFieldValue())
    }
    val stGoldCount = remember {
        mutableStateOf(TextFieldValue())
    }
    val coinVisible by remember {
        derivedStateOf { stGoldCount.value.text.isNotEmpty() }
    }


    fun getNumber(st: MutableState<TextFieldValue>) = st.value.text.toIntOrNull() ?: 0
    val onClickSend = object : () -> Unit {
        override fun invoke() {
            val goldCount = getNumber(stGoldCount)
            val rpCount = getNumber(stCount)
            val words = stMessage.value.text.takeIf { it.isNotEmpty() } ?: defaultMessage
            onPost(goldCount, rpCount, words)
        }

    }

    fun createOnFocus(st: MutableState<TextFieldValue>): (FocusState) -> Unit {
        return {
            st.value = st.value.copy(text = st.value.text, selection = TextRange(st.value.text.length))
        }
    }

    val onGoldFocusChanged: (FocusState) -> Unit = {
        if (it.isFocused) {
            stGoldCount.value =
                stGoldCount.value.copy(text = stGoldCount.value.text, selection = TextRange(stGoldCount.value.text.length))
        }
    }
    val onCountFocusChanged = createOnFocus(stCount)
    Column(
        modifier = Modifier
            .fillMaxWidth(1f)
            .windowInsetsPadding(WindowInsets.navigationBars)
            .background(colorSecondBlack, RoundedCornerShape(topEnd = 16.dp, topStart = 16.dp))
            .padding(horizontal = 32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        AppText(
            text = stringResource(R.string.title_redpackage),
            color = Color.White,
            modifier = Modifier
                .padding(vertical = 16.dp),
            fontSize = 16.sp
        )
        Spacer(modifier = Modifier.height(8.dp))
        Row(modifier = bgModifier, verticalAlignment = Alignment.CenterVertically) {
            Text(text = stringResource(R.string.rp_total_gold), fontSize = 14.sp, color = Color.White)
            TextFieldWithHint(
                modifier = Modifier.weight(1f),
                textFieldModifier = Modifier.onFocusChanged(onGoldFocusChanged),
                keyboardOptions = KeyboardOptions.Default.copy(keyboardType = KeyboardType.Number),
                textAlign = TextAlign.End,
                textFieldValueState = stGoldCount,
                hint = stringResource(R.string.rp_hint_100_gold, minCoin),
                inputDigit = true
            )
            AnimatedVisibility(visible = coinVisible) {
                Row {
                    Spacer(modifier = Modifier.width(4.dp))
                    Image(
                        painter = painterResource(id = R.drawable.ic_u_coin),
                        contentDescription = "coin",
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
        Row(
            modifier = Modifier
                .fillMaxWidth(1f)
                .padding(vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(id = R.drawable.icon_alert),
                contentDescription = "alert",
                modifier = Modifier.size(12.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(text = stringResource(R.string.format_rp_banner, goldCountTrigger), fontSize = 12.sp, color = colorWhite80Alpha)
        }
//        红包个数
        Row(modifier = bgModifier) {
            Text(text = stringResource(R.string.rp_rp_count), fontSize = 14.sp, color = Color.White)
            TextFieldWithHint(
                modifier = Modifier.weight(1f),
                maxLength = 2,
                textFieldModifier = Modifier.onFocusChanged(onCountFocusChanged),
                keyboardOptions = KeyboardOptions.Default.copy(keyboardType = KeyboardType.Number),
                textAlign = TextAlign.End,
                textFieldValueState = stCount,
                hint = stringResource(R.string.rp_hint_count, minCount, maxCount),
                inputDigit = true
            )
        }
        Spacer(modifier = Modifier.height(12.dp))
        Box(modifier = bgModifier) {
            TextFieldWithHint(textFieldValueState = stMessage, hint = defaultMessage, maxLength = maxWordsLength)
        }
//        选择红包开始条件
        Text(
            text = stringResource(R.string.rp_condition),
            color = colorWhite30Alpha,
            modifier = Modifier.padding(vertical = 24.dp)
        )
        val st by getConditionFlow.collectAsState()
        Row(
            modifier = Modifier
                .clickable(onClick = onClickOpenCondition)
                .then(bgModifier),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(text = stringResource(R.string.rp_user_can_open), color = Color.White)
            Spacer(modifier = Modifier.weight(1f))
            Text(
                text = stringResource(
                    id = when (st) {
                        Const.GrabType.ALL -> R.string.rp_all_people_can_get
                        Const.GrabType.ONLY_TRIBE_MEMBER -> R.string.rp_triber_can_get
                        else -> R.string.rp_ff_get
                    }
                ),
                color = colorWhite50Alpha,
            )
            Image(
                painter = painterResource(id = R.drawable.ic_arrow_end_v2),
                contentDescription = "",
                modifier = Modifier.size(14.dp)
            )
        }
        Spacer(modifier = Modifier.height(12.dp))
        Row {
            Spacer(modifier = Modifier.size(24.dp))
            delayTypes.forEachIndexed { index, it ->
                Row(
                    modifier = Modifier
                        .padding(vertical = 10.dp)
                        .clickable(onClick = {
                            onClickDelayType(it.value)
                        }),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(id = if (delayValue == it.value) R.drawable.ic_rb_selected else R.drawable.ic_rb_unselected),
                        modifier = Modifier.size(24.dp),
                        contentDescription = "rd_sel"
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(text = it.name, color = if (delayValue == it.value) Color.White else colorWhite50Alpha)
                }
                if (index == 0) {
                    Spacer(modifier = Modifier.weight(1f))
                }
            }
            Spacer(modifier = Modifier.size(24.dp))
        }
        Spacer(modifier = Modifier.height(30.dp))
        Text(
            text = stringResource(R.string.dispatch_redpackage),
            modifier = Modifier
                .size(260.dp, 48.dp)
                .clickable(onClick = {
                    val goldCount = stGoldCount.value.text.toIntOrNull() ?: 0
                    val rpCount = stCount.value.text.toIntOrNull() ?: 0
                    val words = stMessage.value.text.takeIf { it.isNotEmpty() } ?: defaultMessage
                    onPost(goldCount, rpCount, words)
                })
                .background(
                    Brush.linearGradient(colors = listOf(0xFFFF7294, 0xFFFF285B).map { Color(it) }),
                    shape = Shapes.large
                ),
            color = Color.White,
            textAlign = TextAlign.Center,
            fontSize = 16.sp,
            lineHeight = 48.sp,
        )
        Spacer(modifier = Modifier.size(8.dp))
        settingData.tips?.forEach {
            Text(text = it, color = colorWhite50Alpha, fontSize = 12.sp)
        }
        Spacer(modifier = Modifier.size(30.dp))
    }
}

@Composable
fun TextFieldWithHint(
    modifier: Modifier = Modifier,
    textFieldModifier: Modifier = Modifier,
    textAlign: TextAlign = TextAlign.Start,
    textFieldValueState: MutableState<TextFieldValue>,
    hint: String,
    maxLength: Int = 500,
    keyboardOptions: KeyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Text),
    hintColor: Color = colorWhite50Alpha,
    textColor: Color = Color(0xFFFFE8AA),
    contentAlignment: Alignment = Alignment.CenterStart,
    inputDigit: Boolean = false,
) {
    val onChanged: (TextFieldValue) -> Unit = { fieldValue ->
        val text = fieldValue.text.let { if (inputDigit) it.filter { txt -> txt.isDigit() } else it }.take(maxLength)
        textFieldValueState.value = fieldValue.copy(text = text)
    }
    val hintVisible by remember {
        derivedStateOf { textFieldValueState.value.text.isEmpty() }
    }
    Box(modifier = modifier, contentAlignment = contentAlignment) {
        BasicTextField(
            value = textFieldValueState.value, onValueChange = onChanged,
            modifier = textFieldModifier
                .fillMaxWidth(1f),
            singleLine = true,
            keyboardOptions = keyboardOptions,
            textStyle = TextStyle.Default.merge(
                textAlign = textAlign,
                color = textColor,
                fontSize = 14.sp,
                lineHeight = 15.sp
            ),
            cursorBrush = SolidColor(Color.White)
        )
        if (hintVisible) {
            Text(
                text = hint,
                color = hintColor,
                textAlign = textAlign,
                lineHeight = 15.sp,
                fontSize = 14.sp,
                modifier = Modifier.fillMaxWidth(1f)
            )
        }
    }
}

@Composable
fun keyboardAsState(): State<Boolean> {
    val isKeyboardOpen = remember { mutableStateOf(false) }
    val view = LocalView.current.rootView
    val focusManager = LocalFocusManager.current
    view.setOnApplyWindowInsetsListener { _, insets ->
        val compatInsets = WindowInsetsCompat.toWindowInsetsCompat(insets)
        val isVisible = compatInsets.getInsets(WindowInsetsCompat.Type.ime()).bottom > 0
        isKeyboardOpen.value = isVisible
        if (!isVisible) {
            focusManager.clearFocus(true)
        }
        insets
    }
    DisposableEffect(key1 = view) {
        onDispose {
            view.setOnApplyWindowInsetsListener(null)
        }
    }
    return isKeyboardOpen
}

@Composable
fun RPOpenCondition(valueList: List<FieldValue>, onItemClick: (Int) -> Unit = {}) {
    val itemModifier = Modifier
        .height(54.dp)
        .fillMaxWidth(1f)
    Column(
        modifier = Modifier
            .fillMaxWidth(1f)
            .background(colorSecondBlack, RoundedCornerShape(topEnd = 16.dp, topStart = 16.dp)),
    ) {
        Text(
            text = stringResource(R.string.rp_title_get_condition),
            modifier = itemModifier,
            lineHeight = 54.sp,
            color = colorWhite50Alpha,
            textAlign = TextAlign.Center
        )

        valueList.forEach {
            HorizontalDivider(color = colorBlackDivider)
            TextItem(text = it.name, modifier = Modifier.clickable(onClick = {
                onItemClick.invoke(it.value)
            }))
        }
//        HorizontalDivider(color = colorBlackDivider)
//        TextItem(text = stringResource(R.string.rp_all_people_can_get), modifier = Modifier.clickable(onClick = {
//            onItemClick.invoke(Const.GrabType.ALL)
//        }))
//        HorizontalDivider(color = colorBlackDivider)
//        TextItem(
//            text = stringResource(R.string.rp_triber_can_get),
//            modifier = Modifier.clickable(onClick = { onItemClick.invoke(Const.GrabType.ONLY_TRIBE_MEMBER) })
//        )
//        HorizontalDivider(color = colorBlackDivider)
//        TextItem(
//            text = stringResource(R.string.rp_ff_get),
//            modifier = Modifier.clickable(onClick = { onItemClick.invoke(Const.GrabType.ONLY_FAMILY_AND_FRIEND) })
//        )


        HorizontalDivider(color = colorBlackDivider, thickness = 8.dp)

        TextItem(
            text = stringResource(id = R.string.rc_cancel),
            modifier = Modifier.clickable(onClick = { onItemClick.invoke(-1) })
        )
        Spacer(modifier = Modifier.height(25.dp))
    }
}

@Composable
fun TextItem(modifier: Modifier = Modifier, text: String) {
    Text(
        text = text,
        modifier = modifier.fillMaxWidth(1f),
        color = Color.White,
        lineHeight = 54.sp,
        fontSize = 16.sp,
        textAlign = TextAlign.Center
    )
}