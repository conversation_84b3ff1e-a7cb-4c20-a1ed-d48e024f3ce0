package com.qyqy.ucoo.compose.presentation.redpackage

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.presentation.redpackage.bean.RedPacketList
import com.qyqy.ucoo.compose.presentation.room.formatTime
import com.qyqy.ucoo.compose.ui.fixCurrentPage
import com.qyqy.ucoo.utils.EntityCallback
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

@Composable
fun RedPackagePendant(state: StateFlow<RedPacketList>, modifier: Modifier = Modifier, onItemClicked: EntityCallback<Int> = {}) {
    val data by state.collectAsState()
    if (data.list.isEmpty()) {
        return
    }
    val pagerState = rememberPagerState {
        data.list.size
    }
    if (data.list.size > 1) {
        LaunchedEffect(key1 = "pager-looper", block = {
            while (true) {
                delay(5000)
                try {
                    pagerState.animateScrollToPage((pagerState.fixCurrentPage + 1) % pagerState.pageCount)
                } catch (e: CancellationException) {
                    throw e
                } catch (_: Exception) {
                }
            }
        })
    }
    HorizontalPager(state = pagerState, modifier = modifier) {
        RedPackageComposItem(data.list[it], onItemClicked)
    }
}

@Composable
fun RedPackageComposItem(item: RedPacketList.RedPackageItem, onItemClicked: EntityCallback<Int>) {
    Column(
        modifier = Modifier
            .fillMaxSize(1f)
            .clickable(onClick = {
                onItemClicked(item.id)
            })
            .paint(painter = painterResource(id = R.drawable.icon_small_redpacket))
    ) {
        Spacer(modifier = Modifier.weight(1f))

        val key1 = item.id
        var text by remember(key1 = key1) {
            mutableStateOf("")
        }
        var remainTime = item.getRemainDuration()
//        LogUtil.d("remain text ---:$text,$remainTime,key1:$key1")
        if (remainTime > 0) {
            LaunchedEffect(key1 = key1, block = {
                while (true) {
                    remainTime = item.getRemainDuration()
                    text = formatTime(remainTime)
//                    LogUtil.d("remain text ---:$text,$remainTime")
                    if (remainTime > 0) {
                        val delay = if (remainTime % 1000 == 0L) {
                            1000
                        } else {
                            remainTime % 1000
                        }
                        delay(delay)
                    } else {
                        text = ""
                        break
                    }
                }
            })
        }
        if (text.isNotEmpty()) {
            Text(
                text = text, color = Color.White, fontSize = 10.sp,
                lineHeight = 12.sp,
                modifier = Modifier
                    .height(12.dp)
                    .padding(horizontal = 3.dp)
                    .background(color = Color(0x80000000), shape = RoundedCornerShape(6.dp))
                    .align(Alignment.CenterHorizontally)
            )
        }
        Spacer(modifier = Modifier.height(2.dp))
    }
}

@Composable
@Preview
fun RedPacketListPreviewer() {
    RedPackagePendant(state = MutableStateFlow(RedPacketList(mutableListOf(RedPacketList.RedPackageItem()))))
}

