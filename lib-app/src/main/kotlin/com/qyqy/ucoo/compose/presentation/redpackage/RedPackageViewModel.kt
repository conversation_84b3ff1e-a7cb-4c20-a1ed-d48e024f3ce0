package com.qyqy.ucoo.compose.presentation.redpackage

import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.base.BaseViewModel
import com.qyqy.ucoo.base.FinishEffect
import com.qyqy.ucoo.base.IdleState
import com.qyqy.ucoo.base.LoadingState
import com.qyqy.ucoo.base.UiEffect
import com.qyqy.ucoo.base.UiEvent
import com.qyqy.ucoo.base.UiState
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.data.RedEnvelope
import com.qyqy.ucoo.compose.data.isCollected
import com.qyqy.ucoo.compose.data.isExpired
import com.qyqy.ucoo.compose.data.isOver
import com.qyqy.ucoo.compose.presentation.redpackage.bean.RedPacketDetail
import com.qyqy.ucoo.compose.presentation.redpackage.bean.RedPacketList
import com.qyqy.ucoo.compose.presentation.redpackage.bean.RedPacketSetting
import com.qyqy.ucoo.compose.presentation.redpackage.usecase.GetRedPacketDetail
import com.qyqy.ucoo.compose.presentation.redpackage.usecase.GetRedPacketList
import com.qyqy.ucoo.compose.presentation.redpackage.usecase.GrabRedPacketUseCase
import com.qyqy.ucoo.compose.presentation.redpackage.usecase.PostRedPackageUseCase
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.SendParams
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.sAppKV
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString

class RedPackageViewModel constructor(
    private val roomId: String,
    private val sendParams: SendParams,
    private val api: IApiRedPackage = createApi<ApiRedPackage>(),
    private val postRedPackageUseCase: PostRedPackageUseCase = PostRedPackageUseCase(api),
    private val getRedPacketDetail: GetRedPacketDetail = GetRedPacketDetail(api),
    private val grabUseCase: GrabRedPacketUseCase = GrabRedPacketUseCase(api),
    private val getRedPacketList: GetRedPacketList = GetRedPacketList(roomId, api = api),
    private val isJapan: Boolean = false,
) : BaseViewModel<UiEvent, UiState, UiEffect>() {

    data class ShowDetailDialogEffect(val id: Int, val state: RedEnvelope) : UiEffect

    override fun createInitialState(): UiState = IdleState

    override fun handleEvent(event: UiEvent) {
    }

    private val _settingFlow = MutableStateFlow(RedPacketSetting())
    val settingFlow = _settingFlow.asStateFlow()

    private val _flowGetCondition: MutableStateFlow<Int> = MutableStateFlow(Const.GrabType.ALL)
    val flowGetCondition = _flowGetCondition.asStateFlow()

    private val _delayTypeValue = MutableStateFlow(0)
    val delayValue = _delayTypeValue.asStateFlow()
    private val channelRedPacket = Channel<RedPacketDetail> { }

    private val _rpEffect = MutableSharedFlow<UiEffect>()
    val rpEffect = _rpEffect.asSharedFlow()

    private val _flowRedPacketList = MutableStateFlow(RedPacketList())
    val flowRedPacketList = _flowRedPacketList.asStateFlow()

    private val mapRedPacketState = mutableMapOf<Int, RedEnvelope>()

    init {
        viewModelScope.launch {
            //rtc 红包弹窗消息
            IMCompatCore.customMessageFlow.collectLatest { msg ->
                if (msg.targetId == sendParams.receiver && msg.cmd == MsgEventCmd.RED_PACKET_START_GRAB) {
                    val detail = msg.getJsonValue<RedPacketDetail>("red_packet") ?: return@collectLatest
                    val toRedEnvelop = detail.toRedEnvelop(isJapan)
                    _rpEffect.emit(ShowDetailDialogEffect(detail.id, toRedEnvelop))
                    cacheRedPacketState(detail.id, toRedEnvelop)
                    requestRedPacketList()
                }
            }
        }
        requestRedPacketList()
        requestSettings()
    }

    private fun cacheRedPacketState(id: Int, toRedEnvelop: RedEnvelope) {
        mapRedPacketState[id] = toRedEnvelop
    }

    private fun requestRedPacketList() {
        viewModelScope.launch {
            val result = getRedPacketList().onSuccess { pList ->
                _flowRedPacketList.emit(pList.copy(list = pList.list.filter { it.status == 1 }))
            }.toastError()
        }
    }


    private fun requestSettings() {
        val keySettings = "rp_settings"
        viewModelScope.launch {
            sAppKV.getString(keySettings)?.also { json ->
                if (json.isNotEmpty()) {
                    runCatching {
                        sAppJson.decodeFromString<RedPacketSetting>(json).also {
                            _settingFlow.emit(it)
                        }
                    }
                }
            }
            runApiCatching {
                api.getRedPacketSettings()
            }.onSuccess {
                _settingFlow.emit(it)
                sAppKV.putString(keySettings, sAppJson.encodeToString(it))
            }
        }
    }

    /**
     * ## 详情
     * 1. 先取[mapRedPacketState]缓存
     * 2. 若过期或已领取则不会请求,其他状态请求
     * 3. 过期则移除挂件
     */
    fun getRedPacketStateFlow(redPacketId: Int): StateFlow<RedEnvelope> {
        val initState = mapRedPacketState[redPacketId] ?: RedEnvelope.Loading
        return flow<RedEnvelope> {
            //缓存状态
            emit(initState)
            // 已领取及已过期，则不必请求红包详情
            val complete = initState.isExpired || initState.isCollected
            if (complete) {
                removeRedPacketPendant(redPacketId)
            }
            if (initState != RedEnvelope.Loading && complete) {
                return@flow
            }
            val result = getRedPacketDetail(redPacketId)
            val detail = result.getOrNull()
            val data = detail?.let {
                val redEnvelope = it.toRedEnvelop(isJapan)
                if (redEnvelope.isOver) {
                    removeRedPacketPendant(redPacketId)
                }
                cacheRedPacketState(it.id, redEnvelope)
                redEnvelope
            } ?: RedEnvelope.None
            emit(data)
        }.stateIn(viewModelScope, SharingStarted.Lazily, initState)
    }


    /**
     * 设置可领取人群类别
     */
    fun setCondition(grabType: Int) {
        viewModelScope.launch {
            _flowGetCondition.emit(grabType)
        }
    }

    /**
     * 设置红包开放延迟
     */
    fun setDelayValue(delayValue: Int) {
        viewModelScope.launch {
            _delayTypeValue.emit(delayValue)
        }
    }

    /**
     * 发送红包
     * 发送完或红包倒计时完会收到[MsgEventCmd.RED_PACKET_CREATED] 消息
     */

    fun send(goldCount: Int, rpCount: Int, words: String) =
        viewModelScope.launch {
            setState {
                LoadingState.Loading
            }
            val result = postRedPackageUseCase.execute(
                PostRedPackageParams(
                    if (isJapan) Const.CoinType.JA_GOLD else Const.CoinType.GOLD,
                    goldCount,
                    _flowGetCondition.value,
                    words,
                    rpCount,
                    roomId,
                    2,
                    _delayTypeValue.value
                )
            )
            if (result.isSuccess) {
                setEffect { FinishEffect }
            } else {
                result.toastError()
            }
            setState {
                LoadingState.Idle
            }
        }



    /**
     * 抢红包
     */
    fun grabRedPacket(id: Int, nickname: String) {
        viewModelScope.launch {
            setState {
                LoadingState.Loading
            }
            val result = grabUseCase(id)
            result.onSuccess { ret ->
                _rpEffect.emit(
                    ShowDetailDialogEffect(
                        id, if (ret.success) {
                            RedEnvelope.Success(ret.coin, nickname)
                        } else {
                            RedEnvelope.Failed
                        }.also {
                            //缓存已领取状态
                            cacheRedPacketState(id, RedEnvelope.AlreadyCollected(ret.coin))
                        }
                    )
                )
                //已领取红包，移除该红包挂件
                removeRedPacketPendant(id)
            }.toastError()
            setState {
                LoadingState.Idle
            }
        }
    }

    private suspend fun removeRedPacketPendant(id: Int) {
        val redPacketList = _flowRedPacketList.value
        _flowRedPacketList.emit(redPacketList.copy(list = redPacketList.list.filter { it.id != id }))
    }

}