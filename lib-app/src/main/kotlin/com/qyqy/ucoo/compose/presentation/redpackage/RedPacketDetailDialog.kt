package com.qyqy.ucoo.compose.presentation.redpackage

import android.view.Window
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.core.os.bundleOf
import androidx.fragment.app.activityViewModels
import com.overseas.common.ext.argument
import com.overseas.common.ext.safeViewLifecycleScope
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.ComposeDialogFragment
import com.qyqy.ucoo.compose.data.RedEnvelope
import com.qyqy.ucoo.compose.presentation.room.RedPacketUI
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.utils.LogUtil
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn

/**
 * # 红包弹窗
 * ## 触发：
 * - 点击语音房红包公屏消息
 * - 点击语音房红包挂件
 * - 收到[MsgEventCmd.RED_PACKAGE_CMD]消息
 *      - 红包刚发出来
 *      - 红包倒计时结束
 */
class RedPacketDetailDialog : ComposeDialogFragment() {


    private val updateStateFlow = MutableStateFlow<RedEnvelope>(RedEnvelope.Loading)
    fun update(state: RedEnvelope) {
        updateStateFlow.value = state
    }

    companion object {
        private const val KEY_REDPACKET_ID = "redpacket_id"
        fun newInstance(redPacketId: Int) = RedPacketDetailDialog().apply {
            arguments = bundleOf(KEY_REDPACKET_ID to redPacketId)
        }
    }

    private val redPacketId: Int by argument(KEY_REDPACKET_ID, 0)
    private val viewModel by activityViewModels<RedPackageViewModel>()

    /**
     * [updateStateFlow]优先
     */
    private val stateFlow by lazy {
        val previewState = updateStateFlow.value
        if (previewState != RedEnvelope.Loading) {
            updateStateFlow
        } else {
            viewModel.getRedPacketStateFlow(redPacketId).combine(updateStateFlow) { f1: RedEnvelope, f2: RedEnvelope ->
                if (f2 == RedEnvelope.None || f2 == RedEnvelope.Loading) {
                    f1
                } else {
                    f2
                }
            }
        }.stateIn(safeViewLifecycleScope, SharingStarted.Eagerly, previewState)
    }


    override fun configWindow(window: Window) {
        super.configWindow(window)
        window.attributes.windowAnimations = R.style.Animation_AppCompat_Dialog_ScaleIn
    }

    override val content: @Composable () -> Unit = {
        val data by stateFlow.collectAsState()
        LogUtil.d("rp state===$data")
        RedPacketUI(data = data,
            onSnatch = {
                val info = data as RedEnvelope.Info
                viewModel.grabRedPacket(redPacketId, info.user.nickname)
            }, onThank = {
//                val info = data as RedEnvelope.Success
//                viewModel.thank(info.senderName)
                dismiss()
            }, onConfirm = {
                dismiss()
            })
    }
}