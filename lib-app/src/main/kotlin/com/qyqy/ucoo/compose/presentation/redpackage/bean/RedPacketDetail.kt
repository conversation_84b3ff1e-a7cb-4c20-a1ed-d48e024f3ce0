package com.qyqy.ucoo.compose.presentation.redpackage.bean


import android.os.Parcelable
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.orInvalid
import com.qyqy.ucoo.app
import com.qyqy.ucoo.compose.data.RedEnvelope
import com.qyqy.ucoo.core.Const
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

/**
 *         红包详情
 *         return {
 *             'id': 122,
 *             'sender': xxx,  # 发红包的用户信息,
 *             'coin_type': 1,  # 金额类型，金币/银币(暂时仅支持金币)
 *             'coin_value': 109,  # 金币数,
 *             'number': 19,  # 红包个数
 *             'remained_number': 10,  # 剩余个数
 *             'greets': '',  # 红包祝福语
 *             'delay_time': 0,  # 红包延迟时间
 *             'open_timestamp': **********,  # 红包开抢时间
 *             'grab_type': 红包领取用户,  # 1: 所有人可领取， 2: 仅部落 3: 仅亲友团
 *             'status': 1,  # 红包状态， # 1: 可领取, 2: 已过期
 *             'has_grab': False,  # 是否领取过该红包
 *             'grab_coin': 19,  # 领取金额
 *             'now_timestamp': xxxx,
 *         }
 */
@Parcelize
@Serializable
data class RedPacketDetail(
    @SerialName("coin_type")
    val coinType: Int = Const.CoinType.GOLD,
    @SerialName("coin_value")
    val coinValue: Int = 0,
    @SerialName("delay_time")
    val delayTime: Int = 0,
    @SerialName("grab_type")
    val grabType: Int = Const.GrabType.ALL,
    val greets: String,
    val id: Int = -1000,
    @SerialName("now_timestamp")
    val nowTimestamp: Int,
    val number: Int,
    @SerialName("open_timestamp")
    val openTimestamp: Long,
    @SerialName("remained_number")
    val remainedNumber: Int,
    val status: Int,
    @SerialName("has_grab")
    val hasGrab: Boolean = false,
    @SerialName("grab_coin")
    val grabCoin: Int = 0,//已抢到红包金额
    val sender: AppUser? = null,
) : Parcelable {
    @IgnoredOnParcel
    @Transient
    val grabTypeRes = when (grabType) {
        2 -> R.string.rp_triber_can_get
        3 -> R.string.rp_ff_get
        else -> R.string.rp_all_people_can_get
    }

    @Transient
    @IgnoredOnParcel
    val isExpired: Boolean
        get() = status == 2

    fun toRedEnvelop(isJapan: Boolean = false): RedEnvelope {
        val detail = this
        if (detail.id == -1000) {
            return RedEnvelope.None
        }
        return if (detail.status == 2) {//已过期
            RedEnvelope.Expired
        } else {
            if (detail.hasGrab) {
                val coinCount = detail.grabCoin
                if (coinCount > 0) {
                    RedEnvelope.AlreadyCollected(coinCount)
                } else {
                    RedEnvelope.Failed
                }
            } else {
                //被别人抢完了
                if (detail.remainedNumber <= 0) {
                    RedEnvelope.Failed
                } else {
                    RedEnvelope.Info(
                        detail.sender.orInvalid,
                        detail.greets,
                        if (isJapan) {
                            when (detail.grabType) {
                                2 -> app.getString(R.string.cpd_rp_triber_can_get)
                                3 -> app.getString(R.string.cpd_rp_ff_get)
                                else -> " "
                            }
                        } else app.getString(detail.grabTypeRes),
                        detail.coinValue,
                        detail.openTimestamp * 1000
                    )
                }
            }
        }
    }
}