package com.qyqy.ucoo.compose.presentation.redpackage.bean


import com.overseas.common.sntp.SNTPManager
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class RedPacketList(
    val list: List<RedPackageItem> = emptyList()
) {
    @Serializable
    data class RedPackageItem(
        @SerialName("coin_value")
        val coinValue: Int = 0,
        @SerialName("delay_time")
        val delayTime: Int = 0,
        @SerialName("grab_type")
        val grabType: Int = 1,
        val id: Int = 0,
        @SerialName("now_timestamp")
        val nowTimestamp: Long = 0,
        val number: Int = 0,
        @SerialName("open_timestamp")
        val openTimestamp: Long = 0L,
        @SerialName("remained_number")
        val remainedNumber: Int = 0,
        val status: Int = 0
    ) {
        private val timestampCanOpen = openTimestamp * 1000

        fun getRemainDuration() = timestampCanOpen - SNTPManager.now()
    }
}