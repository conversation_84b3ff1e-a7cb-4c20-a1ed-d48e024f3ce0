package com.qyqy.ucoo.compose.presentation.redpackage.bean


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class RedPacketSetting(
    @SerialName("delay_types")
    val delayTypes: List<FieldValue> = listOf(),
    @SerialName("grab_types")
    val grabTypes: List<FieldValue> = listOf(),
    @SerialName("max_number")
    val maxNumber: Int = 50,
    @SerialName("min_coin")
    val minCoin: Int = 100,
    @SerialName("min_number")
    val minNumber: Int = 1,
    val bannerCoin: Int = 999,
    @SerialName("has_tribe")
    val hasTribe: Boolean = true,
    @SerialName("has_relationship")
    val hasRelationship: Boolean = true,
    val tips: List<String>? = null,
)

@Serializable
data class FieldValue(
    val name: String = "",
    val value: Int = 0
)