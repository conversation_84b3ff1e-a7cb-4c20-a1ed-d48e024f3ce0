package com.qyqy.ucoo.compose.presentation.redpackage.usecase

import com.qyqy.ucoo.compose.presentation.redpackage.IApiRedPackage
import com.qyqy.ucoo.compose.presentation.redpackage.bean.RedPacketDetail
import com.qyqy.ucoo.http.runApiCatching

class GetRedPacketDetail(private val api: IApiRedPackage) : suspend (Int) -> Result<RedPacketDetail> {
    override suspend fun invoke(p1: Int): Result<RedPacketDetail> {
        return runApiCatching { api.getRedPacketDetail(p1) }
    }
}
