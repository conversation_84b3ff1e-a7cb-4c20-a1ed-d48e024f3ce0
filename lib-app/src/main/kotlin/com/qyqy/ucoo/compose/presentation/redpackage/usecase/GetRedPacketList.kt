package com.qyqy.ucoo.compose.presentation.redpackage.usecase

import com.qyqy.ucoo.compose.presentation.redpackage.ApiRedPackage
import com.qyqy.ucoo.compose.presentation.redpackage.IApiRedPackage
import com.qyqy.ucoo.http.runApiCatching

class GetRedPacketList(private val sceneId: String, private val sceneType: Int = 2, private val api: IApiRedPackage) {
    suspend operator fun invoke() = runApiCatching { api.getRedPacketList(sceneId, sceneType) }
}