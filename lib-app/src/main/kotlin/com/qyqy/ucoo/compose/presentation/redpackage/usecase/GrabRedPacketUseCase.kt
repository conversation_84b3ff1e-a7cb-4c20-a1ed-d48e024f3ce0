package com.qyqy.ucoo.compose.presentation.redpackage.usecase

import com.qyqy.ucoo.compose.presentation.redpackage.ApiRedPackage
import com.qyqy.ucoo.compose.presentation.redpackage.IApiRedPackage
import com.qyqy.ucoo.compose.presentation.redpackage.bean.RedPacketResult
import com.qyqy.ucoo.http.runApiCatching
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonPrimitive

/**
 * 抢红包
 */
class GrabRedPacketUseCase(private val api: IApiRedPackage) : suspend (Int) -> Result<RedPacketResult> {
    override suspend fun invoke(redPacketId: Int): Result<RedPacketResult> {
        return runApiCatching { api.grabRedPacket(mapOf("red_packet_id" to redPacketId.toString())) }
    }
}