package com.qyqy.ucoo.compose.presentation.redpackage.usecase

import com.qyqy.ucoo.compose.presentation.redpackage.ApiRedPackage
import com.qyqy.ucoo.compose.presentation.redpackage.IApiRedPackage
import com.qyqy.ucoo.compose.presentation.redpackage.PostRedPackageParams
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.utils.LogUtil

import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive


class PostRedPackageUseCase(private val api: IApiRedPackage) {

    suspend fun execute(redPackageParams: PostRedPackageParams): Result<Pair<String, Int>> = runApiCatching {
        api.postRedPackage(redPackageParams)
    }.onSuccess {
    }.onFailure {
    }.map {
        val rp = it["red_packet"]?.jsonObject
        val id = rp?.get("id")?.jsonPrimitive?.contentOrNull.orEmpty()
        val timestamp = rp?.get("open_timestamp")?.jsonPrimitive?.intOrNull ?: 0
        id to timestamp
    }

}