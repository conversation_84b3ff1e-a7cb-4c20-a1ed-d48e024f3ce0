package com.qyqy.ucoo.compose.presentation.select_user

import androidx.lifecycle.ViewModel
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.utils.LogUtil
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.jsonPrimitive
import org.json.JSONObject
import retrofit2.http.Body
import retrofit2.http.POST


interface UserActionApi {

    @POST("/api/common/v1/contact/choose")
    suspend fun performAction(@Body map: Map<String, String>): ApiResponse<JsonObject>

}

class UserActionViewModel : ViewModel() {
    private val api = createApi<UserActionApi>()
    suspend fun performAction(actionParams: String, userId: Int): Result<JsonObject> = runApiCatching {
        val result = runCatching {
            val jsonObject = JSONObject(actionParams)
            jsonObject.put("target_userid", userId)
            jsonObject.toString()
        }.getOrNull() ?: actionParams
        LogUtil.d("performAction:$result")
        api.performAction(mapOf("params_text" to result))
    }.onSuccess {
        it["action_toast"]?.jsonPrimitive?.contentOrNull?.also { msg ->
            toast(msg)
        }
    }.toastError()
}