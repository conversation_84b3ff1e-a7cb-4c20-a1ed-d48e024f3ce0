package com.qyqy.ucoo.compose.presentation.select_user

import android.content.Context
import android.graphics.drawable.Drawable
import android.os.Bundle
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.requiredHeight
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.UserInfo
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.composePlaceholderDrawable
import com.qyqy.ucoo.compose.presentation.ComposeScreen.Companion.navigate
import com.qyqy.ucoo.compose.presentation.ScreenNavigator
import com.qyqy.ucoo.compose.presentation.ff.UserItem
import com.qyqy.ucoo.compose.state.StateListView
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.theme.colorPageBackground
import com.qyqy.ucoo.compose.theme.colorTheme
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.runWithLoading
import com.qyqy.ucoo.core.Const
import kotlinx.coroutines.launch

@Composable
fun UserItemWithAction(
    user: UserInfo,
    placeholderDrawable: Drawable = composePlaceholderDrawable(),
    buttonText: String,
    onButtonClick: () -> Unit = {},
) {
    UserItem(user = user, placeholderDrawable) {
        Button(
            onClick = onButtonClick,
            contentPadding = PaddingValues(18.dp, 8.dp),
            colors = ButtonDefaults.buttonColors(containerColor = colorTheme),
            modifier = Modifier.widthIn(78.dp, 120.dp)
        ) {
            Text(
                text = buttonText,
                color = Color.White,
                fontSize = 12.sp,
            )
        }
    }
}

@Composable
fun UserList(
    userType: Int, buttonText: String,
    vm: UserListViewModel = viewModel(factory = viewModelFactory {
        initializer {
            UserListViewModel(userType)
        }
    }, key = "user_list_$userType"),
    onUserClick: (userId: Int) -> Unit,
) {
    val placeholderDrawable = composePlaceholderDrawable()
    StateListView(viewModel = vm) { item, _, _, _ ->
        UserItemWithAction(user = item, buttonText = buttonText, placeholderDrawable = placeholderDrawable) {
            onUserClick(item.userId)
        }
    }
}

object SelectUserNavigator : ScreenNavigator {

    @Composable
    override fun ContentScreen(activity: BaseActivity, bundle: Bundle) {
        SelectUserScreen(
            title = bundle.getString(Const.KEY_TITLE).orEmpty(),
            buttonText = bundle.getString(Const.KEY_BUTTON_TEXT).orEmpty(),
            json = bundle.getString(Const.KEY_PARAMS).orEmpty(),
        ) {
            activity.finish()
        }
    }

    fun navigate(context: Context, title: String?, buttonText: String?, params: String?) {
        navigate(context) {
            putExtra(Const.KEY_TITLE, title)
            putExtra(Const.KEY_BUTTON_TEXT, buttonText)
            putExtra(Const.KEY_PARAMS, params)
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun SelectUserScreen(
    title: String,
    buttonText: String,
    vmAction: UserActionViewModel = viewModel(),
    json: String,
    onBack: () -> Unit,
) {
    val scope = rememberCoroutineScope()

    AppTheme {
        val loadingState = LocalContentLoading.current
        val onUserItemClicked: (Int) -> Unit = { userId ->
            scope.launch {
                loadingState.runWithLoading {
                    runCatching {
                        vmAction.performAction(json, userId)
                    }
                }
            }
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .windowInsetsPadding(WindowInsets.statusBars)
                .background(colorPageBackground)
        ) {
            val scope = rememberCoroutineScope()
            AppTitleBar(title = title, onBack = onBack)

            val pagerState = rememberPagerState(0) {
                2
            }

            val titles = stringArrayResource(id = R.array.array_title_invite_family)
            TabRow(
                modifier = Modifier
                    .fillMaxWidth(0.6f)
                    .align(Alignment.CenterHorizontally),
                selectedTabIndex = pagerState.currentPage,
                containerColor = Color.Transparent,
                contentColor = Color.Transparent,
                indicator = { tabPositions ->
                    val tabPoi = tabPositions[pagerState.currentPage]
                    Box(
                        Modifier
                            .tabIndicatorOffset(tabPoi)
                            .requiredSize(10.dp, 3.dp)
                            .background(Color.White, RoundedCornerShape(1.5.dp)),
                    )
                },
                divider = {}
            ) {
                titles.forEachIndexed { index, title ->
                    Tab(
                        text = {
                            Text(text = title, color = Color.White)
                        },
                        modifier = Modifier.requiredHeight(36.dp),
                        selectedContentColor = Color.White,
                        unselectedContentColor = colorResource(id = R.color.white_alpha_50),
                        selected = pagerState.currentPage == index,
                        onClick = {
                            scope.launch {
                                pagerState.scrollToPage(index, 0f)
                            }
                        })
                }
            }
            HorizontalPager(state = pagerState) {
                when (it) {
                    0 -> UserList(UserListViewModel.TYPE_LATEST, buttonText = buttonText, onUserClick = onUserItemClicked)
                    1 -> UserList(UserListViewModel.TYPE_FRIENDS, buttonText = buttonText, onUserClick = onUserItemClicked)
                    else -> Text(text = "我的好友")
                }
            }

        }
    }
}