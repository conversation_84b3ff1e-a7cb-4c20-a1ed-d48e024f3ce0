package com.qyqy.ucoo.compose.presentation.select_user

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import com.qyqy.ucoo.account.UserInfo
import com.qyqy.ucoo.compose.presentation.ff.FamilyFriendApi
import com.qyqy.ucoo.compose.state.StateViewModelWithIntPage
import com.qyqy.ucoo.http.ApiException
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.user.UserApi
import com.qyqy.ucoo.utils.ComposeState
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonPrimitive

class UserListViewModel(private val type: Int = TYPE_LATEST) : StateViewModelWithIntPage<UserInfo>() {
    companion object {
        const val TYPE_LATEST = 0
        const val TYPE_FRIENDS = 1
        const val TYPE_FOCUS = 2
        const val TYPE_FANS = 3
    }

    private val api = createApi<FamilyFriendApi>()
    private val userApi = createApi<UserApi>()
    private var lastChatId = 0

    init {
        refresh()
    }

    private val _loaded: MutableState<Boolean> = mutableStateOf(false)
    val loaded: ComposeState<Boolean> = _loaded

    override suspend fun loadData(pageNum: Int): Result<List<UserInfo>> {
        when (type) {
            TYPE_LATEST -> {//最近聊天
                val lastId = if (pageNum == firstPage) 0 else lastChatId
                val result = runApiCatching { api.getLatestChatUserList(lastId) }
                return if (result.isSuccess) {
                    val jsonObject = result.getOrThrow()
                    lastChatId = jsonObject["last_chat_id"]?.jsonPrimitive?.intOrNull ?: 0
                    _loaded.value = true
                    jsonObject["users"]?.jsonArray?.let { sAppJson.decodeFromJsonElement<List<UserInfo>>(it) }?.let { Result.success(it) }
                        ?: Result.success(emptyList())
                } else {
                    Result.failure<List<UserInfo>>(result.exceptionOrNull() ?: ApiException(msg = "error")).also {
                        toast(it.exceptionOrNull()?.message ?: "")
                    }
                }
            }

            TYPE_FRIENDS -> {
                val lastId = if (pageNum == firstPage) 0 else listFlow.value.lastOrNull()?.relationId ?: 0
                return runApiCatching {
                    api.getFriendsList(lastId)
                }.mapCatching { obj ->
                    _loaded.value = true
                    obj["friends"]?.jsonArray?.let { sAppJson.decodeFromJsonElement<List<UserInfo>>(it) }.orEmpty()
                }
            }

            TYPE_FOCUS -> {//关注
                val lastId = if (pageNum == firstPage) 0 else listFlow.value.lastOrNull()?.relationId ?: 0
                return runApiCatching {
                    userApi.getFollowerList(lastId)
                }.mapCatching { obj ->
                    _loaded.value = true
                    obj.followees.orEmpty()
                }
            }

            else -> {//粉丝
                val lastId = if (pageNum == firstPage) 0 else listFlow.value.lastOrNull()?.relationId ?: 0
                return runApiCatching {
                    userApi.getFansList(lastId)
                }.mapCatching { obj ->
                    _loaded.value = true
                    obj.followers.orEmpty()
                }
            }
        }
    }
}