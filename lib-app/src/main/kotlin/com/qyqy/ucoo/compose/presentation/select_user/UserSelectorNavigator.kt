package com.qyqy.ucoo.compose.presentation.select_user

import android.content.Context
import android.os.Bundle
import android.os.Parcelable
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.requiredHeight
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.os.bundleOf
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.composePlaceholderDrawable
import com.qyqy.ucoo.compose.presentation.ComposeScreen.Companion.navigate
import com.qyqy.ucoo.compose.presentation.ScreenNavigator
import com.qyqy.ucoo.compose.state.EmptyView
import com.qyqy.ucoo.compose.state.LoadMoreEffect
import com.qyqy.ucoo.compose.state.PullRefreshBox
import com.qyqy.ucoo.compose.state.isLoading
import com.qyqy.ucoo.compose.state.isRefreshing
import com.qyqy.ucoo.compose.theme.colorPageBackground
import com.qyqy.ucoo.compose.theme.colorWhite50Alpha
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.moment.MomentApi
import com.qyqy.ucoo.toast
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize

@Parcelize
data class TabUserList(val tabName: String, val type: Int) : Parcelable

@Parcelize
data class UserSelectParams(
    val tabs: List<TabUserList>,
    val initialTabIndex: Int,
    val title: String,
    val buttonText: String,
    val action: String = "none",
    val extra: Bundle = bundleOf(),
    val finishInvokeSuccess: Boolean = true,
) : Parcelable

object UserSelectorNavigator : ScreenNavigator {

    const val KEY_SHARE_MOMENT = "share_moment"

    fun defaultParams(context: Context) = UserSelectParams(
        tabs = listOf(
            TabUserList(
                context.getString(R.string.text_chat),
                UserListViewModel.TYPE_LATEST
            ),
            TabUserList(
                context.getString(R.string.好友),
                UserListViewModel.TYPE_FRIENDS
            ),
            TabUserList(
                context.getString(R.string.follow),
                UserListViewModel.TYPE_FOCUS
            ),
            TabUserList(
                context.getString(R.string.fans),
                UserListViewModel.TYPE_FANS
            ),
        ),
        initialTabIndex = 0,
        title = context.getString(R.string.select_user_to_share),
        buttonText = context.getString(R.string.send)
    )

    fun start(
        context: Context,
        params: UserSelectParams = defaultParams(context)
    ) {
        navigate(context) {
            putExtra(Const.KEY_PARAMS, params)
        }
    }

    @Composable
    override fun ContentScreen(activity: BaseActivity, bundle: Bundle) {
        val params: UserSelectParams? = activity.intent.let {
            try {
                it.getParcelableExtra<UserSelectParams>(Const.KEY_PARAMS)
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
        if (params == null) {
            activity.finish()
            return
        }
        val loadingState = LocalContentLoading.current

        val scope = rememberCoroutineScope()
        val onUserItemClicked: (Int) -> Unit = { userId ->
            scope.launch {
                loadingState.value = true
                when (params.action) {
                    KEY_SHARE_MOMENT -> {
                        val api = createApi<MomentApi>()
                        runApiCatching {
                            api.share(mapOf("moment_id" to params.extra.getInt(Const.KEY_ID), "target_user_id" to userId))
                        }.toastError()
                            .onSuccess {
                                if (params.finishInvokeSuccess) {
                                    activity.onBackPressedDispatcher.onBackPressed()
                                }
                                toast(activity.getString(R.string.share_success))
                            }
                    }

                    else -> {}
                }
                loadingState.value = false
            }
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .windowInsetsPadding(WindowInsets.statusBars)
                .background(colorPageBackground)
        ) {
            val scope = rememberCoroutineScope()
            val titles = remember {
                params.tabs.map { it.tabName }
            }
            val pagerState = rememberPagerState(params.initialTabIndex) {
                params.tabs.size
            }

            AppTitleBar(title = params.title, onBack = {
                activity.onBackPressedDispatcher.onBackPressed()
            })
            TabRow(
                modifier = Modifier
                    .fillMaxWidth(1f),
                selectedTabIndex = pagerState.currentPage,
                containerColor = Color.Transparent,
                contentColor = Color.Transparent,
                indicator = { tabPositions ->
                    val tabPoi = tabPositions[pagerState.currentPage]
                    Box(
                        Modifier
                            .tabIndicatorOffset(tabPoi)
                            .requiredSize(10.dp, 3.dp)
                            .background(Color.White, RoundedCornerShape(1.5.dp)),
                    )
                },
                divider = {}
            ) {
                titles.forEachIndexed { index, title ->
                    Tab(
                        text = {
                            Text(text = title, color = if (index == pagerState.currentPage) Color.White else colorWhite50Alpha)
                        },
                        modifier = Modifier.requiredHeight(36.dp),
                        selectedContentColor = Color.White,
                        unselectedContentColor = colorResource(id = R.color.white_alpha_50),
                        selected = pagerState.currentPage == index,
                        onClick = {
                            scope.launch {
                                pagerState.scrollToPage(index, 0f)
                            }
                        })
                }
            }
            HorizontalPager(state = pagerState) {
                val type = params.tabs[it].type
                UserListContent(type, buttonText = params.buttonText, onUserClick = onUserItemClicked)
            }
        }
    }
}

@Composable
fun UserListContent(
    userType: Int, buttonText: String,
    vm: UserListViewModel = viewModel(factory = viewModelFactory {
        initializer {
            UserListViewModel(userType)
        }
    }, key = "user_list_$userType"),
    onUserClick: (userId: Int) -> Unit,
) {
    val placeholderDrawable = composePlaceholderDrawable()
    val isRefreshing by vm.isRefreshingFlow.collectAsStateWithLifecycle()
    val state = vm.contentState.current
    val list by vm.listFlow.collectAsStateWithLifecycle()
    val loaded by vm.loaded
    val hasMore by vm.hasMoreFlow.collectAsStateWithLifecycle()
    PullRefreshBox(isRefreshing = isRefreshing, onRefresh = { vm.refresh() }) {
        LazyColumn(modifier = Modifier.fillMaxSize()) {
            if (loaded) {
                if (list.isNotEmpty()) {
                    items(list) { item ->
                        UserItemWithAction(user = item, buttonText = buttonText, placeholderDrawable = placeholderDrawable) {
                            onUserClick(item.userId)
                        }
                    }
                } else {
                    item {
                        EmptyView(
                            Modifier
                                .fillMaxWidth()
                                .aspectRatio(0.75f),
                            iconRes = R.drawable.ic_empty_for_fans,
                            textRes = when (userType) {
                                UserListViewModel.TYPE_FOCUS -> R.string.暂无关注的人
                                UserListViewModel.TYPE_FANS -> R.string.暂无粉丝
                                UserListViewModel.TYPE_FRIENDS -> R.string.暂无好友
                                else -> R.string.暂无推荐内容
                            }
                        )
                    }
                }

                item {
                    LoadMoreEffect(hasMore && (!state.isLoading && !state.isRefreshing), ui = {
                        val color = Color.White
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .heightIn(min = 48.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            if (hasMore) {
                                CircularProgressIndicator(modifier = Modifier.size(24.dp), color = color)
                            } else {
                                if (list.size > 10) {
                                    Text(text = stringResource(id = R.string.no_more_data), color = color, fontSize = 12.sp)
                                }
                            }
                        }
                    }, onLoadMore = {
                        vm.loadMore()
                    })
                }
            }

        }
    }
}