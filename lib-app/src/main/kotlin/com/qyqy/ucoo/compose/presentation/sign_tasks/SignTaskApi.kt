package com.qyqy.ucoo.compose.presentation.sign_tasks

import com.qyqy.ucoo.http.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface SignTaskApi {


    @GET("api/itasks/v1/tasks/check_in/info")
    suspend fun getSignInfo(): ApiResponse<JsonObject>

    @GET("api/itasks/v1/tasks/info")
    suspend fun getTasks(): ApiResponse<SignTasks>

    @GET("api/itasks/v1/tasks/info")
    suspend fun getTasksWithParams(@Query("hide_finished_task") hideFinishedTask: Boolean,@Query("prize_type") prizeType: Int): ApiResponse<SignTasks>

    @POST("api/itasks/v1/tasks/check_in")
    suspend fun doSign(@Body body: Map<String, String>): ApiResponse<JsonObject>

    @GET("api/itasks/v1/tasks/to_finish/info")
    suspend fun toFinishTask(@Query("task_id") taskId: Int): ApiResponse<JsonObject>

    @GET("api/itasks/v1/tasks/entry/info")
    suspend fun getSignEntryInfo():ApiResponse<JsonObject>
}