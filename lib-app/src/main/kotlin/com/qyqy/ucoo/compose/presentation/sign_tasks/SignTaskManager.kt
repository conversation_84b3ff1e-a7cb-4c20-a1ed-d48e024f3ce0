package com.qyqy.ucoo.compose.presentation.sign_tasks

import android.app.Activity
import android.content.Context
import android.net.Uri
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.overseas.common.ext.doOnLifecycleEvent
import com.qyqy.ucoo.ActivityLifecycle
import com.qyqy.ucoo.AppInfo
import com.qyqy.ucoo.account.AccountToken
import com.qyqy.ucoo.app
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.presentation.welfare_center.SignAlertDialog
import com.qyqy.ucoo.compose.presentation.welfare_center.TaskCompleteDialog
import com.qyqy.ucoo.core.AppEventListener
import com.qyqy.ucoo.core.AppEventManager
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.bean.Room
import com.qyqy.ucoo.im.bean.isPrivate
import com.qyqy.ucoo.sIsLoginFlow
import com.qyqy.ucoo.sUserKV
import com.qyqy.ucoo.utils.DateChangeFlow
import com.qyqy.ucoo.utils.taskflow.taskShowWithStack
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.booleanOrNull
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonPrimitive
import kotlin.time.DurationUnit
import kotlin.time.toDuration

object SignTaskManager : AppEventListener {

    private val api = createApi<SignTaskApi>()

    //签到任务
    private val _signInfo: MutableStateFlow<TaskSeries> = MutableStateFlow(TaskSeries())
    val signInfo = _signInfo.asStateFlow()

    //新人任务
    private val _newbieTask: MutableStateFlow<TaskSeries> = MutableStateFlow(TaskSeries())
    val newbieTask = _newbieTask.asStateFlow()

    private val signTaskVisibleFlow = MutableStateFlow(JsonObject(emptyMap()))

    //新人任务入口是否可见
    val newbieVisibleFlow = signTaskVisibleFlow.map { it["show_newbie_tasks_entry"]?.jsonPrimitive?.booleanOrNull ?: false }

    //签到任务挂件是否可见
    val signVisibleFlow = signTaskVisibleFlow.map { it["show_check_in_task_entry"]?.jsonPrimitive?.booleanOrNull ?: false }

    private val firstEnterHomePageFlow = MutableSharedFlow<Boolean>()

    private const val KEY_LEAVE_PRIVATE_ROOM = "key_first_leave_private_room"

    //是否离开过私密小屋
    private val flowLeavePrivateRoom = MutableSharedFlow<Boolean>()

    fun refreshTasks(coroutineScope: CoroutineScope = appCoroutineScope) {
        coroutineScope.launch {
            runApiCatching { api.getTasks() }
                .onSuccess { tasks ->
                    val signTask = tasks.taskSeriesList.firstOrNull { it.seriesType == TaskSeries.SERIES_TYPE_SIGN }
                    val newbieTask = tasks.taskSeriesList.firstOrNull { it.seriesType == TaskSeries.SERIES_TYPE_ONCE_TASK }
                    signTask?.let {
                        _signInfo.emit(it)
                    }
                    newbieTask?.let {
                        _newbieTask.emit(it)
                    }
                }
        }
    }

    private fun refreshSignInfo(coroutineScope: CoroutineScope = appCoroutineScope) {
        coroutineScope.launch {
            requestSignTask()
        }
    }

    private suspend fun requestSignTask() = runApiCatching { api.getSignInfo() }.onSuccess { obj ->
        //未命中字段可能为空
        obj.takeIf {
            it.containsKey("series_id") && it.containsKey("today_finished")
        }?.let {
            kotlin.runCatching {
                _signInfo.emit(
                    sAppJson.decodeFromJsonElement(it)
                )
            }
        }
    }

    suspend fun startSign(seriesId: Int) = runApiCatching {
        val params = mutableMapOf("series_id" to seriesId.toString())
        val smId = AppInfo.smBoxId
        if (!smId.isNullOrEmpty()) {
            params["ism_device_id"] = smId
        }
        api.doSign(params)
    }.onSuccess { obj ->
        getSignReward(obj)?.let {
            ActivityLifecycle.topActivity?.also { act ->
                TaskCompleteDialog(act, it).show()
            }
        }
        refreshSignInfo()
    }.toastError()

    private fun getSignReward(jsonObject: JsonObject): String? = jsonObject["success_message"]?.jsonPrimitive?.contentOrNull

    fun observerSignTask(lifecycleOwner: LifecycleOwner, activity: Activity) {
        //请求任物列表，控制签到，新人任务入口可见性
        val flow = sIsLoginFlow.combine(DateChangeFlow(lifecycleOwner, activity)) { isLogin: Boolean, b: Long ->
            if (isLogin) {
                appCoroutineScope.launch {
                    runApiCatching { api.getSignEntryInfo() }.getOrNull()?.let {
                        signTaskVisibleFlow.emit(it)
                        val show = it["show_newbie_tasks_entry"]?.jsonPrimitive?.booleanOrNull ?: false
                        if (show) {
                            refreshTasks()
                        }
                    }
                }
            }
        }
        with(lifecycleOwner) {
            AppEventManager.addListener(this@SignTaskManager)
            doOnLifecycleEvent {
                if (it == Lifecycle.Event.ON_DESTROY) {
                    AppEventManager.removeListener(this@SignTaskManager)
                }
            }

            lifecycleScope.launch {
                launch {
                    flow.collect()
                }
                //签到弹窗展示
                launch {
                    combine(
                        signInfo.filter { it.todayFinished.not() },
                        flowLeavePrivateRoom,
                        firstEnterHomePageFlow
                    ) { f1, leavePrivateRoom, firstEnterHomePage ->
                        if (leavePrivateRoom || (firstEnterHomePage && getFirstLeavePrivateRoomState())) f1 else null
                    }.distinctUntilChanged()
                        .collectLatest {
                            it ?: return@collectLatest
                            delay(500)
                            ActivityLifecycle.topActivity?.run {
                                SignAlertDialog(this, it).taskShowWithStack()
                            }
                        }
                }
            }
        }
    }

    private val mapLinks = mutableMapOf<Int, Uri?>()
    suspend fun gotoCompleteTask(context: Context, taskId: Int) {
        mapLinks[taskId]?.let {
            AppLinkManager.open(context, it)
        } ?: runApiCatching { api.toFinishTask(taskId) }
            .onSuccess {
                val uri = it["jump_link"]?.jsonPrimitive?.contentOrNull?.let { t -> Uri.parse(t) }
                mapLinks[taskId] = uri
                AppLinkManager.open(context, uri)
            }
    }

    private fun isSameDay(t1: Long, t2: Long): Boolean {
        val after = maxOf(t1, t2)
        val before = minOf(t1, t2)
        return (after.toDuration(DurationUnit.MILLISECONDS) - before.toDuration(DurationUnit.MILLISECONDS) - 1.toDuration(
            DurationUnit.DAYS
        )).isNegative()
    }

    override fun onEnterAppMainPage() {
        appCoroutineScope.launch {
            val enterHomePageKey = "enter_home_page_time"
            val last = sUserKV.getLong(enterHomePageKey)
            val current = System.currentTimeMillis()
            if (!isSameDay(last, current)) {
                sUserKV.putLong(enterHomePageKey, current)
                firstEnterHomePageFlow.emit(true)
            }
        }
    }

    override fun onLeaveRoom(room: Room) {
        if (room.isPrivate) {
            appCoroutineScope.launch {
                if (!getFirstLeavePrivateRoomState()) {
                    flowLeavePrivateRoom.emit(true)
                    sUserKV.putBoolean(KEY_LEAVE_PRIVATE_ROOM, true)
                }
            }
        }
    }

    private fun getFirstLeavePrivateRoomState() = sUserKV.getBoolean(KEY_LEAVE_PRIVATE_ROOM, false)
}