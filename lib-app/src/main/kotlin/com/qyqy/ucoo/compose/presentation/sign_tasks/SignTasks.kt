package com.qyqy.ucoo.compose.presentation.sign_tasks


import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class SignTasks(
    @SerialName("task_series_list")
    val taskSeriesList: List<TaskSeries>,
)

@Parcelize
@Serializable
data class TaskSeries(
    @SerialName("all_finished")
    val allFinished: Boolean = true,
    @SerialName("series_id")
    val seriesId: Int = 0,
    @SerialName("series_title")
    val seriesTitle: String = "",
    @SerialName("series_type")
    val seriesType: Int = 0,// 1: 周期性任务(预留类型),  2: 一次性任务, 3: 连续签到(一次性)任务
    @SerialName("prize_type")
    val prizeType: Int = 0,// 5 = 日本金币   7 = 日本钻石
    @SerialName("tasks")
    val tasks: List<Task> = emptyList(),
    @SerialName("today_finished")
    val todayFinished: Boolean = true,
    @SerialName("hide_finished_task")
    val hideFinishedTask: Boolean = true,
    @SerialName("treasure_totips")
    val treasureTotips: String = "",
    @SerialName("finished_totips")
    val finishedTotips: String = "",
    @SerialName("multiplier_help")
    val lineHelp: String = "",
) : Parcelable {
    companion object {
        const val SERIES_TYPE_ONCE_TASK = 2
        const val SERIES_TYPE_SIGN = 3
        const val SERIES_TYPE_DAILY = 4
    }
}

@Parcelize
@Serializable
data class Task(
    @SerialName("condition_times")
    val conditionTimes: Int,//# 完成条件的次数或时长(单位:s)
    @SerialName("condition_type")
    val conditionType: Int,// # 完成条件, (1, 签到'), (2, '语音房上麦'), (3, '赠送他人礼物'), (4, '语音匹配通话')
    @SerialName("desc")
    val desc: String,
    @SerialName("extra")
    val extra: Extra,
    @SerialName("finished")
    val finished: Boolean,
    @SerialName("id")
    val id: Int,
    @SerialName("prize")
    val prize: String,
    @SerialName("prize_type")
    val prizeType: Int = 1,
    @SerialName("progress")
    val progress: String,
    @SerialName("title")
    val title: String,
) : Parcelable {
    val bigSpan: Boolean = extra.bigGoldBox
}

@Parcelize
@Serializable
data class Extra(
    @SerialName("big_gold_box")
    val bigGoldBox: Boolean = false,
    @SerialName("prize_icon")
    val prizeIcon: String = "",
    @SerialName("task_icon")
    val taskIcon: String = "",
    @SerialName("hint")
    val hint: String? = null,
) : Parcelable