package com.qyqy.ucoo.compose.presentation.translation

import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.isProd
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query
import retrofit2.http.Url
import java.io.File

private val baseTranslateUrl: String
    get() = if (isProd) Const.HttpUrl.TRANSLATE_BASE_PROD_URL else Const.HttpUrl.TRANSLATE_BASE_TEST_URL

private val String.translateUrl: String
    get() {
        return if (this.startsWith(File.separator)) {
            "$baseTranslateUrl$this"
        } else {
            "$baseTranslateUrl/$this"
        }
    }

interface TranslateApi {
    @POST
    suspend fun translate(
        @Body map: Map<String, String>,
        @Url url: String = "translate/v1/translate/text".translateUrl,
    ): ApiResponse<JsonObject>

    @GET("api/translator/v1/translate/latest")
    suspend fun queryLanguagePackage(@Query("language_code") code: String): ApiResponse<JsonObject>
}
