package com.qyqy.ucoo.compose.presentation.video_edit

import android.net.Uri
import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.annotation.OptIn
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.common.util.UnstableApi
import com.overseas.common.utils.layoutInflater
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.databinding.LayoutMyVideoBinding
import com.qyqy.ucoo.utils.LogUtil

@OptIn(UnstableApi::class)
@Composable
fun PreviewVideoScreen(url: String) {
    val onBackPressedDispatcher = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher
    val lifecycleOwner = androidx.lifecycle.compose.LocalLifecycleOwner.current
    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        AndroidView(
            factory = {
                LayoutMyVideoBinding.inflate(it.layoutInflater).root
            },
            modifier = Modifier.fillMaxSize(),
        ) {
            if (!it.hasSource()) {
                it.isLoop = true
                it.autoPlay = true
                it.enableController = true
                it.bindLifecycle(lifecycleOwner)
                it.setSource(Uri.parse(url))
            }
        }

        AppTitleBar(
            title = "",
            modifier = Modifier.systemBarsPadding(),
            onBack = { onBackPressedDispatcher?.onBackPressed() }
        )
    }
}
