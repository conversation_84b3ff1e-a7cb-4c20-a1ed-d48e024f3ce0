package com.qyqy.ucoo.compose.presentation.video_edit

import android.os.Bundle
import androidx.compose.runtime.Composable
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.presentation.ScreenNavigator
import com.qyqy.ucoo.compose.ui.UCOOScreen
import com.qyqy.ucoo.compose.ui.video.XVideoViewWidget
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.mine.decodeUrl

object PublishedVideoShowsNavigator : ScreenNavigator {

    @Composable
    override fun ContentScreen(activity: BaseActivity, bundle: Bundle) {
        val url = bundle.getString(Const.KEY_URL)
        val version = bundle.getInt("player_version", 0)
        val autoPlay = bundle.getBoolean("autoPlay", false)
//        val urls = bundle.getStringArrayList("key_video_urls")
        when (version) {
            1 -> {
                UCOOScreen(title = "") {
                    XVideoViewWidget(url?.decodeUrl.orEmpty(), autoPlay)
                }
            }

            else -> {
                PreviewVideoScreen(url?.decodeUrl.orEmpty())
            }
        }
    }
}