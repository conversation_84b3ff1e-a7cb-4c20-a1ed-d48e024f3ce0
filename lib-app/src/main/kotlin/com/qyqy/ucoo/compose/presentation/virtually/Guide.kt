package com.qyqy.ucoo.compose.presentation.virtually

import android.graphics.drawable.Drawable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.MutatePriority
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.bumptech.glide.integration.compose.placeholder
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.composePlaceholderDrawable
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.Loading
import com.qyqy.ucoo.compose.ui.lazy.autoScroll
import com.qyqy.ucoo.compose.vm.virtually.VirtuallyMvi
import kotlinx.coroutines.isActive


@Composable
fun BoxScope.GuidePage(uiState: VirtuallyMvi.ViewState, onSelectChanged: (Int) -> Unit = {}, onCloseMatch: () -> Unit = {}, onCancelMatch: () -> Unit = {}) {

    val list = uiState.info.tags

    IconButton(
        onClick = onCloseMatch, modifier = Modifier
            .align(alignment = Alignment.TopEnd)
            .padding(top = 24.dp, end = 16.dp)
            .size(24.dp, 24.dp)
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_match_guide_cancel),
            contentDescription = null,
            modifier = Modifier
                .size(24.dp, 24.dp),
        )
    }

    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {

        Image(
            painter = painterResource(id = R.drawable.ic_virtual_match_title),
            contentDescription = null,
            modifier = Modifier
                .padding(top = 24.dp)
                .size(284.dp, 114.dp),
        )

        val state = rememberLazyListState()

        LazyRow(
            modifier = Modifier
                .padding(top = 18.dp)
                .fillMaxWidth(),
            state = state,
            contentPadding = PaddingValues(horizontal = 16.dp),
            horizontalArrangement = Arrangement.Center,
        ) {
            itemsIndexed(list, key = { _, item ->
                "GuidePage-${item.id}"
            }) { index, item ->
                val selected = item.id == uiState.tagId
                ComposeImage(
                    model = if (selected) item.selectedImgUrl else item.imgUrl,
                    modifier = Modifier
                        .padding(
                            if (index.rem(2) == 0) PaddingValues(bottom = 48.dp) else PaddingValues(top = 48.dp)
                        )
                        .size(96.dp, 376.dp)
                        .noEffectClickable {
                            if (item.id != uiState.tagId) {
                                onSelectChanged(item.id)
                            }
                        },
                    loading = when {
                        selected && item.drawable != null -> {
                            placeholder(item.drawable ?: composePlaceholderDrawable())
                        }

                        !selected && item.selectedDrawable != null -> {
                            placeholder(item.selectedDrawable ?: composePlaceholderDrawable())
                        }

                        else -> placeholder(composePlaceholderDrawable())
                    },
                    requestBuilderTransform = {
                        it.addListener(object : RequestListener<Drawable> {
                            override fun onLoadFailed(e: GlideException?, model: Any?, target: Target<Drawable>, isFirstResource: Boolean): Boolean {
                                return false
                            }

                            override fun onResourceReady(
                                resource: Drawable,
                                model: Any,
                                target: Target<Drawable>?,
                                dataSource: DataSource,
                                isFirstResource: Boolean
                            ): Boolean {
                                if (selected) {
                                    item.selectedDrawable = resource
                                } else {
                                    item.drawable = resource
                                }
                                return false
                            }
                        })
                    }
                )
            }
        }

        LaunchedEffect(Unit) {
            while (isActive && state.canScrollForward) {
                state.autoScroll(scrollPriority = MutatePriority.UserInput, dx = 16f, animationSpec = tween(durationMillis = 300, easing = LinearEasing))
            }
        }
    }

    if (uiState.showLoading) {
        Loading(onDismissRequest = onCancelMatch)
    }
}
