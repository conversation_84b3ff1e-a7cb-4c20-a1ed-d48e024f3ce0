package com.qyqy.ucoo.compose.presentation.virtually

import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.ucoo.R
import com.qyqy.ucoo.asActivity
import com.qyqy.ucoo.compose.data.Tag
import com.qyqy.ucoo.compose.data.VirtualMatchInfo
import com.qyqy.ucoo.compose.ui.CommonButton
import com.qyqy.ucoo.compose.vm.virtually.VirtualMatchViewModel
import com.qyqy.ucoo.compose.vm.virtually.VirtuallyMvi.ViewEffect
import com.qyqy.ucoo.compose.vm.virtually.VirtuallyMvi.ViewState
import com.qyqy.ucoo.im.chat.ChatActivity
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach


@Composable
fun MainScreenRouter() {

    val context = LocalContext.current
    val viewModel = viewModel<VirtualMatchViewModel>()

    LaunchedEffect(key1 = Unit) {
        viewModel.effects.onEach {
            when (it) {
                is ViewEffect.MatchSuccess -> {
                    ChatActivity.startChatWithUser(context, it.user)
                }

                is ViewEffect.Finish -> {
                    context.asActivity?.finish()
                }
            }
        }.launchIn(this)
    }

    MainScreen(
        viewModel.states.collectAsStateWithLifecycle().value,
        viewModel::select,
        viewModel::startMatch,
        viewModel::close,
        viewModel::cancel,
    )
}

@Composable
fun MainScreen(
    uiState: ViewState,
    onSelectChanged: (Int) -> Unit = {},
    onStartMatch: () -> Unit = {},
    onCloseMatch: () -> Unit = {},
    onCancelMatch: () -> Unit = {},
) {
    VirtualScaffold(
        maskColor = if (uiState.step != 2) null else Color(0x66000000),
        content = {
            if (uiState.step != 2) {
                GuidePage(uiState, onSelectChanged, onCloseMatch, onCancelMatch)
            } else {
                MatchPage(uiState.info, onCloseMatch)
            }
        }
    ) {
        CommonButton(
            modifier = Modifier.size(260.dp, 44.dp),
            gradient = Brush.verticalGradient(
                listOf(Color(0xFF76E6FE), Color(0xFF945EFF))
            ),
            shape = CircleShape,
            onClick = {
                if (uiState.step == 0) {
                    onStartMatch()
                } else {
                    onCloseMatch()
                }
            }
        ) {

            Text(
                text = buildAnnotatedString {
                    when {
                        uiState.step == 0 && uiState.info.matchCost <= 0 -> {
                            append(stringResource(id = R.string.开始匹配))
                        }

                        uiState.step == 0 && uiState.info.matchCost > 0 -> {
                            append(stringResource(id = R.string.开始匹配))
                            withStyle(
                                style = SpanStyle(
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Normal,
                                )
                            ) {
                                append(" ")
                                append(stringResource(id = R.string.开始匹配金币, uiState.info.matchCost))
                            }
                        }

                        else -> {
                            append(stringResource(id = R.string.取消匹配))
                        }
                    }
                },
                color = Color.White,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
            )
        }
    }
}


@Preview(widthDp = 375, heightDp = 812)
@Composable
fun Preview() {
    MainScreen(
        ViewState(
            step = 1,
            info = VirtualMatchInfo(
                tags = List(6) {
                    Tag(it)
                },
                users = List(6) { "" },
            )
        )
    )
}