package com.qyqy.ucoo.compose.presentation.virtually

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.fadeIn
import androidx.compose.animation.scaleIn
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.ConstraintSet
import androidx.constraintlayout.compose.Dimension
import androidx.core.graphics.toColorInt
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import com.overseas.common.utils.dp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.data.VirtualMatchInfo
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeAndroidView
import com.qyqy.ucoo.widget.RadarView
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.random.Random


@Composable
fun BoxScope.MatchPage(info: VirtualMatchInfo, onCloseMatch: () -> Unit = {}) {
    val lifecycleOwner = androidx.lifecycle.compose.LocalLifecycleOwner.current

    var isActive by remember {
        mutableStateOf(false)
    }

    DisposableEffect(key1 = Unit, effect = {

        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                isActive = true
            } else if (event == Lifecycle.Event.ON_PAUSE) {
                isActive = false
            }
        }

        // Add the observer to the lifecycle
        lifecycleOwner.lifecycle.addObserver(observer)

        // When the effect leaves the Composition, remove the observer
        onDispose {
            isActive = false
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    })

    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        AppTitleBar(title = stringResource(id = R.string.虚拟女友匹配), onBack = onCloseMatch)

        MainContent(info.users) {
            isActive
        }
    }
}


@Composable
private fun MainContent(users: List<String>, isActive: () -> Boolean) {

    val visibleList = remember {
        mutableStateListOf<Int>()
    }

    LaunchedEffect(key1 = Unit, block = {
        repeat(8) { // 最多8个头像
            launch {
                // 每次随机等待0.5s ~ 4500s
                delay(Random.nextLong(500, 4500))
                val size = visibleList.size
                if (size >= users.size) {
                    return@launch
                }
                // 每次在所有未点亮的头像随机一个点亮
                val next = Random.nextInt(8.minus(size))
                var i = 0
                for (index in 0..8) {
                    if (index !in visibleList) {
                        if (next == i) { // 跳过所有已点亮的，找到对应未点亮下标的头像
                            visibleList.add(index)
                            break
                        }
                        i++
                    }
                }
            }
        }
    })

    val constraints = decoupledConstraints()

    ConstraintLayout(constraintSet = constraints, modifier = Modifier.fillMaxSize()) {

        ComposeAndroidView(
            provider = isActive,
            factory = {
                RadarView(it).apply {
                    setColor(resources.getColor(R.color.white_alpha_30))
                    mIsAlpha = true
                    mMinRadius = 40.dp
                    mSpace = 50.dp
                    mSpeed = 0.75f.dp
                    mSweepColor = "#FF7BE5FF".toColorInt()
                    mSweepRadius = 170.dp
                    mSweepSpeed = 5f
                }
            },
            modifier = Modifier
                .layoutId("radar")
                .requiredSize(426.dp, 375.dp)
        ) { view, isActive ->
            if (isActive) {
                view.startSearch()
            } else {
                view.stopSearch()
            }
        }

        CircleComposeImage(
            model = userForPreview.avatarUrl,
            modifier = Modifier
                .size(88.dp)
                .layoutId("avatar")
                .border(4.dp, Color(0xFF7BE5FF), CircleShape)
        )

        Text(
            text = stringResource(R.string.正在为你匹配),
            modifier = Modifier.layoutId("hint"),
            fontSize = 18.sp,
            color = Color.White,
            fontWeight = FontWeight.Medium
        )

        repeat(8) {
            val index = visibleList.indexOf(it)
            AnimatedVisibility(
                visible = index != -1,
                modifier = Modifier.layoutId("a${it.plus(1)}"),
                enter = fadeIn() + scaleIn(animationSpec = spring(dampingRatio = Spring.DampingRatioLowBouncy, stiffness = Spring.StiffnessMediumLow))
            ) {
                CircleComposeImage(
                    model = if (index != -1) {
                        users[index]
                    } else {
                        R.drawable.ic_default_male_selected
                    },
                    modifier = Modifier
                        .fillMaxSize()
                        .border(1.dp, Color.White, CircleShape),
                )
            }
        }
    }
}

private fun decoupledConstraints(): ConstraintSet {
    return ConstraintSet {
        val radar = createRefFor("radar")
        val avatar = createRefFor("avatar")
        val hint = createRefFor("hint")

        constrain(radar) {
            centerHorizontallyTo(parent)
            bottom.linkTo(anchor = parent.bottom, margin = 50.dp)
        }

        constrain(avatar) {
            centerTo(radar)
        }

        constrain(hint) {
            centerHorizontallyTo(radar)
            top.linkTo(radar.bottom, (-8).dp)
        }

        val (a1, a2, a3, a4, a5, a6, a7, a8) = createRefsFor("a1", "a2", "a3", "a4", "a5", "a6", "a7", "a8")

        constrain(a1) {
            circular(avatar, -25f, 207.dp)
            width = Dimension.value(48.dp)
            height = Dimension.value(48.dp)
        }

        constrain(a2) {
            circular(avatar, -25f, 90.dp)
            width = Dimension.value(48.dp)
            height = Dimension.value(48.dp)
        }

        constrain(a3) {
            circular(avatar, 25f, 230.dp)
            width = Dimension.value(56.dp)
            height = Dimension.value(56.dp)
        }

        constrain(a4) {
            circular(avatar, 45f, 130.dp)
            width = Dimension.value(64.dp)
            height = Dimension.value(64.dp)
        }

        constrain(a5) {
            circular(avatar, 110f, 130.dp)
            width = Dimension.value(40.dp)
            height = Dimension.value(40.dp)
        }

        constrain(a6) {
            circular(avatar, 135f, 170.dp)
            width = Dimension.value(48.dp)
            height = Dimension.value(48.dp)
        }

        constrain(a7) {
            circular(avatar, -90f, 138.dp)
            width = Dimension.value(56.dp)
            height = Dimension.value(56.dp)
        }

        constrain(a8) {
            circular(avatar, -145f, 150.dp)
            width = Dimension.value(64.dp)
            height = Dimension.value(64.dp)
        }
    }
}