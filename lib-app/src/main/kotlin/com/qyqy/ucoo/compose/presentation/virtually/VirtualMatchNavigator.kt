package com.qyqy.ucoo.compose.presentation.virtually

import android.content.Context
import android.os.Bundle
import androidx.compose.runtime.Composable
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.data.VirtualMatchInfo
import com.qyqy.ucoo.compose.presentation.ComposeScreen.Companion.obtainIntent
import com.qyqy.ucoo.compose.presentation.ScreenNavigator
import com.qyqy.ucoo.core.Const

object VirtualMatchNavigator : ScreenNavigator {

    @Composable
    override fun ContentScreen(activity: BaseActivity, bundle: Bundle) {
        MainScreenRouter()
    }

    fun navigate(context: Context, data: VirtualMatchInfo) {
        context.startActivity(obtainIntent(context) {
            putExtra(Const.KEY_DATA, data)
        })
    }
}