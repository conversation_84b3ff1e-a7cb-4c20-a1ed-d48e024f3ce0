package com.qyqy.ucoo.compose.presentation.virtually

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.requiredHeight
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.ComposeContentScale

@Composable
fun VirtualScaffold(
    maskColor: Color? = null,
    content: @Composable BoxScope.() -> Unit,
    button: @Composable ColumnScope.() -> Unit,
) {
    Column(
        modifier = Modifier
            .verticalScroll(state = rememberScrollState())
            .fillMaxSize()
            .paint(
                painter = painterResource(id = R.drawable.ic_virtual_match_background),
                sizeToIntrinsics = false,
                contentScale = ComposeContentScale.ProxyCrop,
                alignment = Alignment.TopCenter,
            )
            .drawWithContent {
                if (maskColor != null) {
                    drawRect(maskColor)
                }
                drawContent()
            }
            .systemBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .requiredHeight(610.dp)
        ) {
            content()
        }

        button()

        Spacer(modifier = Modifier.height(80.dp))
    }
}