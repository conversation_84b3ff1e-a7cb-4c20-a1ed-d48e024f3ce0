

package com.qyqy.ucoo.compose.presentation.welfare_center

import android.content.Context
import android.os.Bundle
import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.scrollable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.imageResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.PlatformTextStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.google.android.material.animation.ArgbEvaluatorCompat
import com.overseas.common.utils.dpF
import com.overseas.common.utils.id2Color
import com.overseas.common.utils.id2String
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.data.DialogDelegate
import com.qyqy.ucoo.compose.presentation.ScreenNavigator
import com.qyqy.ucoo.compose.presentation.sign_tasks.Extra
import com.qyqy.ucoo.compose.presentation.sign_tasks.SignTaskManager
import com.qyqy.ucoo.compose.presentation.sign_tasks.Task
import com.qyqy.ucoo.compose.presentation.sign_tasks.TaskSeries
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.theme.colorCard
import com.qyqy.ucoo.compose.theme.colorTheme
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.ComposeDialog
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.LoadingLayout
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.runWithLoading
import kotlinx.coroutines.launch
import com.overseas.common.utils.dp as dpNative


private val colorGold = Color(0xFFFFB83D)

object WelfareCenterNavigator : ScreenNavigator {

    @Composable
    override fun ContentScreen(activity: BaseActivity, bundle: Bundle) {
        WelfareCenterScreen {
            activity.finish()
        }
    }
}

@Composable
fun WelfareCenterScreen(onBack: () -> Unit) {
    LaunchedEffect("launch") {
        SignTaskManager.refreshTasks(this)
    }
    AppTheme {
        val image = ImageBitmap.imageResource(id = R.drawable.bg_walfare_header)
        LoadingLayout {
            Box(
                modifier = Modifier
                    .fillMaxSize(1f)
                    .background(Color(0xFF1C1D1E))
            ) {
                val scrollState = rememberScrollState()
                val titleBarColor by remember {
                    derivedStateOf {
                        val percent = scrollState.value.toFloat().div(scrollState.maxValue.toFloat().coerceAtLeast(1f)) * 1.2f
                        val c =
                            ArgbEvaluatorCompat.getInstance()
                                .evaluate(percent.coerceAtMost(1f), android.graphics.Color.TRANSPARENT, 0xFF935DFE.toInt())
                        Color(c)
                    }
                }
                SignContent(scrollState, image)
                Box(modifier = Modifier.drawBehind {
                    drawRect(titleBarColor)
                }) {
                    Box(
                        modifier = Modifier
                            .windowInsetsPadding(WindowInsets.statusBars)
                    ) {
                        AppTitleBar(title = stringResource(id = R.string.welfare_center), onBack = onBack)
                    }
                }
            }
        }
    }
}

@Composable
fun SignDialog(dialogDelegate: DialogDelegate, onDismiss: () -> Unit) {
    if (dialogDelegate.visible) {
        Dialog(onDismissRequest = onDismiss) {
            TaskCompleteLayout(
                dialogDelegate.content,
                dialogDelegate.buttonText,
                buttonColor = id2Color(R.color.color_primary_pink),
                onButtonClick = dialogDelegate.onButtonClick
            )
        }
    }
}

@Composable
private fun TaskCompleteLayout(
    contentText: String,
    buttonText: String,
    @DrawableRes bgResId: Int = R.drawable.bg_dialog_sign,
    buttonColor: Int,
    onButtonClick: () -> Unit = {},
) {
    Box(
        modifier = Modifier
            .width(270.dp)
            .height(219.dp)
    ) {
        Image(painter = painterResource(id = bgResId), contentDescription = "", modifier = Modifier.fillMaxSize(1f))
        Text(
            text = contentText,
            color = colorGold,
            fontSize = 15.sp,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .fillMaxWidth(1f)
                .align(Alignment.Center)
                .padding(horizontal = 16.dp)
        )
        Button(
            contentPadding = PaddingValues(0.dp, 7.dp),
            colors = ButtonDefaults.buttonColors(containerColor = Color(buttonColor)),
            onClick = onButtonClick, modifier = Modifier
                .align(Alignment.BottomCenter)
                .width(160.dp)
                .offset(y = (-20).dp)
        ) {
            Text(text = buttonText)
        }
    }
}


//region 任务完成弹窗
class TaskCompleteDialog(
    context: Context,
    private val content: String,
    private val bgResId: Int = R.drawable.bg_dialog_sign,
    private val buttonTex: String = id2String(R.string.i_known),
    private val buttonColor: Int = id2Color(R.color.color_primary_pink),
    private val onButtonClick: () -> Unit = {},
) : ComposeDialog(context, dialogWidth = 270.dpNative) {
    @Composable
    override fun Content() {
        TaskCompleteLayout(contentText = content, buttonText = buttonTex, bgResId = bgResId, buttonColor) {
            onButtonClick()
            dismiss()
        }
    }
}
//endregion

@Preview(showBackground = true, backgroundColor = 0xFF304FFE)
@Composable
fun SignDialogResultPreviewer() {
    val text = stringResource(id = R.string.i_known)
    SignDialog(DialogDelegate(content = "恭喜完成一项新人任务！获得67金币奖励", buttonText = text, visible = true)) {}
}

@Composable
private fun SignContent(scrollState: ScrollState, image: ImageBitmap, signTaskManager: SignTaskManager = SignTaskManager) {
    val buttonText = stringResource(id = R.string.i_known)
    var dialogDelegate by remember {
        mutableStateOf(DialogDelegate(buttonText = buttonText))
    }
    val loadingState = LocalContentLoading.current
    val signTasks by signTaskManager.signInfo.collectAsState()
    val newbieTasks by signTaskManager.newbieTask.collectAsState()
    val signTaskProvider = { signTasks }
    val coroutineScope = rememberCoroutineScope()
    val onSign = {
        coroutineScope.launch {
            loadingState.runWithLoading {
                SignTaskManager.startSign(signTasks.seriesId)
            }
        }
        Unit
    }
    val ctx = LocalContext.current
    Column(modifier = Modifier
        .background(color = Color(0xFF1C1D1E))
        .fillMaxSize(1f)
        .verticalScroll(scrollState)
        .drawWithContent {
            drawImage(image = image, dstSize = IntSize(size.width.toInt(), (size.width * image.height / image.width).toInt()))
            drawContent()
        }) {
        Box(modifier = Modifier.windowInsetsPadding(WindowInsets.statusBars)) {
            Spacer(
                modifier = Modifier.height(88.dp)
            )
        }
        Column(
            modifier = Modifier
                .fillMaxWidth(1f)
                .padding(horizontal = 16.dp)
                .background(colorCard, shape = Shapes.medium)
                .padding(horizontal = 12.dp, vertical = 20.dp),
        ) {
            LabelText(text = signTasks.seriesTitle)
            if (signTasks.treasureTotips.isNotEmpty()) {
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = signTasks.treasureTotips,
                    fontSize = 14.sp,
                    color = Color.White
                )
            }
            Spacer(modifier = Modifier.height(6.dp))
            if (signTasks.finishedTotips.isNotEmpty()) {
                Text(
                    text = signTasks.finishedTotips,
                    fontSize = 12.sp,
                    modifier = Modifier
                        .background(color = Color(0x33945EFF), shape = Shapes.extraSmall)
                        .padding(8.dp, 6.dp),
                    color = colorTheme
                )
                Spacer(modifier = Modifier.height(16.dp))
            }
            SignTable(signItemProvider = signTaskProvider)
            Spacer(modifier = Modifier.height(20.dp))
            Row(horizontalArrangement = Arrangement.Center, modifier = Modifier.fillMaxWidth(1f)) {
                Button(
                    modifier = Modifier
                        .width(216.dp)
                        .graphicsLayer {
                            alpha = if (signTasks.todayFinished) 0.5f else 1f
                        },
                    onClick = onSign,
                    colors = ButtonDefaults.buttonColors(disabledContainerColor = colorTheme, disabledContentColor = Color.White),
                    enabled = signTasks.todayFinished.not()
                ) {
                    Text(text = stringResource(if (signTasks.todayFinished) R.string.today_is_signed else R.string.click_to_complete_today_sign))
                }
            }
        }
        Spacer(
            modifier = Modifier.height(16.dp)
        )
        Column(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .background(colorCard, shape = Shapes.medium)
                .padding(horizontal = 12.dp)
        ) {
            Spacer(
                modifier = Modifier.height(20.dp)
            )
            LabelText(text = newbieTasks.seriesTitle)
            newbieTasks.tasks.forEachIndexed { index, taskItem ->
                TaskItem(
                    title = taskItem.title,
                    desc = taskItem.progress.takeIf { taskItem.progress.isNotEmpty() } ?: taskItem.desc,
                    prize = taskItem.prize,
                    icon = taskItem.extra.taskIcon,
                    completed = taskItem.finished
                ) {
                    coroutineScope.launch {
                        loadingState.runWithLoading {
                            signTaskManager.gotoCompleteTask(ctx, taskItem.id)
                        }
                    }
                }
                if (index != tasks.lastIndex) {
                    HorizontalDivider(thickness = 0.5.dp, color = Color(0xFF4C4C4C))
                }
            }
        }
        Spacer(modifier = Modifier.height(24.dp))
    }

    SignDialog(dialogDelegate = dialogDelegate) {
        dialogDelegate = dialogDelegate.copy(visible = false)
    }
}


//region 签到弹窗
class SignAlertDialog(context: Context, private val signTaskSeries: TaskSeries) : ComposeDialog(context, dialogWidth = 320.dpF.toInt()) {
    @Composable
    override fun Content() {
        LoadingLayout {
            SignAlert(signTaskSeries) {
                dismiss()
            }
        }
    }
}
//endregion

@Composable
fun SignAlert(signTaskSeries: TaskSeries, onSignComplete: () -> Unit = {}) {
    val tasksProvider = { signTaskSeries }
    val coroutineScope = rememberCoroutineScope()
    val loadingState = LocalContentLoading.current
    val onSign = {
        coroutineScope.launch {
            loadingState.runWithLoading {
                SignTaskManager.startSign(signTaskSeries.seriesId)
            }
            onSignComplete()
        }
        Unit
    }
    Column(
        modifier = Modifier
            .fillMaxWidth(1f)
            .drawBehind {
                val px = 16.dp.toPx()
                drawRoundRect(color = colorCard, cornerRadius = CornerRadius(x = px, y = px), topLeft = Offset(0f, px))
            }
    ) {
        Box {
            Image(
                painter = painterResource(id = R.drawable.ic_sign_dialog_header),
                contentDescription = "",
                modifier = Modifier
                    .fillMaxWidth(1f)
                    .aspectRatio(638f / 204)
            )
            Column(modifier = Modifier.padding(horizontal = 12.dp)) {
                Spacer(modifier = Modifier.height(32.dp))
                Text(
                    text = signTaskSeries.treasureTotips,
                    fontSize = 18.sp,
                    color = Color.White,
                    style = LocalTextStyle.current.merge(
                        TextStyle(platformStyle = PlatformTextStyle(includeFontPadding = false))
                    )
                )
                Text(
                    modifier = Modifier
                        .padding(vertical = 4.dp)
                        .background(color = Color(0xFFE2D3FF), shape = Shapes.extraSmall)
                        .padding(8.dp, 4.dp),
                    text = signTaskSeries.finishedTotips,
                    fontSize = 12.sp,
                    color = Color(0xFF4D13BC)
                )
            }
        }
        Spacer(modifier = Modifier.height(20.dp))
        SignTable(modifier = Modifier.padding(horizontal = 12.dp), tasksProvider)
        Spacer(modifier = Modifier.height(20.dp))
        Row(modifier = Modifier.fillMaxWidth(1f), horizontalArrangement = Arrangement.Center) {
            Button(onClick = onSign, modifier = Modifier.width(216.dp)) {
                Text(text = stringResource(R.string.sign_to_get_gold))
            }
        }
        Spacer(modifier = Modifier.height(20.dp))
    }
}

@Composable
private fun SignTable(modifier: Modifier = Modifier, signItemProvider: () -> TaskSeries) {
    val taskSeries = signItemProvider()
    LazyVerticalGrid(
        modifier = Modifier
            .then(modifier)
            .scrollable(rememberScrollState(), Orientation.Vertical, enabled = false)
            .heightIn(100.dp, 800.dp),
        columns = GridCells.Fixed(4),
        verticalArrangement = Arrangement.spacedBy(10.dp),
        horizontalArrangement = Arrangement.spacedBy(10.dp)
    ) {
        items(items = taskSeries.tasks, span = {
            GridItemSpan(if (it.bigSpan) 2 else 1)
        }) {
            val iconSize = remember(key1 = "iconSize-${it.id}") {
                if (it.prizeType == 1) {
                    DpSize(24.dp, 24.dp)
                } else {
                    DpSize(40.dp, 36.dp)
                }
            }
            SignItem(title = it.title, desc = it.prize, icon = it.extra.prizeIcon, iconSize = iconSize, completed = it.finished)
        }
    }
}

@Preview(showBackground = true, backgroundColor = 0xFFFFFEFE, showSystemUi = false)
@Composable
fun SignDialogPreviewer() {
    SignAlert(TaskSeries(tasks = signItems.map {
        Task(
            conditionTimes = 3,
            conditionType = 3,
            desc = it.desc,
            title = it.title,
            id = 0,
            prize = "",
            progress = "",
            extra = Extra(),
            finished = it.completed
        )
    }))
}

//region Data
private val tasks = buildList<TaskItem> {
    add(TaskItem(R.drawable.ic_task_audio, "语音房上麦超过15分钟", desc = "已累计上麦7分钟", completed = false, gold = 72))
    add(TaskItem(R.drawable.ic_task_call, "语音房上麦超过15分钟", desc = "已累计上麦7分钟", completed = true, gold = 72))
    add(TaskItem(R.drawable.ic_task_gift, "语音房上麦超过15分钟", desc = "已累计上麦7分钟", completed = false, gold = 72))
}

data class TaskItem(val iconRes: Int, val title: String, val desc: String, val completed: Boolean, val gold: Int)

private val signItems = buildList<SignItem> {
    add(SignItem("已领取", "5金币", true, R.drawable.ic_sign_gold))
    add(SignItem("已领取", "10金币", true, R.drawable.ic_sign_gold))
    add(SignItem("第三天", "大额宝箱\n50金币", false, R.drawable.ic_sign_normal_box))
    add(SignItem("第四天", "20金币", false, R.drawable.ic_sign_gold))
    add(SignItem("第五天", "25金币", false, R.drawable.ic_sign_gold))
    add(SignItem("第六天", "30金币", false, R.drawable.ic_sign_gold))
    add(SignItem("第七天", "超级惊喜宝箱\n最高奖励5000金币", false, R.drawable.ic_sign_huge_box))
}

data class SignItem(val title: String, val desc: String, val completed: Boolean, val iconRes: Int) {
    val iconSize = when (iconRes) {
        R.drawable.ic_sign_gold -> DpSize(24.dp, 24.dp)
        R.drawable.ic_sign_normal_box -> DpSize(40.dp, 32.dp)
        else -> DpSize(40.dp, 36.dp)
    }

    val bigSpan = R.drawable.ic_sign_huge_box == iconRes
}
//endregion

//region 任务Item
@Composable
fun TaskItem(icon: String = "", title: String, desc: String, prize: String, completed: Boolean = false, onClick: () -> Unit) {
    Row(modifier = Modifier.padding(vertical = 16.dp), verticalAlignment = Alignment.CenterVertically) {
        ComposeImage(model = icon, modifier = Modifier.size(48.dp))
        Spacer(modifier = Modifier.size(12.dp, 10.dp))
        Column(modifier = Modifier.weight(1f, true)) {
            Text(text = title, fontSize = 13.sp, color = Color.White, lineHeight = 14.sp)
            Spacer(modifier = Modifier.height(6.dp))
            Text(text = desc, fontSize = 12.sp, color = Color.White, modifier = Modifier.graphicsLayer { alpha = 0.5f })
            Spacer(modifier = Modifier.height(4.dp))
            Row(verticalAlignment = Alignment.CenterVertically) {
                Image(painter = painterResource(id = R.drawable.ic_sign_gold), contentDescription = "", modifier = Modifier.size(12.dp))
                Text(text = prize, fontSize = 12.sp, color = colorGold, modifier = Modifier.padding(start = 4.dp))
            }
        }
        Button(
            contentPadding = PaddingValues(15.dp, 6.dp),
            onClick = onClick,
            enabled = !completed,
            modifier = Modifier
                .heightIn(10.dp, 38.dp)
                .graphicsLayer {
                    alpha = if (completed) 0.5f else 1f
                },
            colors = ButtonDefaults.buttonColors(disabledContainerColor = colorTheme, disabledContentColor = Color.White)
        ) {
            Text(text = stringResource(id = if (completed) R.string.complete_already else R.string.go_finish), fontSize = 13.sp)
        }
    }
}
//endregion


//region 签到Item
@Composable
fun SignItem(title: String, desc: String, icon: String, iconSize: DpSize = DpSize(40.dp, 36.dp), completed: Boolean = false) {
    Column(
        modifier = Modifier
            .background(color = Color(if (completed) 0xFF604595 else 0xFF3C334C), shape = Shapes.small)
            .padding(vertical = 8.dp)
            .widthIn(72.dp, 154.dp)
            .height(94.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceBetween,
    ) {
        Text(text = title, color = Color.White, fontSize = 14.sp)
        ComposeImage(model = icon, modifier = Modifier.size(iconSize))
        Text(
            text = desc,
            color = colorGold,
            fontSize = if (desc.length > 6) 9.sp else 11.sp,
            textAlign = TextAlign.Center,
            lineHeight = 13.sp,
            softWrap = true,
            modifier = Modifier
                .fillMaxWidth(1f)
                .heightIn(0.dp, 36.dp)
        )
    }
}
//endregion

@Preview
@Composable
fun SignItemPreview() {
    SignItem(title = "已领取", desc = "超级惊喜宝箱最高奖励5000金币", icon = "")
}


@Composable
fun LabelText(text: String, guideColor: Color = colorTheme) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        Box(
            modifier = Modifier
                .background(guideColor, shape = Shapes.extraSmall)
                .size(4.dp, 16.dp)
        )
        Spacer(modifier = Modifier.width(4.dp))
        Text(text = text, color = Color.White, fontSize = 16.sp)
    }
}

//@Preview(showSystemUi = true)
//@Composable
//fun WelfareCenterScreenPreviewer() {
//    WelfareCenterScreen()
//}