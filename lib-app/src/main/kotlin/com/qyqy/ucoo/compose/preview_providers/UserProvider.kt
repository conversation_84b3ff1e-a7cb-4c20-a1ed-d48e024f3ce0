package com.qyqy.ucoo.compose.preview_providers

import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.UserInfo
import com.qyqy.ucoo.compose.getSampleImageUrl
import com.qyqy.ucoo.compose.presentation.ff.userForPreview

class UserProvider : PreviewParameterProvider<AppUser> {
    companion object {
        val user = AppUser(
            gender = 2,
            nickname = "幼儿园班花",
            avatarUrl = getSampleImageUrl(width = 100, height = 100, (Math.random() * 100).toInt().coerceAtLeast(1)),
            age = 20,
            regionLabel = "台湾",
            regionReasonLabel = "现居地"
        )
        val userBoy = AppUser(
            gender = 1,
            nickname = "龙傲天",
            avatarUrl = getSampleImageUrl(width = 100, height = 100, (Math.random() * 100).toInt().coerceAtLeast(1)),
            age = 22
        )
    }

    override val values: Sequence<AppUser> = sequenceOf(userForPreview)
}

class UserInfoProvider : PreviewParameterProvider<UserInfo> {
    val user = UserInfo().apply {
        this.id = "1"
        gender = 2
        nickname = "幼儿园班花"
        avatarUrl = ""
        age = 20
    }
    override val values: Sequence<UserInfo> = sequenceOf(user)
}