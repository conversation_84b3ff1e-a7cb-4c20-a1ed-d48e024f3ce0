<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="32dp"
    android:height="24dp"
    android:viewportWidth="32"
    android:viewportHeight="24">
  <group>
    <clip-path
        android:pathData="M20.273,9.154h11.194v11.194h-11.194z"/>
    <path
        android:pathData="M25.87,14.751m-5.13,0a5.13,5.13 0,1 1,10.261 0a5.13,5.13 0,1 1,-10.261 0">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="25.87"
            android:startY="9.62"
            android:endX="25.87"
            android:endY="19.881"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFF1BD"/>
          <item android:offset="1" android:color="#FFFECB21"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M25.871,14.751m-4.198,0a4.198,4.198 0,1 1,8.395 0a4.198,4.198 0,1 1,-8.395 0">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="25.87"
            android:startY="10.554"
            android:endX="25.87"
            android:endY="18.949"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFB706"/>
          <item android:offset="1" android:color="#FFFEF8D8"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M25.87,14.751m-3.731,0a3.731,3.731 0,1 1,7.462 0a3.731,3.731 0,1 1,-7.462 0"
        android:fillColor="#FFB81A"/>
    <path
        android:pathData="M25.6,11.752C25.693,11.5 26.048,11.5 26.141,11.752C26.609,13.016 27.606,14.013 28.87,14.481C29.121,14.573 29.121,14.929 28.87,15.022C27.606,15.49 26.609,16.487 26.141,17.751C26.048,18.002 25.693,18.002 25.6,17.751C25.132,16.487 24.135,15.49 22.871,15.022C22.619,14.929 22.619,14.573 22.871,14.481C24.135,14.013 25.132,13.016 25.6,11.752Z"
        android:fillColor="#FFFAE2"/>
  </group>
  <path
      android:pathData="M10.945,4.866h11.194v14.27h-11.194z"
      android:fillColor="#FFBC47"/>
  <path
      android:pathData="M10.945,4.866a5.597,1.866 0,1 0,11.194 0a5.597,1.866 0,1 0,-11.194 0z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.542"
          android:startY="3"
          android:endX="16.542"
          android:endY="6.731"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFF1BD"/>
        <item android:offset="1" android:color="#FFFECB21"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M11.412,4.866a5.13,1.567 0,1 0,10.261 0a5.13,1.567 0,1 0,-10.261 0z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.543"
          android:startY="3.299"
          android:endX="16.543"
          android:endY="6.432"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFB706"/>
        <item android:offset="1" android:color="#FFFEF8D8"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M11.727,4.866a4.816,1.308 0,1 0,9.631 0a4.816,1.308 0,1 0,-9.631 0z"
      android:fillColor="#FFB81A"/>
  <path
      android:pathData="M16.418,3.727C16.48,3.643 16.605,3.643 16.667,3.727C17.041,4.232 17.593,4.577 18.211,4.691L18.277,4.703C18.458,4.737 18.458,4.996 18.277,5.029L18.211,5.042C17.593,5.156 17.041,5.501 16.667,6.006C16.605,6.09 16.48,6.09 16.418,6.006C16.044,5.501 15.492,5.156 14.874,5.042L14.808,5.029C14.627,4.996 14.627,4.737 14.808,4.703L14.874,4.691C15.492,4.577 16.044,4.232 16.418,3.727Z"
      android:fillColor="#FFFAE2"/>
  <path
      android:pathData="M16.338,6.685C12.966,6.685 10.945,5.717 10.945,4.866V6.695C10.945,7.329 11.724,8.597 16.338,8.597C20.953,8.597 22.139,7.262 22.139,6.695V4.866C22.139,5.717 19.711,6.685 16.338,6.685Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="10.945"
          android:startY="6.289"
          android:endX="22.139"
          android:endY="6.289"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFF0C0"/>
        <item android:offset="0.5" android:color="#FFFFDD6E"/>
        <item android:offset="1" android:color="#FFFFEDB3"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M16.338,9.166C12.966,9.166 10.945,8.198 10.945,7.347V9.176C10.945,9.81 11.724,11.078 16.338,11.078C20.953,11.078 22.139,9.743 22.139,9.176V7.347C22.139,8.198 19.711,9.166 16.338,9.166Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="10.945"
          android:startY="8.77"
          android:endX="22.139"
          android:endY="8.77"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFF0C0"/>
        <item android:offset="0.5" android:color="#FFFFDD6E"/>
        <item android:offset="1" android:color="#FFFFEDB3"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M16.338,11.647C12.966,11.647 10.945,10.679 10.945,9.828V11.656C10.945,12.29 11.724,13.559 16.338,13.559C20.953,13.559 22.139,12.224 22.139,11.656V9.828C22.139,10.679 19.711,11.647 16.338,11.647Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="10.945"
          android:startY="11.251"
          android:endX="22.139"
          android:endY="11.251"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFF0C0"/>
        <item android:offset="0.5" android:color="#FFFFDD6E"/>
        <item android:offset="1" android:color="#FFFFEDB3"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M16.338,14.127C12.966,14.127 10.945,13.159 10.945,12.308V14.137C10.945,14.771 11.724,16.039 16.338,16.039C20.953,16.039 22.139,14.704 22.139,14.137V12.308C22.139,13.159 19.711,14.127 16.338,14.127Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="10.945"
          android:startY="13.731"
          android:endX="22.139"
          android:endY="13.731"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFF0C0"/>
        <item android:offset="0.5" android:color="#FFFFDD6E"/>
        <item android:offset="1" android:color="#FFFFEDB3"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M16.338,16.608C12.966,16.608 10.945,15.64 10.945,14.789V16.617C10.945,17.251 11.724,18.52 16.338,18.52C20.953,18.52 22.139,17.185 22.139,16.617V14.789C22.139,15.64 19.711,16.608 16.338,16.608Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="10.945"
          android:startY="16.212"
          android:endX="22.139"
          android:endY="16.212"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFF0C0"/>
        <item android:offset="0.5" android:color="#FFFFDD6E"/>
        <item android:offset="1" android:color="#FFFFEDB3"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M16.339,19.088C12.966,19.088 10.946,18.12 10.946,17.269V19.097C10.946,19.731 11.725,21 16.339,21C20.953,21 22.139,19.665 22.139,19.097V17.269C22.139,18.12 19.712,19.088 16.339,19.088Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="10.946"
          android:startY="18.692"
          android:endX="22.14"
          android:endY="18.692"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFF0C0"/>
        <item android:offset="0.5" android:color="#FFFFDD6E"/>
        <item android:offset="1" android:color="#FFFFEDB3"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M1.57,8.558h10.157v10.749h-10.157z"
      android:fillColor="#FFBC47"/>
  <path
      android:pathData="M1.57,8.558a5.078,1.693 0,1 0,10.157 0a5.078,1.693 0,1 0,-10.157 0z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="6.648"
          android:startY="6.866"
          android:endX="6.648"
          android:endY="10.251"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFF1BD"/>
        <item android:offset="1" android:color="#FFFECB21"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M1.993,8.558a4.655,1.422 0,1 0,9.31 0a4.655,1.422 0,1 0,-9.31 0z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="6.648"
          android:startY="7.137"
          android:endX="6.648"
          android:endY="9.98"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFB706"/>
        <item android:offset="1" android:color="#FFFEF8D8"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M2.279,8.559a4.37,1.187 0,1 0,8.739 0a4.37,1.187 0,1 0,-8.739 0z"
      android:fillColor="#FFB81A"/>
  <path
      android:pathData="M6.535,7.524C6.591,7.448 6.705,7.448 6.761,7.524C7.1,7.983 7.601,8.296 8.162,8.399L8.222,8.411C8.386,8.441 8.386,8.676 8.222,8.706L8.162,8.718C7.601,8.821 7.1,9.134 6.761,9.593C6.705,9.669 6.591,9.669 6.535,9.593C6.195,9.134 5.695,8.821 5.134,8.718L5.074,8.706C4.91,8.676 4.91,8.441 5.074,8.411L5.134,8.399C5.695,8.296 6.195,7.983 6.535,7.524Z"
      android:fillColor="#FFFAE2"/>
  <path
      android:pathData="M6.463,10.209C3.403,10.209 1.57,9.33 1.57,8.558V10.217C1.57,10.793 2.277,11.944 6.463,11.944C10.65,11.944 11.726,10.732 11.726,10.217V8.558C11.726,9.33 9.524,10.209 6.463,10.209Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="1.57"
          android:startY="9.849"
          android:endX="11.726"
          android:endY="9.849"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFF0C0"/>
        <item android:offset="0.5" android:color="#FFFFDD6E"/>
        <item android:offset="1" android:color="#FFFFEDB3"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M6.463,12.473C3.403,12.473 1.57,11.594 1.57,10.822V12.481C1.57,13.057 2.277,14.208 6.463,14.208C10.65,14.208 11.726,12.996 11.726,12.481V10.822C11.726,11.594 9.524,12.473 6.463,12.473Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="1.57"
          android:startY="12.113"
          android:endX="11.726"
          android:endY="12.113"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFF0C0"/>
        <item android:offset="0.5" android:color="#FFFFDD6E"/>
        <item android:offset="1" android:color="#FFFFEDB3"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M6.463,14.737C3.403,14.737 1.57,13.859 1.57,13.086V14.746C1.57,15.321 2.277,16.472 6.463,16.472C10.65,16.472 11.726,15.26 11.726,14.746V13.086C11.726,13.859 9.524,14.737 6.463,14.737Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="1.57"
          android:startY="14.377"
          android:endX="11.726"
          android:endY="14.377"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFF0C0"/>
        <item android:offset="0.5" android:color="#FFFFDD6E"/>
        <item android:offset="1" android:color="#FFFFEDB3"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M6.463,17.001C3.403,17.001 1.57,16.122 1.57,15.35V17.009C1.57,17.585 2.277,18.736 6.463,18.736C10.65,18.736 11.726,17.524 11.726,17.009V15.35C11.726,16.122 9.524,17.001 6.463,17.001Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="1.57"
          android:startY="16.641"
          android:endX="11.726"
          android:endY="16.641"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFF0C0"/>
        <item android:offset="0.5" android:color="#FFFFDD6E"/>
        <item android:offset="1" android:color="#FFFFEDB3"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M6.463,19.265C3.403,19.265 1.57,18.386 1.57,17.614V19.274C1.57,19.849 2.277,21 6.463,21C10.65,21 11.726,19.788 11.726,19.274V17.614C11.726,18.386 9.524,19.265 6.463,19.265Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="1.57"
          android:startY="18.905"
          android:endX="11.726"
          android:endY="18.905"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFF0C0"/>
        <item android:offset="0.5" android:color="#FFFFDD6E"/>
        <item android:offset="1" android:color="#FFFFEDB3"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
