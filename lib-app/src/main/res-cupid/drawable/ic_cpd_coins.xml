<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="28dp"
    android:height="18dp"
    android:viewportWidth="28"
    android:viewportHeight="18">
  <group>
    <clip-path
        android:pathData="M0,6h12v12h-12z"/>
    <path
        android:pathData="M6,12m-5.5,0a5.5,5.5 0,1 1,11 0a5.5,5.5 0,1 1,-11 0">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="6"
            android:startY="6.5"
            android:endX="6"
            android:endY="17.5"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFF1BD"/>
          <item android:offset="1" android:color="#FFFECB21"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M6,12m-4.5,0a4.5,4.5 0,1 1,9 0a4.5,4.5 0,1 1,-9 0">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="6"
            android:startY="7.5"
            android:endX="6"
            android:endY="16.5"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFB706"/>
          <item android:offset="1" android:color="#FFFEF8D8"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M6,12m-4,0a4,4 0,1 1,8 0a4,4 0,1 1,-8 0"
        android:fillColor="#FFB81A"/>
    <path
        android:pathData="M5.71,8.784C5.809,8.515 6.191,8.515 6.29,8.784C6.792,10.14 7.86,11.208 9.216,11.71C9.485,11.809 9.485,12.191 9.216,12.29C7.86,12.792 6.792,13.86 6.29,15.216C6.191,15.485 5.809,15.485 5.71,15.216C5.208,13.86 4.14,12.792 2.784,12.29C2.515,12.191 2.515,11.809 2.784,11.71C4.14,11.208 5.208,10.14 5.71,8.784Z"
        android:fillColor="#FFFAE2"/>
  </group>
  <group>
    <clip-path
        android:pathData="M16,6h12v12h-12z"/>
    <path
        android:pathData="M22,12m-5.5,0a5.5,5.5 0,1 1,11 0a5.5,5.5 0,1 1,-11 0">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="22"
            android:startY="6.5"
            android:endX="22"
            android:endY="17.5"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFF1BD"/>
          <item android:offset="1" android:color="#FFFECB21"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M22,12m-4.5,0a4.5,4.5 0,1 1,9 0a4.5,4.5 0,1 1,-9 0">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="22"
            android:startY="7.5"
            android:endX="22"
            android:endY="16.5"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFB706"/>
          <item android:offset="1" android:color="#FFFEF8D8"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M22,12m-4,0a4,4 0,1 1,8 0a4,4 0,1 1,-8 0"
        android:fillColor="#FFB81A"/>
    <path
        android:pathData="M21.71,8.784C21.809,8.515 22.191,8.515 22.29,8.784V8.784C22.792,10.14 23.86,11.208 25.216,11.71V11.71C25.485,11.809 25.485,12.191 25.216,12.29V12.29C23.86,12.792 22.792,13.86 22.29,15.216V15.216C22.191,15.485 21.809,15.485 21.71,15.216V15.216C21.208,13.86 20.14,12.792 18.784,12.29V12.29C18.515,12.191 18.515,11.809 18.784,11.71V11.71C20.14,11.208 21.208,10.14 21.71,8.784V8.784Z"
        android:fillColor="#FFFAE2"/>
  </group>
  <group>
    <clip-path
        android:pathData="M5,0h18v18h-18z"/>
    <path
        android:pathData="M14,9m-8.25,0a8.25,8.25 0,1 1,16.5 0a8.25,8.25 0,1 1,-16.5 0">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="14"
            android:startY="0.75"
            android:endX="14"
            android:endY="17.25"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFF1BD"/>
          <item android:offset="1" android:color="#FFFECB21"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M14,9m-6.75,0a6.75,6.75 0,1 1,13.5 0a6.75,6.75 0,1 1,-13.5 0">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="14"
            android:startY="2.25"
            android:endX="14"
            android:endY="15.75"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFB706"/>
          <item android:offset="1" android:color="#FFFEF8D8"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M14,9m-6,0a6,6 0,1 1,12 0a6,6 0,1 1,-12 0"
        android:fillColor="#FFB81A"/>
    <path
        android:pathData="M13.565,4.177C13.714,3.772 14.286,3.772 14.435,4.177V4.177C15.188,6.209 16.791,7.812 18.823,8.565V8.565C19.228,8.714 19.228,9.286 18.823,9.435V9.435C16.791,10.188 15.188,11.79 14.435,13.823V13.823C14.286,14.228 13.714,14.228 13.565,13.823V13.823C12.812,11.79 11.21,10.188 9.177,9.435V9.435C8.772,9.286 8.772,8.714 9.177,8.565V8.565C11.21,7.812 12.812,6.209 13.565,4.177V4.177Z"
        android:fillColor="#FFFAE2"/>
  </group>
</vector>
