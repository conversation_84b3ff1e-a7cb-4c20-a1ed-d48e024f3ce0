<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="40dp"
    android:height="40dp"
    android:viewportWidth="40"
    android:viewportHeight="40">
  <path
      android:pathData="M20,20m-20,0a20,20 0,1 1,40 0a20,20 0,1 1,-40 0"
      android:fillColor="#ffffff"
      android:fillAlpha="0.15"/>
  <path
      android:pathData="M23.855,9.986C23.207,9.338 22.157,9.338 21.509,9.986L20,11.495L18.492,9.986C17.844,9.338 16.793,9.338 16.145,9.986C15.497,10.634 15.497,11.684 16.145,12.333L18.827,15.014C19.475,15.662 20.525,15.662 21.173,15.014L23.855,12.333C24.503,11.684 24.503,10.634 23.855,9.986Z"
      android:strokeWidth="1.27281"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="20"
          android:startY="9.5"
          android:endX="20"
          android:endY="15.5"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFE08F"/>
        <item android:offset="0.756" android:color="#FFFF7B1B"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M11,18H29V26C29,28.209 27.209,30 25,30H15C12.791,30 11,28.209 11,26V18Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="18.611"
          android:startY="25.061"
          android:endX="20.297"
          android:endY="30"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF3D9A"/>
        <item android:offset="1" android:color="#FFFF0059"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M11,13H29V26C29,28.209 27.209,30 25,30H15C12.791,30 11,28.209 11,26V13Z"/>
    <path
        android:pathData="M11,17.945h18v1.5h-18z"
        android:fillColor="#FF0159"/>
    <path
        android:pathData="M18,18h4v12h-4z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="20"
            android:startY="18"
            android:endX="20"
            android:endY="30"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFB3BF"/>
          <item android:offset="1" android:color="#00FFB3BF"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M10,13L30,13A1,1 0,0 1,31 14L31,17A1,1 0,0 1,30 18L10,18A1,1 0,0 1,9 17L9,14A1,1 0,0 1,10 13z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="20"
          android:startY="18"
          android:endX="20"
          android:endY="13"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF3D9A"/>
        <item android:offset="1" android:color="#FFFFA1BB"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M18,12.998h4v5.002h-4z"
      android:fillColor="#FF458B"
      android:fillAlpha="0.5"/>
  <group>
    <clip-path
        android:pathData="M10,13L30,13A1,1 0,0 1,31 14L31,17A1,1 0,0 1,30 18L10,18A1,1 0,0 1,9 17L9,14A1,1 0,0 1,10 13z"/>
    <path
        android:pathData="M18,12.998h4v5.002h-4z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="20"
            android:startY="12.998"
            android:endX="20"
            android:endY="18"
            android:type="linear">
          <item android:offset="0" android:color="#99FFB3BF"/>
          <item android:offset="1" android:color="#FFFFB3BF"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
