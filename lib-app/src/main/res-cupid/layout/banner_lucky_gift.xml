<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_lucky_gift"
    android:layout_width="327dp"
    android:layout_height="56dp"
    android:layout_gravity="center"
    android:background="@drawable/cpd_luck_floating_bg"
    >


    <ImageView
        android:id="@+id/iv_1"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="6dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@tools:sample/avatars" />

    <com.qyqy.ucoo.core.banner.TextViewWrapper
        android:id="@+id/tv_wrapper"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginStart="4dp"
        android:gravity="start|center_vertical"
        android:textColor="@color/white"
        android:textSize="13sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tvMultiplier"
        app:layout_constraintStart_toEndOf="@+id/iv_1"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_message"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginEnd="2dp"
            android:gravity="start|center_vertical"
            android:textColor="@color/white"
            android:textSize="13sp"

            tools:text="恭喜恕瑞玛皇...和盖伦成为真爱官宣CP" />
    </com.qyqy.ucoo.core.banner.TextViewWrapper>

    <TextView
        android:id="@+id/tvMultiplier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/cpd_luck_floating_multiplier"
        android:gravity="bottom|center_horizontal"
        android:padding="4dp"
        android:textColor="#FFFFB906"
        android:textSize="16sp"
        android:lines="1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tv_wrapper"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>