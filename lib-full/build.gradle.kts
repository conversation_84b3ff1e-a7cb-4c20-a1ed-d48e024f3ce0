plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
}

android {
    namespace = "com.qyqy.ucoo.resources"
    compileSdk = libs.versions.compileSdk.get().toInt()

    defaultConfig {
        minSdk = libs.versions.minSdk.get().toInt()

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }

    lint {
        abortOnError = false
        disable.add("MissingDefaultResource")
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
        debug {
            initWith(getByName("release"))
            isMinifyEnabled = false
        }
        create("preRelease") {
            initWith(getByName("release"))
        }
    }

    flavorDimensions += listOf("channel", "pay")

    productFlavors {
        create("play") {
            dimension = "channel"
            ndk {
                abiFilters += listOf("armeabi-v7a", "arm64-v8a")
            }
            packaging {
                jniLibs {
                    useLegacyPackaging = true
                }
            }
        }
        create("official") {
            dimension = "channel"
        }
        create("googlePay") {
            dimension = "pay"
        }
        create("commonPay") {
            dimension = "pay"
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }
}

dependencies {

}