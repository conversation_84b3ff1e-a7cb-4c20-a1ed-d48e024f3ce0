package com.qyqy.plugin

import com.squareup.kotlinpoet.ClassName
import com.squareup.kotlinpoet.FileSpec
import com.squareup.kotlinpoet.FunSpec
import com.squareup.kotlinpoet.KModifier
import com.squareup.kotlinpoet.ParameterSpec
import com.squareup.kotlinpoet.PropertySpec
import com.squareup.kotlinpoet.TypeSpec
import org.jetbrains.kotlin.buildtools.api.CompilerArgumentsParseException
import org.jetbrains.kotlin.com.google.gson.JsonElement
import org.jetbrains.kotlin.com.google.gson.JsonObject
import org.jetbrains.kotlin.com.google.gson.JsonParser
import java.io.File
import kotlin.reflect.KClass

class BuildConfigCase(val sourceFile: File, val output: String) {
    private val jsonObject:JsonObject
    init {
        val jsonText = sourceFile.readText()
        jsonObject = JsonParser.parseString(jsonText).asJsonObject
    }

    fun generate(){
        val fileSpec = generateEnvClass(jsonObject).build()
        fileSpec.writeTo(File(output))
    }

    private fun generateEnvClass(jsonObject: JsonObject): FileSpec.Builder {
        //编译字段
        val buildFields = mutableMapOf<String, KClass<*>>()

        //动态环境变量
        val dynamicEnvFields = mutableMapOf<String, KClass<*>>()
        //静态环境变量
        val staticEnvFields = mutableMapOf<String, KClass<*>>()

        //region 先确定固定字段, app信息等
        val fiexed_fields = jsonObject.getAsJsonObject("fixed_fields")
        var hasBuildType = false
        parseObjectFields(fiexed_fields) { key, type ->
            buildFields.put(key, type)
            if (key.uppercase() == "BUILD_TYPE") {
                hasBuildType = true
            }
        }

        if (!hasBuildType) {
            throw CompilerArgumentsParseException("必须有BUILD_TYPE这个字段, 不然没法判断环境")
        }

        //endregion

        //region 再确定固定的环境变量
        val defaultFields = jsonObject.getAsJsonObject("default_fields")
        parseObjectFields(defaultFields) { key, type ->
            staticEnvFields.put(key, type)
        }

        //endregion

        //region 读取环境变量
        val environments = jsonObject.getAsJsonArray("environments")
        parseDynamicFields(environments) { key, value ->
            dynamicEnvFields.put(key, value)
        }
        //endregion
        val envClassName = ClassName("com.qyqy.ucoo", "UCOOEnvironmentInstance")

        return FileSpec.builder("com.qyqy.ucoo", "BuildConfigExt").apply {
            val buildConfigImpl = ClassName("com.qyqy.ucoo", "BuildConfigImpl")

            addType(TypeSpec.enumBuilder("UCOOEnvironmentInstance").apply {
                primaryConstructor(FunSpec.constructorBuilder().apply {
                    dynamicEnvFields.forEach {
                        val upperStr = it.key.uppercase()
                        addParameter(upperStr, it.value)
                        addProperty(
                            PropertySpec.builder(upperStr, it.value)
                                .initializer(upperStr)
                                .build()
                        )

                    }

                }.build())

                environments.forEach {
                    val envJsonObj = it.asJsonObject
                    addEnumConstant(
                        envJsonObj.get("name").asString.uppercase(),
                        TypeSpec.anonymousClassBuilder().apply {
                            dynamicEnvFields.forEach {
                                addSuperclassConstructorParameter(
                                    generateFormat(it.value),
                                    envJsonObj.get(it.key, it.value)
                                )
                            }
                        }.build()
                    )
                }
            }.build())

            addType(TypeSpec.classBuilder("BuildConfigImpl").addModifiers(KModifier.DATA).apply {
                //构造函数
                primaryConstructor(FunSpec.constructorBuilder().apply {
                    //编译的字段需要外部传入
                    buildFields.forEach {
                        val upperStr = it.key.uppercase()
                        addParameter(upperStr, it.value)
                        addProperty(PropertySpec.builder(upperStr, it.value).initializer(upperStr).build())
                    }

//                    addParameter(ParameterSpec.builder("environment", envClassName).defaultValue("null").build())
                    addProperty(PropertySpec.builder("environment", envClassName).apply {
                        mutable(true)
                        setter(FunSpec.setterBuilder().addModifiers(KModifier.PRIVATE).build())
                        initializer("if (DEBUG) UCOOEnvironmentInstance.DEBUG else UCOOEnvironmentInstance.RELEASE")
                    }.build())

                    dynamicEnvFields.forEach {
                        val upperStr = it.key.uppercase()
                        addProperty(PropertySpec.builder(upperStr, it.value).apply {
                            getter(FunSpec.getterBuilder().apply {
                                addStatement("return environment.${upperStr}")
                            }.build())
                        }.build())
                    }

                    staticEnvFields.forEach {
                        val upperStr = it.key.uppercase()
                        val element = defaultFields.get(it.key)
                        if (element.isJsonPrimitive) {
                            val field = element.asJsonPrimitive
                            addProperty(PropertySpec.builder(upperStr, it.value).apply {
                                getter(FunSpec.getterBuilder().apply {
                                    if (field.isString) {
                                        addStatement("return \"${element.asString}\"")
                                    } else {
                                        addStatement("return ${element.asString}")
                                    }
                                }.build())
                            }.build())
                        }
                    }

                }.build())
                //添加伴生对象
                addType(TypeSpec.companionObjectBuilder().apply {
                    addProperty(PropertySpec.builder("Empty", buildConfigImpl).apply {
                        initializer(StringBuilder().apply {
                            append("BuildConfigImpl(")

                            append(buildFields.map {
                                "${it.key.uppercase()} = ${
                                    if (it.value == (Long::class)) {
                                        "0L"
                                    } else if (it.value == Boolean::class) {
                                        "false"
                                    } else {
                                        "\"\""
                                    }
                                }"
                            }.joinToString(","))

//                            append(",")
//
//                            append(dynamicFields.map {
//                                "${it.key.uppercase()} = ${
//                                    if (it.value == (Long::class)) {
//                                        "0L"
//                                    } else if (it.value == Boolean::class) {
//                                        "false"
//                                    } else {
//                                        "\"\""
//                                    }
//                                }"
//                            }.joinToString(","))

                            append(")")
                        }.toString())
                    }.build())

                    addFunction(FunSpec.builder("inject").apply {
                        buildFields.forEach {
                            addParameter(it.key.uppercase(), it.value)
                        }

//                        addParameter("multiEnvString", String::class)
//                        addStatement("val multiEnvs = mutableListOf<UCOOEnvironmentInstance>()")
//
//                        beginControlFlow("if(multiEnvString.isNotEmpty())")
//                        addStatement("val jsonArr = %T(multiEnvString)", ClassName("org.json", "JSONArray"))
//                        beginControlFlow("for (i in 0 until jsonArr.length())")
//                        addStatement("val jsonObj = jsonArr.getJSONObject(i);")
//                        addStatement(StringBuilder().apply {
//                            append("val envObj = UCOOEnvironmentInstance(")
//                            append(dynamicFields.map {
//                                "${it.key.uppercase()} = jsonObj.${getJsonOptFunc(it.value)}(\"${it.key}\")"
//                            }.joinToString(",\n"))
//                            append(")\n")
//                            append("multiEnvs.add(envObj)")
//                        }.toString())
//                        endControlFlow()
//                        endControlFlow()
//
//                        addStatement("environments = multiEnvs")
//                        addStatement("val defaultEnv = multiEnvs.find{ it.NAME == BUILD_TYPE }?:throw Exception(\"未知的环境类型, 编译过程中请检查\")")

                        addStatement(StringBuilder().apply {

                            append("BuildConfig = ")
                            append("BuildConfigImpl(")

                            append(buildFields.map {
                                val uppercase = it.key.uppercase()
                                "$uppercase = $uppercase"
                            }.joinToString(","))

//                            append(",")
//
//                            append(dynamicFields.map {
//                                val uppercase = it.key.uppercase()
//                                "$uppercase = defaultEnv.$uppercase"
//                            }.joinToString(","))

                            append(")")
                        }.toString())
                    }.build())

                    addFunction(FunSpec.builder("switchEnv").apply {
                        addParameter("envStr", String::class)
                        addStatement("val env = UCOOEnvironmentInstance.entries.find { it.NAME == envStr }?: return false")
                        addStatement("BuildConfig.environment = env")
                        addStatement("return true")
                        returns(Boolean::class, "切换环境是否成功?")
                    }.build())

                    addFunction(FunSpec.builder("switchEnv").apply {
                        addParameter("env", envClassName)
                        addStatement("BuildConfig.environment = env")
                        addStatement("return true")
                        returns(Boolean::class, "切换环境是否成功?")
                    }.build())
                }.build())
            }.build())

            addProperty(PropertySpec.builder("BuildConfig", buildConfigImpl).apply {
                mutable()
                addModifiers(KModifier.INTERNAL)
                setter(FunSpec.setterBuilder().addModifiers(KModifier.PRIVATE).build())
                initializer("BuildConfigImpl.Empty")
            }.build())
        }
    }

    private fun getJsonOptFunc(clazz: KClass<*>) = if (clazz == Boolean::class) {
        "optBoolean"
    } else if (clazz == Long::class) {
        "optLong"
    } else if (clazz == Float::class) {
        "optFloat"
    } else {
        "optString"
    }

    private fun parseDynamicFields(jsonElement: JsonElement, block: (String, KClass<*>) -> Unit) {

        val fields = mutableMapOf<String, KClass<*>>()

        if (jsonElement.isJsonArray) {
            jsonElement.asJsonArray.forEach {
                val jsonObj = it.asJsonObject
                jsonObj.keySet().forEach {
                    val element = jsonObj.get(it)
                    if (element.isJsonPrimitive) {
                        val field = element.asJsonPrimitive
                        val type = if (field.isNumber) {
                            Long::class
                        } else if (field.isString) {
                            String::class
                        } else if (field.isBoolean) {
                            Boolean::class
                        } else {
                            return@forEach
                        }
                        fields.putIfAbsent(it, type)
                    }
                }
            }
        } else if (jsonElement.isJsonObject) {
            val jsonObj = jsonElement.asJsonObject
            jsonObj.keySet().forEach {
                val element = jsonObj.get(it)
                if (element.isJsonPrimitive) {
                    val field = element.asJsonPrimitive
                    val type = if (field.isNumber) {
                        Long::class
                    } else if (field.isString) {
                        String::class
                    } else if (field.isBoolean) {
                        Boolean::class
                    } else {
                        return@forEach
                    }
                    fields.putIfAbsent(it, type)
                }
            }
        } else {
            throw RuntimeException("jsonElement is not JsonArray or JsonObject")
        }

        fields.forEach {
            val key = it.key
            val type = it.value
            block(key, type)
        }

    }

    private fun parseObjectFields(jsonObj: JsonObject, block: (String, KClass<*>) -> Unit) {
        jsonObj.keySet().forEach { key ->
            val element = jsonObj.get(key)
            if (element.isJsonPrimitive) {
                val field = element.asJsonPrimitive
                val type = if (field.isNumber) {
                    Long::class
                } else if (field.isString) {
                    String::class
                } else if (field.isBoolean) {
                    Boolean::class
                } else {
                    return@forEach
                }
                block(key, type)
            }
        }
    }

    private fun JsonObject.get(name: String, clazz: KClass<*>): Any {
        return if (clazz == Boolean::class) {
            get(name).asBoolean
        } else if (clazz == Long::class) {
            get(name).asLong
        } else if (clazz == Float::class) {
            get(name).asFloat
        } else {
            get(name).asString
        }
    }

    private fun generateFormat(clazz: KClass<*>): String {
        return if (clazz == Boolean::class) {
            "%B"
        } else if (clazz == Long::class) {
            "%L"
        } else if (clazz == Float::class) {
            "%F"
        } else {
            "%S"
        }
    }

}