package com.qyqy.plugin

import com.android.build.api.variant.AndroidComponentsExtension
import com.android.build.api.variant.ApplicationAndroidComponentsExtension
import com.android.build.api.variant.LibraryAndroidComponentsExtension
import groovyjarjarantlr4.v4.codegen.CodeGeneratorExtension
import org.gradle.api.Action
import org.gradle.api.JavaVersion
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.Task
import org.gradle.api.tasks.compile.JavaCompile
import org.jetbrains.kotlin.com.google.gson.JsonParser
import org.jetbrains.kotlin.gradle.dsl.KotlinCompile
import java.io.File


class UCOOPlugin : Plugin<Project> {
    private fun Project.addTask(name: String, action: Action<Task>) {
        val target = this
        val newTask = target.tasks.register(name) {
            it.doLast(action)
        }
        target.tasks.withType(KotlinCompile::class.java).configureEach {
            it.dependsOn(newTask)
        }
        target.tasks.withType(JavaCompile::class.java).configureEach {
            it.dependsOn(newTask)
        }
    }

    override fun apply(target: Project) {

        val extension = target.extensions.getByType(AndroidComponentsExtension::class.java)

        // parse json

        if (extension is ApplicationAndroidComponentsExtension) {
            //是application
            extension.finalizeDsl { ext ->
                ext.buildFeatures {
                    buildConfig = true
                }

                ext.compileOptions {
                    sourceCompatibility = JavaVersion.VERSION_17
                    targetCompatibility = JavaVersion.VERSION_17
                }
            }
        } else if (extension is LibraryAndroidComponentsExtension) {
            extension.finalizeDsl { ext ->
                ext.sourceSets {
                    named("main") {
                        it.kotlin.srcDirs("build/generated/ucoo/kotlin")
                    }
                }
            }

            target.addTask("generateEnv") {
                val configFile = target.rootProject.file("env.json")
                if (!configFile.exists()) {
                    throw RuntimeException("can't found env.json file in ${configFile.parent}")
                }
                val projectDir = File(target.buildDir.absolutePath, "generated").absolutePath + "/ucoo/kotlin"
                BuildConfigCase(configFile, projectDir).generate()
            }

        } else {
            println(extension.toString())
        }
    }
}